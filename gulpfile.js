var inject = require("gulp-inject");
var gulp = require("gulp");
var concat = require("gulp-concat");
var ngAnnotate = require("gulp-ng-annotate");
var plumber = require("gulp-plumber");
var bowerFiles = require("main-bower-files");
var plugins = require("gulp-load-plugins")();
var uglify = require("gulp-uglify");
var bytediff = require("gulp-bytediff");
var rename = require("gulp-rename");
var templateCache = require("gulp-angular-templatecache");
var clean = require("gulp-clean");
var runSequence = require("run-sequence");
var sass = require("gulp-sass");
var livereload = require("gulp-livereload");
var bower = require("gulp-bower");
var es = require("event-stream");
var babel = require("gulp-babel");
var typescript = require("gulp-typescript");
var tsProject = typescript.createProject("tsconfig.json");

var paths = {
    appScript: "app/**/app.js",
    moduleScripts: "app/**/*.module.js",
    combinedCustomerScriptsList: [
        "app/**/app-customer.js",
        "app/features/customerProducts/customerProducts.module.js",
        "app/features/orders/ordersTabs.module.js",
        "app/features/base/base.module.js",
        "app/features/viewable/viewable.module.js",
        "app/services/services.module.js",
        "app/features/shared/shared.module.js",
        "app/features/viewer/viewer.module.js",
        "app/features/base/**/*.js",
        "app/features/customerProducts/**/*.js",
        "app/app.controller.js",
        "app/customer-app.routes.js",
        "app/features/viewer/customer3D/**/*.js",
        "app/features/viewer/customerPDF/**/*.js",
        "app/features/viewer/extensions/**/*.js",
        "app/features/viewer/pdf/**/*.js",
        "app/features/customerProducts/**/*.js",
        "app/features/viewer/viewerHelper.service.js",
        "app/features/viewable/**/*.js",
        "app/services/**/*.js",
        "app/features/viewer/viewerVariables.service.js",
        "app/features/shared/**/*.js",
        "app/features/orders/**/*.js",
        "!app/**/*.spec.js",
        "!app/**/app.js",
    ],
    otherScripts: "app/**/*.js",
    ignoreTests: "!app/**/*.spec.js",
    ignoreCustomerApp: "!app/**/app-customer.js",
    ignoreCustomerRoutes: "!app/**/customer-app.routes.js",
    images: "./images/**/*",
    translations: "./app/translations/**/*",
    index: "./app/index.html",
    mainStyle: "./app/styles/main.scss",
    devStyles: "./dev/main.css",
    customerStyles: "./customer/main.css",
    prodStyles: "./prod/main.css",
    partials: ["app/**/*.html", "!app/index.html"],
    distProd: "./prod",
    distDev: "./dev",
    distApp: "./app",
    distCustomer: "./customer",
    prodMinApp: "./prod/app.min.js",
    prodMinVend: "./prod/vendor.min.js",
    prodMinTemplates: "./prod/templates.js",
    devApp: "./dev/app.js",
    customerApp: "./customer/app.js",
    devVend: "./dev/vendor.js",
    customerVend: "./customer/vendor.js",
    devTemplates: "./dev/templates.js",
    distScriptsProd: "./dist.prod/scripts",
    scriptsDevServer: "devServer/**/*.js",
    devTypescript: "./dev/**/*.js",
    bowerDir: "bower_components",
    typeScript: "app/**/*.ts",
    externalCSS: [
        "external_lib/accordion.min.css",
        "bower_components/jquery-ui/themes/base/jquery-ui.min.css",
        "bower_components/angular-ivh-treeview/dist/ivh-treeview.css",
        "bower_components/angular-ivh-treeview/dist/ivh-treeview-theme-basic.css",
        "external_lib/color-picker.min.css",
    ],
    externalJS: ["external_lib/rxp-js-min.js", "external_lib/**/*.js"],
};

gulp.task("clean-build-dev", function (callback) {
    runSequence("bower", "clean-dev", "compile-typescript", "devIndex", callback);
});
gulp.task("clean-build-prod", function (callback) {
    runSequence("bower", "clean-prod", "compile-typescript", "prodIndex", callback);
});

gulp.task(
    "prodIndex",
    [
        "minify-app-scripts",
        "minify-vendor-scripts",
        "minify-html-templates",
        "process-images-prod",
        "process-translations-prod",
        "compile-sass-prod",
    ],
    function () {
        var target = gulp.src(paths.index);

        var sources = gulp.src([paths.prodMinVend, paths.prodMinApp, paths.prodMinTemplates, paths.prodStyles], { read: false });

        return target
            .pipe(
                inject(sources, {
                    transform: function (filePath, file, i, length) {
                        var newPath = filePath.replace("/prod/", "");
                        var fileExt = newPath.substr(newPath.length - 4);
                        if (fileExt === ".css") {
                            return '<link rel="stylesheet" href="../' + newPath + '">';
                        } else {
                            return '<script src="../' + newPath + '"></script>';
                        }
                    },
                })
            )
            .pipe(gulp.dest(paths.distProd));
    }
);

gulp.task(
    "devIndex",
    [
        "concatenate-app-scripts",
        "concatenate-vendor-scripts",
        "copy-html-templates",
        "process-images-dev",
        "process-translations-dev",
        "compile-sass",
    ],
    function () {
        var target = gulp.src(paths.index);

        var sources = es.merge(gulp.src([paths.devVend, paths.devApp, paths.devStyles], { read: false }));

        return target.pipe(inject(sources, { relative: true })).pipe(gulp.dest(paths.distDev));
    }
);

gulp.task("process-images-dev", function () {
    return gulp.src(paths.images).pipe(gulp.dest(paths.distDev + "/images/"));
});

gulp.task("process-translations-dev", function () {
    return gulp.src(paths.translations).pipe(gulp.dest(paths.distDev + "/translations/"));
});

gulp.task("process-images-prod", function () {
    return gulp.src(paths.images).pipe(gulp.dest(paths.distProd + "/images/"));
});

gulp.task("process-translations-prod", function () {
    return gulp.src(paths.translations).pipe(gulp.dest(paths.distProd + "/translations/"));
});

gulp.task("concatenate-app-scripts", function () {
    return gulp
        .src([
            paths.distDev,
            paths.appScript,
            paths.moduleScripts,
            paths.otherScripts,
            paths.ignoreTests,
            paths.ignoreCustomerApp,
            paths.ignoreCustomerRoutes,
            paths.devTypescript,
        ])
        .pipe(plumber())
        .pipe(concat("app.js", { newLine: ";" }))
        .pipe(ngAnnotate({ add: true }))
        .pipe(gulp.dest(paths.distDev))
        .pipe(plumber.stop());
});

gulp.task("minify-app-scripts", ["concatenate-app-scripts"], function () {
    return gulp
        .src(paths.distDev + "/app.js")
        .pipe(plumber())
        .pipe(bytediff.start())
        .pipe(babel({ presets: ["es2015"] }))
        .pipe(uglify({ mangle: true }))
        .pipe(bytediff.stop())
        .pipe(rename("app.min.js"))
        .pipe(plumber.stop())
        .pipe(gulp.dest(paths.distProd));
});

orderedVendorScripts = function () {
    return plugins.order(["jquery.js", "angular.js"]);
};

gulp.task("minify-vendor-scripts", ["concatenate-vendor-scripts"], function () {
    return gulp
        .src(paths.distDev + "/vendor.js")
        .pipe(plumber())
        .pipe(bytediff.start())
        .pipe(bytediff.stop())
        .pipe(rename("vendor.min.js"))
        .pipe(plumber.stop())
        .pipe(gulp.dest(paths.distProd));
});

gulp.task("concatenate-vendor-scripts", function () {
    var vendorJsStream = gulp.src(bowerFiles("**/*.js")).pipe(orderedVendorScripts());

    var externalJsStream = gulp.src(paths.externalJS);

    return es.merge(vendorJsStream, externalJsStream).pipe(concat("vendor.js")).pipe(gulp.dest(paths.distDev));
});

gulp.task("minify-html-templates", function () {
    return gulp
        .src(paths.partials)
        .pipe(plumber())
        .pipe(templateCache("templates.js", { module: "cadshareApp" }))
        .pipe(plumber.stop())
        .pipe(gulp.dest(paths.distProd));
});

gulp.task("copy-html-templates", function () {
    return gulp.src(paths.partials).pipe(gulp.dest(paths.distDev));
});

gulp.task("clean-dev", function () {
    return gulp.src(paths.distDev, { read: false }).pipe(clean());
});

gulp.task("clean-prod", function () {
    return gulp.src(paths.distProd, { read: false }).pipe(clean());
});

gulp.task("compile-sass", function () {
    var sassStream = gulp.src(paths.mainStyle).pipe(sass());
    var cssStream = gulp.src(paths.externalCSS);

    return es.merge(sassStream, cssStream).pipe(concat("main.css")).pipe(gulp.dest(paths.distDev));
});

gulp.task("compile-sass-prod", function () {
    var sassStream = gulp.src(paths.mainStyle).pipe(sass());
    var cssStream = gulp.src(paths.externalCSS);

    return es.merge(sassStream, cssStream).pipe(concat("main.css")).pipe(gulp.dest(paths.distProd));
});

gulp.task("compile-typescript", function () {
    return gulp.src(paths.typeScript).pipe(tsProject()).pipe(gulp.dest(paths.distDev));
});

gulp.task("copy-sass-templates", function () {
    return gulp.src(paths.partials).pipe(gulp.dest(paths.distDev));
});

gulp.task("watch-build-dev", ["clean-build-dev", "watch-dev"]);

gulp.task("watch-dev", function () {
    gulp.watch(["app/**/*", "app/**/*.ts"], ["clean-build-dev"]);
});

gulp.task("bower", function () {
    return bower("", {}).pipe(gulp.dest(paths.bowerDir));
});
