(function () {
    'use strict';

    angular
        .module('app.services')
        .factory('manufactureManualService', manufactureManualService);

    manufactureManualService.$inject = ['$http', 'apiConstants', 'userService'];

    function manufactureManualService($http, apiConstants, userService) {

        return {
            fetchPublications: fetchPublications,
            getRangeByManufacturer: getRangeByManufacturer,
            getMachineByRange: getMachineByRange,
            getModelByMachine: getModelByMachine,
            getCompletedModelUploadsByMachine: getCompletedModelUploadsByMachine,
            getManufacturerSubEntitiesForManufacturer: getManufacturerSubEntitiesForManufacturer,
            createManual: createManual,
            updateManual: updateManual,
            assignManualCustomers: assignManualCustomers,
            publishUnpublishManual: publishUnpublishManual,
            fetchManuals: fetchManuals,
            fetchModels: fetchModels
        };

        function fetchPublications() {
            var manufacturerId = userService.getManufacturerId();
            return $http.get(apiConstants.url + '/manufacturers/' + manufacturerId + '/publications', null);
        }

        function fetchManuals(manualId) {
            var manufacturerId = userService.getManufacturerId();
            return $http.get(apiConstants.url + '/manufacturer/' + manufacturerId + '/manualDetails/' + manualId);
        }

        function fetchModels() {
            var manufacturerId = userService.getManufacturerId();
            return $http.get(apiConstants.url + '/manufacturer/' + manufacturerId + '/models', null);
        }

        function getRangeByManufacturer() {
            var manufacturerId = userService.getManufacturerId();
            return $http.get(apiConstants.url + '/manufacturer/' + manufacturerId + '/ranges', null);
        }

        function getMachineByRange(rangeId) {
            return $http.get(apiConstants.url + '/range/' + rangeId + '/machines', null);
        }

        function getModelByMachine(machineId) {
            return $http.get(apiConstants.url + '/machine/' + machineId + '/models', null);
        }

        function getCompletedModelUploadsByMachine(machineId) {
            return $http.get(apiConstants.url + '/machine/' + machineId + '/models?status=PROPERTIES_PROCESSED&setupComplete=true', null);
        }

        function getManufacturerSubEntitiesForManufacturer() {
            var manufacturerId = userService.getManufacturerId();
            return $http.get(apiConstants.url + '/manufacturer/' + manufacturerId + '/manufacturersubentities', null);
        }

        function createManual(modelId, techDocId, videoId, kitId, manualName, serialNumber, featuredModelId, featuredModelUrl, useViewableImage) {
            var manualData = createManualData(manualName, modelId, techDocId, videoId, kitId, serialNumber, featuredModelId, featuredModelUrl, useViewableImage );
            return $http.post(apiConstants.url + '/manual', manualData);
        }

        function updateManual(manualId, modelId, techDocId, videoId, kitId, manualName, serialNumberId, featuredModelId, featuredModelUrl, useViewableImage) {
            var manualData = updateManualData(manualId, manualName, modelId, techDocId, videoId, kitId, serialNumberId, featuredModelId, featuredModelUrl, useViewableImage);
            return $http.put(apiConstants.url + '/manual', manualData);
        }

        function createManualData(manualName, modelId, techDocId, videoId, kitId, serialNumber, featuredModelId, featuredModelUrl, useViewableImage) {
            return {
                "manualName": manualName,
                "manualDescription": "description",
                "modelId": modelId,
                "techDocId": techDocId,
                "videoId": videoId,
                "kitId": kitId,
                "serialNumber": serialNumber,
                "status": "UNPUBLISHED",
                "featuredModelId": featuredModelId,
                "featuredModelUrl": featuredModelUrl,
                "useViewableImage": useViewableImage
            };
        }

        function updateManualData(manualId, manualName, modelId, techDocId, videoId, kitId, serialNumber, featuredModelId, featuredModelUrl, useViewableImage) {
            return {
                "manualId": manualId,
                "manualName": manualName,
                "serialNumber": serialNumber,
                "modelId": modelId,
                "techDocId": techDocId,
                "videoId": videoId,
                "kitId": kitId,
                "featuredModelId": featuredModelId,
                "featuredModelUrl": featuredModelUrl,
                "useViewableImage": useViewableImage
            };
        }

        function assignManualCustomers(manualId, manufacturerSubEntityIds) {
            return $http.put(apiConstants.url + '/manual/' + manualId + '/assign', manufacturerSubEntityIds);
        }

        function publishUnpublishManual(manualId, status) {
            status = status === 'UNPUBLISHED' ? 'PUBLISHED' : 'UNPUBLISHED';
            var data = {status: status};
            return $http.put(apiConstants.url + '/manual/' + manualId + '/status', data);
        }

    }
})();
