(function () {
    'use strict';

    angular
        .module('app.parts')
        .controller('TranslationController', TranslationController);

    TranslationController.$inject = ['$uibModalInstance', 'translationObject', 'userService', 'masterPartService'];

    function TranslationController($uibModalInstance, translationObject, userService, masterPartService) {
        var vm = this;

        vm.save = save;
        vm.cancel = $uibModalInstance.dismiss;

      if (translationObject) {
            vm.description = translationObject.description;
            vm.masterPartId = translationObject.masterPartId;
            vm.partNumber = translationObject.partNumber;
            vm.language = translationObject.language;
            vm.isEdit = translationObject.isEdit;
        } else {
            vm.isEdit = false;
        }

        if (vm.description === "") {
            vm.isEdit = true;
        }
        function save() {
            clearErrors();
            if (vm.isEdit) {
                masterPartService.updateTranslation(vm.masterPartId, vm.language.languageId, vm.description)
                    .then(saveSuccess, saveFailure);
            } else {
                masterPartService.createTranslation(vm.masterPartId, vm.language.languageId, vm.description)
                    .then(saveSuccess, saveFailure);
            }
        }

        function saveSuccess() {
            $uibModalInstance.close();
        }

        function saveFailure(error) {
            vm.hasServerError = true;
            console.log("Error saving price: " + error)
        }

        function clearErrors(){
            vm.hasServerError = false;
            vm.isPickLanguageError = false;
        }
    }
})();
