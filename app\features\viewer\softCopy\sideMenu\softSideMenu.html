<div class="soft-side-menu side-menu " ng-hide="softCopySideMenuCtrl.isMinimized">

    <!--<button class="btn-side-menu" ng-click="softCopySideMenuCtrl.minimizeSideMenu()">
        CLOSE MENU
        <i class="pull-right fa fa-close"></i>
    </button>-->

    <uib-accordion class="soft-copy-side-menu-accordion-panel">
        <uib-accordion-group is-open="softCopySideMenuCtrl.status.isModelBrowserOpen">
            <uib-accordion-heading>
                <i class="fa fa-fw fa-sitemap "></i>&nbsp; Model Browser
                <span ng-show="softCopySideMenuCtrl.status.isModelBrowserOpen" class="pull-right"><i
                        class="fa fa-angle-down"></i>&nbsp;</span>
                <span ng-hide="softCopySideMenuCtrl.status.isModelBrowserOpen" class="pull-right"><i
                        class="fa fa-angle-right"></i>&nbsp;</span>
            </uib-accordion-heading>
            <model-browser></model-browser>
        </uib-accordion-group>
        <uib-accordion-group is-open="softCopySideMenuCtrl.status.isViewerSettingsOpen">
            <uib-accordion-heading>
                <i class="fas fa-sliders"></i>&nbsp; Viewer Settings
                <span ng-show="softCopySideMenuCtrl.status.isViewerSettingsOpen" class="pull-right"><i
                        class="fa fa-angle-down"></i>&nbsp;</span>
                <span ng-hide="softCopySideMenuCtrl.status.isViewerSettingsOpen" class="pull-right"><i
                        class="fa fa-angle-right"></i>&nbsp;</span>
            </uib-accordion-heading>
            <viewer-settings></viewer-settings>
        </uib-accordion-group>

    </uib-accordion>

</div>

<div class="expand-model-browser-button" ng-show="softCopySideMenuCtrl.isMinimized">
    <button class="btn-model" ng-click="softCopySideMenuCtrl.maximizeSideMenu()">
        <i class="fa fa-bars "></i> OPEN MENU
    </button>
</div>