(function () {
    'use strict';

    angular
        .module('app.products')
        .controller('ImageCropperController', ImageCropperController);

    ImageCropperController.$inject = ['$uibModalInstance', '$http', '$q', 'apiConstants', 'cropType', '$translate'];

    function ImageCropperController($uibModalInstance, $http, $q, apiConstants, cropType, $translate) {
        var vm = this;

        vm.close = close;
        vm.ok = ok;

        var ERROR_MSG;
        $translate(['IMG_CROPPER.ERROR_MSG'])
            .then(function (resp) {
                ERROR_MSG = resp["IMG_CROPPER.ERROR_MSG"];
            });

        initialize();

        function initialize() {
            vm.cropper = {};
            vm.cropper.sourceImage = null;
            vm.cropper.croppedImage = null;
            vm.bounds = {};
            vm.bounds.left = 0;
            vm.bounds.right = 0;
            vm.bounds.top = 0;
            vm.bounds.bottom = 0;

            if (cropType === 'LOGO') {
                vm.cropWidth = 182;
                vm.cropHeight = 35;
            } else if(cropType === 'WATERMARK'){
                vm.cropWidth = 250;
                vm.cropHeight = 100;
            }else{
                vm.cropWidth = 375;
                vm.cropHeight = 220;
            }
        }


        function close() {

            $uibModalInstance.close();
        }

        function ok() {

            uploadToAWS()
                .then(function (awsImageUrl) {
                    $uibModalInstance.close(awsImageUrl);
                })
        }

        function uploadToAWS() {
            var promise = $q.defer();
            var splitArray = vm.cropper.croppedImage.split(',');
            var b64Data = splitArray[1];
            var part = splitArray[0].split(':')[1];
            var contentType = part.split(';')[0];
            var fileType = contentType.split('/')[1];
            var blob = b64toBlob(b64Data, contentType);

            var myBlob = blob;
            var fileName = guid() + "." + fileType;
            myBlob.lastModifiedDate = new Date();
            myBlob.name = fileName;

            $http.post(apiConstants.url + '/aws/s3/getpresignedurl', {
                objectKey: myBlob.name,
                fileSizeInBytes: myBlob.size
            })
                .then(function getPresignedUrlSuccess(resp) {
                    console.log("File is: " + myBlob);
                    return $http.put(resp.data.URL, myBlob, {headers: {'Content-Type': myBlob.type}})
                        .then(function () {
                            var imageURL = resp.data.URL.split("?");
                            return promise.resolve(imageURL[0]);
                        }, function (error) {
                            uploadFail(error);
                        });
                }, uploadFail);

            return promise.promise;
        }


        function guid() {
            var d = new Date().getTime();
            return 'xxxx-xxxx-xxxx-xxxx-xxxx-xxxx-xxxx-xxxx'.replace(
                /[xy]/g,
                function (c) {
                    var r = (d + Math.random() * 16) % 16 | 0;
                    d = Math.floor(d / 16);
                    return (c === 'x' ? r : (r & 0x7 | 0x8)).toString(16);
                });
        }


        function uploadFail(error) {
            alert(ERROR_MSG);
            return $q.reject(error);
        }

        function b64toBlob(b64Data, contentType, sliceSize) {
            contentType = contentType || '';
            sliceSize = sliceSize || 512;

            var byteCharacters = atob(b64Data);
            var byteArrays = [];

            for (var offset = 0; offset < byteCharacters.length; offset += sliceSize) {
                var slice = byteCharacters.slice(offset, offset + sliceSize);

                var byteNumbers = new Array(slice.length);
                for (var i = 0; i < slice.length; i++) {
                    byteNumbers[i] = slice.charCodeAt(i);
                }

                var byteArray = new Uint8Array(byteNumbers);

                byteArrays.push(byteArray);
            }

            var blob = new Blob(byteArrays, {type: contentType});
            return blob;
        }

    }
})();
    
    