<h2 ng-if="partsSearchCtrl.masterParts.length > 0" translate>ORDER.KITS</h2>

<div class="responsiveContainer py-0">

    <table class="table table-bordered" ng-show="partsSearchCtrl.masterParts.length > 0">
        <thead>
            <tr>
                <th ng-class="partsSearchCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'"
                    translate>GENERAL.KIT_NUMBER</th>
                <th ng-class="partsSearchCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'"
                    ng-class="partsSearchCtrl.isPreviewStockLevelEnabled ? 'width-40' : 'width-60'"
                    translate>PART_SEARCH.DESC</th>
                <th ng-class="partsSearchCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'"
                    ng-if="partsSearchCtrl.isPreviewStockLevelEnabled && !partsSearchCtrl.isStockWarehousesEnabled"
                    translate>PART_SEARCH.STOCK</th>
                <th translate>GENERAL.ACTIONS</th>
            </tr>
        </thead>

        <tbody>
            <tr class="hoverTableBG"  ng-class="{'strike-through': part.totalQuantity <= 0, 'borderLeft': partsSearchCtrl.accordionStates[$index]}" ng-if="part.type === 'KIT' && part.kitId" ng-click="partsSearchCtrl.toggleKitsAccordion(part.kitId, $index, $event)"
                ng-repeat-start="part in partsSearchCtrl.masterParts | orderBy:partsSearchCtrl.part_sort:partsSearchCtrl.sortReverse">
                <td data-label="{{'PART_SEARCH.PART_NUM' | translate}}">
                    <i class="pr-2 fa" ng-class="partsSearchCtrl.accordionStates[$index] ? 'fa-chevron-up' : 'fa-chevron-down'"
                        style="pointer-events: none;"></i>
                    {{ part.partNumber }}
                    <span ng-if="(part.kitCount || part.partCountInKits) >= 0" class="badge badge-pill badge-primary">{{ part.kitCount ||
                        part.partCountInKits }} Parts</span>
                </td>
                <td data-label="{{'PART_SEARCH.DESC' | translate}}">{{part.kitDescription}}</td>
            
                <td class="disableWordBreak" data-label="{{'PART_SEARCH.STOCK' | translate}}"
                    ng-if="partsSearchCtrl.isPreviewStockLevelEnabled && !partsSearchCtrl.isStockWarehousesEnabled">
                    <span title="{{'ORDER.IN_STOCK' | translate}}" tooltip-trigger="outsideClick"
                        uib-tooltip="{{'ORDER.IN_STOCK' | translate}}" class="success-alert" ng-if="part.stock >= 3"><i
                            class="fas fa-layer-group text-success pointer"></i></span>
                    <span title="{{'ORDER.LOW_STOCK' | translate}}" tooltip-trigger="outsideClick"
                        uib-tooltip="{{'ORDER.LOW_STOCK' | translate}}" class="warning-alert"
                        ng-if="part.stock < 3 && part.stock > 0 "><i class="fas fa-layer-group text-warning pointer"></i></span>
                    <span title="{{'ORDER.STOCK_SUBJECT' | translate}}" tooltip-trigger="outsideClick"
                        uib-tooltip="{{'ORDER.STOCK_SUBJECT' | translate}}" class="warning-alert "
                        ng-if="part.stock === null || part.stock < 1"><i class="fas fa-layer-group text-danger pointer"></i></span>
                </td>
            
                <td>
                    <button aria-labelledby="View Part" class="btn primary"
                        ng-click="partsSearchCtrl.goToMasterPart(part.masterPartId)" translate>PART_SEARCH.VIEW_PART
                    </button>
                </td>
            
            </tr>

            <tr ng-repeat-end ng-if="part.type === 'KIT'">
                <td class="p-0" colspan="100%">
                    <div class="accordion-anim" ng-class="{'open': partsSearchCtrl.accordionStates[$index]}">
                        <table class="table table-bordered w-100">
                            <thead class="borderLeft">
                                <tr>
                                    <th translate>ORDER.PART_DETAILS</th>
                                    <th translate>ORDER.TITLE</th>
                                    <th translate>ORDER.QTY</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="blueTableBG borderLeft" ng-repeat="partDetail in part.kitDetails"
                                    ng-class="partDetail.quantity > 0 ? '' : 'strike-through'">
                                    <td class="col-lg-1 col-12" data-label="{{'ORDER.PART_DETAILS' | translate}}">
                                        <span>{{partDetail.partNumber}}</span>
                                    </td>

                                    <td class="col-lg-2 col-12" data-label="{{'ORDER.TITLE' | translate}}">
                                        {{ (partDetail.descriptions | filter: {languageCode:
                                        currentLanguage})[0].translation }}
                                    </td>

                                    <td class="col-lg-1 col-12" data-label="{{'ORDER.QTY' | translate}}">
                                        <span>{{partDetail.quantity}}</span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </td>
            </tr>

            </tr>
        </tbody>
    </table>

    <div class="px-2" ng-if="!partsSearchCtrl.hasTypeKit() && partsSearchCtrl.masterParts.length > 0">
        <p class="font-weight-bold" translate>CUST_PART_SEARCH.NO_KITS</p>
    </div>

</div>