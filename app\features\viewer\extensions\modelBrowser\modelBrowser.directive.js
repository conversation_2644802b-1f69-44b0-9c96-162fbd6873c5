(function () {
    'use strict';

    angular
        .module('app.viewer')
        .directive('modelBrowser', modelBrowser);

    function modelBrowser() {
        var directive = {
            restrict: 'E',
            templateUrl: 'features/viewer/extensions/modelBrowser/modelBrowser.html',
            controller: 'ModelBrowserController',
            controllerAs: 'modelBrowserCtrl',
            bindToController: true
        };
        return directive;
    }

})();