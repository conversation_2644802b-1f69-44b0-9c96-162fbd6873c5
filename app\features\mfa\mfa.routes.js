(function () {
    'use strict';

    angular.module('app.mfa')
        .config(function ($stateProvider) {
            $stateProvider
                .state('verificationSetup', {
                    url: '/verificationSetup',
                    templateUrl: 'features/mfa/setupMfa.html',
                    controller: 'MfaSetupController as mfaSetupCtrl'
                })
                .state('verificationCode', {
                    url: '/verificationCode',
                    templateUrl: 'features/mfa/verifyMfa.html',
                    controller: 'MfaVerifyController as mfaVerifyCtrl'
                });
        });

})();