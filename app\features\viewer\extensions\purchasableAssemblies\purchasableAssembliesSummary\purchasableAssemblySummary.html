<div class="sidebar-content" ng-show="purchasableAssemblySummaryCtrl.isOpen">
    <p translate>ASSEMBLY_SUMMARY.TITLE_DESC</p>

    <button class="btn primary" ng-click="purchasableAssemblySummaryCtrl.createPurchasableAssembly()" translate>ASSEMBLY_SUMMARY.CREATE_NEW</button>

    <h4 class="mt-16" translate>ASSEMBLY_SUMMARY.MANAGE</h4>

    <table ng-show="purchasableAssemblySummaryCtrl.existingPurchasableAssemblies.length > 0" class="tableViewer table-bordered w-100 bg-white ml-0">
        <tbody>
        <tr ng-repeat="purchasableAssembly in purchasableAssemblySummaryCtrl.existingPurchasableAssemblies">
            <td class="side-menu-table-name">
                {{purchasableAssembly.partNumber}} <span ng-if="purchasableAssembly.partDescription !== null">- {{purchasableAssembly.partDescription}}</span>
            </td>
            <td><a href="" class="btn xsmall danger main-action"
                   ng-click="purchasableAssemblySummaryCtrl.deletePurchasableAssembly(purchasableAssembly)" translate>ASSEMBLY_SUMMARY.DELETE</a></td>
            </tr>
        </tbody>
    </table>

    <p ng-hide="purchasableAssemblySummaryCtrl.existingPurchasableAssemblies.length > 0" translate>
        ASSEMBLY_SUMMARY.NO_ASSEMBLIES
    </p>

</div>