<section class="dpGreenOrders">
    <aside class="d-flex justify-content-between dPlusTabs">
        <ul class="mb-0" role="tablist">
            <li ng-class="{active:DPOrdersTopTabCtrl.isActive('/myOrders')}">
                <a ui-sref="dpOrders.myOrders.orders.enquiries" >
                    {{"DP_ORDERS.MY_ORDERS" | translate}}&nbsp;
                    <span ng-hide="DPOrdersTopTabCtrl.myOrdersUnreadCount == 0" class="badge-small">
                      {{DPOrdersTopTabCtrl.myOrdersUnreadCount}}
                    </span>
                </a>
            </li>
            <li class="customersGreen" ng-class="{active :DPOrdersTopTabCtrl.isActive('/customerOrders')}">
                <a ui-sref="dpOrders.customerOrders.orders.enquiries" >
                    {{"DP_ORDERS.CUST_ORDERS" | translate}}&nbsp;
                    <span ng-hide="DPOrdersTopTabCtrl.customerOrdersUnreadCount == 0" class="badge-small">
                      {{DPOrdersTopTabCtrl.customerOrdersUnreadCount}}
                    </span>
                </a>
            </li>
        </ul>

        <div ng-hide="DPOrdersTopTabCtrl.isCustOrder()">
            <p class="mb-0 myOrdersBlue" translate>DP_ORDERS.SUBMITTED_TO_OEM</p>
        </div>
        <div ng-show="DPOrdersTopTabCtrl.isCustOrder()">
            <p class="mb-0 customersGreen" translate>DP_ORDERS.SUBMITTED_BY_CUSTOMER</p>
        </div>

    </aside>
    <div ui-view>


    </div>
</section>
