<div class="sidebar-content" ng-show="createLinkedPartCtrl.isOpen">
    <form class="form">

        <div>
            <div class="well clearfix">
                <div class="selected-part-text" ng-hide="createLinkedPartCtrl.selectedPart.length > 1">
                    <h4 translate>CREATE_LINKED.SELECTED_PART</h4>
                    <span ng-hide="createLinkedPartCtrl.selectedPart.length === 0">
                    {{createLinkedPartCtrl.selectedPart.partNumber}} &nbsp;<small><strong>{{createLinkedPartCtrl.selectedPart.partDescription}}</strong></small>
                </span>
                    <span ng-show="createLinkedPartCtrl.selectedPart.length === 0" translate>CREATE_LINKED.NONE</span>
                </div>

                <div class="selected-part-error" ng-show="createLinkedPartCtrl.selectedPart.length > 1">
                    <h4 translate>CREATE_LINKED.MULTI_PARTS</h4>
                    <p translate>CREATE_LINKED.ONLY_INDIVIDUAL</p>
                </div>
            </div>

            <div class="input-group">
                <label translate>CREATE_LINKED.SELECT_RANGE</label>
                <div class="select-box w-100">
                    <select ts-select-fix class="h-auto" ng-model="createLinkedPartCtrl.rangeId" ng-change="createLinkedPartCtrl.rangeChanged(createLinkedPartCtrl.rangeId)"
                            ng-options="rangeValue.rangeId as rangeValue.name for rangeValue in createLinkedPartCtrl.rangeValues" ng-required="true">
                    </select>

                    <div class="select-arrow"></div>
                </div>
            </div>

            <div class="input-group">
                <label translate>CREATE_LINKED.SELECT_MACHINE</label>
                <div class="select-box w-100">
                    <select class="h-auto" ts-select-fix ng-model="createLinkedPartCtrl.machineId" ng-change="createLinkedPartCtrl.machineChanged(createLinkedPartCtrl.machineId)"
                            ng-options="machine.machineId as machine.name for machine in createLinkedPartCtrl.machines" ng-required="true" ng-disabled="createLinkedPartCtrl.isMachineDropdownDisabled">
                    </select>
                    <div class="select-arrow"></div>
                </div>
            </div>

            <div class="input-group">
                <label translate>CREATE_LINKED.SELECT_VIEWABLE</label>
                    <div class="select-box w-100">
                        <select class="h-auto" ng-options="viewable as viewable.modelName for viewable in createLinkedPartCtrl.viewables track by viewable.modelId"
                                ng-model="createLinkedPartCtrl.selectedModel" ng-disabled="createLinkedPartCtrl.isViewableDropdownDisabled">
                        </select>
                        <div class="select-arrow"></div>
                    </div>
            </div>

        </div><!-- /side-menu-content -->

        <div class="side-menu-content">

            <div class="error-well"
                 ng-show="createLinkedPartCtrl.errors.noPartSelected || createLinkedPartCtrl.errors.noViewableSelected || createLinkedPartCtrl.errors.alreadyHasLinkedPart">
                <p ng-show="createLinkedPartCtrl.errors.noPartSelected" translate>
                    CREATE_LINKED.ASSOCIATE_ERROR
                </p>
                <p ng-show="createLinkedPartCtrl.errors.noViewableSelected" translate>
                    CREATE_LINKED.VIEWABLE_ERROR
                </p>
                <p ng-show="createLinkedPartCtrl.errors.alreadyHasLinkedPart" translate>
                    CREATE_LINKED.ALREADY_ERROR
                </p>
            </div>

            <div class="kit-actions">
                <button class="btn small secondary" type="button" ng-click="createLinkedPartCtrl.cancel()" translate>GENERAL.CANCEL
                </button>
                <button class="btn small primary" ng-click="createLinkedPartCtrl.saveLinkedPart()" translate>CREATE_LINKED.SAVE
                </button>
            </div>
        </div><!-- /side-menu-content -->
    </form>
</div>