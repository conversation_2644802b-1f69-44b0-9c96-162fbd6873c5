(function () {
    'use strict';

    angular
        .module('app.orders')
        .controller('CheckoutController', CheckoutController);

    CheckoutController.$inject = ['basketService', 'ordersService', '$uibModal', '$scope', 'headerBannerService', '$state', '$stateParams', 
    'userService', '$translate', '$window', 'globalPaymentService', 'apiConstants', '$timeout', 'shippingEngineService' , 'avaTaxService'];

    function CheckoutController(basketService, ordersService, $uibModal, $scope, headerBannerService, $state, $stateParams,
        userService, $translate, $window, globalPaymentService, apiConstants, $timeout, shippingEngineService, avaTaxService) {
        var vm = this;

        vm.orderCreated = [];
        vm.previewPricingEnabled = userService.getPreviewPricingEnabled();
        vm.isPreviewStockLevelEnabled = userService.getPreviewStockLevelEnabled();
        vm.defaultCurrency = userService.getDefaultCurrency();
        vm.isTaxPaymentEnabled = userService.getTaxPayments();

        vm.goToCreate = goToCreate;
        vm.areAllPricesReturned = areAllPricesReturned;
        vm.areAnyPricesReturned = areAnyPricesReturned;
        vm.partUpdated = partUpdated;
        vm.navigateToNewOrder = navigateToNewOrder;
        vm.autoRedirect = false;
        vm.taxPrice = 0;
        vm.currentWarehouse = shippingEngineService.getCurrentWarehouse();
        vm.nextWarehouse = null;
        var ORDER_FAILED, PAYMENT_FAILED, SHIPPING_FAILED, TAX_FAILED;
        $translate(['CHECKOUT_PAGE.ORDER_FAILED', 'CHECKOUT_PAGE.PAYMENT_FAILED', 'CHECKOUT_PAGE.SHIPPING_FAILED', 'CHECKOUT_PAGE.TAX_FAILED'])
            .then(function (resp) {
                ORDER_FAILED = resp["CHECKOUT_PAGE.ORDER_FAILED"];
                PAYMENT_FAILED = resp["CHECKOUT_PAGE.ORDER_FAILED"];
                SHIPPING_FAILED = resp["CHECKOUT_PAGE.SHIPPING_FAILED"];
                TAX_FAILED = resp["CHECKOUT_PAGE.TAX_FAILED"];
            });

        initialize();

        function initialize() {
            updateBasket()
            fetchManualParts()
            getTax();
            getShippingData();
            getPaymentConfig();
            getBasketByCurrentWarehouse();
        }

        function goToCreate() {
            $state.go('create');
        }

        function updateBasket() {
            vm.basket = basketService.getBasket();
            updateTotalItemPrices();
        }

        function partUpdated(part) {
            updateTotalItemPrices();
            basketService.updatePart(part);
            updateBasket();
        }


        function fetchManualParts() {
            var additionalParts = basketService.getManualPartsBasket();
            if (additionalParts.length > 0) {
                vm.savedAdditionalParts = additionalParts;
            } else {
                vm.savedAdditionalParts = [];
            }
        }

        function updateTotalItemPrices() {
            for (var i = 0; i < vm.basket.length; i++) {
                vm.basket[i].totalPrice = vm.basket[i].price * vm.basket[i].quantity;
            }
            calculateEstimatedTotal();
        }

        function calculateEstimatedTotal() {
            vm.estimatedTotal = 0;
            vm.currentWarehouse = shippingEngineService.getCurrentWarehouse()

            if(!vm.currentWarehouse) return;
            for (var i = 0; i < vm.basket.length; i++) {
                if (vm.currentWarehouse.id === vm.basket[i].warehouseId) {
                    vm.estimatedTotal += vm.basket[i].totalPrice;
                }
            }
        }

        function areAllPricesReturned() {
            if (vm.basket.length < 1) {
                return false;
            }
            for (var i = 0; i < vm.basket.length; i++) {
                if (vm.basket[i].price === 0 || vm.basket[i].price === null) {
                    return false;
                }
            }
            return vm.savedAdditionalParts.length <= 0;

        }

        function areAnyPricesReturned() {
            for (var i = 0; i < vm.basket.length; i++) {
                if (vm.basket[i].price !== 0 && vm.basket[i].price !== null) {
                    return true;
                }
            }
            return false;
        }

        function getPaymentConfig() {
            vm.loadingGetConfig = true;
            vm.orderDetail = globalPaymentService.getCurrentOrderDetail();
            if (!vm.orderDetail) return goToCreate();
            vm.orderDetail.price = vm.estimatedTotal;
            const shippingPrice = vm.shippingCarrier.totalAmount.amount;
            const orderExtra = {
                shippingCost: shippingPrice,
                taxAmount: vm.taxPrice
            }
            vm.totalPrice = vm.taxPrice + vm.shippingCarrier.totalAmount.amount + vm.estimatedTotal;
            globalPaymentService.getGlobalPaymentConfig(vm.orderDetail, orderExtra).then(getPaymentConfigSuccess, getPaymentConfigFailed);
        }

        function getPaymentConfigSuccess(jsonFromServerSdk) {
            RealexHpp.setHppUrl(apiConstants.hppUrl);
            RealexHpp.embedded.init(
                "autoload",
                "targetIframe",
                (hppResponse, close) => {
                    if (hppResponse.AUTHCODE) {
                        $('.paymentResult').empty();
                        vm.isLoading = true;
                        const data = {
                            order: vm.orderDetail,
                            shippingCarrier: vm.shippingCarrier,
                            taxInfo: vm.taxInfo,
                            associatedOrderId: vm.orderCreated.length ? vm.orderCreated[0].orderId : null,
                            wareHouseId: vm.currentWarehouse.id
                        }
                        globalPaymentService.createOrder(data, hppResponse).then(createOrderSuccess, createOrderFailed)
                    } else {
                        var errorMsg = hppResponse.MESSAGE || hppResponse.message;
                        $('.paymentResult').html(`<div class="alert alert-danger">${errorMsg}</div>`);

                        close();
                        removeFramePayment();
                        getPaymentConfig();
                    }
                    $scope.$apply();
                },
                jsonFromServerSdk.data,
                {
                    onResize: function (data) {
                        $('#targetIframe').css(data)
                    }
                }
            );
            window.addEventListener('message', (event) => {
                if (apiConstants.hppUrl.includes(event.origin)) {
                    $timeout(() => {
                        vm.loadingGetConfig = false;
                    }, 0)
                }
            });
        }

        function removeFramePayment() {
            const iframe = $('#targetIframe');
            const parent = iframe.parent();
            iframe.remove();
            parent.append('<iframe id="targetIframe" class="w-100 border-0"> </iframe>');
        }

        function getPaymentConfigFailed(error) {
            headerBannerService.setNotification('ERROR', PAYMENT_FAILED, 5000);
        }

        function navigateToNewOrder(orderId) {
            vm.autoRedirect = false;
            $('#thankyouModal').modal('hide');
            if (!vm.isSplitWarehouse) {
                vm.orderCreated = [];
                $state.go('orders.enquiry', { orderId: orderId });
                return;
            }

            vm.isLoading = true;
            shippingEngineService.getShippingRate(vm.orderDetail).then(getShippingRateSuccess, getShippingRateFailed);
        }
       
        function createOrderSuccess(res) {
            if(res.data.message) {
                headerBannerService.setNotification('WARN', res.data.message, 5000);
            }

            vm.newOrder = res.data.data;
            vm.orderCreated.push(vm.newOrder);

            // REMOVE PART IF SPLIT ORDER
            if (vm.basket && vm.basket.length) {
                vm.basket.forEach(obj => basketService.removePart(obj));
            }

            updateBasket();

            getNextWarehouse(vm.basket)

            vm.orderDetail.basket = vm.basket;
            vm.orderDetail.price = vm.estimatedTotal;
            $('#thankyouModal').modal('show');
            continuePayment();
            vm.isLoading = false;
        }

        function createOrderFailed() {
            vm.loadingGetConfig = false;
            vm.isLoading = false;
            headerBannerService.setNotification('ERROR', ORDER_FAILED, 5000);
        }

        function continuePayment() {
            vm.autoRedirect = true;
            $timeout(() => {
                if(vm.isSplitWarehouse && vm.autoRedirect) {
                    // $('#thankyouModal').modal('show');
                    navigateToNewOrder();
                }
            }, 10000);
        }

        function getShippingData() {
            vm.isSplitWarehouse = shippingEngineService.getSplitWarehouse();
            vm.shippingCarrier = shippingEngineService.getShippingCarrier();
            vm.currentWarehouse = shippingEngineService.getCurrentWarehouse();
        }

        function getShippingRateFailed(res) {
            vm.isLoading = false;
            const errors = res.data && res.data.errors;
            const errorMsg = (errors.length > 0 && errors[0].message) || SHIPPING_FAILED;
            headerBannerService.setNotification('ERROR', errorMsg, 5000);
        }

        function getShippingRateSuccess(res) {
            vm.isLoading = false;
            const data = res.data;
            
            if(data.rate_response.errors.length && !data.rate_response.rates.length) {
                headerBannerService.setNotification('ERROR', data.rate_response.errors[0].message, 5000);
            }
              
            const modalInstance = shippingEngineService.showShippingOptions(data);
            modalInstance.result.then((carrier) => {
                shippingEngineService.setShippingCarrier(carrier);
                getShippingData();
                if(!carrier) {
                    resetPrice();
                    return
                };
                if (shippingEngineService.checkCountryRequireTax(data.ship_to.country_code)) {
                    avaTaxService.getTax(vm.orderDetail).then(getTaxSuccess, getTaxFailed);
                }
                removeFramePayment();
                initialize();
            });
        }

        function getTax() {
            if(!vm.isTaxPaymentEnabled) return;
            vm.taxInfo = avaTaxService.getTaxInfo();
            if(vm.taxInfo) {
                vm.taxPrice = vm.taxInfo.totalTax;
                if(vm.shippingCarrier && vm.shippingCarrier.totalAmount) {
                    vm.totalPrice = vm.taxPrice + (vm.shippingCarrier.totalAmount.amount ? vm.shippingCarrier.totalAmount.amount : 0 )+ vm.estimatedTotal;
                }
            }
        }

        function getTaxFailed(err) {
            vm.isLoading = false;
            const errors = err.data && err.data.messages;
            const errorMsg = (errors.length > 0 && errors[0].details) || TAX_FAILED;
            headerBannerService.setNotification('ERROR', errorMsg, 5000);
        }

        function getTaxSuccess(res) {
            vm.isLoading = false;
            avaTaxService.setTaxInfo(res.data);
            getTax();
        }

        function resetPrice() {
            vm.taxPrice = 0;
            avaTaxService.setTaxInfo(null);
            vm.totalPrice = 0;
        }

        function getBasketByCurrentWarehouse() {
            if (vm.basket && vm.basket.length > 0) {
                const currentWarehouse = shippingEngineService.getCurrentWarehouse()
                if(!vm.currentWarehouse) return;
                vm.basket = vm.basket.filter(item => {
                    return item.warehouseId === currentWarehouse.id;
                })
            }
        }

        function getNextWarehouse(nextBasket) {
            if (nextBasket && nextBasket.length > 0) {
                vm.nextWarehouse = shippingEngineService.getWarehouseById(nextBasket[0].warehouseId)
            }
        }
    }
})();
