(function () {
    'use strict';

    angular
        .module('app.shared')
        .controller('PriceController', PriceController);

    PriceController.$inject = ['$uibModalInstance', 'priceObject', 'userService', 'masterPartService'];

    function PriceController($uibModalInstance, priceObject, userService, masterPartService) {
        var vm = this;

        vm.currency = userService.getDefaultCurrency();

        vm.save = save;
        vm.cancel = $uibModalInstance.dismiss;

        if (priceObject) {
            vm.price = priceObject.price;
            vm.partNumber = priceObject.partNumber;
            vm.masterPartId = priceObject.masterPartId;
        }

        function save() {
            vm.hasError = false;
            masterPartService.updatePrice(vm.masterPartId, vm.price)
                .then(saveSuccess, saveFailure);
        }

        function saveSuccess() {
            $uibModalInstance.close(vm.price);
        }

        function saveFailure(error) {
            vm.hasError = true;
            console.log("Error saving price: " + error)
        }
    }
})();
