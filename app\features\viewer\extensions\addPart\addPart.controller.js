(function () {
    'use strict';

    angular
        .module('app.viewer')
        .controller('AddPartController', AddPartController);

    AddPartController.$inject = ['viewerService', '$filter', '$scope', 'viewerHelperService'];

    function AddPartController(viewerService, $filter, $scope, viewerHelperService) {
        var vm = this;

        vm.partsList = [];
        vm.isSpinnerActive = true;
        vm.addPartList = [];
        viewerService.setHasEditedParts = false;

        vm.addNewPart = addNewPart;
        vm.createAllParts = createAllParts;
        vm.removeNewPart = removeNewPart;
        vm.partChanged = partChanged;
        vm.cancelClicked = cancelClicked;
        vm.createPartsClicked = createPartsClicked;
        vm.isTwoD = isTwoD;

        function isTwoD() {
            return viewerHelperService.getIsTwoD();
        }

        function removeNewPart(index) {
            vm.partsList.splice(index, 1);
            if (vm.partsList.length < 1) {
                addBlankPartToTable();
            }
        }

        function createAllParts() {
            var partListData = [];
            for (var i = 0; i < vm.partsList.length; i++) {
                var newPart = vm.partsList[i];

                if (newPart.partNumber != "" && newPart.partNumber != undefined) {
                    var createPartData = {
                        "itemNumber": newPart.itemNumber,
                        "partId": newPart.partId,
                        "partNumber": newPart.partNumber,
                        "fileName": newPart.partNumber,
                        "partDescription": newPart.partDescription,
                        "modelId": parseInt(vm.modelId)
                    };
                    partListData.push(createPartData);
                }
            }

            return viewerService.updateParts(partListData);

        }

        function partChanged() {
            viewerHelperService.setHasEditedParts(true);
        }

        function addNewPart() {
            addBlankPartToTable();
        }

        function addBlankPartToTable() {
            vm.partsList.push({itemNumber: '', partNumber: '', partDescription: ''});
        }

        function cancelClicked() {
            vm.partsList = [];
            viewerHelperService.setHasEditedParts(false);
            vm.onDecline();
        }

        function createPartsClicked() {
            createAllParts()
                .then(function (response) {
                    var output = response.data;
                    vm.addPartList = output;

                    vm.onConfirm();
                });
        }

        $scope.$watch(function () {
                return vm.isVisible();
            },
            function (newVal, oldVal) {
                viewerHelperService.setHasEditedParts(false);
                if (oldVal === false && newVal === true) {
                    vm.isSpinnerActive = true;
                    vm.partsList = [];
                    vm.loadParts()
                        .then(function (response) {
                            vm.partsList = response.data ? response.data : [];
                            addBlankPartToTable();
                            if (vm.partsList.length < 2) {
                                for (var i = 0; i < 6; i++) {
                                    addBlankPartToTable();
                                }
                            }
                            vm.isSpinnerActive = false;
                        });
                }
            });
    }

})();