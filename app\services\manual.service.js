(function () {
    'use strict';

    angular
        .module('app.services')
        .factory('publicationService', publicationService);

    publicationService.$inject = ['$http', 'apiConstants', 'manufactureManualService', 'userService'];

    function publicationService($http, apiConstants, manufactureManualService, userService) {
        return {
            getManual: getManual,
            getKits: getKits,
            getTechDocs: getTechDocs,
            getVideos: getVideos,
            getAllPublications: getAllPublications,
            getAssignedPublicationIdsForSubEntity: getAssignedPublicationIdsForSubEntity,
            assignPublicationsToCustomer: assignPublicationsToCustomer,
            fetchPublications: fetchPublications,
            publishUnpublishManual: publishUnpublishManual,
            publishManual: publishManual
        };

        function getManual(manualId) {
            return $http.get(apiConstants.url + '/manual/' + manualId);
        }

        function getKits(manualId) {
            var isStockWarehousesEnabled = userService.getStockWarehousesEnabled();
            var warehouseId = userService.getWarehouseId();
            var url = apiConstants.url + '/manual/' + manualId + '/kits';
            if (warehouseId && isStockWarehousesEnabled) {
                url += '?warehouseId=' + warehouseId;
            }
            return $http.get(url);
        }

        function getTechDocs(manualId) {
            return $http.get(apiConstants.url + '/manual/' + manualId + '/techDocs');
        }

        function getVideos(manualId) {
            return $http.get(apiConstants.url + '/manual/' + manualId + '/video');
        }

        function getAllPublications(manufacturerId) {
            return $http.get(apiConstants.url + '/manufacturer/' + manufacturerId + '/manualDetails');
        }

        function getAssignedPublicationIdsForSubEntity(manufacturerSubEntityId) {
            return $http.get(apiConstants.url + '/manufacturersubentity/' + manufacturerSubEntityId + '/assignedManualIds?published=false');
        }

        function assignPublicationsToCustomer(manufacturerSubEntityId, publicationIds) {
            return $http.put(apiConstants.url + '/manufacturersubentity/' + manufacturerSubEntityId + '/manuals/assign', publicationIds);
        }

        function fetchPublications() {
            return manufactureManualService.fetchPublications();
        }

        function publishManual(manualId) {
            return $http.put(apiConstants.url + '/manual/' + manualId + '/status', { status: 'PUBLISHED' });
        }

        function publishUnpublishManual(publications) {
            var publicationsCopy = angular.copy(publications);
            var updatedStatus = publications.status === 'UNPUBLISHED' ? 'PUBLISHED' : 'UNPUBLISHED';
            manufactureManualService.publishUnpublishManual(publicationsCopy.id, publicationsCopy.status, updatedStatus)
                .then(function (response) {
                    publications.status = updatedStatus;
                    fetchPublications(); // Call fetchPublications() here
                })
                .catch(function (error) {
                    console.log(error);
                });
        }



    }
})();
