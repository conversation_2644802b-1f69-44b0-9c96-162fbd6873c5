(function () {
    'use strict';

    angular
        .module('app.services')
        .factory('securityService', securityService);

    securityService.$inject = ['$http', 'apiConstants', 'userService', '$q'];

    function securityService($http, apiConstants, userService, $q) {
        return {
            getPermissions: getPermissions,
            updatePermissions: updatePermissions,
            getManufacturerUsers: getManufacturerUsers,
            saveWatermarkSettings: saveWatermarkSettings,
            getWatermarkSettings: getWatermarkSettings,
            getCustomerWatermarkSettings: getCustomerWatermarkSettings,
            updateWatermarkSettings: updateWatermarkSettings
        };

        function getPermissions() {
            var manufacturerId = userService.getManufacturerId();
            return $http.get(apiConstants.url + '/security/manufacturer/' + manufacturerId);
        }

        function updatePermissions(permissionData) {
            var manufacturerId = userService.getManufacturerId();
            return $http.post(apiConstants.url + '/security/manufacturer/' + manufacturerId, permissionData);
        }

        function getManufacturerUsers() {
            var manufacturerId = userService.getManufacturerId();
            return $http.get(apiConstants.url + '/user/manufacturer/' + manufacturerId);
        }

        function saveWatermarkSettings(watermarkSettings) {
            var manufacturerId = userService.getManufacturerId();
            return $http.post(apiConstants.url + '/security/manufacturer/' + manufacturerId + '/watermark', watermarkSettings);
        }

        function updateWatermarkSettings(watermarkSettings) {
            var manufacturerId = userService.getManufacturerId();
            return $http.put(apiConstants.url + '/security/manufacturer/' + manufacturerId + '/watermark', watermarkSettings);
        }


        function getWatermarkSettings() {
            var manufacturerId = userService.getManufacturerId();
            return $http.get(apiConstants.url + '/security/manufacturer/' + manufacturerId + '/watermark');
        }

        function getCustomerWatermarkSettings(modelId) {
            var subEntityId = userService.getManufacturerSubEntityId();
            return $http.get(apiConstants.url + '/security/manufacturerSubEntity/' + subEntityId + '/watermark');
        }

    }
})();
