(function () {
    'use strict';

    angular
        .module('app.shared')
        .controller('OrderConfirmModalController', OrderConfirmModalController);

    OrderConfirmModalController.$inject = ['$uibModalInstance', '$uibModal', '$translate', '$state', 'dataObject', 'headerBannerService', 'awsS3Service', 'ordersService'];

    function OrderConfirmModalController($uibModalInstance, $uibModal, $translate, $state, dataObject, headerBannerService, awsS3Service, ordersService) {

        var vm = this;

        vm.shipPartialOrder = shipPartialOrder;
        vm.shipFullOrder = shipFullOrder;
        vm.cancel = $uibModalInstance.dismiss;

        getTranslations()
        if (dataObject) {
            vm.estDate = dataObject.estDate;
            vm.estimatedDeliveryDate = dataObject.estimatedDeliveryDate;
            vm.order = dataObject.order;
        }

        var WENT_WRONG, MARKED_AS_SHIPPED, MARKED_AS_SHIPPED_PARTIALLY;

        function getTranslations() {
            $translate(['LIVE_ORDER.MARKED_AS_SHIPPED', 'GENERAL.WENT_WRONG', 'LIVE_ORDER.MARKED_AS_SHIPPED_PARTIALLY'])
                .then(function (resp) {
                    MARKED_AS_SHIPPED = resp["LIVE_ORDER.MARKED_AS_SHIPPED"];
                    MARKED_AS_SHIPPED_PARTIALLY = resp["LIVE_ORDER.MARKED_AS_SHIPPED_PARTIALLY"];
                    WENT_WRONG = resp["GENERAL.WENT_WRONG"];
                });
        }

        function shipPartialOrder() {
            var order = vm.order;
            var shipableOrderItems = [];
            for (var i = 0; i < order.orderItems.length; i++) {
                if (order.orderItems[i].quantity > 0) {
                    var item = {
                        orderItemId: order.orderItems[i].orderItemId,
                        partNumber: order.orderItems[i].partNumber,
                        partDescription: order.orderItems[i].partDescription,
                        quantity: order.orderItems[i].quantity,
                        shippedOrderItems: order.orderItems[i].shippedOrderItems
                    };
                    shipableOrderItems.push(item);
                }
            }

            var partsObject = {
                orderItems: shipableOrderItems,
                orderId: order.orderId,
                displayId: order.customOrderDisplay
            };
            $uibModal.open({
                templateUrl: 'features/orders/live/partialShipOrder/partialShipOrder.html',
                controller: 'PartialShipOrderController',
                controllerAs: 'partialShipOrderCtrl',
                resolve: {
                    partsObject: function () {
                        return partsObject;
                    }
                }
            }).result.then(function (response) {
                ordersService.partialShipOrderItems(order.orderId, response.parts, response.shippingDate, response.invoiceURL)
                    .then(updatePartialOrderSuccess(false), updateOrderFailed)
            }, function () {
                vm.isEstimatedDeliveryDateEditable = false;
                console.log('Modal Cancelled');
            });
        }

        function shipFullOrder() {
            $uibModal.open({
                templateUrl: 'features/orders/live/fullShipOrder/fullShipOrderModal.html',
                controller: 'FullShipOrderModalController',
                controllerAs: 'fullShipOrderModalCtrl',
                size: 'sm',
                resolve: {
                    dataObject: function () {
                        return dataObject;
                    }
                }
            }).result.then(function (response) {
                if (response.invoiceURL && response.invoiceURL.length > 0) {
                    vm.order.invoiceUrl = response.invoiceURL;
                }

                vm.order.estimatedDeliveryDate = ordersService.createDateObject(response.shippingDate);
                vm.isEstimatedDeliveryDateEditable = false;
                vm.order.orderStatus = "SHIPPED";
                vm.order.price = vm.total;
                ordersService.updateOrder(vm.order)
                    .then(updateOrderSuccess, updateOrderFailed);
            }, function () {
                vm.isEstimatedDeliveryDateEditable = false;
                console.log('Modal Cancelled');
            });
        }

        function updateOrderSuccess() {
            headerBannerService.setNotification('SUCCESS', MARKED_AS_SHIPPED, 10000);
            $uibModalInstance.close();
        }

        function updatePartialOrderSuccess() {
            headerBannerService.setNotification('SUCCESS', MARKED_AS_SHIPPED_PARTIALLY, 10000);
            $uibModalInstance.close();
        }

        function updateOrderFailed() {
            headerBannerService.setNotification('ERROR', WENT_WRONG, 10000);
            $uibModalInstance.close();
        }


    }
})();
