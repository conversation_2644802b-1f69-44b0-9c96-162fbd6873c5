(function () {
    'use strict';

    angular
        .module('app.shared')
        .controller('Delete<PERSON>ontroller', DeleteController);

    DeleteController.$inject = ['$uibModalInstance','deleteObject','deleteService'];

    function DeleteController($uibModalInstance,deleteObject,deleteService) {
        
    	var vm = this;
    	vm.commonDelete = commonDelete;
  	  	vm.cancel = $uibModalInstance.dismiss;
  	  	if(deleteObject){
  	  		vm.name = deleteObject.name;
  	  		vm.id = deleteObject.id;
  	  		vm.url = deleteObject.url;
            vm.linkedPartsCount = deleteObject.linkedPartsCount
  	  	}
  	  	
	  	function commonDelete() {
	  		deleteService.commonDelete(vm.url)
				.then(deleteSuccess, deleteFailure);
	    }

	    function deleteSuccess() {
            $uibModalInstance.close();
		}

        function deleteFailure(error) {
            console.log(error);
        }
    }
})();
