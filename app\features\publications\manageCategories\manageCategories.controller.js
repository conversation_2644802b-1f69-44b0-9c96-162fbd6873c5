(function () {
    'use strict';

    angular
        .module('app.publications')
        .controller('ManageCategoriesController', ManageCategoriesController);

    // Inject necessary dependencies: $uibModalInstance, publicationService, and the resolved existingCategories
    ManageCategoriesController.$inject = ['$uibModalInstance', 'publicationService', 'existingCategories', '$timeout', '$translate'];

    function ManageCategoriesController($uibModalInstance, publicationService, existingCategories, $timeout, $translate) {
        var vm = this;

        // --- View Model ---
        vm.existingCategories = existingCategories;
        vm.newCategoryName = '';
        vm.isLoading = false;
        vm.errorMessage = '';

        // --- Translation Variables ---
        var CATEGORY_IN_USE;
        $translate(['CREATE_PUBLICATION.CATEGORY_IN_USE'])
            .then(function (resp) {
                CATEGORY_IN_USE = resp["CREATE_PUBLICATION.CATEGORY_IN_USE"];
            });

        // --- Functions ---
        vm.addCategory = addCategory;
        vm.deleteCategory = deleteCategory; 
        vm.startCategoryEdit = startCategoryEdit;
        vm.saveCategoryEdit = saveCategoryEdit;
        vm.cancelCategoryEdit = cancelCategoryEdit;
        vm.cancel = cancel;

        initialize();

        function initialize() {
            loadCategories();
        }

        // --- Function Implementations ---

        function addCategory() {
            if (!vm.newCategoryName) {
                return; // Do nothing if input is empty
            }
            vm.isLoading = true;
            vm.errorMessage = ''; // Clear previous errors

            publicationService.createPublicationCategory(vm.newCategoryName)
                .then(function (response) {
                    // Add the new category at the top of the list with animation
                    var newCategory = {
                        id: response.data.id || response.data.publicationCategoryId,
                        name: vm.newCategoryName,
                        isNew: true // Flag to trigger animation
                    };
                    
                    // Add to the beginning of the array
                    vm.existingCategories.unshift(newCategory);
                    
                    // Remove the animation flag after animation completes
                    $timeout(function() {
                        newCategory.isNew = false;
                    }, 1000);
                    
                    vm.newCategoryName = '';
                })
                .catch(function (error) {
                    console.error("Error adding publication category:", error);
                    vm.errorMessage = "Failed to add category. Please try again."; 
                })
                .finally(function () {
                    vm.isLoading = false;
                });
        }

        function deleteCategory(category) {
    if (!confirm("Are you sure you want to delete the category '" + category.name + "'? This action cannot be undone.")) {
        return;
    }

    vm.isLoading = true;
    vm.errorMessage = '';

    publicationService.deletePublicationCategory(category.id)
        .then(function () {
            return loadCategories();
        })
        .catch(function (error) {
            console.error("Error deleting publication category:", error);
            if (error.status === 422) {
                vm.errorMessage = CATEGORY_IN_USE;
            } else {
                vm.errorMessage = error.data && error.data.error ? error.data.error : "Failed to delete category. Please try again.";
            }
        })
        .finally(function () {
            vm.isLoading = false;
        });
}

function startCategoryEdit(categoryToEdit) {
    vm.existingCategories.forEach(function(cat) {
        if (cat !== categoryToEdit) {
            cat.editing = false;
            delete cat.editName;
        }
    });

    categoryToEdit.editName = angular.copy(categoryToEdit.name);
    categoryToEdit.editing = true;
    vm.errorMessage = '';
}

function saveCategoryEdit(category) {
    if (!category.editName || category.editName === category.name) {
        cancelCategoryEdit(category);
        return;
    }

    vm.isLoading = true;
    vm.errorMessage = '';

    publicationService.updatePublicationCategory(category.id, category.editName)
        .then(function () {
            category.name = category.editName;
            category.editing = false;
            delete category.editName;
        })
        .catch(function (error) {
            console.error("Error updating publication category:", error);
            vm.errorMessage = "Failed to update category. Please try again.";
        })
        .finally(function () {
            vm.isLoading = false;
        });
}

function cancelCategoryEdit(category) {
    category.editing = false;
    delete category.editName;
    vm.errorMessage = '';
}

        // Function to reload categories from the service
        function loadCategories() {
            vm.isLoading = true;
            vm.errorMessage = '';
            return publicationService.getPublicationCategories()
                .then(function (response) {
                     if (response && response.data && response.data.publicationCategories) {
                        vm.existingCategories = response.data.publicationCategories;
                    } else {
                         console.warn('Unexpected response structure for publication categories:', response);
                         vm.existingCategories = Array.isArray(response.data) ? response.data : [];
                    }
                })
                .catch(function (error) {
                    console.error("Error fetching publication categories:", error);
                    vm.errorMessage = "Failed to load categories.";
                    vm.existingCategories = []; // Ensure empty array on error
                })
                .finally(function() {
                    vm.isLoading = false;
                });
        }

        function cancel() {
            $uibModalInstance.dismiss('cancel');
        }
    }
})();