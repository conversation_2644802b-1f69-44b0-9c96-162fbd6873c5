(function () {
    'use strict';

    angular
    .module('app.customer')
    .controller('DPDeleteCustomerCompanyController', DPDeleteCustomerCompanyController);

    DPDeleteCustomerCompanyController.$inject = ['dpCreateUserService', '$uibModalInstance', 'companyDetails'];

    function DPDeleteCustomerCompanyController(dpCreateUserService, $uibModalInstance, companyDetails) {
        var vm = this;

        vm.cancel = $uibModalInstance.dismiss;
        vm.companyName = companyDetails.name;
        vm.manufacturerSubEntityId = companyDetails.manufacturerSubEntityId;

        vm.deleteCompany = deleteCompany;

        function deleteCompany() {
            vm.isDisabled = true;

            dpCreateUserService.deleteManufacturerSubEntity(vm.manufacturerSubEntityId)
            .then(deleteCustomerSuccess, deleteCustomerFailed);
        }

        function deleteCustomerSuccess() {
            $uibModalInstance.close();
        }

        function deleteCustomerFailed(response) {
            vm.companyFailure = true;
            vm.internalFailureMessage = response.data.message;
        }

    }
})();