<section class="body-content">
  <div class="order-details-holder">
    <p class="mb-4"><a href="" class="dark-secondary" ng-click="dpCustomerUserCtrl.goToCustomers()"><i class="fa fa-caret-left"></i> {{"CUSTOMERS.BACK_TO_ALL_CUSTOMERS" | translate}}</a></p>
    <h1>{{dpCustomerUserCtrl.subEntityname}} {{"CUST_USER.TITLE" | translate}}</h1>
    <div translate>CUST_USER.SUBTITLE</div>
  </div>
</section>

  <section class="responsiveContainer m-5">
    <div class="">

      <div class="success-alert" ng-if="customerCtrl.successMessage != ''">
        {{customerCtrl.successMessage}}
      </div>

      <div class="flex p-4 p-md-0">

        <search-filter class="col-12 col-md-3" state-name="'dpCustomersUsers'" value="dpCustomerUserCtrl.searchValue" placeholder-key="CUST_USER.SEARCH_BY_NAME"></search-filter>

        <button class="btn primary ml-auto mr-4 col-12 col-md-auto create-machine mt-3 mt-md-0"
                ng-click="dpCustomerUserCtrl.createUser()" translate>CUST_USER.ADD_USER
        </button>

    </div>

  <table class="table table-bordered">

    <thead>
    <tr>
      <th ng-class="dpCustomerUserCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'" ng-click="dpCustomerUserCtrl.customer_sort='firstName'; dpCustomerUserCtrl.sortReverse = !dpCustomerUserCtrl.sortReverse" translate>CUST_USER.NAME</th>
      <th ng-class="dpCustomerUserCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'"
          ng-click="dpCustomerUserCtrl.customer_sort='emailAddress'; dpCustomerUserCtrl.sortReverse = !dpCustomerUserCtrl.sortReverse" translate>CUST_USER.EMAIL</th>
      <th ng-class="dpCustomerUserCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'"
          ng-click="dpCustomerUserCtrl.customer_sort='createdDate'; dpCustomerUserCtrl.sortReverse = !dpCustomerUserCtrl.sortReverse" translate>CUST_USER.CREATED_DATE</th>

      <th ng-class="dpCustomerUserCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'"
          ng-click="dpCustomerUserCtrl.customer_sort='passwordSet'; dpCustomerUserCtrl.sortReverse = !dpCustomerUserCtrl.sortReverse" translate>CUST_USER.USER_STATUS</th>
      <th translate>CUST_USER.ACTIONS</th>
    </tr>
    </thead>

    <tbody>
    <tr class="p-4" ng-repeat="user in dpCustomerUserCtrl.userList | orderBy:dpCustomerUserCtrl.customer_sort:dpCustomerUserCtrl.sortReverse | filter : dpCustomerUserCtrl.searchValue" ng-class="{'last-item': $last}"
        ng-show="dpCustomerUserCtrl.userList.length > 0">
      <td data-label="{{'CUST_USER.NAME' | translate}}">{{user.firstName}} {{user.lastName}}</td>
      <td data-label="{{'CUST_USER.EMAIL' | translate}}">{{user.emailAddress}}</td>
      <td data-label="{{'CUST_USER.CREATED_DATE' | translate}}">{{user.createdDate | date : "d MMMM y"}}</td>
      <td data-label="{{'CUST_USER.USER_STATUS' | translate}}">{{user.passwordSet == true ? 'Active' : 'Registration Incomplete' }}</td>

      <td class="has-dropdown mobile-right-aligned-btn" ng-if="!dpCustomerUserCtrl.isCDEUser">
        <div class="btn-group" ng-hide="user.userId === adminCtrl.userId">
          <a href="" class="btn xsmall secondary main-action" ng-click="dpCustomerUserCtrl.editUser(user)" translate>CUST_USER.EDIT_USER</a>
          <div href="" class="btn xsmall secondary dropdown-toggle" data-toggle="dropdown"
               aria-haspopup="true" aria-expanded="false">
            <div class="sub-popup">
              <ul class="more-options">
                <li title="Edit User">
                  <a href="" class="dark-secondary" ng-click="dpCustomerUserCtrl.editUser(user)"><i class="fa fa-fw fa-pencil"></i> {{"CUST_USER.EDIT" | translate}}</a>
                </li>
                <li title="Reset Password">
                  <a href="" class="dark-secondary" ng-click="dpCustomerUserCtrl.resetPassword(user)"><i class="fa fa-fw fa-key"></i> {{"CUST_USER.RESET_PASS" | translate}}</a>
                </li>
                <li title="Create Order">
                  <a href="" class="dark-secondary" ng-if="dpCustomerUserCtrl.userList[0].userPermissions.indexOf('Order') !== -1" ng-click="dpCustomerUserCtrl.createOrder(user)"><i class="fa fa-fw fa-user"></i> {{"CUST_USER.CREATE_ORDER" | translate}}</a>
                </li>
                <li title="Delete">
                  <a href="" class="delete" ng-click="dpCustomerUserCtrl.deleteUser(user)"><i class="fa fa-fw fa-trash"></i> {{"CUST_USER.DELETE" | translate}}</a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </td>
      <td ng-if="dpCustomerUserCtrl.isCDEUser">
        <button class="btn primary" ng-click="dpCustomerUserCtrl.createOrder(user)" translate>
          CUST_USER.CREATE_ORDER
        </button>
      </td>



    </tr>

    <tr ng-show="!dpCustomerUserCtrl.userList.length > 0">
      <td colspan="5" translate>CUST_USER.NO_USERS</td>
    </tr>

    <tr ng-hide="dpCustomerUserCtrl.areUsersLoaded" align="center">
      <td class="preloader" colspan="4"><img ng-src="images/cadpreloader.gif" class="ajax-loader" height="60" width="60"/></td>
    </tr>
    </tbody>
  </table>

  <div ng-show="dpCustomerUserCtrl.totalItems > 0">
    <div class="text-right">
      <uib-pagination force-ellipses="true" max-size="4" total-items="dpCustomerUserCtrl.totalItems" ng-model="dpCustomerUserCtrl.currentPage"
                      items-per-page="dpCustomerUserCtrl.itemPerPage" ng-change="dpCustomerUserCtrl.pageChanged()" boundary-links="true" class="pagination-md"
                      previous-text="&lsaquo;" next-text="&rsaquo;" first-text="&laquo;" last-text="&raquo;"></uib-pagination>
    </div>
  </div>
  </div>
</section>

