<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" ng-click="createManualCtrl.cancel()" aria-label="Close">
        <i class="fa fa-close" aria-hidden="true"></i>
    </button>
    <h2 class="modal-title" ng-if="!createManualCtrl.isEdit" translate>CREATE_MANUAL.CREATE_NEW</h2>
    <h2 class="modal-title" ng-if="createManualCtrl.isEdit" translate>CREATE_MANUAL.EDIT_MANUAL</h2>
</div>

<div class="modal-body">
    <form name="createManualForm" class="form">
        <h3 class="group-title" translate>CREATE_MANUAL.MODEL_DETAILS</h3>

        <div class="input-group">
            <label translate>CREATE_MANUAL.SELECT_RANGE</label>
            <div class="d-flex w-100 align-items-center">
                <div class="select-box">
                    <small ng-if="!createManualCtrl.data.rangeId" translate>CREATE_MANUAL.SELECT_RANGE</small>
                    <select
                        ng-options="rangeValue.rangeId as rangeValue.name for rangeValue in createManualCtrl.rangeValues | orderBy:'name'"
                        ng-model="createManualCtrl.data.rangeId"
                        ng-change="createManualCtrl.rangeChanged(createManualCtrl.data.rangeId)"
                    ></select>
                    <div class="select-arrow"></div>
                </div>
            </div>
        </div>

        <div class="input-group">
            <label translate>CREATE_MANUAL.SELECT_MACHINE</label>
            <div class="d-flex w-100 align-items-center">
                <div class="select-box" ng-class="createManualCtrl.machineDropdownClass">
                    <small ng-if="!createManualCtrl.data.machineId" translate>CREATE_MANUAL.SELECT_MACHINE</small>
                    <select
                        ng-options="machine.machineId as machine.name for machine in createManualCtrl.machines | orderBy: 'name'"
                        ng-model="createManualCtrl.data.machineId"
                        ng-change="createManualCtrl.machineChanged(createManualCtrl.data.machineId)"
                        ng-disabled="createManualCtrl.isMachineDropdownDisabled"
                    ></select>
                    <div class="select-arrow"></div>
                </div>
            </div>
        </div>

        <h3 class="group-title" translate>CREATE_MANUAL.FEATURED_VIEWABLE</h3>

        <div class="input-group">
            <label translate>CREATE_MANUAL.FEATURED_VIEWABLE</label>
            <div class="d-flex w-100 align-items-center">
                <div class="select-box" ng-class="createManualCtrl.featuredViewableDropdownClass">
                    <small ng-if="!createManualCtrl.data.featuredModelId" translate>CREATE_MANUAL.SELECT_FEATURED_VIEWABLE</small>
                    <select
                        ng-options="model.id as model.label for model in createManualCtrl.viewables | orderBy: 'label'"
                        ng-model="createManualCtrl.data.featuredModelId"
                        ng-change="createManualCtrl.featuredViewableChanged()"
                        ng-disabled="createManualCtrl.isFeaturedViewableDropdownDisabled"
                    ></select>
                    <div class="select-arrow"></div>
                </div>
            </div>
        </div>

        <div class="mb-3">
            <div class="input-group">
                <label translate>CREATE_MANUAL.ADD_FEATURED</label>
                <div ng-show="createManualCtrl.featuredModelUrl != null">
                    <img ng-src="{{createManualCtrl.featuredModelUrl}}" />
                </div>
            </div>
            <div class="d-flex align-items-center">
                <div class="border-right pr-3">
                    <button type="button" class="btn small secondary" ng-click="createManualCtrl.addFeaturedImage()" translate>
                        CREATE_MANUAL.ADD_IMAGE
                    </button>
                </div>
                <div>
                    <div class="input-group mb-0 pl-3">
                        <input
                            class="inp-chBox"
                            id="chBox"
                            type="checkbox"
                            ng-model="createManualCtrl.useViewableImage"
                            style="display: none"
                        />
                        <label class="chBox m-0 d-flex align-items-center" for="chBox">
                            <span>
                                <svg width="12px" height="10px" viewBox="0 0 12 10">
                                    <polyline points="1.5 6 4.5 9 10.5 1"></polyline>
                                </svg>
                            </span>
                            <span translate>CREATE_MANUAL.PUBLICATION_THUMBNAIL</span>
                        </label>
                    </div>
                    <div></div>
                </div>
            </div>
        </div>

        <h3 class="group-title" translate>CREATE_MANUAL.VIEWABLE_DETAILS</h3>
        <div class="input-group">
            <div
                class="select-check-box multicheckbox"
                ng-dropdown-multiselect
                options="createManualCtrl.modelSelect"
                selected-model="createManualCtrl.models"
                extra-settings="createManualCtrl.multiCheckdownSettings"
                translation-texts="createManualCtrl.modelTextSettings"
                disabled="createManualCtrl.isModelsDropdownDisabled"
                ng-click="createManualCtrl.modelsSelected()"
            ></div>
        </div>

        <h3 class="group-title" translate>CREATE_MANUAL.TECH_DOC_DETAILS</h3>
        <div class="input-group">
            <div
                class="select-check-box multicheckbox"
                ng-dropdown-multiselect
                options="createManualCtrl.techDocSelect"
                selected-model="createManualCtrl.techDocs"
                extra-settings="createManualCtrl.multiCheckdownSettings"
                translation-texts="createManualCtrl.techDocTextSettings"
                ng-click="createManualCtrl.techDocsSelected()"
            ></div>
        </div>

        <h3 class="group-title" translate>CREATE_MANUAL.VIDEO_DETAILS</h3>
        <div class="input-group">
            <div
                class="select-check-box multicheckbox"
                ng-dropdown-multiselect
                options="createManualCtrl.videoSelect"
                selected-model="createManualCtrl.videos"
                extra-settings="createManualCtrl.multiCheckdownSettings"
                translation-texts="createManualCtrl.videoTextSettings"
                ng-click="createManualCtrl.videosSelected()"
            ></div>
        </div>

        <h3 class="group-title" translate>CREATE_MANUAL.KIT_DETAILS</h3>
        <div class="input-group">
            <div
                class="select-check-box multicheckbox"
                ng-dropdown-multiselect
                options="createManualCtrl.kitSelect"
                selected-model="createManualCtrl.kits"
                extra-settings="createManualCtrl.multiCheckdownSettings"
                translation-texts="createManualCtrl.kitTextSettings"
                ng-click="createManualCtrl.kitsSelected()"
            ></div>
        </div>

        <h3 class="group-title" translate>CREATE_MANUAL.MANUAL_DETAILS</h3>

        <div class="input-group">
            <label translate>CREATE_MANUAL.MANUAL_NAME</label>
            <input
                type="text"
                placeholder="{{'CREATE_MANUAL.ENTER_MANUAL_NAME' | translate}}"
                ng-required="true"
                ng-model="createManualCtrl.manualName"
            />
        </div>

        <div class="input-group">
            <label translate>CREATE_MANUAL.SERIAL_NUMBER</label>
            <input type="text" placeholder="{{'CREATE_MANUAL.ENTER_SERIAL' | translate}}" ng-model="createManualCtrl.serialNumber" />
        </div>

        <div class="input-group">
             <label translate>CREATE_MANUAL.ASSIGN_TO_CUST</label>
             <div class="dropdown w-100">
                 <button class="btn btn-default dropdown-toggle" type="button"
                         id="dropdownMenuButton" data-toggle="dropdown"
                         aria-haspopup="true" aria-expanded="false" style="width: 100%;">
                     <span ng-if="createManualCtrl.selectedCustomers.length === 0" translate>CREATE_MANUAL.SELECT_CUSTOMERS</span>
                     <span ng-if="createManualCtrl.selectedCustomers.length > 0 && createManualCtrl.selectedCustomers.length <= 3">
        {{ createManualCtrl.getSelectedCustomerLabels() }}
    </span>
                     <span ng-if="createManualCtrl.selectedCustomers.length > 3">
                 {{ createManualCtrl.selectedCustomers.length }} <span translate>CREATE_MANUAL.CUSTOMERS</span>
             </span>
                 </button>
                 <div class="dropdown-menu" aria-labelledby="dropdownMenuButton"
                      style="width: 100%; height: 200px; overflow-y: auto;">
                     <button class="d-flex align-items-center dropdown-item" type="button" ng-click="createManualCtrl.toggleSelectAll($event)">
                         <input class="mb-0 mr-3" type="checkbox" ng-checked="createManualCtrl.isAllSelected()">
                         {{'CREATE_MANUAL.SELECT_ALL' | translate}}
                     </button>
                     <div class="dropdown-divider"></div>
                     <div class="d-flex align-items-center dropdown-item"
                          ng-repeat="customer in createManualCtrl.allCustomers track by customer.id"
                          ng-click="createManualCtrl.handleCustomerClick(customer, $event)">
                         <input class="mb-0 mr-3" type="checkbox" ng-checked="createManualCtrl.isSelected(customer)">
                         <label class="cursorPointer mb-0">{{ customer.label }}</label>
                     </div>

                 </div>
             </div>
         </div>

        <div class="flex justify-content-end">
            <button type="button" class="btn secondary" ng-click="createManualCtrl.cancel()" translate>GENERAL.CANCEL</button>

            <button
                type="button"
                class="ml-3 btn primary"
                ng-if="!createManualCtrl.isEdit"
                ng-disabled="!createManualForm.$valid || (createManualCtrl.models.length === 0 && createManualCtrl.techDocs.length === 0 && createManualCtrl.videos.length === 0 && createManualCtrl.kits.length === 0)"
                ng-click="createManualCtrl.createManual()"
                translate
            >
                CREATE_MANUAL.CREATE_MANUAL
            </button>

            <button
                type="button"
                class="ml-3 btn primary"
                ng-if="createManualCtrl.isEdit"
                ng-disabled="!createManualForm.$valid || (createManualCtrl.models.length === 0 && createManualCtrl.techDocs.length === 0 && createManualCtrl.videos.length === 0 && createManualCtrl.kits.length === 0)"
                ng-click="createManualCtrl.updateManual()"
                translate
            >
                CREATE_MANUAL.EDIT_MANUAL
            </button>
        </div>
    </form>
</div>
