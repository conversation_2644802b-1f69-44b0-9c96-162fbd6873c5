(function () {
    'use strict';

    angular
        .module('app.viewable')
        .controller('SoftCopyController', SoftCopyController);

    SoftCopyController.$inject = ['viewableService', '$stateParams', '$uibModal', '$interval', '$scope', '$state', '$filter', 'modelService', 'softCopyService', 'pdfMakerService'];

    function SoftCopyController(viewableService, $stateParams, $uibModal, $interval, $scope, $state, $filter, modelService, softCopyService, pdfMakerService) {

        var vm = this;

        vm.modelName = $stateParams.modelName;
        vm.modelId = $stateParams.modelId;
        vm.model = {};
        vm.machineName = $stateParams.machineName;
        vm.softCopiesList = [];
        vm.isSoftCopiesLoaded = false;
        vm.count = 8;
        vm.currentPage = 1;
        vm.itemPerPage = 8;
        vm.start = 0;
        vm.endRecord = vm.itemPerPage;
        vm.sortReverse = false;
        vm.isHeaderListLoaded = false;
        vm.missingTranslationWarnings = [];
        vm.isOpened = false;
        vm.showLoader = false;

        vm.pageChanged = pageChanged;
        vm.backToModels = backToModels;
        vm.openSoftCopyViewerPage = openSoftCopyViewerPage;
        vm.createSoftCopy = createSoftCopy;
        vm.editSoftCopy = editSoftCopy;
        vm.deleteSoftCopy = deleteSoftCopy;
        vm.exportSoftCopy = exportSoftCopy;

        vm.displayAdvancedFilter = false;
        vm.filterValue = {};

        vm.createdByUsers = [''];
        vm.filter_createdBy = "";
        vm.statuses = [''];
        vm.filter_status = "";

        vm.toggleAdvancedFilter = toggleAdvancedFilter;
        vm.searchFilterChange = searchFilterChange;
        vm.applyFilter = applyFilter;
        vm.clearFilter = clearFilter;

        vm.sortReverse = true;
        vm.viewable_sort = 'modelId';

        initialize();

        function initialize() {
            fetchModel();
            fetchSoftCopies();
        }

        function fetchModel() {
            modelService.fetchModel(vm.modelId)
                .then(fetchModelSuccess)
                .catch(fetchModelFailed);
        }

        function fetchModelSuccess(response) {
            vm.model = response.data;
        }


        function fetchModelFailed(error) {
            console.log(error);
            //TODO add proper error handling
        }

        function fetchSoftCopies() {
            softCopyService.fetchSoftCopies(vm.modelId)
                .then(fetchSoftCopySuccess)
                .catch(fetchSoftCopyFailed);
        }

        function fetchSoftCopySuccess(response) {
            vm.softCopiesList = response.data;
            vm.isSoftCopiesLoaded = true;
        }

        function fetchSoftCopyFailed(error) {
            console.log(error);
            vm.isSoftCopiesLoaded = true;
            //TODO add proper error handling
        }

        function backToModels() {
            $state.go('productsModels', {
                productId: $stateParams.productId,
                machineName: vm.machineName
            });
        }

        vm.sortBy = [
            {name: 'modelName', value: 'Model Name'},
            {name: 'filename', value: 'File Name'},
            {name: 'autodeskStatus', value: 'Model Status'},
            {name: 'createdDate', value: 'Create Date'},
            {name: 'createdByUserFirstName', value: 'Created By'}
        ];

        function pageChanged() {
            vm.start = ((vm.currentPage - 1) * vm.itemPerPage);
        }

        function openSoftCopyViewerPage(softCopy) {
            $state.go('softCopyViewer', {
                productId: $stateParams.productId,
                machineName: vm.machineName,
                modelId: vm.modelId,
                modelName: vm.modelName,
                viewableId: softCopy.viewableId,
                viewableName: softCopy.name,
                autodeskURN: vm.model.autodeskUrn,
                translateType: vm.model.translateType,
                softCopyId: softCopy.id
            })
        }

        function createSoftCopy() {
            $uibModal.open({
                templateUrl: 'features/softCopy/createSoftCopyModal/createSoftCopy.html',
                controller: 'CreateSoftCopyController',
                controllerAs: 'createSoftCopyCtrl',
                size: 'md',
                resolve: {
                    model: function () {
                        return vm.model;
                    }
                }
            }).result.then(function () {
                fetchSoftCopies();
            });
        }

        function editSoftCopy(softCopy) {
            $uibModal.open({
                templateUrl: 'features/softCopy/editSoftCopyModal/editSoftCopy.html',
                controller: 'EditSoftCopyController',
                controllerAs: 'editSoftCopyCtrl',
                size: 'md',
                resolve: {
                    softCopy: function () {
                        return softCopy;
                    }
                }
            }).result.then(function () {
                fetchSoftCopies();
            });
        }

        function deleteSoftCopy(softCopy) {
            $uibModal.open({
                templateUrl: 'features/softCopy/deleteSoftCopyModal/deleteSoftCopy.html',
                controller: 'DeleteSoftCopyController',
                controllerAs: 'deleteSoftCopyCtrl',
                size: 'md',
                resolve: {
                    softCopy: function () {
                        return softCopy;
                    }
                }
            }).result.then(function () {
                fetchSoftCopies();
            });
        }

        function exportSoftCopy(softCopy) {
            showSpinner();
            pdfMakerService.createSoftCopyPdf(softCopy);
        }

        function toggleAdvancedFilter() {
            vm.displayAdvancedFilter = !vm.displayAdvancedFilter;
        }

        function searchFilterChange() {
            updateTotalItemCount();
        }

        function updateTotalItemCount() {
            var textFilter = $filter('filter')(vm.softCopiesList, vm.searchValue);
            vm.totalItems = $filter('filter')(textFilter, vm.filterValue, true).length;
        }

        function applyFilter() {
            vm.filterValue = {};

            if (vm.filter_createdBy) {
                vm.filterValue.createdByUserFullName = vm.filter_createdBy;

            }
            if (vm.filter_status) {
                vm.filterValue.autodeskStatusDisplay = vm.filter_status;
            }

            updateTotalItemCount();
        }

        function clearFilter() {

            vm.filterValue = {};
            vm.totalItems = vm.modelList.length;

            vm.filter_createdBy = vm.createdByUsers[0];
            vm.filter_status = vm.statuses[0];

            var searchBox = document.getElementById("searchInput");
            searchBox.value = "";
            vm.searchValue = "";

            console.log("Filter cleared");
        }

        function showSpinner(){
            vm.showLoader = true;
        }

        $scope.$on("hideSpinner", function(){
            vm.showLoader= false;
            $scope.$apply();
        });

    }
})();
