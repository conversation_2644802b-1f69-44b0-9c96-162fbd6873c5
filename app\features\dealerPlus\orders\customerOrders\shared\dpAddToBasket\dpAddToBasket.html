<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" ng-click="dpAddToBasketCtrl.cancel()"
            aria-label="Close"><i class="fa fa-close" aria-hidden="true"></i>
    </button>
    <h2 class="modal-title">{{"ADD_TO_BASKET.TITLE" | translate}} #{{dpAddToBasketCtrl.displayId}}</h2>
</div>

<div class="modal-body">
    <div class="error-well" ng-if="dpAddToBasketCtrl.errorSplittingOrder">
        <p translate>ADD_TO_BASKET.ERROR</p>
    </div>
    <div class="error-well" ng-if="dpAddToBasketCtrl.quantityMismatchError">
        <p translate>ADD_TO_BASKET.ENTER_VALID_QUANTITY</p>
    </div>
    <div class="error-well" ng-if="dpAddToBasketCtrl.nothingSelectedError">
        <p translate>ADD_TO_BASKET.AT_LEAST_ONE</p>
    </div>


    <p translate>ADD_TO_BASKET.CHOOSE_TO_ADD</p>

    <form class="form">

        <h3 translate>ADD_TO_BASKET.IDENTIFIED_PARTS</h3>
        <table class="table table-bordered">
            <thead>
            <tr>
                <th translate>ADD_TO_BASKET.PART_NO</th>
                <th translate>ADD_TO_BASKET.PART_DESC</th>
                <th translate>ADD_TO_BASKET.QTY</th>
                <th translate>ADD_TO_BASKET.QTY_TO_ADD</th>
                <th translate>ADD_TO_BASKET.ADD</th>
            </tr>
            </thead>

            <tbody>
            <tr ng-repeat="item in dpAddToBasketCtrl.partsList">
                <td>{{item.partNumber}}</td>
                <td>{{item.partDescription}}</td>
                <td>{{item.oldQuantity}}</td>
                <td><input ng-model="item.quantity" type="number" min="0" max="{{item.oldQuantity}}"></td>
                <td><input ng-model="item.isSelected" type="checkbox"></td>
            </tr>

            </tbody>
        </table>
        <br>

        <h3 ng-show="dpAddToBasketCtrl.manualPartsList.length>0" translate>ADD_TO_BASKET.MANUALLY_ADDED_PARTS</h3>
        <table class="table table-bordered" ng-show="dpAddToBasketCtrl.manualPartsList.length>0">
            <thead>
            <tr>
                <th translate>ADD_TO_BASKET.PART_NO</th>
                <th translate>ADD_TO_BASKET.PART_DESC</th>
                <th translate>ADD_TO_BASKET.QTY</th>
                <th translate>ADD_TO_BASKET.QTY_TO_ADD</th>
                <th translate>ADD_TO_BASKET.ADD</th>
            </tr>
            </thead>

            <tbody>
            <tr ng-repeat="manualItem in dpAddToBasketCtrl.manualPartsList">
                <td>{{manualItem.partNumber}}</td>
                <td>{{manualItem.partDescription}}</td>
                <td>{{manualItem.oldQuantity}}</td>
                <td><input ng-model="manualItem.quantity" type="number" min="0" max="{{manualItem.oldQuantity}}"></td>
                <td><input ng-model="manualItem.isSelected" type="checkbox"></td>
            </tr>

            </tbody>
        </table>

        <div class="modal-actions mobileBtnFullWidth">
            <button type="button" class="btn secondary" ng-click="dpAddToBasketCtrl.cancel()" translate>GENERAL.CANCEL</button>

            <button type="button" class="dpGreenModal btn primary-outline"
                    ng-click="dpAddToBasketCtrl.addAllToBasket()" translate>ADD_TO_BASKET.ADD_ALL
            </button>

            <button type="button" class="dpGreenModal btn primary"
                    ng-click="dpAddToBasketCtrl.addToBasket()"  translate>ADD_TO_BASKET.ADD_TO_BASKET
            </button>


        </div>

    </form>
</div>