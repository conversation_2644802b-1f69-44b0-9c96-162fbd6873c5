<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" ng-click="dpCreateCustomerCompanyCtrl.cancel()"
            aria-label="Close"><i
            class="fa fa-close" aria-hidden="true"></i></button>
    <h2 class="modal-title" translate>CREATE_CUSTOMER.TITLE</h2>
    <h3 ng-if="dpCreateCustomerCompanyCtrl.companyFailure" class="error-alert">
        {{dpCreateCustomerCompanyCtrl.internalFailureMessage}}</h3>
                </div>


  <div class="modal-body">
    <form class="form" name="createCompanyForm" ng-submit="dpCreateCustomerCompanyCtrl.createNewCompany()">
        <div class="input-group">
            <label translate>CREATE_CUSTOMER.COMPANY_NAME</label>
                <input type="text" placeholder="{{'CREATE_CUSTOMER.ENTER_COMPANY' | translate}}"
                       ng-model="dpCreateCustomerCompanyCtrl.newCompanyName" ng-required="true">
        </div>

        <div class="input-group">
            <label translate>CREATE_CUSTOMER.COMPANY_TYPE</label>
            <div class="select-box">
                <select ng-model="dpCreateCustomerCompanyCtrl.type" id="typeSelection" ng-required="true" >
                    <option value="" disabled selected translate>CREATE_CUSTOMER.SELECT_COMPANY</option>
                    <option  value="Dealer" translate>CREATE_CUSTOMER.DEALER </option>
                    <option  value="Customer" translate>CREATE_CUSTOMER.CUSTOMER </option>
                </select>
                <div class="select-arrow"></div>
            </div>
        </div>

        <div class="input-group">
            <label translate>CREATE_CUSTOMER.DEFAULT_DISCOUNT</label>
            <input type="number" step="1" placeholder="{{'CREATE_CUSTOMER.ENTER_DISCOUNT' | translate}}"
                   ng-model="dpCreateCustomerCompanyCtrl.defaultDiscount" ng-required="false">
        </div>

        <div class="modal-actions">
            <button type="button" class="btn small secondary" data-dismiss="modal"
                    ng-click="dpCreateCustomerCompanyCtrl.cancel()" translate>
                GENERAL.CANCEL
            </button>

            <button type="submit" class="btn small primary"
                    ng-disabled="!createCompanyForm.$valid || dpCreateCustomerCompanyCtrl.isDisabled" translate>CREATE_CUSTOMER.CREATE_COMPANY
            </button>
        </div>
    </form>
  </div>


