(function () {
    'use strict';

    angular
        .module('app.viewer')
        .directive('workSideMenu', workSideMenu);

    function workSideMenu() {
        var directive = {
            restrict: 'E',
            templateUrl: 'features/viewer/extensions/workInstructionSideMenu/workInstructionSideMenu.html',
            controller: 'WorkInstructionSideMenuController',
            controllerAs: 'workInstructionSideMenuCtrl',
            bindToController: true
        };
        return directive;
    }

})();