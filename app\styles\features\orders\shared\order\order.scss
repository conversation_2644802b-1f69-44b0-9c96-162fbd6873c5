.order-field{

  border-bottom: 1px lightgrey solid;

}

.underline_order{

  border: lightgrey 1px solid;

}

hr.hr-text {
  border-top: 1px lightgrey solid;
  margin: 0 1rem 1rem 1.5rem;
}

.pricingSummary{

  td{

    text-align:right;

  }

}

.fa-customStack{

  cursor: pointer;
  height: 1em!important;
  width: 1em!important;

.fa-stack-2x {
  font-size: 1.25em;
  transition: ease-in-out 0.2s;
  color:#1a85fc;
}

.fa-stack-1x {
  font-size: 0.7em;
}

}

.fa-customStack:hover .fa-stack-2x {
  color: #0353ad !important;
}

.customLineHeight{

  line-height: 5vh;

}

.fa-stackSmall{

  cursor: pointer;
  width:1.5em!important;
  line-height: 1.5em!important;

  .fa-stack-2x {
    font-size: 1.5em;
  }

  .fa-stack-1x {
    font-size: 0.7em;
  }

}

.fa-stackSmall:hover {

  .fa-circle-thin:before{
    content:"\f111"!important;
  }

  .fa-comments:before,.fa-file-pdf:before,.fa-trash:before,.fa-undo:before{
    color:white;
  }
}

.newCommentAnim_order {
  animation: bell 1s infinite;
  transition: opacity 1s linear;
  animation-iteration-count: 20;
  z-index: 1;
}

@keyframes bell {
  0% {
    box-shadow: 0 0 0 0px rgb(51, 146, 252);
  }
  100% {
    box-shadow: 0 0 0 20px rgba(0, 0, 0, 0);
  }
}

.cadBlue {
  color: #1a85fc;
  transition: ease-in-out 0.2s;

  .hover {
    color: #004da0;
    font-size: 1.2em;
  }
}

/* Order of Items */

.priceSummary{

  order:3;

}

.csvDownload{

  order:4;

}

.identifiedParts{

  order:2;

}

.addPartsOrder, .manuallyAddedOrder{

  order:4;

}

.csvOrder{
  order: 10;
}

.orderManagement{
  order: 1;
}


/* Parts Table */

.dataTables_filter {
  float: right;
}

.order_partsList .table td:nth-child(2) {
  overflow: hidden;
  //white-space: nowrap;
  text-overflow: ellipsis;
}

.noPartsBG{

  background:#f9f9f9;

}

/* Responsive Table Styling Order Page */

.table_contents_order {
  border-collapse: collapse;
  margin: 0;
  padding: 0;
  width: 100%;
  table-layout: initial;
  background: white;
}

.table_contents_order caption {
  font-size: 1.5em;
  margin: .5em 0 .75em;
}

.table_contents_order tr {
  padding: .35em;
  transition: 0.5s ease-in-out;
  border-bottom: 2px solid #ddd;
}

.table_contents_order tr:first-child:last-child {
  border: 0px solid #ddd;
}

.table_contents_order th,
.table_contents_order td {
  padding: .625em;
  text-align: left;
}

.table_contents_order td {
  cursor: pointer;
}

.table_contents_order th {
  font-size: .85em;
  text-transform: uppercase;
  padding: 1.5rem 1rem;
}

.table_contents_order thead{
  border-top: 0.9px solid #dddddd;
  background-color: white;
}

.search_contents{

  border: 1px solid #ccc;
  background: white;

}

.table_contents_order tr:nth-child(odd){

  background: none;

}

.table_contents_order thead tr:first-child{

  background: white;

}
.table_contents_order thead {

  background: white;

}

@media screen and (max-width: 768px) {

  .table_contents_order {
    border: 0;
    background:transparent;
  }

  .table_contents_order caption {
    font-size: 1.3em;
  }

  .table_contents_order thead {
    border: none;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
  }

  .table_contents_order tr {
    display: block;
    background: white;
    overflow-wrap: break-word;
  }

  .table_contents_order td {
    border-bottom: 1px solid #ddd;
    font-size: .8em;
    text-align: left;
    display: flex;
    justify-content: space-between;
    padding:1rem;
    word-break: break-word;
    flex-wrap: wrap;
  }

  .table_contents_order td::before {

    content: attr(data-label);
    float: left;
    font-weight: bold;
    text-transform: uppercase;
    padding-right:0.5em;

  }

  .table_contents_order td:last-child {
    border-bottom: 0;
  }

  .search-area{

    width:100%!important;

  }

}

s_container{
  border-radius: 10px;
}

.order {
  border: 1px solid #ccc;
  border-collapse: collapse;
  margin: 0;
  padding: 0;
  width: 100%;
  table-layout: fixed;
}

.order caption {
  font-size: 1.5em;
  margin: 0.5em 0 0.75em;
}

.order_heading{

  background: #F2F6F9;

}

.order_buttons{

  background: white;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px

}

.order_body tr {
  border: 1px solid #ddd;
  padding: 0.35em;
  background: white;
}

.order th,
.order td {
  padding: 0.625em;
  word-break: break-word;
}

.order th {
  font-size: 0.85em;
  letter-spacing: 0.1em;
  text-transform: uppercase;
}

.search-panel {
  display: inline-block;
  width: auto;
}

.searchgroup {
  width: 340px;
  display: inline-block;
}

.order_pageNumber{

  background: white;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px

}

.order-filter-panel{

  width: 100%;
  padding: 1.5em;
  background: white;

  filter-header {
    font-size: 0.8em;
    text-transform: uppercase;
    font-weight: 700;
  }
  .first-filter {
  }

  .filter-option {
    display: inline-block;
    margin-right: $spacing*2;
    margin-top: $spacing;
  }
  .filter-buttons {
    margin-top: $spacing*2;

    .btn:first-child{
      margin-right: $spacing;
    }
  }

}

.customDropdown:hover{
  position: relative;
  display: inline-block;
  vertical-align: middle;
}

.customDropdown:hover > .dropdown-menu {
  display: block;
  cursor:pointer;
  transition: 0.2s;
}

.customDropdown:hover > .dropdown-menu > li:hover{
  background-color: #eaeaea;
}

.alignEditEnd{

  margin-inline-start: auto;

}

@media screen and (max-width: 800px) {
  .order {
    border: 0;
  }

  .order caption {
    font-size: 1.3em;
  }

  .order thead {
    border: none;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
  }

  .order tr {
    border-bottom: 3px solid #ddd;
    display: block;
    margin-bottom: 0.625em;
  }

  .order td {
    border-bottom: 1px solid #ddd;
    display: block;
    font-size: 1em;
  }

  .order td::before {
    /*
    * aria-label has no advantage, it won't be read inside a table
    content: attr(aria-label);
    */
    content: attr(data-label);
    font-weight: bold;
    text-transform: uppercase;
    padding-right:10px;

  }

  .order td:last-child {
    border-bottom: 0;
  }

  .products-filter {

    margin-left: auto;

  }

  .order-details-holder {
    width: 100%;
    float: left;

  }

}

.toggle-container {
  background: $grey; 
  border-radius: 50rem;
  display: flex;
  padding: 1px;
  width: max-content;
  margin: auto;

  .btn-toggle-warehouse {
    border: none;
    width: 50%;
    border-radius: 50rem;
    &--ca {
      color: #29911b;
    }
  
    &--us {
      color: #ffc107;
    }
  }
}

.selectWarehouse {
  margin-top: 16px;
}

.dropdownWarehouse{

  .dropdown-toggle{

    background: white;
    border: 1px solid #D2DAE5;
    min-width: 13.3em;

  }

  ul{

    li input {
      display:none;
    }

  }

}

/* Small Devices */

@media screen and (max-width: 576px) {

  .alignEditEnd{

    margin-inline-start: initial;

}

}