(function () {
    'use strict';

    angular
        .module('app.base')
        .controller('HeaderBannerController', HeaderBannerController);

    HeaderBannerController.$inject = ['$scope', 'headerBannerService', '$timeout'];

    function HeaderBannerController($scope, headerBannerService, $timeout) {
        var vm = this;

        vm.notificationDisplay = false;
        vm.level = "";
        vm.errorMsg = "";
        vm.timeout = 0;

        $scope.$on("bannerNotification", createBanner);

        vm.closeNotification = closeNotification;

        function createBanner() {
            var notification = headerBannerService.getNotification();
            if (notification.level && notification.level.length > 0) {
                vm.notificationDisplay = true;

                vm.level = notification.level;
                vm.errorMsg = notification.text;
                vm.timeout = notification.timeout ? notification.timeout : 10000;

                if (vm.timeout > 0) {
                    $timeout(closeNotification, vm.timeout);
                }
            }
        }

        function closeNotification() {
            headerBannerService.removeNotification();
            vm.notificationDisplay = false;
        }
    }

})();
