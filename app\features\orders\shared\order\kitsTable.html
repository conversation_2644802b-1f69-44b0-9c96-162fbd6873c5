<h2 class="">
    <span translate>ORDER.KITS</span>
  </h2>
  <p translate>ORDER.EX_WORKS_DISCLAIMER</p>

  <table class="table table-bordered equal-width">
    <thead>
      <tr>
        <th class="col-md-3 col-12" translate>ORDER.KIT</th>
        <th class="col-md-2 col-12" translate>ORDER.QTY</th>
        <th ng-if="!((viewOrderCtrl.hidePrice || viewOrderCtrl.hasNoPrices) && !viewOrderCtrl.isPriceEditable)"
          translate>
          ORDER.ITEM_PRICE
        </th>
        <th ng-if="viewOrderCtrl.hasVisibleDiscountedPrices() && !(viewOrderCtrl.hidePrice)" translate>
          ORDER.DISCOUNTED_PRICE
        </th>
        <th
          ng-if="!(viewOrderCtrl.hidePrice || (viewOrderCtrl.hasNoPrices && !viewOrderCtrl.isPriceEditable))"
          translate>
          ORDER.TOTAL_PRICE
        </th>
        <th ng-show="viewOrderCtrl.showPartialShipped" translate>
          ORDER.QTY_SHIPPED
        </th>
        <th
          ng-if="viewOrderCtrl.isPreviewStockLevelEnabled && !viewOrderCtrl.isDealerPlusCustomer && !viewOrderCtrl.isStockWarehousesEnabled || viewOrderCtrl.isStockWarehousesEnabled && !viewOrderCtrl.isDealerPlusCustomer && viewOrderCtrl.displayWarehouseName"
          translate>
          ORDER.STOCK
        </th>
        <th translate>GENERAL.ACTIONS</th>
      </tr>
    </thead>
    <tbody>
      <tr ng-repeat-start="kit in viewOrderCtrl.data.orderItems | filter: {kitId: ''}" class="hoverTableBG"
        ng-click="viewOrderCtrl.toggleKitsAccordion(kit.kitId, $index, $event)"
        ng-class="{'strike-through': kit.quantity <= 0, 'borderLeft': viewOrderCtrl.accordionStates[$index]}">
        <td data-label="{{'ORDER.KIT' | translate}}">
          <p>
            <i class="pr-2 fa" ng-class="viewOrderCtrl.accordionStates[$index] ? 'fa-chevron-up' : 'fa-chevron-down'"
              style="pointer-events: none;">
            </i><span class="font-weight-bold">{{kit.partNumber}}</span> <span ng-if="kit.partNumber">-</span> {{kit.partDescription}}
          </p>
        </td>

        <td data-label="{{'ORDER.QTY' | translate}}" ng-show="viewOrderCtrl.isQuantityEditable"
          class="kitstableHoverBG">
          <span ng-show="kit.quantity > 0">
            <input type="number" ng-model="kit.quantity" ng-change="viewOrderCtrl.updateItemTotals(true)" />
          </span>
          <span ng-hide="kit.quantity > 0">{{kit.quantity}}</span>
        </td>

        <td data-label="{{'ORDER.QTY' | translate}}"
          ng-hide="viewOrderCtrl.isQuantityEditable && !viewOrderCtrl.hidePrice">
          {{kit.quantity}}
        </td>

        <td class="disableWordBreak" data-label="{{'ORDER.ITEM_PRICE' | translate}}"
          ng-hide="viewOrderCtrl.hidePrice || viewOrderCtrl.isPriceEditable || viewOrderCtrl.hasNoPrices || (kit.price === 0 && viewOrderCtrl.showTBC)">
          {{ kit.price | currency: viewOrderCtrl.selectedCurrency.symbol :2}}
        </td>
        <td class="disableWordBreak" data-label="{{'ORDER.ITEM_PRICE' | translate}}"
          ng-hide="viewOrderCtrl.hidePrice || viewOrderCtrl.isPriceEditable || kit.price > 0 || !viewOrderCtrl.showTBC || viewOrderCtrl.hasNoPrices">
          -
        </td>

        <td class="disableWordBreak" ng-show="viewOrderCtrl.isPriceEditable && !viewOrderCtrl.hidePrice"
          data-label="{{'ORDER.ITEM_PRICE' | translate}}">
          <div class="inputWithIconWrap" ng-show="kit.quantity > 0">
            <span ng-show="kit.quantity > 0" class="input-icon">{{viewOrderCtrl.selectedCurrency.symbol}}&nbsp;</span>
            <input onclick="this.select()" ng-init="kit.price = kit.price || 0" ng-model="kit.price"
              ng-change="viewOrderCtrl.updateItemTotals(true)" type="number" class="input-with-icon" />
            <span ng-hide="kit.quantity > 0" class="input-icon">{{kit.price |
              currency:viewOrderCtrl.selectedCurrency.symbol:2}}</span>
          </div>
        </td>

        <td
          ng-show="viewOrderCtrl.hasVisibleDiscountedPrices() && !viewOrderCtrl.hidePrice"
          class="disableWordBreak" data-label="{{'ORDER.DISCOUNTED_PRICE' | translate}}">
          <span>
              {{ kit.discountedPrice ? (kit.discountedPrice | currency:viewOrderCtrl.selectedCurrency.symbol:2) : '-' }}</span>
        </td>

        <td ng-hide="viewOrderCtrl.hidePrice || (viewOrderCtrl.hasNoPrices && !viewOrderCtrl.isPriceEditable)"
        class="disableWordBreak" data-label="{{'ORDER.TOTAL_PRICE' | translate}}">
          <span>
            {{ kit.totalPrice ? (kit.totalPrice | currency:viewOrderCtrl.selectedCurrency.symbol:2) : '-' }}
          </span>
        </td>

        <td data-label="{{'ORDER.QTY_SHIPPED' | translate}}" ng-show="viewOrderCtrl.showPartialShipped"><span>
            {{kit.shippedQuantity || 0}}</span>
        </td>

        <td data-label="{{'ORDER.STOCK' | translate}}"
          ng-if="viewOrderCtrl.isPreviewStockLevelEnabled && !viewOrderCtrl.isStockWarehousesEnabled && !viewOrderCtrl.isDealerPlusCustomer">
          <span title="{{'ORDER.IN_STOCK' | translate}}" tooltip-trigger="outsideClick"
            uib-tooltip="{{'ORDER.IN_STOCK' | translate}}" class="success-alert" ng-if="kit.stock >= 3"><i
              class="fas fa-layer-group text-success pointer"></i></span>
          <span title="{{'ORDER.LOW_STOCK' | translate}}" tooltip-trigger="outsideClick"
            uib-tooltip="{{'ORDER.LOW_STOCK' | translate}}" class="warning-alert"
            ng-if="kit.stock < 3 && kit.stock > 0 "><i class="fas fa-layer-group text-warning pointer"></i></span>
          <span title="{{'ORDER.STOCK_SUBJECT' | translate}}" tooltip-trigger="outsideClick"
            uib-tooltip="{{'ORDER.STOCK_SUBJECT' | translate}}" class="warning-alert"
            ng-if="kit.stock === null || kit.stock < 1"><i class="fas fa-layer-group text-danger pointer"></i></span>
        </td>

        <td class="disableWordBreak" data-label="{{'ORDER.STOCK' | translate}}"
          ng-if="viewOrderCtrl.isStockWarehousesEnabled && viewOrderCtrl.displayWarehouseName">
          <div>
            <p>
              <span title="{{'ORDER.IN_STOCK' | translate}}" tooltip-trigger="outsideClick"
                uib-tooltip="{{'ORDER.IN_STOCK' | translate}}" class="success-alert mx-2" ng-if="kit.stock >= 3"><i
                  class="fas fa-circle text-success pointer"></i></span>
              <span title="{{'ORDER.LOW_STOCK' | translate}}" tooltip-trigger="outsideClick"
                uib-tooltip="{{'ORDER.LOW_STOCK' | translate}}" class="warning-alert mx-2"
                ng-if="kit.stock < 3 && kit.stock > 0 "><i class="fas fa-circle text-warning pointer"></i></span>
              <span title="{{'ORDER.STOCK_SUBJECT' | translate}}" tooltip-trigger="outsideClick"
                uib-tooltip="{{'ORDER.STOCK_SUBJECT' | translate}}" class="warning-alert mx-2"
                ng-if="kit.stock === null || kit.stock < 1"><i class="fas fa-circle text-danger pointer"></i></span>
              <span>
                {{ viewOrderCtrl.data.warehouseName }}</span>
            </p>
          </div>
        </td>

        <td data-label="{{'GENERAL.ACTIONS' | translate}}">
          <div class="d-flex justify-content-center align-items-center cadGap">

        <!--       <button type="button" class="btn secondary partnotes disableButton">
                <i class="fas fa-sticky-note fa-lg fa-fw"></i>
              </button> -->
              
              <button type="button" class="btn secondary warning disableButton">
                <i class="fa fa-file-pdf fa-lg fa-fw"></i>
              </button>

            <button type="button" ng-class="kit.commentThread && item.unreadComment ? 'newCommentAnim_order' : '' || item.unreadComment ? 'unreadcomment' : '' || {'disableButton': (viewOrderCtrl.data.orderStatus == 'CLOSED' && kit.commentThread === null) ||
                (viewOrderCtrl.data.orderStatus == 'CANCELLED' && kit.commentThread === null) ||
                (viewOrderCtrl.data.orderStatus == 'SHIPPED' && kit.commentThread === null) ||
                (viewOrderCtrl.data.orderStatus == 'EXTERNAL' && kit.commentThread === null) ||
            (viewOrderCtrl.enquiriesOnly && kit.commentThread === null)}" ng-disabled="(viewOrderCtrl.data.orderStatus == 'CLOSED' && kit.commentThread === null) ||
                    (viewOrderCtrl.data.orderStatus == 'CANCELLED' && kit.commentThread === null) ||
                    (viewOrderCtrl.data.orderStatus == 'SHIPPED' && kit.commentThread === null) ||
                    (viewOrderCtrl.data.orderStatus == 'EXTERNAL' && kit.commentThread === null) ||
                    (viewOrderCtrl.enquiriesOnly && kit.commentThread === null)"
              uib-tooltip="{{'ORDER.COMMENTS_TOOLTIP' | translate}}"
              ng-click="viewOrderCtrl.openComment(kit.commentThread.id, kit.orderItemId)"
              class="btn secondary comments">
              <i ng-show="kit.commentThread === null" class="far fa-comments fa-lg fa-fw"></i>

              <i ng-show="!item.unreadComment && kit.commentThread !== null" class="fa fa-comments fa-lg fa-fw"></i>
              <i ng-if="kit.commentThread && item.unreadComment" class="fa fa-comments fa-lg fa-fw"></i>
            </button>

            <span ng-show="viewOrderCtrl.isActionActive">
              <button ng-show="!kit.archived" uib-tooltip="{{'ORDER.REMOVE' | translate}}"
                ng-click="viewOrderCtrl.removeKit($index)" class="btn secondary danger">
                <i class="fa fa-trash-o fa-lg fa-fw"></i>
              </button>

              <button ng-show="kit.archived" uib-tooltip="{{'ORDER.UNDO' | translate}}"
                ng-click="viewOrderCtrl.removeKit($index)" class="btn secondary danger">
                <i class="fa fa-undo fa-lg fa-fw"></i>
              </button>
            </span>

            <span ng-show="viewOrderCtrl.deleteOrderItem" uib-tooltip="{{'ORDER.REMOVE' | translate}}">
              <button ng-show="kit.quantity > 0" ng-click="viewOrderCtrl.deleteItem($index)"
                class="btn secondary danger">
                <i class="fa fa-trash-o fa-lg fa-fw"></i>
              </button>
            </span>
          </div>
        </td>
      </tr>

      <tr ng-repeat-end>
        <td class="p-0" colspan="100%">
          <div class="accordion-anim" ng-class="{'open': viewOrderCtrl.accordionStates[$index]}">
            <table class="table table-bordered w-100 inner-table-widths">
              <tbody>
                <tr class="blueTableBG borderLeft" ng-repeat="part in kit.kitDetails.parts"
                  ng-class="part.quantity > 0 ? '' : 'strike-through'">
                  <td class="col-md-3 col-12" data-label="{{'ORDER.PART_DETAILS' | translate}}">
                    <span ng-if="viewOrderCtrl.isCustomer || !part.masterPartId">{{part.partNumber}} - {{ (part.descriptions | filter: {languageCode: currentLanguage})[0].translation }}</span>

                    <a ng-if="!viewOrderCtrl.isCustomer && part.masterPartId"
                      ng-click="viewOrderCtrl.goToMasterPart(part.masterPartId)" href="">{{part.partNumber}}</a>
                    <span ng-if="!viewOrderCtrl.isCustomer && part.masterPartId">{{ (part.descriptions | filter: {languageCode: currentLanguage})[0].translation }}</span>
                  </td>

                  <td class="col-md-2 col-12" data-label="{{'ORDER.QTY' | translate}}">
                    <span>{{part.quantity}}</span>
                  </td>

                    <td col-span="100%" class="bg-white"></td>

                </tr>
              </tbody>
            </table>
          </div>
        </td>
      </tr>

      <tr ng-hide="viewOrderCtrl.isOrderItemsLoaded" align="center">
        <td class="preloader" colspan="6">
          <img ng-src="images/cadpreloader.gif" class="ajax-loader" height="60" width="60" />
        </td>
      </tr>
      <tr ng-if="!viewOrderCtrl.hasKits()">
        <td class="flex-start noPartsBG" colspan="8" translate>
          ORDER.NO_KITS_PARTS
        </td>
      </tr>
    </tbody>
  </table>