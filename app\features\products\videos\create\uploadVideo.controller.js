(function () {
    'use strict';

    angular
        .module('app.shared')
        .controller('UploadVideoController', UploadVideoController);

    UploadVideoController.$inject = ['$uibModalInstance', 'dataObject', '$scope', 'awsS3Service', 'manufacturerProductService'];

    function UploadVideoController($uibModalInstance, dataObject, $scope, awsS3Service, manufacturerProductService) {
        var vm = this;

        vm.save = save;
        vm.cancel = $uibModalInstance.dismiss;

        if (dataObject) {
            vm.name = dataObject.name ? dataObject.name : null;
            vm.description = dataObject.description ? dataObject.description : null;
            vm.id = dataObject.id ? dataObject.id : null;
            vm.url = dataObject.url ? dataObject.url : null;
            vm.isEdit = dataObject.id ? true : false;
        }

        function save() {
            vm.wrongVideoSource = false;
            validateVideoSource();

            if (vm.parsedUrl) {
                vm.isSaving = true;
                if (vm.id) {
                    manufacturerProductService.editVideo(vm.name, vm.description, vm.parsedUrl, vm.id)
                        .then(saveVideoSuccess, serviceFailed);
                } else {
                    manufacturerProductService.createVideo(vm.name, vm.description, vm.parsedUrl)
                        .then(saveVideoSuccess, serviceFailed);
                }
            } else {
                vm.wrongVideoSource = true;
            }
        }

        function validateVideoSource() {
            vm.parsedUrl = null;
            if (vm.url.includes("vimeo.com")) {
                vm.parsedUrl = vm.url;
                return true;
            }

            var regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=)([^#\&\?]*).*/;
            var match = vm.url.match(regExp);

            if (match && match[2].length === 11) {
                var videoId = match[2];
                vm.parsedUrl = "https://youtube.com/embed/" + videoId;
                return true;
            } else {
                vm.parsedUrl = null;
                return false;
            }
        }

        function serviceFailed(error) {
            vm.error = true;
            vm.isSaving = false;
            console.error("Upload tec doc failed: " + error.data);
        }

        function saveVideoSuccess() {
            $uibModalInstance.close();
        }
    }
})();
