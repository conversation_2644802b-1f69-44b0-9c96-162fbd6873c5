<section class="responsiveContainer">

            <div class="flex p-4 p-md-0">
                <search-filter class="col-12 col-md-3" state-name="'customerTech'" on-search-change="customerTechDocsCtrl.searchFilterChange()" value="customerTechDocsCtrl.searchValue" placeholder-key="CUST_TECH_DOCS.SEARCH_BY"></search-filter>
            </div>

    <table class="table table-bordered">
        <thead>
        <tr>
            <th class="clickableRow" ng-class="customerTechDocsCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'"
                ng-click="customerTechDocsCtrl.viewable_sort='name'; customerTechDocsCtrl.sortReverse = !customerTechDocsCtrl.sortReverse"
                translate>
                CUST_TECH_DOCS.DOCUMENT_NAME
            </th>
            <th class="clickableRow" ng-class="customerTechDocsCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'"
                ng-click="customerTechDocsCtrl.viewable_sort='description'; customerTechDocsCtrl.sortReverse = !customerTechDocsCtrl.sortReverse"
                translate>
                CUST_TECH_DOCS.DOCUMENT_SUMMARY
            </th>
            <th style="width:230px;" translate>GENERAL.ACTIONS</th>
        </tr>
        </thead>
        <tbody>
        <tr ng-repeat="doc in customerTechDocsCtrl.docsList | orderBy:customerTechDocsCtrl.viewable_sort:customerTechDocsCtrl.sortReverse | filter : customerTechDocsCtrl.searchValue  | myLimitTo : customerTechDocsCtrl.count : customerTechDocsCtrl.start"
            ng-show="customerTechDocsCtrl.docsList.length > 0">
            <td data-label="{{'CUST_TECH_DOCS.DOCUMENT_NAME' | translate}}">{{doc.name}}</td>
            <td data-label="{{'CUST_TECH_DOCS.DOCUMENT_SUMMARY' | translate}}">{{doc.description}}</td>
            <td>
                <button href="" class="btn xsmall primary" ng-click="customerTechDocsCtrl.viewDoc(doc)" translate>
                    GENERAL.VIEW
                </button>
            </td>
        </tr>

        <tr ng-show="!customerTechDocsCtrl.docsList.length > 0" align="center">
            <td colspan="3" translate>CUST_TECH_DOCS.NO_DOCS</td>
        </tr>

        <tr ng-show="customerTechDocsCtrl.docsList === null" align="center">
            <td class="preloader" colspan="3">
                <img ng-src="images/cadpreloader.gif" class="ajax-loader" height="60" width="60"/>
            </td>
        </tr>
        </tbody>
    </table>

    <div ng-show="customerTechDocsCtrl.totalItems > 0">
        <div class="text-right">
            <uib-pagination max-size="4" force-ellipses="true" total-items="customerTechDocsCtrl.totalItems"
                            ng-model="customerTechDocsCtrl.currentPage"
                            items-per-page="customerTechDocsCtrl.itemPerPage"
                            ng-change="customerTechDocsCtrl.pageChanged()" boundary-links="true" class="pagination-md"
                            previous-text="&lsaquo;" next-text="&rsaquo;" first-text="&laquo;"
                            last-text="&raquo;"></uib-pagination>
        </div>
    </div>
</section>