(function () {
    'use strict';

    angular
        .module('app.products')
        .controller('TechDocsController', TechDocsController);

    TechDocsController.$inject = ['manufacturerProductService', '$uibModal', '$state', 'uploadModelService', 'headerBannerService', '$window', '$translate', '$scope'];

    function TechDocsController(manufacturerProductService, $uibModal, $state, uploadModelService, headerBannerService, $window, $translate, $scope) {
        var vm = this;

        vm.sortReverse = false;
        vm.endRecord = vm.itemPerPage;
        vm.docsList = null

        vm.loadingInfiniteScrollData = false;
        vm.isTechDocsLoaded = false;
        vm.showBackToTopButton = false;
        vm.isFixedHeader = false;

        var UPLOAD_SUCCESS, DELETE_SUCCESS;
        $translate(['TECH_DOCS.UPLOAD_SUCCESS', 'TECH_DOCS.DELETE_SUCCESS'])
            .then(function (resp) {
                UPLOAD_SUCCESS = resp["TECH_DOCS.UPLOAD_SUCCESS"];
                DELETE_SUCCESS = resp["TECH_DOCS.DELETE_SUCCESS"];
            });


        vm.createDoc = createDoc;
        vm.editDoc = editDoc;
        vm.deleteDoc = deleteDoc;
        vm.viewDoc = viewDoc;
        vm.scrollToTop = scrollToTop;
        vm.loadMoreInfiniteScroll = loadMoreInfiniteScroll;

        initialize();


        function initialize() {
            vm.loadingInfiniteScrollData = true;
            manufacturerProductService.getTechDocs()
                .then(function (resp) {
                    getTechDocsSuccess(resp);
                }, getTechDocsFailed);
        }

        function getTechDocs() {
            vm.loadingInfiniteScrollData = true;
            manufacturerProductService.getTechDocs()
                .then(function (resp) {
                    getTechDocsSuccess(resp);
                }, getTechDocsFailed);
        }

        function getTechDocsSuccess(response) {
            vm.allTechDocs = response.data;
            vm.totalItems = vm.allTechDocs.length;
            vm.docsList = vm.allTechDocs.slice(0, 100);
            vm.isTechDocsLoaded = true;
            vm.loadingInfiniteScrollData = false;
            handleInfiniteScroll();
        }

        function getTechDocsFailed(error) {
            headerBannerService.setNotification('ERROR', error.data.error, 10000);
            vm.loadingInfiniteScrollData = false;
        }

        function viewDoc(techDoc) {
            $window.open(techDoc.url, "_blank");
        }

            var lastScrollTop = 0;
window.addEventListener('scroll', handleInfiniteScroll);

function handleInfiniteScroll() {
    var threshold = 250;
    var scrollTop = window.scrollY;

    if (scrollTop > lastScrollTop) {
        vm.isFixedHeader = scrollTop > threshold;
    } else if (scrollTop < threshold){
        vm.isFixedHeader = false;
    }
    lastScrollTop = scrollTop;  

    
    if (window.innerHeight + scrollTop >= document.body.offsetHeight - 2 && !vm.loadingInfiniteScrollData && !vm.initialLoadDone) {
        loadMoreInfiniteScroll();
    }
}

        function loadMoreInfiniteScroll() {
            vm.loadingInfiniteScrollData = true;

            var nextItems = vm.allTechDocs.slice(vm.docsList.length, vm.docsList.length + 100);
            if (nextItems.length === 0) {
                vm.initialLoadDone = true;
                vm.loadingInfiniteScrollData = false;
                return;
            }

            vm.docsList = vm.docsList.concat(nextItems);

            vm.loadingInfiniteScrollData = false;
            if (vm.docsList.length >= vm.totalItems) {
                vm.initialLoadDone = true;
            }
        }

  function scrollToTop() {
      $window.scrollTo({ top: 0, behavior: "smooth" });
      $("html, body").animate({ scrollTop: 0 }, "slow", function () {
        $("#scrollToTop").removeClass("scrolled-past");
      });
    }

    angular.element($window).on("scroll", function () {
      vm.showBackToTopButton = this.pageYOffset > 100;
      $scope.$apply();
    });

        function createDoc() {
            headerBannerService.removeNotification();
            var dataObject = {};
            openUploadTechDocModal(dataObject)
        }

        function editDoc(techDoc) {
            headerBannerService.removeNotification();
            var dataObject = {
                name: techDoc.name,
                description: techDoc.description,
                id: techDoc.id,
                url: techDoc.url,
                filename: techDoc.filename
            }
            openUploadTechDocModal(dataObject)
        }

        function openUploadTechDocModal(dataObject) {
            $uibModal.open({
                templateUrl: 'features/products/techDocs/create/uploadTechDoc.html',
                controller: 'UploadTechDocController',
                controllerAs: 'uploadTechDocCtrl',
                size: 'md',
                resolve: {
                    dataObject: function () {
                        return dataObject;
                    }
                }
            }).result.then(function () {
                headerBannerService.setNotification('SUCCESS', UPLOAD_SUCCESS, 10000);
                getTechDocs();
            }, function () {
                console.log('Modal Cancelled');
            });
        }

        function deleteDoc(techDoc) {
            headerBannerService.removeNotification();
            var deleteObject = {
                name: techDoc.name,
                id: techDoc.id,
                url: '/techDoc/' + techDoc.id
            };

            $uibModal.open({
                templateUrl: 'features/shared/commonDelete/deleteDialogBox.html',
                controller: 'DeleteController',
                controllerAs: 'deleteCtrl',
                size: 'sm',
                resolve: {
                    deleteObject: function () {
                        return deleteObject;
                    }
                }
            }).result.then(function () {
                headerBannerService.setNotification('SUCCESS', DELETE_SUCCESS, 5000);
                getTechDocs();
            }, function () {
                console.log('Modal Cancelled');
            });
        }

        vm.actions = [
            {
                title: "View",
                onClick: function (entity) { vm.viewDoc(entity); },
                icon: "fa-eye",
                label: function (entity) {
                    // Check if the URL ends with '.pdf'
                    if (entity.url.endsWith('.pdf')) {
                        return $translate.instant('TECH_DOCS.VIEW');
                    } else {
                        return $translate.instant('TECH_DOCS.DOWNLOAD');
                    }
                }
            },
            {
                title: "Edit",
                onClick: function (entity) { vm.editDoc(entity); },
                icon: "fa-pencil",
                label: function () { return $translate.instant('TECH_DOCS.EDIT'); }
            },
            {
                title: "Delete",
                onClick: function (entity) { vm.deleteDoc(entity); },
                icon: "fa-trash",
                label: function () { return $translate.instant('TECH_DOCS.DELETE'); }
            }
        ];


    }
})();
