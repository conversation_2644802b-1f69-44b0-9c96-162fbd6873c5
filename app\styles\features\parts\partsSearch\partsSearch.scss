.btn-anim {
    transition: 0.5s ease-in-out;
    cursor: pointer;
}

.btn-anim:hover {
    cursor: pointer;
    color: #fff;
    background-color: #0069d9;
    border-color: #0062cc;
}

.input-group-text-btn {
    padding: 0.375rem 1.5rem;
    color: #ffffff;
    background: #3392fc;
    font-size: 1em;
    font-weight: bold;
    border: #1a85fc solid 2px;
}

.search_mobile_disable {
    color: white;
}

@media screen and (max-width: 768px) {
    .search_mobile_disable {
        display: none;
        color: white;
    }
}

.note-accordion {
    background-color: transparent;
    border: none;
    padding: 0;
    box-shadow: none;
}

.notes-accordion:hover {
    cursor: pointer;
}

.fixed-width-btn {
    min-width: 140px;
    min-height: 41px;
    @media (max-width: 992px) {
        min-height: 38px;
    }
}

.check-icon-wrapper {
    position: relative;
}

.check-icon-wrapper i {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

 .tooltip-container {
    display: inline-block;
    position: relative;

.tooltip-trigger {
    background: none;
    border: none;
    color: #007bff;
    padding: 0;
}

.supersessionHistoryHeader {
    border-bottom: 1px solid lightgrey;
    padding-bottom: 10px;
}

.custom-tooltip {
    display: none;
    position: absolute;
    left: calc(100% + 10px);
    top: 50%;
    transform: translateY(-50%);
    background-color: white;
    border: 1px solid #ccc;
    padding: 15px;
    width: 250px;
    max-height: 300px;
    overflow: auto;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    border-radius: 4px;
}

.custom-tooltip.last-item {
    transform: translateY(-100%);
}

.supersessionViewerPart {
    background: #F8FBFD;
        padding: 5px;
        border-radius: 10px;
        border: solid 1px;
        border-color: #F2F6F9;
        align-items: center;
        width: 100%;
}

.tooltip-title {
    margin: 0;
    color: #333;
    font-weight: bold;
    word-break: keep-all;
}

.tooltip-description {
    margin: 0;
    color: #666;
    font-size: 14px;
}

@media screen and (max-width: 768px) {
    .custom-tooltip {
            left: 50%;
            bottom: calc(100% + 10px);
            top: auto;
            transform: translateX(-50%);
            width: 200px;
        }
    
        .custom-tooltip.last-item {
            transform: translateX(-50%);
        }
}

.tooltip.show {
    display: block;
}
 }

 .borderTopSupersession {
    border-top: 3px solid #3392FC;
    border-left: 3px solid #3392FC;
    border-right: 3px solid #3392FC;
    background:inherit!important;
  }
  
  .borderBottomSupersession {
    border-bottom: 3px solid #3392FC;
    border-left: 3px solid #3392FC;
    border-right: 3px solid #3392FC;
    background:inherit!important;
  }
