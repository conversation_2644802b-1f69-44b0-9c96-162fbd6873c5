.myOrdersBlue{

  color:#3392FC;

}

.customersGreen{

 color: #069456;

}

.customersGreen{

  color: #069456;

  .blob_unreadEnquiries {
    border-left: #069456 3px solid;
  }

  .btn.unreadcomment {
    border: solid 1px #069456;
    position: relative;
  }

  .btn.unreadcomment:hover {
    border: solid 1px #069456;
  }

  .blob_unreadEnquiries{
    border-left: #069456 3px solid;
  }

  .blob_orders {
    color: #069456;
  }

  .blob_order {
    color: #069456;
  }

}

.dPlusTabs ul .customersGreen.active a {
  border-bottom-color: #069456;
  padding-bottom: 15px;
}

.sub-tabs ul .customersGreen.active a {
  border-bottom-color: #069456;
}

.customersGreen a:active:hover, .customersGreen a:link:hover, .customersGreen a:visited:hover{

  color: #069456;

}

.customersGreen .badge-small {

  background: rgba(6, 148, 86, 0.5);

}

.dPlusTabs{

  background-color:white;
  padding: 1.5% 2% 0% 0%;
  border-top: 1px solid #D2DAE5;
  position: relative;

  ul{

    .sub-tabs ul {
      list-style: none;
      margin: 0;
      padding: 0;
      border-bottom: 1px solid #D2DAE5;

    }

  }

  ul li{

    margin: 0 40px -1px 0;
    padding: 0px;
    display: inline-flex;

  }

  ul li.active a {
    border-bottom-color: #3392FC;
    padding-bottom: 15px;
  }

  ul li a {
    border-bottom: 3px solid transparent;
    color: #373D45;
    cursor: pointer;
  }


}

/* Dealer Plus Order Green Styling */

.dpGreenOrder{

.btn.primary, .btn-primary {
  color: #FFFFFF;
  background-color: #069456;
  border-color: #069456;
}

  .btn.primary:hover, .btn.primary .active, .btn-primary:hover, .btn-primary .active {
    color: #FFFFFF;
    background-color: #047c48;
  }

  .btn.primary-outline {
    color: #069456;
    background: #FFFFFF;
    border: solid 1px #069456;
  }

  .btn.primary-outline:hover, .btn.primary-outline .active {
    color: #FFFFFF;
    background: #047c48;
  }

  .btn small primary{

    color: #FFFFFF;
    background-color: #069456;
    border-color: #069456;

  }

  input[type="checkbox"]:checked {
    border: 1px solid #047c48;
    background: #069456;
    color: #fff;
  }

  a:active .fa-fw, a:link .fa-fw, a:visited{
    color: #069456;
  }

  .badge-pill.primary {
    border-color: #069456;
    color: #069456;
  }

  .btn-primary:not(:disabled):not(.disabled).active, .btn-primary:not(:disabled):not(.disabled):active, .show>.btn-primary.dropdown-toggle {
    color: #fff;
    background-color: #069456;
    border-color: #047c48;
  }

}

.dpGreenOrders.badge-pill.primary {
    border-color: #069456;
    color: #069456;
  }

.dpGreenOrders.sub-tabs ul li.active a {
    border-bottom-color: #069456;
  }

.dpGreenModal.btn.primary, .dpGreenModal.btn-primary {
    color: #FFFFFF;
    background-color: #069456;
    border-color: #069456;
  }

.dpGreenModal.btn.primary:hover, .dpGreenModal.btn.primary .active, .dpGreenModal.btn-primary:hover, .dpGreenModal.btn-primary .active {
    color: #FFFFFF;
    background-color: #047c48;
  }

.dpGreenModal.btn.primary-outline {
  color: #069456;
  background: #FFFFFF;
  border: solid 1px #069456;
}

.dpGreenModal.input-group-text-btn {
  padding: 0.375rem 1.5rem;
  color: #FFFFFF;
  background: #069456;
  font-size: 1em;
  font-weight: bold;
  border: #069456 solid 2px;
}

.dpGreenModal.radio {
  accent-color: #069456;
}

@media only screen and (max-width: 900px) {

  .table_contents{

    .mobileClassAlignmentDp{

justify-content: end;

    }

  }

}

/* Dealer Plus Master Part Page */

.customCardBody{

  border-bottom:2px dashed lightgrey;
  margin-bottom: 1em;

}

.modal-body{

.underline{

  border-bottom:1px dashed lightgrey;
  margin-bottom: 0.75em!important;
  padding-bottom: 0.35em;

}

}