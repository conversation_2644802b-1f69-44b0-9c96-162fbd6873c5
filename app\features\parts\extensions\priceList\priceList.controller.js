(function () {
    'use strict';

    angular
        .module('app.shared')
        .controller('PriceListController', PriceListController);

    PriceListController.$inject = ['$uibModalInstance', 'priceListObject', 'priceListService'];

    function PriceListController($uibModalInstance, priceListObject, priceListService) {
        var vm = this;
        
        vm.save = save;
        vm.cancel = $uibModalInstance.dismiss;

        if (priceListObject) {
            vm.price = priceListObject.price;
            vm.priceListIdentifier = priceListObject.identifier;
            vm.currencySymbol = priceListObject.currencySymbol;
        }

        function save() {
            vm.hasError = false;
            priceListService.updatePriceList(priceListObject.masterPartId, priceListObject.priceId, vm.price)
                .then(saveSuccess, saveFailure);
        }

        function saveSuccess() {
            $uibModalInstance.close(vm.price);
        }

        function saveFailure(error) {
            vm.hasError = true;
            console.log("Error saving priceList: " + error)
        }
    }
})();
