(function () {
    'use strict';

    angular
        .module('app.orders')
        .controller('EnquiryController', EnquiryController);

    EnquiryController.$inject = ['ordersService', '$rootScope', '$scope', '$controller', 'userService', '$state', 'headerBannerService', '$uibModal', '$translate'];

    function EnquiryController(ordersService, $rootScope, $scope, $controller, userService, $state, headerBannerService, $uibModal, $translate) {
        var vm = this;

        angular.extend(vm, $controller('OrderController', {$scope: $scope}));

        var QUOTE_GENERATED_TEXT, DRAFT_SAVED_TEXT, PRICE_ZERO, PRICE_ZERO_DESCRIPTION, SHIPPING_ZERO, SHIPPING_ZERO_DESCRIPTION, SHIPPING_PRICE_ZERO, SHIPPING_PRICE_ZERO_DESCRIPTION, CONFIRM_ORDER_ARCHIVED, ARCHIVE_MODAL, CONFIRM_ORDER_BANNER;

        vm.isManufacturer = userService.isManufacturer();
        vm.enquiryPurchaseOrder = userService.getEnquiryPurchaseOrder();
        vm.enquiriesOnly = userService.getEnquiriesOnly();
        vm.isCDE = userService.isCDE();
        vm.currencies = userService.getCurrencyList();

        vm.addManualParts = [];

        vm.nextStep = nextStep;
        vm.altStep = altStep;
        vm.goToSplitOrder = goToSplitOrder;
        vm.orderEdited = orderEdited;
        vm.editCustomOrderNumber = editCustomOrderNumber;
        vm.removeAddManualPart = removeAddManualPart;
        vm.saveAddManualParts = saveAddManualParts;
        vm.cancelCreateAddManualParts = cancelCreateAddManualParts;
        vm.createAddManualPart = createAddManualPart;
        vm.openComment = openComment;
        vm.reloadOrder = reloadOrder;
        vm.openPriceIsZeroModal = openPriceIsZeroModal;
        vm.openShippingIsZeroModal = openShippingIsZeroModal;
        vm.openShippingPriceIsZeroModal = openShippingPriceIsZeroModal;
        vm.isPartInformationAvailable = isPartInformationAvailable;
        vm.archiveOrder = archiveOrder;
        vm.duplicateOrder = duplicateOrder;
        vm.onlyKitsPresent = onlyKitsPresent;
        vm.hasVisibleDiscountedPrices = hasVisibleDiscountedPrices;
        vm.isFarmer = userService.isFarmer();
        vm.previewPricingEnabled = userService.getPreviewPricingEnabled();

        initialize();

        function initialize() {

            if (vm.isManufacturer && ('SUBMITTED' || 'PREPARING')) {
                vm.showPurchaseOrder = false;
                vm.isArchiveActive = true;
            } else {
                vm.showPurchaseOrder = vm.enquiryPurchaseOrder;
                vm.isArchiveActive = false;
            }
            vm.showPartDescription = false;
            vm.showModelName = false;
            vm.showMachineName = false;
            vm.showEstimatedDelivery = false;
            vm.displayAdditionalParts = false;
            vm.isDiscountEditable = false;
            vm.isDiscountVisible = true;
            vm.isDiscountEnabled = true;
            vm.isOrderEdited = false;
            vm.isAddPartsVisible = true;
            vm.showCancelOrderButton = true;
            vm.showEditDetailsButton = true;
            vm.showEditOrderNumberButton = true;
            vm.deleteOrderItem = false;
            vm.splitOrders = [];
            if (vm.isManufacturer) {
                $translate(['ENQUIRY.GENERATE_QUOTE', 'ENQUIRY.SAVE_DRAFT'])
                    .then(function (resp) {
                        vm.nextStepText = resp['ENQUIRY.GENERATE_QUOTE'];
                        vm.altStepText = resp['ENQUIRY.SAVE_DRAFT'];
                    });
                vm.showNextStep = true;
                vm.showAltStepBtn = true;
                vm.isShippingEditable = true;
                vm.isPriceEditable = true;
                vm.isQuantityEditable = true;
                vm.isActionActive = true;
                vm.isDetailsEditable = false;
                vm.isNotesEditable = true;
                vm.displayNotes = true;
                vm.showSplitOrderBtn = true;
                vm.showEditDetailsButton = true;
                vm.showEditOrderNumberButton = true;
                vm.showCurrencySelection = true;
                vm.hidePrice = false;
                vm.exportPartDataCsvVisible = true;
                if (vm.externallyProcessed) {
                    vm.showExternallyProcessButton = true;
                }
                vm.showPartSearch = true;
                vm.isDisabledDiscountEditing = userService.getDisableDiscountEditing();
            } else {
                vm.showNextStep = false;
                vm.showAltStepBtn = false;
                vm.isShippingEditable = false;
                vm.isPriceEditable = false;
                vm.isQuantityEditable = false;
                vm.isActionActive = false;
                vm.isDetailsEditable = true;
                vm.isNotesEditable = false;
                vm.displayNotes = false;
                vm.showSplitOrderBtn = false;
                vm.showEditDetailsButton = false;
                vm.showEditOrderNumberButton = false;
                vm.hidePrice = !userService.getPreviewPricingEnabled();
                vm.showTBC = true;
                vm.exportPartDataCsvVisible = false;
            }
            $translate(['ENQUIRY.PENDING_SUBMITTED', 'ENQUIRY.DRAFT_SAVED_TEXT', 'ENQUIRY.QUOTE_GENERATED', 'ORDERS.ENQUIRY', 'ORDER.PRICE_ZERO', 'ORDER.PRICE_ZERO_DESCRIPTION', 'ORDER.SHIPPING_ZERO_DESCRIPTION', 'ORDER.SHIPPING_ZERO', 'ORDER.SHIPPING_PRICE_ZERO', 'ORDER.CONFIRM_ORDER_ARCHIVED', 'ORDER.SHIPPING_PRICE_ZERO_DESCRIPTION', 'ORDER.CONFIRM_ORDER_ARCHIVED', 'ORDER.CONFIRM_ORDER_CLOSED', 'ORDER.ARCHIVE_ORDER', 'ORDERS.CONFIRM_ORDER_BANNER', 'ORDERS.ARCHIVE_MODAL', 'ORDERS.CONFIRM_ORDER_ARCHIVED'])
                .then(function (resp) {
                    vm.orderStatusPill = resp['ENQUIRY.PENDING_SUBMITTED'];
                    DRAFT_SAVED_TEXT = resp['ENQUIRY.DRAFT_SAVED_TEXT'];
                    QUOTE_GENERATED_TEXT = resp['ENQUIRY.QUOTE_GENERATED'];
                    PRICE_ZERO = resp["ORDER.PRICE_ZERO"];
                    PRICE_ZERO_DESCRIPTION = resp["ORDER.PRICE_ZERO_DESCRIPTION"];
                    SHIPPING_ZERO = resp["ORDER.SHIPPING_ZERO"];
                    SHIPPING_ZERO_DESCRIPTION = resp["ORDER.SHIPPING_ZERO_DESCRIPTION"];
                    SHIPPING_PRICE_ZERO = resp["ORDER.SHIPPING_PRICE_ZERO"];
                    SHIPPING_PRICE_ZERO_DESCRIPTION = resp["ORDER.SHIPPING_PRICE_ZERO_DESCRIPTION"];
                    CONFIRM_ORDER_ARCHIVED = resp["ORDERS.CONFIRM_ORDER_ARCHIVED"];
                    CONFIRM_ORDER_BANNER = resp["ORDERS.CONFIRM_ORDER_BANNER"];
                    ARCHIVE_MODAL = resp["ORDERS.ARCHIVE_MODAL"];
                    CONFIRM_ORDER_BANNER = resp["ORDERS.CONFIRM_ORDER_BANNER"];
                    vm.STAGE = resp['ORDERS.ENQUIRY'];
                });

            isCDEDisclaimerEnabled();
            getOrder();
        }

        function getOrder() {
            ordersService.getOrder(vm.orderId)
                .then(getOrdersSuccess, serviceCallFailed);
        }

        function duplicateOrder() {
            ordersService.duplicateOrder(vm.orderId)
                .then(function (response) {
                    var duplicatedOrder = response.data;
                    $state.go('create', {
                        orderDetails: duplicatedOrder
                    });
                }, function (error) {
                    console.error('Failed to duplicate order:', error);
                });
        }

        function getOrdersSuccess(response) {
            $rootScope.$broadcast("Update-Unread-Order-Tabs");
            if (response.data.orderStatus === 'SUBMITTED' || response.data.orderStatus === 'PREPARING') {
                vm.data = response.data;
                vm.orderLists = response.data.orderItems;
                vm.totalItems = vm.orderLists.length;
                vm.billingAddress = vm.createReadableAddress(response.data.billingAddress);
                vm.shippingAddress = vm.createReadableAddress(response.data.shippingAddress);
                vm.data.originalShippingPrice = vm.data.shippingPrice;
                vm.data.shippingPrice = vm.data.shippingPrice ? vm.data.shippingPrice : 0;
                vm.isOrderItemsLoaded = true;
                vm.additionalParts = vm.data.additionalParts;
                vm.parentOrderId = vm.data.parentOrderId;
                vm.parentCustomOrderDisplay = vm.data.parentCustomOrderDisplay;
                vm.customOrderDisplay = vm.data.customOrderDisplay;
                vm.isPreviewStockLevelEnabled = userService.getPreviewStockLevelEnabled();
                vm.isStockWarehousesEnabled = userService.getStockWarehousesEnabled();
                vm.associatedOrderId = vm.data.associatedOrderId;
                vm.associatedCustomOrderDisplay = vm.data.associatedCustomOrderDisplay;
                vm.selectedCurrency = userService.getCurrencyData(vm.data.currency);
                vm.showERPRefVisible = userService.isManufacturer() && vm.data.visSalesOrderNoX != null;
                vm.displayWarehouseName = vm.data.warehouseName !== null && vm.data.warehouseName !== undefined;
                fetchShippingRequirements();

                if (vm.additionalParts.length > 0) {
                    vm.displayAdditionalParts = true;
                    vm.updateManualPartTotals();
                }

                if (response.data.orderStatus === 'PREPARING') {
                    $translate(['ENQUIRY.ENQUIRY_PREPARING'])
                        .then(function (resp) {
                            vm.orderStatusPill = resp['ENQUIRY.ENQUIRY_PREPARING'];
                        });
                }
                if (vm.isManufacturer) {
                    vm.isDiscountVisible = true;
                    vm.isDiscountEditable = !vm.isDisabledDiscountEditing ? true : false;
                    vm.isDiscountEnabled = true;
                }

                calculateSplitOrders();
                vm.updateItemTotals(true, this, true);

                var manufacturerSubEntityId = vm.data.manufacturerSubEntityId;

                vm.orderLists.forEach(function (item) {
                    if (item.kitId) {
                        var id = vm.isManufacturer ? userService.getManufacturerId() : manufacturerSubEntityId;
                        ordersService.getKit(id, item.kitId, vm.isManufacturer)
                            .then(kitResponse => {
                                item.kitDetails = kitResponse;
                            });
                    }
                });
            } else {
                vm.redirectToOrder(response.data.orderId, response.data.orderStatus);
            }
        }

        function serviceCallFailed(error) {
            vm.isOrderItemsLoaded = true;
            headerBannerService.setNotification('ERROR', error.data.error, 10000);
        }


        function openPriceIsZeroModal() {
            var confirmObject = {
                titleText: PRICE_ZERO,
                bodyText: PRICE_ZERO_DESCRIPTION
            };
            $uibModal.open({
                templateUrl: 'features/shared/confirmationModal/confirmModal.html',
                controller: 'ConfirmModalController',
                controllerAs: 'confirmModalCtrl',
                size: 'sm',
                resolve: {
                    confirmObject: function () {
                        return confirmObject;
                    }
                }
            }).result.then(function () {
                vm.data.orderStatus = "QUOTE";
                updateData();
                ordersService.updateOrder(vm.data)
                    .then(generateQuoteSuccess, updateOrderFailed);
            }, function () {
                console.log('Modal Cancelled');
            });
        }

        function openShippingIsZeroModal() {
            var confirmObject = {
                titleText: SHIPPING_ZERO,
                bodyText: SHIPPING_ZERO_DESCRIPTION
            };
            $uibModal.open({
                templateUrl: 'features/shared/confirmationModal/confirmModal.html',
                controller: 'ConfirmModalController',
                controllerAs: 'confirmModalCtrl',
                size: 'sm',
                resolve: {
                    confirmObject: function () {
                        return confirmObject;
                    }
                }
            }).result.then(function () {
                vm.data.orderStatus = "QUOTE";
                updateData();
                ordersService.updateOrder(vm.data)
                    .then(generateQuoteSuccess, updateOrderFailed);
            }, function () {
                console.log('Modal Cancelled');
            });
        }

        function openShippingPriceIsZeroModal() {
            var confirmObject = {
                titleText: SHIPPING_PRICE_ZERO,
                bodyText: SHIPPING_PRICE_ZERO_DESCRIPTION
            };
            $uibModal.open({
                templateUrl: 'features/shared/confirmationModal/confirmModal.html',
                controller: 'ConfirmModalController',
                controllerAs: 'confirmModalCtrl',
                size: 'sm',
                resolve: {
                    confirmObject: function () {
                        return confirmObject;
                    }
                }
            }).result.then(function () {
                vm.data.orderStatus = "QUOTE";
                updateData();
                ordersService.updateOrder(vm.data)
                    .then(generateQuoteSuccess, updateOrderFailed);
            }, function () {
                console.log('Modal Cancelled');
            });
        }

        function nextStep() {
            vm.isNextStepDisabled = true;
            if (vm.isShippingEditable && vm.data.shippingPrice === 0 && (vm.hasPricesMissing || vm.ifManuallyAddedHasPricesMissing))  {
                openShippingPriceIsZeroModal();
                vm.isNextStepDisabled = false;
            } else if (vm.hasPricesMissing || vm.ifManuallyAddedHasPricesMissing) {
                openPriceIsZeroModal();
                vm.isNextStepDisabled = false;
            } else if (vm.isShippingEditable && vm.data.shippingPrice === 0) {
                openShippingIsZeroModal();
                vm.isNextStepDisabled = false;
            } else if (vm.splitOrders.length > 0 && vm.isShippingEditable && vm.data.shippingPrice !== 0) {
                vm.data.orderStatus = "QUOTE";
                updateData();
                ordersService.updateOrder(vm.data)
                    .then(generateQuoteSuccess, updateOrderFailed);
            } else {
                vm.data.orderStatus = "QUOTE";
                updateData();
                ordersService.updateOrder(vm.data)
                    .then(generateQuoteSuccess, updateOrderFailed);
            }

        }

        function updateData() {
            vm.data.price = vm.total;
            vm.data.additionalParts = vm.additionalParts;
            vm.data.currency = vm.selectedCurrency;
            delete vm.data.associatedCustomOrderDisplay;
            if (vm.isCustDetailEdit) {vm.data.additionalParts = vm.additionalParts;
                vm.data.contactName = vm.newContactName;
                vm.data.deliveryName = vm.newDeliveryName;
                vm.data.contactNumber = vm.newContactNumber;
                vm.data.deliveryNumber = vm.newDeliveryNumber;
                vm.data.shippingAddress = JSON.parse(vm.newDeliveryAddress);
                vm.data.billingAddress = JSON.parse(vm.newBillingAddress);
            }
        }

        function altStep() {
            vm.data.orderStatus = "PREPARING";
            updateData();
            updateOrder();
        }

        function updateOrder() {
            delete vm.data.associatedCustomOrderDisplay;
            ordersService.updateOrder(vm.data)
                .then(saveDraftSuccess, updateOrderFailed);
        }

        function saveDraftSuccess() {
            headerBannerService.setNotification('SUCCESS', DRAFT_SAVED_TEXT, 10000);
            vm.isOrderEdited = false;
            vm.isCustDetailEdit = false;
            getOrder();
        }

        function generateQuoteSuccess() {
            headerBannerService.setNotification('SUCCESS', QUOTE_GENERATED_TEXT, 10000);
            vm.isOrderEdited = false;
            $state.go('orders.quotation', {
                orderId: vm.data.orderId
            });
        }

        function updateOrderFailed(error) {
            headerBannerService.setNotification('ERROR', error.data.error, 10000);
            vm.isNextStepDisabled = false;
        }

        function goToSplitOrder(splitOrderId) {
            $state.go('orders.enquiry', {orderId: splitOrderId});
        }

        function calculateSplitOrders() {
            vm.splitOrders = [];
            if (vm.orderLists) {
                for (var i = 0; i < vm.orderLists.length; i++) {
                    if (vm.orderLists[i].splitOrderIds && vm.orderLists[i].splitOrderIds.length > 0) {
                        for (var j = 0; j < vm.orderLists[i].splitOrderIds.length; j++) {
                            if (!_.findWhere(vm.splitOrders, {orderId: vm.orderLists[i].splitOrderIds[j]})) {
                                var splitOrder = {
                                    orderId: vm.orderLists[i].splitOrderIds[j],
                                    displayId: vm.orderLists[i].splitOrderCustomDisplayNumbers[j] ? vm.orderLists[i].splitOrderCustomDisplayNumbers[j] : vm.orderLists[i].splitOrderIds[j]
                                }
                                vm.splitOrders.push(splitOrder);
                            }

                        }
                    }
                }
            }
            if (vm.additionalParts) {
                for (var k = 0; k < vm.additionalParts.length; k++) {
                    if (vm.additionalParts[k].splitOrderIds && vm.additionalParts[k].splitOrderIds.length > 0) {
                        for (var l = 0; l < vm.additionalParts[k].splitOrderIds.length; l++) {
                            if (!_.findWhere(vm.splitOrders, {orderId: vm.additionalParts[k].splitOrderIds[l]})) {
                                var splitOrder = {
                                    orderId: vm.additionalParts[k].splitOrderIds[l],
                                    displayId: vm.additionalParts[k].splitOrderCustomDisplayNumbers[l] ? vm.additionalParts[k].splitOrderCustomDisplayNumbers[l] : vm.additionalParts[k].splitOrderIds[l]
                                }
                                vm.splitOrders.push(splitOrder);
                            }
                        }
                    }
                }
            }

        }

        $scope.$on("Order-Edited", orderEdited);

        function orderEdited() {
            vm.isOrderEdited = true;
        }

        function editCustomOrderNumber() {
            $uibModal.open({
                templateUrl: 'features/orders/enquiries/customNumber/customNumberModal.html',
                controller: 'CustomNumberController',
                controllerAs: 'customNumberCtrl',
                size: 'sm',
                resolve: {
                    customNumberObject: function () {
                        return {customOrderDisplay: vm.customOrderDisplay, orderId: vm.orderId};
                    }
                }
            }).result.then(function (customOrderDisplay) {
                vm.data.customOrderDisplay = customOrderDisplay
            });
        }

        function createAddManualPart() {
            var emptyPart = {
                "partNumber": "",
                "partDescription": "",
                "machineName": "",
                "quantity": 1
            };
            vm.addManualParts.push(emptyPart);
            vm.addManualPartsActive = true;
        }

        function removeAddManualPart(manualPart) {
            var itemToRemoveIndex = _.findIndex(vm.addManualParts, {
                partNumber: manualPart.partNumber,
                partDescription: manualPart.partDescription
            });
            if (itemToRemoveIndex > -1) {
                vm.addManualParts.splice(itemToRemoveIndex, 1);
            }
            vm.addManualPartsActive = vm.addManualParts.length > 0;

        }

        function saveAddManualParts() {
            if (vm.addManualPartsForm.$valid) {
                for (var i = 0; i < vm.addManualParts.length; i++) {
                    vm.addManualParts[i].price = 0;
                    vm.addManualParts[i].totalPrice = 0;
                    vm.addManualParts[i].orderId = vm.orderId;
                    vm.data.additionalParts.push(vm.addManualParts[i])
                }

                vm.addManualParts = [];
                if (vm.data.additionalParts.length > 0) {
                    vm.displayAdditionalParts = true;
                }
                vm.addManualPartsActive = false;

                updateOrder();
            }
        }

        function cancelCreateAddManualParts() {
            vm.addManualParts = [];
            vm.addManualPartsActive = false;
        }

        $rootScope.$on('$stateChangeStart',
            function (event, toState, toParams, fromState, fromParams) {
                event.preventDefault();
                // transitionTo() promise will be rejected with
                // a 'transition prevented' error
            });

        function openComment(commentThread, orderItemId) {
            var commentObject = {
                threadId: commentThread,
                orderId: vm.orderId,
                orderItemId: orderItemId
            };
            if (vm.enquiriesOnly) {
                commentObject.blockAddingComment = true;
            }

            $uibModal.open({
                templateUrl: 'features/shared/comments/comments.html',
                controller: 'CommentController',
                controllerAs: 'commentsCtrl',
                resolve: {
                    commentObject: function () {
                        return commentObject;
                    }
                }
            }).result.then(function (commentObject) {

                if (commentObject.orderItemId) {
                    var index = _.findIndex(vm.data.orderItems, {orderItemId: commentObject.orderItemId});
                    vm.data.orderItems[index].commentThread = {};
                    vm.data.orderItems[index].commentThread.orderItemId = commentObject.orderItemId;
                    vm.data.orderItems[index].commentThread.id = commentObject.threadId;
                    vm.data.orderItems[index].commentThread.orderId = commentObject.orderId;
                } else {
                    vm.data.commentThread = {};
                    vm.data.commentThread.id = commentObject.threadId;
                    vm.data.commentThread.orderId = commentObject.orderId;
                }
            }, function () {
                console.log('Modal Cancelled');
            });
        }

        function reloadOrder() {
            getOrder();
        }

        function isCDEDisclaimerEnabled() {
            if (vm.isCustomer) {
                vm.showDisclaimer = vm.isCDE;
            }
        }

        function isPartInformationAvailable(item) {
            var partInformation = {
                showPartDescription: item.partDescription,
                showModelName: item.modelName,
                showMachineName: item.machineName
            };
            vm.showPartDescription = partInformation.showPartDescription;
            vm.showModelName = partInformation.showModelName;
            vm.showMachineName = partInformation.showMachineName;
            return partInformation;
        }

        function fetchShippingRequirements() {
            if (!vm.isFarmer) {
                ordersService.getShippingRequirements(vm.data.manufacturerSubEntityId).then(function(response) {
                    var shippingRequirements = response.data.shippingRequirements;
                    vm.orderShippingRequirement = shippingRequirements.find(req => req.shippingRequirementId === vm.data.shippingRequirementId);
                    vm.isShippingRequirementsValid = !!vm.orderShippingRequirement && !!vm.orderShippingRequirement.preferredCourier && !!vm.orderShippingRequirement.courierNumber;
                }, function(error) {
                    console.error("Error fetching shipping requirements:", error);
                    vm.isShippingRequirementsValid = false;
                });
              }
            }

        function onlyKitsPresent() {
            if (!vm.data || !vm.data.orderItems) {
                return false;
            }
            return vm.data.orderItems.every(item => item.kitId && !item.partId);
        }
    
        function archiveOrder() {
            var confirmObject = {
                titleText: CONFIRM_ORDER_ARCHIVED,
                bodyText: ARCHIVE_MODAL
            };

            $uibModal.open({
                templateUrl: 'features/shared/confirmationModal/confirmModal.html',
                controller: 'ConfirmModalController',
                controllerAs: 'confirmModalCtrl',
                size: 'sm',
                resolve: {
                    confirmObject: function () {
                        return confirmObject;
                    }
                }
            }).result.then(function () {
                vm.data.orderStatus = "CLOSED";
                ordersService.archiveOrder(vm.orderId)
                    .then(function () {
                        headerBannerService.setNotification('SUCCESS', CONFIRM_ORDER_BANNER, 2000);
                        $state.go('orders.historicalorders');
                    });
            }, function () {
                console.log('Modal Cancelled');
            });
        }

        function hasVisibleDiscountedPrices() {

            if (!vm.data || !vm.data.orderItems) {
                return false;
            }

            var orderItems = vm.data.orderItems;

            var hasDiscountedPrices = orderItems.some(item => item.discountedPrice && item.discountedPrice !== 0);

            return hasDiscountedPrices;
        }
    }
})();
