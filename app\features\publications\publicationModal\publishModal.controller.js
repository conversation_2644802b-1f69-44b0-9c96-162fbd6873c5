(function () {
    'use strict';

    angular
        .module('app.publications')
        .controller('publishModalController', publishModalController);

    publishModalController.$inject = ['$uibModalInstance'];

    function publishModalController($uibModalInstance) {

        var vm = this;

        vm.publishLater = publishLater;
        vm.publishNow = publishNow;
        vm.cancel = cancel;

        function publishLater() {
            $uibModalInstance.close('publishLater');
        }

        function publishNow() {
            $uibModalInstance.close('publishNow');
        }

        function cancel() {
            $uibModalInstance.dismiss('cancel');
        }

    }

    })();
