(function () {
    'use strict';

    angular
        .module('cadshareApp')

        .config(configure)
        .config(languageConfigure)
        .config(trackJsConfigure)
        .run(defaultHeaders);

    configure.$inject = ['$httpProvider'];
    defaultHeaders.$inject = ['$http'];
    languageConfigure.$inject = ['$translateProvider'];
    trackJsConfigure.$inject = ['$provide', 'apiConstants'];

    function configure($httpProvider) {
        $httpProvider.interceptors.push('authInterceptor');
    }

    function defaultHeaders($http) {
        $http.defaults.headers.common['Authorization'] = 'Basic Y2xpZW50YXBwOnNlY3JldA==';
        $http.defaults.headers.post["Content-Type"] = "application/json";
    }

    function languageConfigure($translateProvider) {
        $translateProvider.useStaticFilesLoader({
            prefix: './translations/',
            suffix: '.json'
        }).preferredLanguage('EN')
            .useLocalStorage()
            .useMissingTranslationHandlerLog()
            .useSanitizeValueStrategy('escape');
    }

    function trackJsConfigure($provide, apiConstants) {
        if (!apiConstants.jsAlertDisabled) {
            $provide.decorator("$exceptionHandler", ["$delegate", "$window", function ($delegate, $window) {
                return function (exception, cause) {
                    window.TrackJS && TrackJS.track(exception);
                };
            }]);
        }
    }

})();
