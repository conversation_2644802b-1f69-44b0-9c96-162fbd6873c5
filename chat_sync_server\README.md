# Multi-room Chat Application

A multi-user multi-room chat application built with nodejs and socket.io.

<img src="chat-app.png" />

Inspired from - http://psitsmike.com/2011/10/node-js-and-socket-io-multiroom-chat-tutorial/

Features:
 - Multiple users can chat together in real-time
 - Users can create new rooms
 - Users can join/leave different rooms
 - All users get notified when a user joins/leaves their current room 

To install dependency:

`npm install`

To run app:

`node server`

Open your browser and go to:

`localhost:5001`
