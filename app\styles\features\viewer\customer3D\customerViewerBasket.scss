.basket-container {
    background-color: #FFFFFF;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 1rem;
    border: 1px solid #DDDDDD;
    width: 100%;
    max-width: 30vw;
    min-width: 30vw;
    max-height: 47vh;
    height: auto;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 1;

    &.banner-active {
            max-height: 40vh;
        }

                @media (max-width: 1200px) {
                    
                }
            
                @media (max-width: 992px) {
                    max-width: 50vw;
                    min-width: 50vw;
                    max-height: 80vh;
                }
            
                @media (max-width: 768px) {
                    max-width: 50vw;
                    min-width: 50vw;
                    max-height: 80vh;
                }
            
                @media (max-width: 576px) {
                    max-width: 50vw;
                    min-width: 50vw;
                    max-height: 80vh;
                }
}

.basket-container .basket-heading {
    background-color: #F1F1F1;
    padding: 1rem;
    border-top-left-radius: 1rem;
    border-top-right-radius: 1rem;
}

.basket-container .basket-content {
    flex: 1;
    overflow-y: auto;
    max-height: 40vh;
    border-bottom: solid 1px lightgrey;
}

.basket-container .table-no-responsive {
    width: 100%;
    table-layout: fixed;
}

.basket-container .table-no-responsive th,
.basket-container .table-no-responsive td {
    word-wrap: break-word;
}

.basket-container .btn {
    margin: 1rem;
    align-self: flex-end;
}
