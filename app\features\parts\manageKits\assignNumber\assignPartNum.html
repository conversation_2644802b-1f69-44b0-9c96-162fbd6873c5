<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" ng-click="assignMPCtrl.cancel()" aria-label="Close">
        <i class="fa fa-close" aria-hidden="true"></i>
    </button>
    <h2 class="modal-title" translate>KIT_ASSEMBLY.ASSIGN_PART_KIT</h2>
</div>

<div class="modal-body">
    <form ng-submit="assignMPCtrl.create()">
        <div class="radio mb-4">
            <label class="flex cadGap font-weight-bold mb-0 cursor-pointer">
                <input class="m-0 cursor-pointer" type="radio" ng-model="assignMPCtrl.actionType" value="existing"
                    ng-change="assignMPCtrl.clearInputs()" checked>
                {{'KIT_ASSEMBLY.ASSIGN_EXISTING_PART' | translate}}
            </label>
        </div>
        <div class="radio">
            <label class="flex cadGap font-weight-bold mb-0 cursor-pointer">
                <input class="m-0 cursor-pointer" type="radio" ng-model="assignMPCtrl.actionType" value="new"
                    ng-change="assignMPCtrl.clearInputs()">
                {{'KIT_ASSEMBLY.CREATE_PART' | translate}}
            </label>
        </div>
        <hr class="my-4" style="border-color: #d3d3d3;">

        <div ng-if="assignMPCtrl.actionType === 'existing'">
            <div class="m-0 d-flex">
                <div class="input-group flex-nowrap">
                    <input type="text" class="form-control mr-0"
                        placeholder="{{'KIT_ASSEMBLY.SEARCH_PART_NUMBER' | translate}}"
                        ng-model="assignMPCtrl.searchQuery"
                        ng-keypress="($event.which === 13) && assignMPCtrl.search()" />
                    <div class="input-group-append">
                        <button class="input-group-text input-group-text-btn input-group-text-btn btn-anim cadGap"
                            type="button" ng-click="assignMPCtrl.search()">
                            <i class="pr-0 pr-md-3 fa fa-search"></i>
                            {{'KIT_ASSEMBLY.SEARCH_BUTTON' | translate}}
                        </button>
                    </div>
                </div>
            </div>

            <div class="modal-message" ng-if="!assignMPCtrl.isPartSearchMessageLoading && assignMPCtrl.searchPerformed">
                <div class="d-flex cadGap" ng-if="assignMPCtrl.partExists && !assignMPCtrl.partInAnotherKit">
                    <p class="font-weight-bold text-success" text-danger translate>KIT_ASSEMBLY.PART_EXISTS</p>
                    <p translate>KIT_ASSEMBLY.PART_EXISTS_CONT</p>
                </div>
                <p ng-if="assignMPCtrl.partInAnotherKit" class="font-weight-bold text-danger" translate>
                    KIT_ASSEMBLY.PART_IN_ANOTHER_KIT</p>
                <p class="font-weight-bold text-danger"
                    ng-if="!assignMPCtrl.isPartSearchMessageLoading && !assignMPCtrl.partExists" translate>
                    KIT_ASSEMBLY.PART_DOES_NOT_EXIST</p>
            </div>

            <div ng-if="assignMPCtrl.partExists">
                <h3 class="mb-0" translate>KIT_ASSEMBLY.PART_NUMBER</h3>
                <p>{{assignMPCtrl.partNumber}}</p>
                <h3 class="mb-0" translate>KIT_ASSEMBLY.DESC</h3>
                <p>{{assignMPCtrl.description}}</p>
            </div>
        </div>

        <div ng-if="assignMPCtrl.actionType === 'new'">
            <div title="Kit Part Number / Kit Part Description">
                <div class="modal-message mt-3">
                    <h3 translate>KIT_ASSEMBLY.PART_NUMBER</h3>
                    <input ng-model="assignMPCtrl.newPartNumber" name="newPartNumber" type="text"
                        placeholder="{{'KIT_ASSEMBLY.ENTER_PART' | translate}}" required />
                </div>
        
                <div class="modal-message mt-3 mb-3">
                    <div class="d-flex align-items-center cadGap">
                        <h3 translate>KIT_ASSEMBLY.DESC</h3>
                        <i class="fa fa-info-circle mb-1 cadBlue" aria-hidden="true" data-toggle="tooltip" data-placement="top"
                            title="{{'KIT_ASSEMBLY.PART_DESC_TOOLTIP' | translate}}"></i>
                    </div>
                    <input ng-model="assignMPCtrl.newDescription" name="newDescription" type="text"
                        placeholder="{{'KIT_ASSEMBLY.ENTER_DESC' | translate}}" required />
                </div>
            </div>
            <div class="modal-message mb-3">
                <div class="d-flex align-items-center cadGap">
                    <h3 translate>KIT_ASSEMBLY.DEFAULT_LANG</h3>
                    <i class="fa fa-info-circle mb-1 cadBlue" aria-hidden="true" data-toggle="tooltip" data-placement="top"
                        title="{{'KIT_ASSEMBLY.SPECIFY_LANGUAGE' | translate}}"></i>
                </div>
                <select class="custom_select" ng-model="assignMPCtrl.language"
                    ng-options="language as language.displayText for language in assignMPCtrl.languages" ng-required="true">
                    <option value="" disabled selected translate>KIT_ASSEMBLY.SELECT</option>
                </select>
            </div>
        </div>

        <div ng-if="assignMPCtrl.error !== ''" class="modal-message mt-2 mb-2" style="color: red">{{ assignMPCtrl.error
            }}</div>

        <div class="modal-actions">
            <button type="button" class="btn small secondary" ng-click="assignMPCtrl.cancel()"
                translate>KIT_ASSEMBLY.CANCEL</button>
            <button type="submit" class="btn small primary" ng-disabled="assignMPCtrl.submitDisabled()"
                translate>KIT_ASSEMBLY.ASSIGN</button>
        </div>
    </form>
</div>