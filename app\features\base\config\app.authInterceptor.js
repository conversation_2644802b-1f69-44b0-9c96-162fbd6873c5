(function () {
    'use strict';

    angular
        .module('cadshareApp')
        .factory('authInterceptor', authInterceptor);

    authInterceptor.$inject = ['$q', 'tokenService', 'headerBannerService', '$state'];

    function authInterceptor($q, tokenService, headerBannerService, $state) {
        return {
            'request': function (config) {
                //unless one of these specific endoints, content type header defaults to application/json
                if(config.url.indexOf('inventory') === -1 
                    && config.url.indexOf('translation') === -1 
                    && config.url.indexOf('uploadDetailsFile') === -1 
                    && config.url.indexOf('uploadSparePartIdentifiersFile') === -1 
                    && config.url.indexOf('priceList') === -1){
                    config.headers = config.headers || {};
                    config.headers["Content-Type"] = "application/json";
                }

                //if its one of the OAuth endpoints, ensure content type & authorisation headers
                if (config.url.indexOf('oauth') !== -1) {
                    config.data = config.data || {};
                    config.data = $.param(config.data);
                    config.headers["Content-Type"] = "application/x-www-form-urlencoded";
                    config.headers["Authorization"] = "Basic Y2xpZW50YXBwOnNlY3JldA==";
                    return config;
                
                //unless one of these listed endpoints, pass access token & language
                } else if (config.url.indexOf('user/password/reset') === -1 
                    && config.url.indexOf('user/password/forgot') === -1 
                    && config.url.indexOf('user/password/resend') === -1
                    && config.url.indexOf('user/manufacturers') === -1
                    && config.url.indexOf('amazonaws') === -1 
                    && config.url.indexOf('.html') === -1
                    && config.url.indexOf('lock') === -1 
                    && config.url.indexOf('manufacturer/subdomain/') === -1
                    && config.url.indexOf('preAuth') === -1 
                    && config.url.indexOf('translations') === -1) {
                    return new Promise(function(resolve) {
                        tokenService.getOauthToken().then((token)=>{
                            config.headers.Authorization = 'Bearer ' + token;
                            
                            if(localStorage.getItem("NG_TRANSLATE_LANG_KEY")){
                                config.params = config.params || {};
                                config.params.language = localStorage.getItem("NG_TRANSLATE_LANG_KEY");
                            }
                            resolve(config);
                        });
                    })
                //if Autodesk endpoints, use Autodesk access token
                } else if (config.url.indexOf('autodesk') !== -1 
                    && config.url.indexOf('/token/autodesk') === -1 
                    && config.url.indexOf('/autodeskresources') === -1) {
                        config.headers["Content-Type"] = "application/octet-stream";
                        config.headers.Authorization = 'Bearer ' + tokenService.getAutodeskToken();
                        return config;
                
                //if uploading files to AWS S3, config content type headers accordingly
                } else if (config.url.indexOf('amazonaws') !== -1) {
                    if (config.url.indexOf('.pdf') !== -1) {
                        config.headers["Content-Type"] = "application/pdf";
                    } else if (config.url.indexOf('.xlsx')!== -1) {
                        config.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                    }else{
                        config.headers["Content-Type"] = "image/png";
                    }
                    delete config.headers.Authorization;
                    return config;
                
                } else {
                    return config;
                }
            },

            'response': function (response) {
                if (response.status === 401) {
                    localStorage.clear();
                    return $q.reject(response);
                }
                return response || $q.when(response);
            },

            'responseError': function (response) {
                if (response.status <= 0) {
                    var currentPage = $state.current.name.toUpperCase();
                    var isLoginPage = (currentPage.includes("LOGIN") || currentPage.includes("FORGOTPASSWORD") || currentPage.includes("PASSWORD"));
                    if (!isLoginPage) {
                        headerBannerService.setNotification('ERROR', "Connection Refused", 5000);
                    }
                    return;
                }
                return $q.reject(response);
            },
        };
    }
})();
