<section class="m-5">

<div class="mb-5">

    <div>

        <h1 class="">{{'ASSIGN_PUBLICATIONS.ASSIGNED_FOR' | translate}} {{dpAssignPublicationsCtrl.customerName}}</h1>
        <p class="mb-0 " translate>ASSIGN_PUBLICATIONS.DESCRIPTION</p>

    </div>

</div>

<div class="d-flex justify-content-between align-items-center flex-wrap listBoxContainer">

    <div class="col-12 col-md-5 p-5 listBoxLeft d-flex flex-column">

        <h3 class="" translate>ASSIGN_PUBLICATIONS.AVAILABLE</h3>

        <ul class="list-unstyled">

            <div ng-click="dpAssignPublicationsCtrl.areAllAvailableSelected" ng-class="dpAssignPublicationsCtrl.areAllAvailableSelected ? 'assignlistboxHoverCustom' : 'assignlistboxStyleCustom'" class="px-3 px-lg-5">

                <div class="col-12">

                    <div class="d-flex justify-content-between align-items-center text-container">

                        <input title="Select All" type="checkbox" ng-model="dpAssignPublicationsCtrl.areAllAvailableSelected" ng-click="dpAssignPublicationsCtrl.toggleSelectAllAvailable()" class="mb-0 mr-5 checkbox">

                        <div class="input-group col mb-0">
                            <input ng-model="dpAssignPublicationsCtrl.availableSearchValue" type="search"
                                   class="form-control h-100 p-3 mr-0 ng-pristine ng-valid ng-empty ng-touched" name="SearchDualList"
                                   placeholder="{{'ASSIGN_PUBLICATIONS.SEARCH' | translate}}">
                            <span ng-if="dpAssignPublicationsCtrl.availableSearchValue != null" ng-click="dpAssignPublicationsCtrl.clearAvailableSearch()" class="search-clear"><i class="fas fa-times"></i></span>
                            <div class="input-group-append">
                                <span class="input-group-text"><i class="fa fa-search"></i></span>
                            </div>
                        </div>

                    </div>

                </div>

            </div>

            </label>

        </ul>

        <ul class="list-group list-unstyled">

            <div href="" ng-class="publication.selected ? 'assignlistboxHover' : 'assignlistboxStyle'" ng-click="publication.selected = !publication.selected; dpAssignPublicationsCtrl.updateAllAvailableCheckbox()" class="assignlistboxStyle py-3 px-3 py-lg-4 px-lg-5 mb-3" ng-repeat="publication in dpAssignPublicationsCtrl.availablePublications | filter : dpAssignPublicationsCtrl.availableSearchValue">

                <div class="d-flex col-12 justify-content-between align-items-center">

                    <div class="d-flex justify-content-between align-items-center text-container">

                        <input type="checkbox" ng-model="publication.selected" class="mb-0 mr-5 checkbox">

                        <p class="mb-0">
                            <strong> {{publication.name}} </strong>
                        </p>

                    </div>
                </div>

            </div>

                </label>

        </ul>

    </div>

    <div class="my-4 my-md-0 col-md-1 col-12 listbox_items d-flex justify-content-center text-center flex-md-column flex-row align-items-center">

        <button title="Move selected item from available to assigned." class="m-2 item btn assignlistboxLeft"
                ng-click="dpAssignPublicationsCtrl.moveSelectedFromAvailableToAssigned()">
            <i class="fas fa-angle-right"></i>
        </button>

        <button title="Move selected item from assigned to available." class="m-2 item btn assignlistboxRight"
                ng-click="dpAssignPublicationsCtrl.moveSelectedFromAssignedToAvailable()">
            <i class="fas fa-angle-left"></i>
        </button>

    </div>

    <div class="col-12 col-md-5 listBoxRight p-5 ">

        <h3 class="" translate>ASSIGN_PUBLICATIONS.ASSIGNED</h3>

        <ul class="list-unstyled">

            <div ng-click="dpAssignPublicationsCtrl.areAllAssignedSelected" ng-class="dpAssignPublicationsCtrl.areAllAssignedSelected ? 'assignlistboxHoverCustom' : 'assignlistboxStyleCustom'" class="px-3 px-lg-5">

                <div class="col-12">

                    <div class="d-flex justify-content-between align-items-center text-container">

                        <input title="Select All" type="checkbox" ng-model="dpAssignPublicationsCtrl.areAllAssignedSelected" ng-click="dpAssignPublicationsCtrl.toggleSelectAllAssigned()" class="mb-0 mr-5 checkbox">

                        <div class="input-group col mb-0">
                            <input ng-model="dpAssignPublicationsCtrl.assignedSearchValue" type="search"
                                   class="form-control p-3 h-100 mr-0 ng-pristine ng-valid ng-empty ng-touched" name="SearchDualList"
                                   placeholder="{{'ASSIGN_PUBLICATIONS.SEARCH' | translate}}">
                            <span ng-if="dpAssignPublicationsCtrl.assignedSearchValue != null" ng-click="dpAssignPublicationsCtrl.clearAssignedSearch()" class="search-clear"><i class="fas fa-times"></i></span>
                            <div class="input-group-append">
                                <span class="input-group-text"><i class="fa fa-search"></i></span>
                            </div>
                        </div>

                    </div>

                </div>

            </div>

            </label>

        </ul>

        <ul class="list-group list-unstyled">

            <li ng-class="publication.selected ? 'assignlistboxHover' : 'assignlistboxStyle'" ng-click="publication.selected = !publication.selected; dpAssignPublicationsCtrl.updateAllAssignedCheckbox()" class="assignlistboxStyle py-3 px-3 px-lg-5 mb-3"
                ng-repeat="publication in dpAssignPublicationsCtrl.assignedPublications | filter : dpAssignPublicationsCtrl.assignedSearchValue">

                <div class="d-flex col-12 justify-content-between align-items-center">

                    <div class="d-flex justify-content-between align-items-center text-container">

                    <input type="checkbox" ng-model="publication.selected" class="mb-0 mr-5  checkbox">
                    <p class="mb-0">
                        <strong> {{publication.name}} </strong>
                       </p>

                    </div>

                </div>

            </li>

        </ul>

    </div>

</div>

<div class="flex justify-content-end listboxButtons my-5">

    <button ng-click="dpAssignPublicationsCtrl.cancel()" class="btn primary px-5" translate>
        ASSIGN_PUBLICATIONS.RESET
    </button>

    <button ng-class="dpAssignPublicationsCtrl.isPageEdited ? 'btn primary' : 'btn-cancel'" ng-click="dpAssignPublicationsCtrl.save()" class="btn primary ml-3" translate>
        ASSIGN_PUBLICATIONS.SAVE_CHANGES
    </button>

</div>

</section>