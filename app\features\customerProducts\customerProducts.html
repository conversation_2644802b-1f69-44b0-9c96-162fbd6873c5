<!--<div ng-show="customerProductsCtrl.downloadInProgress">
    <div class="overlay-cover-tablet panel-dark form"></div>

    <div class="download-preloader-bg" style="margin-top:170px">
        <div>
            <i class="fas fa-sync-alt fa-spin"></i>

            <p id="loader-text">Please Wait&hellip; </p>

            <p>Viewable is currently downloading, this may take several minutes</p>
            <p>Downloading {{customerProductsCtrl.downloadText}}</p>
        </div>
    </div>
</div>-->

<section class="container-fluid p-5 d-flex flex-wrap">
    <!-- Side Menu -->
    <aside class="sub-tabs product-range m-4 m-md-3">
        <h1 class="menu-title">{{"CUST_PRODUCTS.CATEGORIES" | translate}}</h1>
        <ul
                class="scrollable-list"
                role="tablist"
                ranges-data="customerProductsCtrl.ranges"
                match-height="{{ customerProductsCtrl.isGridView ? '.customerProductsGrid' : '.table' }}"
                toggle-value="customerProductsCtrl.isGridView"
        >
            <li ng-class="{active: customerProductsCtrl.activeTab === 'ALL_PRODUCTS'}">
                <a class="truncate-text mb-0" ng-click="customerProductsCtrl.filterMachinesList('ALL_PRODUCTS')">
                    <span translate>CUST_PRODUCTS.ALL_PRODUCTS</span>
                </a>
            </li>
            <li
                    ng-repeat="category in customerProductsCtrl.ranges | orderBy:customerProductsCtrl.sortAndPin"
                    ng-class="{active: category.active}"
                    ng-if="category.name !== 'ALL_PRODUCTS'">
                <a ng-click="customerProductsCtrl.filterMachinesList(category.name)" ng-hide="category.name === null" title="{{category.name}}">
                    <span class="truncate-text">{{category.name}}</span>&nbsp;
                    <span ng-hide="category.count == 0" class="badge-small"> {{category.count}} </span>
                </a>
            </li>
        </ul>
    </aside>

    <!-- Main Content -->
    <div class="main-content flex-grow-1">
        <!-- Search and Header -->
        <div class="row justify-content-between align-items-center">
            <h1 class="feature-header mb-0 col-md-6" translate>CUST_PRODUCTS.SELECT_PART_TO_ORDER</h1>
            <search-filter
                class="col-12 col-md-3"
                state-name="'customerProducts'"
                value="customerProductsCtrl.searchValue"
                placeholder-key="CUST_PRODUCTS.SEARCH_BY_PRODUCTS"
            ></search-filter>

            <!-- <button class="btn btn-secondary" ng-click="customerProductsCtrl.toggleView()" style="margin-top: -18px; background-color: #007bff;">
                <i class="fa" ng-class="{'fa-list': !customerProductsCtrl.isGridView, 'fa-th-large': customerProductsCtrl.isGridView}"></i>
            </button>         -->
        </div>

        <!-- Products Grid -->
        <div class="customerProductsGrid flex" ng-show="customerProductsCtrl.isGridView">
            <div
                class="customerProductsItemContainer d-flex col-md-3 col-lg-3 flex-column px-0"
                ng-repeat="manual in customerProductsCtrl.publicationList | orderBy:customerProductsCtrl.order_item | filter :customerProductsCtrl.searchValue | limitTo : customerProductsCtrl.count  : customerProductsCtrl.start"
            >
                <div class="customerProductsItem m-4 m-md-3">
                    <div class="customerProductsImage" ng-click="customerProductsCtrl.displayModels(manual)">
                        <img
                            class="w-100"
                            ng-src="{{manual.coverImageUrl || './images/PDF-placeholder-landscape.png'}}"
                            alt="thumbnailProducts"
                        />

                        <div class="customerProductsHover">
                            <a class="btn primary view-all" href=""
                                ><i class="fa fa-eye" aria-hidden="true"></i> {{"GENERAL.VIEW" | translate}}</a
                            >
                        </div>
                    </div>

                    <div class="customerProductsInformationBox p-4">
                        <h3 class="text-overflow">{{manual.publicationName}}</h3>
                        <small>{{manual.serialNumber}} &nbsp;</small>
                        <div class="pull-right">{{"CUST_PRODUCTS.VIEWABLES" | translate}}: {{manual.attachedModels}}</div>

                        <p
                            ng-if="customerProductsCtrl.isTabletApp && customerProductsCtrl.isOnline && (!manual.is2d || manual.attachedModels > 1)"
                        >
                            <span ng-show="customerProductsCtrl.isManualDownloaded(manual.publicationId)"
                                ><button class="btn download-btn success" ng-click="customerProductsCtrl.reDownloadManual(manual)">
                                    Refresh Download <i class="fa fa-refresh"></i></button
                            ></span>
                            <span ng-hide="customerProductsCtrl.isManualDownloaded(manual.publicationId)"
                                ><button class="btn download-btn primary" ng-click="customerProductsCtrl.downloadManual(manual)">
                                    Download
                                </button></span
                            >
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Products List -->
        <table class="table table-bordered productsListTable" ng-show="!customerProductsCtrl.isGridView">
            <thead>
                <tr>
                    <th>Image</th>
                    <th>Product Name</th>
                    <th>Details</th>
                    <th>Action</th>
                </tr>
            </thead>

            <tbody>
                <tr
                    ng-repeat="manual in customerProductsCtrl.publicationList | orderBy:customerProductsCtrl.order_item | filter :customerProductsCtrl.searchValue | limitTo : customerProductsCtrl.count : customerProductsCtrl.start"
                >
                    <!-- Image Column -->
                    <td>
                        <div class="customerProductsImage">
                            <img
                                ng-src="{{manual.coverImageUrl || './images/PDF-placeholder-landscape.png'}}"
                                alt="thumbnailProducts"
                            />
                        </div>
                    </td>

                    <!-- Product Name Column -->
                    <td>
                        <h3 class="text-overflow">{{manual.publicationName}}</h3>
                    </td>

                    <!-- Details Column -->
                    <td>
                        <div>{{manual.serialNumber}} &nbsp;</div>
                        <div>{{"CUST_PRODUCTS.VIEWABLES" | translate}}: {{manual.attachedModels}}</div>
                    </td>

                    <!-- Action Column -->
                    <td>
                        <a class="btn primary view-all ng-binding" href="" ng-click="customerProductsCtrl.displayModels(manual)" ng-disabled="!customerProductsCtrl.hasFeaturedViewable(manual)"
                            ><i class="fa fa-eye" aria-hidden="true"></i> {{"GENERAL.VIEW" | translate}}</a
                        >

                        <!-- Buttons for Tablet App -->
                        <span
                            ng-if="customerProductsCtrl.isTabletApp && customerProductsCtrl.isOnline && (!manual.is2d || manual.attachedModels > 1)"
                        >
                            <span ng-show="customerProductsCtrl.isManualDownloaded(manual.publicationId)">
                                <button class="btn download-btn success" ng-click="customerProductsCtrl.reDownloadManual(manual)">
                                    Refresh Download <i class="fa fa-refresh"></i>
                                </button>
                            </span>
                            <span ng-hide="customerProductsCtrl.isManualDownloaded(manual.publicationId)">
                                <button class="btn download-btn primary" ng-click="customerProductsCtrl.downloadManual(manual)">
                                    Download
                                </button>
                            </span>
                        </span>
                    </td>
                </tr>
            </tbody>
        </table>

        <h2 ng-hide="customerProductsCtrl.publicationList.length >0" translate>CUST_PRODUCTS.NO_PUBLICATIONS_ASSIGNED</h2>

        <div ng-show="customerProductsCtrl.totalItems > 0">
            <uib-pagination
                force-ellipses="true"
                max-size="4"
                total-items="customerProductsCtrl.totalItems"
                ng-model="customerProductsCtrl.currentPage"
                items-per-page="customerProductsCtrl.itemPerPage"
                ng-change="customerProductsCtrl.pageChanged()"
                boundary-links="true"
                class="pagination-md"
                previous-text="&lsaquo;"
                next-text="&rsaquo;"
                first-text="&laquo;"
                last-text="&raquo;"
            ></uib-pagination>
        </div>
    </div>
</section>