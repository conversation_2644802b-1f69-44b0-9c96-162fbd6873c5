(function () {
    'use strict';

    angular
        .module('app.viewer')
        .directive('linkedPartSummary', linkedPartSummary);

    function linkedPartSummary() {
        var directive = {
            restrict: 'E',
            templateUrl: 'features/viewer/extensions/linkedPart/linkedPartSummary/linkedPartSummary.html',
            controller: 'LinkedPartSummaryController',
            controllerAs: 'linkedPartSummaryCtrl',
            bindToController: true
        };
        return directive;
    }

})();