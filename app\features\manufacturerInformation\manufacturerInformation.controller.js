(function () {
    'use strict';

    angular
        .module('app.manufacturerInformation')
        .controller('ManufacturerInformationController', ManufacturerInformationController);

    ManufacturerInformationController.$inject = ['$uibModal', '$state', 'headerBannerService', 'userService', 'manufacturerService', 'viewerSettingsService', '$translate'];

    function ManufacturerInformationController($uibModal, $state, headerBannerService, userService, manufacturerService, viewerSettingsService, $translate) {
        var vm = this;

        vm.isEdit = false;
        vm.count = 8;
        vm.currentPage = 1;
        vm.itemPerPage = 8;
        vm.start = 0;
        vm.sortReverse = false;
        vm.endRecord = vm.itemPerPage;
        vm.areUsersLoaded = false;
        vm.successMessage = "";
        vm.filter_range = "";
        vm.ranges = [];
        vm.sortReverse = true;
        vm.admin_sort = 'userId';
        vm.phone = "";
        vm.email = "";
        vm.emailSignature = "";
        vm.logoResponseUrl = "";
        vm.additionalEmails = [];

        vm.viewerColour = userService.getBackgroundColour();
        vm.edgingEnabledDefault = userService.getEdgingEnabledDefault();
        vm.contactUsPageEnabled = userService.getContactUsPageEnabled();
        vm.userId = userService.getUserId();

        vm.pageChanged = pageChanged;
        vm.deleteUser = deleteUser;
        vm.resetPassword = resetPassword;
        vm.editUser = editUser;
        vm.createUser = createUser;
        vm.addLogoImage = addLogoImage;
        vm.saveDetails = saveDetails;
        vm.editDetails = editDetails;
        vm.cancelDetails = cancelDetails;
        vm.addAdditionalEmail = addAdditionalEmail;
        vm.removeAdditionalEmail = removeAdditionalEmail;


        var selectedUser = {};

        var MACHINE, RANGE, CREATED_DATE, UPDATED_SUCCESS, USER, BY_CONFIRMING, EMAIL_LINK, SEND_RESET;
        function getTranslations(){
            $translate(['ADMIN.MACHINE', 'ADMIN.RANGE', 'ADMIN.CREATED_DATE', 'ADMIN.UPDATED_SUCCESS', 'ADMIN.USER', 'ADMIN.EMAIL_LINK', 'ADMIN.BY_CONFIRMING', 'ADMIN.SEND_RESET'])
                .then(function (resp) {
                    MACHINE = resp["ADMIN.MACHINE"];
                    RANGE = resp["ADMIN.RANGE"];
                    CREATED_DATE = resp["ADMIN.CREATED_DATE"];
                    UPDATED_SUCCESS = resp["ADMIN.UPDATED_SUCCESS"];
                    USER = resp["ADMIN.USER"];
                    EMAIL_LINK = resp["ADMIN.EMAIL_LINK"];
                    BY_CONFIRMING = resp["ADMIN.BY_CONFIRMING"];
                    SEND_RESET = resp["ADMIN.SEND_RESET"];
                    vm.sortBy = [
                        {name: 'name', value: MACHINE},
                        {name: 'rangeName', value: RANGE},
                        {name: 'createdDate', value: CREATED_DATE}
                    ];
                });
        }

        getTranslations();
        initialize();


        function initialize() {
            fetchManufacturerDetails();
            fetchUsers();

        }

        function fetchManufacturerDetails() {
            manufacturerService.fetchManufacturerDetails()
                .then(fetchManufacturerDetailsSuccess, fetchManufacturerDetailsFailed)
        }

        function fetchManufacturerDetailsSuccess(response) {
            var details = response.data;
            if (details) {
                vm.phone = details.phone;
                vm.email = details.supportEmail;
                vm.emailSignature = details.emailSignature;
                vm.logoResponseUrl = details.logoUrl;
                vm.additionalEmails = details.additionalEmails || [];
            }
        }

        function fetchManufacturerDetailsFailed(error) {
            headerBannerService.setNotification('ERROR', error.data.error, 10000);
        }

        function saveDetails() {
            // Filter out empty additional emails before saving
            var filteredAdditionalEmails = vm.edit.additionalEmails ? vm.edit.additionalEmails.filter(function(email) {
                return email && email.trim() !== '';
            }) : [];

            manufacturerService.updateManufacturerDetails(vm.edit.phone, vm.edit.email, vm.edit.emailSignature, vm.logoResponseUrl, vm.edit.viewerColour, vm.edit.edgingEnabledDefault, vm.edit.contactUsPageEnabled, filteredAdditionalEmails)
                .then(updateManufacturerDetailsSuccess, updateManufacturerDetailsFailed)
        }

        function editDetails() {
            vm.edit = {
                phone: vm.phone,
                email: vm.email,
                emailSignature: vm.emailSignature,
                viewerColour: vm.viewerColour,
                edgingEnabledDefault: vm.edgingEnabledDefault,
                contactUsPageEnabled: vm.contactUsPageEnabled,
                additionalEmails: angular.copy(vm.additionalEmails)
            };
            vm.isEdit = true;
        }

        function cancelDetails() {
            vm.edit = {};
            vm.isEdit = false;
        }

        function updateManufacturerDetailsSuccess() {
            headerBannerService.setNotification('SUCCESS', UPDATED_SUCCESS, 5000);
            vm.isEdit = false;
            vm.phone = vm.edit.phone;
            vm.email = vm.edit.email;
            vm.emailSignature = vm.edit.emailSignature;
            vm.viewerColour = vm.edit.viewerColour;
            vm.edgingEnabledDefault = vm.edit.edgingEnabledDefault;
            vm.contactUsPageEnabled = vm.edit.contactUsPageEnabled;
            vm.additionalEmails = angular.copy(vm.edit.additionalEmails);
            userService.setBackgroundColour(vm.edit.viewerColour)
            userService.setEdgingEnabledDefault(vm.edit.edgingEnabledDefault)
            userService.setContactUsPageEnabled(vm.edit.contactUsPageEnabled)
            vm.edit = {};
        }

        function updateManufacturerDetailsFailed(error) {
            headerBannerService.setNotification('ERROR', error.data.error, 10000);
        }

        function fetchUsers() {
            userService.getInternalUsers()
                .then(getInternalUsersSuccess, getInternalUsersFailed)
        }

        function getInternalUsersSuccess(response) {
            vm.userList = response.data;
            vm.totalItems = vm.userList.length;
            vm.areUsersLoaded = true;
        }

        function getInternalUsersFailed(error) {
            vm.areUsersLoaded = false;
            headerBannerService.setNotification('ERROR', error.data.error, 10000);
        }

        function pageChanged() {
            vm.start = ((vm.currentPage - 1) * vm.itemPerPage);
        }

        function deleteUser(user) {
            vm.successMessage = "";
            var deleteObject = {
                name: USER + user.firstName + " " + user.lastName,
                id: user.userId,
                url: '/user/' + user.userId
            };

            $uibModal.open({
                templateUrl: 'features/shared/commonDelete/deleteDialogBox.html',
                controller: 'DeleteController',
                controllerAs: 'deleteCtrl',
                size: 'sm',
                resolve: {
                    deleteObject: function () {
                        return deleteObject;
                    }
                }
            }).result.then(function () {
                fetchUsers();
            });
        }

        function resetPassword(user) {
            selectedUser = user;
            var confirmObject = {
                titleText: SEND_RESET,
                bodyText: BY_CONFIRMING + " \"" + user.firstName + " " + user.lastName + "\" " + EMAIL_LINK
            };
            $uibModal.open({
                templateUrl: 'features/shared/confirmationModal/confirmModal.html',
                controller: 'ConfirmModalController',
                controllerAs: 'confirmModalCtrl',
                size: 'sm',
                resolve: {
                    confirmObject: function () {
                        return confirmObject;
                    }
                }
            }).result
                .then(sendPasswordEmailConfirmed, doNothing);
        }

        function sendPasswordEmailConfirmed() {
            userService.sendResetPassword(selectedUser.userId);
        }

        function doNothing() {
            //do nothing
        }

        function editUser(user) {
            var createObject = {
                type: "Internal",
                permissionsArray: user.userPermissions,
                firstName: user.firstName,
                lastName: user.lastName,
                emailAddress: user.emailAddress,
                userId: user.userId,
                active: user.active,
                userStatus: user.userStatus
            };
            $uibModal.open({
                templateUrl: 'features/shared/editUserModal/editUser.html',
                controller: 'EditUserController',
                controllerAs: 'editUserCtrl',
                size: 'md',
                backdrop: 'static',
                resolve: {
                    createObject: function () {
                        return createObject;
                    }
                }
            })
                .result.then(function () {
                $state.reload()
            });
        }

        function createUser() {
            var createObject = {type: "Internal"};
            $uibModal.open({
                templateUrl: 'features/shared/createUserModal/createUser.html',
                controller: 'CreateUserController',
                controllerAs: 'createUserCtrl',
                size: 'md',
                backdrop: 'static',
                resolve: {
                    createObject: function () {
                        return createObject;
                    }
                }
            })
                .result.then(function () {
                $state.reload()
            });
        }

        function addLogoImage() {
            var cropType = 'LOGO';

            $uibModal.open({
                templateUrl: 'features/products/imageCropper/imageCropper.html',
                controller: 'ImageCropperController',
                controllerAs: 'imageCropperCtrl',
                size: 'xl',
                backdrop: 'static',
                resolve: {
                    cropType: function () {
                        return cropType;
                    }
                }
            }).result.then(function (response) {
                if (response) {
                    vm.logoResponseUrl = response;
                }
            });
        }

        function addAdditionalEmail() {
            if (!vm.edit) {
                vm.edit = {};
            }
            if (!vm.edit.additionalEmails) {
                vm.edit.additionalEmails = [];
            }
            vm.edit.additionalEmails.push('');
        }

        function removeAdditionalEmail(index) {
            if (vm.edit && vm.edit.additionalEmails && index >= 0 && index < vm.edit.additionalEmails.length) {
                vm.edit.additionalEmails.splice(index, 1);
            }
        }

    }
})();
