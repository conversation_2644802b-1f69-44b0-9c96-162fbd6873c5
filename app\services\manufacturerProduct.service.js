(function () {
    'use strict';

    angular
        .module('app.services')
        .factory('manufacturerProductService', manufacturerProductService);

    manufacturerProductService.$inject = ['$http', 'apiConstants', 'userService', 'headerBannerService'];

    function manufacturerProductService($http, apiConstants, userService, headerBannerService) {
        return {
            fetchMachines: fetchMachines,
            getProductsByManufacturer: getProductsByManufacturer,
            createNewRange: createNewRange,
            createModel: createModel,
            createMachine: createMachine,
            getRangeByManufacturer: getRangeByManufacturer,
            editMachine: editMachine,
            getProductsByManufacturer: getProductsByManufacturer,
            getTechDocs: getTechDocs,
            createTechDoc: createTechDoc,
            editTechDoc: editTechDoc,
            deleteTechDoc: deleteTechDoc,
            getVideos: getVideos,
            createVideo: createVideo,
            editVideo: editVideo,
            deleteVideo: deleteVideo,
            getKits: getKits
        };

        function fetchMachines() {
            var manufacturerId = userService.getManufacturerId();
            return $http.get(apiConstants.url + '/manufacturer/' + manufacturerId + '/machines', null);
        }

        function getProductsByManufacturer() {
            var manufacturerId = userService.getManufacturerId();
            return $http.get(apiConstants.url + '/manufacturers/' + manufacturerId + '/products', null);
        }

        function getRangeByManufacturer() {
            var manufacturerId = userService.getManufacturerId();
            var url = apiConstants.url + '/manufacturers/' + manufacturerId + '/product-ranges';
            return $http.get(url);
        }

        function createMachine(rangeId, name, thumnailUrl) {
            var machineData = createMachineData(name, rangeId, thumnailUrl);
            return $http.post(apiConstants.url + '/machine', machineData);
        }

        function createMachineData(name, rangeId, thumnailUrl) {
            return {
                "name": name,
                "description": "",
                "rangeId": rangeId,
                "thumbnailUrl": thumnailUrl
            };
        }

        function createNewRange(range) {
            var manufacturerId = userService.getManufacturerId();
            var rangeData = {
                "name": range,
                "description": "",
                "manufacturerId": manufacturerId
            };
            return $http.post(apiConstants.url + '/range', rangeData);
        }

        function createModel(modelName, machineId, urn, topLevelAssembly, fileType, fileName, originalFileName) {

            var data = {
                "modelName": modelName,
                "machineId": machineId,
                "modelDescription": "",
                "autodeskUrn": urn,
                "topLevelAssembly": topLevelAssembly,
                "fileType": fileType,
                "filename": fileName,
                "originalFilename": originalFileName
            };
            return $http.post(apiConstants.url + '/model', data);
        }

        function editMachine(rangeId, name, machineId, thumbnailUrl) {
            var updateData = {
                "name": name,
                "description": "",
                "rangeId": parseInt(rangeId),
                "thumbnailUrl": thumbnailUrl
            };
            return $http.put(apiConstants.url + '/machine/' + machineId, updateData);
        }


        function getTechDocs() {
            return $http.get(apiConstants.url + '/techDoc');
        }

        function createTechDoc(name, description, url, filename, pageCount) {
            var techDocData = {
                name: name,
                description: description,
                url: url,
                filename: filename,
                pageCount: pageCount
            };
            return $http.post(apiConstants.url + '/techDoc', techDocData);
        }

        function editTechDoc(name, description, url, filename, pageCount, techDocId) {
            var techDocData = {
                name: name,
                description: description,
                url: url,
                filename: filename,
                pageCount: pageCount
            };
            return $http.put(apiConstants.url + '/techDoc/' + techDocId, techDocData);
        }

        function deleteTechDoc(techDocId){
            return $http.delete(apiConstants.url + '/techDoc/' + techDocId);
        }
        function getVideos() {
            return $http.get(apiConstants.url + '/video');
        }

        function createVideo(name, description, url) {
            var videoData = {
                name: name,
                description: description,
                url: url
            };
            return $http.post(apiConstants.url + '/video', videoData);
        }

        function editVideo(name, description, url, videoId) {
            var videoData = {
                name: name,
                description: description,
                url: url
            };
            return $http.put(apiConstants.url + '/video/' + videoId, videoData);
        }

        function deleteVideo(videoId){
            return $http.delete(apiConstants.url + '/video/' + videoId);
        }

        function getKits() {
            var manufacturerId = userService.getManufacturerId();
            return $http.get(apiConstants.url + "/manufacturers/" + manufacturerId + "/master-part-kits");
        }

    }
})();
