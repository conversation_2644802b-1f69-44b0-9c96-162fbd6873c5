// Content for 'cadshare_extension.js'

var _viewerstate = null;
var _translateTool = null;

//Variable to save translated and roated model ids
var transformedFragIdMap = {};
var _rotateTool = null;
var _eventHandleTool = null;
var viewer = null;

//Variable to store Explode scale
var currentExplodeScale;

var translateBtn = null;
var txTool = null;
var rotateTool = null;

// Variables to save & manage operations data
var explodeNodeIdsWithParent = [];
var explodeNodeIdsAsSingleEntity = [];
var nodeIdsSetsExplodeAsSingleEntity = [];
var nodesToActuallyExplode = [];

var fragPositionArray = []; //Contains translation vector of translated fragments using translate tool
var translatedNodeIds = [];
var translatedFragIds = [];
var rotatedFragIds = [];
var recentlyUpdatedFragIds = [];

var rangeVal = 0;

var rotatedNodeIds = []; //currently not using
var allFragmentsPositionArray = []; //currently not using

function doExplosion() {
    var explodeRange = document.getElementById("explode_range");
    rangeVal = explodeRange.value / 100;
    //document.getElementById("demo").innerHTML = rangeVal;
    if (document.getElementById("manufacturerViewerId")) {
        nodesToActuallyExplode = angular
            .element(document.getElementById("manufacturerViewerId"))
            .scope()
            .accordion.groups["0"].$$childTail.modelBrowserCtrl.calculateExplodeNodes();

        if (nodesToActuallyExplode.length > 500 && rangeVal) {
            angular.element(document.getElementById("manufacturerViewerId")).scope().manufacturerViewerCtrl.showSpinner();

            setTimeout(() => {
                explodeModel(rangeVal, explodeNodeIdsWithParent, nodesToActuallyExplode);
                angular.element(document.getElementById("manufacturerViewerId")).scope().manufacturerViewerCtrl.hideSpinner();
            }, 100);
        } else {
            explodeModel(rangeVal, explodeNodeIdsWithParent, nodesToActuallyExplode);
        }

        var sharingViewer = viewer.getExtension("SharingViewer");
        if (sharingViewer !== null) {
            GLOBAL_SHARING_EVENT("EXPLODE");
        }
    } else if (document.getElementById("customerViewerId")) {
        nodesToActuallyExplode = angular
            .element(document.getElementById("customerViewerId"))
            .scope()
            .accordion.groups["0"].$$childTail.modelBrowserCtrl.calculateExplodeNodes();

        if (nodesToActuallyExplode.length > 500 && rangeVal) {
            angular.element(document.getElementById("customerViewerId")).scope().customerViewerCtrl.showSpinner();

            setTimeout(() => {
                explodeModel(rangeVal, explodeNodeIdsWithParent, nodesToActuallyExplode);
                angular.element(document.getElementById("customerViewerId")).scope().customerViewerCtrl.hideSpinner();
            }, 100);
        } else {
            explodeModel(rangeVal, explodeNodeIdsWithParent, nodesToActuallyExplode);
        }

        var sharingViewer = viewer.getExtension("SharingViewer");
        if (sharingViewer !== null) {
            GLOBAL_SHARING_EVENT("EXPLODE");
        }
    } else if (document.getElementById("softCopyViewerIdentifier")) {
        nodesToActuallyExplode = angular
            .element(document.getElementById("softCopyViewerIdentifier"))
            .scope()
            .accordion.groups["0"].$$childTail.modelBrowserCtrl.calculateExplodeNodes();

        if (nodesToActuallyExplode.length > 500 && rangeVal) {
            angular.element(document.getElementById("softCopyViewerIdentifier")).scope().softCopyViewerCtrl.showSpinner();

            explodeModel(rangeVal, explodeNodeIdsWithParent, nodesToActuallyExplode);
            angular.element(document.getElementById("softCopyViewerIdentifier")).scope().softCopyViewerCtrl.hideSpinner();
        } else {
            explodeModel(rangeVal, explodeNodeIdsWithParent, nodesToActuallyExplode);
        }
    } else if (document.getElementById("workInstructionsManufacturerViewerId")) {
        nodesToActuallyExplode = angular
            .element(document.getElementById("workInstructionsManufacturerViewerId"))
            .scope()
            .accordion.groups["0"].$$childTail.modelBrowserCtrl.calculateExplodeNodes();

        if (nodesToActuallyExplode.length > 500 && rangeVal) {
            angular
                .element(document.getElementById("workInstructionsManufacturerViewerId"))
                .scope()
                .manufacturerWorkInstructionsCtrl.showSpinner();

            explodeModel(rangeVal, explodeNodeIdsWithParent, nodesToActuallyExplode);
            angular
                .element(document.getElementById("workInstructionsManufacturerViewerId"))
                .scope()
                .manufacturerWorkInstructionsCtrl.hideSpinner();
        } else {
            explodeModel(rangeVal, explodeNodeIdsWithParent, nodesToActuallyExplode);
        }
    }
}

function CADShareExtension(viewer, options) {
    Autodesk.Viewing.Extension.call(this, viewer, options);

    _translateTool = new TranslateTool(viewer);
    viewer.toolController.registerTool(_translateTool);

    _rotateTool = new RotateTool(viewer);
    viewer.toolController.registerTool(_rotateTool);

    _eventHandleTool = new EventHandleTool(viewer);
    viewer.toolController.registerTool(_eventHandleTool);
}

CADShareExtension.prototype = Object.create(Autodesk.Viewing.Extension.prototype);
CADShareExtension.prototype.constructor = CADShareExtension;

CADShareExtension.prototype.load = function () {
    viewer = this.viewer;

    // viewer.addEventListener(
    // Autodesk.Viewing.SELECTION_CHANGED_EVENT,
    // onItemSelected);

    //Elements to get input from the viewer

    var explodeRange = document.getElementById("explode_range");
    if (explodeRange) {
        explodeRange.addEventListener("input", doExplosion);
    }

    // var explodeBtn = document.getElementById('explode_button');
    // explodeBtn.addEventListener('click', function() {
    // for (var itr = 0; itr < 100; itr++) {
    // var rangeVal = itr/100;
    // explodeModel(rangeVal, explodeNodeIdsWithParent, nodeIdsSetsExplodeAsSingleEntity);
    // }
    // });

    // var debugBtn = document.getElementById('dubug_button');
    // debugBtn.addEventListener('click', function() {
    // explodeRange.valueAsNumber -= 1;
    // var rangeVal = explodeRange.value/100;
    // document.getElementById("demo").innerHTML = rangeVal;
    // explodeModel(rangeVal, explodeNodeIdsWithParent, nodeIdsSetsExplodeAsSingleEntity);
    // });

    //Translate control
    /*  translateBtn = document.getElementById('translate_button');
        if(translateBtn){
            translateBtn.addEventListener('click', function() {
                txTool = _translateTool.getName();
                 if(viewer.toolController.getActiveTool().activeName === txTool){
                    viewer.toolController.deactivateTool(txTool);
                    translateBtn.style.background= "#4696D1";
                }else{
                    viewer.toolController.activateTool(txTool);
                    translateBtn.style.background= "#5cb85c";
                }
            });
        }*/

    //Rotate control
    // var rotateBtn = document.getElementById('rotate_button');
    // rotateBtn.addEventListener('click', function() {
    //     var txTool = _translateTool.getName();
    //     var rxTool = _rotateTool.getName();
    //     viewer.toolController.deactivateTool(txTool);
    //     viewer.toolController.activateTool(rxTool);
    // });

    /*  var save_viewerstateBtn = document.getElementById('SaveStateButton');
        save_viewerstateBtn.addEventListener('click', function() {
            _viewerstate = viewer.getState();
            _viewerstate.name = "saved_viewerstate";
        });*/

    /* var restore_viewerstateBtn = document.getElementById('RestoreStateButton');
       restore_viewerstateBtn.addEventListener('click', function() {
           angular.element(document.getElementById('manufacturerViewerId')).scope().adminViewerCtrl.test();
       });*/

    // Added context menu options to get input from the viewer
    /*    viewer.registerContextMenuCallback(  'MyChangingColorMenuItems', ( menu, status ) => {
              if( status.hasSelected ) {
              menu.push({
                  title: 'Explode as Single Entity',
                  target: () => {
                  var selectedItems = this.viewer.getAggregateSelection();
              addNodeIdsToExplodeAsSingle(selectedItems);
              var rangeValue = explodeRange.value/100;
              refreshExplode( explodeNodeIdsWithParent, nodeIdsSetsExplodeAsSingleEntity);
          }
          });
              menu.push({
                  title: 'Explode with respective parent',
                  target: () => {
                  var selectedItems = this.viewer.getAggregateSelection();
              addNodeIdsToExplodeWithParent(selectedItems);
              var rangeValue = explodeRange.value/100;
              refreshExplode( explodeNodeIdsWithParent, nodeIdsSetsExplodeAsSingleEntity);
          }
          });
          }
      });*/

    //On loading geometry activating event handling tool
    this.viewer.addEventListener(Autodesk.Viewing.GEOMETRY_LOADED_EVENT, this.onGeometryLoaded);

    return true;
};

CADShareExtension.prototype.onGeometryLoaded = function () {
    var eventTool = _eventHandleTool.getName();
    viewer.toolController.activateTool(eventTool);
};

CADShareExtension.prototype.unload = function () {
    _viewerstate = null;
    _translateTool = null;
    _rotateTool = null;
    _eventHandleTool = null;
    viewer = null;

    translateBtn = null;
    txTool = null;

    //rotate parts of model
    rotateTool = null;

    // iables to save & manage operations data
    explodeNodeIdsWithParent = [];
    explodeNodeIdsAsSingleEntity = [];
    nodeIdsSetsExplodeAsSingleEntity = [];
    nodesToActuallyExplode = [];

    fragPositionArray = []; //Contains translation vector of translated fragments using translate tool
    translatedNodeIds = [];
    translatedFragIds = [];
    rotatedFragIds = [];
    recentlyUpdatedFragIds = [];

    rangeVal = 0;
    return true;
};

CADShareExtension.prototype.getState = function (viewerState) {
    saveTransform(viewerState);
};

/**Function to animate the model ids
 * Set play true for animation
 * @param {*} isPlay
 */
CADShareExtension.prototype.setPlay = function (isPlay) {
    this.play = isPlay;
};

CADShareExtension.prototype.restoreState = function (viewerState, immediate) {
    // animate model is play is set true
    if (this.play) this.animateModel(viewerState, immediate);
    else restoreTransform(viewerState, immediate);

    //console.log("frag bounding");
    //calculateFragBounding(viewer);
};

/**Function to animate the model ids
 * Animate translated and rotated parts of model
 * @param {*} viewerState
 * @param {*} immediate
 */
CADShareExtension.prototype.animateModel = function (viewerState, immediate) {
    this.setPlay(false);
    rangeVal = viewerState.extensionScale;
    if (document.getElementById("manufacturerViewerId")) {
        var explodeRange = document.getElementById("explode_range");
        explodeRange.value = rangeVal * 100;
    } else if (document.getElementById("workInstructionManufacturerViewerId")) {
        var explodeRange = document.getElementById("explode_range");
        explodeRange.value = rangeVal * 100;
    } else if (document.getElementById("customerViewerId")) {
        var explodeRange = document.getElementById("explode_range");
        explodeRange.value = rangeVal * 100;
    }
    explodeNodeIdsWithParent = viewerState.explodeNodeIdsWithParent;
    nodesToActuallyExplode = viewerState.nodesToActuallyExplode ? viewerState.nodesToActuallyExplode : [];

    explodeModel(rangeVal, explodeNodeIdsWithParent, nodesToActuallyExplode);

    if (viewerState.transformedObjects) {
        const period = 1.8;

        const easingFunc = (t) => {
            return easeInOutExpo(t, 0, 1, period * 0.7);
        };

        //   this.translatedNodeIds = viewerState.translatedNodeIds;
        this.animateTransform(viewerState, easingFunc, period).then(() => {
            currentExplodeScale = viewerState.explodeScale;
        });

        transformedFragIdMap = Object.assign({}, viewerState.transformedObjects);

        viewer.impl.sceneUpdated(true);
    }
};

/**Function to animate the model ids
 * Calculate transformed position for model ids
 * @param {*} targetState
 * @param {*} easing
 * @param {*} period
 * @returns
 */
CADShareExtension.prototype.animateTransform = function (targetState, easing, period = 2.0) {
    return new Promise(async (resolve, reject) => {
        const currentFragIds = Object.keys(transformedFragIdMap);

        const targetFragIds = Object.keys(targetState.transformedObjects);

        const fullFragIds = [...currentFragIds, ...targetFragIds];

        const fragProxyTasks = fullFragIds.map((fragId) => {
            const fragProxy = viewer.impl.getFragmentProxy(viewer.model, fragId);

            fragProxy.getAnimTransform();

            const targetTransform = targetState.transformedObjects[fragId] || {
                quaternion: { _x: 0, _y: 0, _z: 0, _w: 1 },
                position: { x: 0, y: 0, z: 0 },
            };

            fragProxy.step = {
                dx: (targetTransform.position.x - fragProxy.position.x) / period,
                dy: (targetTransform.position.y - fragProxy.position.y) / period,
                dz: (targetTransform.position.z - fragProxy.position.z) / period,

                dQx: (targetTransform.quaternion._x - fragProxy.quaternion._x) / period,
                dQy: (targetTransform.quaternion._y - fragProxy.quaternion._y) / period,
                dQz: (targetTransform.quaternion._z - fragProxy.quaternion._z) / period,
                dQw: (targetTransform.quaternion._w - fragProxy.quaternion._w) / period,
            };

            fragProxy.initialTransform = {
                quaternion: {
                    _x: fragProxy.quaternion._x,
                    _y: fragProxy.quaternion._y,
                    _z: fragProxy.quaternion._z,
                    _w: fragProxy.quaternion._w,
                },
                position: {
                    x: fragProxy.position.x,
                    y: fragProxy.position.y,
                    z: fragProxy.position.z,
                },
            };

            fragProxy.targetTransform = targetTransform;

            return fragProxy;
        });

        const fragProxies = await Promise.all(fragProxyTasks);

        // Create all fragment animation tasks
        const animationTasks = fragProxies.map((fragProxy) => {
            return {
                step: (dt) => {
                    fragProxy.quaternion._x += fragProxy.step.dQx * dt;
                    fragProxy.quaternion._y += fragProxy.step.dQy * dt;
                    fragProxy.quaternion._z += fragProxy.step.dQz * dt;
                    fragProxy.quaternion._w += fragProxy.step.dQw * dt;

                    fragProxy.position.x += fragProxy.step.dx * dt;
                    fragProxy.position.y += fragProxy.step.dy * dt;
                    fragProxy.position.z += fragProxy.step.dz * dt;

                    fragProxy.updateAnimTransform();
                },

                ease: (t) => {
                    const eased = easing(t / period);

                    const _targetQuat = fragProxy.targetTransform.quaternion;
                    const _initQuat = fragProxy.initialTransform.quaternion;

                    const initQuat = new THREE.Quaternion(_initQuat._x, _initQuat._y, _initQuat._z, _initQuat._w);

                    const targetQuat = new THREE.Quaternion(_targetQuat._x, _targetQuat._y, _targetQuat._z, _targetQuat._w);

                    initQuat.slerp(targetQuat, eased);

                    fragProxy.quaternion._x = initQuat.x;
                    fragProxy.quaternion._y = initQuat.y;
                    fragProxy.quaternion._z = initQuat.z;
                    fragProxy.quaternion._w = initQuat.w;

                    const targetPos = fragProxy.targetTransform.position;
                    const initPos = fragProxy.initialTransform.position;

                    fragProxy.position.x = eased * targetPos.x + (1 - eased) * initPos.x;
                    fragProxy.position.y = eased * targetPos.y + (1 - eased) * initPos.y;
                    fragProxy.position.z = eased * targetPos.z + (1 - eased) * initPos.z;

                    fragProxy.updateAnimTransform();
                },

                finalStep: () => {
                    fragProxy.quaternion._x = fragProxy.targetTransform.quaternion._x;
                    fragProxy.quaternion._y = fragProxy.targetTransform.quaternion._y;
                    fragProxy.quaternion._z = fragProxy.targetTransform.quaternion._z;
                    fragProxy.quaternion._w = fragProxy.targetTransform.quaternion._w;

                    fragProxy.position.copy(fragProxy.targetTransform.position);

                    fragProxy.updateAnimTransform();
                },
            };
        });

        // create explode animation task

        let scale = parseFloat(currentExplodeScale);

        const targetScale = parseFloat(targetState.explodeScale);

        if (targetScale != scale) {
            var scaleStep = (targetScale - scale) / period;

            animationTasks.push({
                step: (dt) => {
                    scale += scaleStep * dt;

                    selectiveExplode(viewer, scale, fullFragIds);
                },

                ease: (t) => {
                    const eased = easing(t / period);

                    const easedScale = scale + eased * (targetScale - scale);

                    selectiveExplode(viewer, easedScale, fullFragIds);
                },

                finalStep: () => {
                    selectiveExplode(viewer, targetScale, fullFragIds);

                    viewer.explodeSlider.value = targetScale;
                },
            });
        }

        let animationId = 0;
        let elapsed = 0;

        const stopwatch = new Stopwatch();

        const animateTransformStep = () => {
            const dt = stopwatch.getElapsedMs() * 0.001;

            elapsed += dt;

            if (elapsed < period) {
                animationTasks.forEach((task) => {
                    task.ease(elapsed);
                });

                animationId = requestAnimationFrame(animateTransformStep);
            } else {
                // end of animation
                animationTasks.forEach((task) => {
                    task.finalStep();
                });

                cancelAnimationFrame(animationId);

                viewer.autocam.shotParams.duration = 1.0;
            }

            viewer.impl.sceneUpdated(true);

            resolve();
        };

        viewer.autocam.shotParams.duration = period;

        animationId = requestAnimationFrame(animateTransformStep);
    });
};

/**Function to animate the model ids
 * Method to animate selected ids on viewer
 * @param {*} viewer
 * @param {*} scale
 * @param {*} excludedFragIds
 * @param {*} model
 */
function selectiveExplode(viewer, scale, excludedFragIds, model = null) {
    model = model || viewer.activeModel || viewer.model;

    var svf = model.getData();

    var mc = model.getVisibleBounds(true).center();

    var fragList = model.getFragmentList();

    var pt = new THREE.Vector3();

    // Input scale is in the range 0-1, where 0
    // means no displacement, and 1 maximum reasonable displacement.
    scale *= 2;

    // If we have a full part hierarchy we can use a
    // better grouping strategy when exploding
    if (svf.instanceTree && svf.instanceTree.nodeAccess.nodeBoxes && scale !== 0) {
        var scaledExplodeDepth = scale * (svf.instanceTree.maxDepth - 1) + 1;
        var explodeDepth = 0 | scaledExplodeDepth;
        var currentSegmentFraction = scaledExplodeDepth - explodeDepth;

        var it = svf.instanceTree;
        var tmpBox = new Float32Array(6);

        (function explodeRec(nodeId, depth, cx, cy, cz, ox, oy, oz) {
            var oscale = scale * 2;

            // smooth transition of this tree depth
            // from non-exploded to exploded state
            if (depth == explodeDepth) {
                oscale *= currentSegmentFraction;
            }

            it.getNodeBox(nodeId, tmpBox);

            var mycx = 0.5 * (tmpBox[0] + tmpBox[3]);
            var mycy = 0.5 * (tmpBox[1] + tmpBox[4]);
            var mycz = 0.5 * (tmpBox[2] + tmpBox[5]);

            if (depth > 0 && depth <= explodeDepth) {
                var dx = (mycx - cx) * oscale;
                var dy = (mycy - cy) * oscale;
                var dz = (mycz - cz) * oscale;

                // var omax = Math.max(dx, Math.max(dy, dz));
                ox += dx;
                oy += dy;
                oz += dz;
            }

            svf.instanceTree.enumNodeChildren(
                nodeId,
                function (dbId) {
                    explodeRec(dbId, depth + 1, mycx, mycy, mycz, ox, oy, oz);
                },
                false
            );

            svf.instanceTree.enumNodeFragments(
                nodeId,
                function (fragId) {
                    if (excludedFragIds.indexOf(fragId.toString()) < 0) {
                        pt.x = ox;
                        pt.y = oy;
                        pt.z = oz;

                        fragList.updateAnimTransform(fragId, null, null, pt);
                    }
                },
                false
            );
        })(svf.instanceTree.getRootId(), 0, mc.x, mc.y, mc.x, 0, 0, 0);
    } else {
        var boxes = fragList.fragments.boxes;

        var nbFrags = fragList.getCount();

        for (var fragId = 0; fragId < nbFrags; ++fragId) {
            if (excludedFragIds.indexOf(fragId.toString()) < 0) {
                if (scale == 0) {
                    fragList.updateAnimTransform(fragId);
                } else {
                    var box_offset = fragId * 6;

                    var cx = 0.5 * (boxes[box_offset] + boxes[box_offset + 3]);
                    var cy = 0.5 * (boxes[box_offset + 1] + boxes[box_offset + 4]);
                    var cz = 0.5 * (boxes[box_offset + 2] + boxes[box_offset + 5]);

                    cx = scale * (cx - mc.x);
                    cy = scale * (cy - mc.y);
                    cz = scale * (cz - mc.z);

                    pt.x = cx;
                    pt.y = cy;
                    pt.z = cz;

                    fragList.updateAnimTransform(fragId, null, null, pt);
                }
            }
        }
    }
}

/**Function to animate the model ids
 *
 * @returns
 */
function easeInOutExpo(t, b, c, d) {
    if (t === 0) {
        b;
    }
    if (t === d) {
        b + c;
    }
    if ((t /= d / 2) < 1) {
        return (c / 2) * Math.pow(2, 10 * (t - 1)) + b;
    } else {
        return (c / 2) * (-Math.pow(2, -10 * --t) + 2) + b;
    }
}

function addNodeIdsToExplodeWithParent(selectedItems) {
    explodeNodeIdsWithParent = [];
    var itr;
    for (itr = 0; itr < selectedItems.length; itr++) {
        explodeNodeIdsWithParent.push.apply(explodeNodeIdsWithParent, selectedItems[itr].selection);
    }
}

/*
function addNodeIdsToExplodeAsSingle(selectedItems) {
    explodeNodeIdsAsSingleEntity = [];
    var itr;
    for (itr = 0; itr < selectedItems.length; itr++) {
        explodeNodeIdsAsSingleEntity.push.apply(
            explodeNodeIdsAsSingleEntity,
            selectedItems[itr].selection
        );
    }
    //nodeIdsSetsExplodeAsSingleEntity.push.apply(nodeIdsSetsExplodeAsSingleEntity, explodeNodeIdsAsSingleEntity);
    nodeIdsSetsExplodeAsSingleEntity.push(explodeNodeIdsAsSingleEntity);
}*/

/*function addIdToExplodeAsSingleEntity(id) {
    if (nodeIdsSetsExplodeAsSingleEntity.indexOf(id) < 0) {
        nodeIdsSetsExplodeAsSingleEntity.push(id);
        refreshExplode(explodeNodeIdsWithParent, nodeIdsSetsExplodeAsSingleEntity);
        if (document.getElementById("manufacturerViewerId")) {
            angular
                .element(document.getElementById("manufacturerViewerId"))
                .scope()
                .manufacturerViewerCtrl.weldmentsUpdated(
                nodeIdsSetsExplodeAsSingleEntity
            );
        }
    }
}*/

function bulkAddIdToExplodeAsSingleEntity(ids) {
    nodeIdsSetsExplodeAsSingleEntity = ids;
    if (document.getElementById("manufacturerViewerId")) {
        angular
            .element(document.getElementById("manufacturerViewerId"))
            .scope()
            .manufacturerViewerCtrl.weldmentsUpdated(nodeIdsSetsExplodeAsSingleEntity);
    } else if (document.getElementById("customerViewerId")) {
        angular
            .element(document.getElementById("customerViewerId"))
            .scope()
            .customerViewerCtrl.weldmentsUpdated(nodeIdsSetsExplodeAsSingleEntity);
    } else if (document.getElementById("softCopyViewerIdentifier")) {
        angular
            .element(document.getElementById("softCopyViewerIdentifier"))
            .scope()
            .softCopyViewerCtrl.weldmentsUpdated(nodeIdsSetsExplodeAsSingleEntity);
    } else if (document.getElementById("workInstructionsManufacturerViewerId")) {
        angular
            .element(document.getElementById("workInstructionsManufacturerViewerId"))
            .scope()
            .manufacturerWorkInstructionsCtrl.weldmentsUpdated(nodeIdsSetsExplodeAsSingleEntity);
    }
}

function bulkAddIdToAxialExplodeAsSingleEntity(ids) {
    nodeIdsSetsExplodeAsSingleEntity = ids;
    if (document.getElementById("manufacturerViewerId")) {
        angular
            .element(document.getElementById("manufacturerViewerId"))
            .scope()
            .manufacturerViewerCtrl.weldmentsUpdated(nodeIdsSetsExplodeAsSingleEntity);
    }
}

/*function removeIdFromExplodeAsSingleEntity(id) {
    if (nodeIdsSetsExplodeAsSingleEntity.indexOf(id) > -1) {
        var index = nodeIdsSetsExplodeAsSingleEntity.indexOf(id);
        nodeIdsSetsExplodeAsSingleEntity.splice(index, 1);
        refreshExplode(explodeNodeIdsWithParent, nodeIdsSetsExplodeAsSingleEntity);
        if (document.getElementById("manufacturerViewerId")) {
            angular
                .element(document.getElementById("manufacturerViewerId"))
                .scope()
                .manufacturerViewerCtrl.weldmentsUpdated(
                nodeIdsSetsExplodeAsSingleEntity
            );
        }
    }
}
*/

/*function resetToHome(originalWeldments) {
    nodeIdsSetsExplodeAsSingleEntity = originalWeldments ? originalWeldments : [];
    fragPositionArray = [];
    translatedNodeIds = [];
    translatedFragIds = [];
    rotatedFragIds = [];

    if (document.getElementById('manufacturerViewerId')) {
        var explodeRange = document.getElementById('explode_range');
        explodeRange.value = 0;
    }
    explodeModel(0, [], nodeIdsSetsExplodeAsSingleEntity);
}*/

/** Sets animation transforms for all fragments to create an "exploded view": Each fragment is displaced
 * away from the model bbox center, so that you can distuinguish separate components.
 *
 * If the model data provides a model hierarchy (given via model.getData().instanceTree), it is also considered for the displacement.
 * In this case, we recursively shift each object away from the center of its parent node's bbox.
 *
 * @param {number} scale - In [0,1]. 0 means no displacement (= reset animation transforms).
 *                                   1 means maximum displacement, where the shift distance of an object varies
 *                                   depending on distance to model center and hierarchy level.
 * @param {array} nodeIdsToExplodeWithParent - Array of Node Ids to be explode with their respective parents.
 * @param {array} setsOfNodeIdsToExplodeAsSingle - Array of array that contains Sets of Node Ids to be explode as single entity.
 */
function explodeModel(scale, nodeIdsToExplodeWithParent, setsOfNodeIdsToExplodeAsSingle) {
    //axial explode
    var axisElement = document.getElementById("explode_axis");
    var selectedAxis = "Radial";
    //customer axial explode
    var explode_axis_customer = localStorage.getItem('EXPLODE_AXIS');
    if(explode_axis_customer) {
        selectedAxis = explode_axis_customer;
    }

    //manufacturer axial explode
    if (axisElement) {
        selectedAxis = axisElement.value;
    }
    //if (!_models.length)
    //return;
    var pt = new THREE.Vector3();
    //for (var q = 0; q < _models.length; q++) {
    var model = viewer.model; //_models[q];
    var it = model.getData().instanceTree;
    var fragList = model.getFragmentList();
    var mc = model.getVisibleBounds(true).getCenter();
    //Input scale is in the range 0-1, where 0
    //means no displacement, and 1 maximum reasonable displacement.
    scale *= 2;
    //If we have a full part hierarchy we can use a
    //better grouping strategy when exploding
    if (it && it.nodeAccess.nodeBoxes && scale !== 0) {
        // computing cumulative bounding boxes for the set of entities which needs to be explode as single
        var setOfCumulativeBoundingBoxes = [];
        var setOfEntitiesExplodeAsSingle = [];
        computeCumulativeBBox(it, setsOfNodeIdsToExplodeAsSingle, setOfCumulativeBoundingBoxes, setOfEntitiesExplodeAsSingle);
        nodeIdsToExplodeWithParent = addAllChildNodes(it, nodeIdsToExplodeWithParent);

        // If scale is small (close to 0), the shift is only applied to the topmost levels of the hierarchy.
        // With increasing s, we involve more and more hierarchy levels, i.e., children are recursively shifted
        // away from their parent node centers.
        // Since explodeValue is integer, it will behave discontinous during a transition from s=0 to s=1.
        // To keep the overall transition continuous, we use the fractional part of scaledExplodeDepth
        // to smoothly fade-in the transition at each hierarchy level.
        // levels beyond explodeDepth, we stop shifting children away from their parent.
        //
        var scaledExplodeDepth = scale * (it.maxDepth - 1) + 1;
        var explodeDepth = 0 | scaledExplodeDepth;
        var currentSegmentFraction = scaledExplodeDepth - explodeDepth;
        var tmpBox = new Float32Array(6);
        // Define recursive function to traverse object hierarchy. Each object is shifted away
        // from the bbox center of its parent.
        //  number nodeId:   dbId of the current instanceTree node
        //  int depth:       tracks hierarchy level (0 for root)
        //  vec3 (cx,cy,cz): center of the parent object (after applying the displacement to the parent object)
        //  vec3 (ox,oy,oz): accumuled displacement from all parents on the path to root
        (function explodeRec(nodeId, depth, cx, cy, cz, ox, oy, oz) {
            var oscale = scale * 2; //TODO: also possibly related to depth
            if (depth == explodeDepth) oscale *= currentSegmentFraction; //smooth transition of this tree depth from non-exploded to exploded _viewerstate
            // get bbox center of this node
            it.getNodeBox(nodeId, tmpBox);
            //var nodeName = it.getNodeName(nodeId);
            var mycx = 0.5 * (tmpBox[0] + tmpBox[3]);
            var mycy = 0.5 * (tmpBox[1] + tmpBox[4]);
            var mycz = 0.5 * (tmpBox[2] + tmpBox[5]);
            // The root node (depth==0) has no parent to shift away from.
            // For child nodes with level > explodDepth, we don't apply additional displacement anymore - just pass the displacement of the parents.
            if (
                depth > 0 &&
                depth <= explodeDepth &&
                nodeIdsToExplodeWithParent.indexOf(nodeId) < 0 &&
                translatedNodeIds.indexOf(nodeId) < 0
            ) {
                // add displacement to move this object away from its parent's bbox center (cx, cy, cz)

                var dx = (mycx - cx) * oscale;
                var dy = (mycy - cy) * oscale;
                var dz = (mycz - cz) * oscale;

                //var omax = Math.max(dx, Math.max(dy, dz));
                // sum up offsets: The final displacement of a node is accumulated by its own shift and
                // the shifts of all nodes up to the root.
                ox += dx;
                oy += dy;
                oz += dz;
            }

            //Handling the translatedNodeIds in this block
            if (translatedNodeIds.indexOf(nodeId) > -1) {
                var fragmentID = getFragId(it, nodeId);
                var translationVector = new THREE.Vector3();

                for (var index in fragPositionArray) {
                    if (fragPositionArray[index].key == fragmentID) {
                        translationVector = fragPositionArray[index].value;
                        break;
                    }
                }

                var dx = (mycx + translationVector.x - cx) * scale * 2;
                var dy = (mycy + translationVector.y - cy) * scale * 2;
                var dz = (mycz + translationVector.z - cz) * scale * 2;

                ox += dx;
                oy += dy;
                oz += dz;
            }

            // continue recursion with child objects (if any)
            it.enumNodeChildren(
                nodeId,
                function (dbId) {
                    explodeRec(dbId, depth + 1, mycx, mycy, mycz, ox, oy, oz);
                },
                false
            );
            pt.x = ox;
            pt.y = oy;
            pt.z = oz;

            var newTranslationvector = new THREE.Vector3();

            // set translation as anim transform for all fragments associated with the current node
            it.enumNodeFragments(
                nodeId,
                function (fragId) {
                    //axial explode
                    if (selectedAxis === "X") newTranslationvector.x = pt.x;
                    else if (selectedAxis === "Y") newTranslationvector.y = pt.y;
                    else if (selectedAxis === "Z") newTranslationvector.z = pt.z;
                    else {
                        newTranslationvector.x = pt.x;
                        newTranslationvector.y = pt.y;
                        newTranslationvector.z = pt.z;
                    }
                    //axial explode

                    //Adding respective translation to the translatedFragIds
                    if (translatedFragIds.indexOf(fragId) > -1) {
                        var isPositionUpdated = false;
                        var translationVector = new THREE.Vector3();
                        for (var index in fragPositionArray) {
                            if (fragPositionArray[index].key == fragId) {
                                translationVector = fragPositionArray[index].value;
                                isPositionUpdated = true;
                                break;
                            }
                        }
                        if (isPositionUpdated) {
                            newTranslationvector.x += translationVector.x;
                            newTranslationvector.y += translationVector.y;
                            newTranslationvector.z += translationVector.z;
                        }
                    }

                    fragList.updateAnimTransform(fragId, null, null, newTranslationvector);
                },
                false
            );
        })(it.getRootId(), 0, mc.x, mc.y, mc.x, 0, 0, 0); // run on root to start recursion

        explodeAsSingleEntity(
            it,
            fragList,
            scale,
            explodeDepth,
            currentSegmentFraction,
            setOfEntitiesExplodeAsSingle,
            setOfCumulativeBoundingBoxes,
            mc,
            selectedAxis
        );
    } else {
        // Float32Array array with 6 floats per bbox.
        var boxes = fragList.fragments.boxes;
        for (var i = 0, iEnd = fragList.getCount(); i < iEnd; i++) {
            if (scale == 0) {
                // reset to unexploded _viewerstate, i.e., remove all animation transforms

                //Avoiding resetting translation for transformed fragments
                var translationVector = new THREE.Vector3();
                var isTransformUpdated = false;
                var isPositionUpdated = false;
                if (translatedFragIds.indexOf(i) > -1) {
                    for (var index in fragPositionArray) {
                        if (fragPositionArray[index].key == i) {
                            translationVector = fragPositionArray[index].value;
                            isPositionUpdated = true;
                            break;
                        }
                    }
                }
                if (isPositionUpdated) {
                    fragList.updateAnimTransform(i, null, null, translationVector);
                    isTransformUpdated = true;
                }

                if (!isTransformUpdated) fragList.updateAnimTransform(i);
            } else {
                // get start index of the bbox for fragment i.
                var box_offset = i * 6;
                // get bbox center of fragment i
                var cx = 0.5 * (boxes[box_offset] + boxes[box_offset + 3]);
                var cy = 0.5 * (boxes[box_offset + 1] + boxes[box_offset + 4]);
                var cz = 0.5 * (boxes[box_offset + 2] + boxes[box_offset + 5]);
                // compute translation vector for this fragment:
                // We shift the fragment's bbox center c=(cx,cy,cz) away from the overall model center mc,
                // so that the distance between the two will finally be scaled up by a factor of (1.0 + scale).
                //
                pt.x = scale * (cx - mc.x);
                pt.y = scale * (cy - mc.y);
                pt.z = scale * (cz - mc.z);
                //fragList.updateAnimTransform(i, null, null, pt);
            }
        }
    }
    //}
    recentlyUpdatedFragIds = [];
    model.visibleBoundsDirty = true;
    viewer.impl.sceneUpdated(true);
}

function explodeAsSingleEntity(
    it,
    fragList,
    scale,
    explodeDepth,
    currentSegmentFraction,
    setOfEntitiesExplodeAsSingle,
    setOfCumulativeBoundingBoxes,
    mc,
    selectedAxis
) {
    var scaleFactor = scale * 2;
    var commonParentDepth = 0;
    var point = new THREE.Vector3();

    for (var key in setOfEntitiesExplodeAsSingle) {
        var commonParentNodeBoxCenter = new THREE.Vector3();
        var currentTransformOfCommonParent = new THREE.Vector3();
        var nodeIds = setOfEntitiesExplodeAsSingle[key].value;
        getCommonParentPosition(
            it,
            scale,
            explodeDepth,
            currentSegmentFraction,
            nodeIds,
            commonParentNodeBoxCenter,
            currentTransformOfCommonParent,
            mc,
            commonParentDepth
        );

        if (explodeDepth == commonParentDepth + 1) scaleFactor *= currentSegmentFraction;
        var bBox = setOfCumulativeBoundingBoxes[key].value;
        var myX = 0.5 * (bBox[0] + bBox[3]);
        var myY = 0.5 * (bBox[1] + bBox[4]);
        var myZ = 0.5 * (bBox[2] + bBox[5]);

        var translationVector = new THREE.Vector3();
        for (var itr = 0; itr < nodeIds.length; itr++) {
            var nodeId = nodeIds[itr];
            if (translatedNodeIds.indexOf(nodeId) > -1) {
                var fragmentID = getFragId(it, nodeId);
                if (fragmentID != null) {
                    for (var index in fragPositionArray) {
                        if (fragPositionArray[index].key == fragmentID) {
                            translationVector = fragPositionArray[index].value;
                            break;
                        }
                    }
                    break;
                }
            }
        }
        //axial explode
        if (selectedAxis === "X") {
            //if X axis is selected
            point.x = (myX + translationVector.x - commonParentNodeBoxCenter.x) * scaleFactor;
            point.x += currentTransformOfCommonParent.x + translationVector.x;
        } else if (selectedAxis === "Y") {
            //if Y axis is selected
            point.y = (myY + translationVector.y - commonParentNodeBoxCenter.y) * scaleFactor;
            point.y += currentTransformOfCommonParent.y + translationVector.y;
        } else if (selectedAxis === "Z") {
            //if Z axis is selected
            point.z = (myZ + translationVector.z - commonParentNodeBoxCenter.z) * scaleFactor;
            point.z += currentTransformOfCommonParent.z + translationVector.z;
        } else {
            // if no axis is selected explode everything
            point.x = (myX + translationVector.x - commonParentNodeBoxCenter.x) * scaleFactor;
            point.x += currentTransformOfCommonParent.x + translationVector.x;
            point.y = (myY + translationVector.y - commonParentNodeBoxCenter.y) * scaleFactor;
            point.y += currentTransformOfCommonParent.y + translationVector.y;
            point.z = (myZ + translationVector.z - commonParentNodeBoxCenter.z) * scaleFactor;
            point.z += currentTransformOfCommonParent.z + translationVector.z;
        }
        //axial explode

        for (var itr = 0; itr < nodeIds.length; itr++) {
            var nodeId = nodeIds[itr];
            // set translation as anim transform for all fragments associated with the current node
            it.enumNodeFragments(
                nodeId,
                function (fragId) {
                    fragList.updateAnimTransform(fragId, null, null, point);
                },
                false
            );
        }
    }
}

function computeCumulativeBBox(it, setsOfNodeIdsToExplodeAsSingle, setOfCumulativeBoundingBoxes, setOfEntitiesExplodeAsSingle) {
    for (var i = 0; i < setsOfNodeIdsToExplodeAsSingle.length; i++) {
        var NodeIds = addAllChildNodes(it, setsOfNodeIdsToExplodeAsSingle[i]);
        // var NodeIds = setsOfNodeIdsToExplodeAsSingle[i];
        var cumulativeBBox = new Float32Array(6);
        it.getNodeBox(NodeIds[0], cumulativeBBox);
        var minX = cumulativeBBox[0]; //Number.MAX_SAFE_INTEGER
        var minY = cumulativeBBox[1];
        var minZ = cumulativeBBox[2];
        var maxX = cumulativeBBox[3]; //Number.MIN_SAFE_INTEGER
        var maxY = cumulativeBBox[4];
        var maxZ = cumulativeBBox[5];
        for (var itr = 0; itr < NodeIds.length; itr++) {
            it.getNodeBox(NodeIds[itr], cumulativeBBox);
            minX = Math.min(minX, cumulativeBBox[0]);
            minY = Math.min(minY, cumulativeBBox[1]);
            minZ = Math.min(minZ, cumulativeBBox[2]);
            maxX = Math.max(maxX, cumulativeBBox[3]);
            maxY = Math.max(maxY, cumulativeBBox[4]);
            maxZ = Math.max(maxZ, cumulativeBBox[5]);
        }
        cumulativeBBox[0] = minX;
        cumulativeBBox[1] = minY;
        cumulativeBBox[2] = minZ;
        cumulativeBBox[3] = maxX;
        cumulativeBBox[4] = maxY;
        cumulativeBBox[5] = maxZ;

        // setOfCumulativeBoundingBoxes.push.apply(setOfCumulativeBoundingBoxes, cumulativeBBox);
        setOfCumulativeBoundingBoxes.push({ key: i, value: cumulativeBBox });
        setOfEntitiesExplodeAsSingle.push({ key: i, value: NodeIds });
    }
}

function getCommonParentId(it, setOfNodeIds) {
    /* var model = viewer.model //_models[q];
      var it = model.getData().instanceTree; */
    var nodeIdPath = [];
    var allNodePaths = [];

    //Getting path of all NodeIds from set
    (function explodeRec(nodeId, depth) {
        for (var itr = 0; itr <= setOfNodeIds.length; itr++) {
            if (nodeId == setOfNodeIds[itr]) {
                allNodePaths[nodeId] = nodeIdPath.slice(0, depth + 1);
                break;
            }
        }
        nodeIdPath[depth] = nodeId;

        it.enumNodeChildren(
            nodeId,
            function (dbId) {
                explodeRec(dbId, depth + 1);
            },
            false
        );
    })(it.getRootId(), 0); // run on root to start recursion

    //Getting common parent of all NodeIds from set
    var sameLevelNodeIds = [];
    var nodePath = [];
    var commonParent = null;
    var levelsMismatched = false;
    for (var level = 0; level <= it.maxDepth; level++) {
        sameLevelNodeIds = [];
        for (var index in allNodePaths) {
            nodePath = allNodePaths[index];
            levelNodeId = nodePath[level];
            if (levelNodeId != undefined) {
                sameLevelNodeIds.push(levelNodeId);
            } else {
                levelsMismatched = true;
                break;
            }
        }

        var allEqual = function allEqual(arr) {
            return arr.every(function (v) {
                return v == arr[0];
            });
        };
        if (allEqual(sameLevelNodeIds) && !levelsMismatched) {
            commonParent = sameLevelNodeIds[0];
        } else {
            break;
        }
    }

    if (commonParent == null) commonParent = it.getRootId();

    return commonParent;
}

function getCommonParentPosition(
    it,
    scale,
    explodeDepth,
    currentSegmentFraction,
    setOfNodeIds,
    commonParentNodeBoxCenter,
    currentTransformOfCommonParent,
    mc,
    commonParentDepth
) {
    var commonParentNodeId = getCommonParentId(it, setOfNodeIds);
    var nodeName = it.getNodeName(commonParentNodeId);
    var rootNodeId = it.getRootId();
    var parentNodePath = [];
    var tempNodeId = commonParentNodeId;
    while (true) {
        parentNodePath.push(tempNodeId);
        if (tempNodeId == rootNodeId) {
            break;
        }
        tempNodeId = it.getNodeParentId(tempNodeId);
    }

    var pathLength = parentNodePath.length - 1;
    var tmpBox = new Float32Array(6);
    (function explodeRec(nodeId, depth, cx, cy, cz, ox, oy, oz) {
        var oscale = scale * 2;
        if (depth == explodeDepth) oscale *= currentSegmentFraction;
        it.getNodeBox(nodeId, tmpBox);
        var mycx = 0.5 * (tmpBox[0] + tmpBox[3]);
        var mycy = 0.5 * (tmpBox[1] + tmpBox[4]);
        var mycz = 0.5 * (tmpBox[2] + tmpBox[5]);
        if (pathLength >= 0) {
            if (parentNodePath[pathLength] == nodeId) {
                pathLength--;
                if (depth > 0 && depth <= explodeDepth) {
                    var dx = (mycx - cx) * oscale;
                    var dy = (mycy - cy) * oscale;
                    var dz = (mycz - cz) * oscale;
                    ox += dx;
                    oy += dy;
                    oz += dz;
                }
            }
            if (pathLength == -1) {
                commonParentDepth = depth;
                currentTransformOfCommonParent.x = ox;
                currentTransformOfCommonParent.y = oy;
                currentTransformOfCommonParent.z = oz;
            }
        }

        it.enumNodeChildren(
            nodeId,
            function (dbId) {
                explodeRec(dbId, depth + 1, mycx, mycy, mycz, ox, oy, oz);
            },
            false
        );
    })(it.getRootId(), 0, mc.x, mc.y, mc.x, 0, 0, 0); // run on root to start recursion

    var rootNodeBBox = new Float32Array(6);
    it.getNodeBox(commonParentNodeId, rootNodeBBox);
    commonParentNodeBoxCenter.x = 0.5 * (rootNodeBBox[0] + rootNodeBBox[3]);
    commonParentNodeBoxCenter.y = 0.5 * (rootNodeBBox[1] + rootNodeBBox[4]);
    commonParentNodeBoxCenter.z = 0.5 * (rootNodeBBox[2] + rootNodeBBox[5]);
}

function addAllChildNodes(instanceTree, NodeIds) {
    var allChildNodeIds = [];
    for (var itr = 0; itr < NodeIds.length; itr++) {
        var nodeId = NodeIds[itr];
        instanceTree.enumNodeChildren(
            nodeId,
            function (dbId) {
                allChildNodeIds.push(dbId);
            },
            true
        );
    }
    return allChildNodeIds;
}

function getNodeId(it, reqFragId) {
    var retVal = null;
    for (var itr = 0; itr < translatedNodeIds.length; itr++) {
        var nodeId = translatedNodeIds[itr];
        it.enumNodeFragments(
            nodeId,
            function (fragId) {
                if (reqFragId == fragId) retVal = nodeId;
            },
            false
        );
        if (retVal != null) break;
    }
    return retVal;
}

function getFragId(it, reqNodeId) {
    var retVal = null;
    it.enumNodeFragments(
        reqNodeId,
        function (fragId) {
            retVal = fragId;
        },
        false
    );
    return retVal;
}

function updateFragPositionArray(fragId, position) {
    var isPositionUpdated = false;
    for (var index in fragPositionArray) {
        if (fragPositionArray[index].key == fragId) {
            fragPositionArray[index].value = position;
            isPositionUpdated = true;
        }
    }
    if (!isPositionUpdated) fragPositionArray.push({ key: fragId, value: position });
}

function saveTransform(viewerState) {
    if (rangeVal == undefined) rangeVal = 0;
    viewerState.extensionScale = rangeVal;

    //Storing all the variables which has operation data
    viewerState.fragPositionArray = JSON.parse(JSON.stringify(fragPositionArray)); //deep copying
    viewerState.translatedNodeIds = JSON.parse(JSON.stringify(translatedNodeIds));
    viewerState.translatedFragIds = JSON.parse(JSON.stringify(translatedFragIds));
    viewerState.rotatedFragIds = JSON.parse(JSON.stringify(rotatedFragIds));
    viewerState.explodeNodeIdsWithParent = JSON.parse(JSON.stringify(explodeNodeIdsWithParent));
    viewerState.nodeIdsSetsExplodeAsSingleEntity = JSON.parse(JSON.stringify(nodeIdsSetsExplodeAsSingleEntity));
    viewerState.nodesToActuallyExplode = JSON.parse(JSON.stringify(nodesToActuallyExplode));
    viewerState.recentlyUpdatedFragIds = JSON.parse(JSON.stringify(recentlyUpdatedFragIds));

    // Update the state for transformed Ids for animation
    currentExplodeScale = currentExplodeScale || viewer.getExplodeScale();

    viewerState.explodeScale = currentExplodeScale;
    viewerState.transformedObjects = {};
    for (const fragId in transformedFragIdMap) {
        const fragProxy = viewer.impl.getFragmentProxy(viewer.model, fragId);

        fragProxy.getAnimTransform();

        viewerState.transformedObjects[fragId] = {
            quaternion: fragProxy.quaternion,
            position: fragProxy.position,
        };
    }
    viewerState.transforms = {};
    for (var index in rotatedFragIds) {
        fragId = rotatedFragIds[index];
        var fragProxy = viewer.impl.getFragmentProxy(viewer.model, fragId);

        fragProxy.getAnimTransform();

        viewerState.transforms[fragId] = {
            quaternion: fragProxy.quaternion,
        };
    }

    //console.log(JSON.stringify(viewerState));
}

function restoreTransform(viewerState, immediate) {
    //Restoring all the variable which saves operation data
    fragPositionArray = viewerState.fragPositionArray;
    translatedNodeIds = viewerState.translatedNodeIds;
    translatedFragIds = viewerState.translatedFragIds;
    rotatedFragIds = viewerState.rotatedFragIds;
    explodeNodeIdsWithParent = viewerState.explodeNodeIdsWithParent;
    nodeIdsSetsExplodeAsSingleEntity = viewerState.nodeIdsSetsExplodeAsSingleEntity;

    nodesToActuallyExplode = viewerState.nodesToActuallyExplode ? viewerState.nodesToActuallyExplode : [];

    if (document.getElementById("manufacturerViewerId")) {
        angular
            .element(document.getElementById("manufacturerViewerId"))
            .scope()
            .manufacturerViewerCtrl.weldmentsUpdated(nodeIdsSetsExplodeAsSingleEntity);
    } else if (document.getElementById("customerViewerId")) {
        angular
            .element(document.getElementById("customerViewerId"))
            .scope()
            .customerViewerCtrl.weldmentsUpdated(nodeIdsSetsExplodeAsSingleEntity);
    } else if (document.getElementById("workInstructionsManufacturerViewerId")) {
        angular
            .element(document.getElementById("workInstructionsManufacturerViewerId"))
            .scope()
            .manufacturerWorkInstructionsCtrl.weldmentsUpdated(nodeIdsSetsExplodeAsSingleEntity);
    } else if (document.getElementById("softCopyViewerIdentifier")) {
        angular
            .element(document.getElementById("softCopyViewerIdentifier"))
            .scope()
            .softCopyViewerCtrl.weldmentsUpdated(nodeIdsSetsExplodeAsSingleEntity);
    }

    rotatedFragIds.forEach(function (fragId) {
        //var transform = state.transforms[fragId] || {
        var transform = viewerState.transforms[fragId] || {
            quaternion: { _x: 0, _y: 0, _z: 0, _w: 1 },
        };

        var fragProxy = viewer.impl.getFragmentProxy(viewer.model, fragId);

        fragProxy.getAnimTransform();

        fragProxy.quaternion._x = transform.quaternion._x;
        fragProxy.quaternion._y = transform.quaternion._y;
        fragProxy.quaternion._z = transform.quaternion._z;
        fragProxy.quaternion._w = transform.quaternion._w;

        fragProxy.updateAnimTransform();
    });

    rangeVal = viewerState.extensionScale;
    if (
        document.getElementById("manufacturerViewerId") ||
        document.getElementById("workInstructionsManufacturerViewerId") ||
        document.getElementById("customerViewerId")
    ) {
        var explodeRange = document.getElementById("explode_range");
        explodeRange.value = rangeVal * 100;
    }

    explodeModel(rangeVal, explodeNodeIdsWithParent, nodesToActuallyExplode);

    recentlyUpdatedFragIds = viewerState.recentlyUpdatedFragIds;

    recentlyUpdatedFragIds.forEach(function (fragId) {
        var position = new THREE.Vector3();
        for (var index in fragPositionArray) {
            if (fragPositionArray[index].key == fragId) {
                position = fragPositionArray[index].value;
                break;
            }
        }
        var fragProxy = viewer.impl.getFragmentProxy(viewer.model, fragId);

        fragProxy.getAnimTransform();
        fragProxy.position.x = position.x;
        fragProxy.position.y = position.y;
        fragProxy.position.z = position.z;

        fragProxy.updateAnimTransform();
        /*if (fragId == recentlyUpdatedFragIds[recentlyUpdatedFragIds.length -1]) {
            console.log("FRAG LOOP END " + fragId);
        }*/
    });
}

function getAllFragIdsInSetTranslate(selection, predefinedSetOfEntities) {
    var allNodeIdsInSet = [];
    var allFragIdsInSet = [];
    var model = this.viewer.model; //_models[q];
    var it = model.getData().instanceTree;
    mergeArrays(allNodeIdsInSet, selection.dbIdArray);
    for (var itr = 0; itr < selection.dbIdArray.length; itr++) {
        for (var i = 0; i < predefinedSetOfEntities.length; i++) {
            var NodeIds = addAllChildNodes(it, predefinedSetOfEntities[i]);
            //var NodeIds = predefinedSetOfEntities[i];

            if (NodeIds.indexOf(selection.dbIdArray[itr]) > -1) {
                mergeArrays(allNodeIdsInSet, NodeIds);
                break;
            }
        }
    }

    for (var iterator = 0; iterator < allNodeIdsInSet.length; iterator++) {
        var nodeId = allNodeIdsInSet[iterator];
        it.enumNodeFragments(
            nodeId,
            function (fragId) {
                allFragIdsInSet.push(fragId);
            },
            false
        );
    }

    mergeArrays(translatedNodeIds, allNodeIdsInSet);
    mergeArrays(translatedFragIds, allFragIdsInSet);
    mergeArrays(recentlyUpdatedFragIds, allFragIdsInSet);
    return allFragIdsInSet;
}

function getAllFragIdsInSetRotate(selection, predefinedSetOfEntities) {
    var allNodeIdsInSet = [];
    var allFragIdsInSet = [];
    var model = this.viewer.model; //_models[q];
    var it = model.getData().instanceTree;
    mergeArrays(allNodeIdsInSet, selection.dbIdArray);
    for (var itr = 0; itr < selection.dbIdArray.length; itr++) {
        for (var i = 0; i < predefinedSetOfEntities.length; i++) {
            var NodeIds = addAllChildNodes(it, predefinedSetOfEntities[i]);
            //var NodeIds = predefinedSetOfEntities[i];

            if (NodeIds.indexOf(selection.dbIdArray[itr]) > -1) {
                mergeArrays(allNodeIdsInSet, NodeIds);
                break;
            }
        }
    }

    for (var iterator = 0; iterator < allNodeIdsInSet.length; iterator++) {
        var nodeId = allNodeIdsInSet[iterator];
        it.enumNodeFragments(
            nodeId,
            function (fragId) {
                allFragIdsInSet.push(fragId);
            },
            false
        );
    }

    mergeArrays(translatedNodeIds, allNodeIdsInSet);
    mergeArrays(translatedFragIds, allFragIdsInSet);
    mergeArrays(recentlyUpdatedFragIds, allFragIdsInSet);
    mergeArrays(rotatedNodeIds, allNodeIdsInSet);
    mergeArrays(rotatedFragIds, allFragIdsInSet);
    return allFragIdsInSet;
}

function mergeArrays(arr1, arr2) {
    if (!arr1 || !arr2) return null;

    for (var itr = 0; itr < arr2.length; itr++) {
        if (!Array.isArray(arr1) || !arr1.length) {
            arr1.push(arr2[itr]);
            continue;
        }
        if (arr1.indexOf(arr2[itr]) == -1) {
            arr1.push(arr2[itr]);
        }
    }
    return arr1;
}

//////////////////////////////////////////////////////////////////
//Tool to handle events Mouse down & Selection changed so that they can used when activated transform tool
EventHandleTool.prototype.constructor = EventHandleTool;

function EventHandleTool(viewer) {
    this.viewer = viewer;
    this._event = null;
    this.active = false;
    this._selection = null;
    this.onAggregateSelectionChanged = this.onAggregateSelectionChanged.bind(this);
}

EventHandleTool.prototype.getNames = function () {
    return ["Viewing.EventHandle.Tool"];
};

EventHandleTool.prototype.getName = function () {
    return "Viewing.EventHandle.Tool";
};

EventHandleTool.prototype.activate = function () {
    if (!this.active) {
        this.active = true;

        this.viewer.addEventListener(Autodesk.Viewing.AGGREGATE_SELECTION_CHANGED_EVENT, this.onAggregateSelectionChanged);
    }
};

EventHandleTool.prototype.deactivate = function () {
    if (this.active) {
        this.active = false;
    }
};

EventHandleTool.prototype.handleButtonDown = function (event, button) {
    this._event = event;
};

EventHandleTool.prototype.onAggregateSelectionChanged = function (event) {
    if (event.selections && event.selections.length) {
        this._selection = event.selections[0];
    }
};

/////////////////////////////////////////////////////////////////
function TranslateTool(viewer) {
    this.active = false;

    this.viewer = viewer;

    this._hitPoint = null;

    this._isDragging = false;

    this.fullTransform = false;

    this._transformMesh = null;

    this._transformControlTx = null;

    this._selectedFragProxyMap = {};

    this._predefinedSetOfEntities = [];

    this.onTxChange = this.onTxChange.bind(this);

    this.onAggregateSelectionChanged = this.onAggregateSelectionChanged.bind(this);

    this.onCameraChanged = this.onCameraChanged.bind(this);
}

TranslateTool.prototype.constructor = TranslateTool;

/////////////////////////////////////////////////////////////////
//
//
/////////////////////////////////////////////////////////////////
TranslateTool.prototype.getNames = function () {
    return ["Viewing.Transform.Tool"];
};

/////////////////////////////////////////////////////////////////
//
//
/////////////////////////////////////////////////////////////////
TranslateTool.prototype.getName = function () {
    return "Viewing.Transform.Tool";
};

///////////////////////////////////////////////////////////////////////////
// Creates a dummy mesh to attach control to
//
///////////////////////////////////////////////////////////////////////////
TranslateTool.prototype.createTransformMesh = function () {
    var material = new THREE.MeshPhongMaterial({ color: 0xff0000 });

    this.viewer.impl.matman().addMaterial("transform-tool-material", material, true);

    var sphere = new THREE.Mesh(new THREE.SphereGeometry(0.0001, 5), material);

    sphere.position.set(0, 0, 0);

    return sphere;
};

///////////////////////////////////////////////////////////////////////////
// on translation change
//
///////////////////////////////////////////////////////////////////////////
TranslateTool.prototype.onTxChange = function () {
    if (this._isDragging && this._transformControlTx.visible) {
        var translation = new THREE.Vector3(
            this._transformMesh.position.x - this._selection.model.offset.x,
            this._transformMesh.position.y - this._selection.model.offset.y,
            this._transformMesh.position.z - this._selection.model.offset.z
        );

        for (var fragId in this._selectedFragProxyMap) {
            var fragProxy = this._selectedFragProxyMap[fragId];

            var position = new THREE.Vector3(
                this._transformMesh.position.x - fragProxy.offset.x,
                this._transformMesh.position.y - fragProxy.offset.y,
                this._transformMesh.position.z - fragProxy.offset.z
            );

            fragProxy.position = position;

            fragProxy.updateAnimTransform();

            updateFragPositionArray(fragId, position);

            //saveAllFragmentsPosition()

            //fragList.updateAnimTransform(fragId, null, null, position);
        }

        // this.emit('transform.translate', {
        // model: this._selection.model,
        // translation: translation
        // })

        // Update varibale for transformed model ids for animation
        var fragIds = Object.keys(this._selectedFragProxyMap);
        fragIds.forEach((fragId) => {
            transformedFragIdMap[fragId] = true;
        });
    }

    this.viewer.impl.sceneUpdated(true);
    var sharingViewer = viewer.getExtension("SharingViewer");
    if (sharingViewer !== null) GLOBAL_SHARING_EVENT("TRANSLATE");
};

///////////////////////////////////////////////////////////////////////////
// on camera changed
//
///////////////////////////////////////////////////////////////////////////
TranslateTool.prototype.onCameraChanged = function () {
    if (this._transformControlTx) {
        this._transformControlTx.update();
    }
};

///////////////////////////////////////////////////////////////////////////
// item selected callback
//
///////////////////////////////////////////////////////////////////////////
TranslateTool.prototype.onAggregateSelectionChanged = function (event) {
    if (event.selections && event.selections.length) {
        this._selection = event.selections[0];

        if (this.fullTransform) {
            this._selection.fragIdsArray = [];

            var fragCount = this._selection.model.getFragmentList().fragments.fragId2dbId.length;

            for (var fragId = 0; fragId < fragCount; ++fragId) {
                this._selection.fragIdsArray.push(fragId);
            }

            this._selection.dbIdArray = [];

            var instanceTree = this._selection.model.getData().instanceTree;

            var rootId = instanceTree.getRootId();

            this._selection.dbIdArray.push(rootId);
        }

        // this.emit('transform.modelSelected',
        // this._selection)

        this.initializeSelection(this._hitPoint);
    } else {
        this.clearSelection();
    }
};

TranslateTool.prototype.initializeSelection = function (hitPoint) {
    var _this = this;

    if (hitPoint == null) return;
    this._selectedFragProxyMap = {};

    this._predefinedSetOfEntities = nodeIdsSetsExplodeAsSingleEntity;

    var modelTransform = this._selection.model.transform || {
        translation: { x: 0, y: 0, z: 0 },
    };

    this._selection.model.offset = {
        x: hitPoint.x - modelTransform.translation.x,
        y: hitPoint.y - modelTransform.translation.y,
        z: hitPoint.z - modelTransform.translation.z,
    };

    this._transformControlTx.visible = true;

    this._transformControlTx.setPosition(hitPoint);

    this._transformControlTx.addEventListener("change", this.onTxChange);

    this.viewer.addEventListener(Autodesk.Viewing.CAMERA_CHANGE_EVENT, this.onCameraChanged);

    var allSelectedFragIdsInSet = getAllFragIdsInSetTranslate(this._selection, this._predefinedSetOfEntities);

    //this._selection.fragIdsArray.forEach((fragId)=> {
    allSelectedFragIdsInSet.forEach(function (fragId) {
        var fragProxy = _this.viewer.impl.getFragmentProxy(_this._selection.model, fragId);

        fragProxy.getAnimTransform();

        fragProxy.offset = {
            x: hitPoint.x - fragProxy.position.x,
            y: hitPoint.y - fragProxy.position.y,
            z: hitPoint.z - fragProxy.position.z,
        };

        _this._selectedFragProxyMap[fragId] = fragProxy;
    });
};

function Stopwatch() {
    this._lastTime = performance.now();
}

Stopwatch.prototype.start = function () {
    this._lastTime = performance.now();
};

Stopwatch.prototype.getElapsedMs = function () {
    var time = performance.now();

    var elapsedMs = time - this._lastTime;

    this._lastTime = time;

    return elapsedMs;
};

TranslateTool.prototype.clearSelection = function () {
    if (this.active) {
        this._selection = null;

        this._selectedFragProxyMap = {};

        this._transformControlTx.visible = false;

        this._transformControlTx.removeEventListener("change", this.onTxChange);

        this.viewer.removeEventListener(Autodesk.Viewing.CAMERA_CHANGE_EVENT, this.onCameraChanged);

        this.viewer.impl.sceneUpdated(true);
    }
};

///////////////////////////////////////////////////////////////////////////
// normalize screen coordinates
//
///////////////////////////////////////////////////////////////////////////
TranslateTool.prototype.normalize = function (screenPoint) {
    var viewport = this.viewer.navigation.getScreenViewport();

    var n = {
        x: (screenPoint.x - viewport.left) / viewport.width,
        y: (screenPoint.y - viewport.top) / viewport.height,
    };

    return n;
};

///////////////////////////////////////////////////////////////////////////
// get 3d hit point on mesh
//
///////////////////////////////////////////////////////////////////////////
TranslateTool.prototype.getHitPoint = function (event) {
    var screenPoint = {
        x: event.clientX,
        y: event.clientY,
    };

    var n = this.normalize(screenPoint);

    var hitPoint = this.viewer.utilities.getHitPoint(n.x, n.y);

    return hitPoint;
};

///////////////////////////////////////////////////////////////////
//
//
///////////////////////////////////////////////////////////////////
TranslateTool.prototype.activate = function () {
    if (!this.active) {
        this.active = true;

        var bbox = this.viewer.model.getBoundingBox();

        this.viewer.impl.createOverlayScene("TransformToolOverlay");

        this._transformControlTx = new THREE.TransformControls(this.viewer.impl.camera, this.viewer.impl.canvas, "translate");

        this._transformControlTx.setSize(bbox.getBoundingSphere().radius * 5);

        this._transformControlTx.visible = false;

        this.viewer.impl.addOverlay("TransformToolOverlay", this._transformControlTx);

        this._transformMesh = this.createTransformMesh();

        this._transformControlTx.attach(this._transformMesh);

        this.viewer.addEventListener(Autodesk.Viewing.AGGREGATE_SELECTION_CHANGED_EVENT, this.onAggregateSelectionChanged);

        var selection = _eventHandleTool._selection;

        var Event = _eventHandleTool._event;
        if (Event != null && selection != null) {
            this._selection = selection;
            this._hitPoint = this.getHitPoint(Event);
            this.initializeSelection(this._hitPoint);
        }
    }
};

///////////////////////////////////////////////////////////////////////////
// deactivate tool
//
///////////////////////////////////////////////////////////////////////////
TranslateTool.prototype.deactivate = function () {
    if (this.active) {
        this.active = false;

        this.viewer.impl.removeOverlay("TransformToolOverlay", this._transformControlTx);

        this._transformControlTx.removeEventListener("change", this.onTxChange);

        this.viewer.impl.removeOverlayScene("TransformToolOverlay");

        this.viewer.removeEventListener(Autodesk.Viewing.CAMERA_CHANGE_EVENT, this.onCameraChanged);

        this.viewer.removeEventListener(Autodesk.Viewing.AGGREGATE_SELECTION_CHANGED_EVENT, this.onAggregateSelectionChanged);
    }
};

///////////////////////////////////////////////////////////////////////////
//
//
///////////////////////////////////////////////////////////////////////////
TranslateTool.prototype.handleButtonDown = function (event, button) {
    this._hitPoint = this.getHitPoint(event);

    this._isDragging = true;

    if (this._transformControlTx.onPointerDown(event)) return true;

    return false;
};

///////////////////////////////////////////////////////////////////////////
//
//
///////////////////////////////////////////////////////////////////////////
TranslateTool.prototype.handleButtonUp = function (event, button) {
    this._isDragging = false;

    if (this._transformControlTx.onPointerUp(event)) {
        return true;
    }

    return false;
};

///////////////////////////////////////////////////////////////////////////
//
//
///////////////////////////////////////////////////////////////////////////
TranslateTool.prototype.handleMouseMove = function (event) {
    if (this._isDragging) {
        if (this._transformControlTx.onPointerMove(event)) {
            return true;
        }

        return false;
    }

    if (this._transformControlTx.onPointerHover(event)) return true;

    return false;
};

TranslateTool.prototype.handleKeyDown = function (event, keyCode) {
    //Escape keyCode is 27
    if (keyCode == 27) {
        this.deactivate();
        return false;
    }
};

///////////////////////////////////////////////////////////////////
//
//
///////////////////////////////////////////////////////////////////
function EventsEmitter() {
    this._events = {};
}

EventsEmitter.prototype.constructor = EventsEmitter;

///////////////////////////////////////////////////////////////////
// Supports multiple events space-separated
//
///////////////////////////////////////////////////////////////////
EventsEmitter.prototype.on = function (events, fct) {
    var _this2 = this;

    events.split(" ").forEach(function (event) {
        _this2._events[event] = _this2._events[event] || [];
        _this2._events[event].push(fct);
    });

    return this;
};

///////////////////////////////////////////////////////////////////
// Supports multiple events space-separated
//
///////////////////////////////////////////////////////////////////
EventsEmitter.prototype.off = function (events, fct) {
    var _this3 = this;

    if (events == undefined) {
        this._events = {};
        return;
    }

    events.split(" ").forEach(function (event) {
        if (event in _this3._events === false) return;

        if (fct) {
            _this3._events[event].splice(_this3._events[event].indexOf(fct), 1);
        } else {
            _this3._events[event] = [];
        }
    });

    return this;
};

///////////////////////////////////////////////////////////////////
//
//
///////////////////////////////////////////////////////////////////
EventsEmitter.prototype.emit = function (event /* , args... */) {
    if (this._events[event] === undefined) return;

    var tmpArray = this._events[event].slice();

    for (var i = 0; i < tmpArray.length; ++i) {
        var result = tmpArray[i].apply(this, Array.prototype.slice.call(arguments, 1));

        if (result !== undefined) return result;
    }

    return undefined;
};

///////////////////////////////////////////////////////////////////
//
//
///////////////////////////////////////////////////////////////////
EventsEmitter.prototype.guid = function () {
    var format = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : "xxxxxxxxxxxx";

    var d = new Date().getTime();

    var guid = format.replace(/[xy]/g, function (c) {
        var r = (d + Math.random() * 16) % 16 | 0;
        d = Math.floor(d / 16);
        return (c == "x" ? r : (r & 0x7) | 0x8).toString(16);
    });

    return guid;
};

/////////////////////////////////////////////////////////////////
// Class constructor
//
/////////////////////////////////////////////////////////////////
function RotateTool(viewer) {
    var _this4 = this;

    EventsEmitter.call(this);

    this.keys = {};

    this.active = false;

    this.viewer = viewer;

    this.fullTransform = false;

    this._predefinedSetOfEntities = [];

    this.allSelectedFragIdsInSet = [];

    this.viewer.toolController.registerTool(this);

    this.onAggregateSelectionChangedHandler = function (e) {
        _this4.onAggregateSelectionChanged(e);
    };
}

RotateTool.prototype = Object.create(EventsEmitter.prototype);
RotateTool.prototype.constructor = RotateTool;

/////////////////////////////////////////////////////////////////
// Enable tool
//
/////////////////////////////////////////////////////////////////
RotateTool.prototype.enable = function (enable) {
    var name = this.getName();

    if (enable) {
        this.viewer.toolController.activateTool(name);
    } else {
        this.viewer.toolController.deactivateTool(name);
    }
};

/////////////////////////////////////////////////////////////////
//
//
/////////////////////////////////////////////////////////////////
RotateTool.prototype.getNames = function () {
    return ["Viewing.Rotate.Tool"];
};

/////////////////////////////////////////////////////////////////
//
//
/////////////////////////////////////////////////////////////////
RotateTool.prototype.getName = function () {
    return "Viewing.Rotate.Tool";
};

///////////////////////////////////////////////////////////////////
// activate tool
//
///////////////////////////////////////////////////////////////////
RotateTool.prototype.activate = function () {
    if (!this.active) {
        this.active = true;

        this.viewer.addEventListener(Autodesk.Viewing.AGGREGATE_SELECTION_CHANGED_EVENT, this.onAggregateSelectionChangedHandler);

        var selection = _eventHandleTool._selection;
        if (selection != null) {
            this.selection = selection;
            this.emit("transform.modelSelected", this.selection);
            this.drawControl();
            this.viewer.fitToView(this.selection.dbIdArray);
        }
    }
};

///////////////////////////////////////////////////////////////////////////
// deactivate tool
//
///////////////////////////////////////////////////////////////////////////
RotateTool.prototype.deactivate = function () {
    if (this.active) {
        this.active = false;

        if (this.rotateControl) {
            this.rotateControl.remove();
            this.rotateControl = null;
        }

        this.viewer.removeEventListener(Autodesk.Viewing.AGGREGATE_SELECTION_CHANGED_EVENT, this.onAggregateSelectionChangedHandler);
    }
};

///////////////////////////////////////////////////////////////////////////
// Component Selection Handler
// (use Autodesk.Viewing.AGGREGATE_SELECTION_CHANGED_EVENT instead of
//  Autodesk.Viewing.SELECTION_CHANGED_EVENT - deprecated )
//
///////////////////////////////////////////////////////////////////////////
RotateTool.prototype.onAggregateSelectionChanged = function (event) {
    if (this.rotateControl && this.rotateControl.engaged) {
        this.rotateControl.engaged = false;

        this.viewer.select(this.selection.dbIdArray);

        return;
    }

    if (event.selections && event.selections.length) {
        var selection = event.selections[0];

        this.selection = selection;

        this.emit("transform.modelSelected", this.selection);

        if (this.fullTransform) {
            this.selection.fragIdsArray = [];

            var fragCount = selection.model.getFragmentList().fragments.fragId2dbId.length;

            for (var fragId = 0; fragId < fragCount; ++fragId) {
                this.selection.fragIdsArray.push(fragId);
            }

            this.selection.dbIdArray = [];

            var instanceTree = selection.model.getData().instanceTree;

            var rootId = instanceTree.getRootId();

            this.selection.dbIdArray.push(rootId);
        }

        this.drawControl();

        this.viewer.fitToView(this.selection.dbIdArray);
    } else {
        this.clearSelection();
    }
};

///////////////////////////////////////////////////////////////////////////
// Selection cleared
//
///////////////////////////////////////////////////////////////////////////
RotateTool.prototype.clearSelection = function () {
    this.selection = null;

    if (this.rotateControl) {
        this.rotateControl.remove();

        this.rotateControl = null;

        this.viewer.impl.sceneUpdated(true);
    }
};

///////////////////////////////////////////////////////////////////////////
// Draw rotate control
//
///////////////////////////////////////////////////////////////////////////
RotateTool.prototype.drawControl = function () {
    var _this5 = this;

    var bBox = this.geWorldBoundingBox(this.selection.fragIdsArray, this.selection.model.getFragmentList());

    this.center = new THREE.Vector3((bBox.min.x + bBox.max.x) / 2, (bBox.min.y + bBox.max.y) / 2, (bBox.min.z + bBox.max.z) / 2);

    var size = Math.max(bBox.max.x - bBox.min.x, bBox.max.y - bBox.min.y, bBox.max.z - bBox.min.z) * 0.8;

    if (this.rotateControl) {
        this.rotateControl.remove();
    }

    this.rotateControl = new RotateControl(this.viewer, this.center, size);

    this._predefinedSetOfEntities = nodeIdsSetsExplodeAsSingleEntity;
    this.allSelectedFragIdsInSet = getAllFragIdsInSetRotate(this.selection, this._predefinedSetOfEntities);

    this.rotateControl.on("transform.rotate", function (data) {
        _this5.rotateFragments(
            _this5.selection.model,
            // this.selection.fragIdsArray,
            _this5.allSelectedFragIdsInSet,
            data.axis,
            data.angle,
            _this5.center
        );

        _this5.viewer.impl.sceneUpdated(true);
    });
};

///////////////////////////////////////////////////////////////////////////
//
//
///////////////////////////////////////////////////////////////////////////
RotateTool.prototype.handleButtonDown = function (event, button) {
    if (this.rotateControl) {
        if (this.rotateControl.onPointerDown(event)) {
            return true;
        }
    }

    if (button === 0 && this.keys.Control) {
        this.isDragging = true;

        this.mousePos = {
            x: event.clientX,
            y: event.clientY,
        };

        return true;
    }

    return false;
};

///////////////////////////////////////////////////////////////////////////
//
//
///////////////////////////////////////////////////////////////////////////
RotateTool.prototype.handleButtonUp = function (event, button) {
    if (this.rotateControl) {
        this.rotateControl.onPointerUp(event);
    }

    if (button === 0) {
        this.isDragging = false;
    }

    return false;
};

///////////////////////////////////////////////////////////////////////////
//
//
///////////////////////////////////////////////////////////////////////////
RotateTool.prototype.handleMouseMove = function (event) {
    if (this.rotateControl) {
        this.rotateControl.onPointerHover(event);
    }

    if (this.isDragging) {
        if (this.selection) {
            var offset = {
                x: this.mousePos.x - event.clientX,
                y: event.clientY - this.mousePos.y,
            };

            this.mousePos = {
                x: event.clientX,
                y: event.clientY,
            };

            var angle = Math.sqrt(offset.x * offset.x + offset.y * offset.y);

            var sidewaysDirection = new THREE.Vector3();
            var moveDirection = new THREE.Vector3();
            var eyeDirection = new THREE.Vector3();
            var upDirection = new THREE.Vector3();
            var camera = this.viewer.getCamera();
            var axis = new THREE.Vector3();
            var eye = new THREE.Vector3();

            eye.copy(camera.position).sub(camera.target);

            eyeDirection.copy(eye).normalize();

            upDirection.copy(camera.up).normalize();

            sidewaysDirection.crossVectors(upDirection, eyeDirection).normalize();

            upDirection.setLength(offset.y);

            sidewaysDirection.setLength(offset.x);

            moveDirection.copy(upDirection.add(sidewaysDirection));

            axis.crossVectors(moveDirection, eye).normalize();

            this.rotateFragments(
                this.selection.model,
                // this.selection.fragIdsArray,
                this.allSelectedFragIdsInSet,
                axis,
                (angle * Math.PI) / 180,
                this.center
            );

            this.viewer.impl.sceneUpdated(true);
        }

        return true;
    }

    return false;
};

///////////////////////////////////////////////////////////////////////////
//
//
///////////////////////////////////////////////////////////////////////////
RotateTool.prototype.handleKeyDown = function (event, keyCode) {
    this.keys[event.key] = true;

    //Escape keyCode is 27
    if (keyCode == 27) this.deactivate();

    return false;
};

///////////////////////////////////////////////////////////////////////////
//
//
///////////////////////////////////////////////////////////////////////////
RotateTool.prototype.handleKeyUp = function (event, keyCode) {
    this.keys[event.key] = false;

    return false;
};

///////////////////////////////////////////////////////////////////////////
// Rotate selected fragments
//
///////////////////////////////////////////////////////////////////////////
RotateTool.prototype.rotateFragments = function (model, fragIdsArray, axis, angle, center) {
    var _this6 = this;

    var quaternion = new THREE.Quaternion();

    quaternion.setFromAxisAngle(axis, angle);

    fragIdsArray.forEach(function (fragId, idx) {
        var fragProxy = _this6.viewer.impl.getFragmentProxy(model, fragId);

        fragProxy.getAnimTransform();

        var position = new THREE.Vector3(fragProxy.position.x - center.x, fragProxy.position.y - center.y, fragProxy.position.z - center.z);

        position.applyQuaternion(quaternion);

        position.add(center);

        fragProxy.position = position;

        fragProxy.quaternion.multiplyQuaternions(quaternion, fragProxy.quaternion);

        if (idx === 0) {
            var euler = new THREE.Euler();

            euler.setFromQuaternion(fragProxy.quaternion, 0);

            _this6.emit("transform.rotate", {
                rotation: euler,
                model: model,
            });
        }

        updateFragPositionArray(fragId, fragProxy.position);

        fragProxy.updateAnimTransform();

        transformedFragIdMap[fragId] = true;
    });
};

///////////////////////////////////////////////////////////////////////////
// returns bounding box as it appears in the viewer
// (transformations could be applied)
//
///////////////////////////////////////////////////////////////////////////
RotateTool.prototype.geWorldBoundingBox = function (fragIds, fragList) {
    var fragbBox = new THREE.Box3();
    var nodebBox = new THREE.Box3();

    fragIds.forEach(function (fragId) {
        fragList.getWorldBounds(fragId, fragbBox);
        nodebBox.union(fragbBox);
    });

    return nodebBox;
};

function RotateControl(viewer, center, size) {
    EventsEmitter.call(this);

    this.engaged = false;

    this.overlayScene = "rotateControlScene";
    this.domElement = viewer.impl.canvas;
    this.camera = viewer.impl.camera;
    this.viewer = viewer;
    this.center = center;
    this.size = size;
    this.gizmos = [];

    this.viewer.impl.createOverlayScene(this.overlayScene);

    this.createAxis(center, new THREE.Vector3(1, 0, 0), size * 0.85, 0xff0000);

    this.createAxis(center, new THREE.Vector3(0, 1, 0), size * 0.85, 0x00ff00);

    this.createAxis(center, new THREE.Vector3(0, 0, 1), size * 0.85, 0x0000ff);

    // World UP = Y

    if (this.camera.worldup.y) {
        this.gizmos.push(
            this.createGizmo(
                center,
                new THREE.Euler(0, Math.PI / 2, 0),
                size * 0.0045,
                size * 0.8,
                0xff0000,
                Math.PI,
                new THREE.Vector3(1, 0, 0)
            )
        );

        this.gizmos.push(
            this.createGizmo(
                center,
                new THREE.Euler(Math.PI / 2, 0, 0),
                size * 0.0045,
                size * 0.8,
                0x00ff00,
                2 * Math.PI,
                new THREE.Vector3(0, 1, 0)
            )
        );

        this.gizmos.push(
            this.createGizmo(center, new THREE.Euler(0, 0, 0), size * 0.0045, size * 0.8, 0x0000ff, Math.PI, new THREE.Vector3(0, 0, 1))
        );
    } else {
        // World UP = Z

        this.gizmos.push(
            this.createGizmo(
                center,
                new THREE.Euler(Math.PI / 2, Math.PI / 2, 0),
                size * 0.0045,
                size * 0.8,
                0xff0000,
                Math.PI,
                new THREE.Vector3(1, 0, 0)
            )
        );

        this.gizmos.push(
            this.createGizmo(
                center,
                new THREE.Euler(Math.PI / 2, 0, 0),
                size * 0.0045,
                size * 0.8,
                0x00ff00,
                Math.PI,
                new THREE.Vector3(0, 1, 0)
            )
        );

        this.gizmos.push(
            this.createGizmo(center, new THREE.Euler(0, 0, 0), size * 0.0045, size * 0.8, 0x0000ff, 2 * Math.PI, new THREE.Vector3(0, 0, 1))
        );
    }

    this.picker = this.createSphere(size * 0.02);

    var material = new THREE.LineBasicMaterial({
        color: 0xffff00,
        linewidth: 1,
        depthTest: false,
        depthWrite: false,
        transparent: true,
    });

    this.angleLine = this.createLine(this.center, this.center, material);

    viewer.impl.sceneUpdated(true);
}

RotateControl.prototype = Object.create(EventsEmitter.prototype);
RotateControl.prototype.constructor = RotateControl;

///////////////////////////////////////////////////////////////////////////
// Draw a line
//
///////////////////////////////////////////////////////////////////////////
RotateControl.prototype.createLine = function (start, end, material) {
    var geometry = new THREE.Geometry();

    geometry.vertices.push(new THREE.Vector3(start.x, start.y, start.z));

    geometry.vertices.push(new THREE.Vector3(end.x, end.y, end.z));

    var line = new THREE.Line(geometry, material);

    this.viewer.impl.addOverlay(this.overlayScene, line);

    return line;
};

///////////////////////////////////////////////////////////////////////////
// Draw a cone
//
///////////////////////////////////////////////////////////////////////////
RotateControl.prototype.createCone = function (start, dir, length, material) {
    dir.normalize();

    var end = {
        x: start.x + dir.x * length,
        y: start.y + dir.y * length,
        z: start.z + dir.z * length,
    };

    var orientation = new THREE.Matrix4();

    orientation.lookAt(start, end, new THREE.Object3D().up);

    var matrix = new THREE.Matrix4();

    matrix.set(1, 0, 0, 0, 0, 0, 1, 0, 0, -1, 0, 0, 0, 0, 0, 1);

    orientation.multiply(matrix);

    var geometry = new THREE.CylinderGeometry(0, length * 0.2, length, 128, 1);

    var cone = new THREE.Mesh(geometry, material);

    cone.applyMatrix(orientation);

    cone.position.x = start.x + (dir.x * length) / 2;
    cone.position.y = start.y + (dir.y * length) / 2;
    cone.position.z = start.z + (dir.z * length) / 2;

    this.viewer.impl.addOverlay(this.overlayScene, cone);

    return cone;
};

///////////////////////////////////////////////////////////////////////////
// Draw one axis
//
///////////////////////////////////////////////////////////////////////////
RotateControl.prototype.createAxis = function (start, dir, size, color) {
    var end = {
        x: start.x + dir.x * size,
        y: start.y + dir.y * size,
        z: start.z + dir.z * size,
    };

    var material = new THREE.LineBasicMaterial({
        color: color,
        linewidth: 3,
        depthTest: false,
        depthWrite: false,
        transparent: true,
    });

    this.createLine(start, end, material);

    this.createCone(end, dir, size * 0.1, material);
};

///////////////////////////////////////////////////////////////////////////
// Draw a rotate gizmo
//
///////////////////////////////////////////////////////////////////////////
RotateControl.prototype.createGizmo = function (center, euler, size, radius, color, range, axis) {
    var material = new GizmoMaterial({
        color: color,
    });

    var subMaterial = new GizmoMaterial({
        color: color,
    });

    var torusGizmo = new THREE.Mesh(new THREE.TorusGeometry(radius, size, 64, 64, range), material);

    var subTorus = new THREE.Mesh(new THREE.TorusGeometry(radius, size, 64, 64, 2 * Math.PI), subMaterial);

    subTorus.material.highlight(true);

    var transform = new THREE.Matrix4();

    var q = new THREE.Quaternion();

    q.setFromEuler(euler);

    var s = new THREE.Vector3(1, 1, 1);

    transform.compose(center, q, s);

    torusGizmo.applyMatrix(transform);

    subTorus.applyMatrix(transform);

    var plane = this.createBox(this.size * 100, this.size * 100, 0.01);

    plane.applyMatrix(transform);

    subTorus.visible = false;

    this.viewer.impl.addOverlay(this.overlayScene, torusGizmo);

    this.viewer.impl.addOverlay(this.overlayScene, subTorus);

    torusGizmo.subGizmo = subTorus;
    torusGizmo.plane = plane;
    torusGizmo.axis = axis;

    return torusGizmo;
};

///////////////////////////////////////////////////////////////////////////
// Draw a box
//
///////////////////////////////////////////////////////////////////////////
RotateControl.prototype.createBox = function (w, h, d) {
    var material = new GizmoMaterial({
        color: 0x000000,
    });

    var geometry = new THREE.BoxGeometry(w, h, d);

    var box = new THREE.Mesh(geometry, material);

    box.visible = false;

    this.viewer.impl.addOverlay(this.overlayScene, box);

    return box;
};

///////////////////////////////////////////////////////////////////////////
// Draw a sphere
//
///////////////////////////////////////////////////////////////////////////
RotateControl.prototype.createSphere = function (radius) {
    var material = new GizmoMaterial({
        color: 0xffff00,
    });

    var geometry = new THREE.SphereGeometry(radius, 32, 32);

    var sphere = new THREE.Mesh(geometry, material);

    sphere.visible = false;

    this.viewer.impl.addOverlay(this.overlayScene, sphere);

    return sphere;
};

///////////////////////////////////////////////////////////////////////////
// Creates Raycatser object from the pointer
//
///////////////////////////////////////////////////////////////////////////
RotateControl.prototype.pointerToRaycaster = function (pointer) {
    var pointerVector = new THREE.Vector3();
    var pointerDir = new THREE.Vector3();
    var ray = new THREE.Raycaster();

    var rect = this.domElement.getBoundingClientRect();

    var x = ((pointer.clientX - rect.left) / rect.width) * 2 - 1;
    var y = -((pointer.clientY - rect.top) / rect.height) * 2 + 1;

    if (this.camera.isPerspective) {
        pointerVector.set(x, y, 0.5);

        pointerVector.unproject(this.camera);

        ray.set(this.camera.position, pointerVector.sub(this.camera.position).normalize());
    } else {
        pointerVector.set(x, y, -1);

        pointerVector.unproject(this.camera);

        pointerDir.set(0, 0, -1);

        ray.set(pointerVector, pointerDir.transformDirection(this.camera.matrixWorld));
    }

    return ray;
};

///////////////////////////////////////////////////////////////////////////
//
//
///////////////////////////////////////////////////////////////////////////
RotateControl.prototype.onPointerDown = function (event) {
    var pointer = event.pointers ? event.pointers[0] : event;

    if (pointer.button === 0) {
        var ray = this.pointerToRaycaster(pointer);

        var intersectResults = ray.intersectObjects(this.gizmos, true);

        if (intersectResults.length) {
            this.gizmos.forEach(function (gizmo) {
                gizmo.visible = false;
            });

            this.selectedGizmo = intersectResults[0].object;

            this.selectedGizmo.subGizmo.visible = true;

            this.picker.position.copy(intersectResults[0].point);

            this.angleLine.geometry.vertices[1].copy(intersectResults[0].point);

            this.lastDir = intersectResults[0].point.sub(this.center).normalize();

            this.angleLine.geometry.verticesNeedUpdate = true;

            this.angleLine.visible = true;

            this.picker.visible = true;
        } else {
            this.picker.visible = false;
        }

        this.engaged = this.picker.visible;

        this.viewer.impl.sceneUpdated(true);
    }

    return this.picker.visible;
};

///////////////////////////////////////////////////////////////////////////
//
//
///////////////////////////////////////////////////////////////////////////
RotateControl.prototype.onPointerHover = function (event) {
    var pointer = event.pointers ? event.pointers[0] : event;

    if (this.engaged) {
        var ray = this.pointerToRaycaster(pointer);

        var intersectResults = ray.intersectObjects([this.selectedGizmo.plane], true);

        if (intersectResults.length) {
            var intersectPoint = intersectResults[0].point;

            var dir = intersectPoint.sub(this.center).normalize();

            var cross = new THREE.Vector3();

            cross.crossVectors(this.lastDir, dir);

            var sign = Math.sign(cross.dot(this.selectedGizmo.axis));

            this.emit("transform.rotate", {
                angle: sign * dir.angleTo(this.lastDir),
                axis: this.selectedGizmo.axis,
            });

            this.lastDir = dir;

            var pickerPoint = new THREE.Vector3(
                this.center.x + dir.x * this.size * 0.8,
                this.center.y + dir.y * this.size * 0.8,
                this.center.z + dir.z * this.size * 0.8
            );

            this.picker.position.copy(pickerPoint);

            this.angleLine.geometry.vertices[1].copy(pickerPoint);
        }

        this.angleLine.visible = true;

        this.angleLine.geometry.verticesNeedUpdate = true;
    } else {
        this.angleLine.visible = false;

        var ray = this.pointerToRaycaster(pointer);

        var intersectResults = ray.intersectObjects(this.gizmos, true);

        if (intersectResults.length) {
            this.picker.position.set(intersectResults[0].point.x, intersectResults[0].point.y, intersectResults[0].point.z);

            this.picker.visible = true;
        } else {
            this.picker.visible = false;
        }
    }

    this.viewer.impl.sceneUpdated(true);
};

///////////////////////////////////////////////////////////////////////////
//
//
///////////////////////////////////////////////////////////////////////////
RotateControl.prototype.onPointerUp = function (event) {
    var _this7 = this;

    this.angleLine.visible = false;

    this.picker.visible = false;

    this.gizmos.forEach(function (gizmo) {
        gizmo.visible = true;
        gizmo.subGizmo.visible = false;
    });

    this.viewer.impl.sceneUpdated(true);

    setTimeout(function () {
        _this7.engaged = false;
    }, 100);
};

///////////////////////////////////////////////////////////////////////////
//
//
///////////////////////////////////////////////////////////////////////////
RotateControl.prototype.normalize = function (screenPoint) {
    var viewport = this.viewer.navigation.getScreenViewport();

    var n = {
        x: (screenPoint.x - viewport.left) / viewport.width,
        y: (screenPoint.y - viewport.top) / viewport.height,
    };

    return n;
};

///////////////////////////////////////////////////////////////////////////
//
//
///////////////////////////////////////////////////////////////////////////
RotateControl.prototype.projectOntoPlane = function (worldPoint, normal) {
    var dist = normal.dot(worldPoint);

    return new THREE.Vector3(worldPoint.x - dist * normal.x, worldPoint.y - dist * normal.y, worldPoint.z - dist * normal.z);
};

///////////////////////////////////////////////////////////////////////////
//
//
///////////////////////////////////////////////////////////////////////////
RotateControl.prototype.remove = function () {
    this.viewer.impl.removeOverlayScene(this.overlayScene);
};

///////////////////////////////////////////////////////////////////////////////
// Highlightable Gizmo Material
//
///////////////////////////////////////////////////////////////////////////////
function GizmoMaterial(parameters) {
    THREE.MeshBasicMaterial.call(this);

    this.setValues(parameters);

    this.colorInit = this.color.clone();
    this.opacityInit = this.opacity;
    this.side = THREE.FrontSide;
    this.depthWrite = false;
    this.transparent = true;
    this.depthTest = false;
}

GizmoMaterial.prototype = Object.create(THREE.MeshBasicMaterial.prototype);

GizmoMaterial.prototype.constructor = GizmoMaterial;

///////////////////////////////////////////////////////////////////////////
//
//
///////////////////////////////////////////////////////////////////////////
GizmoMaterial.prototype.highlight = function (highlighted) {
    if (highlighted) {
        this.color.setRGB(1, 230 / 255, 3 / 255);
        this.opacity = 1;
    } else {
        this.color.copy(this.colorInit);
        this.opacity = this.opacityInit;
    }
};

Autodesk.Viewing.theExtensionManager.registerExtension("CADShareExtension", CADShareExtension);
