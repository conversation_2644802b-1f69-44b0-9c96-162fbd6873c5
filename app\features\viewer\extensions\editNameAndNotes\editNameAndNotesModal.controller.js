(function () {
    'use strict';

    angular
        .module('app.viewer')
        .controller('EditNameAndNotesModalController', EditNameAndNotesModalController);

    EditNameAndNotesModalController.$inject = ['$uibModalInstance', 'snapshotDetails'];

    function EditNameAndNotesModalController($uibModalInstance, snapshotDetails) {
        var vm = this;

        vm.ok = ok;
        vm.cancel = cancel;

        initialize();

        function initialize(){
            vm.name = snapshotDetails.name;
            vm.notes = snapshotDetails.notes;
        }

        function ok() {
            var updatedFields = {};
            updatedFields.name = vm.name;
            updatedFields.notes = vm.notes;

            $uibModalInstance.close(updatedFields);
        }

        function cancel() {
            $uibModalInstance.dismiss();
        }
    }

})();
