.admin_container{
    border-radius: 10px;
}

.admin {
    border: 1px solid #ccc;
    border-collapse: collapse;
    margin: 0;
    padding: 0;
    width: 100%;
    table-layout: fixed;
}

.admin caption {
    font-size: 1.5em;
    margin: 0.5em 0 0.75em;
}

.admin_heading{

    background: #F2F6F9;

}

.admin_buttons{

    background: white;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px

}

.admin_body tr {
    border: 1px solid #ddd;
    padding: 0.35em;
    background: white;
}

.admin th,
.admin td {
    padding: 1em;
    word-break: break-word;
}

.admin th {
    font-size: 0.85em;
    letter-spacing: 0.1em;
    text-transform: uppercase;
}

.admin_pageNumber{

    background: white;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px

}

@media screen and (max-width: 800px) {
    .admin {
        border: 0;
    }

    .admin caption {
        font-size: 1.3em;
    }

    .admin thead {
        border: none;
        clip: rect(0 0 0 0);
        height: 1px;
        margin: -1px;
        overflow: hidden;
        padding: 0;
        position: absolute;
        width: 1px;
    }

    .admin tr {
        border-bottom: 3px solid #ddd;
        display: block;
        margin-bottom: 0.625em;
    }

    .admin td {
        border-bottom: 1px solid #ddd;
        display: block;
        font-size: 1em;
    }

    .admin td::before {
        /*
        * aria-label has no advantage, it won't be read inside a table
        content: attr(aria-label);
        */
        content: attr(data-label);
        float: left;
        font-weight: bold;
        text-transform: uppercase;
        padding-right:10px;

    }

    .admin td:last-child {
        border-bottom: 0;
    }

    .products-filter {

        margin-left: auto;

    }

    .admin_buttons .searchgroup{
        width:100%;
    }

    .admin_buttons .search-panel {
        display: inline-block;
        width: 100%;
    }

    .admin_buttons .btn{

        width:100%;

    }

}
