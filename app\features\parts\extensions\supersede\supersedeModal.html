<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" ng-click="supersedeCtrl.cancel()" aria-label="Close"><i class="fa fa-close" aria-hidden="true"></i></button>
    <h2 class="modal-title" translate>SUPERSEDE.TITLE</h2>
</div>

</div>

<div class="modal-body">
    <p translate>SUPERSEDE.PLEASE_SELECT</p>
    <div class="input-group">
        <label translate>SUPERSEDE.SELECT_RANGE</label>
        <div style="width: 100%;">
            <select ts-select-fix ng-model="supersedeCtrl.rangeId" ng-change="supersedeCtrl.rangeChanged(supersedeCtrl.rangeId)"
                    ng-options="ranges.rangeId as ranges.name for ranges in supersedeCtrl.ranges" placeholder="{{'SUPERSEDE.RANGE' | translate}}" ng-required="true">
            </select>

            <div class="select-arrow"></div>
        </div>
    </div>

    <div class="input-group">
        <label translate>SUPERSEDE.SELECT_PROD</label>
        <div style="width: 100%;">
            <select ng-model="supersedeCtrl.machineId" ng-change="supersedeCtrl.machineChanged(supersedeCtrl.machineId)" ng-class="supersedeCtrl.isMachineDropdownDisabled ? 'disabled-dropdown' : ''"
                    ng-options="machine.machineId as machine.name for machine in supersedeCtrl.machines" placeholder="{{'SUPERSEDE.MACHINE' | translate}}" ng-required="true" ng-disabled="supersedeCtrl.isMachineDropdownDisabled">
            </select>
            <div class="select-arrow"></div>
        </div>
    </div>

    <div class="input-group">
        <label translate>SUPERSEDE.SELECT_VIEWABLE</label>
        <div style="width: 100%;">
            <select ng-options="viewable.modelId as viewable.modelName for viewable in supersedeCtrl.viewables" ng-class="supersedeCtrl.isViewableDropdownDisabled ? 'disabled-dropdown' : ''"
                    ng-model="supersedeCtrl.viewableId" ng-disabled="supersedeCtrl.isViewableDropdownDisabled">
            </select>
            <div class="select-arrow"></div>
        </div>
    </div>

    <p class="modal-message" style="color: red" ng-if="supersedeCtrl.errors.noViewableSelected" translate>
        SUPERSEDE.PLEASE_SELECT_VIEWABLE
    </p>
    <p class="modal-message" style="color: red" ng-if="supersedeCtrl.errors.serviceError" translate>
        GENERAL.WENT_WRONG
    </p>

    <div class="modal-actions">
        <a class="btn small secondary" href="" ng-click="supersedeCtrl.cancel()" translate>GENERAL.CANCEL</a>
        <a class="btn small primary" href="" ng-click="supersedeCtrl.save()" translate>SUPERSEDE.SAVE</a>
    </div>

</div>


