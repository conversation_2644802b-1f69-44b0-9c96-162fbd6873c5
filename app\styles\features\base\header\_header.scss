.site-header {
  @extend %clearfix;
  width     : 100%;
  height    : 100%;
  min-height: 56px;
  background: $white;
  position  : relative;
  z-index   : 10;
  -webkit-box-shadow: 0px 4px 6px 0px rgba($black, 0.06);
  -moz-box-shadow:    0px 4px 6px 0px rgba($black, 0.06);
  box-shadow:         0px 4px 6px 0px rgba($black, 0.06);
  padding: 0% 2.5%;

  .logo {
    float      : left;
    display    : block;
    line-height: $header-height;
    padding-right: 30px;

    img {
      height    : 24px;
      margin-top: -8px;
    }
  }

  .basketCount {
    //margin-top: 20px;
  }

  .dropdown-toggle::after{

    display: inline-flex;
    align-items: center;
    content: "\f107";
    font-family: FontAwesome;
    margin-left: initial;
    vertical-align: initial;
    border:none;
    margin-left:5px;

  }

  .dropdown_additional{

    cursor: pointer;

  }

  .site-nav {
    float: left;

    ul {
      @extend %clearlist;
      @extend %clearfix;
      margin-left: $spacing;

      li {
        float: left;
        position: relative;
        display: block;
        line-height: $header-height;
        margin-left: 8px;
        font-size: 0.875em;
        font-weight: 600!important;

        a {
          font-weight: 800!important;
          padding: $spacing $spacing*2;
        }
        &.active {
          a {
            background: lighten($blue, 32%);
          }
        }
      }
    }
    &.nav-right {
      float: right;
      ul li {
        &:hover {
          a {
            background: transparent;
          }
        }
      }
    }
  }
}

.dropdown-item.active, .dropdown-item:active{

  background: lighten($blue, 32%);
  color:black;

}

.site-header .site-nav ul li a:hover {

  background: darken($lightback, 2%);

}

.site-header .site-nav ul li a:active {

  background: lighten($blue, 32%);

}

.site-header .site-nav ul li a:active {

  background: lighten($blue, 32%);

}

.sub-site-header {
  @extend %clearfix;
  width     : 100%;
  background: $white;
  padding   : 0 $spacing*2;
  position  : relative;
  z-index   : 15;

}

.mobile-sub-site-header {
  display: flex;
  align-content: center;
  justify-content: flex-end;
  align-items: center;
  line-height: 56px;
}

.on-behalf-of-banner {

  .banner-item {
    float: left;
    margin: 10px;
  }

  button{

    &:hover{
      border: 1px solid darken(#C5771C, 10%);
      color:darken(#C5771C, 10%);
    }
  }
}

.language-header-btn{
  border: none;
}

.nav-mobile-icon .fa{

  color:green;

}

.user-avatar{
  -moz-border-radius: 4em / 4em;
  -webkit-border-radius: 4em 4em;
  border-radius: 4em / 4em;
  margin-left: 0.3em;
  margin-top: 0.2em;
  width:40px;
  height:40px;
  background-color:#3392FC;
  display: flex;
  justify-content: center;
  align-items: center;
}

.nameInitials{

  color: white;
  font-weight: 700;

}

.User-Dropdown {
  display: none;
  position: absolute;
  border-radius: 7px;
  background: #fff;
  box-shadow: 0px 0px 8px rgba(214, 214, 214, 0.78);
  list-style: none;
  padding: 0 20px;
  width: 225px;
  margin: 0;
  top: 50px;
  right: -9%;
}
.User-Dropdown-Positon{
  right: 15px;
}
.User-Dropdown:before {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  margin-left: -0.5em;
  right: 0px;
  box-sizing: border-box;
  border: 7px solid black;
  border-color: transparent transparent #ffffff #ffffff;
  transform-origin: 0 0;
  transform: rotate(135deg);
  box-shadow: -3px 3px 3px -3px rgba(214, 214, 214, 0.78);
}
.User-Dropdown.U-open {
  display: block;
}
.User-Dropdown > li {
  padding: 0px;
  line-height: 47px;
  border-bottom: 1px solid rgba(215, 215, 215, 0.17);
}
.User-Dropdown > li:last-child {
  border-bottom: 0px;
}
.site-nav ul .User-Dropdown li a {
  font-size: 1.1em;
  padding: 1em;
  text-decoration: none;
  color: #1787e0;
  transition: all 0.2s ease-out;
}
.User-Dropdown span {
  background: #16d67a;
  padding: 3px 10px;
  color: #fff;
  border-radius: 30px;
}
.site-nav ul .User-Dropdown p {
  font-size: 1.1em;
  padding: 1em;
  text-decoration: none;
  color: black;
  transition: all 0.2s ease-out;
  margin-left: 8px;
  margin-bottom: 0;
  border-bottom: 1px solid rgba(215, 215, 215, 0.17);
  word-break: break-word;
}
#mySidenav p{
  text-decoration: none;
  color: black;
  transition: all 0.2s ease-out;
  border-bottom: 1px solid rgba(215, 215, 215, 0.17);
  word-break: break-word;
  white-space: break-spaces;
  padding: 8px 8px 16px 32px;
  min-width: 250px!important;
}
.User-Dropdown > li:before {
  content: "";
  width: 0px;
  height: 40px;
  position: absolute;
  background: #2196F3;
  margin-top: 8px;
  border-radius: 0 1px 1px 0;
  left: 0px;
  transition: all 0.2s ease;
}
.User-Dropdown > li:hover:before {
  width: 5px;
  border-radius: 30px;
}
.User-Dropdown > li a:hover {
  margin-left: 5px;
}

.navigation_custom ul li ul {
  visibility: hidden;
  opacity: 0;
  transition: all 0.5s ease;
  display: none;
}

.navigation_custom ul li:hover > ul,
.navigation_custom ul li ul:hover {
  visibility: visible;
  opacity: 1;
  display: block;
}

.navigation_custom{

  ul li {
    color:white;
    &:hover {
      .user-avatar{
        background: #2196F3!important;
      }
      .profile:hover {
      }
    }
  }

}

.sideBar-maxWidth {
  white-space: normal;
  overflow: hidden;
  width: 250px;
}

// Small devices
@media (max-width: 1251px) {

  #sidebarContainer{

    overflow: hidden;

  }

  .site-nav{

    display:none;

  }

  .nav-mobile-icon{

    color:green;

  }



  .mobile-site-header{

    display: flex;
    align-items: center;
    flex-direction: row;
    justify-content: space-between;

  }

  .sidenav {
    height: 100%;
    width: 0;
    position: fixed;
    z-index: 1;
    top: 0;
    right: 0;
    background-color: white;
    overflow-x: hidden;
    padding-top: 60px;
    transition: 0.5s;
    white-space: nowrap;
    -webkit-box-shadow: 0px 4px 6px 0px rgba($black, 0.16);
    -moz-box-shadow:    0px 4px 6px 0px rgba($black, 0.16);
    box-shadow:         0px 4px 6px 0px rgba($black, 0.16);
  }

  .sidenav a {
    padding: 8px 8px 8px 32px;
    text-decoration: none;
    font-size: 1em;
    color: black;
    display: block;
    transition: 0.3s;
  }

  .sidenav .fa {
    cursor: pointer;
  }

  .sidenav .dropdown {
    padding: 8px 8px 8px 32px;
  }

  .sidenav a:hover {
    color: #2f2f2f;
    background-color: #f1f1f1;
    cursor: pointer;
  }

  .sidenav ul li a:active, .sidenav ul li a.active {
    color: black;
    background-color: lighten($blue, 32%);
    cursor: pointer;
  }

  .sidenav .closeBtn:hover {
    background-color: white;
  }

  .sidenav .closeBtn {
    position: absolute;
    top: -10px;
    right: 0;
    font-size: 36px;
    margin-left: 50px;
  }

  .openbtn {

    cursor: pointer;

  }

  /* Style page content - use this if you want to push the page content to the right when you open the side navigation */
  #main {
    transition: margin-left .5s;
    padding: 20px;

  }

  .user-area-mobile{

    position: absolute;
    top: 11px;
    left: 25px;
    font-size: 1.3rem;

  }

  .user-avatar{
    -moz-border-radius: 4em / 4em;
    -webkit-border-radius: 4em 4em;
    border-radius: 4em / 4em;
    margin-left: 0.3em;
    margin-top: 0.2em;
    width:35px;
    height:34px;
    background-color:#3392FC;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .nameInitials{

    color: white;
    font-weight: 700;

  }

}

// X-Large devices
@media (min-width: 1250px) {

  .sidenav, .openNav{

    display:none;

  }

}

