<section class="d-flex flex-column flex-md-row">

    <div class="product-grid col-md-4">

        <div class="panel m-5 py-0">

		    <img class="w-100" src='{{customerManualCtrl.imageURL}}'>

		    <div class="d-flex justify-content-between align-items-center flex-wrap p-4">

        <h2 class="feature-header m-0">
            {{customerManualCtrl.featuredViewable && customerManualCtrl.featuredViewable.modelName ? customerManualCtrl.featuredViewable.modelName : customerManualCtrl.manualName}}
        </h2>

		<button class="btn primary" ng-click="customerManualCtrl.goToViewer(customerManualCtrl.featuredViewable)" ng-if="customerManualCtrl.manual.featuredModelId" translate>
            GENERAL.VIEW
		</button>

		    </div>

        </div>

    </div>

    <div class="col-md-8 px-0">

        <div class="panel m-5 p-5">

        <aside class="sub-tabs">
            <ul role="tablist">
                <li ng-repeat="tab in customerManualCtrl.tabs" ui-sref-active="active">
                    <a ui-sref="{{tab.route}}">
                        {{tab.title}}&nbsp;
                    </a>
                </li>
            </ul>
        </aside>

        <div ui-view></div>

        </div>

    </div>

</section>
