<div class="sidebar-content" ng-show="linkedPartSummaryCtrl.isOpen">
    <p translate>LINKED_PART.DESC</p>

    <button class="btn primary" ng-click="linkedPartSummaryCtrl.createLinkedPart()" translate>LINKED_PART.CREATE_NEW</button>

    <h4 translate>LINKED_PART.MANAGE</h4>

    <table ng-show="linkedPartSummaryCtrl.existingLinkedParts.length > 0" class="tableViewer table-bordered w-100 bg-white ml-0">
        <tbody>
        <tr ng-repeat="linkedPart in linkedPartSummaryCtrl.existingLinkedParts">
            <td class="side-menu-table-name">
                {{linkedPart.partNumber}} <span ng-if="linkedPart.partDescription !== null">- {{linkedPart.partDescription}}</span>
                <small>{{"LINKED_PART.LINKED_TO" | translate}}: <strong>{{linkedPart.modelName}}</strong></small>
            </td>
            <td class="has-dropdown">
                <div class="btn-group">
                    <a href="" class="btn xsmall secondary main-action"
                       ng-click="linkedPartSummaryCtrl.editLinkedPart(linkedPart)" translate>LINKED_PART.EDIT</a>
                    <div href="" class="btn xsmall secondary dropdown-toggle" data-toggle="dropdown"
                         aria-haspopup="true" aria-expanded="false">
                        <div class="sub-popup">
                            <ul class="more-options">
                                <li title="Edit">
                                    <a href="" class="dark-secondary"
                                       ng-click="linkedPartSummaryCtrl.editLinkedPart(linkedPart)"><i
                                            class="fa fa-fw fa-pencil"></i> {{"LINKED_PART.EDIT" | translate}}</a>
                                </li>
                                <li title="Delete">
                                    <a href="" class="delete"
                                       ng-click="linkedPartSummaryCtrl.deleteLinkedPart(linkedPart)"><i
                                            class="fa fa-fw fa-trash"></i> {{"LINKED_PART.DELETE" | translate}}</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </td>
        </tr>
        </tbody>
    </table>

    <p ng-hide="linkedPartSummaryCtrl.existingLinkedParts.length > 0" translate>
        LINKED_PART.NO_LINKS
    </p>

</div>