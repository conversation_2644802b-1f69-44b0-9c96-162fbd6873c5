<div class="modal-header info">
    <button type="button" class="close" data-dismiss="modal" ng-click="uploadVideoCtrl.cancel()"
            aria-label="Close">
        <i class="fa fa-close" aria-hidden="true"></i>
    </button>

    <h2 ng-if="!uploadVideoCtrl.isEdit" class="modal-title" translate>UPLOAD_VIDEO.TITLE</h2>
	  <h2 ng-if="uploadVideoCtrl.isEdit" class="modal-title" translate>UPLOAD_VIDEO.EDIT</h2>
</div>

<div class="modal-body">
    <form class="form" ng-submit="uploadVideoCtrl.save()">
        <div class="input-group">
            <label translate>UPLOAD_VIDEO.NAME</label>
            <input ng-model="uploadVideoCtrl.name" type="text" required
                   placeholder="{{'UPLOAD_VIDEO.NAME' | translate}}">
        </div>

        <div class="input-group">
            <label translate>UPLOAD_VIDEO.SUMMARY</label>
            <input ng-model="uploadVideoCtrl.description" type="text" required
                   placeholder="{{'UPLOAD_VIDEO.SUMMARY' | translate}}">
        </div>
        <div class="input-group">
            <label translate>UPLOAD_VIDEO.VIDEO_URL</label>
            <p translate>UPLOAD_VIDEO.VIDEO_URL_DESC</p>
            <input ng-model="uploadVideoCtrl.url" type="text" required
                   placeholder="{{'UPLOAD_VIDEO.VIDEO_URL' | translate}}">
        </div>

        <p class="modal-message" style="color: red" ng-if="uploadVideoCtrl.error" translate>
            GENERAL.WENT_WRONG
        </p>
        <p class="modal-message" style="color: red" ng-if="uploadVideoCtrl.wrongVideoSource" translate>
            UPLOAD_VIDEO.WRONG_VIDEO_SOURCE
        </p>

        <div class="modal-actions">
            <button class="btn small secondary" href="" ng-click="uploadVideoCtrl.cancel()" type="button" translate>GENERAL.CANCEL</button>
            <button class="btn small primary" href="" ng-hide="uploadVideoCtrl.isSaving" type="submit" translate>UPLOAD_VIDEO.SAVE</button>
            <button class="btn small primary" href="" ng-show="uploadVideoCtrl.isSaving">
                <span class="spinner-border text-light" role="status" aria-hidden="true" type="button"></span>
                {{"UPLOAD_VIDEO.SAVING" | translate}}
            </button>
        </div>
    </form>

</div>