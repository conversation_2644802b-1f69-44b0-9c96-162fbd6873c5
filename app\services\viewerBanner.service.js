(function () {
    'use strict';

    angular
        .module('app.services')
        .factory('viewerBannerService', viewerBannerService);

    viewerBannerService.$inject = ['$rootScope', '$timeout', '$uibModal', 'viewerHelperService', '$translate'];

    function viewerBannerService($rootScope, $timeout, $uibModal, viewerHelperService, $translate) {

        var notification = {};
        var aKit = "";
        var anAssembly = "";
        var assemblyAndKit = "";
        var thisPart = "";
        var isAvailableAs = "";
        var translationsComplete = false;

        $translate(['VIEWER_BANNER.A_KIT', 'VIEWER_BANNER.AN_ASSEMBLY', 'VIEWER_BANNER.ASSEMBLY_AND_KIT', 'VIEWER_BANNER.THIS_PART', 'VIEWER_BANNER.IS_AVAILABLE_AS'])
            .then(function (resp) {
                aKit = resp["VIEWER_BANNER.A_KIT"];
                anAssembly = resp["VIEWER_BANNER.AN_ASSEMBLY"];
                assemblyAndKit = resp["VIEWER_BANNER.ASSEMBLY_AND_KIT"];
                thisPart = resp["VIEWER_BANNER.THIS_PART"];
                isAvailableAs = resp["VIEWER_BANNER.IS_AVAILABLE_AS"];
                translationsComplete = true
            });


        return {
            setNotification: setNotification,
            setKitNotification: setKitNotification,
            getNotification: getNotification,
            removeNotification: removeNotification,
            selectAssembly: selectAssembly,
            viewKit: viewKit
        };

        /**
         * @param {string} level - values are ERROR, WARN, SUCCESS and INFO
         **/
        function setNotification(level, text, timeout) {
            notification = {"level": level, "text": text, "timeout": timeout};
            $rootScope.$broadcast("viewerNotification");
        }

        function getNotification() {
            return notification;
        }

        function removeNotification() {
            $timeout(function () {
                notification = {};
                $rootScope.$broadcast("viewerNotification");
            });
        }

        function setKitNotification(partData, inKit, kits, isInAssembly) {
            if(translationsComplete){
                var bannerMessage = "";
                var level = "";
                if (!isInAssembly) {
                    bannerMessage = aKit;
                    level = "KIT";
                } else if (!inKit) {
                    bannerMessage = anAssembly;
                    level = "ASSEMBLY";
                } else {
                    bannerMessage = assemblyAndKit;
                    level = "KITASSEMBLY";
                }

                notification = {
                    "level": level,
                    "text": thisPart + partData.partNumber + isAvailableAs + bannerMessage,
                    "timeout": 0,
                    "partData": partData,
                    "kits": kits
                };
                $rootScope.$broadcast("viewerNotification");
            }else{
                $timeout(setKitNotification(partData, inKit, kits, isInAssembly), 500)
            }

        }

        function viewKit(kits) {

            $uibModal.open({
                keyboard: false,
                templateUrl: 'features/viewer/extensions/buyKit/kitModal.html',
                controller: 'KitModalController as kitModalCtrl',
                resolve: {
                    kits: function () {
                        return kits;
                    }
                }
            })
                .result.then(function (response) {
            });
        }

        function selectAssembly(partData) {
            viewerHelperService.selectParentPart(partData.objectId);
        }
    }
})();
