(function () {
    'use strict';

    angular
        .module('app.viewer')
        .controller('KitBuilderController', KitBuilderController);

    KitBuilderController.$inject = ['kitService', 'viewerBannerService', '$stateParams', '$uibModal', '$rootScope', '$scope'];

    function KitBuilderController(kitService, viewerBannerService, $stateParams, $uibModal, $rootScope, $scope) {
        var vm = this;

        vm.isOpen = true;
        vm.existingKits = [];

        vm.createKit = createKit;
        vm.editKit = editKit;
        vm.deleteKit = deleteKit;

        initialize();

        function initialize() {
            vm.modelId = $stateParams.modelId;
            fetchKits();
        }

        function fetchKits(){
             kitService.fetchKitsForModel(vm.modelId)
                 .then(fetchKitsForModelSuccess, fetchKitsForModelFailed);
        }

        function fetchKitsForModelSuccess(response) {
            vm.existingKits = response.data;
        }

        function fetchKitsForModelFailed(error) {
            viewerBannerService.setNotification("ERROR", error);
        }

        function createKit() {
            vm.isOpen = false;
            $rootScope.$broadcast("create-kit-opened");
        }

        function editKit(kit) {
            vm.isOpen = false;
            $rootScope.$broadcast("create-kit-opened", kit.id);
        }

        function deleteKit(kit) {
            vm.successMessage = "";
            var deleteObject = {
                name: "Kit " + kit.title,
                id: kit.id,
                url: '/kit/' + kit.id
            };

            $uibModal.open({
                templateUrl: 'features/shared/commonDelete/deleteDialogBox.html',
                controller: 'DeleteController',
                controllerAs: 'deleteCtrl',
                size: 'sm',
                resolve: {
                    deleteObject: function () {
                        return deleteObject;
                    }
                }
            }).result.then(function () {
                fetchKits();
            });
        }

        $scope.$on("create-kit-closed", function () {
            vm.isOpen = true;
            initialize();
        });
    }

})();
