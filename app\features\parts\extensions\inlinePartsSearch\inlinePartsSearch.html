<div class="responsiveContainer">
    <form ng-submit="inlinePartsSearchCtrl.search()">

        <div class="search-area">

            <div class="input-group">
                <input  class="form-control mr-0" type="search" ng-model="inlinePartsSearchCtrl.searchValue" placeholder="{{inlinePartsSearchCtrl.isSearchOnlyPartNumbers ? 'INLINE_SEARCH.SEARCH_BY_PART' : 'INLINE_SEARCH.SEARCH_BY_PART_AND_DESC'  | translate}}">
                <div class="input-group-append">
                    <button ng-class="[inlinePartsSearchCtrl.isDealerPlusPage() ? 'dpGreenModal input-group-text-btn' : 'input-group-text-btn', {'disabledElement': !inlinePartsSearchCtrl.searchValue || inlinePartsSearchCtrl.searchValue.trim() === ''}]" 
                    type="button" 
                    ng-click="inlinePartsSearchCtrl.search()" 
                    ng-hide="inlinePartsSearchCtrl.searching" 
                    class="input-group-text-btn btn-anim" 
                    ng-disabled="!inlinePartsSearchCtrl.searchValue || inlinePartsSearchCtrl.searchValue.trim() === ''">
                        <i class="pr-0 pr-md-3 fa fa-search"></i><span class="search_mobile_disable bg-transparent">{{'INLINE_SEARCH.SEARCH' | translate}}</span>
                    </button>
                    <button ng-class="inlinePartsSearchCtrl.isDealerPlusPage() ? 'dpGreenModal input-group-text-btn' : 'input-group-text-btn'" type="button" ng-show="inlinePartsSearchCtrl.searching" class="input-group-text-btn btn-anim" ng-class="inlinePartsSearchCtrl.isDealerPlusPage() ? 'dpGreenModal' : ''">
                        <i class="pr-0 pr-md-3 fa fa-search"></i><span class="search_mobile_disable bg-transparent">{{'INLINE_SEARCH.SEARCHING' | translate}}</span>
                        <span class="spinner-border text-light" role="status" aria-hidden="true"></span>
                    </button>
                </div>
            </div>

        </div>

        <div class="text-center" ng-hide="inlinePartsSearchCtrl.isSearchOnlyPartNumbers">
            <span class="" translate>INLINE_SEARCH.SEARCH_BY</span>
            <label class="radio-inline">
                <input type="radio" ng-model="inlinePartsSearchCtrl.searchBy" value="partNumber" ng-class="inlinePartsSearchCtrl.isDealerPlusPage() ? 'dpGreenModal radio' : ''"
                       style="white-space:nowrap"><span style="white-space:nowrap" translate>INLINE_SEARCH.PART_NUM</span>
            </label>
            <label class="radio-inline">
                <input type="radio" ng-model="inlinePartsSearchCtrl.searchBy" value="partDescription" ng-class="inlinePartsSearchCtrl.isDealerPlusPage() ? 'dpGreenModal radio' : ''"
                       style="white-space:nowrap"><span style="white-space:nowrap" translate>INLINE_SEARCH.PART_DESC</span>
            </label>
        </div>

    </form>
</div>

<!-- Kits Table -->

<div ng-if="!inlinePartsSearchCtrl.isLoading">
    <div ng-if="!inlinePartsSearchCtrl.isOnOptionSetPage && !inlinePartsSearchCtrl.isOnAdditionalPartPage"
        ng-include="'features/parts/extensions/inlinePartsSearch/inlinePurchaserSearchKitsTable.html'"></div>
    <div class="mb-4" ng-include="'features/parts/extensions/inlinePartsSearch/inlinePurchaserSearchPartsTable.html'">
    </div>
</div>

<div ng-show="inlinePartsSearchCtrl.hasSearchError">
    <h3 class="error-alert center-align" translate>PART_SEARCH.SEARCH_ERROR</h3>
</div>