(function () {
    "use strict";

    angular.module("app.services").factory("uploadPartService", uploadPartService);

    uploadPartService.$inject = ["$http", "apiConstants"];

    function uploadPartService($http, apiConstants) {
        return {
            getHeaderDetails: getHeaderDetails,
            uploadPartDetailsFile: uploadPartDetailsFile,
            uploadSparePartDetailsFile: uploadSparePartDetailsFile,
        };

        function getHeaderDetails(file) {
            return $http.post(apiConstants.url + "/model/uploadDetails/headers", file, { headers: { "Content-Type": "text/csv" } });
        }

        function uploadPartDetailsFile(modelId, file, data) {
            var fd = new FormData();
            fd.append("file", file);
            fd.append("nameColumn", data.nameColumn);
            fd.append("objectIdColumn", data.objectIdColumn);
            fd.append("descriptionColumn", data.descriptionColumn);
            fd.append("updateDescription", data.updateDescription);
            fd.append("numberColumn", data.numberColumn);
            fd.append("updateNumber", data.updateNumber);
            var uploadUrl = apiConstants.url + "/model/" + modelId + "/uploadDetailsFile";
            return $http.post(uploadUrl, fd, {
                transformRequest: angular.identity,
                headers: { "Content-Type": undefined },
            });
        }

        function uploadSparePartDetailsFile(modelId, file) {
            var fd = new FormData();
            fd.append("file", file);
            fd.append("partNumber", "0");
            fd.append("sparePartIdentifier", "1");

            var uploadUrl = apiConstants.url + "/api/georg/" + modelId + "/uploadSparePartIdentifiersFile";
            return $http.post(uploadUrl, fd, {
                transformRequest: angular.identity,
                headers: { "Content-Type": undefined },
            });
        }
    }
})();
