(function () {
    'use strict';

    angular
        .module('app.services')
        .factory('dashboardService', dashboardService);

    dashboardService.$inject = ['$http', 'apiConstants'];

    function dashboardService($http, apiConstants) {
        return {

            getTotalEnquiries: getTotalEnquiries,
            getTotalQuotes: getTotalQuotes,
            getTotalOrders: getTotalOrders,
            getConversionRate: getConversionRate,
            getValueOfQuotes: getValueOfQuotes,
            getValueOfOrders: getValueOfOrders,
            getMostActiveCustomers: getMostActiveCustomers,
            getHighestOrderValueCustomers: getHighestOrderValueCustomers,
            getMostOrderedParts: getMostOrderedParts,
            getOrderValues: getOrderValues
        };

        function getTotalEnquiries(manufacturerId, timePeriodEnum, manufacturerSubEntityId, customStartDate, customEndDate) {
            var params = createParamString(timePeriodEnum, customStartDate, customEndDate, manufacturerSubEntityId);
            return $http.get(apiConstants.url + '/dashboard/' + manufacturerId + '/totalEnquiries' + params);
        }

        function getTotalQuotes(manufacturerId, timePeriodEnum, manufacturerSubEntityId, customStartDate, customEndDate) {
            var params = createParamString(timePeriodEnum, customStartDate, customEndDate, manufacturerSubEntityId);
            return $http.get(apiConstants.url + '/dashboard/' + manufacturerId + '/totalQuotes' + params);
        }

        function getTotalOrders(manufacturerId, timePeriodEnum, manufacturerSubEntityId, customStartDate, customEndDate) {
            var params = createParamString(timePeriodEnum, customStartDate, customEndDate, manufacturerSubEntityId);
            return $http.get(apiConstants.url + '/dashboard/' + manufacturerId + '/totalOrders' + params);
        }

        function getConversionRate(manufacturerId, timePeriodEnum, manufacturerSubEntityId, customStartDate, customEndDate) {
            var params = createParamString(timePeriodEnum, customStartDate, customEndDate, manufacturerSubEntityId);
            return $http.get(apiConstants.url + '/dashboard/' + manufacturerId + '/conversionRate' + params);
        }

        function getValueOfQuotes(manufacturerId, timePeriodEnum, manufacturerSubEntityId, customStartDate, customEndDate) {
            var params = createParamString(timePeriodEnum, customStartDate, customEndDate, manufacturerSubEntityId);
            return $http.get(apiConstants.url + '/dashboard/' + manufacturerId + '/quoteValue' + params);
        }

        function getValueOfOrders(manufacturerId, timePeriodEnum, manufacturerSubEntityId, customStartDate, customEndDate) {
            var params = createParamString(timePeriodEnum, customStartDate, customEndDate, manufacturerSubEntityId);
            return $http.get(apiConstants.url + '/dashboard/' + manufacturerId + '/orderValue' + params);
        }

        function getMostActiveCustomers(manufacturerId, timePeriodEnum, customStartDate, customEndDate) {
            var params = createParamString(timePeriodEnum, customStartDate, customEndDate) + "&limit=5";
            return $http.get(apiConstants.url + '/dashboard/' + manufacturerId + '/mostActiveCustomers' + params);
        }

        function getHighestOrderValueCustomers(manufacturerId, timePeriodEnum, customStartDate, customEndDate) {
            var params = createParamString(timePeriodEnum, customStartDate, customEndDate) + "&limit=5";
            return $http.get(apiConstants.url + '/dashboard/' + manufacturerId + '/customersMostOrders' + params);
        }

        function getMostOrderedParts(manufacturerId, timePeriodEnum, manufacturerSubEntityId, customStartDate, customEndDate) {
            var params = createParamString(timePeriodEnum, customStartDate, customEndDate, manufacturerSubEntityId) + "&limit=10";
            return $http.get(apiConstants.url + '/dashboard/' + manufacturerId + '/mostOrderedParts' + params);
        }

        function getOrderValues(manufacturerId, timePeriodEnum, manufacturerSubEntityId, customStartDate, customEndDate) {
            var params = createParamString(timePeriodEnum, customStartDate, customEndDate, manufacturerSubEntityId);
            return $http.get(apiConstants.url + '/dashboard/' + manufacturerId + '/chart/orderValue' + params);
        }

        function createParamString(timePeriodEnum, customStartDate, customEndDate, manufacturerSubEntityId) {
            var paramString = "?period=" + timePeriodEnum;

            var notSubEntityValue = manufacturerSubEntityId === -1 || manufacturerSubEntityId === undefined || manufacturerSubEntityId === null;
            paramString = notSubEntityValue ? paramString : paramString + "&manufacturerSubEntityId=" + manufacturerSubEntityId;

            if (timePeriodEnum === 'CUSTOM') {
                var customDateString = "&customStart=" + customStartDate + "&customEnd="+customEndDate
                paramString = paramString + customDateString;
            }

            return paramString;
        }
    }
})();
