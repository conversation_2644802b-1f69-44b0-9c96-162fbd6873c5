<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" ng-click="createNewNameCtrl.cancel()"
            aria-label="Close"><i class="fa fa-close" aria-hidden="true"></i>
    </button>
    <h2 class="modal-title" id="myModalLabel" translate>CONTACT_NAME.ADD_NEW_NAME</h2>
  </div>
<div class="modal-body">
    <form name="editCustomerDetailsForm" class="form">

        <div class="error-alert" ng-if="!editCustomerDetailsForm.$valid && !editCustomerDetailsForm.$pristine">
            <p class="errortext" translate>
                CONTACT_NAME.PLEASE_COMPLETE_ALL_FIELDS
            </p></div>
      
        <div class="input-group">
          <label>{{"CONTACT_NAME.NAME" | translate}} *</label>
            <input type="text" ng-required="true" ng-model="createNewNameCtrl.data.contactName" placeholder="{{'CONTACT_NAME.ENTER_NAME' | translate}}">
        </div>


        <div class="modal-actions">
          <button type="button" class="btn secondary" ng-click="createNewNameCtrl.cancel()" translate>GENERAL.CANCEL</button> &nbsp;
          
          <button type="button" class="btn primary" ng-class="createNewNameCtrl.isDealerPlusPage() ? 'dpGreenModal' : ''" ng-disabled="!editCustomerDetailsForm.$valid"
                  ng-click="createNewNameCtrl.createName()" translate>CONTACT_NAME.CREATE_NAME
          </button>
  
        </div>

    </form>
  </div>