.order-details-holder{
  width:50%; float:left;
  h1 {
    font-size:1.4em;
    margin-bottom:$spacing;
  }
  p {
    margin-bottom: 0;
  }
}
.customer-details-holder{
  width:75%; 
  float:left; 
  margin-top: $spacing*2;
}
.split-order-details-holder{
  width:75%;
  float:left;
  margin-top: $spacing*2;
}
.address-holder{
  width:30%; float:left;
}

.notes-holder{
  width:60%; float:left;
}
.edit-customer-details-holder{
  width:100%;
}
.edit-address-holder{
  width:21%;
  float:left;
  select{
    margin: 0 $spacing $spacing*2 0;
    width: 100%;
    padding: 1px;
  }
}
.contact-details-holder{
  width:30%; float:left;
  margin-left:3%;
}
.stock-order-holder{
  vertical-align: top;
}
.edit-contact-details-holder{
  width:21%;
  margin-left:5%;
  float:left;
  input{
    margin: 0 0 $spacing*2 0;
    width: 100%;
  }
  select{
    margin: 0 $spacing $spacing*2 0;
    width: 100%;
    padding: 1px;
  }
}
.delivery-details-holder{
  width:20%;
  float: left;
  margin-left:3%;
}
.edit-delivery-details-holder{
  width:21%;
  margin-left:5%;
  float: left;
}
.comments-holder{
  width:20%;
  float:right;
  margin-left:3%;
}
.timeline-holder{
  width:22%; float:right;
  margin-top:$spacing*2;
}
.order-items-holder{
  width:72%; float:left;
  margin-top:$spacing*2;
}
.order-manual-items-holder{
  width:72%; float:left;
  margin-top:$spacing*2;
}
.basket-items-holder{
  width:100%;
  float:left;
  margin-top:$spacing*4;
}
.pricing-summary-holder{
  width:22%; float:right;
  margin-top:$spacing*2;
}

.currency-text{
  width:33%; float:left;
  margin-left: $spacing;
  margin-top: $spacing;
}
.currency-selector{
  max-width:30%;
  margin-bottom: 0;
  font-family: "Open Sans", sans-serif;
  font-size: 0.9em;
}

.clearfix {
  width:100%;
  clear: both;
  display: block;
}


.posRelative {
  position: relative;
}

.mydiv {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 100000;
  background-color: #000;
  opacity: 0.6;
}

.ajax-loader {
}

.full-width-comment {
  display    : inline-block;
  font-size  : 1em;
  font-family: $body-font;
  line-height: 34px;
  border       : 1px solid $divider-color;
  border-radius: $border-radius;
  background   : $lightback;
  color      : $textdark;
  width: 100%;
}

.full-width-comment-registration {
  display    : inline-block;
  font-size  : 1em;
  font-family: $body-font;
  line-height: 1.5em;
  border       : 1px solid $divider-color;
  border-radius: $border-radius;
  background   : $lightback;
  color      : $textdark;
  width: 100%;
}

.white-bg-text-box{
  background: white;
  margin-bottom: 8px;
  min-height: 40px;
}

.strike-through{
  text-decoration: line-through;
}

.dark-text{
  color: black;
}

.has-error{
  border: 1px solid red !important;
}

.stockorderContainer{

  input[type="checkbox"]{

    cursor: pointer;
    background-color: white;
    background-clip: padding-box;
  }

  input[type="checkbox"]:hover{

    background: white;
    color: #eeeeee;
    border: 1px solid #0477f8;
  }

  input[type="checkbox"]:checked {

    background: #3392FC;
    color: #fff;
  }

}

.edit-delivery-details-holder input:disabled {
  background: #dddddd;
}

.btn.unreadcomment {
  color: black;
  background-color: white;
  border: solid 1px #1a85fc;
  position: relative;
}

.btn.unreadcomment:hover {
  color: black;
  background-color: white;
  border: solid 1px #1a85fc;
}

.blob_unreadEnquiries{
  border-left: #1a85fc 3px solid;
  background: white;
  align-items: center;
  padding: 4%;
  box-shadow: 0px 0px 4px rgba(68,68,68, 0.3);
  display: inline;
}

.blobs-container {
  display: inline-flex;
}

.blob_orders {
  color: #3392FC;
  background: white;
  border-radius: 50%;
  box-shadow: 0 0 0 0 black;
  height: 30px;
  width: 30px;
  transform: scale(1);
  animation: pulse 1s infinite;
  animation-iteration-count: 5;
  transition:opacity 1s linear;
  display: flex;
  justify-content: center;
  align-items: center;
}

.blob_order {
  color: #3392FC;
  background: white;
  border-radius: 50%;
  box-shadow: 0 0 0 0 black;
  height: 30px;
  width: 30px;
  transform: scale(1);
  animation: pulse 1s infinite;
  animation-iteration-count: 5;
  transition:opacity 1s linear;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: -15px;
  right: -14px;
}

.grey_pulse{
  background: rgb(244, 244, 244);
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0px rgba(51, 146, 252, 0.2);
  }
  100% {
    box-shadow: 0 0 0 20px rgba(0, 0, 0, 0);
  }
}

