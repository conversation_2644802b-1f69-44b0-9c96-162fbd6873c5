<div class="modal-header info">
    <button type="button" class="close" data-dismiss="modal" ng-click="fullShipOrderModalCtrl.cancel()"
            aria-label="Close">
        <i class="fa fa-close" aria-hidden="true"></i>
    </button>

    <h2 class="modal-title" translate>ORDER_CONFIRM.TITLE</h2>
</div>


<div class="modal-body">

    <p class="modal-message">{{'ORDER_CONFIRM.IF_CONFIRM' | translate}}</p>
    <h3 translate>SHIP_ITEMS.DELIVERY_DATE</h3>
    <input type="text" id="datepicker" autocomplete="off" ng-model="fullShipOrderModalCtrl.shippingDate"
           placeholder="dd/mm/yyyy"/>
    </br>
    <h4 class="mt-4" translate>ORDER_CONFIRM.ATTACH_INVOICE</h4>
    <div class="upload-box">
        <div>
            <i class="fa fa-upload"></i>
            <h4 class="file-uploader" translate>ORDER_CONFIRM.CHOOSE_FILE</h4>
            <input type="file" class="fileupload" ng-click="$event = $event" multiple="multiple"
                   onchange="angular.element(this).scope().fullShipOrderModalCtrl.invoiceAttached(event)"
                   accept=".pdf"/>
        </div>
    </div>
    <p>
        <emphasis ng-repeat="invoice in fullShipOrderModalCtrl.invoiceFileNames"><span ng-if="!$first">, </span>{{invoice}}</emphasis>
    </p>

    <div class="error-well" ng-if="fullShipOrderModalCtrl.shippingDateError">
        <p translate>SHIP_ITEMS.PICK_DATE</p>
    </div>

    <div class="modal-actions">
        <a class="btn small secondary" href="" ng-click="fullShipOrderModalCtrl.cancel()" translate>GENERAL.CANCEL</a>
        <a class="btn small primary" href="" ng-hide="fullShipOrderModalCtrl.isConfirming" ng-click="fullShipOrderModalCtrl.confirm()" translate>ORDER_CONFIRM.CONFIRM</a>
        <a class="btn small primary" href="" ng-show="fullShipOrderModalCtrl.isConfirming">
            <span class="spinner-border text-light" role="status" aria-hidden="true"></span>
            {{'ORDER_CONFIRM.CONFIRMING' | translate}}
        </a>
    </div>
</div>