

<section class="body-content">

  <div class="products-details-holder">
    <p><a href="" class="dark-secondary" ng-click="workInstructionsCtrl.backToModels()"><i class="fa fa-caret-left"></i>
      Back to all models for
      {{workInstructionsCtrl.machineName}}</a></p>
    <h2 class="viewables-header">WorkInstructions for {{workInstructionsCtrl.modelName}}</h2>
  </div>
</section>
<section class="body-content product-catalogue">
  <div class="left-panel">
    <h2 class="titletail"><span>Model Details</span></h2>
    <div class="panel">
      <div class="manufacturer-details">
        <p class="title">Filename</p>
        <p>{{workInstructionsCtrl.model.originalFilename}}</p>
        <p class="title">Status</p>
        <span ng-class="workInstructionsCtrl.model.autodeskStatus.includes('PROPERTIES_PROCESSED') ? 'badge-pill primary' : 'badge-pill'"> {{workInstructionsCtrl.model.autodeskStatusDisplay | capitalize}} </span>
        <p class="title">Created Date</p>
        <p>{{workInstructionsCtrl.model.createdDate | date : "d MMMM y"}}</p>
        <p class="title">Created By</p>
        <p>{{workInstructionsCtrl.model.createdByUserFullName}}</p>
      </div>
    </div>
  </div>

    <div class="panel right-panel">

    <div class="panel-heading">
        <div class="pull-left">
    <div class="search-panel">
      <div class="input-icon-wrap searchgroup pull-left">
        <input id="searchInput" type="search" ng-model="workInstructionsCtrl.searchValue"
               ng-change="workInstructionsCtrl.searchFilterChange()"
               placeholder="Search by name">
        <i class="fa fa-search" aria-hidden="true"></i>
      </div>
    </div>
        </div>
        <div class="pull-right">
      <button class="btn primary create-machine" href="" ng-click="workInstructionsCtrl.createWorkInstructions()">
        Create New Work Instructions
      </button>
    </div>
    </div>

    <table class="product-table">
      <thead>
      <tr>
        <th>Work Instructions</th>
        <th class="align-right">Actions</th>
      </tr>
      </thead>
      <tbody>
      <tr ng-repeat="workInstructions in workInstructionsCtrl.workInstructionsList | filter :workInstructionsCtrl.searchValue | filter : workInstructionsCtrl.filterValue : true | myLimitTo : workInstructionsCtrl.count : workInstructionsCtrl.start">
        <td><strong>{{workInstructions.name}}</strong></td>

        <td class="has-dropdown">
          <div class="btn-group">
            <button href="" class="btn secondary main-action"
                    ng-click="workInstructionsCtrl.openWorkInstructionsViewerPage(workInstructions)">View
            </button>
            <div href="" class="btn secondary dropdown-toggle" data-toggle="dropdown" aria-haspopup="true"
                 aria-expanded="false">
              <div class="sub-popup viewables">
                <ul class="more-options">
                  <li title="View Work Instructions">
                    <a href="" class="dark-secondary"
                       ng-click="workInstructionsCtrl.openWorkInstructionsViewerPage(workInstructions)"><i
                        class="fa fa-fw fa-eye"></i>View</a>
                  </li>
                  <li title="Edit Work Instructions">
                    <a href="" class="dark-secondary"
                       ng-click="workInstructionsCtrl.editWorkInstructions(workInstructions)"><i
                        class="fa fa-fw fa-pencil"></i>Edit</a>
                  </li>
                  <li title="Delete Work Instructions">
                    <a href="" class="delete"
                       ng-click="workInstructionsCtrl.deleteWorkInstructions(workInstructions)"><i
                            class="fa fa-fw fa-trash"></i>Delete</a>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </td>
      </tr>
      <tr ng-show="!workInstructionsCtrl.workInstructionsList.length > 0 && workInstructionsCtrl.isWorkInstructionsLoaded">
        <td colspan="7">No work Instructions available</td>
      </tr>

      <tr ng-hide="workInstructionsCtrl.isWorkInstructionsLoaded" align="center">
        <td class="preloader" colspan="7"><img ng-src="images/cadpreloader.gif" class="ajax-loader" height="60"
                                               width="60"/>
        </td>
      </tr>
      </tbody>
    </table>
  </div>
</div>
</section>

