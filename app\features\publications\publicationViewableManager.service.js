(function () {
    "use strict";

    angular
        .module("app.publications")
        .factory("publicationViewableManagerService", publicationViewableManagerService);

    publicationViewableManagerService.$inject = [
        "manufacturerPublicationService",
        "$timeout",
        "$translate",
        "headerBannerService",
        "$q"
    ];

    function publicationViewableManagerService(
        manufacturerPublicationService,
        $timeout,
        $translate,
        headerBannerService,
        $q
    ) {
        var service = {
            tempSelectedViewableIds: [],
            tempSelectedViewables: [],
            data: {
                rangeId: null,
                productId: null,
                featuredModelId: null,
                useViewableImage: false
            },
            selectedViewables: [],
            searchResults: [],
            searchPerformed: false,
            searching: false,
            viewables: [],
            activeViewableTab: 'find',
            searchTerm: '',
            isRangeDropdownDisabled: true,
            isProductDropdownDisabled: true,
            isFeaturedViewableDropdownDisabled: true,
            featuredModelUrl: "images/placeholder.jpg",
            rangeValues: [],
            products: [],

            initializeManager: initializeManager,
            populateFromPublication: populateFromPublication,
            getRange: getRange,
            rangeChanged: rangeChanged,
            getProducts: getProducts,
            productChanged: productChanged,
            addViewable: addViewable,
            removeViewable: removeViewable,
            shouldShowAvailableViewablesList: shouldShowAvailableViewablesList,
            setUpFeaturedViewable: setUpFeaturedViewable,
            featuredViewableChanged: featuredViewableChanged,
            performSearch: performSearch,
            clearSearch: clearSearch,
            getDisplayedViewables: function() { 
                // For search tab, return search results
                if (service.activeViewableTab === 'search' && service.searchPerformed) {
                    return service.searchResults || [];
                }
                else if (service.activeViewableTab === 'find' && service.data.rangeId && service.data.productId) {
                    return service.viewables || [];
                }
                // Default: return empty array
                return [];
            },
            setAsFeatured: setAsFeatured,
            removeFeatured: removeFeatured,
            addSelectedViewables: addSelectedViewables,
            toggleTempSelectedViewable: toggleTempSelectedViewable,
            setUseViewableImageFlag: function(flag) { service.data.useViewableImage = flag; service.featuredModelUrl = flag ? service.featuredModelUrl : 'images/placeholder.jpg'; },
            setActiveTab: setActiveTab,
            isViewableSelected: isViewableSelected,

            getServiceData: function() { return service; },
            clearTemporarySelections: clearTemporarySelections,

        };

        var modelsSetupComplete = false;
        var featuredViewableReturned = false;
        var oldFeaturedViewable = null;
        var SELECT_RANGE_PLACEHOLDER = "Select Range...";
        var WENT_WRONG = "Something went wrong. Please try again.";

        $translate(["CREATE_PUBLICATION.SELECT_RANGE", "GENERAL.WENT_WRONG"]).then(function (resp) {
            SELECT_RANGE_PLACEHOLDER = resp["CREATE_PUBLICATION.SELECT_RANGE"] || SELECT_RANGE_PLACEHOLDER;
            WENT_WRONG = resp["GENERAL.WENT_WRONG"] || WENT_WRONG;
        });

        /**
         * Add all viewables whose IDs are in tempSelectedViewableIds to selectedViewables, then clear tempSelectedViewableIds.
         */
        function addSelectedViewables() {
            var displayed = service.getDisplayedViewables();
            service.tempSelectedViewableIds.forEach(function(id) {
                var viewable = displayed.find(function(v) { return v.id === id; });
                if (viewable) {
                    service.addViewable(viewable);
                }
            });
            service.tempSelectedViewableIds = [];
        }
        
        /**
         * Toggle the selection of a viewable by id for the temp selection array.
         */
        function toggleTempSelectedViewable(id) {
            var idx = service.tempSelectedViewableIds.indexOf(id);
            if (idx === -1) {
                service.tempSelectedViewableIds.push(id);
            } else {
                service.tempSelectedViewableIds.splice(idx, 1);
            }
        }

        return service;

    function addSelectedViewables() {
        if (service.tempSelectedViewables && service.tempSelectedViewables.length > 0) {
            service.tempSelectedViewables.forEach(function(viewable) {
                var alreadySelected = service.selectedViewables.some(function(v) { return v.id === viewable.id; });
                if (!alreadySelected) {
                    service.addViewable(viewable);
                }
            });
        }
        
        // Clear the temporary selections after adding
        service.tempSelectedViewableIds = [];
        service.tempSelectedViewables = [];
    }
    
    function toggleTempSelectedViewable(id) {
        var idx = service.tempSelectedViewableIds.indexOf(id);
        if (idx === -1) {
            // Add to selected IDs
            service.tempSelectedViewableIds.push(id);
            
            // Also store the viewable object
            var displayed = service.getDisplayedViewables();
            var viewable = displayed.find(function(v) { return v.id === id; });
            if (viewable) {
                service.tempSelectedViewables.push(angular.copy(viewable));
            }
        } else {
            // Remove from selected IDs
            service.tempSelectedViewableIds.splice(idx, 1);
            
            // Also remove from stored viewables
            var viewableIdx = service.tempSelectedViewables.findIndex(function(v) { return v.id === id; });
            if (viewableIdx !== -1) {
                service.tempSelectedViewables.splice(viewableIdx, 1);
            }
        }
    }
    
    function clearTemporarySelections() {
       service.tempSelectedViewableIds = [];
       service.tempSelectedViewables = [];
   }

        function setActiveTab(tabName) {
            if (service.activeViewableTab !== tabName) {
                service.activeViewableTab = tabName;
            }
        }

        function initializeManager(isEditing, publicationDataForEdit, useViewableImageFlag) {
            console.log("publicationViewableManagerService: initializeManager called");
            service.data.rangeId = null;
            service.data.productId = null;
            service.data.featuredModelId = null;
            service.selectedViewables = [];
            service._publicationViewablesForEdit = [];
            service.searchResults = [];
            service.searchPerformed = false;
            service.searching = false;
            service.viewables = [];
            service.activeViewableTab = 'find';
            service.searchTerm = '';
            service.isRangeDropdownDisabled = true;
            service.isProductDropdownDisabled = true;
            service.isFeaturedViewableDropdownDisabled = true;
            service.featuredModelUrl = "images/placeholder.jpg";
            service.rangeValues = [];
            service.products = [];
            modelsSetupComplete = false;
            featuredViewableReturned = false;
            oldFeaturedViewable = null;
            service.data.useViewableImage = !!useViewableImageFlag;

            if (isEditing && publicationDataForEdit) {
                populateFromPublication(publicationDataForEdit);
                if (publicationDataForEdit.rangeId) {
                    service.data.rangeId = publicationDataForEdit.rangeId;
                }
                if (publicationDataForEdit.productId) {
                    service.data.productId = publicationDataForEdit.productId;
                }
            }
            getRange(isEditing);
        }

        function populateFromPublication(publicationData) {
            console.log("publicationViewableManagerService: populateFromPublication called");
            service.data.featuredModelId = publicationData.featuredViewableId;
            service.data.useViewableImage = publicationData.useViewableImage;
            service.featuredModelUrl = publicationData.featuredModelUrl || "images/placeholder.jpg";

            if (publicationData.viewables && Array.isArray(publicationData.viewables)) {
                service._publicationViewablesForEdit = publicationData.viewables.map(function(v) {
                    return { id: v.id, selectedForPublication: v.selectedForPublication !== false };
                });
                service.selectedViewables = publicationData.viewables.filter(function(v){ return v.selectedForPublication !== false; }).map(function(viewable) {
                    var newViewable = angular.copy(viewable);
                    if (viewable.rangeName && typeof viewable.range === 'undefined') {
                        newViewable.range = { name: viewable.rangeName };
                    }
                    if (viewable.productName && typeof viewable.product === 'undefined') {
                        newViewable.product = { name: viewable.productName };
                    }
                    if (service.data.featuredModelId && viewable.id === service.data.featuredModelId) {
                        newViewable.featuredViewable = true;
                    }
                    return newViewable;
                });
            } else {
                service.selectedViewables = [];
                service._publicationViewablesForEdit = [];
            }
            featuredViewableReturned = true;
        }

        function getRange() {
            manufacturerPublicationService.getProductRanges()
                .then(function(resp) {
                    var ranges = resp.data.productRanges || [];
                    service.rangeValues = ranges;
                    service.isRangeDropdownDisabled = false;
                })
                .catch(function(err) {
                    console.error("Error fetching ranges:", err);
                    service.isRangeDropdownDisabled = false;
                });
        }

        function rangeChanged(rangeId, preselectProductId) {
            service.data.rangeId = rangeId;
            service.viewables = [];
            service.searchResults = [];
            service.searchPerformed = false;
            service.data.productId = null;
            service.products = [];
            service.isProductDropdownDisabled = true;
            service.isFeaturedViewableDropdownDisabled = true;
            modelsSetupComplete = false;
            featuredViewableReturned = false;
            oldFeaturedViewable = null;

            if (rangeId) {
                getProducts(preselectProductId);
            } else {
                service.products = []; // Clear products if no range
                service.viewables = []; // Clear viewables if no range
            }
            console.log("Selected viewables after range change:", service.selectedViewables.length, service.selectedViewables);
        }

        function getProducts(preselectProductId) {
            console.log("publicationViewableManagerService: getProducts called for rangeId:", service.data.rangeId);
            if (!service.data.rangeId) return;
            service.isProductDropdownDisabled = true;
            manufacturerPublicationService.getProductsByRange(service.data.rangeId)
                .then(function(resp) {
                    service.products = resp.data.products || [];
                    service.isProductDropdownDisabled = service.products.length === 0;
                    if (preselectProductId && service.products.some(p => p.id === preselectProductId)) {
                        service.data.productId = preselectProductId;
                        productChanged();
                    } else if (service.products.length > 0 && !preselectProductId) {
                        // auto-select first product if not preselecting
                    } else {
                        service.viewables = [];
                        modelsSetupComplete = false;
                        setUpFeaturedViewable();
                    }
                })
                .catch(function(err) {
                    console.error("Error fetching products:", err);
                    service.products = [];
                    service.isProductDropdownDisabled = true;
                });
        }

        function productChanged() {
            console.log("publicationViewableManagerService: productChanged called with productId:", service.data.productId);
            console.log("Selected viewables BEFORE productChanged:", service.selectedViewables.length, service.selectedViewables);
            var productId = service.data.productId;
            service.viewables = [];
            modelsSetupComplete = false;
            
            // Clear temporary selections when product changes
            clearTemporarySelections();

            if (!productId) {
                service.isFeaturedViewableDropdownDisabled = true;
                setUpFeaturedViewable();
                console.log("Selected viewables AFTER productChanged (no productId):", service.selectedViewables.length, service.selectedViewables);
                return;
            }

            manufacturerPublicationService.filterViewablesByProduct(productId)
                .then(function(filterResults) {
                    console.log("Filter results:", filterResults ? filterResults.length : 0, filterResults);
                    service.viewables = (filterResults && Array.isArray(filterResults)) ? filterResults : [];
                    service.isFeaturedViewableDropdownDisabled = service.viewables.length === 0;
                    modelsSetupComplete = true;

                    if (service._publicationViewablesForEdit && service._publicationViewablesForEdit.length > 0) {
                        var currentlySelectedIds = service.selectedViewables.map(sv => sv.id);
                        console.log("Currently selected IDs:", currentlySelectedIds);
                        console.log("Publication viewables for edit:", service._publicationViewablesForEdit);
                        service._publicationViewablesForEdit.forEach(function(pubViewableOriginal) {
                            if (pubViewableOriginal.selectedForPublication) {
                                var matchingViewable = service.viewables.find(v => v.id === pubViewableOriginal.id);
                                console.log("Looking for viewable ID", pubViewableOriginal.id, "found:", !!matchingViewable);
                                if (matchingViewable && !currentlySelectedIds.includes(matchingViewable.id)) {
                                    console.log("Adding viewable to selected:", matchingViewable.name);
                                    service.selectedViewables.push(angular.copy(matchingViewable));
                                }
                            }
                        });
                    }
                    setUpFeaturedViewable();
                    console.log("Selected viewables AFTER productChanged (success):", service.selectedViewables.length, service.selectedViewables);
                })
                .catch(function(error) {
                    console.error('Error fetching viewables:', error);
                    service.viewables = [];
                    service.isFeaturedViewableDropdownDisabled = true;
                    headerBannerService.setNotification("ERROR", WENT_WRONG, 3000);
                    setUpFeaturedViewable();
                    console.log("Selected viewables AFTER productChanged (error):", service.selectedViewables.length, service.selectedViewables);
                });
        }

        function addViewable(viewable) {
            if (viewable && !service.selectedViewables.some(v => v.id === viewable.id)) {
                service.selectedViewables.push(angular.copy(viewable)); // Ensure a copy is pushed
            }
        }

        function removeViewable(viewable) {
            var index = service.selectedViewables.findIndex(v => v.id === viewable.id);
            if (index > -1) {
                service.selectedViewables.splice(index, 1);
                if (service.data.featuredModelId === viewable.id) {
                    service.data.featuredModelId = null;
                    if(service.data.useViewableImage) service.featuredModelUrl = "images/placeholder.jpg";
                    featuredViewableChanged();
                }
            }
        }

        function shouldShowAvailableViewablesList() {
            var showFindResults = service.activeViewableTab === 'find' &&
                                  service.data.rangeId &&
                                  service.data.productId &&
                                  service.viewables &&
                                  service.viewables.length > 0;
            var showSearchResults = service.activeViewableTab === 'search' &&
                                    service.searchTerm &&
                                    service.searchResults &&
                                    service.searchResults.length > 0;
            return showFindResults || showSearchResults;
        }

        function setUpFeaturedViewable() {
            console.log("publicationViewableManagerService: setUpFeaturedViewable called");
            if (featuredViewableReturned && modelsSetupComplete && service.data.featuredModelId) {
                oldFeaturedViewable = service.data.featuredModelId;
                var featuredViewableInCurrentList = service.viewables.find(v => v.id === service.data.featuredModelId);
                if (!featuredViewableInCurrentList) {
                    featuredViewableInCurrentList = service.selectedViewables.find(v => v.id === service.data.featuredModelId);
                }
                if (featuredViewableInCurrentList) {
                    if (service.data.useViewableImage && featuredViewableInCurrentList.thumbnailUrl) {
                        service.featuredModelUrl = featuredViewableInCurrentList.thumbnailUrl;
                    } else if (service.data.useViewableImage) {
                        service.featuredModelUrl = "images/placeholder.jpg";
                    }
                    featuredViewableChanged();
                } else {
                    console.warn('Featured viewable ID ' + service.data.featuredModelId + ' not found in available or selected viewables.');
                    service.data.featuredModelId = null;
                    if(service.data.useViewableImage) service.featuredModelUrl = "images/placeholder.jpg";
                    oldFeaturedViewable = null;
                    featuredViewableChanged();
                }
            } else if (featuredViewableReturned && modelsSetupComplete) {
                service.data.featuredModelId = null;
                if(service.data.useViewableImage) service.featuredModelUrl = "images/placeholder.jpg";
                oldFeaturedViewable = null;
                featuredViewableChanged();
            }
        }

        function featuredViewableChanged() {
            console.log("publicationViewableManagerService: featuredViewableChanged called");
            var currentFeaturedId = service.data.featuredModelId;
            oldFeaturedViewable = currentFeaturedId;
        }

        function performSearch() {
            var deferred = $q.defer();

            if (!service.searchTerm || service.searchTerm.trim() === '') {
                console.log('[publicationViewableManagerService.performSearch] Search term is empty. Clearing results.');
                service.searchResults = [];
                service.searchPerformed = false;
                deferred.resolve();
                return deferred.promise;
            }

            console.log('[publicationViewableManagerService.performSearch] Performing search for term:', service.searchTerm);
            service.activeViewableTab = 'search';

            manufacturerPublicationService.searchViewables(service.searchTerm)
                .then(function(response) {
                    console.log('[publicationViewableManagerService.performSearch] searchViewables API success. Response:', response);
                    if (response && response.data && angular.isDefined(response.data.searchResults) && angular.isArray(response.data.searchResults)) {
                        service.searchResults = response.data.searchResults;
                    } else {
                        service.searchResults = [];
                    }
                    service.searchPerformed = true;
                    deferred.resolve(response);
                })
                .catch(function(error) {
                    console.error('[publicationViewableManagerService.performSearch] Error from manufacturerPublicationService.searchViewables:', error);
                    service.searchResults = [];
                    service.searchPerformed = true;
                    deferred.reject(error);
                });

            return deferred.promise;
        }

        function clearSearch() {
            service.searchTerm = '';
            service.searchResults = [];
            service.searchPerformed = false;
            $timeout(function() {
                var searchInput = document.getElementById('publicationSearchInput');
                if (searchInput) {
                    searchInput.focus();
                }
            });
        }

        function setAsFeatured(viewable) {
            console.log("publicationViewableManagerService: setAsFeatured called for viewable:", viewable);
            
            // First, clear the featuredViewable flag from all viewables
            service.selectedViewables.forEach(function(sv) {
                if (sv.featuredViewable) {
                    sv.featuredViewable = false;
                }
            });
            
            if (viewable && viewable.id) {
                // Add the viewable to selected viewables if it's not already there
                if (!service.selectedViewables.some(sv => sv.id === viewable.id)) {
                    addViewable(angular.copy(viewable)); 
                }
                
                // Set the new featured viewable ID
                service.data.featuredModelId = viewable.id;
                
                // Find the viewable in the selectedViewables array and mark it as featured
                var selectedViewable = service.selectedViewables.find(sv => sv.id === viewable.id);
                if (selectedViewable) {
                    selectedViewable.featuredViewable = true;
                }
                
                // Update the featured image URL if needed
                if (service.data.useViewableImage && viewable.thumbnailUrl) {
                    service.featuredModelUrl = viewable.thumbnailUrl;
                } else if (service.data.useViewableImage) {
                    service.featuredModelUrl = "images/placeholder.jpg";
                }
                
                featuredViewableChanged();
            } else {
                // If no viewable provided, clear the featured viewable
                service.data.featuredModelId = null;
                if(service.data.useViewableImage) service.featuredModelUrl = "images/placeholder.jpg";
                featuredViewableChanged();
            }
            
            console.log("After setAsFeatured, selectedViewables:", service.selectedViewables);
        }
        
        function removeFeatured(viewable) {
            console.log("publicationViewableManagerService: removeFeatured called for viewable:", viewable);
            
            // Clear the featuredViewable flag from all viewables
            service.selectedViewables.forEach(function(sv) {
                if (sv.featuredViewable) {
                    sv.featuredViewable = false;
                }
            });
            
            // Clear the featured model ID
            service.data.featuredModelId = null;
            
            // Reset the featured model URL if needed
            if(service.data.useViewableImage) {
                service.featuredModelUrl = "images/placeholder.jpg";
            }
            
            featuredViewableChanged();
        }

        function isViewableSelected(viewableId) {
            return service.selectedViewables.some(function(v) { return v.id === viewableId; });
        }
    }
})();
