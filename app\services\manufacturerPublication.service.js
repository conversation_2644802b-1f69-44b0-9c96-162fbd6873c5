(function () {
    'use strict';

    angular
        .module('app.services')
        .factory('manufacturerPublicationService', manufacturerPublicationService);

    manufacturerPublicationService.$inject = ['$http', 'apiConstants', 'userService'];

    function manufacturerPublicationService($http, apiConstants, userService) {

        return {
            fetchPublications: fetchPublications,
            getRangeByManufacturer: getRangeByManufacturer,
            getMachineByRange: getMachineByRange,
            getModelByMachine: getModelByMachine,
            getCompletedModelUploadsByMachine: getCompletedModelUploadsByMachine,
            getManufacturerSubEntitiesForManufacturer: getManufacturerSubEntitiesForManufacturer,
            createManual: createManual,
            updateManual: updateManual,
            assignManualCustomers: assignManualCustomers,
            publishUnpublishManual: publishUnpublishManual,
            fetchManuals: fetchManuals,
            fetchModels: fetchModels,
            uploadImage: uploadImage,
            getProductRanges: getProductRanges,
            getProductsByRange: getProductsByRange,
            getViewables: getViewables,
            filterViewablesByProduct: filterViewablesByProduct,
            searchViewables: searchViewables,
            getPublicationCategories: getPublicationCategories,
            getPublicationCategory: getPublicationCategory,
            createPublicationCategory: createPublicationCategory,
            updatePublicationCategory: updatePublicationCategory,
            deletePublicationCategory: deletePublicationCategory,
            getPublication: getPublication,
            createPublication: createPublication,
            updatePublication: updatePublication,
            deletePublication: deletePublication,
            publishPublication: publishPublication,
            unpublishPublication: unpublishPublication,
            getPublicationImage: getPublicationImage
        };

        function fetchPublications() {
            var manufacturerId = userService.getManufacturerId();
            return $http.get(apiConstants.url + '/manufacturers/' + manufacturerId + '/publications');
        }

        function fetchManuals(manualId) {
            var manufacturerId = userService.getManufacturerId();
            return $http.get(apiConstants.url + '/manufacturer/' + manufacturerId + '/manualDetails/' + manualId);
        }

        function fetchModels() {
            var manufacturerId = userService.getManufacturerId();
            return $http.get(apiConstants.url + '/manufacturer/' + manufacturerId + '/models', null);
        }

        function getRangeByManufacturer() {
            var manufacturerId = userService.getManufacturerId();
            var url = apiConstants.url + '/manufacturers/' + manufacturerId + '/product-ranges';
            return $http.get(url);
        }

        function getMachineByRange(rangeId) {
            return $http.get(apiConstants.url + '/range/' + rangeId + '/machines', null);
        }

        function getModelByMachine(machineId) {
            return $http.get(apiConstants.url + '/machine/' + machineId + '/models', null);
        }

        function getCompletedModelUploadsByMachine(machineId) {
            return $http.get(apiConstants.url + '/machine/' + machineId + '/models?status=PROPERTIES_PROCESSED&setupComplete=true', null);
        }

        function getManufacturerSubEntitiesForManufacturer() {
            var manufacturerId = userService.getManufacturerId();
            return $http.get(apiConstants.url + '/manufacturer/' + manufacturerId + '/manufacturersubentities', null);
        }

        function createManual(modelId, techDocId, videoId, kitId, manualName, serialNumber, featuredModelId, featuredModelUrl, useViewableImage) {
            var manualData = createManualData(manualName, modelId, techDocId, videoId, kitId, serialNumber, featuredModelId, featuredModelUrl, useViewableImage );
            return $http.post(apiConstants.url + '/manual', manualData);
        }

        function updateManual(manualId, modelId, techDocId, videoId, kitId, manualName, serialNumberId, publicationCategoryId, featuredModelId, featuredModelUrl, useViewableImage) {
            var manualData = updateManualData(manualId, manualName, modelId, techDocId, videoId, kitId, serialNumberId, publicationCategoryId, featuredModelId, featuredModelUrl, useViewableImage);
            return $http.put(apiConstants.url + '/manual', manualData);
        }

        function createManualData(manualName, modelId, techDocId, videoId, kitId, serialNumber, featuredModelId, featuredModelUrl, useViewableImage) {
            return {
                "manualName": manualName,
                "manualDescription": "description",
                "modelId": modelId,
                "techDocId": techDocId,
                "videoId": videoId,
                "kitId": kitId,
                "serialNumber": serialNumber,
                "status": "UNPUBLISHED",
                "featuredModelId": featuredModelId,
                "featuredModelUrl": featuredModelUrl,
                "useViewableImage": useViewableImage
            };
        }

        function updateManualData(manualId, manualName, modelId, techDocId, videoId, kitId, serialNumber, publicationCategoryId, featuredModelId, featuredModelUrl, useViewableImage) {
            return {
                "manualId": manualId,
                "manualName": manualName,
                "serialNumber": serialNumber,
                "publicationCategoryId": publicationCategoryId,
                "modelId": modelId,
                "techDocId": techDocId,
                "videoId": videoId,
                "kitId": kitId,
                "featuredModelId": featuredModelId,
                "featuredModelUrl": featuredModelUrl,
                "useViewableImage": useViewableImage
            };
        }

        function assignManualCustomers(publicationId, manufacturerSubEntityIds) {
            if (typeof publicationId === 'object' && publicationId !== null) {
                publicationId = publicationId.data && publicationId.data.publicationId ? publicationId.data.publicationId :
                               publicationId.data && publicationId.data.id ? publicationId.data.id :
                               publicationId.id;
            }

            if (!publicationId || typeof publicationId !== 'number' && typeof publicationId !== 'string') {
                console.error('Invalid publication ID:', publicationId);
                return Promise.reject('Invalid publication ID');
            }

            // Convert all IDs to numbers
            var numericIds = manufacturerSubEntityIds.map(function(id) {
                return typeof id === 'string' ? parseInt(id, 10) : id;
            }).filter(function(id) {
                return !isNaN(id);
            });

            console.log('Assigning customers to publication', publicationId, ':', numericIds);
            return $http.put(apiConstants.url + '/manual/' + publicationId + '/assign', numericIds);
        }

        function publishUnpublishManual(publicationId, status) { 
            // The status parameter is the NEW status we want to set, not the current status
            // No need to toggle it here as that's already done in the calling service
            var data = {status: status};
            console.log('Setting publication', publicationId, 'to status', status);
            return $http.put(apiConstants.url + '/manual/' + publicationId + '/status', data);
        }

        function uploadImage(imageBase64, contentType) {
            var manufacturerId = userService.getManufacturerId();
            var url = apiConstants.url + '/manufacturers/' + manufacturerId + '/images';
            var payload = {
                imageBase64: imageBase64,
                contentType: contentType
            };
            return $http.post(url, payload);
        }

        function getProductRanges() {
            var manufacturerId = userService.getManufacturerId();
            var url = apiConstants.url + '/manufacturers/' + manufacturerId + '/product-ranges';
            return $http.get(url);
        }

        function getProductsByRange(productRangeId) {
            var manufacturerId = userService.getManufacturerId();
            var url = apiConstants.url + '/manufacturers/' + manufacturerId + '/product-ranges/' + productRangeId + '/products';
            return $http.get(url);
        }

        function getViewables(manufacturerId) {
            var url = apiConstants.url + '/manufacturers/' + manufacturerId + '/viewables';
            return $http.get(url).then(function(response) {
                return response.data.viewables;
            });
        }

        function filterViewablesByProduct(productId) {
            var manufacturerId = userService.getManufacturerId();
            var url = apiConstants.url + '/manufacturers/' + manufacturerId + '/viewables/filter';
            var payload = { filterProductId: productId };
            return $http.post(url, payload).then(function(response) {
                return response.data.filterResults;
            });
        }

        function searchViewables(searchTerm) {
            var manufacturerId = userService.getManufacturerId();
            var url = apiConstants.url + '/manufacturers/' + manufacturerId + '/viewables/search';
            var payload = { searchParameter: searchTerm };
            return $http.post(url, payload);
        }

        function getPublicationCategories() {
            var manufacturerId = userService.getManufacturerId();
            var url = apiConstants.url + '/manufacturers/' + manufacturerId + '/publication-categories';
            return $http.get(url);
        }

        function getPublicationCategory(publicationCategoryId) {
            var manufacturerId = userService.getManufacturerId();
            var url = apiConstants.url + '/manufacturers/' + manufacturerId + '/publication-categories/' + publicationCategoryId;
            return $http.get(url);
        }

        function createPublicationCategory(categoryName) {
            var manufacturerId = userService.getManufacturerId();
            var url = apiConstants.url + '/manufacturers/' + manufacturerId + '/publication-categories';
            var payload = { name: categoryName };
            return $http.post(url, payload);
        }

        function updatePublicationCategory(publicationCategoryId, newCategoryName) {
            var manufacturerId = userService.getManufacturerId();
            var url = apiConstants.url + '/manufacturers/' + manufacturerId + '/publication-categories/' + publicationCategoryId;
            var payload = { name: newCategoryName };
            return $http.put(url, payload);
        }

        function deletePublicationCategory(publicationCategoryId) {
            var manufacturerId = userService.getManufacturerId();
            var url = apiConstants.url + '/manufacturers/' + manufacturerId + '/publication-categories/' + publicationCategoryId;
            return $http.delete(url);
        }

        function getPublication(publicationId) { 
            // Extract the ID if it's an object
            if (typeof publicationId === 'object' && publicationId !== null) {
                publicationId = publicationId.data && publicationId.data.publicationId ? publicationId.data.publicationId :
                               publicationId.data && publicationId.data.id ? publicationId.data.id :
                               publicationId.id;
            }

            var manufacturerId = userService.getManufacturerId();
            var url = apiConstants.url + '/manufacturers/' + manufacturerId + '/publications/' + publicationId;
            return $http.get(url);
        }

        function createPublication(publicationData) { 
             var manufacturerId = userService.getManufacturerId();
             var url = apiConstants.url + '/manufacturers/' + manufacturerId + '/publications';
             return $http.post(url, publicationData)
                .then(function(response) {
                    // Ensure we return the data in a consistent format
                    return {
                        data: response.data,
                        id: response.data.id || response.data.publicationId
                    };
                });
        }

        function updatePublication(publicationId, publicationData) { 
             var manufacturerId = userService.getManufacturerId();
             var url = apiConstants.url + '/manufacturers/' + manufacturerId + '/publications/' + publicationId;
             return $http.put(url, publicationData);
        }

        function deletePublication(publicationId) { 
             var manufacturerId = userService.getManufacturerId();
             var url = apiConstants.url + '/manufacturers/' + manufacturerId + '/publications/' + publicationId;
             return $http.delete(url);
        }

        function publishPublication(publicationId) { 
             var manufacturerId = userService.getManufacturerId();
             var url = apiConstants.url + '/manufacturers/' + manufacturerId + '/publications/' + publicationId + '/publish';
             return $http.post(url, null);
        }

        function unpublishPublication(publicationId) { 
             var manufacturerId = userService.getManufacturerId();
             var url = apiConstants.url + '/manufacturers/' + manufacturerId + '/publications/' + publicationId + '/unpublish';
             return $http.post(url, null);
        }

        function getPublicationImage(imageId) {
            var manufacturerId = userService.getManufacturerId();
            var url = apiConstants.url + '/manufacturers/' + manufacturerId + '/images/' + imageId;
            return $http.get(url);
        }

    }
})();
