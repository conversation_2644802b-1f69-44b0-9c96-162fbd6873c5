(function () {
    'use strict';

    angular
        .module('app.services')
        .factory('manufacturerService', manufacturerService);

    manufacturerService.$inject = ['$http', 'apiConstants', 'userService'];

    var manufacturer = null;
    var manufacturerId = null;
    
    function manufacturerService($http, apiConstants, userService) {
        return {
            setManufacturer: setManufacturer,
            getManufacturer: getManufacturer,
            getManufacturerId: getManufacturerId,
            fetchManufacturerDetails: fetchManufacturerDetails,
            updateManufacturerDetails: updateManufacturerDetails,
            fetchManufacturerDetailsByDomain: fetchManufacturerDetailsByDomain
        };


        function setManufacturer(obj) {
            manufacturer = obj;
        }

        function getManufacturer() {
            return manufacturer;
        }
      
        function getManufacturerId() {
            return manufacturer && manufacturer.manufacturerId;
        }

        function fetchManufacturerDetails() {
            var manufacturerId = userService.getManufacturerId();

            return $http.get(apiConstants.url + '/manufacturer/' + manufacturerId, null);
        }

        function updateManufacturerDetails(phone, email, emailSignature, logoResponseUrl, viewerColour, edgingEnabledDefault, contactUsPageEnabled, additionalEmails) {
            var manufacturerId = userService.getManufacturerId();

            // Clean up additionalEmails - only include id field for existing records
            var cleanedAdditionalEmails = (additionalEmails || []).map(function(emailObj) {
                var cleanedObj = {
                    email: emailObj.email,
                    label: emailObj.label
                };
                // Only include id if it exists (for existing records)
                if (emailObj.hasOwnProperty('id') && emailObj.id !== null && emailObj.id !== undefined) {
                    cleanedObj.id = emailObj.id;
                }
                return cleanedObj;
            });

            var updateData = {
                "phone": phone,
                "supportEmail": email,
                "emailSignature": emailSignature,
                "logoUrl": logoResponseUrl,
                "viewerColour": viewerColour,
                "edgingEnabledDefault": edgingEnabledDefault,
                "contactUsPageEnabled": contactUsPageEnabled,
                "additionalEmails": cleanedAdditionalEmails
            };
            return $http.put(apiConstants.url + '/manufacturer/' + manufacturerId + '/updateDetails', updateData);
        }

        function fetchManufacturerDetailsByDomain(subDomain) {

            return $http.get(apiConstants.url + '/manufacturer/subdomain/' + subDomain, null);
        }
    }
})();
