(function () {
    'use strict';

    angular
        .module('app.services')
        .factory('manufacturerService', manufacturerService);

    manufacturerService.$inject = ['$http', 'apiConstants', 'userService'];

    var manufacturer = null;
    var manufacturerId = null;
    
    function manufacturerService($http, apiConstants, userService) {
        return {
            setManufacturer: setManufacturer,
            getManufacturer: getManufacturer,
            getManufacturerId: getManufacturerId,
            fetchManufacturerDetails: fetchManufacturerDetails,
            updateManufacturerDetails: updateManufacturerDetails,
            fetchManufacturerDetailsByDomain: fetchManufacturerDetailsByDomain
        };


        function setManufacturer(obj) {
            manufacturer = obj;
        }

        function getManufacturer() {
            return manufacturer;
        }
      
        function getManufacturerId() {
            return manufacturer && manufacturer.manufacturerId;
        }

        function fetchManufacturerDetails() {
            var manufacturerId = userService.getManufacturerId();

            return $http.get(apiConstants.url + '/manufacturer/' + manufacturerId, null);
        }

        function updateManufacturerDetails(phone, email, emailSignature, logoResponseUrl, viewerColour, edgingEnabledDefault, contactUsPageEnabled, additionalEmails) {
            var manufacturerId = userService.getManufacturerId();

            var updateData = {
                "phone": phone,
                "supportEmail": email,
                "emailSignature": emailSignature,
                "logoUrl": logoResponseUrl,
                "viewerColour": viewerColour,
                "edgingEnabledDefault": edgingEnabledDefault,
                "contactUsPageEnabled": contactUsPageEnabled,
                "additionalEmails": additionalEmails || []
            };
            return $http.put(apiConstants.url + '/manufacturer/' + manufacturerId + '/updateDetails', updateData);
        }

        function fetchManufacturerDetailsByDomain(subDomain) {

            return $http.get(apiConstants.url + '/manufacturer/subdomain/' + subDomain, null);
        }
    }
})();
