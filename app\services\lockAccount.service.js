(function () {
    'use strict';

    angular
        .module('app.services')
        .factory('lockAccountService', lockAccountService);

    lockAccountService.$inject = ['$http', 'apiConstants'];

    function lockAccountService($http, apiConstants) {
        return {
            lockUser: lockUser
        };

        function lockUser(userId) {
            return $http.get(apiConstants.url + '/security/user/' + userId + '/lock');
        }
    }
})();
