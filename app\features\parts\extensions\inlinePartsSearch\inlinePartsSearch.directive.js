(function () {
    'use strict';

    angular
        .module('app.parts')
        .directive('inlinePartsSearch', inlinePartsSearch);

    function inlinePartsSearch() {
        var directive = {
            restrict: 'E',
            templateUrl: 'features/parts/extensions/inlinePartsSearch/inlinePartsSearch.html',
            controller: 'InlinePartsSearchController',
            controllerAs: 'inlinePartsSearchCtrl',
            scope: {onAddClicked: '&', subEntityId: '=', userId: '='},
            bindToController: true
        };
        return directive;
    }

})();