(function () {
    'use strict';

    angular
        .module('app.viewer')
        .controller('KitModalController', KitModalController);

    KitModalController.$inject = ['$uibModalInstance', 'kitService', 'basketService', 'kits', '$stateParams', 'userService', '$timeout' ];

    function KitModalController($uibModalInstance, kitService, basketService, kits, $stateParams, userService, $timeout) {
        var vm = this;

        var machineName = $stateParams.machineName;

        vm.partNumber = "";
        vm.kitList = [];

        vm.cancel = cancel;

        vm.addKitToBasket = addKitToBasket;
        vm.isStockWarehousesEnabled = userService.getStockWarehousesEnabled();

        initialize();

        function initialize() {
            vm.kitList = kits;
            vm.hasOrderRole = userService.hasOrderRole();
        }

        function cancel() {
            $uibModalInstance.close();
        }

        function addKitToBasket(kit) {
            kit.clicked = true;
            var kitItem = {
              masterPartNumber: kit.masterPartNumber,
              kitPrice: kit.kitPrice,
              kitId: kit.id,
              description: kit.masterPartDescription,
              modelId: kit.modelId ? kit.modelId.toString() : null,
              quantity: 1,
              parts: kit.parts,
              kitStockLevel: kit.kitStockLevel,
              masterPartKitId: kit.masterPartKitId,
              stock: kit.stock,
            };
            basketService.addKit(kitItem);
            $timeout(function () {
              kits.clicked = false;
            }, 500);
          }
    }

})();
