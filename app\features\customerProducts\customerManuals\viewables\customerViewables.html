<section ng-if="customerViewablesCtrl.isReady && customerViewablesCtrl.modelList.length > 0">
    <h1 translate>CUST_VIEWABLES.SELECT_VIEWABLE</h1>

    <div class="customerProductsGrid row">
        <div
            class="customerProductsItemContainer d-flex col-12 col-md-6 col-lg-4 flex-column"
            ng-repeat="viewable in customerViewablesCtrl.modelList"
        >
            <div class="customerProductsItem">
                <div class="customerViewablesImage" ng-click="customerViewablesCtrl.goToViewer(viewable)">
                    <div class="customerProductsHover">
                        <a class="btn primary view-all" href="">
                            <i class="fa fa-eye" aria-hidden="true"></i> {{"GENERAL.VIEW" | translate}}
                        </a>
                    </div>

                    <img class="w-100" ng-src="{{viewable.thumbnailUrl || './images/PDF-placeholder.png'}}" alt="thumbnailViewables" />
                </div>

                <div class="customerProductsInformationBox p-4">
                    <h3 class="cadWrap pb-0 mb-0">{{viewable.name}}</h3>
                    <small>{{viewable.rangeName}} - {{viewable.productName}}</small>
                </div>
            </div>
        </div>
    </div>
</section>

<section ng-if="customerViewablesCtrl.isReady && customerViewablesCtrl.modelList.length === 0">
    <h2 translate>CUST_VIEWABLES.NO_VIEWABLES</h2>
</section>
