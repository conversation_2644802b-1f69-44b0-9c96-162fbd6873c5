<section ng-if="customerViewablesCtrl.isReady && customerViewablesCtrl.modelList.length > 0">
    <h1 translate>CUST_VIEWABLES.SELECT_VIEWABLE</h1>

    <div class="customerProductsGrid row">
        <div
            class="customerProductsItemContainer d-flex col-12 col-md-6 col-lg-4 flex-column"
            ng-repeat="model in customerViewablesCtrl.modelList"
        >
            <div class="customerProductsItem">
                <div class="customerViewablesImage" ng-click="customerViewablesCtrl.goToViewer(model)">
                    <div class="customerProductsHover">
                        <a class="btn primary view-all" href=""
                            ><i class="fa fa-eye" aria-hidden="true"></i> {{"GENERAL.VIEW" | translate}}</a
                        >
                    </div>

                    <img class="w-100" src="{{model.thumbnailUrl}}" class="" alt="thumbnailViewables" />
                </div>

                <div class="customerProductsInformationBox p-4">
                    <h3 class="cadWrap pb-0 mb-0">{{model.modelName}}</h3>
                </div>
            </div>
        </div>
    </div>
</section>

<section ng-if="customerViewablesCtrl.isReady && customerViewablesCtrl.modelList.length === 0">
    <h2 translate>CUST_VIEWABLES.NO_VIEWABLES</h2>
</section>
