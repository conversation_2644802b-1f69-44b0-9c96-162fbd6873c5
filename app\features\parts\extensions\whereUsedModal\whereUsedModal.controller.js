(function () {
    'use strict';

    angular
        .module('app.shared')
        .controller('whereUsedModalController', whereUsedModalController);

    whereUsedModalController.$inject = ['$uibModalInstance', 'confirmObject', 'models', 'partNumber', '$state', '$stateParams', '$window', 'userService'];

    function whereUsedModalController($uibModalInstance, confirmObject, models, partNumber, $state, $stateParams, $window, userService) {
        var vm = this;
        vm.models = models;
        vm.cancel = $uibModalInstance.dismiss;
        vm.confirm = confirm;
        vm.goToModel = goToModel;
        vm.goToViewer = goToViewer;
        vm.isManufacturerSubEntity = userService.isManufacturerSubEntity();

        // Store the current state in session storage when the modal is opened
        $window.sessionStorage.setItem('fromWhereUsedModal', 'true');

        if(confirmObject){
            vm.titleText = confirmObject.titleText;
            vm.bodyText = confirmObject.bodyText;
        }

        function goToViewer(model) {
            $uibModalInstance.close('viewModel');

            // Retrieve the previous state from session storage
            // Store the previous state in $stateParams or another service
            $stateParams.previousState = $window.sessionStorage.getItem('fromWhereUsedModal');
            console.log ("previousState: " + $stateParams.previousState);

            var stateName = model.is2d ? "customerPdfViewer" : "customerViewer";

            $state.go(stateName, {
                machineName: model.machineName,
                autodeskURN: model.autodeskUrn,
                modelId: model.modelId,
                viewableName: model.modelName,
                onBehalfOf: $stateParams.onBehalfOf,
                manualId: $stateParams.manualId,
                translateType: model.translateType,
                productId: $stateParams.productId,
                roomGuid: null,
                fromWhereUsedModal: true,
                partNumber: partNumber
            });
        }

        function goToModel(model) {
            $uibModalInstance.close('viewModel');

            // Retrieve the previous state from session storage
            // Store the previous state in $stateParams or another service
            $stateParams.previousState = $window.sessionStorage.getItem('fromWhereUsedModal');
            console.log ("previousState: " + $stateParams.previousState);

            var stateName = model.is2d ? "pdfViewer" : "manufacturerViewer";

            $state.go(stateName, {
                productId: model.machineId,
                autodeskURN: model.autodeskUrn,
                modelId: model.modelId,
                machineName: model.machineName,
                viewableName: model.modelName,
                translateType: model.translateType,
                fromWhereUsedModal: true
            });

        }

        function confirm() {
            $uibModalInstance.close();
        }
    }
})();
