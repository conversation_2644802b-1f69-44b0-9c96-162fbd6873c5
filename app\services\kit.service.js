(function () {
    'use strict';

    angular
        .module('app.services')
        .factory('kitService', kitService);

    kitService.$inject = ['$http', 'apiConstants'];

    function kitService($http, apiConstants) {
        return {
            fetchKit: fetchKit,
            fetchKitsForModel: fetchKitsForModel,
            fetchKitsForPart: fetchKitsForPart,
            createKit: createKit,
            editKit: editKit,
            deleteKit: deleteKit
        };

        function fetchKit(kitId) {
            return $http.get(apiConstants.url + '/kit/' + kitId);
        }

        function fetchKitsForModel(modelId) {
            return $http.get(apiConstants.url + '/kit/model/' + modelId);
        }

        function fetchKitsForPart(partId) {
            return $http.get(apiConstants.url + '/kit/part/' + partId);
        }

        function createKit(modelId, title, description, kitParts) {
            var kit = {
                "title": title,
                "description": description,
                "parts": kitParts
            };
            return $http.post(apiConstants.url + '/kit/model/' + modelId, kit);
        }

        function editKit(title, description, parts, id) {
            var updatedKit = {
                title: title,
                description: description,
                parts: parts,
                id: id
            };
            return $http.put(apiConstants.url + '/kit', updatedKit);
        }

        function deleteKit(kitId) {
            return $http.delete(apiConstants.url + '/kit/' + kitId);
        }


    }
})();
