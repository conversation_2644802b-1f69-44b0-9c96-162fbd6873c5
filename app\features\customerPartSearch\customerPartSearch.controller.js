(function () {
  "use strict";

  angular
    .module("app.parts")
    .controller("CustomerPartSearchController", CustomerPartSearchController);

  CustomerPartSearchController.$inject = [
    "masterPartService",
    "userService",
    "basketService",
    "headerBannerService",
    "$timeout",
    "$translate",
    "ordersService",
    "$uibModal",
    "$stateParams",
    "$rootScope",
    "persistentSearch",
    "$window",
    "$scope",
  ];

  function CustomerPartSearchController(
    masterPartService,
    userService,
    basketService,
    headerBannerService,
    $timeout,
    $translate,
    ordersService,
    $uibModal,
    $stateParams,
    $rootScope,
    persistentSearch,
    $window,
    $scope
  ) {
    var vm = this;
    var ENTER_QUANTITY;
    var SEARCH_FAILED;

    $translate([
      "CUST_PART_SEARCH.ENTER_QUANTITY",
      "CUST_PART_SEARCH.SEARCH_FAILED",
    ]).then(function (resp) {
      ENTER_QUANTITY = resp["CUST_PART_SEARCH.ENTER_QUANTITY"];
      SEARCH_FAILED = resp["CUST_PART_SEARCH.SEARCH_FAILED"];
    });

    vm.searchBy = "partNumber";
    vm.searching = false;
    vm.showSupersession = false;
    vm.masterParts = [];
    vm.kits = [];
    vm.isPreviewStockLevelEnabled = userService.getPreviewStockLevelEnabled();
    vm.defaultCurrency = userService.getDefaultCurrency();
    vm.previewPricingEnabled = userService.getPreviewPricingEnabled();
    vm.isManufacturer = userService.isManufacturer();
    vm.hidePrice = hidePrice;
    vm.isCustomerSearchManualsOnly = userService.getCustomerSearchManualsOnly();
    vm.isSearchOnlyPartNumbers = userService.getSearchOnlyPartNumbers();
    vm.isDealerPlusCustomer =
      userService.isManufacturerSubEntity() &&
      userService.isDealerPlusCustomer();
    vm.hasOrdersAccess = userService.hasOrderRole();
    vm.searchBy = persistentSearch.getPartSearchType();
    vm.isStockWarehousesEnabled = userService.getStockWarehousesEnabled();
    vm.hideUnpublishedParts = userService.getHideUnpublishedParts();

    vm.isLoading = false;

    vm.search = search;
    vm.addToBasket = addToBasket;
    vm.addToBasketRecent = addToBasketRecent;
    vm.addToBasketFrequent = addToBasketFrequent;
    vm.quantityUpdated = quantityUpdated;
    vm.recentQuantityUpdated = recentQuantityUpdated;
    vm.frequentQuantityUpdated = frequentQuantityUpdated;
    vm.whereUsedModal = whereUsedModal;
    vm.toggleKitsAccordion = toggleKitsAccordion;
    vm.manufacturerSubEntityId = userService.getManufacturerSubEntityId();
    vm.addKitToBasket = addKitToBasket;
    vm.toggleSupersessionHistory = toggleSupersessionHistory;
    vm.fetchSupersessionHistory = fetchSupersessionHistory;
    vm.toggleSupersessionAccordion = toggleSupersessionAccordion;

    vm.isPartNoteAdded = false;
    vm.accordionStates = {};
    vm.accordionSupersession = {};

    var WHERE_USED, WHERE_USED_BODY;
    $translate(["PART_SEARCH.WHERE_USED", "PART_SEARCH.WHERE_USED_BODY"]).then(
      function (resp) {
        WHERE_USED = resp["PART_SEARCH.WHERE_USED"];
        WHERE_USED_BODY = resp["PART_SEARCH.WHERE_USED_BODY"];
      }
    );

    initialize();

    function initialize() {
      hidePrice();
      ordersService
        .getMostFrequentlyOrdered()
        .then(mostFrequentlySuccess, serviceFailed);
      ordersService
        .getMostRecentlyOrdered()
        .then(mostRecentlySuccess, serviceFailed);

      // Check for saved searchValue in sessionStorage
      var savedSearchValue = sessionStorage.getItem("customerPartSearchValue");
      if (savedSearchValue) {
        vm.searchValue = savedSearchValue;
        search(); // Perform search
      }

      $rootScope.$on("$locationChangeStart", function (event, newUrl, oldUrl) {
        if (oldUrl.includes("customerPartSearch")) {
          // If leaving customerPartSearch page
          sessionStorage.removeItem("customerPartSearchValue"); // Remove saved searchValue
        }
      });

      $rootScope.$watch(
        function () {
          return vm.searchBy;
        },
        function (newValue, oldValue) {
          if (newValue !== oldValue) {
            persistentSearch.setPartSearchType(newValue);
          }
        }
      );
    }

    function mostFrequentlySuccess(response) {
      vm.frequentParts = response.data;
      frequentQuantityUpdated();
    }

    function mostRecentlySuccess(response) {
      vm.recentParts = response.data;
      recentQuantityUpdated();
    }

    function serviceFailed() {
      console.log("Service failed");
    }

    function addToBasket(index) {
      var partToAdd = angular.copy(vm.masterParts[index]); // Creating a copy of the part because it will affect the UI when using supersession part number
  
      if (partToAdd.quantity > 0) {
          if (partToAdd.machineName === null || partToAdd.machineName === undefined) {
              partToAdd.machineName = "Part Search";
          }
  
        if (partToAdd.inSupersession) {
          partToAdd.partNumber = partToAdd.maxSupersessionPartNumber;
          partToAdd.masterPartId = partToAdd.maxSupersessionPartId;

          if (partToAdd.supersessionDetails && partToAdd.supersessionDetails.length > 0) {

            var currentSupersessionPart = partToAdd.supersessionDetails[0];

            if (currentSupersessionPart.description && currentSupersessionPart.description.trim() !== "") {
              partToAdd.description = currentSupersessionPart.description;
            } else {
              partToAdd.description = "";
            }

            if (currentSupersessionPart.price) {
              partToAdd.price = currentSupersessionPart.price;
            } else {
              partToAdd.price = null;
            }
          }
        }
  
          basketService.addPart(partToAdd);
          vm.masterParts[index].clicked = true;
  
          $timeout(function () {
              vm.masterParts[index].clicked = false;
          }, 500);
      } else {
          headerBannerService.setNotification("WARN", ENTER_QUANTITY, 3000);
      }
  }

    function addKitToBasket(index) {
      var kitToAdd = vm.kits[index];

      masterPartService.getPurchaserKit(vm.manufacturerSubEntityId, kitToAdd.kitId)
        .then(function (response) {
          if (response.data && response.data.parts) {
            kitToAdd.kitDetails = response.data.parts;

            if (kitToAdd.quantity > 0) {
              var kitItem = {
                masterPartNumber: kitToAdd.partNumber,
                kitPrice: (kitToAdd.price && typeof kitToAdd.price === 'object') ? kitToAdd.price.value : null,
                kitId: kitToAdd.kitId,
                description: kitToAdd.partDescription,
                modelId: kitToAdd.modelId ? kitToAdd.modelId.toString() : null,
                quantity: kitToAdd.quantity,
                parts: kitToAdd.kitDetails,
                masterPartKitId: kitToAdd.masterPartKitId,
                stock: kitToAdd.stock,
                machineName: kitToAdd.machineName || "Kit Search",
                modelName: kitToAdd.modelName
              };

              basketService.addKit(kitItem);
              vm.kits[index].clicked = true;

              $timeout(function () {
                vm.kits[index].clicked = false;
              }, 500);
            } else {
              headerBannerService.setNotification("WARN", ENTER_QUANTITY, 3000);
            }
          }
        })
        .catch(function (error) {
          console.log("Error fetching kit details:", error);
        });
    }

    function search() {
      sessionStorage.setItem("customerPartSearchValue", vm.searchValue);

      var onBehalfOf =
        $stateParams.onBehalfOf && $stateParams.onBehalfOf !== "null"
          ? JSON.parse(decodeURIComponent(atob($stateParams.onBehalfOf)))
          : undefined;

      console.log("is on behalf of: ", onBehalfOf);

      var manufacturerSubEntityId = onBehalfOf
        ? onBehalfOf.manufacturerSubEntityId
        : userService.getManufacturerSubEntityId();

      var onBehalfOfUserId = userService.getOnBehalfOfUserId();

      vm.masterParts = [];
      vm.kits = []; 
      vm.accordionStates = [];
      vm.accordionSupersession = [];
      vm.resultsReturned = false;
      vm.searchError = null;
      vm.searching = true;
      vm.isLoading = true;

      var partNumber, partDescription;

      if (vm.searchBy === "partNumber") {
        partNumber = vm.searchValue;
        partDescription = null;
      } else if (vm.searchBy === "partDescription") {
        partDescription = vm.searchValue;
        partNumber = null;
      }

      var kitSearchPromise = masterPartService
        .purchaserKitSearch(manufacturerSubEntityId, partNumber, partDescription)
        .then(searchKitSuccess);

      var partSearchPromise = masterPartService
        .subEntityPartSearch(manufacturerSubEntityId, partNumber, partDescription, onBehalfOfUserId)
        .then(searchSuccess, searchFailed);

      // Waitfor both promises
      Promise.all([kitSearchPromise, partSearchPromise]).then(function () {
        $timeout(function () {
          vm.isLoading = false; // hide loading
          vm.searching = false; // stop searching
          vm.resultsReturned = true; // results returned
        });
      });
    }

    function searchKitSuccess(resp) {
      vm.kits = resp.data.kits !== null ? resp.data.kits : [];
      vm.kits.forEach(function(kit) {
        if (kit.quantity === undefined || kit.quantity === null || kit.quantity < 1) {
          kit.quantity = 1;
        }
      });
    }

    function searchSuccess(resp) {
      vm.masterParts =
        resp.data.masterParts !== null ? resp.data.masterParts : [];
      console.log("Master parts search result:", vm.masterParts);
      vm.masterParts.forEach(function (part) {
        part.noteShow = false;
        part.show = !vm.hideUnpublishedParts || part.usedInModelCount > 0;
        if (part.note) {
          part.partNote = part.note.split('\n')
          part.isOpen = true;
          console.log("Part:", part);
          if (part.show) { part.noteShow = true; }
        }
        

        part.hoverTableBG = !!part.partNote;
        part.partNoteClass = part.hoverTableBG;
        part.borderLeft = part.isOpen;
      });
      setDefaultMasterPartsQuantity();
      quantityUpdated();
      vm.isPartNoteAdded = true;
    }

    function setDefaultMasterPartsQuantity() {
      for (var i = 0; i < vm.masterParts.length; i++) {
        if (!vm.masterParts[i].quantity) {
          vm.masterParts[i].quantity = 1;
        }
      }
    }

    function searchFailed() {
      vm.searchError = SEARCH_FAILED;
      vm.searching = false;
      vm.isLoading = false;
    }

    function quantityUpdated() {
      for (var i = 0; i < vm.masterParts.length; i++) {
        vm.masterParts[i].totalPrice = vm.masterParts[i].price ?
          vm.masterParts[i].price * vm.masterParts[i].quantity : "TBC"; 
      }

      for (var j = 0; j < vm.kits.length; j++) {
        if (vm.kits[j].quantity === undefined) {
          vm.kits[j].quantity = 1;
        }
        vm.kits[j].totalPrice = vm.kits[j].price && vm.kits[j].price.value ?
          vm.kits[j].price.value * vm.kits[j].quantity : "TBC"; 
      }
    }

    function recentQuantityUpdated() {
      for (var i = 0; i < vm.recentParts.length; i++) {
        vm.recentParts[i].totalPrice =
          vm.recentParts[i].price * vm.recentParts[i].quantity;
      }
    }

    function frequentQuantityUpdated() {
      for (var i = 0; i < vm.frequentParts.length; i++) {
        vm.frequentParts[i].totalPrice =
          vm.frequentParts[i].price * vm.frequentParts[i].quantity;
      }
    }

    function addToBasketRecent(index) {
      var partToAdd = vm.recentParts[index];

      if (partToAdd.quantity > 0) {
        if (
          partToAdd.machineName === null ||
          partToAdd.machineName === undefined
        ) {
          partToAdd.machineName = "Part Search";
        }
        basketService.addPart(partToAdd);
        vm.recentParts[index].clicked = true;
        $timeout(function () {
          vm.recentParts[index].clicked = false;
        }, 500);
      } else {
        headerBannerService.setNotification("WARN", ENTER_QUANTITY, 3000);
      }
    }

    function addToBasketFrequent(index) {
      var partToAdd = vm.frequentParts[index];

      if (partToAdd.quantity > 0) {
        if (
          partToAdd.machineName === null ||
          partToAdd.machineName === undefined
        ) {
          partToAdd.machineName = "Part Search";
        }
        basketService.addPart(partToAdd);
        vm.frequentParts[index].clicked = true;
        $timeout(function () {
          vm.frequentParts[index].clicked = false;
        }, 500);
      } else {
        headerBannerService.setNotification("WARN", ENTER_QUANTITY, 3000);
      }
    }

    function handleModalClose(ifViewModel) {
      if (ifViewModel === "viewModel") {
      } else {
        $window.sessionStorage.setItem("fromWhereUsedModal", "false");
      }
    }

    function whereUsedModal(part) {
      var subEntityId = userService.getManufacturerSubEntityId();
      masterPartService
        .getPublishedModelsForPart(subEntityId, part.partNumber)
        .then(function (response) {
          part.models = response.data.models;
          var modalInstance = $uibModal.open({
            templateUrl:
              "features/parts/extensions/whereUsedModal/whereUsedModal.html",
            controller: "whereUsedModalController",
            controllerAs: "whereUsedModalCtrl",
            resolve: {
              partNumber: function () {
                return part.partNumber;
              },
              models: function () {
                return part.models;
              },
              confirmObject: function () {
                return {
                  titleText: WHERE_USED,
                  bodyText: WHERE_USED_BODY,
                };
              },
            },
          });

          // Handle modal close or dismiss events
          modalInstance.result.then(handleModalClose, handleModalClose);
        }, serviceFailed);
    }

    function toggleKitsAccordion(kitId, index, $event) {
      if ($event.target.tagName !== 'INPUT' && $event.target.tagName !== 'BUTTON' && $event.target.tagName !== 'I') {
        if (vm.accordionStates[index] === undefined) {
          vm.accordionStates[index] = false;
        }

        // Toggle Accordion
        vm.accordionStates[index] = !vm.accordionStates[index];
        fetchAndDisplayPurchaserKitDetails(kitId, index)
      }
    }

    function fetchAndDisplayPurchaserKitDetails(kitId, index) {
      masterPartService.getPurchaserKit(vm.manufacturerSubEntityId, kitId)
        .then(function (response) {
          if (response.data && response.data.parts) {
            var kit = vm.kits[index];
            if (kit) {
              kit.kitDetails = response.data.parts;
            }
          }
        })
        .catch(function (error) {
          console.log(error);
        });
    }

    function toggleSupersessionHistory(selectedPart) {
      var isAlreadyOpen = selectedPart.showSupersessionHistory;
      vm.masterParts.forEach(function (part) {
        part.showSupersessionHistory = false;
      });
      if (!isAlreadyOpen) {
        selectedPart.showSupersessionHistory = true;
        fetchSupersessionHistory(selectedPart.maxSupersessionPartNumber);
      }
    }

    vm.masterParts.forEach(function () {
      vm.showSupersessionHistory = false;
      fetchSupersessionHistory
    });

    function fetchSupersessionHistory(masterPartNumber) {
      console.log("Fetching supersession history for part:", masterPartNumber);
  
      if (!masterPartNumber) {
          console.error("No masterPartNumber provided for fetching supersession history.");
          return;
      }
  
      vm.isSupersessionLoading = true;
      var purchaserId = userService.getManufacturerSubEntityId();
      var currentLanguage = $translate.use();
  
      masterPartService.getSupersessionHistoryForPurchaser(purchaserId, masterPartNumber)
          .then(response => {
              var history = response.data.supersessionHistory.sort((a, b) => b.supersessionIndex - a.supersessionIndex);
              
              if (!history || history.length === 0) {
                  console.error("No supersession history data found in the response.");
                  vm.hasError = true;
              } else {
                
                  vm.supersessionHistory = history.map(item => {
                      let matchingDescription = "No description available";
                      if (item.languageDescriptions && item.languageDescriptions.length > 0) {
                          for (let desc of item.languageDescriptions) {
                              if (desc.code === currentLanguage) {
                                  matchingDescription = desc.description;
                                  break;
                              }
                          }
                      }
                      return {
                          masterPartId: item.masterPartId,
                          partNumber: item.partNumber,
                          partDescription: matchingDescription,
                          supersedingPartNumber: item.supersedingPartNumber
                      };
                  });
  
                  vm.hasError = false;
                  console.log("Supersession history fetched successfully:", vm.supersessionHistory);
              }
          })
          .catch(error => {
              console.error("Error fetching supersession history:", error);
              vm.hasError = true;
          })
          .finally(() => {
              vm.isSupersessionLoading = false;
          });
  }    

    function hidePrice() {
      if (vm.isManufacturer) {
        vm.hidePrice = false;
      } else {
        vm.hidePrice = !userService.getPreviewPricingEnabled();
      }
      return vm.hidePrice;
    }

    angular.element(document).on('click', function (event) {
      $scope.$apply(function () {
        var tooltipContainers = document.querySelectorAll('.tooltip-container');
        var clickedInsideTooltip = false;

        tooltipContainers.forEach(function (container) {
          if (container.contains(event.target)) {
            clickedInsideTooltip = true;
          }
        });

        if (!clickedInsideTooltip) {
          vm.masterParts.forEach(function (part) {
            if (part.showSupersessionHistory) {
              part.showSupersessionHistory = false;
            }
          });
        }
      });
    });

    function toggleSupersessionAccordion(maxSupersessionPartNumber, index) {
      toggleAccordionState(index);
      fetchAndUpdateMasterPartDetails(maxSupersessionPartNumber, index);
    }

    function toggleAccordionState(index) {
      vm.accordionSupersession[index] = !vm.accordionSupersession[index];
      $timeout(function () { });
    }

    function fetchAndUpdateMasterPartDetails(maxSupersessionPartNumber, index) {
      var manufacturerSubEntityId = vm.manufacturerSubEntityId;
      var onBehalfOfUserId = vm.onBehalfOfUserId;
      var isExactMatch = true;

      masterPartService.subEntityPartSearch(manufacturerSubEntityId, maxSupersessionPartNumber, null, onBehalfOfUserId, isExactMatch)
        .then((searchResponse) => {
          console.log('Received search response for supersession:', searchResponse);
          var supersessionParts = searchResponse.data.masterParts || [];
          console.log('Supersession parts:', supersessionParts);

          if (supersessionParts.length > 0) {
            vm.masterParts[index].supersessionDetails = supersessionParts;
          }
        })
        .catch((error) => {
          console.error('Error fetching master part details:', error);
        });
    }
  }
})();