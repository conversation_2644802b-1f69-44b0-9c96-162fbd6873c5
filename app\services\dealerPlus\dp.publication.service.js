(function () {
    'use strict';

    angular
        .module('app.services')
        .factory('dpPublicationService', dpPublicationService);

    dpPublicationService.$inject = ['$http', 'apiConstants'];

    function dpPublicationService($http, apiConstants) {
        var dealerPlusPrefix = "/dealerplus";
        return {
            getManual: getManual,
            getKits: getKits,
            getTechDocs: getTechDocs,
            getVideos: getVideos,
            getAllPublications: getAllPublications,
            getAssignedPublicationIdsForSubEntity: getAssignedPublicationIdsForSubEntity,
            assignPublicationsToPurchaser: assignPublicationsToPurchaser

        };

        function getManual(manualId) {
            return $http.get(apiConstants.url + dealerPlusPrefix + '/manual/' + manualId);
        }

        function getKits(manualId) {
            return $http.get(apiConstants.url + dealerPlusPrefix + '/manual/' + manualId + '/kits');
        }

        function getTechDocs(manualId) {
            return $http.get(apiConstants.url + dealerPlusPrefix + '/manual/' + manualId + '/techDocs');
        }

        function getVideos(manualId) {
            return $http.get(apiConstants.url + dealerPlusPrefix + '/manual/' + manualId + '/video');
        }

        function getAllPublications(manufacturerId) {
            return $http.get(apiConstants.url + dealerPlusPrefix + '/manufacturer/' + manufacturerId + '/manualDetails');
        }

        function getAssignedPublicationIdsForSubEntity(manufacturerSubEntityId) {
            return $http.get(apiConstants.url + dealerPlusPrefix + '/manufacturersubentity/' + manufacturerSubEntityId + '/assignedManualIds?published=false');
        }

        function assignPublicationsToPurchaser(dealerPlusId, purchaserId, publicationIds) {
            var url = apiConstants.url + '/purchasers/' + dealerPlusId + '/publications/assign-to-purchaser';
            var payload = {
                purchaserId: purchaserId,
                publications: publicationIds
            };
            return $http.post(url, payload);
        }

    }
})();
