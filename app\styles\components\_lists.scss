// =============================================================================
// Lists
// =============================================================================

.product-grid {
    @extend %clearfix;
    @extend %clearlist;
    
    display:inline-block;
    width  : 100%;

    li {
        float        : left;
        width        : calc(25% - 24px);
        overflow     : hidden;
        @include border-radius($border-radius);
        -webkit-box-shadow: 0 2px 6px rgba($black,0.2); 
        box-shadow: 0 2px 6px rgba($black,0.2); 
        margin-top: $spacing*2;
        margin-bottom: $spacing*2;

        &:not(:nth-child(4n)) {
            margin-right: $spacing*4;
        }

        &:hover {
          transform: translateY(-3px); 
            .overlay {
                visibility: visible;
                opacity   : 1;
            }
        }

        .img-wrap {
            @extend %bg-size;
            background-position: center center;
            background-repeat  : no-repeat;
            overflow           : hidden;
            position           : relative;
            height             : 220px;

            .view-all {
                @extend %absolute-center;
                z-index: 1;
            }
        }

        .product-info {
            padding   : $spacing*2;
            background: $white;
            color     : $textdark;

            hr{
            display: block;
            margin: 1em 0;
            padding: 0;
            height: 1px;
            border: 0;
            border-top: 1px solid $grey;
            width: calc(100% + 32px);
            margin-left: -16px;}

        h3 {
                margin-bottom: 0.5em;
                text-overflow: ellipsis;
                overflow     : hidden;
                white-space  : nowrap;
            }

            small {
                color: lighten($textdark,10%);
            }
            p {
                margin-top: $spacing*2;
                margin-bottom: 0;
                font-size: 0.8em;
                font-weight:700;
                a {
                  color: $blue;
                }
            }
        }
    }

    &.viewable-image {
        float: left;
        width: 25%;
    }
}

.model-grid {
    @extend %clearfix;
    @extend %clearlist;

    display:inline-block;
    width  : 100%;

    li {
        float        : left;
        min-width        : 49%;
        max-width: 250px;
        overflow     : hidden;
        @include border-radius($border-radius);
        -webkit-box-shadow: 0 2px 6px rgba($black,0.2);
        box-shadow: 0 2px 6px rgba($black,0.2);
        margin-top: $spacing;
        margin-bottom: $spacing;

        &:not(:nth-child(2n)) {
            margin-right: $spacing*2;
        }

        &:hover {
          transform: translateY(-3px);
            .overlay {
                visibility: visible;
                opacity   : 1;
            }
        }

        .img-wrap {
            @extend %bg-size;
            background-position: center center;
            background-repeat  : no-repeat;
            overflow           : hidden;
            position           : relative;
            height             : 140px;
            width: 140px;
            float:left;
            border-right: 1px solid rgba($black,0.2);

            .view-all {
                @extend %absolute-center;
                z-index: 1;
            }
        }

        .product-info {
            margin   : $spacing*2;
            background: $white;
            color     : $textdark;
            width: calc(100% - 176px);
            float: left;

            hr{
            display: block;
            margin: 1em 0;
            padding: 0;
            height: 1px;
            border: 0;
            border-top: 1px solid $grey;
            width: calc(100% + 32px);
            margin-left: -16px;}

        h3 {
                margin-bottom: 0.5em;
                text-overflow: ellipsis;
                overflow     : hidden;
                white-space  : nowrap;
            }

            small {
                color: lighten($textdark,10%);
            }
            p {
                margin-top: $spacing*2;
                margin-bottom: 0;
                font-size: 0.8em;
                font-weight:700;
                a {
                  color: $blue;
                }
            }
        }
    }

    &.viewable-image {
        float: left;
        width: 25%;
    }
}

.sub-tabs { margin-bottom:$spacing*4;
    @extend %clearlist;
    @extend %clearfix;
    ul {
        list-style: none;
        margin:0;
        padding:0;
        float: left;
        width: 100%;
        border-bottom:1px solid $divider-color;

       li { float: left;
           margin:0 $spacing*5 -1px 0;
           padding:0px;
           display: inline;

          a { float: left;
              padding:8px 0 4px 0;
              border-bottom:5px solid transparent;
              color:$textdark;
              cursor: pointer;
            &:hover {
                border-bottom-color:$divider-color;
            }
          }
          &.active {
            a {
                border-bottom-color:$blue;
            }
          }
       }
    }
}