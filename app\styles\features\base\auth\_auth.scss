// =============================================================================
// Auth
// =============================================================================
.default-theme {
  //background         : linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.7)), url(./././images/construction.jpeg);
  background: #e9ebee;
}

.cde-theme {
  background: radial-gradient(rgba(84, 183, 255, 1), #4696d1);
}

.dev1-theme {
  background: radial-gradient(rgba(84, 183, 255, 1), #4696d1);
}

.auth-section {
  margin: 100px;
}

.auth {

  // background-position: left center;
  //background-repeat  : no-repeat;
  //position           : absolute;
  //min-width          : $min-width;
  position: absolute;
  width: 100%;
  height: 100vh;
  top: 0;
  z-index: 11;
  background-size: cover;

  img:first-of-type {
    margin: 0 auto;
    display: block;
    max-height: 80px;
  }


  .store-img {

    img {
      display: inline-block;
      height: 40px;
    }
  }

  h3 {
    text-align: center;
    font-size: 20px;
    line-height: $spacing*2;
    text-transform: uppercase;
  }

  p {
    text-align: center;
  }

  .auth-panel {
    @extend %absolute-center;
    width: 90%;
    height: auto;
    color: $textdark;
    -webkit-box-shadow: 0 2px 6px rgba($black, 0.2);
    box-shadow: 0 2px 6px rgba($black, 0.4);
    @include border-radius(5px);

    @include breakpoint(medium) {
      width: 30%;
    }

    .message {
      margin-bottom: $spacing;
      text-align: center;
    }


    .btn,
    input:not([type="checkbox"]) {
      width: 100%;
      padding: $spacing*2 $spacing;
      font-size: 1em;
      @include border-radius(4px);
    }

    .btn {
      padding: $spacing;
    }

    .content {
      background: $white;
      padding: $spacing*2;

      &.setPassword {
        padding: $spacing*2;
      }
    ;

      &.head {
        border-bottom: 1px solid $divider-color;

      }

      .inputSpacing {
        margin-bottom: $spacing*2;
      }
    }

    .sub {
      background: $white;
      text-align: center;
      border-top: 1px solid $divider-color;
      padding: $spacing*2 0 $spacing*2 0;
    }

    .forgot,
    .remember {
      width: 100%;
      display: block;
    }

    .remember {
      text-align: right;

      * {
        display: inline-block;
      }

      input {
        width: auto;
      }
    }

    .forgot {
      padding-top: $spacing*2;
      text-align: center;
    }

  }

}


.google-logo {
  height: 60px !important;
  margin: 0 16px;
}

.qr-verification {
  height: 160px !important;
  margin: 16px auto;
}

.pl-50 {
  padding-left: 50px;
}

.lang-button {
  position: absolute;
  top: 0;
  right: 0;
}