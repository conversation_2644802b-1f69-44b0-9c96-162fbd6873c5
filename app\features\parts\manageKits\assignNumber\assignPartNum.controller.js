(function () {
    "use strict";

    angular.module("app.parts").controller("assignMPNumberController", assignMPNumberController);

    assignMPNumberController.$inject = ["masterPartService", "$uibModalInstance", "userService", "$stateParams" , "$state"];

    function assignMPNumberController(masterPartService, $uibModalInstance, userService, $stateParams, $state) {
        var vm = this;

        vm.create = create;
        vm.search = search;
        vm.submitDisabled = submitDisabled;
        vm.cancel = cancel;
        vm.searchPerformed = false;
        vm.partInAnotherKit = false;
        vm.isPartSearchMessageLoading = false;
        vm.origin = $stateParams.origin;
        vm.partExists = false;
        vm.searchQuery = "";
        vm.partNumber = "";
        vm.description = "";
        vm.language = null;
        vm.error = "";
        vm.actionType = 'existing';

        initialize();

        function initialize() {
            vm.isEditMode = !!$stateParams.kitId;
            vm.languages = userService.getUserLanguages();
            if (vm.languages && vm.languages.length > 0) {
                vm.language = vm.languages[0];
            }
        }

        $(function () {
            $('[data-toggle="tooltip"]').tooltip();
        });

        function create() {
            if (vm.actionType === 'new') {
                performCreation(vm.newPartNumber, vm.newDescription, vm.language.languageId);
            } else {
                if (!vm.searchPerformed) {
                    checkPartExistence(vm.partNumber).then(function (exists) {
                        vm.partExists = exists;
                        proceedWithAction();
                    });
                } else {
                    proceedWithAction();
                }
            }
        }

        function submitDisabled() {
            if (vm.actionType === 'new') {
                return !vm.newPartNumber || !vm.newDescription || !vm.language;
            } else {
                return !vm.partExists || vm.partInAnotherKit;
            }
        }

        function cancel() {
            if (vm.origin === 'masterPart') {
                window.history.back();
            } else {
                $state.go('manageKits');
            }
            $uibModalInstance.dismiss('cancel');
        }

        function cancel() {
            if (vm.origin === 'masterPart') {
                window.history.back();
            } else {
                $state.go('manageKits');
            }
            $uibModalInstance.dismiss('cancel');
        }

        function proceedWithAction() {
            if (vm.partExists) {
                // Part exists, close modal and pass existing information
                $uibModalInstance.close({
                    partNumber: vm.partNumber,
                    description: vm.description,
                    kitMasterPartId: vm.kitMasterPartId,
                    price: vm.price  // Ensure price is included if available
                });
            } else {
                // Part does not exist, create new
                performCreation();
            }
        }

        function performCreation(partNumber, description, languageId) {
            masterPartService
                .createMasterPart(
                    partNumber,
                    description,
                    languageId,
                    vm.price
                )
                .then(function (response) {
                    vm.kitMasterPartId = response.data;
                    $uibModalInstance.close({
                        partNumber: partNumber,
                        description: description,
                        kitMasterPartId: vm.kitMasterPartId,
                        price: vm.price,
                    });
                }, serviceFailure);
        }

        function serviceFailure(error) {
            if (error.status === 302) {
                vm.error = "Part Number already exists in the system.";
            } else {
                vm.error = "Something went wrong. try again later.";
            }
        }

        function search() {
            if (vm.searchQuery.trim() === "") {
                return;
            }

            vm.searchPerformed = true;
            vm.isPartSearchMessageLoading = true;
            vm.partExists = false; 
            vm.partInAnotherKit = false; 
            clearInputs();

            var manufacturerId = userService.getManufacturerId();

            masterPartService.manufacturerPartSearch(manufacturerId, null, vm.searchQuery).then(findPart, partSearchFailed);
        }

        function clearInputs() {
            vm.partNumber = "";
            vm.description = "";
            vm.error = "";
        }

        function findPart(response) {
            console.log("findPart response:", response);
            var foundPart = response.data.masterParts.find((part) => part.partNumber === vm.searchQuery);
            console.log("Found part:", foundPart);
            if (foundPart) {
                vm.partNumber = foundPart.partNumber;
                vm.description = foundPart.description;
                vm.kitMasterPartId = foundPart.masterPartId;
                vm.price = foundPart.price;
                vm.partExists = true;
                console.log("Part exists, proceeding to check if it exists in a kit.");
                checkIfPartExistsInAKit(foundPart.masterPartId);
            } else {
                vm.partNumber = vm.searchQuery;
                vm.partExists = false;
                vm.isPartSearchMessageLoading = false;
                console.log("Part does not exist.");
            }
        }

        function checkIfPartExistsInAKit(partNumber) {
            vm.isPartSearchMessageLoading = true;
            masterPartService.getMasterPartDetails(partNumber).then(function(response) {
                vm.partInAnotherKit = response.data.inKit;
                vm.isPartSearchMessageLoading = false;
            }, function(error) {
                console.log("Error checking if part is in a kit", error);
                vm.isPartSearchMessageLoading = false;
            });
        }

        function partSearchFailed(error) {
            console.log("partSearchFailed", error);
            vm.partNumber = vm.searchQuery;
            vm.partExists = false;
        }

        function checkPartExistence(partNumber) {
            masterPartService.manufacturerPartSearch(userService.getManufacturerId(), null, partNumber).then(
                function (response) {
                    var partExists = response.data.masterParts.some((part) => part.partNumber === partNumber);
                    vm.partExists = partExists;
                    if (partExists) {
                        checkIfPartExistsInAKit(partNumber);
                    } else {
                        vm.partInAnotherKit = false;
                    }
                },
                function (error) {
                    console.log("Error checking part existence", error);
                    return false;
                }
            );
        }
    }
})();
