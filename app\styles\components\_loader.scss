.loader {
  display:flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  position: fixed!important;
  z-index: 55;
  background: rgba(39, 41, 46, 0.8);
  width: 100%;
  height: 100%;
  top: 0;
  //background-image: url('images/cadpreloader-dark.gif');
  //background-repeat: no-repeat;
  //background-position: 50% 50%;
  //height: 60px;
  //width: 60px;
  //background-size: 60px 60px;

  p, i {
    color: #FFFFFF!important;
    font-size: 21px;
    text-align: center;
    margin-top: 16px;
  }

  p{
    &:first-of-type {
      font-size: 26px;
    }
  }

  i {
    margin-bottom: 16px;
    /*color: #FFFFFF;*/
    color: #3392FC!important;
    font-size: 54px!important;
  }
}

.modal-loader{
  width: 100%;
  height: 100%;
}

#loader-text{
  text-align: center;
}
#myProgress {
  position: relative;
  width: 100%;
  height: 30px;
  background-color: #ddd;
}

#myBar {
  position: absolute;
  height: 100%;
  background-color: #4CAF50;
}

.overlay-cover-tablet {
height: calc(100vh + 60px);
width: 100vw;
position: absolute;
overflow: hidden;
  background: rgba(39, 41, 46, 0.8);
  z-index: 11;
  width: 100%;
  position: fixed;
  top: -60px;
  left: 0;
}

// OLD
.preloader-bg{
  background-color: rgba(0,0,0,0.6);
  width: 100vw;
  height: 100vh;
  text-align: center;
  display:table-cell;
  vertical-align:middle;
  //position:absolute;
  //position:absolute;
  overflow: hidden;
  p {
    width: 100%;
    color: #FFFFFF!important;
    font-size: 21px;
    text-align: center;
    //margin-top: 16px;
  }
  a{
    color:#ffffff;
    -webkit-transition:color 0.5s ease-out;
    -moz-transition: color 0,5s ease-out;
    -o-transition: color 0.5s ease-out;
    transition: color 0.5s ease-out;
  }
}


//NEW
.download-preloader-bg{
    width: 100vw;
    height: 100vh;
    text-align: center;
    display: table-cell;
    vertical-align: middle;
    z-index: 56!important;
    position: absolute;

  p, i {
    width: 100%;
    color: #FFFFFF!important;
    font-size: 21px;
    text-align: center;
    margin-top: 16px;
  }

  p{

    &:first-of-type
    {
      font-weight: 700;
      font-size: 26px;
    }
  }

  .loader-info{
    font-weight: 700;
    font-size: 26px;
  }

  i {
    margin-bottom: 16px;
    /*color: #FFFFFF;*/
    color: #3392FC!important;
    font-size: 54px!important;
  }
}

.float-bottom{
  bottom:40px;
  position: absolute;
  width:100%;
}
