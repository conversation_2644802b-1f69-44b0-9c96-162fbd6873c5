(function () {
    'use strict';

    angular
        .module('app.support')
        .controller('SupportController', SupportController);

    SupportController.$inject = ['manufacturerPublicationService', '$state', 'userService', 'customerService', 'createMeetingService'];

    function SupportController (manufacturerPublicationService, $state, userService, customerService, createMeetingService) {
        var vm = this;
        vm.startNewMeeting = startNewMeeting;
        vm.cancel = cancel;
        vm.rangeChanged = rangeChanged;
        vm.machineChanged = machineChanged;
        vm.modelChanged = modelChanged;
        vm.fetchUsers = fetchUsers;
        vm.removeUser = removeUser;
        vm.message = "";
        vm.fullName = userService.getFullName();
        vm.isInternalUserDropdownDisabled = true;
        vm.isUserDropdownDisabled = true;
        vm.isStartMeetingDisabled = true;
        vm.invitedUsers = [];
        vm.userList = [];
        vm.selectedUsers = [];
        vm.selectedInternalUsers = [];
        vm.selectedModelId = null;
        vm.internalUsersSelectSettings = {
            checkBoxes: true,
            showCheckAll: false,
            showUncheckAll: false,
            buttonClasses: buttonStyling(),
            styleActive: true
        };

        vm.usersSelectSettings = {
            checkBoxes: true,
            showCheckAll: false,
            showUncheckAll: false,
            buttonClasses: buttonStyling(),
            styleActive: true
        };

        vm.internalUsersInvitedListEventListeners = {
            onItemSelect: addInternalUsersToInvitedList,
            onItemDeselect: removeUser
        };

        vm.usersInvitedListEventListeners = {
            onItemSelect: addUsersToInvitedList,
            onItemDeselect: removeUser
        }

        function addInternalUsersToInvitedList(selectedUser){
            let name = "";
            vm.internalUserList.forEach( user => {
                if(selectedUser.id === user.id) name = user.label;
            });
            vm.invitedUsers.push({id: selectedUser.id,
                name: name,
                companyName: "Internal"
            })
            enableStartMeetingButton();
        }

        function enableStartMeetingButton(){
            if(vm.invitedUsers.length !== 0 && vm.selectedModelId !== null)
                vm.isStartMeetingDisabled = false;
            else 
                vm.isStartMeetingDisabled = true;
        }

        function addUsersToInvitedList(selectedUser){
            let name = "";
            let companyName = "";
            vm.userList.forEach( user => {
                if(selectedUser.id === user.id){ 
                    name = user.label;
                    companyName = user.companyName;
                }
            });
            vm.invitedUsers.push({id: selectedUser.id,
                name: name,
                companyName: companyName 
            })
            enableStartMeetingButton();
        }

        function buttonStyling() {
            return "btn btn-default multiCheckbox";
        }

        initialize();

        function initialize() {
            getRange();
            fetchCustomerService();
            fetchInternalUsers();
        }

        function getRange() {
            manufacturerPublicationService.getRangeByManufacturer()
                .then(getRangeSuccess, getRangeFailure);
        }

        function getRangeSuccess(response) {
            vm.rangeValues = response.data;
        }

        function getRangeFailure(err) {
            console.log(err);
        }

        function rangeChanged(rangeId) {
            if (rangeId)
                vm.rangeId = rangeId;
            getMachineByRange(vm.rangeId);
        }

        function getMachineByRange(rangeId) {
            manufacturerPublicationService.getMachineByRange(rangeId)
                .then(machineRangeSuccess, machineRangeFailure);
        }

        function machineRangeSuccess(response) {
            vm.machines = response.data;
        }

        function machineRangeFailure(err) {
            console.log(err);
        }

        function machineChanged(machineId) {
            if (machineId)
                vm.machineId = machineId;
            getModelByMachine(vm.machineId);
        }

        function getModelByMachine(machineId) {
            manufacturerPublicationService.getModelByMachine(machineId)
                .then(modelMachineSuccess, modelMachineFailure);
        }

        function modelMachineSuccess(response){
            vm.models = response.data;
        }

        function modelMachineFailure(error){
            console.log(error);
        }

        function modelChanged(modelId){
            if (modelId){
                vm.modelId = modelId;
                enableStartMeetingButton();
            }
        }

        function fetchCustomerService() {
            customerService.getCustomers()
                .then(getCustomersSuccess, getCustomersFailed)
        }

        function getCustomersSuccess(response) {
            vm.customerList = [];
            vm.totalItems = vm.customerList.length;
            vm.selectedCustomers = [];

            for (var i = 0; i < response.data.length; i++) {
                var customerData = {customer_id: response.data[i].manufacturerSubEntityId, label: response.data[i].name};
                vm.customerList.push(customerData);
            }
        }

        function getCustomersFailed(error) {
            vm.areCustomersLoaded = false;
            headerBannerService.setNotification('ERROR', error.data.error, 10000);
        }

        function fetchUsers(subEntityId) {
            vm.selectedUsers.forEach( (user) => { 
                vm.removeUser(user);
            });
            vm.userList = [];
            vm.selectedUsers = [];
            userService.getCustomerUsers(subEntityId)
                .then(getCustomerUsersSuccess, getCustomerUsersFailed)
        }
        
        function getCustomerUsersSuccess(response) {
            vm.userList = [];
            vm.selectedUsers = [];

            for (var i = 0; i < response.data.length; i++) {
                let companyName = "";
                vm.customerList.forEach(customer => {
                    if(customer.customer_id === vm.selctedCustomerId)
                        companyName = customer.label;
                });
                var userData = {id: response.data[i].userId, label: response.data[i].firstName + " " + 
                    response.data[i].lastName, companyId: vm.selctedCustomerId,  companyName : companyName};
                vm.userList.push(userData);
            }
            vm.isUserDropdownDisabled = false;
        }

        function getCustomerUsersFailed(error) {
            console.log("Error in fetch Users:", error);
        }
        
        function fetchInternalUsers(){
            userService.getInternalUsers()
            .then(getInternalUsersSuccess, getInternalUsersFailed)
            vm.intenalUserList = [];
        }

        function getInternalUsersSuccess(response){
            vm.internalUserList = [];
            vm.selectedInternalUsers = [];

            for (var i = 0; i < response.data.length; i++) {
                var userData = {id: response.data[i].userId, label: response.data[i].firstName + " " + 
                    response.data[i].lastName };
                vm.internalUserList.push(userData);
            }
            vm.isInternalUserDropdownDisabled = false;
        }

        function getInternalUsersFailed(error){
            console.log("Error in fetch Internal Users:", error);
        }

        function removeUser(user){
            var i = vm.invitedUsers.length;
            while(i--){
                if( vm.invitedUsers[i] && vm.invitedUsers[i].hasOwnProperty("id") 
                    && (vm.invitedUsers[i].id === user.id ) ){ 
                        vm.invitedUsers.splice(i,1);
                }
                if(vm.selectedUsers[i] && vm.selectedUsers[i].hasOwnProperty("id") 
                && (vm.selectedUsers[i].id === user.id ) ){ 
                    vm.selectedUsers.splice(i,1);
                }
                if(vm.selectedInternalUsers[i] && vm.selectedInternalUsers[i].hasOwnProperty("id") 
                && (vm.selectedInternalUsers[i].id === user.id ) ){ 
                    vm.selectedInternalUsers.splice(i,1);
                }
            }

            enableStartMeetingButton();
        }

        function startNewMeeting(){   
            var customerUserId = [];
            var manufacturerUserId = [];

            vm.meetingInfo = {
                modelId : vm.selectedModelId,
                machineId: vm.selectedMachineId
            };

            vm.models.forEach( model => {
                if(model.modelId === vm.selectedModelId)    {   
                    vm.meetingInfo.modelName = model.modelName;
                    vm.meetingInfo.autodeskUrn = model.autodeskUrn;
                }
            });

            vm.machines.forEach( machine => {
                if(machine.machineId === vm.selectedMachineId)
                    vm.meetingInfo.machineName = machine.name;
            });
            
            vm.selectedUsers.forEach( user => {
                customerUserId.push(user.id)
            });

            vm.selectedInternalUsers.forEach( user => {
                manufacturerUserId.push(user.id)
            });

            createMeetingService.sendEmail(vm.selectedModelId, customerUserId, manufacturerUserId)
            .then(startMeetingSuccess, startMeetingFailed);
        }

        function connectMeetingServer(guid){
            $state.go("manufacturerViewer", {
                productId: vm.meetingInfo.machineId,
                autodeskURN: vm.meetingInfo.autodeskUrn,
                modelId: vm.meetingInfo.modelId,
                machineName: vm.meetingInfo.machineName,
                viewableName: vm.meetingInfo.modelName,
                guid: guid
            });
        }

        function startMeetingSuccess(response){
            var url = response.data;
            var guid = GetURLParameter(url, "guid");
            createMeetingService.setInvitedUsers({invitedUsers: vm.invitedUsers, host: vm.fullName, roomId:guid });
            connectMeetingServer(guid);
            console.log("meeting api called");
        }

        function startMeetingFailed(error){
            console.log("Error in calling meeting api : ", error);
        }

        function GetURLParameter (url, parameter) {
            var search;
            var parsed;
            var count;
            var loop;
            var searchPhrase;
            search = url.indexOf("?");
            if (search < 0) {
                return "";
            }
            searchPhrase = parameter + "=";
            parsed = url.substr(search+1).split("&");
            count = parsed.length;
            for(loop=0;loop<count;loop++) {
                if (parsed[loop].substr(0,searchPhrase.length)==searchPhrase) {
                    return decodeURI(parsed[loop].substr(searchPhrase.length));
                }
            }
            return "";
        }

        function cancel(){
            vm.invitedUsers = [];
            vm.userList = [];
            vm.models = [];
            vm.machines = [];
            vm.selectedInternalUsers = [];
            vm.selectedUsers = [];
            vm.selectedRangeId = null;
            vm.selectedMachineId = null;
            vm.selectedModelId = null;
            vm.selctedCustomerId = null;
        }
    }
})();
