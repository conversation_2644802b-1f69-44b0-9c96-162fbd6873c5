(function () {
  "use strict";

  angular
    .module("app.viewer")
    .controller("CustomerKitController", CustomerKitController);

  CustomerKitController.$inject = [
    "publicationService",
    "basketService",
    "$stateParams",
    "$timeout",
    "$translate",
    "userService",
  ];

  function CustomerKitController(
    publicationService,
    basketService,
    $stateParams,
    $timeout,
    $translate,
    userService
  ) {
    var vm = this;

    var manualId = $stateParams.manualId;
    var WENT_WRONG;
    vm.kitList = [];
    vm.initialized = false;
    vm.handleAddKitClick = handleAddKitClick;
    vm.isStockWarehousesEnabled = userService.getStockWarehousesEnabled();

    vm.addKitToBasket = addKitToBasket;

    $translate(["GENERAL.WENT_WRONG"]).then(function (resp) {
      WENT_WRONG = resp["GENERAL.WENT_WRONG"];
    });

    initialize();

    function initialize() {
      vm.hasOrderRole = userService.hasOrderRole();
      publicationService.getKits(manualId).then(getKitsSuccess, serviceFailed);
    }

    function getKitsSuccess(response) {
      vm.kitList = response.data;
      vm.initialized = true;
    }

    function serviceFailed(error) {
      headerBannerService.setNotification("ERROR", WENT_WRONG, 10000);
      console.error(error.data);
      vm.initialized = true;
    }

    function handleAddKitClick(event, kit) {
      event.preventDefault();
      event.stopPropagation();
      vm.addKitToBasket(kit);
    }

    function addKitToBasket(kit) {
      kit.clicked = true;
      var kitItem = {
        masterPartNumber: kit.masterPartNumber,
        kitPrice: kit.kitPrice,
        kitId: kit.id,
        description: kit.masterPartDescription,
        modelId: kit.modelId ? kit.modelId.toString() : null,
        quantity: 1,
        parts: kit.parts,
        masterPartKitId: kit.masterPartKitId,
        stock: kit.stock,
        machineName: kit.machineName,
        modelName: kit.modelName,
      };
      basketService.addKit(kitItem);
      $timeout(function () {
        kit.clicked = false;
      }, 500);
    }

    // function addKitToBasket(kit) {
    //     kit.clicked = true;
    //     for (var i = 0; i < kit.parts.length; i++) {
    //         var kit = {
    //             masterPartNumber: kit.masterPartNumber,
    //             kitPrice: kit.price,
    //             partNumber: kit.parts[i].partNumber,
    //             partId: kit.parts[i].partId,
    //             masterPartId: kit.parts[i].masterPartId,
    //             quantity: kit.parts[i].quantity,
    //             partDescription: kit.parts[i].description ? kit.parts[i].description : kit.parts[i].partDescription,
    //             modelId: kit.modelId ? kit.modelId.toString() : null,
    //             price: kit.parts[i].price,
    //             stock: kit.parts[i].stock
    //         };
    //         basketService.addKit(kitItem);
    //     }
    //     $timeout(function () {
    //         kit.clicked = false;
    //     }, 500);

    // }
  }
})();
