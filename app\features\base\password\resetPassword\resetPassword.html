<section class="d-flex justify-content-center auth-section" ng-if="resetCtrl.browserIE">
    <div class="auth-box">
        <img alt="CadShareLogo" class="width-90 center-align" ng-src="{{appCtrl.logoUrl}}">
    </div>
</section>

<div class="auth {{appCtrl.subdomain}}-theme flex-center">
    <div class="error-well" ng-if="resetCtrl.browserIE">
        <h2 translate>LOGIN.UNSUPPORTED_BROWSER</h2>
        <p translate>LOGIN.UNSUPPORTED_BROWSER_TEXT</p>
        <p translate>LOGIN.UNSUPPORTED_BROWSER_RECOMMENDATION</p>
    </div>

    <div ng-hide="resetCtrl.browserIE">

            <form name="resetCtrl.form" class="form" ng-if="!resetCtrl.showSuccessMessage">

                <div class="sign-panels">

                    <img  ng-src="{{appCtrl.logoUrl}}">
                    <h3 class="mt-8">{{resetCtrl.passwordAction}} {{'LOGIN.PASSWORD' | translate}}</h3>

                    <div class="error-well"
                         ng-if="!resetCtrl.showSuccessMessage" ng-show="resetCtrl.form.newPassword.$error.pattern" translate>
                        PASSWORD.NUM_AND_LETTER
                    </div>

                    <div class="error-well"
                         ng-if="!resetCtrl.showSuccessMessage" ng-show="resetCtrl.form.newPassword.$error.minlength" translate>
                        PASSWORD.EIGHT_CHARS
                    </div>
                    <div class="error-well"
                         ng-if="!resetCtrl.showSuccessMessage" ng-show="resetCtrl.form.newPassword.$error.maxlength" translate>
                        PASSWORD.TWENTY_FIVE_CHARS
                    </div>

                    <div class="error-well"
                         ng-if="resetCtrl.errorMessage !== ''" ng-bind-html="resetCtrl.errorMessage"></div>

                    <div class="error-well" ng-hide="resetCtrl.arePasswordsValid()" translate>
                        PASSWORD.DONT_MATCH
                    </div>

                    <input class="inputSpacing" name="newPassword" type="password" ng-model="resetCtrl.newPassword"
                           ng-maxlength="25"
                           ng-minlength="8" ng-pattern="/^((?=.*[a-z])(?=.*[A-Z])(?=.*[0-9]))(?=.{8,})/"
                           placeholder="{{'PASSWORD.NEW_PASS' | translate}}">

                    <input class="inputSpacing" name="confirmPassword" type="password" ng-model="resetCtrl.confirmPassword"
                           ng-maxlength="25"
                           ng-minlength="8" ng-pattern="/^((?=.*[a-z])(?=.*[A-Z])(?=.*[0-9]))(?=.{8,})/"
                           placeholder="{{'PASSWORD.NEW_PASS_AGAIN' | translate}}">

                    <button class="btn primary" ng-click="resetCtrl.resetPassword()">
                        {{resetCtrl.passwordAction}}
                    </button>

                </div>

            </form>

        <p class="message success-alert" role="alert" ng-if="resetCtrl.showSuccessMessage">{{'PASSWORD.SUCCESS_CREATED' | translate}} <a class="light" ui-sref="login">{{'PASSWORD.CLICK_HERE' | translate}}</a> {{'PASSWORD.RETURN_LOGIN' | translate}}</p>

    </div>
</div>
