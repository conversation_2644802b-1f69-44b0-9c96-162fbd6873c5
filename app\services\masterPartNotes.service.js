(function () {
    "use strict";

    angular.module("app.services").factory("mpNotesService", mpNotesService);

    mpNotesService.$inject = ["$http", "apiConstants"];

    function mpNotesService($http, apiConstants) {
        return {
            getMasterPartNote: getMasterPartNote,
            createMasterPartNote: createMasterPartNote,
            updateMasterPartNote: updateMasterPartNote,
            deleteMasterPartNote: deleteMasterPartNote,
        };

        function getMasterPartNote(manufacturerId, noteId) {
            return $http.get(apiConstants.url + "/manufacturer/" + manufacturerId + "/masterpartnote/id/" + noteId);
        }

        function createMasterPartNote(manufacturerId, partNote) {
            return $http.post(apiConstants.url + "/create/masterpartnote/" + manufacturerId, {
                partNote: partNote,
            });
        }

        function updateMasterPartNote(manufacturerId, noteId, partNote) {
            return $http.put(apiConstants.url + "/manufacturer/" + manufacturerId + "/update/masterpartnote/" + noteId, {
                partNote: partNote,
            });
        }

        function deleteMasterPartNote(manufacturerId, noteId) {
            return $http.delete(apiConstants.url + "/manufacturer/" + manufacturerId + "/delete/partnote/" + noteId);
        }
    }
})();
