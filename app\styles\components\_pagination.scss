// =============================================================================
// Pagination
// =============================================================================

.pagination {
    @extend %clearlist;
    width     : 100%;
    text-align: center;
    margin: 24px 0 8px 0;
    display: block;

    li {
        display       : inline-block;
        vertical-align: bottom;
        position      : relative;
        margin-right  : $spacing;

        &:last-child {
            margin-right: 0;

            &:after {
                display: none;
            }
        }

        a {
            display: block;
            padding: 0!important;
            text-align: center;
            @include border-radius(5px);
            border: 1px solid #D2DAE5;
            line-height: 25px;
            font-size: 1.6em;
            /* padding-bottom: 4px; */
            background: #F2F6F9;
            height: 32px;
            WIDTH: 32px;

            &:active,
            &:link,
            &:visited {
                color: $textdark2;
            }

            &:hover {
                background: darken($lightback,15%);
            }
        }

        &.pagination-page {
            a {
                font-size: 1em;
                padding-bottom: 0;
                line-height: 30px;
                font-weight: 700;
            }
        }

        &.active {
            a {
                background: darken($lightback,15%);
            }
        }
    }
}
