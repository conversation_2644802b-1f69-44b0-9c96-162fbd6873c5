// =============================================================================
// Base
// =============================================================================

* {
    box-sizing            : border-box!important;
    -moz-box-sizing       : border-box!important;
    -webkit-font-smoothing: antialiased;
}

html {
    width      : 100%;
    //min-width  : $min-width;
}

body {
    color      : $textdark;
    background : $lightback;
    font-family: $body-font;
    font-size  : $base-font-size;
    line-height: 1.3;
}

::-moz-selection {
}

::selection {
}

hr {
    display   : block;
    margin    : 1em 0;
    padding   : 0;
    height    : 1px;
    border    : 0;
    border-top: 1px solid $textdark;
}

audio,
canvas,
iframe,
img,
svg,
video {
    max-width     : 100%;
    vertical-align: middle;
}

a{

    cursor:pointer;

}

fieldset {
    margin : 0;
    padding: 0;
    border : 0;
}

textarea {
    resize: vertical;
    width:100%;
}

.l {
    float: left;
}

.r {
    float: right!important;
}

.ra {
    text-align: right;
}

.ca {
    text-align: center;
}

.embed-container {
    position      : relative;
    padding-bottom: 56.25%;
    height        : 0;
    overflow      : hidden;
    max-width     : 100%;

    embed,
    iframe,
    object {
        position: absolute;
        top     : 0;
        left    : 0;
        width   : 100%;
        height  : 100%;
    }
}

.btn.primary{

    cursor:pointer;

}

.flex-center{

    display:flex;
    justify-content: center;
    align-items:center;
    flex-wrap: wrap;

}

.flex{

    display:flex;
    align-items:center;
    flex-wrap: wrap;

}

.grid{

    display:grid;

}