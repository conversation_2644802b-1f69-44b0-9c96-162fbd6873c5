(function () {
    "use strict";

    angular.module("app.services").factory("LockStateService", LockStateService);

    LockStateService.$inject = ["$rootScope"];

    function LockStateService($rootScope) {
        var service = {
            checkLockState: checkLockState,
            isAnyNodeLocked: isAnyNodeLocked,
        };

        function checkLockState(modelTree) {
            var updatedTree = modelTree[0];
            var anyLocked = isAnyNodeLocked(updatedTree);
            $rootScope.$broadcast("bulk-lock-state-updated", anyLocked);
        }

        function isAnyNodeLocked(node) {
            if (node.isLocked) {
                return true;
            }
            if (node.childParts && node.childParts.length > 0) {
                for (let child of node.childParts) {
                    if (isAnyNodeLocked(child)) {
                        return true;
                    }
                }
            }

            return false;
        }

        return service;
    }
})();
