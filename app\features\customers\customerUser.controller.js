(function () {
    'use strict';

    angular
        .module('app.customer')
        .controller('CustomerUserController', CustomerUserController);

    CustomerUserController.$inject = ['$uibModal', '$state', '$stateParams', 'headerBannerService', 'userService', 'apiConstants', '$translate'];

    function CustomerUserController($uibModal, $state, $stateParams, headerBannerService, userService, apiConstants, $translate) {

        var vm = this;

        vm.isCDEUser = false;
        vm.isFarmerUser = false;
        vm.sortReverse = false;
        vm.endRecord = vm.itemPerPage;
        vm.areUsersLoaded = false;
        vm.successMessage = "";
        vm.filter_range = "";
        vm.ranges = [];
        vm.subEntityname = $stateParams.name;
        vm.sortReverse = true;
        vm.customer_sort = 'userId';
        vm.isSupreme = userService.isSupreme();

        vm.loadingInfiniteScrollData = false;
        vm.showBackToTopButton = false;
        vm.isFixedHeader = false;

        var SURNAME, EMAIL, USER_, SEND_RESET, WILL_BE_EMAILED, BY_CONFIRMING;
        $translate(['CUST_USER.SURNAME', 'CUST_USER.EMAIL', 'CUST_USER.USER_', 'CUST_USER.SEND_RESET', 'CUST_USER.WILL_BE_EMAILED', 'CUST_USER.BY_CONFIRMING'])
            .then(function (resp) {
                SURNAME = resp["CUST_USER.SURNAME"];
                EMAIL = resp["CUST_USER.EMAIL"];
                USER_ = resp["CUST_USER.USER_"];
                SEND_RESET = resp["CUST_USER.SEND_RESET"];
                WILL_BE_EMAILED = resp["CUST_USER.WILL_BE_EMAILED"];
                BY_CONFIRMING = resp["CUST_USER.BY_CONFIRMING"];

                vm.sortBy = [
                    {name: 'lastname', value: SURNAME},
                    {name: 'emailaddress', value: EMAIL}
                ];
            });

        vm.deleteUser = deleteUser;
        vm.resetPassword = resetPassword;
        vm.editUser = editUser;
        vm.createUser = createUser;
        vm.createOrder = createOrder;
        vm.goToCustomers = goToCustomers;
        vm.scrollToTop = scrollToTop;
        vm.loadMoreInfiniteScroll = loadMoreInfiniteScroll;

        var selectedUser = {};

        initialize();

        function initialize() {
            isUserCDE();
            isUserFarmer();
            fetchUsers();
        }

        function isUserCDE() {
            var cdeId = 0
            if (apiConstants.cdeId)
            {
                cdeId = apiConstants.cdeId;
            }
            vm.isCDEUser = userService.getManufacturerId() === cdeId;
        }

        function isUserFarmer() {
            vm.isFarmerUser = ($stateParams.type === "CUSTOMER" && vm.isSupreme)
        }

        function fetchUsers() {
            vm.loadingInfiniteScrollData = true;
            userService.getCustomerUsers($stateParams.subEntityId)
                .then(getCustomerUsersSuccess, getCustomerUsersFailed)
        }

        function toPascalCase(str) {
            return str
            .toLowerCase()
            .match(/[A-Z]{2,}(?=[A-Z][a-z]+[0-9]*|\b)|[A-Z]?[a-z]+[0-9]*|[A-Z]|[0-9]+/g)
            .map(x => x.charAt(0).toUpperCase() + x.slice(1).toLowerCase())
            .join(' ');
        }

        function getCustomerUsersSuccess(response) {
          vm.allUsers = response.data;
            vm.totalItems = vm.allUsers.length;
             vm.userList = vm.allUsers.slice(0, 100);
            vm.loadingInfiniteScrollData = false;
            vm.userList.forEach(element => {
                element.userStatus = toPascalCase(element.userStatus);
            });
            vm.areUsersLoaded = true;
            handleInfiniteScroll();
        }

        function getCustomerUsersFailed(error) {
            vm.areUsersLoaded = false;
            headerBannerService.setNotification('ERROR', error.data.error, 10000);
        }

        function deleteUser(user) {
            vm.successMessage = "";
            var deleteObject = {
                name: USER_ + user.firstName + " " + user.lastName,
                id: user.userId,
                url: '/user/' + user.userId + '/customer'
            };

            $uibModal.open({
                templateUrl: 'features/shared/commonDelete/deleteDialogBox.html',
                controller: 'DeleteController',
                controllerAs: 'deleteCtrl',
                size: 'sm',
                resolve: {
                    deleteObject: function () {
                        return deleteObject;
                    }
                }
            }).result.then(function () {
                fetchUsers();
            }, function () {
                console.log('Modal Cancelled');
            });
        }

        function resetPassword(user) {
            selectedUser = user;
            var confirmObject = {
                titleText: SEND_RESET,
                bodyText: BY_CONFIRMING + " \"" + user.firstName + " " + user.lastName + "\" " + WILL_BE_EMAILED
            };
            $uibModal.open({
                templateUrl: 'features/shared/confirmationModal/confirmModal.html',
                controller: 'ConfirmModalController',
                controllerAs: 'confirmModalCtrl',
                size: 'sm',
                resolve: {
                    confirmObject: function () {
                        return confirmObject;
                    }
                }
            }).result
                .then(sendPasswordEmailConfirmed, doNothing);
        }

        function sendPasswordEmailConfirmed() {
            userService.sendResetPassword(selectedUser.userId);
        }

        function doNothing() {
            //do nothing
        }

        function editUser(user) {
            var createObject = {
                type: $stateParams.type,
                permissionsArray: user.userPermissions,
                firstName: user.firstName,
                lastName: user.lastName,
                emailAddress: user.emailAddress,
                userId: user.userId,
                active: user.active,
                userStatus: user.userStatus,
                manufactuerSubEntityId: $stateParams.subEntityId,
                visContactId: user.visContactId
            };
            $uibModal.open({
                templateUrl: 'features/shared/editUserModal/editUser.html',
                controller: 'EditUserController',
                controllerAs: 'editUserCtrl',
                size: 'md',
                backdrop: 'static',
                resolve: {
                    createObject: function () {
                        return createObject;
                    }
                }
            })
                .result.then(function () {
                $state.reload()
            }, function () {
                console.log('Modal Cancelled');
            });
        }

        function createUser() {
            var createObject = {type: $stateParams.type, manufactuerSubEntityId: $stateParams.subEntityId};

            $uibModal.open({
                templateUrl: 'features/shared/createUserModal/createUser.html',
                controller: 'CreateUserController',
                controllerAs: 'createUserCtrl',
                size: 'md',
                backdrop: 'static',
                resolve: {
                    createObject: function () {
                        return createObject;
                    }
                }
            })
                .result.then(function () {
                $state.reload()
            }, function () {
                console.log('Modal Cancelled');
            });
        }

        var ON_BEHALF_OF_USER;
        function createOrder(user) {
            ON_BEHALF_OF_USER = user;
            userService.getOnBehalfOfUser(user.userId).then(response => ON_BEHALF_OF_USER = response.data)
            userService.getCurrencyForUser(user.userId)
                .then(getUserCurrencySuccess);
        }


        function getUserCurrencySuccess(response) {
            ON_BEHALF_OF_USER.manufacturerSubEntityId = $stateParams.subEntityId;
            ON_BEHALF_OF_USER.currency = response.data;
            $state.go("publishedProductsOnBehalfOf", {
                onBehalfOf: btoa(encodeURIComponent(JSON.stringify(ON_BEHALF_OF_USER))),
            });
        }

        function goToCustomers() {
            $state.go('customers');
        }

       var lastScrollTop = 0;
window.addEventListener('scroll', handleInfiniteScroll);

function handleInfiniteScroll() {
    var threshold = 250;
    var scrollTop = window.scrollY;

    if (scrollTop > lastScrollTop) {
        vm.isFixedHeader = scrollTop > threshold;
    } else if (scrollTop < threshold){
        vm.isFixedHeader = false;
    }
    lastScrollTop = scrollTop;  

    
    if (window.innerHeight + scrollTop >= document.body.offsetHeight - 2 && !vm.loadingInfiniteScrollData && !vm.initialLoadDone) {
        loadMoreInfiniteScroll();
    }
}

        function loadMoreInfiniteScroll() {
            vm.loadingInfiniteScrollData = true;

            var nextItems = vm.allUsers.slice(vm.userList.length, vm.userList.length + 100);
            if (nextItems.length === 0) {
                vm.initialLoadDone = true;
                vm.loadingInfiniteScrollData = false;
                return;
            }

            vm.userList = vm.userList.concat(nextItems);

            vm.loadingInfiniteScrollData = false;
            if (vm.userList.length >= vm.totalItems) {
                vm.initialLoadDone = true;
            }
        }

  function scrollToTop() {
      $window.scrollTo({ top: 0, behavior: "smooth" });
      $("html, body").animate({ scrollTop: 0 }, "slow", function () {
        $("#scrollToTop").removeClass("scrolled-past");
      });
    }

    }
})();
