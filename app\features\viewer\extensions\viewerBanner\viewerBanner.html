<div ng-controller="ViewerBannerController as viewerBannerCtrl" ng-show="viewerBannerCtrl.notificationDisplay"
     class="viewerBanner">
    <div class="header-banner container-fluid {{viewerBannerCtrl.level}}"
    ng-style="viewerBannerCtrl.isSideMenuOpen && { width: 'calc(100% - 420px)', right : 'auto' }">

        {{viewerBannerCtrl.errorMsg}}

        <div class="d-inline">
        <button class="btn secondary" ng-show="viewerBannerCtrl.isAssembly"
                ng-click="viewerBannerCtrl.selectAssembly()" translate>
            VIEW_BANNER.SELECT_ASSEMBLY
        </button>

        <button class="btn secondary" ng-show="viewerBannerCtrl.hasKit" ng-click="viewerBannerCtrl.viewKit()" translate>
            VIEW_BANNER.VIEW_KIT
        </button>
        </div>

        <i class="fas fa-times" style="margin: 0.3em;cursor:pointer;position: absolute;top: 0;right: 0;" aria-hidden="true"
           ng-click="viewerBannerCtrl.closeNotification()"></i>

    </div>
</div>
</div>