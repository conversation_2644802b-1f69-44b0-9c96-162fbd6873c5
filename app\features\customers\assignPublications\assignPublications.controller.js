(function () {
    'use strict';

    angular
        .module('app.customer')
        .controller('AssignPublicationsController', AssignPublicationsController);

    AssignPublicationsController.$inject = ['publicationService', 'userService', '$stateParams', 'headerBannerService', '$translate', 'filterFilter'];

    function AssignPublicationsController(publicationService, userService, $stateParams, headerBannerService, $translate, filterFilter) {

        var vm = this;

        vm.availablePublications = [];
        vm.assignedPublications = [];
        vm.isPageEdited = false;

        var CHANGES_SAVED, CHANGES_CANCELED;
        $translate(['ASSIGN_PUBLICATIONS.CHANGES_SAVED', 'ASSIGN_PUBLICATIONS.CHANGES_CANCELED'])
            .then(function (resp) {
                CHANGES_SAVED = resp["ASSIGN_PUBLICATIONS.CHANGES_SAVED"];
                CHANGES_CANCELED = resp["ASSIGN_PUBLICATIONS.CHANGES_CANCELED"];
            });


        vm.moveSelectedFromAvailableToAssigned = moveSelectedFromAvailableToAssigned;
        vm.moveSelectedFromAssignedToAvailable = moveSelectedFromAssignedToAvailable;
        vm.toggleSelectAllAvailable = toggleSelectAllAvailable;
        vm.toggleSelectAllAssigned = toggleSelectAllAssigned;
        vm.updateAllAvailableCheckbox = updateAllAvailableCheckbox;
        vm.updateAllAssignedCheckbox = updateAllAssignedCheckbox;
        vm.clearAssignedSearch = clearAssignedSearch;
        vm.clearAvailableSearch = clearAvailableSearch;
        vm.save = save;
        vm.cancel = cancel;

        initialize();

        function initialize() {
            vm.availablePublications = [];
            vm.assignedPublications = [];
            vm.areAllAssignedSelected = false;
            vm.areAllAvailableSelected = false;
            getAllPublications();
            vm.customerName = $stateParams.name;
        }

        function getAllPublications() {
            publicationService.fetchPublications()
                .then(getAllPublicationsSuccess);
        }

        function getAllPublicationsSuccess(response) {
            vm.availablePublications = response.data.publications;
            getAssignedPublicationIdsForSubEntity();
        }

        function getAssignedPublicationIdsForSubEntity() {
            var subEntityId = $stateParams.subEntityId;
            publicationService.getAssignedPublicationIdsForSubEntity(subEntityId)
                .then(getAssignedPublicationIdsForSubEntitySuccess);
        }

        function getAssignedPublicationIdsForSubEntitySuccess(response) {
            var publicationIds = response.data;

            for (var i = 0; i < publicationIds.length; i++) {
                var publicationId = publicationIds[i];
                moveBetweenListsById( publicationId, vm.availablePublications, vm.assignedPublications);
            }
        }

        function getSelectedPublicationIdsFromList(list, filter) {
            var selectedPublicationIds = [];
            var filterList = filterFilter(list, filter);
            for (var i = 0; i < filterList.length; i++) {
                if (filterList[i].selected === true) {
                    selectedPublicationIds.push(filterList[i].id);
                }
            }
            return selectedPublicationIds;
        }

        function toggleSelectAllAvailable() {
            if(vm.areAllAvailableSelected){
                deselectAll(vm.availablePublications)
            }else{
                selectAll(vm.availablePublications)
            }
        }

        function toggleSelectAllAssigned() {
            if(vm.areAllAssignedSelected){
                deselectAll(vm.assignedPublications)
            }else{
                selectAll(vm.assignedPublications)
            }
        }

        function selectAll(list) {
            for (var i = 0; i < list.length; i++) {
                list[i].selected = true;
            }
        }
        function deselectAll(list) {
            for (var i = 0; i < list.length; i++) {
                list[i].selected = false;
            }
        }

        function updateAllAvailableCheckbox(){
            for (var i = 0; i < vm.availablePublications.length; i++) {
                if(vm.availablePublications[i].selected !== true){
                    vm.areAllAvailableSelected = false;
                    return;
                }
            }
            vm.areAllAvailableSelected = true;
        }

        function updateAllAssignedCheckbox(){
            for (var i = 0; i < vm.assignedPublications.length; i++) {
                if(vm.assignedPublications[i].selected !== true){
                    vm.areAllAssignedSelected = false;
                    return;
                }
            }
            vm.areAllAssignedSelected = true;
        }

        function getIndexOfPublicationInList(publicationId, list) {
            var myPublication = _.findWhere(list, {id: publicationId});
            return list.indexOf(myPublication);
        }

        function moveSelectedFromAvailableToAssigned() {
            var selectedPublicationIds = getSelectedPublicationIdsFromList(vm.availablePublications, vm.availableSearchValue);
            for (var i = 0; i < selectedPublicationIds.length; i++) {
                var publicationId = selectedPublicationIds[i];
                moveBetweenListsById(publicationId, vm.availablePublications, vm.assignedPublications);
                updateButtonAndCheckboxes();
            }
        }
        
        function moveBetweenListsById(publicationId, startList, endList) {
            var index = getIndexOfPublicationInList(publicationId, startList);
            var publicationToMove = startList[index];
            startList.splice(index, 1);
            publicationToMove.selected = false;
            endList.push(publicationToMove);
        }
        
        function moveSelectedFromAssignedToAvailable() {
            var selectedPublicationIds = getSelectedPublicationIdsFromList(vm.assignedPublications, vm.assignedSearchValue);
            for (var i = 0; i < selectedPublicationIds.length; i++) {
                var publicationId = selectedPublicationIds[i];
                moveBetweenListsById(publicationId, vm.assignedPublications, vm.availablePublications);
                updateButtonAndCheckboxes();
            }
        }

        function updateButtonAndCheckboxes(){
            vm.isPageEdited = true;
            vm.areAllAssignedSelected = false;
            vm.areAllAvailableSelected = false;
        }

        function save() {
            var assignedPublicationIds = [];
            for (var i = 0; i < vm.assignedPublications.length; i++) {
                assignedPublicationIds.push(vm.assignedPublications[i].id)
            }

            var purchaserId = $stateParams.subEntityId;
            var manufacturerId = userService.getManufacturerId();
            publicationService.assignPublicationsToPurchaser(manufacturerId, purchaserId, assignedPublicationIds)
                .then(assignSuccess, assignFailure)
        }

        function assignSuccess(){
            vm.isPageEdited = false;
            headerBannerService.setNotification('SUCCESS', CHANGES_SAVED, 5000);
        }

        function assignFailure(){
            headerBannerService.setNotification('ERROR', CHANGES_SAVED_FAILED, 5000);
        }

        function cancel() {
            initialize();
            headerBannerService.setNotification('INFO', CHANGES_CANCELED, 5000);
        }

        function clearAvailableSearch(){
            vm.availableSearchValue = "";
        }

        function clearAssignedSearch(){
            vm.assignedSearchValue = "";
        }

    }

})();
