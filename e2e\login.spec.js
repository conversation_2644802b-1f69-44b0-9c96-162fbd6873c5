describe('Dev end to end tests', function() {
    it('should login', function() {
        browser.get('http://dev1.cadshare.co.s3-website-us-west-2.amazonaws.com/dev/#!/login');

        element(by.model('loginCtrl.username')).sendKeys('<EMAIL>');
        element(by.model('loginCtrl.password')).sendKeys('FredTheRed');
        element(by.css('[ng-click="loginCtrl.authenticateUser()"]')).click();

        browser.waitForAngular();

        expect(browser.driver.getCurrentUrl()).toBe("http://dev1.cadshare.co.s3-website-us-west-2.amazonaws.com/dev/#!/orders/enquiries");

    });
});