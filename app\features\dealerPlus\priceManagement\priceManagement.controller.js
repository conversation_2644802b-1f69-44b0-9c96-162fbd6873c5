(function () {
    'use strict';

    angular
        .module('app.parts')
        .controller('PriceManagementController', PriceManagementController);

    PriceManagementController.$inject = ['DPMasterPartService', '$state', 'userService', '$translate', '$scope', '$uibModal'];

    function PriceManagementController(DPMasterPartService, $state, userService, $translate, $scope, $uibModal) {
        var vm = this;
        var ACTIVE_EDIT_PART_ID;
        var isCSVSelected = false;
        var timeout;
        var partProcess = ['MASTERPART_INVENTORY_EXPORT', 'MASTERPART_INVENTORY_UPLOAD'];

        vm.sortReverse = false;
        vm.part_sort = 'partNumber';
        vm.searchBy = 'partNumber';
        vm.searching = false;
        vm.searchResults = [];
        vm.isActiveEdit = false;
        vm.defaultCurrency = userService.getDPCustomerDefaultCurrency();
        vm.isSearchOnlyPartNumbers = userService.getSearchOnlyPartNumbers();

        vm.search = search;
        vm.viewPart = viewPart;
        vm.editPart = editPart;
        vm.cancelEdit = cancelEdit;
        vm.saveEdit = saveEdit;
        vm.exportPrices = exportPrices;
        vm.downloadTemplate = downloadTemplate;
        vm.fileChanged = fileChanged;
        vm.upload = upload;
        vm.downloadCSV = downloadCSV;

        var SELECT_CSV, PLEASE_SELECT_CSV, UPLOAD_SUCCESS, ERROR, WENT_WRONG, EXPORT, UPLOAD;
        $translate(['PARTS_UPLOAD.SELECT_CSV', 'PARTS_UPLOAD.PLEASE_SELECT_CSV', 'PARTS_UPLOAD.UPLOAD_SUCCESS', 'PARTS_UPLOAD.ERROR', 'GENERAL.WENT_WRONG', 'PARTS_UPLOAD.EXPORT', 'PARTS_UPLOAD.UPLOAD'])
            .then(function (resp) {
                SELECT_CSV = resp["PARTS_UPLOAD.SELECT_CSV"];
                PLEASE_SELECT_CSV = resp["PARTS_UPLOAD.PLEASE_SELECT_CSV"];
                UPLOAD_SUCCESS = resp["PARTS_UPLOAD.UPLOAD_SUCCESS"];
                ERROR = resp["PARTS_UPLOAD.ERROR"];
                WENT_WRONG = resp["GENERAL.WENT_WRONG"];
                EXPORT = resp["PARTS_UPLOAD.EXPORT"];
                UPLOAD = resp["PARTS_UPLOAD.UPLOAD"];
            });

        initialize();

        function initialize() {
            DPMasterPartService.fetchProgress(partProcess)
                .then(progressSuccess, failed);
        }

        function viewPart(partId) {
            $state.go('dpMasterPart', {masterPartId: partId})
        }

        function search() {
            vm.searchResults = [];
            vm.resultsReturned = false;
            vm.searching = true;

            var manufacturerId = userService.getManufacturerId();
            var purchaserId = userService.getManufacturerSubEntityId();

            DPMasterPartService.partSearch(
                manufacturerId,
                purchaserId,
                vm.searchValue,
                vm.searchBy
            ).then(searchSuccess, searchFailed);
        }

        function searchSuccess(resp) {
            vm.searchResults = resp.data.masterParts !== null ? resp.data.masterParts : [];
            vm.searching = false;
            vm.resultsReturned = true;
        }

        function searchFailed() {
            vm.searchError = true;
            vm.searching = false;
        }

        function editPart(partId) {
            var partPosition = findPartInArray(partId);
            vm.searchResults[partPosition].oldValue = vm.searchResults[partPosition].price;
            vm.searchResults[partPosition].isBeingEdited = true;
            vm.isActiveEdit = true;
        }

        function cancelEdit(partId) {
            var partPosition = findPartInArray(partId);
            vm.searchResults[partPosition].price = vm.searchResults[partPosition].oldValue;
            vm.searchResults[partPosition].isBeingEdited = false;
            vm.isActiveEdit = false;
            ACTIVE_EDIT_PART_ID = partId;
        }

        function saveEdit(partId) {
            ACTIVE_EDIT_PART_ID = partId;
            var partPosition = findPartInArray(partId);
            var price = vm.searchResults[partPosition].price;
            DPMasterPartService.updatePrice(partId, price)
                .then(saveEditSuccess, saveEditFailed);
        }

        function saveEditSuccess() {
            var partPosition = findPartInArray(ACTIVE_EDIT_PART_ID);
            vm.searchResults[partPosition].isBeingEdited = false;
            vm.isActiveEdit = false;
            ACTIVE_EDIT_PART_ID = null;
        }

        function saveEditFailed() {
            headerBannerService.setNotification('ERROR', WENT_WRONG, 10000);
        }

        function findPartInArray(partId) {
            return vm.searchResults.indexOf(_.findWhere(vm.searchResults, {masterPartId: partId}));
        }

        function clearMessages() {
            vm.successMessage = "";
            vm.errorMessage = "";
        }

        function exportPrices() {
            clearMessages();
            vm.exporting = true;
            DPMasterPartService.getParts()
                .then(getSuccess, failed);
        }

        function getSuccess(response) {
            DPMasterPartService.fetchProgress(partProcess)
                .then(progressSuccess, failed);
        }

        function progressSuccess(response) {
            if (response.data && response.data.length > 0) {
                vm.displayProgress = true;
                vm.processes = response.data;

                //Check all found records are complete
                var processComplete = true;
                for (var i = 0; i < vm.processes.length; i++) {
                    if (vm.processes[i].status != 'COMPLETE') {
                        processComplete = false;
                        break;
                    } else {
                        processComplete = true;
                    }
                }
                populateProgressDisplayNames(vm.processes);
                if (processComplete) {
                    stopPollingUpdates()
                } else {
                    beginPollingUpdates();
                }
            }
        }

        function populateProgressDisplayNames(processes) {
            for (var i = 0; i < processes.length; i++) {
                if (processes[i].process === 'MASTERPART_INVENTORY_EXPORT') {
                    processes[i].processDisplayName = EXPORT;
                } else if (processes[i].process === 'MASTERPART_INVENTORY_UPLOAD') {
                    processes[i].processDisplayName = UPLOAD;
                }
            }
        }

        function beginPollingUpdates() {
            timeout = setTimeout(getSuccess, 15000);
            vm.exporting = true;
        }

        function stopPollingUpdates() {
            clearTimeout(timeout);
            vm.exporting = false;
        }

        function downloadTemplate() {
            clearMessages();
            vm.downloading = true;
            DPMasterPartService.getPartHeaders()
                .then(getHeadersSuccess, failed);
        }

        function getHeadersSuccess(response) {
            var content = response.data.toString();
            var filename = "Parts Template.csv";
            exportToCSV(content, filename);
            vm.downloading = false;
        }

        function failed() {
            vm.errorMessage = ERROR;
            vm.exporting = false;
            vm.downloading = false;
        }

        function fileChanged(obj) {
            clearMessages();
            isCSVSelected = false;

            var elem = obj.target || obj.srcElement;
            if (elem.files.length > 0) {
                vm.file = elem.files[0];

                var ext = vm.file.name.substring(vm.file.name.lastIndexOf('.') + 1);
                vm.filename = vm.file.name;
                $scope.$digest();
                if (ext.toUpperCase() !== "CSV") {
                    isCSVSelected = false;
                    vm.errorMessage = SELECT_CSV;
                } else {
                    isCSVSelected = true;
                }
            }
        }

        function upload() {
            clearMessages();

            if (isCSVSelected) {
                vm.uploading = true;
                DPMasterPartService.uploadParts(vm.file)
                    .then(uploadSuccess, uploadFailed);
            } else {
                vm.errorMessage = PLEASE_SELECT_CSV
            }
        }

        function uploadSuccess(resp) {
            vm.successMessage = UPLOAD_SUCCESS;
            vm.uploading = false;
            vm.exporting = true;
            DPMasterPartService.fetchProgress(partProcess)
                .then(progressSuccess, failed);
        }

        function uploadFailed(error) {
            vm.errorMessage = error.data.message;
            vm.uploading = false;
        }

        function exportToCSV(content, filename) {
            var blob = new Blob([content], {type: 'text/csv;charset=utf-8;'});
            if (navigator.msSaveBlob) { // IE 10+
                navigator.msSaveBlob(blob, filename);
            } else {
                var link = document.createElement("a");
                if (link.download !== undefined) { // feature detection
                    // Browsers that support HTML5 download attribute
                    var url = URL.createObjectURL(blob);
                    link.setAttribute("href", url);
                    link.setAttribute("download", filename);
                    link.style.visibility = 'hidden';
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                }
            }
        }

        function downloadCSV(process, processName) {
            var filename = processName + '.csv';
            downloadCSVFileContents(process.s3Url, filename)
        }

        function downloadCSVFileContents(s3Url, filename) {
            var csvResponse = "";
            var xhr = new XMLHttpRequest();
            xhr.open("GET", s3Url);
            xhr.onload = function () {
                if (xhr.status == 200) {
                    csvResponse = xhr.responseText;
                    exportToCSV(csvResponse, filename);
                }
            }
            xhr.send();
        }
    }
})();
