(function () {
    'use strict';

    angular
        .module('app.viewer')
        .controller('EditNameModalController', EditNameModalController);

    EditNameModalController.$inject = ['$uibModalInstance', 'stateName'];

    function EditNameModalController($uibModalInstance, stateName) {
        var vm = this;

        vm.ok = ok;
        vm.cancel = cancel;
        vm.newName = stateName;


        function ok() {
            $uibModalInstance.close(vm.newName);
        }

        function cancel() {
            $uibModalInstance.dismiss();
        }
    }

})();
