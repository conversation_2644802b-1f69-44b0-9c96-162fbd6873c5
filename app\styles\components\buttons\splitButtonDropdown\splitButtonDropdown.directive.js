(function () {
    'use strict';

    angular
        .module('app.customer')
        .directive('splitButtonDropdown', splitButtonDropdown);

    function splitButtonDropdown() {
        var directive = {
            restrict: 'E',
            templateUrl: 'styles/components/buttons/splitButtonDropdown/splitButtonDropdown.html',
            scope: {
                mainAction: '&',
                mainActionLabel: '@',
                actions: '=',
                entity: '='
            },
            controller: SplitButtonDropdownController,
            controllerAs: 'splitButtonDropdownCtrl',
            bindToController: true,
        };

        return directive;
    }

    function SplitButtonDropdownController() {
        var vm = this;
    }
})();
