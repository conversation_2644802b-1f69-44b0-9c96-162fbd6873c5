(function () {
    'use strict';

    angular
        .module('app.orders')
        .controller('SplitOrderController', SplitOrderController);

    SplitOrderController.$inject = ['$uibModalInstance', 'partsObject', 'ordersService', 'userService', '$state'];

    function SplitOrderController($uibModalInstance, partsObject, ordersService, userService, $state) {
        var vm = this;

        vm.partsList = JSON.parse(JSON.stringify(partsObject.orderItems));
        vm.manualPartsList = JSON.parse(JSON.stringify(partsObject.manualItems));
        vm.orderId = partsObject.orderId;
        vm.displayId = partsObject.displayId ? partsObject.displayId : partsObject.orderId;

        vm.isDealerPlusPage = isDealerPlusPage;
        vm.cancel = cancel;
        vm.splitOrder = splitOrder;
        vm.getSplitOrderMaxQuantity = getSplitOrderMaxQuantity;

        initialize();

        function initialize() {
            for (var i = 0; i < vm.partsList.length; i++) {
                vm.partsList[i].oldQuantity = vm.partsList[i].quantity;
                if (vm.partsList.length === 1) {
                    vm.partsList[i].quantity = vm.partsList[i].quantity - 1;;
                }
            }
            for (var i = 0; i < vm.manualPartsList.length; i++) {
                vm.manualPartsList[i].oldQuantity = vm.manualPartsList[i].quantity;
                if (vm.manualPartsList.length === 1) {
                vm.manualPartsList[i].quantity = vm.manualPartsList[i].quantity - 1;
                }
            }
        }

        function cancel() {
            $uibModalInstance.dismiss();
        }

        function splitOrder() {
            vm.errorSplittingOrder = false;
            var parts = [];
            var addParts = [];

            for (var i = 0; i < vm.partsList.length; i++) {
                if (vm.partsList[i].isSelected) {
                    parts.push(vm.partsList[i])
                }
            }
            for (var i = 0; i < vm.manualPartsList.length; i++) {
                if (vm.manualPartsList[i].isSelected) {
                    addParts.push(vm.manualPartsList[i]);
                }
            }

            if (validateSplitOrderData(parts, addParts)) {
                ordersService.splitOrder(vm.orderId, parts, addParts)
                    .then(splitOrderSuccess, splitOrderFailure)
            }
        }

        function validateSplitOrderData(parts, addParts) {
            vm.nothingSelectedError = false;
            vm.quantityMismatchError = false;

            if (parts.length + addParts.length > 0) {
                for (var i = 0; i < parts.length; i++) {
                    if (parts[i].quantity === undefined || parts[i].quantity > parts[i].oldQuantity) {
                        vm.quantityMismatchError = true;
                        break;
                    }
                }
                for (var i = 0; i < addParts.length; i++) {
                    if (addParts[i].quantity === undefined || addParts[i].quantity > addParts[i].oldQuantity) {
                        vm.quantityMismatchError = true;
                        break;
                    }
                }
            } else {
                vm.nothingSelectedError = true;
            }
            return !vm.nothingSelectedError && !vm.quantityMismatchError;
        }

        function splitOrderSuccess() {
            $uibModalInstance.close();
        }

        function splitOrderFailure() {
            vm.errorSplittingOrder = true;
        }

        function isDealerPlusPage(){
            return userService.isDealerPlusUser() && $state.current.name.includes("customerOrders");
        }

        function getSplitOrderMaxQuantity(item) {
            if (vm.partsList.length === 1 || vm.manualPartsList.length === 1) {
                return item.oldQuantity - 1;
            } else {
                return item.oldQuantity;
            }
        }

    }
})();