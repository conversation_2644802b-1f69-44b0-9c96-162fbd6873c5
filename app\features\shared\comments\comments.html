<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" ng-click="commentsCtrl.cancel()" aria-label="Close">
        <i class="fa fa-close" aria-hidden="true"></i>
    </button>

    <h2 class="modal-title" translate>COMMENTS.ORDER_COMMENTS</h2>
  </div>
  <div class="modal-body">
    <div class="comment p-4" ng-repeat="comment in commentsCtrl.comments" ng-show="commentsCtrl.comments.length > 0" >
        <p class="comment-name pb-2"><strong>{{comment.createdByUserName}}</strong> - <small>{{comment.createdDate | date : 'dd MMM yyyy HH:mm'}}</small></p>
        <p class="comment-message">{{comment.message}}</p>
    </div>

<p ng-hide="commentsCtrl.comments.length > 0" translate>COMMENTS.CURRENTLY_NO_COMMENTS_FOR_ORDER</p>

<textarea size="250" class="full-width-comment" placeholder="{{'COMMENTS.TYPE_COMMENT_HERE' | translate}}" ng-model="commentsCtrl.message" ng-if="commentsCtrl.allowAddingComments" autofocus></textarea>

<div class="modal-actions">
    <button class="btn small secondary" href="" ng-click="commentsCtrl.cancel()" translate>COMMENTS.CLOSE</button>

    <button class="btn small primary" href="" ng-click="commentsCtrl.addComment()" ng-if="commentsCtrl.allowAddingComments" ng-class="commentsCtrl.isDealerPlusPage() ? 'dpGreenModal btn primary' : 'btn primary'" translate>
        COMMENTS.ADD_COMMENT
    </button>
</div>
</div>

