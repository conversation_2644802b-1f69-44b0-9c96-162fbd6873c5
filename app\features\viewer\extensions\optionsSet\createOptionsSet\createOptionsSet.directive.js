(function () {
    'use strict';

    angular
        .module('app.viewer')
        .directive('createOptionsSet', createOptionsSet);

    function createOptionsSet() {
        var directive = {
            restrict: 'E',
            templateUrl: 'features/viewer/extensions/optionsSet/createOptionsSet/createOptionsSet.html',
            controller: 'CreateOptionsSetController',
            controllerAs: 'createOptionsSetCtrl',
            bindToController: true
        };
        return directive;
    }

})();