.sign-panels {
  width: 450px;
  background: #fff;
  padding: 48px 40px 36px;
  margin: 20px auto;
  border-radius: 10px;
  box-shadow: 0 5px 10px rgba(0,0,0,.05);
}

.grey-text{

  color:#757575;

}

.login {
  position: relative
}

.title {
  color: #333
}

.title span {
  display: block;
  font-size: 46px;
  font-weight: 700
}

.title p {
  font-size: 20px;
  font-weight: 500
}

.sign-panels input {
  width: 100%;
  display: block;
  margin-bottom: 15px;
  height: 50px;
  border-radius: 4px;
  border: solid 1px #bdbdbd;
  text-align: left;
  padding: 10px;
  font-size: 15px;
  color: black;
  font-weight: 500;
  cursor:pointer;
  transition: 0.5s;
}

.sign-panels input:focus {
  outline: 0;
  border: solid 1px #007bff;
}

.btn-signin {
  display: inline-block;
  width: 100%;
  margin-top: 10px;
  height: 50px;
  background: #007bff;
  color: #fff;
  border-radius: 4px;
  padding: 11px;
  font-size: 20px;
  text-decoration: none;
  font-weight: 500;
  border: none;
  box-shadow: none;
  cursor: pointer;
  transition: 0.5s;
}

.btn-signin:hover {
  background: #0064d0;
}

.btn-reset {
  font-size: 19px;
  font-weight: 500;
  color: #9f9f9f;
  display: block;
  text-decoration: none
}

.btn-reset .fa {
  margin-left: 6px
}

.error {
  display: block;
  color: #007bff;
  font-size: 20px;
  font-weight: 600;
  margin: 15px 0
}

.chBox {
  margin: auto;
  -webkit-user-select: none;
  user-select: none;
  cursor: pointer;
}
.chBox span {
  display: inline-block;
  vertical-align: middle;
  transform: translate3d(0, 0, 0);
}
.chBox span:first-child {
  position: relative;
  min-width: 18px;
  height: 18px;
  border-radius: 3px;
  transform: scale(1);
  vertical-align: middle;
  border: 1px solid #9098A9;
  transition: all 0.2s ease;
}
.chBox span:first-child svg {
  position: absolute;
  top: 3px;
  left: 2px;
  fill: none;
  stroke: #FFFFFF;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
  stroke-dasharray: 16px;
  stroke-dashoffset: 16px;
  transition: all 0.3s ease;
  transition-delay: 0.1s;
  transform: translate3d(0, 0, 0);
}
.chBox span:first-child:before {
  content: "";
  width: 100%;
  height: 100%;
  background: #506EEC;
  display: block;
  transform: scale(0);
  opacity: 1;
  border-radius: 50%;
}
.chBox span:last-child {
  padding-left: 8px;
}
.chBox:hover span:first-child {
  border-color: #506EEC;
}

.inp-chBox:checked + .chBox span:first-child {
  background: #506EEC;
  border-color: #506EEC;
  animation: wave 0.4s ease;
}
.inp-chBox:checked + .chBox span:first-child svg {
  stroke-dashoffset: 0;
}
.inp-chBox:checked + .chBox span:first-child:before {
  transform: scale(3.5);
  opacity: 0;
  transition: all 0.6s ease;
}

@keyframes wave {
  50% {
    transform: scale(0.9);
  }
}

@media screen and (max-width: 600px) {

  .sign-panels {
    width: 350px;
  }

}