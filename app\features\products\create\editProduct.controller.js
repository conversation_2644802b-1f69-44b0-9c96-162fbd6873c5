(function () {
    'use strict';

    angular
        .module('app.products')
        .controller('EditProductController', EditProductController);

    EditProductController.$inject = ['manufacturerProductService', '$uibModalInstance', 'product', '$scope', '$uibModal', 'headerBannerService', '$translate'];
    function EditProductController(manufacturerProductService, $uibModalInstance, product, $scope, $uibModal, headerBannerService, $translate) {

        var vm = this;

        vm.cancel = $uibModalInstance.dismiss;
        vm.productThumbnailUrl = "images/placeholder.jpg";
        vm.isEditMachine = true;
        vm.isCreateMode = true;
        $scope.data = {rangeId: ''};

        vm.loading = false;

        if (product !== null) {
            vm.isCreateMode = false;
            vm.productName = product.name;
            vm.productId = product.productId || product.id;
            vm.productThumbnailUrl = product.thumbnailUrl || "images/placeholder.jpg";
            
            // Store the range name if it's a string
            vm.rangeName = typeof product.range === 'string' ? product.range : null;
            
            console.log('Editing product:', product);
            console.log('Product ID:', vm.productId);
            console.log('Range name:', vm.rangeName);
            
            // We'll set the rangeId after loading the ranges
        }

        vm.createNewRangeField = createNewRangeField;
        vm.cancelRange = cancelRange;
        vm.createNewRange = createNewRange;
        vm.editProduct = editProduct;
        vm.createProduct = createProduct;
        vm.addProductImage = addProductImage;
        vm.rangeChanged = rangeChanged;

        $translate(['GENERAL.CREATE', 'GENERAL.EDIT','PRODUCTS_CATALOG.NEW_PRODUCT','PRODUCTS_CATALOG.PRODUCT'])
            .then(function (resp) {
                vm.createModal = resp["GENERAL.CREATE"];
                vm.editModal= resp["GENERAL.EDIT"];
                vm.newProduct = resp["PRODUCTS_CATALOG.NEW_PRODUCT"];
                vm.product = resp["PRODUCTS_CATALOG.PRODUCT"];
            });

        initialize();
        function initialize() {
            getRange();
        }

        function editProduct() {
            vm.loading = true;
            manufacturerProductService.editMachine(vm.rangeId, vm.productName, vm.productId, vm.productThumbnailUrl)
                .then(editProductSuccess, editProductFailure);
        }

        function editProductSuccess() {
            vm.productCreated = true;
            vm.loading=false;
            $uibModalInstance.close();
        }

        function editProductFailure(error) {
            console.log(error);
        }

        function createProduct() {
            vm.loading = true;
            manufacturerProductService.createMachine(vm.rangeId, vm.productName, vm.productThumbnailUrl)
                .then(editProductSuccess, editProductFailure);
        }

        function rangeChanged(rangeId) {
            vm.rangeId = rangeId;
        }

        function getRange(value) {
            manufacturerProductService.getRangeByManufacturer()
                .then(getRangeSuccess, getRangeFailure);
        }

        function getRangeSuccess(response) {
            vm.rangeValues = response.data.productRanges || [];
            
            // If we're in edit mode
            if (!vm.isCreateMode) {
                // If we have a range name, find the corresponding range ID
                if (vm.rangeName && vm.rangeValues.length > 0) {
                    // Find the range ID by matching the name
                    var matchingRange = vm.rangeValues.find(function(range) {
                        return range.name === vm.rangeName;
                    });
                    
                    if (matchingRange) {
                        vm.rangeId = matchingRange.id;
                        $scope.data.rangeId = matchingRange.id;
                        console.log('Found matching range ID:', matchingRange.id, 'for name:', vm.rangeName);
                    } else {
                        console.log('Could not find matching range for name:', vm.rangeName);
                    }
                }
                // If we already have a range ID, make sure it's set in the dropdown
                else if (vm.rangeId) {
                    $scope.data.rangeId = vm.rangeId;
                    console.log('Setting range dropdown to existing ID:', vm.rangeId);
                }
            }
        }

        function getRangeFailure(error) {
            console.log(error);
        }

        function createNewRangeField() {
            vm.isEditMachine = false;
        }

        function cancelRange() {
            vm.isEditMachine = true;
        }

        function createNewRange() {
            vm.rangeFailure = false;

            manufacturerProductService.createNewRange(vm.newRange)
                .then(createNewRangeSuccess, createNewRangeFailed);
        }

        function createNewRangeSuccess(response) {
            getRange();
            vm.rangeId = response;
            $scope.data = {rangeId: response};
            cancelRange();
        }

        function createNewRangeFailed(error) {
            vm.rangeFailure = true;
            vm.internalFailureMessage = error.data.message;
        }

        function addProductImage() {
            var cropType = 'PRODUCT';
            $uibModal.open({
                templateUrl: 'features/products/imageCropper/imageCropper.html',
                controller: 'ImageCropperController',
                controllerAs: 'imageCropperCtrl',
                size: 'xl',
                backdrop: 'static',
                resolve: {
                    cropType: function () {
                        return cropType;
                    },
                    manufacturerId: function() {
                        return null;
                    },
                    source: function() {
                        return 'editProduct';
                    }
                }
            }).result.then(function (response) {
                if (response) {
                    vm.productThumbnailUrl = response;
                }
            });
        }

    }
})();
