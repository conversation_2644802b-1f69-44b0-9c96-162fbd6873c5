(function () {
    'use strict';

    angular
        .module('app.services')
        .factory('purchasableAssemblyService', purchasableAssemblyService);

    purchasableAssemblyService.$inject = ['$http', 'apiConstants', '$q'];

    function purchasableAssemblyService($http, apiConstants, $q) {
        return {
            fetchPurchasableAssemblysForModel: fetchPurchasableAssemblysForModel,
            createPurchasableAssembly: createPurchasableAssembly
        };

        function fetchPurchasableAssemblysForModel(modelId) {
            return $http.get(apiConstants.url + '/modelAssembly/model/' + modelId);
        }

        function createPurchasableAssembly(modelId, partId) {
            return $http.put(apiConstants.url + '/modelAssembly/model/' + modelId + '/part/' + partId);
        }

    }
})();
