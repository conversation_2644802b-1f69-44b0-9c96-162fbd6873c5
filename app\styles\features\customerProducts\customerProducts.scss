.customerProductsGrid{

  .customerProductsItem{

    cursor: pointer;
    -webkit-box-shadow: 0 5px 15px 0 rgba(0, 0, 0, .1);
    box-shadow: 0 5px 15px 0 rgba(0, 0, 0, .1);
    background-color: white;
    flex-grow: 2;
    margin-bottom:1rem;

    @media (hover: none) and (pointer: coarse) {
      .customerProductsHover {
        display: none !important;
      }
    }

      .customerProductsHover {

        opacity: 0;
        transition: visibility 0s, opacity 0.1s linear;
        position: absolute;

        .btn{

          z-index:100;

        }

      }

      &:hover {

        .customerProductsImage, .customerViewablesImage{
          opacity:0.8;
        }
            .customerProductsHover {

              opacity: 1;
              z-index:100;
              transition: visibility 0s, opacity 0.1s linear;

            }

          }

    .customerProductsInformationBox{

      z-index:101;
      background-color: white;
      border-bottom-left-radius: 5px;
      border-bottom-right-radius: 5px;
      cursor: pointer;

    }

    .customerProductsImage, .customerViewablesImage{

      align-items: center;
      flex-direction: column;
      justify-content: center;
      display: flex;
      transition: visibility 0s, opacity 0.1s linear;

    }

  }

}