(function () {
    'use strict';

    angular
        .module('app.products')
        .controller('ProductsTabsController', ProductsTabsController);

    ProductsTabsController.$inject = ['$translate'];

    function ProductsTabsController($translate) {
        var vm = this;

        var PROD_CATALOG, TECH_DOCS,VIDEOS;
        $translate(['PROD_TABS.PROD_CATALOG', 'PROD_TABS.TECH_DOCS', 'PROD_TABS.VIDEOS'])
            .then(function (resp) {
                PROD_CATALOG = resp["PROD_TABS.PROD_CATALOG"];
                TECH_DOCS = resp["PROD_TABS.TECH_DOCS"];
                VIDEOS = resp["PROD_TABS.VIDEOS"];

                vm.tabs = [
                    {title: PROD_CATALOG, route: 'products.catalogue'},
                    {title: TECH_DOCS, route: 'products.techDocs'},
                    {title: VIDEOS, route: 'products.videos'}];
            });



    }
})();
