<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" ng-click="linkCtrl.cancel()" aria-label="Close"><i class="fa fa-close" aria-hidden="true"></i></button>
    <h2 class="modal-title" translate>LINK.TITLE</h2>
</div>

</div>

<div class="modal-body">
    <p translate>LINK.PLEASE_SELECT</p>
    <div class="input-group">
        <label translate>LINK.SELECT_RANGE</label>
        <div style="width: 100%;">
            <select ts-select-fix ng-model="linkCtrl.rangeId" ng-change="linkCtrl.rangeChanged(linkCtrl.rangeId)"
                    ng-options="ranges.rangeId as ranges.name for ranges in linkCtrl.ranges" placeholder="{{'LINK.RANGE' | translate}}" ng-required="true">
            </select>

            <div class="select-arrow"></div>
        </div>
    </div>

    <div class="input-group">
        <label translate>LINK.SELECT_PRODUCT</label>
        <div style="width: 100%;">
            <select ng-model="linkCtrl.machineId" ng-change="linkCtrl.machineChanged(linkCtrl.machineId)" ng-class="linkCtrl.isMachineDropdownDisabled ? 'disabled-dropdown' : ''"
                    ng-options="machine.machineId as machine.name for machine in linkCtrl.machines" placeholder="{{'LINK.MACHINE' | translate}}" ng-required="true" ng-disabled="linkCtrl.isMachineDropdownDisabled">
            </select>
            <div class="select-arrow"></div>
        </div>
    </div>

    <div class="input-group">
        <label translate>LINK.SELECT_VIEWABLE</label>
        <div style="width: 100%;">
            <select ng-options="viewable.modelId as viewable.modelName for viewable in linkCtrl.viewables" ng-class="linkCtrl.isViewableDropdownDisabled ? 'disabled-dropdown' : ''"
                    ng-model="linkCtrl.viewableId" ng-disabled="linkCtrl.isViewableDropdownDisabled">
            </select>
            <div class="select-arrow"></div>
        </div>
    </div>

    <p class="modal-message" style="color: red" ng-if="linkCtrl.errors.noViewableSelected" translate>
        LINK.PLEASE_SELECT_VIEWABLE
    </p>
    <p class="modal-message" style="color: red" ng-if="linkCtrl.errors.serviceError" translate>
        LINK.ERROR
    </p>

    <div class="modal-actions">
        <a class="btn small secondary" href="" ng-click="linkCtrl.cancel()" translate>GENERAL.CANCEL</a>
        <a class="btn small primary" href="" ng-click="linkCtrl.save()" translate>LINK.SAVE</a>
    </div>

</div>


