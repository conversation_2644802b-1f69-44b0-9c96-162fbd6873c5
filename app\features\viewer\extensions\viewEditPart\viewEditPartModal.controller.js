(function () {
    'use strict';

    angular
        .module('app.viewer')
        .controller('ViewEditPartModalController', ViewEditPartModalController);

    ViewEditPartModalController.$inject = ['$uibModalInstance', 'partNode', 'viewerService', 'viewerBannerService', '$translate'];

    function ViewEditPartModalController($uibModalInstance, partNode, viewerService, viewerBannerService, $translate) {
        var vm = this;

        vm.isEditMode = false;
        vm.edits = {partNumber: '', partDescription: ''};

        vm.saveEdits = saveEdits;
        vm.cancel = cancel;
        vm.exitEditMode = exitEditMode;
        vm.enterEditMode = enterEditMode;

        var EDIT_FAIL, EDIT_SUCCESS;
        $translate(['VIEW_EDIT_PART.EDIT_FAIL', 'VIEW_EDIT_PART.EDIT_SUCCESS'])
            .then(function (resp) {
                EDIT_FAIL = resp["VIEW_EDIT_PART.EDIT_FAIL"];
                EDIT_SUCCESS = resp["VIEW_EDIT_PART.EDIT_SUCCESS"];
            });

        initialize();

        function initialize() {

            vm.partNumber = partNode.partNumber ? partNode.partNumber : 'N/A';
            vm.partDescription = partNode.partDescription ? partNode.partDescription : 'N/A';
            vm.edits.partNumber = vm.partNumber;
            vm.edits.partDescription = vm.partDescription;
        }


        function saveEdits() {
            var part = {
                "partId": partNode.partId,
                "partNumber": vm.edits.partNumber,
                "partDescription": vm.edits.partDescription,
                "modelId": partNode.modelId,
                "partWeight": partNode.partWeight,
                "partMassUnit": partNode.partMassUnit
            };
            var partList = [part];

            viewerService.updateParts(partList).then(updatePartSuccess, updatePartFailed);
        }

        function updatePartSuccess() {
            viewerBannerService.setNotification('SUCCESS', EDIT_SUCCESS, 1000 );
            $uibModalInstance.close(vm.edits);
        }

        function updatePartFailed() {
            viewerBannerService.setNotification('ERROR', EDIT_FAIL, 2000 );
            cancel();
        }

        function cancel() {
            $uibModalInstance.dismiss();
        }

        function enterEditMode() {
            vm.isEditMode = true;
        }

        function exitEditMode() {
            vm.isEditMode = false;
        }
    }

})();
