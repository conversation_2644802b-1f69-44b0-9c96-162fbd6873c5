(function () {
  'use strict';

  angular
    .module('app.services')
    .factory('registerService', registerService);

  registerService.$inject = ['$http', 'apiConstants', '$location'];

  function registerService($http, apiConstants, $location) {

    return {
      getRangesByManufacturerId: getRangesByManufacturerId,
      getManualsByRangeId: getManualsByRangeId,
      getFolderTaxCertLink: getFolderTaxCertLink,
      uploadTaxExempFileToS3: uploadTaxExempFileToS3,
      signUp: signUp
    };

    function getRangesByManufacturerId(manufacturerId) {
      return $http.get(`${apiConstants.url}/register/manufacturer/${manufacturerId}/ranges`);
    }
    
    function getFolderTaxCertLink(manufacturerId, payload) {
      return $http.post(`${apiConstants.url}/register/manufacturer/${manufacturerId}/getpresignedurl/taxexemptioncertificate`, payload);
    }

    function uploadTaxExempFileToS3(url, file) {
      return $http.put(url, file, {headers: {'Content-Type': file.type}});
    }

    function getManualsByRangeId(rangeId) {
      return $http.get(`${apiConstants.url}/register/range/${rangeId}/manuals`);
    }

    
    function signUp(payload) {
      var siteUrl = $location.protocol() + '://' + $location.host();
      var config = {
        headers: { 'Site-Url': siteUrl },
      };
      return $http.post(`${apiConstants.url}/register`, payload, config);
    }

  }
})();
