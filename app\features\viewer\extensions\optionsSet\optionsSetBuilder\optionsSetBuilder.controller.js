(function () {
    'use strict';

    angular
        .module('app.viewer')
        .controller('OptionsSetBuilderController', OptionsSetBuilderController);

    OptionsSetBuilderController.$inject = ['optionsSetService', 'viewerBannerService', '$stateParams', '$uibModal', '$rootScope', '$scope'];

    function OptionsSetBuilderController(optionsSetService, viewerBannerService, $stateParams, $uibModal, $rootScope, $scope) {
        var vm = this;

        vm.isOpen = true;
        vm.existingOptionsSets = {};
        vm.createOptionSet = createOptionSet;
        vm.editOptionSet = editOptionSet;
        vm.deleteOptionSet = deleteOptionSet;

        initialize();

        function initialize() {
            vm.modelId = $stateParams.modelId;
            fetchOptionsSets();
        }

        function fetchOptionsSets(){
            optionsSetService.fetchOptionsSetsForModel(vm.modelId)
                 .then(fetchOptionsSetsForModelSuccess, fetchOptionsSetsForModelFailed);
        }

        function fetchOptionsSetsForModelSuccess(response) {
            vm.existingOptionsSets = response.data;
        }

        function fetchOptionsSetsForModelFailed(error) {
            viewerBannerService.setNotification("ERROR", error);
        }

        function createOptionSet() {
            vm.isOpen = false;
            $rootScope.$broadcast("create-options-set-opened");
        }

        function editOptionSet(optionsSet) {
            vm.isOpen = false;
            $rootScope.$broadcast("create-options-set-opened", optionsSet.id);
        }

        function deleteOptionSet(optionsSet) {
            vm.successMessage = "";
            var deleteObject = {
                name: "Options Set for part " + optionsSet.partNumber,
                id: optionsSet.id,
                url: '/optionsSet/' + optionsSet.id
            };

            $uibModal.open({
                templateUrl: 'features/shared/commonDelete/deleteDialogBox.html',
                controller: 'DeleteController',
                controllerAs: 'deleteCtrl',
                size: 'sm',
                resolve: {
                    deleteObject: function () {
                        return deleteObject;
                    }
                }
            }).result.then(function () {
                fetchOptionsSets();
            });
        }

        $scope.$on("create-options-set-closed", function () {
            vm.isOpen = true;
            initialize();
        });

        $scope.$on("optionsSet-updated", function () {
            initialize();
        });
    }

})();
