(function () {
    'use strict';

    angular
        .module('app.customer')
        .controller('DeleteWorkInstructionsController', DeleteWorkInstructionsController);

    DeleteWorkInstructionsController.$inject = ['workInstructionsService', '$uibModalInstance', 'workInstructions'];

    function DeleteWorkInstructionsController(workInstructionsService, $uibModalInstance, workInstructions) {
        var vm = this;

        vm.cancel = $uibModalInstance.dismiss;
        vm.workInstructions = workInstructions;

        vm.deleteWorkInstructions = deleteWorkInstructions;

        function deleteWorkInstructions() {
            vm.isDisabled = true;

            workInstructionsService.deleteWorkInstructions(workInstructions.viewableId)
                .then(deleteWorkInstructionsSuccess, deleteWorkInstructionsFailed);
        }

        function deleteWorkInstructionsSuccess() {
            $uibModalInstance.close();
        }

        function deleteWorkInstructionsFailed(response) {
            vm.deleteFailure = true;
            vm.internalFailureMessage = response.data.message;
        }
    }
})();