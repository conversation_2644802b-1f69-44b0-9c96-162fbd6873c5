<div class="d-flex justify-content-center cadSplitDropdown">
    <div class="btn-group btn-hover">
        <button type="button" class="btn xsmall secondary main-action" ng-click="splitButtonDropdownCtrl.mainAction()">{{splitButtonDropdownCtrl.mainActionLabel}}</button>
        <div class="dropdown-split">
            <button type="button" class="btn xsmall secondary dropdown-toggle dropdown-toggle-split" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <span class="sr-only">Toggle Dropdown</span>
            </button>
            <div class="dropdown-menu">
                <ul class="list-unstyled m-0 p-0">
                    <li ng-repeat="action in splitButtonDropdownCtrl.actions" title="{{action.title}}">
                        <a ng-show="!action.show || action.show(splitButtonDropdownCtrl.entity)" href="" class="px-3 py-2 dark-secondary" ng-click="action.onClick(splitButtonDropdownCtrl.entity)">
                            <i class="fa fa-fw {{action.icon}}"></i> {{action.label(splitButtonDropdownCtrl.entity)}}
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
