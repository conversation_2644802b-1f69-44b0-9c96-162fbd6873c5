// =============================================================================
// Layout
// =============================================================================
.body-content {
    @extend %clearfix;
    padding: $spacing * 3 $spacing * 4;
    min-width: $min-width;
    max-width: $max-width;
    margin: 0 auto;

    &.product-catalogue {
        padding: 0px $spacing * 4;
    }

    &.primary-panel {
        padding: 0px $spacing * 4;
    }

    &.master-part-content {
        padding: 0px 32px;
    }

    &.small-content {
        min-width: 0;
        max-width: $min-width;
        padding: $spacing * 2 $spacing;
    }
}

.overlay {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: rgba($lightback, 0.6);
    visibility: hidden;
    opacity: 0;
    transition: visibility 0s, opacity 0.1s linear;
    z-index: 0;

    &.visible {
        visibility: visible;
        opacity: 1;
    }
}

.content-panel {
    &.lrg {
        width: 70%;
    }

    &.sml {
        width: 25%;
    }

    &.panel-dark {
        background: $white;
        padding: $spacing * 2;
        @include border-radius(5px);

        h3 {
            margin: $spacing * 2 0 $spacing;

            &:first-child {
                margin-top: 0;
            }

            &.use-shipping {
                display: inline-block;

                span {
                    font-size: 0.9em;
                    margin-left: $spacing;

                    input {
                        width: auto;
                        border: $divider-color;
                    }
                }
            }
        }
    }
}

.panel {
    padding: $spacing * 2 0;
    background: $white;
    -webkit-box-shadow: 0 2px 6px rgba($black, 0.2);
    box-shadow: 0 2px 6px rgba($black, 0.2);
    @include border-radius(5px);

    &.no-padding {
        padding: 0;
    }

    &.small-padding {
        padding: $spacing 0;
    }

    &.tb-padding {
        padding: $spacing 0;
    }

    &.customer-viewable {
        width: calc(75% - 32px);
        float: right;
        margin-top: 0;
    }

    &.security-panel {
        padding: 16px 16px 23px 16px;
        margin-bottom: 32px;

        form {
            margin-bottom: $spacing * 2;
        }

        .toggle {
            margin: $spacing * 3 0 0 7px;
        }
    }

    .small-panel-content {
        padding: $spacing $spacing * 2 0 $spacing * 2;

        .titletail {
            margin-bottom: $spacing;
        }
    }

    .expand-panel-content {
        padding: 0 $spacing * 2;

        h4 {
            margin-bottom: 5px;
        }
    }

    .panel-heading {
        .pull-right,
        .pull-left {
            margin-bottom: $spacing;
        }

        .pull-right {
            .btn {
                margin-right: $spacing * 2;
            }
        }
    }
}

.watermark-image {
    background: $lightback;
}

// alignment
.vertical-align {
    display: -webkit-flex;
    display: flex;
    align-items: center;
}

.justify-content {
    justify-content: space-between;
    display: -webkit-flex;
    display: flex;
    align-items: center;
}

.center-align {
    margin: 0 auto;
    display: block;
    text-align: center;
}

.partDetailsContentMax {
    display: inline-block;
    //width:calc(100vw - 420px - 260px);
    width: calc(100vw - 260px);
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-top: 1px solid #c1c1c1;

    .input-group {
        margin-bottom: 0;
    }

    button {
        width: 126px;
    }

    button:first-of-type {
        margin-right: 8px;
    }

    > .part-description {
        width: 38%;

        input {
            width: 100%;
        }
    }

    .product-price {
        //width: 16%;

        input {
            width: calc(100% - 20px);
        }
    }

        @media (max-width: 1000px) {
            .partDetailsContent {
                flex-direction: column;
                align-items: stretch !important;
            }
    
            .vertical-divider {
                width: 100% !important;
                height: 1px !important;
                margin: 1rem 0 !important;
            }
    
            .partDetailsActionBtns {
    
                align-self: start !important;
    
            }
    
            .partDetailsSupersessionNumber {
    
                text-wrap: wrap !important;
    
            }
        }

    > div {
        display: inline-block;
        margin-right: 16px;

        > label {
            .input-group {
                margin-bottom: 4px;
            }
        }
    }

    > div:last-of-type {
        display: flex;
        align-items: center;
        margin-right: 0;
        justify-content: space-between;
        margin-top: 6px;

        > i {
            cursor: pointer;
            margin: 0 8px;
            font-size: 20px;
        }
    }
}
.partDetailsContentMin {

    display: inline-block;
    //width:calc(100vw - 420px - 260px);
    width: calc(100vw - 680px);
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-top: 1px solid #c1c1c1;

    .input-group {
        margin-bottom: 0;
    }

    button {
        width: 126px;
    }

    // button:first-of-type {
    //     margin-right: 8px;
    // }

    > .part-description {
        width: 38%;

        input {
            width: 100%;
        }
    }

    .product-price {
        width: 16%;

        input {
            width: calc(100% - 20px);
        }
    }

        @media (max-width: 1300px) {
            .partDetailsContent {
                flex-direction: column;
                align-items: stretch !important;
            }
    
            .vertical-divider {
                width: 100% !important;
                height: 1px !important;
                margin: 1rem 0 !important;
            }
    
            .partDetailsActionBtns {
    
                align-self: start !important;
    
            }
    
            .partDetailsSupersessionNumber {
    
                text-wrap: wrap !important;
    
            }
        }

    > div {
        display: inline-block;
    //     margin-right: 16px;

    //     > label {
    //         .input-group {
    //             margin-bottom: 4px;
    //         }
    //     }
    }

    // > div:last-of-type {
    //     display: flex;
    //     align-items: center;
    //     margin-right: 0;
    //     justify-content: space-between;
    //     margin-top: 6px;

    //     > i {
    //         cursor: pointer;
    //         margin: 0 8px;
    //         font-size: 20px;
    //     }
    // }
}

@media (max-width: 1500px) {

    .partDetailsPartNumber {
        flex-wrap: wrap;
    }

}

.partDetailsContent{

    .vertical-divider {
            width: 1px;
            background-color: #007bff;
            margin: 0 1rem;
        }
    
        .viewerFooterIcon {
            cursor: pointer;
            font-size: 20px;
        }
    
        .spinner-container {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100%;
            min-height: 70px;
            text-align: center;
        }

        label{

           font-size: 0.9em;

        }

        .partDetailsSupersessionNumber {
        
           font-size: 0.9em!important;
        
        }

}

.multiple-parts-selected {
    display: block;

    > h4 {
        margin: 0 0 4px 0;
    }

    > p {
        margin: 0;
    }
}

//Master part

.master-part-details {
    height: 450px;
}

.master-part-details,
.master-part-details-split {
    .titletail {
        margin: 0 0 12px 0;
    }
}

.master-part-details .panel {
    height: calc(100% - 64px);
    overflow: auto;
}
.master-part-details-split {
    height: 400px;
}
.master-part-details-half .panel {
    height: calc(50% - 64px);
}

.titletail-masterpart {
    text-transform: uppercase;
    font-size: 0.95em;
    position: relative;
    font-weight: 600;
    margin: 0 0 $spacing * 2 0;
    margin: 0 0 20px 0;
    z-index: 1;

    span {
        background: $lightback;
        padding: 0 $spacing * 2 0 0;
    }
    &:before {
        border-top: 2px solid lighten($divider-color, 3%);
        content: "";
        margin: 0 auto;
        position: absolute;
        top: 50%;
        left: 0;
        right: 0px;
        bottom: 0;
        width: calc(100% - 136px);
        z-index: -1;
    }
}

// Aligners

.flex-right {
    margin-left: auto !important;
}

.pull-left {
    float: left;
}

.pull-right {
    float: right;
}

.pull-center {
    left: 70%;
    display: flex !important;
    align-items: center;
    justify-content: center;
}

.checkbox-padding {
    padding-top: 1%;
}

//sizes for widths
.width-5 {
    width: 5%;
}

.width-10 {
    width: 10%;
}

.width-15 {
    width: 15%;
}

.width-20 {
    width: 20%;
}

.width-25 {
    width: 25%;
}

.width-30 {
    width: 30%;
}

.width-35 {
    width: 35%;
}

.width-40 {
    width: 40%;
}

.width-45 {
    width: 45%;
}

.width-50 {
    width: 50%;
}

.width-55 {
    width: 55%;
}

.width-60 {
    width: 60%;
}

.width-65 {
    width: 65%;
}

.width-70 {
    width: 70%;
}

.width-75 {
    width: 75%;
}

.width-80 {
    width: 80%;
}

.width-85 {
    width: 85%;
}

.width-90 {
    width: 90%;
}

.width-95 {
    width: 95%;
}

.width-100 {
    width: 100%;
}

/* uncomment this if you want to see BR tags easily */
/*br{
  display: block;
  line-height: 600px;
}*/

// Small info panel left & Main panel right, would be used on admin and softcopy pages etc...

.left-panel {
    width: 22%;
    float: left;
}

.right-panel {
    width: 75%;
    float: right;
}

//margin styles (8px multiples)

.mt-0 {
    margin-top: 0;
}

.mr-0 {
    margin-right: 0;
}

.ml-0 {
    margin-left: 0;
}

.mb-0 {
    margin-bottom: 0;
}

// 8px
.mb-8 {
    margin-bottom: 8px;
}

.mt-8 {
    margin-top: 8px;
}

.mr-8 {
    margin-right: 8px;
}

.ml-8 {
    margin-left: 8px;
}

// 16px

.mb-16 {
    margin-bottom: 16px;
}

.mt-16 {
    margin-top: 16px;
}

.mr-16 {
    margin-right: 16px;
}

.ml-16 {
    margin-left: 16px;
}

.mlr-16 {
    margin-left: 16px;
    margin-right: 16px;
}

.m-16 {
    margin: 16px;
}

.mt-32 {
    margin-top: 32px;
}

.mt-neg-16 {
    margin-top: -16px;
}

.mt-n1 {
    margin-top: -15px !important;
}
