(function () {
    "use strict";

    angular.module("app.viewable").factory("viewableService", viewableService);

    viewableService.$inject = ["$http", "apiConstants", "$rootScope"];

    function viewableService($http, apiConstants, $rootScope) {
        var isCsvProcessing = false;
        return {
            fetchModelTranslationWarnings: fetchModelTranslationWarnings,
            editModel: editModel,
            setIsCsvProcessing: setIsCsvProcessing,
            getIsCsvProcessing: getIsCsvProcessing,
        };

        function fetchModelTranslationWarnings(modelId) {
            return $http.get(apiConstants.url + "/model/" + modelId + "/translationErrors", null);
        }

        function editModel(modelName, modelId) {
            var updateData = {
                modelName: modelName,
                modelDescription: "New Description",
            };
            return $http.put(apiConstants.url + "/model/" + modelId, updateData);
        }

        function setIsCsvProcessing(status) {
            if (isCsvProcessing !== status) {
                isCsvProcessing = status;
                $rootScope.$broadcast("csvProcessingStatusChanged", isCsvProcessing);
            }
        }

        function getIsCsvProcessing() {
            return isCsvProcessing;
        }
    }
})();
