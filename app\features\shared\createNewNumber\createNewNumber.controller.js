(function () {
    'use strict';

    angular
        .module('app.shared')
        .controller('CreateNewNumberController', CreateNewNumberController);

    CreateNewNumberController.$inject = ['$uibModalInstance', 'ordersService', '$state', 'customerUserId', 'userService'];

    function CreateNewNumberController($uibModalInstance, ordersService, $state, customerUserId, userService) {

        var vm = this;

        vm.cancel = $uibModalInstance.dismiss;
        vm.createNumber = createNumber;
        vm.isDealerPlusPage = isDealerPlusPage;

        function createNumber() {
            var userId = null;
            if(customerUserId !== null){
                userId = customerUserId;
            }else{
                if($state.params.onBehalfOf && $state.params.onBehalfOf !== "null"){
                    var decrypted = atob($state.params.onBehalfOf);
                    var parsed = JSON.parse(decodeURIComponent(decrypted));
                    userId = parsed.userId
                }
            }
            ordersService.createNumber(vm.data, userId)
                .then(createNumberSuccess, createNumberFailure);
        }

        function createNumberSuccess(resp) {
            $uibModalInstance.close(resp.data);
        }

        function createNumberFailure(error) {
            headerBannerService.setNotification('ERROR', error.data.error, 10000);
        }

        function isDealerPlusPage(){
            return userService.isDealerPlusUser() && $state.current.name.includes("customerOrders");
        }

    }
})();
