(function () {
    'use strict';

    angular
        .module('app.services')
        .factory('manufacturerProgressService', manufacturerProgressService);

    manufacturerProgressService.$inject = ['$http', 'apiConstants', 'userService'];

    function manufacturerProgressService($http, apiConstants, userService) {
        return {
            fetchProgress: fetchProgress,
            fetchViewableBOMProgress: fetchViewableBOMProgress,
            remove: remove
        };
        function fetchProgress(process) {
            var manufacturerId = userService.getManufacturerId();
            var paramString = "?";
            if (Array.isArray(process)) {
                for (var i = 0; i < process.length; i++) {
                    paramString = paramString + 'process=' + process[i] + '&';
                }
            } else {
                paramString = paramString + 'process=' + process;
            }
            return $http.get(apiConstants.url + '/manufacturer/progress/' + manufacturerId + paramString, null);
        }

        function fetchViewableBOMProgress(machineId, viewableId) {
            var manufacturerId = userService.getManufacturerId();
            var paramString = '';
            if (machineId && machineId > 0) {
                paramString = "&machineId=" + machineId;
            }
            if (viewableId && viewableId > 0) {
                paramString = paramString + "&modelId=" + viewableId;
            }

            var url = apiConstants.url + '/manufacturer/progress/' + manufacturerId + 
                '?process=VIEWABLE_BOM_EXPORT&process=VIEWABLE_BOM_UPLOAD&process=VIEWABLE_SPARE_PART_UPLOAD';
            return $http.get(url + paramString, null);
        }

        function remove(progressId) {
            $http.delete(apiConstants.url + '/manufacturer/progress/delete/' + progressId)
                .then(function (response) {
                    console.log('BOM removed successfully');
                })
                .catch(function (error) {
                    console.error('Failed to remove BOM:', error);
                });
        }
        
    }
})();
