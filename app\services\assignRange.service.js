(function () {
    'use strict';

    angular
        .module('app.services')
        .factory('assignRangeService', assignRangeService);

    assignRangeService.$inject = ['$http', 'apiConstants', 'userService'];

    function assignRangeService($http, apiConstants, userService) {
        return {

            assignRangeToManufacturerSubEntity: assignRangeToManufacturerSubEntity,
            getRangesForManufacturer: getRangesForManufacturer,
            getAssignedRangeIdsForSubEntity: getAssignedRangeIdsForSubEntity,
            assignRangesToCustomer: assignRangesToCustomer
        };

        function assignRangeToManufacturerSubEntity(range, manufacturerSubEntityId) {
            var customerData = [range];
            return $http.post(apiConstants.url + '/manufacturersubentity/' + manufacturerSubEntityId + '/range', customerData);
        }

        function getRangesForManufacturer() {
            var manufacturerId = userService.getManufacturerId();
            return $http.get(apiConstants.url + '/manufacturer/' + manufacturerId + '/ranges');
        }

        function getAssignedRangeIdsForSubEntity(subEntityId) {
            return $http.get(apiConstants.url + '/manufacturersubentity/' + subEntityId + '/assignedRangeIds');
        }

        function assignRangesToCustomer(manufacturerSubEntityId, publicationIds) {
            return $http.put(apiConstants.url + '/manufacturersubentity/' + manufacturerSubEntityId + '/ranges/assign', publicationIds);
        }



    }
})();
