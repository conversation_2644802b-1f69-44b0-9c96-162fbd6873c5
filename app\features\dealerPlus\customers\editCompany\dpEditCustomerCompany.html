
<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" ng-click="dpEditCustomerCompanyCtrl.cancel()"
            aria-label="Close"><i
            class="fa fa-close" aria-hidden="true"></i></button>
    <h2 class="modal-title" translate>EDIT_COMPANY.TITLE</h2>
    <h3 ng-if="dpEditCustomerCompanyCtrl.companyFailure" class="error-alert">
        {{dpEditCustomerCompanyCtrl.internalFailureMessage}}</h3>
</div>


<div class="modal-body">
    <form class="form" name="createCompanyForm" ng-submit="dpEditCustomerCompanyCtrl.editCompany()" >
        <div class="input-group">
            <label translate>EDIT_COMPANY.COMPANY_NAME</label>
            <input type="text" placeholder="{{'EDIT_COMPANY.CREATE_NEW' | translate}}"
                   ng-model="dpEditCustomerCompanyCtrl.newCompanyName" ng-required="true">
        </div>

		<div class="input-group">
			  <label translate>EDIT_COMPANY.DEFAULT_DISCOUNT</label>
			  <input type="number" step="1" placeholder="{{'EDIT_COMPANY.ENTER_DISCOUNT' | translate}}"
				   ng-model="dpEditCustomerCompanyCtrl.newDefaultDiscount" ng-required="true">
		</div>

        <div class="modal-actions">

            <button type="button" class="btn small secondary" data-dismiss="modal"
                    ng-click="dpEditCustomerCompanyCtrl.cancel()" translate>
                GENERAL.CANCEL
            </button>

            <button type="submit" class="btn small primary"
                    ng-disabled="!createCompanyForm.$valid || dpEditCustomerCompanyCtrl.isDisabled" translate>
			  EDIT_COMPANY.EDIT_BUTTON
            </button>

        </div>
    </form>
</div>


