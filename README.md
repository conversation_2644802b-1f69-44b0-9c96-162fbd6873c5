# CADshare UI App - sourcetree is rotten

## Quick summary
This readme explains how to get up and running with the user interface application component of CADshare. This provides the Cadshare front end UI client, which interfaces with the rest-webservices backend services through API calls.

## JavaScript/TypeScript Hybrid
As an AngularJs app, the CADsahre web-app consists primarily of Js files. However, as we have latterly introduced TypeScript (for the obvious benefit of type-safety as well as object-orientation) we now have Ts files. The `/app` folder contains all Js & Ts source files, which are transpiled and copied to the `/dev` folder when running the app locally.

## Pre-requisites
* GIT (or a GIT client) installed
* Node Version Manager (nvm for Windows) installed for managing multiple versions of node

## Steps
1. Install Node (& npm)

```
nvm install 10.16.0 
nvm use 10.16.0
```

2. Use npm to bootstrap install additional package & depdendency managers & tools
```
npm install
```

3. Install additional package dependencies:
```
bower install
```

4a. Build the solution using gulp task manager
```
gulp clean-build-dev
```

5a. Run the app through a web server
```
http-server -a localhost -c-1 -o dev
```

or 

4b. Build and run together

```
gulp clean-build-dev && http-server -a localhost -c-1 -o dev
```


