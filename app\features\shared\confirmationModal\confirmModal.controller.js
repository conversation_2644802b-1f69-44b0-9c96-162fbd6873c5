(function () {
    'use strict';

    angular
        .module('app.shared')
        .controller('ConfirmModalController', ConfirmModalController);

    ConfirmModalController.$inject = ['$uibModalInstance', 'confirmObject', 'userService', '$state'];

    function ConfirmModalController($uibModalInstance, confirmObject, userService, $state) {
        var vm = this;

        vm.isDealerPlusPage = isDealerPlusPage;
        vm.confirm = confirm;
        vm.cancel = $uibModalInstance.dismiss;

        if(confirmObject){
            vm.titleText = confirmObject.titleText;
            vm.bodyText = confirmObject.bodyText;
        }

        function confirm() {
            $uibModalInstance.close();
        }

        function isDealerPlusPage(){
            return userService.isDealerPlusUser() && $state.current.name.includes("customerOrders");
        }
    }
})();
