(function () {
    'use strict';

    angular
        .module('app.viewer')
        .directive('createKit', createKit);

    function createKit() {
        var directive = {
            restrict: 'E',
            templateUrl: 'features/viewer/extensions/createKit/createKit.html',
            controller: 'CreateKitController',
            controllerAs: 'createKitCtrl',
            bindToController: true
        };
        return directive;
    }

})();