(function () {
    'use strict';

    angular
        .module('app.base')
        .controller('RegisterController', RegisterController);

    RegisterController.$inject = ['$uibModal', 'registerService', 'manufacturerService', '$scope', '$state'];

    function RegisterController($uibModal, registerService, manufacturerService, $scope, $state) {
        var vm = this;

        $scope.multiSelectEvent = { onItemSelect: onItemSelect};

        vm.deliveryAddress = null;
        vm.hasTaxExemptionCert = false;
        vm.machines = [];
        vm.submitting = false;
        vm.error = false;

        vm.manufacturerId = manufacturerService.getManufacturerId();

        vm.signUp = signUp;
        vm.updateAddress = updateAddress;
        vm.viewUpdateMachine = viewUpdateMachine;
        vm.onTaxExemptChanged = onTaxExemptChanged;
        vm.goToLogin = goToLogin;
        vm.submitted = false;

        function onTaxExemptChanged(hasTaxExemptionCert) {
            vm.hasTaxExemptionCert = hasTaxExemptionCert;
        }

        function updateAddress() {
            $uibModal.open({
                templateUrl: 'features/shared/createNewAddress/createNewAddress.html',
                controller: 'CreateNewAddressController',
                controllerAs: 'createNewAddressCtrl',
                resolve:
                {
                    customerUserId: function () {
                        return null;
                    },
                    isRegisterMode: function () {
                        return true;
                    },
                    addressData: function () {
                        if (!vm.deliveryAddress && vm.user) {
                            return { companyName: vm.user.companyName }
                        }
                        return vm.deliveryAddress;
                    },
                    showCompanyName: function() {
                        return false;
                    }
                }
            }).result.then(function (resp) {
                vm.deliveryAddress = resp;
                vm.deliveryAddressStr = `${resp.addressLine1}, ${resp.city}${resp.state ? ', ' + resp.state : ''}, ${resp.postcode}, ${resp.country}`
            });
        }

        function viewUpdateMachine() {
            $uibModal.open({
                templateUrl: 'features/base/register/machineModal/registerMachineModal.html',
                controller: 'RegisterMachineController',
                controllerAs: 'registMachineCtrl',
                resolve: {
                    machines: function () { return vm.machines || []; }
                }
            }).result.then(function (machines) {
                vm.machines = machines;
            });
        }

        function signUp() {
            $scope.registerForm.$submitted = true;
            vm.isSignUpError = false;
            if ($scope.registerForm.$invalid || !vm.machines.length || !vm.deliveryAddressStr) {
                vm.submitting = false;
                vm.error = true;
            } else if ($scope.registerForm.$valid || vm.machines.length || vm.deliveryAddressStr){
                vm.submitting = true;
                vm.error = false;
            }
            if ($scope.registerForm.$invalid || !vm.machines.length) return;
            const payload = {
                user: {
                    emailAddress: vm.user.emailAddress,
                    organisationName: vm.user.companyName,
                    firstName: vm.user.firstName,
                    lastName: vm.user.lastName,
                    mobilePhoneNumber: vm.user.contactNumber,
                    manufacturerId: vm.manufacturerId,
                },
                address: vm.deliveryAddress,
                taxExemption: vm.hasTaxExemptionCert,
                assignedManualIds: vm.machines.map(item => item.manualId),
            }
            registerService.signUp(payload).then(signUpSuccess, signUpFailed)
        }

        function signUpSuccess(res) {
            vm.submitting = false;
            $uibModal.open({
                templateUrl: 'features/base/register/registerComplete.html',
                scope: $scope
            });
        }

        function signUpFailed(error) {
            vm.submitting = false;
            vm.isSignUpError = true;
            vm.messageErrorLoginFail = "";
            if (error.status === 409) {
                vm.messageErrorLoginFail = error.data.message;
                vm.manufacturerSupportEmail = manufacturerService.getManufacturer().supportEmail;
            }
        }

        function goToLogin() {
            $state.go("login");
        }

        function onItemSelect(item) {
            const manual = vm.manuals.find(el => el.manualId === item.id);
            const machine = {
                id: manual.id,
                rangeId: vm.rangeSelected.rangeId,
                rangeName: vm.rangeSelected.name,
                manualId: manual.id,
                manualName: manual.manualName
            }
            vm.machines.push(machine);
        }

    }
})();
