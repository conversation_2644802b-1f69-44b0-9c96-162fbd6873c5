<form class="p-5 form needs-validation" name="createCompanyForm" novalidate>

    <div class="d-flex align-items-center justify-content-between" ng-if="!userSettingsCtrl.isCurrentUserId">
        <h1>
            <span ng-bind="userSettingsCtrl.firstName"></span>
            <span ng-bind="userSettingsCtrl.lastName"></span>
            <span>-</span>
            <span ng-bind="'CREATE_USER.USER_SETTINGS' | translate"></span>
        </h1>
    </div>

    <div class="d-flex align-items-center cadGap"
        ng-if="userSettingsCtrl.userId == userSettingsCtrl.userId">
        <h1 translate>CREATE_USER.CUSTOMER_ACCOUNT_SUBS
        </h1>
        <span class="tooltip-icon ng-scope mb-3" style="cursor: pointer;" data-toggle="tooltip" data-placement="top"
            title="{{'CREATE_USER.CUSTOMER_ACCOUNT_SUBS_TOOLTIP' | translate}}">
            <i class="fa fa-info-circle cadBlue" style="font-size: 1.25em;"></i>
        </span>
    

    </div>

    <div class="d-flex customDivStyling" ng-if="!userSettingsCtrl.isCurrentUserId">
        <div class="input-group mb-0">
            <label translate>CREATE_USER.FIRST_NAME</label>
            <input class="mb-0" type="text" name="firstName" placeholder="{{'CREATE_USER.FIRST_NAME' | translate}}"
                ng-model="userSettingsCtrl.firstName" ng-required="true"
                ng-class="{'is-invalid': createCompanyForm.firstName.$invalid && userSettingsCtrl.isFormSubmitted}" />
        </div>

        <div class="input-group mb-0">
            <label translate>CREATE_USER.LAST_NAME</label>
            <input class="mb-0" type="text" name="lastName" placeholder="{{'CREATE_USER.LAST_NAME' | translate}}"
                ng-model="userSettingsCtrl.lastName" ng-required="true"
                ng-class="{'is-invalid': createCompanyForm.lastName.$invalid && userSettingsCtrl.isFormSubmitted}" />
        </div>

        <div class="input-group mb-0">
            <label translate>CREATE_USER.EMAIL_ADDRESS</label>
            <input class="mb-0" type="email" name="emailAddress"
                placeholder="{{'CREATE_USER.EMAIL_ADDRESS' | translate}}" ng-model="userSettingsCtrl.emailAddress"
                ng-required="true"
                ng-class="{'is-invalid': createCompanyForm.emailAddress.$invalid && userSettingsCtrl.isFormSubmitted}" />
        </div>
        <div class="input-group mb-0" ng-if="userSettingsCtrl.isSupreme && userSettingsCtrl.isDealer">
            <label>Visibility Contact ID</label>
            <input class="mb-0" type="number" placeholder="Visibility Contact ID"
                ng-model="userSettingsCtrl.visContactId" ng-required="true" />
        </div>
    </div>

    <section class="d-flex cadGap cadTabs" ng-if="!userSettingsCtrl.isCurrentUserId">
        <div ng-click="userSettingsCtrl.showMenuAccessTab()" class="cadBulkHoverAlt"
            ng-class="{active: userSettingsCtrl.activeTab === 'menuAccess'}">
            <button class="link-button" href="javascript:void(0)" translate>CREATE_USER.MENU_ACCESS</button>
            <span class="tooltip-icon ng-scope" style="cursor: pointer;" data-toggle="tooltip" data-placement="top"
                title="{{'CREATE_USER.MENU_ACCESS_TOOLTIP' | translate}}">
                <i class="fa fa-info-circle cadBlue" style="font-size: 1.25em;"></i>
            </span>
        </div>
        <div ng-click="userSettingsCtrl.showPermissionsTab()" class="cadBulkHoverAlt"
            ng-class="{active: userSettingsCtrl.activeTab === 'permissions'}"
            ng-hide="userSettingsCtrl.isPriceListEnabled">
            <button class="link-button" href="javascript:void(0)" translate>CREATE_USER.PERMISSIONS</button>
            <span class="tooltip-icon ng-scope" style="cursor: pointer;" data-toggle="tooltip" data-placement="top"
                title="{{'CREATE_USER.PERMISSIONS_TOOLTIP' | translate}}">
                <i class="fa fa-info-circle cadBlue" style="font-size: 1.25em;"></i>
            </span>
        </div>
        <div ng-click="userSettingsCtrl.showAccountSubscriptionsTab()" class="cadBulkHoverAlt"
            ng-class="{active: userSettingsCtrl.activeTab === 'accountSubscriptions'}">
            <button class="link-button" href="javascript:void(0)" translate>CREATE_USER.CUSTOMER_ACCOUNT_SUBS</button>
            <span class="tooltip-icon ng-scope" style="cursor: pointer;" data-toggle="tooltip" data-placement="top"
                title="{{'CREATE_USER.CUSTOMER_ACCOUNT_SUBS_TOOLTIP' | translate}}">
                <i class="fa fa-info-circle cadBlue" style="font-size: 1.25em;"></i>
            </span>
        </div>
    </section>

    <div class="customDivStyling" ng-show="userSettingsCtrl.activeTab === 'menuAccess'"
        ng-if="!userSettingsCtrl.isCurrentUserId">
        <div ng-if="userSettingsCtrl.type === 'Internal'">
            <h3 class="group-title" translate>CREATE_USER.USER_PERMISSIONS</h3>

            <table class="simple-table">
                <thead>
                    <tr>
                        <th translate>CREATE_USER.PERMISSION</th>
                        <th translate>CREATE_USER.DESCRIPTION</th>
                        <th></th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong translate>CREATE_USER.ORDERS</strong></td>
                        <td translate>CREATE_USER.ORDERS_SUBTEXT</td>
                        <td>
                            <input type="checkbox" ng-model="userSettingsCtrl.permissions.orders" class="checkbox" />
                        </td>
                    </tr>
                    <tr>
                        <td><strong translate>CREATE_USER.PRODUCTS</strong></td>
                        <td translate>CREATE_USER.PRODUCTS_SUBTEXT</td>
                        <td>
                            <input type="checkbox" ng-model="userSettingsCtrl.permissions.products" class="checkbox" />
                        </td>
                    </tr>
                    <tr>
                        <td><strong translate>CREATE_USER.PUBLICATIONS</strong></td>
                        <td translate>CREATE_USER.PUBLICATIONS_SUBTEXT</td>
                        <td>
                            <input type="checkbox" ng-model="userSettingsCtrl.permissions.publications"
                                class="checkbox" />
                        </td>
                    </tr>
                    <tr>
                        <td><strong translate>CREATE_USER.CUSTOMERS</strong></td>
                        <td translate>CREATE_USER.CUSTOMERS_SUBTEXT</td>
                        <td>
                            <input type="checkbox" ng-model="userSettingsCtrl.permissions.customers" class="checkbox" />
                        </td>
                    </tr>
                    <tr>
                        <td><strong translate>CREATE_USER.ADMIN</strong></td>
                        <td translate>CREATE_USER.ADMIN_SUBTEXT</td>
                        <td>
                            <input type="checkbox" ng-model="userSettingsCtrl.permissions.admin" class="checkbox" />
                        </td>
                    </tr>
                    <tr>
                        <td><strong translate>CREATE_USER.SECURITY</strong></td>
                        <td translate>CREATE_USER.SECURITY_SUBTEXT</td>
                        <td>
                            <input type="checkbox" ng-model="userSettingsCtrl.permissions.security" class="checkbox" />
                        </td>
                    </tr>
                    <tr>
                        <td><strong translate>CREATE_USER.PARTS</strong></td>
                        <td translate>CREATE_USER.PARTS_SUBTEXT</td>
                        <td>
                            <input type="checkbox" ng-model="userSettingsCtrl.permissions.parts" class="checkbox" />
                        </td>
                    </tr>
                    <tr ng-show="userSettingsCtrl.dashboardEnabled">
                        <td><strong translate>CREATE_USER.DASHBOARD</strong></td>
                        <td translate>CREATE_USER.DASHBOARD_SUBTEXT</td>
                        <td>
                            <input type="checkbox" ng-model="userSettingsCtrl.permissions.dashboard" class="checkbox" />
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <div class="customDivStyling" ng-show="userSettingsCtrl.activeTab === 'permissions'"
        ng-if="!userSettingsCtrl.isCurrentUserId">
        <div class="mt-2" ng-if="userSettingsCtrl.type === 'Internal'" ng-show="userSettingsCtrl.isManufacturer">
            <h3 class="group-title" translate>CREATE_USER.USER_SETTINGS</h3>

            <table class="simple-table">
                <thead>
                    <tr>
                        <th translate>CREATE_USER.PERMISSION</th>
                        <th translate>CREATE_USER.DESCRIPTION</th>
                        <th></th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong translate>CREATE_USER.DISCOUNT</strong></td>
                        <td translate>CREATE_USER.DISCOUNT_SUBTEXT</td>
                        <td>
                            <input type="checkbox" ng-model="userSettingsCtrl.isDiscountEditable" class="checkbox" />
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <section class="responsiveContainer pb-0"
        ng-show="userSettingsCtrl.activeTab === 'accountSubscriptions' || userSettingsCtrl.isCurrentUserId">
        <div class="flex col-12 px-4 border-bottom">
            <div class="flex border-bottom">
                <div class="flex border-bottom">
                    <div class="d-flex align-items-center">
                        <search-filter state-name="'userSettings'" value="userSettingsCtrl.searchValue"
                            placeholder-key="ADMIN.SEARCH_BY_NAME"></search-filter>
                        <app-checkbox-component label="'CREATE_USER.SHOW_SELECTED_ONLY'"
                            is-checked="userSettingsCtrl.isShowSelectedOnlyChecked"
                            is-checked-change="userSettingsCtrl.toggleShowSelectedOnly(isChecked)"></app-checkbox-component>
                        <app-checkbox-component label="'CREATE_USER.SELECT_ALL'"
                            is-checked="userSettingsCtrl.isSelectAllChecked"
                            is-checked-change="userSettingsCtrl.toggleSelectAll(isChecked)"></app-checkbox-component>
                    </div>
                </div>
            </div>
        </div>

        <!--             <div class="d-flex align-items-center">
                        <app-checkbox-component class="ml-auto" label="'CREATE_USER.SUBSCRIBE_TO_ALL'"
                        is-checked="userSettingsCtrl.isSubscribeAllChecked"
                        is-checked-change="userSettingsCtrl.toggleSubscribeAll(isChecked)"></app-checkbox-component>
        
                        <span class="tooltip-icon mr-4 ng-scope" style="cursor: pointer;"
                        data-toggle="tooltip" data-placement="top"
                        title="{{'CREATE_USER.SUBSCRIBE_TO_ALL_TOOLTIP' | translate}}">
                        <i class="fa fa-info-circle cadBlue" style="font-size: 1.25em;"></i>
                        </span>
                    </div> -->

        <div class="p-4" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 16px;">
            <div ng-repeat="user in userSettingsCtrl.userList | filter : userSettingsCtrl.searchValue | filter : (userSettingsCtrl.isShowSelectedOnlyChecked ? {checked: true} : {})"
                style="display: flex; align-items: center;">
                <input type="checkbox" class="m-0" ng-model="user.checked"
                    id="checkbox_{{ user.manufacturerSubEntityId }}"
                    ng-change="userSettingsCtrl.handleCheckboxChange()">
                <label for="checkbox_{{ user.manufacturerSubEntityId }}" style="margin-left: 8px;">{{ user.name
                    }}</label>
            </div>
        </div>
    </section>

    <div class="modal-actions mt-2 float-right">
        <button type="button" class="btn secondary ml-0 ml-lg-2" data-dismiss="modal"
            ng-click="userSettingsCtrl.cancel()" translate>
            GENERAL.CANCEL
        </button>
        <button type="button" class="btn primary ml-0 mt-lg-0"
            ng-disabled="userSettingsCtrl.isFormSubmitted && !createCompanyForm.$valid"
            ng-click="userSettingsCtrl.saveUser(createCompanyForm)" translate>
            CREATE_USER.SAVE
        </button>
    </div>
</form>