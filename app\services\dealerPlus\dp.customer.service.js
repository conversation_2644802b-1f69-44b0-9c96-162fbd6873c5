(function () {
        'use strict';

        angular
            .module('app.services')
            .factory('dpCustomerService', dpCustomerService);

        dpCustomerService.$inject = ['$http', 'apiConstants', 'userService'];

        function dpCustomerService($http, apiConstants, userService) {
            var dealerPlusPrefix = "/dealerplus";
            return {
                getCustomers: getCustomers
            };

            function getCustomers() {
                var subEntityid = userService.getManufacturerSubEntityId();
                return $http.get(apiConstants.url + dealerPlusPrefix + '/dealer/' + subEntityid + '/manufacturersubentities')
            }

        }

    }

)();