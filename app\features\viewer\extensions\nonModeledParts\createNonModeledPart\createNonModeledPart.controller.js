(function () {
    'use strict';

    angular
        .module('app.viewer')
        .controller('CreateNonModeledPartController', CreateNonModeledPartController);

    CreateNonModeledPartController.$inject = ['nonModeledPartService', '$stateParams', '$scope', 'viewerService', 'viewerBannerService', '$rootScope', 'viewerHelperService', 'optionsSetService', '$translate'];

    function CreateNonModeledPartController(nonModeledPartService, $stateParams, $scope, viewerService, viewerBannerService, $rootScope, viewerHelperService, optionsSetService, $translate) {
        var vm = this;

        vm.isOpen = false;
        vm.nonModeledParts = [];
        vm.selectedPart = [];
        vm.optionsSetIds = [];
        vm.isEdit = false;
        vm.modelId = $stateParams.modelId;

        vm.validateNonModelledParts = validateNonModelledParts;
        vm.saveNonModeledPart = saveNonModeledPart;
        vm.cancel = cancel;
        vm.removePart = removePart;
        vm.addAnotherPart = addAnotherPart;

        var CREATE_SUCCESS;
        $translate(['CREATE_NON_MODELLED.CREATE_SUCCESS'])
            .then(function (resp) {
                CREATE_SUCCESS = resp["CREATE_NON_MODELLED.CREATE_SUCCESS"];
            });

        initialize();

        function initialize(nonModeledPartId) {
            if (nonModeledPartId) {
                vm.isEdit = true;
                fetchNonModeledPart(nonModeledPartId);
            } else {
                vm.isEdit = false;
                vm.nonModeledParts = [{partDescription: "", partNumber: ""}, {partDescription: "", partNumber: ""}];
            }
        }

        function fetchNonModeledPart(nonModeledPartId) {
            nonModeledPartService.fetchNonModeledPart(nonModeledPartId)
                .then(fetchNonModeledPartSuccess, serviceCallFailed);
        }

        function fetchNonModeledPartSuccess(response) {
            vm.nonModeledParts = response.data.parts;
            viewerHelperService.selectParts([response.data.objectId]);
        }

        function addAnotherPart() {
            vm.nonModeledParts.push({partDescription: "", partNumber: ""});
        }

        function removePart(index) {
            vm.nonModeledParts.splice(index, 1);
            if (vm.nonModeledParts.length < 1) {
                addAnotherPart();
            }
        }

        function cancel() {
            vm.nonModeledParts = [];
            vm.isOpen = false;
            $rootScope.$broadcast("create-non-modeled-parts-closed");
        }

        function validateNonModelledParts() {
            if (vm.nonModeledParts.length >= 1) {
                for (var i = 0; i < vm.nonModeledParts.length; i++) {
                    if (vm.nonModeledParts[i].partDescription === "" || vm.nonModeledParts[i].partNumber === "") {
                        return false;
                    }
                }
            } else {
                return false;
            }
            return true;
        }

        function hasDuplicatePartNumbers() {
            for (var i = 0; i < vm.nonModeledParts.length; i++) {
                for (var j = 0; j < vm.nonModeledParts.length; j++) {
                    if (i !== j && vm.nonModeledParts[i].partNumber !== "" && vm.nonModeledParts[j].partNumber !== "") {
                        if (vm.nonModeledParts[i].partNumber === vm.nonModeledParts[j].partNumber) {
                            return true;
                        }
                    }
                }
            }
            return false;
        }

        function saveNonModeledPart() {
            vm.errors = {};
            vm.errors.noPartSelected = vm.selectedPart.partId === undefined;
            vm.errors.notEnoughParts = !validateNonModelledParts();
            vm.errors.duplicatePartNumber = hasDuplicatePartNumbers();

            if (vm.errors.noPartSelected || vm.errors.notEnoughParts || vm.errors.duplicatePartNumber) {
                return;
            }

            if (vm.isEdit) {
                nonModeledPartService.editNonModeledPart(vm.nonModeledPartId, vm.selectedPart.partId, vm.nonModeledParts)
                    .then(saveNonModeledPartSuccess, serviceCallFailed);
            } else {
                nonModeledPartService.createNonModeledPart(vm.modelId, vm.selectedPart.partId, vm.nonModeledParts)
                    .then(saveNonModeledPartSuccess, serviceCallFailed);
            }
            if (vm.hasOptionsSet) {
                for (var i = 0; i < vm.optionsSetIds.length; i++) {
                    optionsSetService.deleteOptionsSet(vm.optionsSetIds[i]).then(function () {
                        $rootScope.$broadcast("optionsSet-updated");
                    });
                }
            }
        }

        function saveNonModeledPartSuccess() {
            viewerBannerService.setNotification("SUCCESS", CREATE_SUCCESS);
            vm.isOpen = false;
            $rootScope.$broadcast("create-non-modeled-parts-closed");
        }

        function serviceCallFailed(error) {
            viewerBannerService.setNotification("ERROR", error.error);
        }

        $scope.$on("viewer-part-selected", function (event, partViewerDetails) {
            if (partViewerDetails.length === 1) {
                vm.selectedPart = partViewerDetails[0].part;
                if (partViewerDetails.nonModelled) {
                    vm.hasNonModeled = partViewerDetails.nonModelled;
                    vm.optionsSetDescription = "";
                    vm.optionsSetIds = [];
                    for (var i = 0; i < partViewerDetails.optionsSet.length; i++) {
                        vm.optionsSetDescription = vm.optionsSetDescription + partViewerDetails.optionsSet[i].description;
                        vm.optionsSetDescription = (i === partViewerDetails.optionsSet.length - 1) ? vm.optionsSetDescription : vm.optionsSetDescription + ", ";
                        vm.optionsSetIds.push(partViewerDetails.optionsSet[i].id);
                    }
                } else {
                    vm.optionsSetDescription = "";
                    vm.optionsSetIds = [];
                    vm.hasNonModeled = false;
                }
            } else if (partViewerDetails.length > 1) {
                vm.selectedPart = partViewerDetails;
            } else {
                vm.selectedPart = [];
            }
        });


        $scope.$on("create-non-modeled-parts-opened", function (event, nonModeledPartId) {
            vm.isOpen = true;
            vm.nonModeledPartId = nonModeledPartId;
            initialize(vm.nonModeledPartId);
        });
    }

})();
