(function () {
    'use strict';

    angular
        .module('app.viewer')
        .controller('ViewerSettingsController', ViewerSettingsController);

  ViewerSettingsController.$inject = ['viewerSettingsService', 'viewerBannerService', '$stateParams', '$rootScope'];

    function ViewerSettingsController(viewerSettingsService, viewerBannerService, $stateParams, $rootScope) {
        var vm = this;

        vm.isOpen = true;

        vm.toggleLineDrawingEnabled = toggleLineDrawingEnabled;
        vm.toggleEdgingEnabled = toggleEdgingEnabled;
        vm.toggleViewLocked = toggleViewLocked;

        initialize();

      function initialize() {
        vm.modelId = $stateParams.modelId;
      }

        function updateViewerSettings() {
                viewerSettingsService.updateViewerSettings(vm.modelId, vm.data);
                $rootScope.$broadcast("viewer-settings-changed", vm.data);
        }

        function toggleLineDrawingEnabled() {
            updateViewerSettings()
        }

        function toggleEdgingEnabled() {
            updateViewerSettings()
        }

        function toggleViewLocked() {
          updateViewerSettings()
        }

        $rootScope.$on("viewer-settings-loaded", function (event, viewerSettings) {
            vm.data = {};
            vm.data.lineDrawingEnabled = viewerSettings.lineDrawingEnabled;
            vm.data.edgingEnabled = viewerSettings.edgingEnabled;
            vm.data.viewLocked = viewerSettings.viewLocked;
        });
    }

})();
