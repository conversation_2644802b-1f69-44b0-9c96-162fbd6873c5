(function () {
  "use strict";

  angular
    .module("app.services")
    .factory("masterPartService", masterPartService);

  masterPartService.$inject = ["$http", "apiConstants", "$q"];

  function masterPartService($http, apiConstants, $q) {
    return {
      createMasterPart: createMasterPart,
      uploadLanguages: uploadLanguages,
      getMasterPartLanguagesHeaders: getMasterPartLanguagesHeaders,
      getMasterPartLanguages: getMasterPartLanguages,
      uploadInventory: uploadInventory,
      getInventoryHeaders: getInventoryHeaders,
      getInventory: getInventory,
      getPriceList: getPriceList,
      updatePrice: updatePrice,
      updateStock: updateStock,
      getLinkedTechDocsForPart: getLinkedTechDocsForPart,
      updateLinkedTechDocsForPart: updateLinkedTechDocsForPart,
      deleteMasterPart: deleteMasterPart,
      partSearch: partSearch,
      getMasterPartDetails: getMasterPartDetails,
      getStock: getStock,
      getPrice: getPrice,
      getKitsForMasterPart: getKitsForMasterPart,
      getKit: getKit,
      getKitsForManufacturer: getKitsForManufacturer,
      createKit: createKit,
      editKit: editKit,
      deleteKit: deleteKit,
      removePartFromKit: removePartFromKit,
      getOptionSetForMasterPart: getOptionSetForMasterPart,
      getOptionSet: getOptionSet,
      createOptionSet: createOptionSet,
      editOptionSet: editOptionSet,
      deleteOptionSet: deleteOptionSet,
      getAdditionalPart: getAdditionalPart,
      createAdditionalPart: createAdditionalPart,
      editAdditionalPart: editAdditionalPart,
      deleteAdditionalPart: deleteAdditionalPart,
      getLink: getLink,
      createLink: createLink,
      editLink: editLink,
      deleteLink: deleteLink,
      getSupersede: getSupersede,
      createSupersede: createSupersede,
      editSupersede: editSupersede,
      deleteSupersede: deleteSupersede,
      getTranslations: getTranslations,
      createTranslation: createTranslation,
      updateTranslation: updateTranslation,
      deleteTranslation: deleteTranslation,
      getModelsForParts: getModelsForParts,
      uploadPriceList: uploadPriceList,
      getBasketWarehouseStock: getBasketWarehouseStock,
      getBasketPartsWeight: getBasketPartsWeight,
      manufacturerPartSearch: manufacturerPartSearch,
      subEntityPartSearch: subEntityPartSearch,
      getPublishedModelsForPart: getPublishedModelsForPart,
      updatePartNote: updatePartNote,
      deletePartNote: deletePartNote,
      getPurchaserKit: getPurchaserKit,
      getManufacturerKit: getManufacturerKit,
      purchaserKitSearch: purchaserKitSearch,
      supersedePart: supersedePart,
      removePartFromSupersession: removePartFromSupersession,
      splitPartFromSupersession: splitPartFromSupersession,
      getSupersessionHistory: getSupersessionHistory,
      getSupersessionHistoryForPurchaser: getSupersessionHistoryForPurchaser,
      manufacturerKitSearch: manufacturerKitSearch
    };

    function updatePartNote(masterPartId, note) {
      var updateNote =
        apiConstants.url + "/masterPart/" + masterPartId + "/note";
      return $http.put(updateNote, { note: note });
    }

    function deletePartNote(masterPartId) {
      var deleteNote =
        apiConstants.url + "/masterPart/" + masterPartId + "/note";
      return $http.delete(deleteNote);
    }

    function uploadLanguages(file) {
      var fd = new FormData();
      fd.append("file", file);
      var uploadUrl = apiConstants.url + "/translation";
      return $http.post(uploadUrl, fd, {
        transformRequest: angular.identity,
        headers: { "Content-Type": undefined },
      });
    }

    function uploadInventory(file) {
      var fd = new FormData();
      fd.append("file", file);
      var uploadUrl = apiConstants.url + "/inventory";
      return $http.post(uploadUrl, fd, {
        transformRequest: angular.identity,
        headers: { "Content-Type": undefined },
      });
    }

    function getMasterPartLanguagesHeaders() {
      return $http.get(apiConstants.url + "/masterPartDownload/headers");
    }

    function getMasterPartLanguages() {
      return $http.get(apiConstants.url + "/masterPartDownload");
    }

    function getInventoryHeaders() {
      return $http.get(apiConstants.url + "/inventory/headers");
    }

    function getInventory() {
      return $http.get(apiConstants.url + "/inventory");
    }

    function getPriceList() {
      return $http.get(apiConstants.url + "/priceList");
    }

    function partSearch(
      manufacturerId,
      manufacturerSubEntityId,
      searchParam,
      searchBy,
      onBehalfOfUserId,
      isExactMatch
    ) {
      if (manufacturerId && manufacturerSubEntityId) {
        return subEntityPartSearch(
          manufacturerSubEntityId,
          searchBy === "partNumber" ? searchParam : null,
          searchBy === "description" ? searchParam : null,
          onBehalfOfUserId,
          isExactMatch
        );
      }

      if (manufacturerId && !manufacturerSubEntityId) {
        return manufacturerPartSearch(
          manufacturerId,
          searchBy === "description" ? searchParam : null,
          searchBy === "partNumber" ? searchParam : null,
          isExactMatch
        );
      }
    }

    function manufacturerPartSearch(
      manufacturerId,
      partDescription,
      partNumber,
      isExactMatch
    ) {
      return $http.post(
        apiConstants.url +
          "/manufacturers/" +
          manufacturerId +
          "/master-parts/search",
        {
          partNumber: partNumber,
          partDescription: partDescription,
          exactMatch: isExactMatch,
        }
      );
    }
    
    function manufacturerKitSearch(manufacturerId, partNumber, partDescription) {
      console.log('Performing manufacturer kit search:', { manufacturerId, partNumber, partDescription });
      
      return $http.post(
        apiConstants.url + "/manufacturers/" + manufacturerId + "/master-part-kits/search",
        {
          partNumber: partNumber,
          partDescription: partDescription,
        }
      ).then(function(response) {
        console.log('manufacturer kit search response:', response);
        return response;
      }).catch(function(error) {
        console.error('manufacturer kit search error:', error);
        throw error;
      });
    }
    
    function subEntityPartSearch(
      manufacturerSubEntityId,
      partNumber,
      partDescription,
      onBehalfOfUserId,
      isExactMatch
    ) {
      return $http.post(
        apiConstants.url +
          "/manufacturer-sub-entities/" +
          manufacturerSubEntityId +
          "/master-parts/search",
        {
          partNumber: partNumber,
          partDescription: partDescription,
          onBehalfOfUserId: onBehalfOfUserId,
          exactMatch: isExactMatch,
        }
      ).then(function(response) {
        console.log('sub entity part search response:', response);
        return response;
      }).catch(function(error) {
        console.error('sub entity part search error:', error);
        throw error;
      });
    }

    function purchaserKitSearch(purchaserId, partNumber, partDescription) {
      
      return $http.post(
        apiConstants.url + "/purchasers/" + purchaserId + "/master-part-kits/search",
        {
          partNumber: partNumber,
          partDescription: partDescription,
        }
      ).then(function(response) {
        return response;
      }).catch(function(error) {
        console.error('Purchaser kit search error:', error);
        throw error;
      });
    }

    function getMasterPartDetails(masterPartId) {
      return $http.get(apiConstants.url + "/masterPart/" + masterPartId);
    }

    function getStock(masterPartId) {
      return $http.get(
        apiConstants.url + "/masterPart/" + masterPartId + "/stock"
      );
    }

    function getPrice(masterPartId) {
      return $http.get(
        apiConstants.url + "/masterPart/" + masterPartId + "/price"
      );
    }

    function updatePrice(masterPartId, price) {
      return $http.put(
        apiConstants.url + "/masterPart/" + masterPartId + "/price",
        { price: price }
      );
    }

    function updateStock(masterPartId, stock) {
      return $http.put(
        apiConstants.url + "/masterPart/" + masterPartId + "/stock",
        { stock: stock }
      );
    }

    function getLinkedTechDocsForPart(masterPartId) {
      return $http.get(
        apiConstants.url + "/masterPart/" + masterPartId + "/techDoc"
      );
    }

    function updateLinkedTechDocsForPart(masterPartId, techDocIds) {
      return $http.put(
        apiConstants.url + "/masterPart/" + masterPartId + "/techDoc",
        techDocIds
      );
    }

    function deleteMasterPart(masterPartId) {
      return $http.delete(apiConstants.url + "/masterPart/" + masterPartId);
    }

    function getLink(masterPartId) {
      return $http.get(
        apiConstants.url + "/masterPart/" + masterPartId + "/linkedPart"
      );
    }

    function createLink(masterPartId, modelId) {
      return $http.post(
        apiConstants.url + "/masterPart/" + masterPartId + "/linkedPart",
        { modelId: modelId }
      );
    }

    function editLink(masterPartId, modelId) {
      return $http.put(
        apiConstants.url + "/masterPart/" + masterPartId + "/linkedPart",
        { modelId: modelId }
      );
    }

    function deleteLink(masterPartId) {
      return $http.delete(
        apiConstants.url + "/masterPart/" + masterPartId + "/linkedPart"
      );
    }

    function getSupersede(masterPartId) {
      return $http.get(
        apiConstants.url + "/masterPart/" + masterPartId + "/superseded"
      );
    }

    function createSupersede(masterPartId, modelId) {
      return $http.post(
        apiConstants.url + "/masterPart/" + masterPartId + "/superseded",
        { modelId: modelId }
      );
    }

    function editSupersede(masterPartId, modelId) {
      return $http.put(
        apiConstants.url + "/masterPart/" + masterPartId + "/superseded",
        { modelId: modelId }
      );
    }

    function deleteSupersede(masterPartId) {
      return $http.delete(
        apiConstants.url + "/masterPart/" + masterPartId + "/superseded"
      );
    }

    function getTranslations(masterPartId) {
      return $http.get(
        apiConstants.url + "/masterPart/" + masterPartId + "/translation"
      );
    }

    function createTranslation(masterPartId, languageId, description) {
      return $http.post(
        apiConstants.url +
          "/masterPart/" +
          masterPartId +
          "/translation/language/" +
          languageId,
        { description: description }
      );
    }

    function updateTranslation(masterPartId, languageId, description) {
      return $http.put(
        apiConstants.url +
          "/masterPart/" +
          masterPartId +
          "/translation/language/" +
          languageId,
        { description: description }
      );
    }

    function deleteTranslation(masterPartId, languageId) {
      return $http.delete(
        apiConstants.url +
          "/masterPart/" +
          masterPartId +
          "/translation/language/" +
          languageId
      );
    }

    function getKitsForMasterPart(masterPartId) {
      return $http.get(
        apiConstants.url + "/masterPart/" + masterPartId + "/kit"
      );
    }

    function getKit(kitId) {
      return $http.get(apiConstants.url + "/masterPart/kit/" + kitId);
    }

    function getKitsForManufacturer(manufacturerId) {
      return $http.get(
        apiConstants.url + "/manufacturer/" + manufacturerId + "/kits"
      );
    }

    function createKit(masterPartId, title, description, kitParts) {
      return $http.post(
        apiConstants.url + "/masterPart/" + masterPartId + "/kit",
        {
          title: title,
          description: description,
          parts: kitParts,
        }
      );
    }

    function editKit(title, description, parts, kitId) {
      var updatedKit = {
        title: title,
        description: description,
        parts: parts,
      };
      return $http.put(
        apiConstants.url + "/masterPart/kit/" + kitId,
        updatedKit
      );
    }

    function deleteKit(kitId) {
      return $http.delete(apiConstants.url + "/masterPart/kit/" + kitId);
    }

    function removePartFromKit(kitId, masterPartId) {
      return $http.delete(
        apiConstants.url +
          "/masterPart/kit/" +
          kitId +
          "/masterPart/" +
          masterPartId
      );
    }

    function getOptionSetForMasterPart(masterPartId) {
      return $http.get(
        apiConstants.url + "/masterPart/" + masterPartId + "/optionSet"
      );
    }

    function getOptionSet(optionId) {
      return $http.get(apiConstants.url + "/masterPart/optionSet/" + optionId);
    }

    function createOptionSet(masterPartId, description, optionSet) {
      return $http.post(
        apiConstants.url + "/masterPart/" + masterPartId + "/optionSet",
        {
          description: description,
          optionsSet: optionSet,
        }
      );
    }

    function editOptionSet(description, optionSet, optionId) {
      var updatedOptionSet = {
        description: description,
        optionsSet: optionSet,
        optionId: optionId,
      };
      return $http.put(
        apiConstants.url + "/masterPart/optionSet/" + optionId,
        updatedOptionSet
      );
    }

    function deleteOptionSet(optionId) {
      return $http.delete(
        apiConstants.url + "/masterPart/optionSet/" + optionId
      );
    }

    function createAdditionalPart(masterPartId, additionalParts) {
      return $http.post(
        apiConstants.url + "/masterPart/" + masterPartId + "/nonModelled",
        {
          nonModelledPart: additionalParts,
        }
      );
    }

    function editAdditionalPart(masterPartId, additionalPart) {
      return $http.put(
        apiConstants.url + "/masterPart/" + masterPartId + "/nonModelled/",
        {
          nonModelledPart: additionalPart,
        }
      );
    }

    function deleteAdditionalPart(masterPartId) {
      return $http.delete(
        apiConstants.url + "/masterPart/" + masterPartId + "/nonModelled"
      );
    }

    function getAdditionalPart(masterPartId) {
      return $http.get(
        apiConstants.url + "/masterPart/" + masterPartId + "/nonModelled"
      );
    }

    function getModelsForParts(masterPartId) {
      return $http.get(
        apiConstants.url + "/masterPart/" + masterPartId + "/models"
      );
    }

    function getPublishedModelsForPart(subEntityId, partNumber) {
      return $http.post(
        apiConstants.url + "/dealers/" + subEntityId + "/model-search",
        { partNumber: partNumber }
      );
    }

    function createMasterPart(partNumber, description, languageId) {
      var createObject = {
        partNumber: partNumber,
        translations: [
          {
            languageId: languageId,
            description: description,
          },
        ],
      };
      return $http.post(apiConstants.url + "/masterPart/", createObject);
    }

    function uploadPriceList(file) {
      var fd = new FormData();
      fd.append("file", file);
      var uploadUrl = apiConstants.url + "/priceList";
      return $http.post(uploadUrl, fd, {
        transformRequest: angular.identity,
        headers: { "Content-Type": undefined },
      });
    }

    function getBasketWarehouseStock(manufacturerId, masterPartIds) {
      return $http.put(
        apiConstants.url +
          "/manufacturers/" +
          manufacturerId +
          "/masterPartStock",
        masterPartIds
      );
    }

    function getBasketPartsWeight(masterPartIds) {
      return $http.put(apiConstants.url + "/masterPart/weight", masterPartIds);
    }

    function supersedePart(manufacturerId, masterPartId, supersedingPartNumber) {
      return $http.post(
        apiConstants.url + "/manufacturers/" + manufacturerId + "/master-parts/" + masterPartId + "/supersede",
        {
          supersedingMasterPartId: supersedingPartNumber
        }
      );
    }

    function removePartFromSupersession(manufacturerId, masterPartId) {
      return $http.post(
        apiConstants.url +
        "/manufacturers/" +
        manufacturerId +
        "/master-parts/" +
        masterPartId +
        "/supersession-remove-part"
      );
    }

    function splitPartFromSupersession(manufacturerId, masterPartId) {
      return $http.post(
        apiConstants.url +
        "/manufacturers/" +
        manufacturerId +
        "/master-parts/" +
        masterPartId +
        "/supersession-split"
      );
    }

    function getSupersessionHistory(manufacturerId, masterPartId) {
      return $http.get(
        apiConstants.url +
        "/manufacturers/" +
        manufacturerId +
        "/master-parts/" +
        masterPartId +
        "/supersession-history"
      );
    }

    function getSupersessionHistoryForPurchaser(purchaserId, masterPartNumber) {
      return $http.get(apiConstants.url + "/purchasers/" + purchaserId + "/master-parts/" + masterPartNumber + "/supersession-history");
  }


    function getManufacturerKit(manufacturerId, kitId) {
      var url = apiConstants.url + '/manufacturers/' + manufacturerId + '/master-part-kits/' + kitId;
      return $http.get(url);
    }

    function getPurchaserKit(purchaserId, kitId) {
      var url = apiConstants.url + '/purchasers/' + purchaserId + '/master-part-kits/' + kitId;
      return $http.get(url);
    }
  }
})();
