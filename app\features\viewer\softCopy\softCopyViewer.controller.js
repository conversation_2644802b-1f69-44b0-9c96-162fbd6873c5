var LEADER_LIST;

(function () {
        'use strict';

        angular
            .module('app.viewer')
            .controller('SoftCopyViewerController', SoftCopyViewerController);

        SoftCopyViewerController.$inject = ['viewerSettingsService', 'viewerService', '$q', '$uibModal',
            '$stateParams', '$state', '$scope', '$window', 'viewerHelperService', '$timeout', 'viewerVariablesService',
            '$rootScope', 'viewerBannerService', 'tokenService', 'userService', 'apiConstants', 'modelService'];

        function SoftCopyViewerController(viewerSettingsService, viewerService, $q, $uibModal, $stateParams, $state,
            $scope, $window, viewerHelperService, $timeout, viewerVariablesService, $rootScope, viewerBannerService,
            tokenService, userService, apiConstants, modelService) {

            var vm = this;
            var viewerApp;

            var options = viewerService.getAutodeskToken()
                .then(function (resp) {
                    options = resp;
                    options.useADP = false;
                    if ($stateParams.translateType === "SVF2") {
                        options.env = 'AutodeskProduction2';
                        options.api = 'streamingV2';
                    }
                    initialize();
                });

            var parentId = "ROOT";
            var BASE_STEP = {stateName: "Reset to Top Level", stateId: "ROOT"};
            var leafNodes = [];
            var priorSelection = [];
            var customWeldments = [];
            var statesReturned = false;
            var viewerLoaded = false;
            var isSetupComplete = false;
            var translateToolBtn;
            var isGhosted = false;
            var isOverwriteSnapshot = false;
            var viewer = {};
            var customsSettingsAppliedFlag = false;
            var currentSnapshot = {};
            var isCtrlPressed = false;

            vm.isLinkModelPartActive = false;
            vm.showLinkPartModel = false;
            vm.modelId = $stateParams.modelId;
            vm.modelName = $stateParams.modelName;
            vm.productId = $stateParams.productId;
            vm.model = {};
            vm.isNonModeledPartsActive = false;
            vm.currentList = [];
            vm.snapshots = [];
            vm.steps = [BASE_STEP];
            vm.viewerMessage = '';
            vm.showCreateNewPart = false;
            vm.machineName = $stateParams.machineName;
            vm.viewableName = $stateParams.viewableName;
            vm.isExplodeSliderVisible = false;
            vm.viewerSettings = {};
            vm.selectedParts = [];
            vm.partDetailsOpen = false;
            vm.isPartEditMode = false;
            vm.partEdits = {};
            vm.leaderTable = [];
            vm.kitTable = [];
            vm.isPartsVisible = false;
            vm.isKitsVisible = false;

            vm.saveSnapshot = saveSnapshot;
            vm.loadSnapshot = loadSnapshot;
            vm.editSnapshotName = editSnapshotName;
            vm.deleteSnapshot = deleteSnapshot;
            vm.weldmentsUpdated = weldmentsUpdated;
            vm.overwriteSnapshot = overwriteSnapshot;
            vm.backToSoftCopy = backToSoftCopy;
            vm.showSpinner = showSpinner;
            vm.hideSpinner = hideSpinner;
            vm.disableTranslate = disableTranslate;
            vm.deactivateExplodeTool = deactivateExplodeTool;
            vm.addLeaderToTable = addLeaderToTable;
            vm.deleteLeaderFromTable = deleteLeaderFromTable;
            vm.toggleKitsVisibility = toggleKitsVisibility;
            vm.togglePartsVisibility = togglePartsVisibility;
            vm.notNull = notNull;

            viewerVariablesService.setModelId($stateParams.modelId);

            function getModel() {
                modelService.fetchModel(vm.modelId).then(getModelSuccess);
            }

            function getModelSuccess(response) {
                vm.model = response.data;
                leafNodes = JSON.parse(response.data.leafNodes) || [];
                isSetupComplete = response.data.isSetupComplete;
            }

            function getSnapshots() {
                viewerService.getSoftCopyViewableBySoftCopyId($stateParams.viewableId)
                    .then(geViewableSuccess);
            }

            function initialize() {
                getModel();
                viewerHelperService.showSpinner();
                autodeskViewerInitialize();

                getSnapshots();
            }

            function autodeskViewerInitialize() {

                $scope.$on('$locationChangeStart', function (event) {
                    if (!viewerLoaded) {
                        event.preventDefault();
                        console.log("Viewable loading back navigation disabled");
                    } else {
                        console.log("Viewer Shutdown On Navigation");
                    }
                });

                Autodesk.Viewing.Initializer(options, function onInitialized() {
                    var config3d = {
                        extensions: ['CADShareExtension', 'Autodesk.NPR']
                    };
                    viewerApp = new Autodesk.Viewing.ViewingApplication('MyViewerDiv');
                    viewerApp.registerViewer(viewerApp.k3D, Autodesk.Viewing.GuiViewer3D, config3d);

                    var documentId = "urn:" + $stateParams.autodeskURN;
                    viewerApp.loadDocument(documentId, onDocumentLoadSuccess, loadFailure);
                });
            }

            function getInitialStateDetails() {
                getViewableStateDetailPlusChildren('ROOT').then(function (data) {
                    addReturnedStateDetails(data);
                    getStateDetailsSuccess();
                })
            }

            function getViewableStateDetailPlusChildren(stateId) {
                return viewerService.getViewableStateDetails(vm.viewableId, stateId, 1).then(
                    addReturnedStateDetails);
            }

            function addReturnedStateDetails(stateDetails) {
                if (stateDetails) {
                    for (var i = 0; i < stateDetails.length; i++) {
                        if (!findSnapshotByStateId(stateDetails[i].stateId)) {
                            vm.snapshots.push(stateDetails[i]);
                        }
                    }
                    refreshSnapshotList();
                }
            }

            function getStateDetailsSuccess() {
                statesReturned = true;
                $rootScope.$broadcast('loading-complete');
            }

            function geViewableSuccess(data) {
                if(data) {
                    vm.viewerSettings.lineDrawingEnabled = data.lineDrawingEnabled;
                    vm.viewerSettings.edgingEnabled = data.edgingEnabled;
                    vm.viewerSettings.viewLocked = data.viewLocked;
                    vm.viewableId = data.id;

                    $rootScope.$broadcast("viewer-settings-loaded", vm.viewerSettings);
                }

                getInitialStateDetails();
            }

            function onDocumentLoadSuccess() {
                var viewables = viewerApp.bubble.search({'type': 'geometry'});

                if (viewables.length === 0) {
                    console.error('Document contains no viewables.');
                    return;
                }

                viewerApp.selectItem(viewables[0].data);
                viewerApp.getCurrentViewer().addEventListener(Autodesk.Viewing.SELECTION_CHANGED_EVENT,
                    onSelectionChanged);
                viewerApp.getCurrentViewer().addEventListener(Autodesk.Viewing.GEOMETRY_LOADED_EVENT,
                    onViewerLoadedSuccess);

                viewerHelperService.setViewerApp(viewerApp.getCurrentViewer());
                viewerApp.getCurrentViewer()._hotkeyManager.popHotkeys('Autodesk.Escape');
            }

            $scope.$on('model-tree-initialized', function (event, modelTree) {
                vm.modelTree = modelTree;
                viewerHelperService.setPartTreeTopId(vm.modelTree[0].objectId);
            });

            function loadFailure(viewerErrorCode) {
                console.error('onDocumentLoadFailure() - errorCode:' + viewerErrorCode);
                viewerHelperService.hideSpinner();
            }

            function applyDefaultSettings(viewer) {
                var bgColour = viewerHelperService.getBackgroundColour();
                viewer.setBackgroundColor(bgColour.r, bgColour.g, bgColour.b, bgColour.r, bgColour.g, bgColour.b);
                viewer.setTheme("light-theme");
                viewer.setEnvMapBackground(false);
                viewer.setGroundReflection(false);
                viewer.setGroundShadow(false);
                viewer.setOptimizeNavigation(false);
                viewer.hidePoints(true);
                viewer.hideLines(true);
                viewer.setProgressiveRendering(true);
                viewer.setQualityLevel(false, false);
                viewer.setGhosting(isGhosted);
                viewer.impl.selectionMaterialTop.opacity = 0;
            }

            function getSnapshotViewport(snapshot) {
                return JSON.parse(snapshot.state).viewport;
            }

            function viewportsAreEquivalent(currentViewport, snapshotViewport) {
                var currentViewportDefiningFields =
                    {
                        distanceToOrbit: currentViewport.distanceToOrbit,
                        eye: currentViewport.eye,
                        target: currentViewport.target
                    };

                var snapshotViewportDefiningFields =
                    {
                        distanceToOrbit: snapshotViewport.distanceToOrbit,
                        eye: snapshotViewport.eye,
                        target: snapshotViewport.target
                    };

                return _.isEqual(snapshotViewportDefiningFields,
                    currentViewportDefiningFields);
            }

            function onFinalRender() {
                if (!customsSettingsAppliedFlag) {
                    var currentViewport = viewerApp.getCurrentViewer().getState().viewport;
                    var snapshotViewport = getSnapshotViewport(currentSnapshot);

                    if (viewportsAreEquivalent(currentViewport, snapshotViewport)) {
                        viewerHelperService.applyCustomViewerSettings(viewerApp.getCurrentViewer(), vm.viewerSettings);
                        customsSettingsAppliedFlag = true;
                        viewerApp.getCurrentViewer().removeEventListener(
                            Autodesk.Viewing.FINAL_FRAME_RENDERED_CHANGED_EVENT, onFinalRender);
                    }
                }
            }

            function configureViewerAndToolbar(viewer) {
                applyDefaultSettings(viewer);

                var toolbar = viewer.toolbar;

                var navTools = toolbar.getControl('navTools');
                navTools.removeControl('toolbar-bimWalkTool');

                var modelTools = toolbar.getControl('modelTools');
                modelTools.removeControl('toolbar-measurementSubmenuTool');
                modelTools.removeControl('toolbar-explodeTool');

                toolbar.removeControl("settingsTools");

                translateToolBtn = new Autodesk.Viewing.UI.Button('translate-tool');
                translateToolBtn.onClick = function (e) {
                    txTool = _translateTool.getName();

                    if (viewer.toolController.getActiveTool().activeName === txTool) {
                        viewer.toolController.deactivateTool(txTool);
                        translateToolBtn.removeClass('active');
                        translateToolBtn.addClass('inactive');
                    } else {
                        deactivateExplodeTool();
                        leaderToolDeactivated();
                        viewer.toolController.activateTool(txTool);
                        translateToolBtn.removeClass('inactive');
                        translateToolBtn.addClass('active');
                    }
                };
                translateToolBtn.addClass('translate-tool');
                translateToolBtn.setToolTip('Translate');

                var explodeToolBtn = new Autodesk.Viewing.UI.Button('explode-tool');
                explodeToolBtn.onClick = function (e) {
                    toggleExplodePartVisibility();
                };
                explodeToolBtn.addClass('adsk-icon-explode');
                explodeToolBtn.setToolTip('Explode parts');

                var ghostToolBtn = new Autodesk.Viewing.UI.Button('ghosting-tool');
                ghostToolBtn.onClick = function (e) {
                    toggleGhosting();

                    if (isGhosted) {
                        ghostToolBtn.removeClass('inactive');
                        ghostToolBtn.addClass('active');
                    } else {
                        ghostToolBtn.removeClass('active');
                        ghostToolBtn.addClass('inactive');
                    }
                };
                ghostToolBtn.addClass('ghosting-tool');
                ghostToolBtn.setToolTip('Toggle ghosting on or off');

                var reverseViewBtn = new Autodesk.Viewing.UI.Button('reverse-view');
                reverseViewBtn.onClick = function () {
                    flip180()
                };
                reverseViewBtn.addClass('reverse-view');
                reverseViewBtn.setToolTip('Reverse angle');

                var subToolbar = new Autodesk.Viewing.UI.ControlGroup('cadshare-toolbar');
                subToolbar.addControl(explodeToolBtn);
                subToolbar.addControl(translateToolBtn);
                subToolbar.addControl(ghostToolBtn);
                subToolbar.addControl(reverseViewBtn);
                toolbar.addControl(subToolbar, {index: 0});
            }

            function deactivateExplodeTool() {
                $timeout(function () {
                    vm.isExplodeSliderVisible = false;
                })
            }

            function flip180() {
                var bbox = viewer.impl.getVisibleBounds(false, false);
                var pivot = bbox.center();
                var oldTarget = viewer.navigation.getTarget();
                var oldPosition = viewer.navigation.getPosition();

                var newPosition = {
                    x: oldPosition.x + 2.0 * (pivot.x - oldPosition.x),
                    y: oldPosition.y + 2.0 * (pivot.y - oldPosition.y),
                    z: oldPosition.z + 2.0 * (pivot.z - oldPosition.z)
                };

                var newTarget = {
                    x: oldTarget.x + 2.0 * (pivot.x - oldTarget.x),
                    y: oldTarget.y + 2.0 * (pivot.y - oldTarget.y),
                    z: oldTarget.z + 2.0 * (pivot.z - oldTarget.z)
                };

                viewer.navigation.setView(newPosition, newTarget);
            }

            function toggleExplodePartVisibility() {
                disableTranslate();
                vm.isExplodeSliderVisible = !vm.isExplodeSliderVisible;
                if (vm.isExplodeSliderVisible) {
                    leaderToolDeactivated();
                }
                $scope.$apply();
            }

            function toggleGhosting() {
                isGhosted = !isGhosted;
                viewer.setGhosting(isGhosted);
            }

            function backToSoftCopy() {
                $state.go("softCopy", {
                    productId: vm.productId,
                    machineName: vm.machineName,
                    modelId: vm.modelId,
                    modelName: vm.modelName
                });
            }

            function onViewerLoadedSuccess(response) {
                viewer = response.target;
                viewerHelperService.buildCustomContextMenu(viewer);
                configureViewerAndToolbar(viewer);
                viewerHelperService.setIsTwoD(false);

                createLeaderToolbarButton();
                viewer.addEventListener(Autodesk.Viewing.CAMERA_CHANGE_EVENT, drawLeaders);

                viewerLoaded = true;
                $rootScope.$broadcast('loading-complete');
            }

            $scope.$on('loading-complete', function () {
                if (viewerLoaded && statesReturned) {
                    if (viewerApp.getCurrentViewer().model.getData().instanceTree) {
                        if (isSetupComplete) {
                            loadSnapshot('ROOT');
                            viewerHelperService.hideSpinner();
                        }
                    } else {
                        $timeout(function () {
                            console.log("looping");
                            $rootScope.$broadcast('loading-complete');
                        }, 1000);
                    }
                }
            });

            function getLockedParts() {
                var lockedArray = [];
                for (var i = 0; i < nodeIdsSetsExplodeAsSingleEntity.length; i++) {
                    lockedArray.push(nodeIdsSetsExplodeAsSingleEntity[i][0]);
                }
                return lockedArray;
            }

            function calculateSelectionIds(idArray, lockedArray) {
                var idsToSelect = [];
                for (var k = 0; k < idArray.length; k++) {
                    var dbId = idArray[k];

                    var parentId = dbId;
                    while (parentId !== vm.modelTree[0].objectId) {
                        parentId = viewerHelperService.getParentId(parentId);
                        if (lockedArray.indexOf(parentId) > -1) {
                            dbId = parentId;
                        }
                    }
                    if (idsToSelect.indexOf(dbId) === -1) {
                        idsToSelect.push(dbId);
                    }
                }
                return idsToSelect;
            }


            function getWeldmentId(dbId) {
                var lockedArray = getLockedParts();
                var parentId = dbId;
                while (parentId !== vm.modelTree[0].objectId) {
                    parentId = viewerHelperService.getParentId(parentId);
                    if (lockedArray.indexOf(parentId) > -1) {
                        dbId = parentId;
                    }
                }
                return dbId;
            }

            var DOING_AGG_EVENT = false;
            function onSelectionChanged(data) {
                if(DOING_AGG_EVENT){return true;}
                if (data.dbIdArray.length > 0) {

                    if (isCtrlPressed && _.difference(data.dbIdArray, priorSelection).length === 1) {
                        var newObjectId = _.difference(data.dbIdArray, priorSelection);
                        var weldmentId = getWeldmentId(newObjectId);
                        if (priorSelection.indexOf(weldmentId) >= 0) {
                            priorSelection.splice(priorSelection.indexOf(weldmentId), 1);
                            viewerHelperService.selectParts(priorSelection);
                            return true;
                        }
                    }

                    priorSelection = viewerHelperService.getSelectedParts();
                    var lockedArray = getLockedParts();
                    var idsToSelect = calculateSelectionIds(data.dbIdArray, lockedArray);
                    var visibleNodes = viewerVariablesService.getVisibleNodes();
                    var visibleIdsToSelect = _.intersection(visibleNodes, idsToSelect);


                    if (_.difference(priorSelection, idsToSelect).length > 0) {
                        viewerHelperService.selectParts(visibleIdsToSelect);
                    } else {
                        DOING_AGG_EVENT = true;
                        viewerApp.getCurrentViewer().clearSelection();
                        var selections = [];
                        var selection = {};
                        selection.model = data.model;
                        selection.dbIdArray = Array.isArray(visibleIdsToSelect) ? visibleIdsToSelect : [visibleIdsToSelect];
                        selections.push(selection);

                        viewerApp.getCurrentViewer().setAggregateSelection(selections.map(selection => ({
                            model: selection.model,
                            ids: selection.dbIdArray,
                            selectionType: Autodesk.Viewing.SelectionType.OVERLAYED})));

                        DOING_AGG_EVENT = false;
                        onValidatedSelectionChange(visibleIdsToSelect);
                    }

                } else {
                    if (viewerApp.getCurrentViewer().getSelection().length === 0) {
                        $rootScope.$broadcast("viewer-part-selected", []);
                    }
                }
                return true;
            }

            $scope.$on("viewer-part-selected", function (event, partViewerDetails) {
                if (partViewerDetails.length === 1) {
                    vm.selectedParts = [partViewerDetails[0].part];
                } else if (partViewerDetails.length === 0) {
                    vm.selectedParts = [];
                    vm.partDetailsOpen = false;
                } else if (partViewerDetails.length > 0) {
                    vm.selectedParts = partViewerDetails;
                    vm.partDetailsOpen = false;
                }
            });

            function onValidatedSelectionChange() {
                if (vm.isNonModeledPartsActive) {
                    $timeout(function () {
                        viewerBannerService.removeNotification();
                        vm.showCreateNewPart = true;
                    });
                }
            }

            $scope.$on('activateNonModeledParts', function (event, args) {
                //May need to remove this line
                priorSelection = [];
                vm.isNonModeledPartsActive = true;
                var idsArray = Array.isArray(args.ids) ? args.ids : [args.ids];
                viewerHelperService.selectParts(idsArray);
            });

            function shutDownViewer() {
                if (viewerApp && viewerApp.getCurrentViewer() != null) {
                    viewerApp.getCurrentViewer().removeEventListener(Autodesk.Viewing.SELECTION_CHANGED_EVENT,
                        onSelectionChanged);
                    viewerApp.getCurrentViewer().finish();
                }
            }

            $scope.$on('$destroy', function () {
                shutDownViewer();
            });

            function refreshSnapshotList() {
                vm.currentList = findChildSnapshots(parentId);
            }

            function findSnapshotByStateId(id) {
                return _.findWhere(vm.snapshots, {stateId: id});
            }

            function findChildSnapshots(id) {
                return _.filter(vm.snapshots, function (snapshot) {
                    return snapshot.parentId === id;
                });
            }

            function transitionViewLocked(currentViewer) {
                currentViewer.navigation.setIsLocked(true);
                currentViewer.navigation.setLockSettings({
                    "pan": true,
                    "orbit": true,
                    "roll": true,
                    "fov": true,
                    "gotoview": true
                });//zoom is the only one not enabled to prevent user zooming in transition
            }

            function transitionToSnapshotViewLockSettings() {
                customsSettingsAppliedFlag = false;
                transitionViewLocked(viewerApp.getCurrentViewer());
            }

            function applyNewStateDetail(stateId) {
                var snapshot = findSnapshotByStateId(stateId);
                currentSnapshot = snapshot;
                parentId = stateId;
                viewerApp.getCurrentViewer().addEventListener(Autodesk.Viewing.FINAL_FRAME_RENDERED_CHANGED_EVENT,
                    onFinalRender);

                var jsonState = JSON.parse(snapshot.state);

                viewerApp.getCurrentViewer().restoreState(jsonState, true);
                viewerHelperService.isolateParts(snapshot.visibleDbIds);

                vm.leadersList = jsonState.leadersList ? JSON.parse(jsonState.leadersList) : [];
                drawLeaders();
                loadLeaderTable();

                // refreshSnapshotList();
                vm.steps = [];
                updateSteps(stateId);
            }

            function loadSnapshot(stateId) {
                transitionToSnapshotViewLockSettings();
                disableTranslate();
                getStateDetailPlusChildren(stateId).then(applyNewStateDetail(stateId));
            }

            function getStateDetailPlusChildren(stateId) {
                return viewerService.getViewableStateDetails(vm.viewableId, stateId, 1).then(
                    addReturnedStateDetails);
            }

            function updateSteps(stateId) {
                var snapshot = findSnapshotByStateId(stateId);
                if (stateId !== "ROOT") {
                    vm.steps.unshift(snapshot);
                    updateSteps(snapshot.parentId);
                } else {
                    snapshot.stateName = "Reset to Top Level";
                    vm.steps.unshift(snapshot);
                }
            }

            var removedSnapshots = [];

            function deleteConfirmed() {
                var snapshotToDelete = removedSnapshots[0];
                fetchChildSnapshots(snapshotToDelete)

                //remove this from parents child list
                var parentSnapshot = findSnapshotByStateId(snapshotToDelete.parentId);
                var parentIndex = vm.snapshots.indexOf(parentSnapshot);
                var parentsChildIds = JSON.parse(parentSnapshot.childrenIds);
                var childIndex = parentsChildIds.indexOf(snapshotToDelete.stateId);

                var snapshotsJSON = JSON.parse(vm.snapshots[parentIndex].childrenIds);
                snapshotsJSON.splice(childIndex, 1);
                vm.snapshots[parentIndex].childrenIds = JSON.stringify(snapshotsJSON);

                removeChildSnapshots();
                refreshSnapshotList();
                deleteStateDetail(snapshotToDelete.stateId);
            }

            function deleteSnapshot(id) {
                removedSnapshots = [];
                var snapshotToDelete = findSnapshotByStateId(id);
                removedSnapshots.push(snapshotToDelete);

                var confirmObject = {
                    titleText: "Delete snapshot?",
                    bodyText: "If you confirm then the snapshot \"" + snapshotToDelete.stateName
                        + "\" and all its children will be deleted."
                };
                $uibModal.open({
                    templateUrl: 'features/shared/confirmationModal/confirmModal.html',
                    controller: 'ConfirmModalController',
                    controllerAs: 'confirmModalCtrl',
                    size: 'sm',
                    resolve: {
                        confirmObject: function () {
                            return confirmObject;
                        }
                    }
                }).result
                    .then(deleteConfirmed, doNothing);
            }

            function fetchChildSnapshots(snapshot) {
                var childIds = JSON.parse(snapshot.childrenIds);
                if (childIds.length > 0) {
                    for (var i = 0; i < childIds.length; i++) {
                        var childSnapshot = findSnapshotByStateId(childIds[i]);

                        removedSnapshots.push(childSnapshot);

                        if (childSnapshot) {
                            fetchChildSnapshots(childSnapshot);
                        }
                    }
                }
                return removedSnapshots;
            }

            function removeChildSnapshots() {
                if (removedSnapshots.length > 0) {
                    for (var i = 0; i < removedSnapshots.length; i++) {
                        var index = vm.snapshots.indexOf(removedSnapshots[i]);
                        vm.snapshots.splice(index, 1);
                    }
                }
            }

            function saveSnapshot() {
                takeScreenShot()
                    .then(screenShotSuccess);
            }

            function addIdToParentsChildList(childId, parentId) {
                var parentSnapshot = findSnapshotByStateId(parentId);
                var index = vm.snapshots.indexOf(parentSnapshot);
                var childIds = JSON.parse(vm.snapshots[index].childrenIds);
                childIds.push(childId);

                vm.snapshots[index].childrenIds = JSON.stringify(childIds);
                updateStateDetail(parentSnapshot);
            }

            function createStateDetail(currentState) {
                return viewerService.createSoftCopyStateDetail(currentState, $stateParams.viewableId);
            }

            function updateStateDetail(snapshot) {
                return viewerService.updateSoftCopyStateDetail(snapshot, $stateParams.viewableId);
            }

            function deleteStateDetail(id) {
                return viewerService.deleteSoftCopyStateDetail(id, $stateParams.viewableId);
            }

            function updateImageUrl(currentState, isOverwriteSnapshot) {
                viewerService.uploadThumbnailToAWS(currentState.imgUrl)
                    .then((function (stateId) {
                        return function (newUrl) {
                            var snapshot = findSnapshotByStateId(stateId);
                            var index = vm.snapshots.indexOf(snapshot);
                            vm.snapshots[index].imgUrl = newUrl;
                            if (vm.snapshots[index].softCopyDetail) {
                                vm.snapshots[index].softCopyDetail.highResImgUrl = newUrl;
                            }
                            if (isOverwriteSnapshot) {
                                vm.steps[vm.steps.length - 1].imgUrl = snapshot.imgUrl;
                                if (vm.steps[vm.steps.length - 1].softCopyDetail) {
                                    vm.steps[vm.steps.length - 1].softCopyDetail.highResImgUrl = snapshot.imgUrl;
                                }
                            }
                            updateStateDetail(snapshot);
                            viewerHelperService.hideSpinner();
                        };
                    })(currentState.stateId))
                    ['catch'](function (error) {
                    viewerHelperService.hideSpinner();
                    uploadThumbnailToAWSFailed();
                })
            }

            function screenShotSuccess(imgUrl) {
                viewerHelperService.showSpinner();
                var currentState = {};

                currentState.childrenIds = '[]';
                var state = viewerApp.getCurrentViewer().getState();
                if (state) {
                    state.leadersList = JSON.stringify(vm.leadersList);
                }
                currentState.state = JSON.stringify(state);

                currentState.imgUrl = imgUrl;
                currentState.viewableId = vm.viewableId;
                currentState.softCopyDetail = {};
                currentState.softCopyDetail.markedUpParts = getMarkedUpParts();

                if (isOverwriteSnapshot) {
                    var snapshot = findSnapshotByStateId(parentId);
                    currentState.id = snapshot.id ? snapshot.id : '';
                    currentState.stateId = parentId;
                    currentState.childrenIds = snapshot.childrenIds;
                    currentState.visibleDbIds = JSON.stringify(viewerVariablesService.getVisibleNodes());
                    currentState.stateName = snapshot.stateName;
                    currentState.parentId = snapshot.parentId;
                    currentState.notes = snapshot.notes ? snapshot.notes : '';
                    var index = vm.snapshots.findIndex(function (obj) {
                        return obj.stateId === parentId
                    });
                    vm.snapshots.splice(index, 1, currentState);
                    updateStateDetail(currentState)
                        .then(function () {
                            updateImageUrl(currentState, true);
                        });
                    isOverwriteSnapshot = false;
                } else {

                    currentState.stateId = isSetupComplete ? viewerService.guid() : 'ROOT';
                    currentState.visibleDbIds = JSON.stringify(viewerVariablesService.getVisibleNodes());
                    currentState.stateName = currentState.stateId;
                    currentState.parentId = isSetupComplete ? parentId : 'NOT_ROOT';
                    currentState.notes = '';
                    currentState.viewableId = vm.viewableId;
                    vm.snapshots.push(currentState);
                    if (isSetupComplete) {
                        addIdToParentsChildList(currentState.stateId, parentId);
                    }
                    createStateDetail(currentState).then(function (resp) {
                            currentState.id = resp.data;
                            updateImageUrl(currentState, false);
                        }
                    );
                }

                refreshSnapshotList();
            }

            function uploadThumbnailToAWSFailed() {
                console.error("Upload of thumbnail to aws failed!");
            }

            function takeScreenShot() {
                return $q(function (resolve) {
                    viewerHelperService.selectParts([]);

                    $timeout(function () {
                        var viewer = viewerApp.getCurrentViewer();
                        //var bounds = viewer.canvas.getBoundingClientRect();
                        var vw = viewer.container.clientWidth;
                        let vh = viewer.container.clientHeight;
                        viewer.getScreenShot(vw, vh, resolve);
                    }, 100);

                });
            }

            function editSnapshotName(stateId) {
                var snapshot = findSnapshotByStateId(stateId);
                var index = vm.snapshots.indexOf(snapshot);
                var name = vm.snapshots[index].stateName ? vm.snapshots[index].stateName : "";
                var notes = vm.snapshots[index].notes ? vm.snapshots[index].notes : "";
                var snapshotDetails = {name: name, notes: notes};
                var modalInstance = $uibModal.open({
                    templateUrl: 'features/viewer/extensions/editNameAndNotes/editNameAndNotesModal.html',
                    controller: 'EditNameAndNotesModalController as editNameAndNotesCtrl',
                    resolve: {
                        snapshotDetails: function () {
                            return snapshotDetails;
                        }
                    }
                });

                modalInstance.result.then(function (newSnapshotDetails) {
                    if (newSnapshotDetails.name !== '' && newSnapshotDetails.name !== undefined) {
                        var snapshot = findSnapshotByStateId(stateId);
                        var index = vm.snapshots.indexOf(snapshot);
                        vm.snapshots[index].stateName = newSnapshotDetails.name;
                        vm.snapshots[index].notes = newSnapshotDetails.notes ? newSnapshotDetails.notes : "";
                        refreshSnapshotList();
                        updateStateDetail(vm.snapshots[index]);
                    }
                });
            }

            //called from cadshare_extension
            function weldmentsUpdated(weldmentArray) {
                var lockedItems = [];
                for (var x = 0; x < weldmentArray.length; x++) {
                    var nonArrayId = weldmentArray[x][0];
                    lockedItems.push(nonArrayId);

                }
                customWeldments = lockedItems;
                $rootScope.$broadcast('locked-model-tree-updated', lockedItems);
            }

            function disableTranslate() {
                var txTool = _translateTool.getName();
                viewer.toolController.deactivateTool(txTool);
                translateToolBtn.removeClass('translate-tool-active');
                translateToolBtn.removeClass('active');
                translateToolBtn.addClass('translate-tool');
            }

            function overwriteSnapshot($event) {
                $event.stopPropagation();
                var snapshot = findSnapshotByStateId(parentId);
                var snapshotName = snapshot.stateName;
                var confirmObject = {
                    titleText: "Overwrite snapshot?",
                    bodyText: "If you confirm then the snapshot \"" + snapshotName + "\" will be overwritten."
                };
                $uibModal.open({
                    templateUrl: 'features/shared/confirmationModal/confirmModal.html',
                    controller: 'ConfirmModalController',
                    controllerAs: 'confirmModalCtrl',
                    size: 'sm',
                    resolve: {
                        confirmObject: function () {
                            return confirmObject;
                        }
                    }
                }).result
                    .then(overwriteConfirmed, doNothing);
            }

            function showSpinner() {
                viewerHelperService.showSpinner();
            }

            function hideSpinner() {
                viewerHelperService.hideSpinner();
            }

            function doNothing() {
                //do nothing
            }

            function overwriteConfirmed() {
                isOverwriteSnapshot = true;
                takeScreenShot()
                    .then(screenShotSuccess);
            }

            $scope.$on("viewer-settings-changed", function (event, viewerSettings) {
                vm.viewerSettings = viewerSettings;
                viewerHelperService.applyCustomViewerSettings(viewer, vm.viewerSettings);
            });

            //--------------------- START OF SOFT COPY SPECIFIC CODE --------------------------------------------------

            vm.leadersList = [];
            var _partNumber = "";
            var isLeaderToolActive = true;
            var _objectId;
            var LINES_MATERIAL = new THREE.LineBasicMaterial({
                color: new THREE.Color(0x000000),
                transparent: true,
                depthTest: false,
                opacity: 1.0
            });
            var isFirstPointSelection;
            var _startWorldPoint;
            var _startScreenPoint;
            var leaderToolButton;

//-------------------- Initialization Methods --------------------
            //  restoreLeaders();

//-------------------- End Initialization Methods --------------------

            function saveLeaders(viewerState) {
                //TODO need to figure out how to save into state from controller method
                viewerState.leadersList = JSON.parse(JSON.stringify(vm.leadersList));
            }

            function restoreLeaders() {
                vm.leadersList = viewer.getState().leadersList ? viewer.getState().leadersList : [];
                drawLeaders();
            }

            function createLeaderToolbarButton() {
                leaderToolButton = new Autodesk.Viewing.UI.Button('create-leader-tool-button');
                leaderToolButton.onClick = toggleLeaderToolActive;
                leaderToolButton.addClass('leaders-tool');
                leaderToolButton.setToolTip('Create leader');

                var subToolbar = new Autodesk.Viewing.UI.ControlGroup('Leader-Toolbar');
                subToolbar.addControl(leaderToolButton);

                viewer.toolbar.addControl(subToolbar);
            }

//-------------------- End Initialization Methods --------------------

            function toggleLeaderToolActive() {
                isLeaderToolActive = !isLeaderToolActive;
                if (isLeaderToolActive) {
                    leaderToolDeactivated();
                } else {
                    leaderToolActivated();
                }
            }

            function leaderToolActivated() {

                leaderToolButton.removeClass('inactive');
                leaderToolButton.addClass('active');
                isFirstPointSelection = true;
                viewer.canvas.addEventListener("click", onMouseClick);

                var viewerCanvas = document.getElementsByTagName('canvas')[0];
                viewerCanvas.setAttribute('style', 'cursor: pointer !important');

                disableTranslate();
                deactivateExplodeTool();
            }

            function leaderToolDeactivated() {
                leaderToolButton.removeClass('active');
                leaderToolButton.addClass('inactive');
                viewer.canvas.removeEventListener("click", onMouseClick);
                removeDrawingLine();
                removeDrawingLine();
                viewer.canvas.removeEventListener("mousemove", onMouseMove);

                //TODO possibly remove style we set before insteadof trying to set back to default
                var viewerCanvas = document.getElementsByTagName('canvas')[0];
                viewerCanvas.setAttribute('style', 'cursor: default !important');
            }

            function getParentId(dbId) {
                return viewer.model.getData().instanceTree.nodeAccess.getParentId(dbId);
            }

            function partSelection(dbId) {
                var lockedArray = [];
                for (var i = 0; i < nodeIdsSetsExplodeAsSingleEntity.length; i++) {
                    lockedArray.push(nodeIdsSetsExplodeAsSingleEntity[i][0]);
                }
                var rootNode = vm.modelTree[0].objectId;
                var parentId = dbId;
                while (parentId !== rootNode) {
                    parentId = getParentId(parentId);
                    if (lockedArray.indexOf(parentId) > -1) {
                        dbId = parentId;
                    }
                }
                return dbId;
            }

            function onMouseClick(ev) {
                var bounds = viewer.canvas.getBoundingClientRect();
                var screenX = ev.clientX - bounds.left;
                var screenY = ev.clientY - bounds.top;
                var screenPoint = new THREE.Vector3(screenX, screenY, 0);

                if (isFirstPointSelection) {
                    var ctw = viewer.clientToWorld(screenX, screenY, true);
                    if (ctw !== null) {
                        _objectId = partSelection(ctw.dbId);
                        if (isPartUnlabeled(_objectId)) {
                            _startScreenPoint = screenPoint;
                            _startWorldPoint = ctw.point;
                            isFirstPointSelection = false;

                            // getPartNumberDetails(_objectId);
                            var viewerCanvas = document.getElementsByTagName('canvas')[0];
                            viewerCanvas.setAttribute('style', 'cursor: crosshair !important');
                            viewer.canvas.addEventListener("mousemove", onMouseMove);
                        }
                    }
                } else {
                    var endScreenPoint = screenPoint;
                    var endWorldPoint = viewer.impl.clientToViewport(screenX, screenY, true);
                    if (endWorldPoint !== null) {
                        isFirstPointSelection = true;

                        var leaderData = {
                            startWorldPoint: _startWorldPoint,
                            startScreenPoint: _startScreenPoint,
                            endWorldPoint: endWorldPoint,
                            endScreenPoint: endScreenPoint,
                            objectId: _objectId
                        };

                        vm.leadersList.push(leaderData);
                        addLeaderToTable(_objectId);
                        drawLeaders();

                        var viewerCanvas = document.getElementsByTagName('canvas')[0];
                        viewerCanvas.setAttribute('style', 'cursor: pointer !important');
                        removeDrawingLine();
                        viewer.canvas.removeEventListener("mousemove", onMouseMove);
                    }
                }
            }

            function onMouseMove(ev) {
                var bounds = viewer.canvas.getBoundingClientRect();
                var screenX = ev.clientX - bounds.left;
                var screenY = ev.clientY - bounds.top;
                var endWorldPoint = viewer.impl.clientToViewport(screenX, screenY, true);
                drawDrawingLine(_startWorldPoint, endWorldPoint);
            }

            function isPartUnlabeled(objectId) {
                return _.findWhere(vm.leadersList, {objectId: objectId}) === undefined;
            }

            function drawLeadersLine() {
                viewer.impl.createOverlayScene("leaderOverlay", LINES_MATERIAL);

                for (var i = 0; i < vm.leadersList.length; i++) {
                    var lineGeom = createLineGeometry(vm.leadersList[i].startWorldPoint,
                        vm.leadersList[i].endWorldPoint);
                    viewer.impl.addOverlay("leaderOverlay", lineGeom);
                }
                viewer.impl.invalidate(true);
            }

            function drawDrawingLine(startPoint, endPoint) {
                removeDrawingLine();

                var lineGeom = createLineGeometry(startPoint, endPoint);
                viewer.impl.createOverlayScene("drawingOverlay", LINES_MATERIAL);
                viewer.impl.addOverlay("drawingOverlay", lineGeom);
                viewer.impl.invalidate(true)
            }

            function createLineGeometry(startPoint, endPoint) {
                var geometry = new THREE.Geometry();

                var projectedPoint = new THREE.Vector3(endPoint.x, endPoint.y, endPoint.z);
                projectedPoint.unproject(viewer.impl.camera);
                geometry.vertices.push(projectedPoint);
                geometry.vertices.push(startPoint);
                return new THREE.Line(geometry, LINES_MATERIAL, THREE.LinePieces);
            }

            function removeDrawingLine() {
                viewer.impl.removeOverlay("drawingOverlay");
                viewer.impl.removeOverlayScene("drawingOverlay");
            }

            function drawLeaders() {
                drawLeadersLine();
                drawLeadersCircles();
            }

            function drawLeadersCircles() {
                //drawStartCircle();
                drawNumberCircle();
            }

            function drawStartCircle() {
                removeStartCircle();
                var canvasBounds = viewer.canvas.getBoundingClientRect();
                var width = canvasBounds.width;
                var height = canvasBounds.height;
                var orthoCamera = createOrthoCamera(width, height);
                viewer.impl.createOverlayScene('leaderOverlay-startCircle', null, null, orthoCamera);

                for (var i = 0; i < vm.leadersList.length; i++) {
                    var screenPoint = vm.leadersList[i].startScreenPoint;
                    var circle = createCircle(screenPoint, width, height, 5);
                    viewer.impl.addOverlay('leaderOverlay-startCircle', circle);
                }

                viewer.impl.invalidate(true)
            }

            function drawEndCircle() {
                var canvasBounds = viewer.canvas.getBoundingClientRect();
                var width = canvasBounds.width;
                var height = canvasBounds.height;
                var orthoCamera = createOrthoCamera(width, height);
                viewer.impl.createOverlayScene('leaderOverlay-endCircle', null, null, orthoCamera);

                for (var i = 0; i < vm.leadersList.length; i++) {
                    var screenPoint = vm.leadersList[i].endScreenPoint;
                    var circle = createCircle(screenPoint, width, height, 20);
                    viewer.impl.addOverlay('leaderOverlay-endCircle', circle);
                }

                viewer.impl.invalidate(true)
            }

            function createCircle(screenPoint, width, height, radius) {
                var x = screenPoint.x - (width / 2);
                var y = -(screenPoint.y - (height / 2));

                var circle_geometry = new THREE.CircleGeometry(radius, 64);
                var circle_material = new THREE.MeshBasicMaterial({color: 'rgb(1,1,1)'});
                var circle = new THREE.Mesh(circle_geometry, circle_material);
                circle.position.set(x, y, 0);
                return circle;
            }

            function createOrthoCamera(width, height) {
                return new THREE.OrthographicCamera(-width / 2, width / 2, height / 2, -height / 2, 0, 30);
            }

            function drawNumber(numId, leader) {
                var canvasBounds = viewer.canvas.getBoundingClientRect();
                var width = canvasBounds.width;
                var height = canvasBounds.height;
                var orthoCamera = createOrthoCamera(width, height);
                viewer.impl.createOverlayScene('leaderOverlay-numberCircle', null, null, orthoCamera);

                LOAD_FONT('./images/lato.fnt', function (err, font) {
                    var geometry = CREATE_GEOM({
                        width: 20,
                        align: 'center',
                        font: font
                    });

                    geometry.update(numId.toString());

                    var textureLoader = new THREE.TextureLoader();
                    textureLoader.load('./images/lato.png', function (texture) {
                        var material = new THREE.MeshBasicMaterial(
                            {map: texture, transparent: true, side: THREE.DoubleSide});

                        var textMesh = new THREE.Mesh(geometry, material);
                        textMesh.scale.y = -2;
                        var screenPoint = leader.endScreenPoint;
                        var x = screenPoint.x - (width / 2);
                        var y = -(screenPoint.y - (height / 2));
                        textMesh.position.set(x - 10, y - 10, 0);
                        viewer.impl.addOverlay('leaderOverlay-numberCircle', textMesh);
                    });

                    viewer.impl.invalidate(true);
                })
            }

            function drawNumberCircle() {
                removeNumberCircles();
                drawEndCircle();
                drawNumbers();
            }

            function drawNumbers() {
                for (var i = 0; i < vm.leadersList.length; i++) {
                    var numId = i + 1;
                    var leader = vm.leadersList[i];
                    drawNumber(numId, leader);
                }
            }

            function removeNumberCircles() {
                viewer.impl.removeOverlay("leaderOverlay-numberCircle");
                viewer.impl.removeOverlayScene("leaderOverlay-numberCircle");
                viewer.impl.removeOverlay("leaderOverlay-endCircle");
                viewer.impl.removeOverlayScene("leaderOverlay-endCircle");
            }

            function removeStartCircle() {
                viewer.impl.removeOverlay("leaderOverlay-startCircle");
                viewer.impl.removeOverlayScene("leaderOverlay-startCircle");
            }

            function addLeaderToTable(objectId) {
                viewerService.getPartViewerDetails(vm.modelId, [objectId])
                    .then(function (resp) {
                        var kits = "";
                        if (resp.data.inKit) {
                            kits = populateKits(resp.data.kits);
                        }
                        var leader = {
                            partNumber: resp.data.part.partNumber,
                            partDescription: resp.data.part.partDescription,
                            objectId: resp.data.part.objectId,
                            inKit: resp.data.inKit,
                            kits: kits
                        };
                        var leaderObj = _.findWhere(vm.leadersList, {objectId: resp.data.part.objectId});
                        var index = vm.leadersList.indexOf(leaderObj);
                        vm.leaderTable[index] = leader;
                    })
            }

            var kitIdentifier = 'A';

            function populateKits(kits) {
                var kitText = "";
                for (var i = 0; i < kits.length; i++) {
                    var kitLabel = getKitAlphaId(kits[i]);
                    kitText = kitText.length === 0 ? kitLabel : kitText + ", " + kitLabel;
                }
                return kitText;
            }

            function getKitAlphaId(kit) {
                var kitLabel;
                var kitInTable = _.findWhere(vm.kitTable, {id: kit.id});
                if (kitInTable === undefined) {
                    kitLabel = createNewKitLabel(kit);
                } else {
                    kitLabel = kitInTable.label;
                }
                return kitLabel
            }

            function createNewKitLabel(kit) {
                kit.label = kitIdentifier;
                vm.kitTable.push(kit);
                incrementKitIdentifier();
                return kit.label;
            }

            function incrementKitIdentifier() {
                kitIdentifier = String.fromCharCode(kitIdentifier.charCodeAt(0) + 1);
            }

            function deleteLeaderFromTable(leaderObjectId) {
                var leaderObj = _.findWhere(vm.leadersList, {objectId: leaderObjectId});
                var index = vm.leadersList.indexOf(leaderObj);
                vm.leadersList.splice(index, 1);
                vm.leaderTable = [];
                vm.kitTable = [];
                kitIdentifier = "A";
                for (var i = 0; i < vm.leadersList.length; i++) {
                    addLeaderToTable(vm.leadersList[i].objectId);
                }
                drawLeaders();
            }

            function loadLeaderTable() {
                vm.leaderTable = [];
                vm.kitTable = [];
                kitIdentifier = "A";
                for (var i = 0; i < vm.leadersList.length; i++) {
                    addLeaderToTable(vm.leadersList[i].objectId);
                }
            }

            function toggleKitsVisibility() {
                vm.isKitsVisible = !vm.isKitsVisible;
            }

            function togglePartsVisibility() {
                vm.isPartsVisible = !vm.isPartsVisible;
            }

            function getMarkedUpParts() {
                var objectIds = [];
                for (var i = 0; i < vm.leadersList.length; i++) {
                    objectIds.push(vm.leadersList[i].objectId);
                }
                return JSON.stringify(objectIds);
            }

            function notNull(arg) {
                return !!arg;
            }
        }
    }

)
();
