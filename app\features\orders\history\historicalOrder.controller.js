(function () {
    'use strict';

    angular
        .module('app.orders')
        .controller('HistoricalOrderController', HistoricalOrderController);

    HistoricalOrderController.$inject = ['ordersService', '$scope', '$controller', 'userService', 'headerBannerService', '$window', '$uibModal', '$translate', '$state'];

    function HistoricalOrderController(ordersService, $scope, $controller, userService, headerBannerService, $window, $uibModal, $translate, $state) {
        var vm = this;
        var WENT_WRONG;

        angular.extend(vm, $controller('OrderController', {$scope: $scope}));

        vm.isManufacturer = userService.isManufacturer();
        vm.viewInvoice = viewInvoice;
        vm.openComment = openComment;
        vm.duplicateOrder = duplicateOrder;
        vm.onlyKitsPresent = onlyKitsPresent;
        vm.hasVisibleDiscountedPrices = hasVisibleDiscountedPrices;
        vm.previewPricingEnabled = userService.getPreviewPricingEnabled();

        initialize();

        function initialize() {
            vm.hidePartSearch = true;
            vm.showPurchaseOrder = true;
            vm.isShippingEditable = false;
            vm.isDetailsEditable = false;
            vm.isPriceEditable = false;
            vm.showAltStepBtn = false;
            vm.isPurchaseOrderEditable = false;
            vm.showEstimatedDelivery = true;
            vm.isActionActive = false;
            vm.deleteOrderItem = false;
            vm.showNextStep = false;
            vm.isEstimatedDeliveryDateEditable = false;
            vm.isHistoricalOrder = true;
            vm.isNotesEditable = false;
            vm.displayNotes = false;
            vm.isDiscountEditable = false;
            vm.isDiscountVisible = false;
            vm.isDiscountEnabled = false;
            vm.exportPartDataCsvVisible = true;
            vm.showSplitOrderBtn = false;
            vm.showPartDescription = false;
            vm.showModelName = false;
            vm.showProductName = false;
            vm.showPartialShipped = userService.getPartialShippingEnabled();

            $translate(['HIST_ORDER.HIST_CLOSED', 'GENERAL.WENT_WRONG','ORDERS.ORDER'])
                .then(function (resp) {
                    vm.orderStatusPill = resp['HIST_ORDER.HIST_CLOSED'];
                    WENT_WRONG = resp['GENERAL.WENT_WRONG'];
                    vm.STAGE = resp['ORDERS.ORDER'];
                });

            ordersService.getOrder(vm.orderId)
                .then(getOrdersSuccess, serviceCallFailed);
        }

        function getOrdersSuccess(response) {
            if (response.data.orderStatus === 'CLOSED' || response.data.orderStatus === 'CANCELLED' || response.data.orderStatus === 'EXTERNAL' || response.data.orderStatus === 'ARCHIVED') {
                vm.archived = true;
                vm.data = response.data;
                vm.orderLists = response.data.orderItems;
                vm.totalItems = vm.orderLists.length;
                vm.billingAddress = vm.createReadableAddress(response.data.billingAddress);
                vm.shippingAddress = vm.createReadableAddress(response.data.shippingAddress);
                vm.data.originalShippingPrice = (vm.data.orderExtraData && vm.data.orderExtraData.shippingCost) || vm.data.shippingPrice;
                vm.data.taxPrice = (vm.data.orderExtraData && vm.data.orderExtraData.taxAmount) || 0;
                vm.data.shippingPrice = vm.data.shippingPrice ? vm.data.shippingPrice : 0;
                vm.isOrderItemsLoaded = true;
                vm.displayNotes = vm.data.notes !== null && vm.data.notes !== undefined;
                vm.showOrderReference = vm.data.orderExtraData && vm.data.orderExtraData.globalPaymentTransactionId;
                vm.showERPRefVisible = userService.isManufacturer() && vm.data.visSalesOrderNoX != null;
                vm.updateItemTotals(true, this, true);
                calculateSplitOrders();
                vm.selectedCurrency = userService.getCurrencyData(vm.data.currency);
                vm.isPartInformationAvailable = isPartInformationAvailable;
                if (vm.data.percentageDiscount > 0) {
                    vm.isDiscountVisible = true;
                }


                var manufacturerSubEntityId = vm.data.manufacturerSubEntityId;
                vm.orderLists.forEach(function (item) {
                    if (item.kitId) {
                        var id = vm.isManufacturer ? userService.getManufacturerId() : manufacturerSubEntityId;
                        ordersService.getKit(id, item.kitId, vm.isManufacturer)
                            .then(kitResponse => {
                                item.kitDetails = kitResponse;
                            });
                    }
                });

                if (response.data.orderStatus === 'CANCELLED') {
                    $translate(['HIST_ORDER.HIST_CANCELLED'])
                        .then(function (resp) {
                            vm.orderStatusPill = resp['HIST_ORDER.HIST_CANCELLED'];
                        });

                }
                if (response.data.orderStatus === 'EXTERNAL') {
                    $translate(['HIST_ORDER.HIST_EXTERNAL'])
                        .then(function (resp) {
                            vm.orderStatusPill = resp['HIST_ORDER.HIST_EXTERNAL'];
                        });

                }
                if (response.data.orderStatus === 'ARCHIVED') {
                    $translate(['HIST_ORDER.HIST_ARCHIVED'])
                        .then(function (resp) {
                            vm.orderStatusPill = resp['HIST_ORDER.HIST_ARCHIVED'];
                        });

                }
                vm.isPartiallyShipped = hasBeenPartiallyShipped();
            } else {
                vm.redirectToOrder(response.data.orderId, response.data.orderStatus);
            }
        }

        function serviceCallFailed(error) {
            vm.isOrderItemsLoaded = true;
            headerBannerService.setNotification('ERROR', WENT_WRONG, 10000);
        }

        function calculateSplitOrders() {
            vm.splitOrders = [];
            if (vm.orderLists) {
                for (var i = 0; i < vm.orderLists.length; i++) {
                    if (vm.orderLists[i].splitOrderIds && vm.orderLists[i].splitOrderIds.length > 0) {
                        for (var j = 0; j < vm.orderLists[i].splitOrderIds.length; j++) {
                            if (!_.findWhere(vm.splitOrders, {orderId: vm.orderLists[i].splitOrderIds[j]})) {
                                var splitOrder = {
                                    orderId: vm.orderLists[i].splitOrderIds[j],
                                    displayId: vm.orderLists[i].splitOrderCustomDisplayNumbers[j] ? vm.orderLists[i].splitOrderCustomDisplayNumbers[j] : vm.orderLists[i].splitOrderIds[j]
                                }
                                vm.splitOrders.push(splitOrder);
                            }
                        }
                    }
                }
            }

        }

        function viewInvoice(id) {
            if (id) {
                $window.open(vm.data.invoiceUrl[id], '_blank');
            } else {
                $window.open(vm.data.invoiceUrl[0], '_blank');
            }
        }

        function openComment(commentThread, orderItemId) {
            var commentObject = {
                threadId: commentThread,
                orderId: vm.orderId,
                orderItemId: orderItemId,
                blockAddingComment: true
            };

            $uibModal.open({
                templateUrl: 'features/shared/comments/comments.html',
                controller: 'CommentController',
                controllerAs: 'commentsCtrl',
                resolve: {
                    commentObject: function () {
                        return commentObject;
                    }
                }
            }).result.then(function (commentObject) {

                if (commentObject.orderItemId) {
                    var index = _.findIndex(vm.data.orderItems, {orderItemId: commentObject.orderItemId});
                    vm.data.orderItems[index].commentThread = {};
                    vm.data.orderItems[index].commentThread.orderItemId = commentObject.orderItemId;
                    vm.data.orderItems[index].commentThread.id = commentObject.threadId;
                    vm.data.orderItems[index].commentThread.orderId = commentObject.orderId;
                } else {
                    vm.data.commentThread = {};
                    vm.data.commentThread.id = commentObject.threadId;
                    vm.data.commentThread.orderId = commentObject.orderId;
                }
            }, function () {
                console.log('Modal Cancelled');
            });

        }

        function hasBeenPartiallyShipped() {
            for (var i = 0; i < vm.data.orderHistory.length; i++) {
                if (vm.data.orderHistory[i].orderStatus === "Order partially shipped") {
                    return true;
                }
            }
            return false;
        }

        function isPartInformationAvailable(item) {
            var partInformation = {
                showPartDescription: item.partDescription,
                showModelName: item.modelName,
                showMachineName: item.machineName
            };
            vm.showPartDescription = partInformation.showPartDescription;
            vm.showModelName = partInformation.showModelName;
            vm.showMachineName = partInformation.showMachineName;
            return partInformation;
        }

        function duplicateOrder() {
            ordersService.duplicateOrder(vm.orderId)
                .then(function (response) {
                    var duplicatedOrder = response.data;
                    $state.go('create', {
                        orderDetails: duplicatedOrder
                    });
                }, function (error) {
                    console.error('Failed to duplicate order:', error);
                });
        }

        function onlyKitsPresent() {
            if (!vm.data || !vm.data.orderItems) {
                return false;
            }
            return vm.data.orderItems.every(item => item.kitId && !item.partId);
        }

        function hasVisibleDiscountedPrices() {

            if (!vm.data || !vm.data.orderItems) {
                return false;
            }

            var orderItems = vm.data.orderItems;

            var hasDiscountedPrices = orderItems.some(item => item.discountedPrice && item.discountedPrice !== 0);

            return hasDiscountedPrices;
        }

    }
})();
