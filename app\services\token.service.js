(function () {
    'use strict';

    angular
        .module('app.services')
        .factory('tokenService', tokenService);

    tokenService.$inject = ['$state', '$injector', 'apiConstants', '$q'];

    function tokenService($state, $injector, apiConstants, $q) {

        return {
            validateEmail: validateEmail,
            oauthLogin: oauthLogin,
            getOauthToken: getOauthToken,
            getAutodeskToken: getAutodeskToken,
            isOauthTokenValid: isOauthTokenValid,
            oauthLogOut: oauthLogOut,
            setAutodeskToken: setAutodeskToken,
            refreshOauthToken: refreshOauthToken,
            preAuthLogin: preAuthLogin
        };

        function validateEmail(email) {
            return $injector.get('$http').post(apiConstants.url + '/user/manufacturers', {'email': email});
        }

        function getOauthToken() {
            return new Promise(function(resolve, reject) {
                var oauthToken = localStorage.getItem("OAuth Token");

                if (oauthToken) {
                    if (isOauthTokenValid()) {
                        resolve(oauthToken);
                    } else {
                        refreshOauthToken().then(()=> {
                            oauthToken = localStorage.getItem("OAuth Token");
                            resolve(oauthToken);
                        });
                    }
                } else {
                    oauthLogOut();
                    if($state.current.name === 'register') return;
                    $state.go('login');
                    reject();
                }
            });
        }


        function isOauthTokenValid() {
            var tokenExpiryTime = localStorage.getItem("OAuth Expiry Time");
            if (null === tokenExpiryTime) {
                return false;
            }
            var currentTime = new Date().getTime();
            return currentTime < tokenExpiryTime;
        }

        function refreshTokenSuccess(response) {
            storeTokenDetails(response.data);
            return;
        }

        function refreshOauthToken() {
            return new Promise(function(resolve, reject) {
                var refresh_token = getOauthRefreshToken();
                if(refresh_token !== null) {
                    $injector.get('$http')
                            .post(apiConstants.url + '/oauth/token?refresh_token=' + refresh_token + '&grant_type=refresh_token')
                            .then((response) => {
                                refreshTokenSuccess(response);
                                resolve();
                            },(error)=>{
                                refreshTokenFailure();
                                reject(error);
                            });
                } else {
                    reject(error);
                }
            });
        }

        function refreshTokenFailure() {
            $state.go('login');
        }

        function storeTokenDetails(token) {
            setOauthToken(token.access_token);
            setOauthRefreshToken(token.refresh_token);
            setOauthExpiryTime(token.expires_in);
        }

        function oauthLogin(username, password) {
            return new Promise(function(resolve, reject) {
                var userData = {username: username, password: password, grant_type: 'password'};

                return $injector.get('$http').post(apiConstants.url + '/oauth/token', userData)
                    .then((response) => {
                        oauthLoginSuccess(response);
                        resolve();
                },(error) => {
                    reject(oauthLoginFailed(error));
                });
            });
        }

        function oauthLoginSuccess(response) {
            storeTokenDetails(response.data);
            return $q.resolve();
        }

        function oauthLoginFailed(error) {
            return error.data.error_description;
        }

        function setOauthToken(oauthToken) {
            localStorage.setItem("OAuth Token", oauthToken);
        }

        function setOauthRefreshToken(refreshToken) {
            localStorage.setItem("OAuth Refresh Token", refreshToken);
        }

        function getOauthRefreshToken() {
            return localStorage.getItem("OAuth Refresh Token");
        }

        function setOauthExpiryTime(expiryTime) {
            var tokenExpiryTime = new Date().setSeconds(expiryTime);
            localStorage.setItem("OAuth Expiry Time", tokenExpiryTime.toString());
        }

        function oauthLogOut() {
            localStorage.removeItem("OAuth Token");
            localStorage.removeItem("OAuth Refresh Token");
            localStorage.removeItem("OAuth Expiry Time");
            localStorage.removeItem("liveMeetingInfo");
            $injector.get('userService').removeUserInfoFromStorage();
        }


        function getAutodeskToken() {
            return localStorage.getItem("Autodesk Token");
        }

        function setAutodeskToken(autodeskToken) {
            return localStorage.setItem("Autodesk Token", autodeskToken.accessToken);
        }

        function preAuthLoginSuccess(response) {
            storeTokenDetails(response.data.cadshareToken);
            return $q.resolve();
        }

        function preAuthLoginFailed(response) {
            return $q.reject(response.data);
        }

        function preAuthLogin(client, token) {
            var userData = {client: client.toUpperCase(), token: token};

            return $injector.get('$http').post(apiConstants.url + '/preAuth', userData)
                .then(preAuthLoginSuccess, preAuthLoginFailed);
        }
    }
})();