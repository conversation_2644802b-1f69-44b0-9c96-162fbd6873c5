(function () {
    'use strict';

    angular
        .module('app.viewer')
        .controller('ViewerBannerController', ViewerBannerController);

    ViewerBannerController.$inject = ['$scope', '$rootScope', 'viewerBannerService', '$timeout'];

    function ViewerBannerController($scope, $rootScope, viewerBannerService, $timeout) {
        var vm = this;

        vm.notificationDisplay = false;
        vm.isSideMenuOpen = false;
        vm.level = "";
        vm.errorMsg = "";
        vm.timeout = 0;

        vm.partData = {};
        vm.kit = false;
        vm.kits =[];

        $scope.$on("viewerNotification", createBanner);

        $scope.$watch('viewerBannerCtrl.notificationDisplay', function (newValue) {
            $rootScope.bannerNotificationDisplay = newValue;
        });

        vm.closeNotification = closeNotification;

        vm.selectAssembly = selectAssembly;
        vm.viewKit = viewKit;

        $rootScope.$on("side-menu-minimized", function () {
            vm.isSideMenuOpen = false;
        });

        $rootScope.$on("side-menu-maximized", function () {
            vm.isSideMenuOpen = true;
        });

        function createBanner() {
            var notification = viewerBannerService.getNotification();
            if (notification && notification.level && notification.level.length > 0) {

                $scope.$applyAsync(function(){
                    vm.notificationDisplay = true;
                    vm.level = notification.level;
                    vm.errorMsg = notification.text;
                    vm.timeout = notification.timeout ? notification.timeout : 0;
                });

                if (notification.level == 'KIT') {
                    $scope.$applyAsync(function(){
                        vm.level = 'INFO';
                        vm.hasKit = true;
                        vm.isAssembly = false;
                        vm.partData = notification.partData;
                        vm.kits = notification.kits;
                    });
                }
                if (notification.level == 'ASSEMBLY') {
                    $scope.$applyAsync(function(){
                        vm.level = 'INFO';
                        vm.hasKit = false;
                        vm.isAssembly = true;
                        vm.partData = notification.partData;
                        vm.kits = [];
                    });
                }
                if (notification.level == 'KITASSEMBLY') {
                    $scope.$applyAsync(function(){
                        vm.level = 'INFO';
                        vm.hasKit = true;
                        vm.isAssembly = true;
                        vm.partData = notification.partData;
                        vm.kits = notification.kits;
                    });
                }

                if (vm.timeout > 0) {
                    $timeout(closeNotification, vm.timeout);
                }
            } else {
                $scope.$applyAsync(function(){
                    vm.notificationDisplay = false;
                });
            }
        }

        function closeNotification() {
            viewerBannerService.removeNotification();
            vm.notificationDisplay = false;
        }

        function viewKit() {
            viewerBannerService.viewKit(vm.kits);
        }

        function selectAssembly() {
            viewerBannerService.selectAssembly(vm.partData);
        }
    }

})();
