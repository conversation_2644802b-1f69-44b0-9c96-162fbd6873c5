(function () {
    'use strict';

    angular
        .module('app.products')
        .controller('CustomerTechDocsController', CustomerTechDocsController);

    CustomerTechDocsController.$inject = ['publicationService', '$stateParams', 'headerBannerService', '$window'];

    function CustomerTechDocsController(publicationService, $stateParams, headerBannerService, $window) {
        var vm = this;

        var manualId = $stateParams.manualId;
        vm.count = 8;
        vm.currentPage = 1;
        vm.itemPerPage = 8;
        vm.start = 0;
        vm.sortReverse = false;
        vm.endRecord = vm.itemPerPage;
        vm.docsList = null;
        vm.filterValue = {};
        vm.searchValue = "";


        vm.pageChanged = pageChanged;
        vm.viewDoc = viewDoc;
        vm.searchFilterChange = searchFilterChange;

        initialize();

        function initialize() {
            getTechDocs();
        }

        function getTechDocs() {
            publicationService.getTechDocs(manualId)
                .then(getTechDocsSuccess)
                .catch(getTechDocsFailed);
        }

        function getTechDocsSuccess(response) {
            vm.docsList = response.data;
            vm.totalItems = vm.docsList.length;
        }

        function getTechDocsFailed(error) {
            vm.docsList = [];
            headerBannerService.setNotification('ERROR', error.data.error, 10000);
        }

        function viewDoc(techDoc){
            $window.open(techDoc.url, "_blank");
        }

        function pageChanged() {
            vm.start = ((vm.currentPage - 1) * vm.itemPerPage);
        }

        function searchFilterChange() {
            updateTotalItemCount();
        }

        function updateTotalItemCount() {
            var textFilter = $filter('filter')(vm.customerList, vm.searchValue);
            vm.totalItems = $filter('filter')(textFilter, vm.filterValue, true).length;
        }

    }
})();
