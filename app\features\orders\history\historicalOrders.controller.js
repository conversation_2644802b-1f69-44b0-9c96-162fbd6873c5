(function () {
    'use strict';

    angular
        .module('app.orders')
        .controller('HistoricalOrdersController', HistoricalOrdersController);

    HistoricalOrdersController.$inject = ['ordersService', '$scope', '$controller', 'userService', 'headerBannerService', '$translate', '$window'];

    function HistoricalOrdersController(ordersService, $scope, $controller, userService, headerBannerService, $translate, $window) {
        var vm = this;
        angular.extend(vm, $controller('OrdersController', {$scope: $scope}));

        vm.stage = {};

        $translate(['HIST_ORDER.HIST_ORDERS', 'HIST_ORDER.HIST_ORDER'])
            .then(function (resp) {
                vm.stage.plural = resp['HIST_ORDER.HIST_ORDERS'];
                vm.stage.single = resp['HIST_ORDER.HIST_ORDER'];
            });
        vm.viewState = 'orders.historicalorder';
        vm.showPrice = true;
        vm.hideCancelOrderButton = true;
        vm.isFixedHeader = false;

        vm.loadingInfiniteScrollData = false;
        vm.isOrdersLoaded = false;
        vm.showBackToTopButton = false;

         vm.scrollToTop = scrollToTop;
        vm.loadMoreInfiniteScroll = loadMoreInfiniteScroll;
        vm.previewPricingEnabled = userService.getPreviewPricingEnabled();
        vm.isManufacturer = userService.isManufacturer();

        initialize();

        
        function initialize() {
            vm.loadingInfiniteScrollData = true;
            ordersService.getHistoricalOrders()  
                .then(function(resp) {
                    getOrdersSuccess(resp);
                }, serviceCallFailed);
}

             function getOrdersSuccess(resp) {
            vm.allOrders = resp.data.orders;
            vm.totalItems = vm.allOrders.length;
            vm.orders = vm.allOrders.slice(0, 100); 
            vm.isOrdersLoaded = true;
            vm.loadingInfiniteScrollData = false;
            handleInfiniteScroll();
        }

        function serviceCallFailed(resp) {
            headerBannerService.setNotification('ERROR', resp.data.error, 10000);
            vm.isOrdersLoaded = true;
        }

       var lastScrollTop = 0;
window.addEventListener('scroll', handleInfiniteScroll);

function handleInfiniteScroll() {
    var threshold = 250;
    var scrollTop = window.scrollY;

    if (scrollTop > lastScrollTop) {
        vm.isFixedHeader = scrollTop > threshold;
    } else if (scrollTop < threshold){
        vm.isFixedHeader = false;
    }
    lastScrollTop = scrollTop;  

    
    if (window.innerHeight + scrollTop >= document.body.offsetHeight - 2 && !vm.loadingInfiniteScrollData && !vm.initialLoadDone) {
        loadMoreInfiniteScroll();
    }
}

        function loadMoreInfiniteScroll() {
            vm.loadingInfiniteScrollData = true;

            var nextItems = vm.allOrders.slice(vm.orders.length, vm.orders.length + 100);
            if (nextItems.length === 0) {
                vm.initialLoadDone = true;
                vm.loadingInfiniteScrollData = false;
                return;
            }

            vm.orders = vm.orders.concat(nextItems);

            vm.loadingInfiniteScrollData = false;
            if (vm.orders.length >= vm.totalItems) {
                vm.initialLoadDone = true;
            }
        }

  function scrollToTop() {
      $window.scrollTo({ top: 0, behavior: "smooth" });
      $("html, body").animate({ scrollTop: 0 }, "slow", function () {
        $("#scrollToTop").removeClass("scrolled-past");
      });
    }

    angular.element($window).on("scroll", function () {
      vm.showBackToTopButton = this.pageYOffset > 100;
      $scope.$apply();
    });
    }
})();
