#!/usr/bin/env bash
SERVICE_BRANCH_URL=http://cadshare-review-$CI_COMMIT_REF_NAME.eu-west-1.elasticbeanstalk.com
DEV_URL=http://development.qwsbgumwf2.eu-west-1.elasticbeanstalk.com/

RESPONSE=$(curl --write-out %{http_code} --silent --output /dev/null "${SERVICE_BRANCH_URL}/actuator/health")
if [ $RESPONSE == 200 ]
then
  echo "Using services review environment"
  sed -i -e 's@SERVICES_URL@'$SERVICE_BRANCH_URL'@g' constants/branch-constants.js
else
  echo "Using services development environment"
  sed -i -e 's@SERVICES_URL@'${DEV_URL}'@g' constants/branch-constants.js
fi

cp constants/branch-constants.js app/features/base/config/app.constants.js