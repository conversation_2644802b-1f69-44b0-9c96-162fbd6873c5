<p class="page-desc" translate>PRODUCTS_CATALOG.DESCRIPTION</p>

<section class="responsiveContainer">

    <!-- Fixed header for infinite scroll -->
    
    <div id="{{productsCatalogueCtrl.isFixedHeader ? 'infiniteScrollFixedHeader' : 'infiniteScrollStaticHeader'}}"
        class="flex p-4 p-md-0">
        <search-filter class="col-12 col-md-3 px-0 px-md-4" state-name="'products'"
            on-search-change="productsCatalogueCtrl.searchFilterChange()" value="productsCatalogueCtrl.searchValue"
            placeholder-key="PRODUCTS_CATALOG.SEARCH_BY"></search-filter>

        <button class="btn primary mr-2 col-12 col-md-auto my-3 my-md-0"
            ng-click="productsCatalogueCtrl.toggleAdvancedFilter()">
            <span class="text-white">{{'PRODUCTS_CATALOG.FILTER' | translate}}</span>
            <i ng-hide="productsCatalogueCtrl.displayAdvancedFilter" class="pl-2 fa fa-plus"></i>
            <i ng-show="productsCatalogueCtrl.displayAdvancedFilter" class="pl-2 fa fa-minus"></i>
        </button>
        <button class="btn secondary col-12 col-md-auto mb-3 mb-md-0" href=""
            ng-disabled="!productsCatalogueCtrl.filter_range && !productsCatalogueCtrl.searchValue"
            ng-click="productsCatalogueCtrl.clearFilter()" translate>PRODUCTS_CATALOG.CLEAR_ALL</button>

        <button class="btn primary ml-auto mr-4 col-12 col-md-auto create-product"
            ng-click="productsCatalogueCtrl.createProductPopUp()" translate>PRODUCTS_CATALOG.CREATE_NEW
        </button>
    
        <div class="gloBl-filter-panel cadGap mb-4" ng-show="productsCatalogueCtrl.displayAdvancedFilter">
            <div class="filter-option">
                <div class="input-icon-wrap first-filter">
                    <small ng-show="!productsCatalogueCtrl.filter_range" class="filter-header"
                        translate>PRODUCTS_CATALOG.FILTER_RANGE</small>
                    <div class="select-box">
                        <select ng-change="productsCatalogueCtrl.handleFilterChange()"
                            ng-model="productsCatalogueCtrl.filter_range"
                            ng-options="range as range for range in productsCatalogueCtrl.ranges">
                        </select>
                    </div>
                    <i ng-class="{'selectActive': productsCatalogueCtrl.filter_range}" class="fa fa-filter"
                        aria-hidden="true"></i>
                </div>
            </div>
        </div>
    </div>

		<div class="responsiveContainer">

        <table class="table table-bordered product-catalogue-table">

            <thead>
                <tr>
                    <th ng-class="productsCatalogueCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'"
                        ng-click="productsCatalogueCtrl.viewable_sort='name'; productsCatalogueCtrl.sortReverse = !productsCatalogueCtrl.sortReverse"
                        translate>PRODUCTS_CATALOG.PRODUCT</th>
                    <th ng-class="productsCatalogueCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'"
                        ng-click="productsCatalogueCtrl.viewable_sort='rangeName'; productsCatalogueCtrl.sortReverse = !productsCatalogueCtrl.sortReverse"
                        translate>PRODUCTS_CATALOG.RANGE</th>
                    <th ng-class="productsCatalogueCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'"
                        ng-click="productsCatalogueCtrl.viewable_sort='createdDate'; productsCatalogueCtrl.sortReverse = !productsCatalogueCtrl.sortReverse"
                        translate>PRODUCTS_CATALOG.CREATE_DATE</th>
                    <th translate>PRODUCTS_CATALOG.ACTIONS</th>
                </tr>
            </thead>

            <tbody class="products_body" infinite-scroll="productsCatalogueCtrl.loadMoreInfiniteScroll()" infinite-scroll-distance="3"
                infinite-scroll-disabled="productsCatalogueCtrl.loadingInfiniteScrollData">
                <tr class="p-4"
                    ng-repeat="product in productsCatalogueCtrl.productList | orderBy:productsCatalogueCtrl.viewable_sort:productsCatalogueCtrl.sortReverse | filter : productsCatalogueCtrl.searchValue | filter : productsCatalogueCtrl.filterValue"
                    ng-show="productsCatalogueCtrl.productList.length > 0" ng-class="{'last-item': $last}">
                    <td data-label="{{'ADMIN.NAME' | translate}}">{{product.name}}</td>
                    <td data-label="{{'ADMIN.RANGE' | translate}}">{{product.range || product.rangeName}}</td>
                    <td data-label="{{'ADMIN.CREATED_DATE' | translate}}">{{product.createdDate | date : "d MMMM y"}}
                    </td>


                    <td>
                        <split-button-dropdown main-action="productsCatalogueCtrl.openViewablePage(product)"
                            main-action-label="{{'PRODUCTS_CATALOG.LIST_VIEWABLES' | translate}}"
                            actions="productsCatalogueCtrl.actions" entity="product">
                        </split-button-dropdown>
                    </td>

                </tr>

                <tr ng-show="!productsCatalogueCtrl.productList.length > 0 && productsCatalogueCtrl.isProductsLoaded">
                    <td colspan="4" translate>PRODUCTS_CATALOG.NO_MACHINES</td>
                </tr>

                <tr ng-hide="productsCatalogueCtrl.isProductsLoaded" align="center">
                    <td class="preloader" colspan="5"><img ng-src="images/cadpreloader.gif" class="ajax-loader"
                            height="60" width="60" /></td>
                </tr>
            </tbody>
        </table>

        </tbody>
        </table>

            <span ng-click="productsCatalogueCtrl.scrollToTop()" id="backToTopBtn" title="Go to top" class="fas fa-arrow-alt-circle-up" ng-show="productsCatalogueCtrl.showBackToTopButton"></span>

    </div> <!-- Responsive Container -->

</section>