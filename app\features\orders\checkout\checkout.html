<section class="body-content">

	<section class="col-12 col-md-6">
			<p><a href="" class="d-flex align-items-center cadGap dark-secondary" ng-click="checkoutCtrl.goToCreate()"><i class="fa fa-caret-left"></i>{{'CHECKOUT_PAGE.BACK_TO_BASKET' | translate}}</a></p>
	</section>

	<section class="d-flex justify-content-between flex-wrap">

			<div class="col-12 col-md-8 customDivContainer">
				<div class="customDivStyling">
					<div>
						<h2><span translate>CHECKOUT_PAGE.PAYMENT_DETAILS</span></h2>
						<div class="container-loading" ng-show="checkoutCtrl.loadingGetConfig || checkoutCtrl.isLoading"> 
							<span class="spinner-border" role="status" aria-hidden="true"></span> 
						</div>
						<div class="paymentResult"></div>
						<iframe id="targetIframe" class="w-100 heightVh100 border-0"></iframe>
					</div>

						<!-- Pay now modal trigger -->

						<!-- OLD Modal -->
						<!-- <div style="z-index:2000" class="modal fade" id="thankyouModal" tabindex="-1" role="dialog" aria-labelledby="thankyouModalLabel" aria-hidden="true">
								<div class="modal-dialog" role="document">
									<div class="modal-content">
											<div class="modal-body d-flex justify-content-center align-items-center flex-column p-5">
													<i class="fa fa-check cadBlue" style="font-size: 3.5em!important" aria-hidden="true"></i>
													<h1 class="text-center mb-2" translate>CHECKOUT_PAGE.THANKS_ORDER</h1>
													<h2 class="text-center w-50 mb-0 font-weight-normal" translate>CHECKOUT_PAGE.PAYMENT_COMPLETED</h2>
													<p class="text-center my-4">
														<span class="font-weight-bold" translate>CHECKOUT_PAGE.ORDER_REFERENCE</span>
														<span>{{ checkoutCtrl.newOrderId }}</span>
													</p>
													<a><p class="text-center hoverActiveLink" ng-click="checkoutCtrl.navigateToNewOrder()" translate>CHECKOUT_PAGE.ORDER_SUMMARY_LINK</p></a>
											</div>
									</div>
								</div>
						</div> -->

						<div style="z-index:2000" class="modal fade" id="thankyouModal" tabindex="-1" role="dialog" aria-labelledby="thankyouModalLabel" aria-hidden="true">
							<div class="modal-dialog" role="document">
								<div class="modal-content">
										<div class="modal-body d-flex justify-content-center align-items-center flex-column p-5">
												<i class="fa fa-check cadBlue" style="font-size: 3.5em!important" aria-hidden="true"></i>
												<h1 class="text-center mb-3 w-75 font-weight-normal"><span>{{'CHECKOUT_PAGE.PAYMENT_SUCCESS' | translate}}</span>
													<strong ng-if="checkoutCtrl.currentWarehouse">{{checkoutCtrl.currentWarehouse.name}}</strong> 
												</h1>

												<h2 ng-hide="checkoutCtrl.isSplitWarehouse">{{'CHECKOUT_PAGE.ORDER_COMPLETE' | translate}}</h2>
												<p class="text-center">
													<span class="font-weight-bold" translate>CHECKOUT_PAGE.ORDER_REFERENCE</span> {{checkoutCtrl.newOrder.orderExtraData.globalPaymentTransactionId}}
												</p>
												<span ng-show="checkoutCtrl.isSplitWarehouse" class="text-center"><span>{{'CHECKOUT_PAGE.AUTOMATICALLY_REDIRECT' | translate}}</span>
												<strong ng-if="checkoutCtrl.nextWarehouse">{{checkoutCtrl.nextWarehouse.name}}</strong>, <span>{{'CHECKOUT_PAGE.PLEASE_CLICK' | translate}}</span> 
												<a href class="hoverActiveLink" ng-click="checkoutCtrl.navigateToNewOrder(checkoutCtrl.newOrder.orderId)">{{'CHECKOUT_PAGE.HERE' | translate}}</a>
												</span>
												<div ng-show="checkoutCtrl.orderCreated.length > 1" class="mb-3">
														{{'CHECKOUT_PAGE.YOUR_ORDER_WAS_SPLIT' | translate}}
													<span  ng-repeat="order in checkoutCtrl.orderCreated">
															<a href class="hoverActiveLink" ng-click="checkoutCtrl.navigateToNewOrder(order.orderId)">{{order.customOrderDisplay}}</a> <span ng-show="!$last">or</span>
													</span>
													{{'CHECKOUT_PAGE.TO_YOUR_ORDER_SUMMARY' | translate}}
												</div>
												<a><p class="text-center hoverActiveLink font-weight-bold" ng-show="!checkoutCtrl.isSplitWarehouse" ng-click="checkoutCtrl.navigateToNewOrder(checkoutCtrl.newOrder.orderId)" translate>CHECKOUT_PAGE.ORDER_SUMMARY_LINK</p></a>
										</div>
								</div>
							</div>
					</div>

				</div>

			</div>

			<div class="col-12 col-md-4 customDivContainer">
			<div class="customDivStyling orderSumary">

					<h2><span translate>CHECKOUT_PAGE.ORDER_SUMMARY</span></h2>

				  	<hr class="underline_order border-bottom-0">

					<ul class="d-flex flex-column customLineHeight list-unstyled" ng-if="checkoutCtrl.areAnyPricesReturned() && checkoutCtrl.previewPricingEnabled">

							<li style="text-align:left;" ng-show="checkoutCtrl.areAllPricesReturned()">
								<span class="d-flex justify-content-between">
									<strong class="pr-2"><span translate>CHECKOUT_PAGE.SUB_TOTAL</span></strong>{{checkoutCtrl.estimatedTotal | currency:checkoutCtrl.defaultCurrency.symbol:2}}
								</span>
							</li>

							<li style="text-align:left;">
								<span class="d-flex justify-content-between">
									<strong class="pr-2"><span translate>CHECKOUT_PAGE.SHIPPING</span> ({{ checkoutCtrl.shippingCarrier.description | capitalize }})</strong>{{checkoutCtrl.shippingCarrier.totalAmount.amount | currency:checkoutCtrl.defaultCurrency.symbol:2}}
								</span>
							</li>

														
							<li style="text-align:left;" ng-show="checkoutCtrl.isTaxPaymentEnabled">
								<span class="d-flex justify-content-between">
									<strong class="pr-2"><span translate>CHECKOUT_PAGE.TAX</span></strong>{{checkoutCtrl.taxPrice | currency:checkoutCtrl.defaultCurrency.symbol:2}}
								</span>
							</li>

							<li style="text-align:left;" class="d-flex justify-content-between" ng-hide="checkoutCtrl.areAllPricesReturned()">
								<strong class="pr-2" translate>CREATE_ORDER.EST_TOTAL</strong> TBC
							</li>

							<li><span>{{'CHECKOUT_PAGE.ORDER_FROM' | translate}}</span> {{checkoutCtrl.currentWarehouse.name}}</li>
					</ul>

					<div class="totalSection d-flex flex-column customLineHeight" ng-show="checkoutCtrl.areAllPricesReturned()">
						<span class="d-flex totalSection-content">
							<strong class="pr-2"><span translate>CHECKOUT_PAGE.TOTAL</span></strong>{{checkoutCtrl.totalPrice | currency:checkoutCtrl.defaultCurrency.symbol:2}}
						</span>
					</div>

			</div>
			</div>

			<section class="d-flex justify-content-between flex-wrap w-100">
			<div class="col-12 customDivContainer">
					<div class="customDivStyling">

							<h2><span translate>CHECKOUT_PAGE.YOUR_BASKET</span></h2>

							<table class="table table-bordered">
							<thead>
							<tr>
									<th translate>ORDER.PART_NO</th>
									<th translate>ORDER.PRODUCT</th>
									<th translate>ORDER.QUANTITY</th>
									<th ng-if="checkoutCtrl.previewPricingEnabled && checkoutCtrl.areAnyPricesReturned()" class="width-10" translate>ORDER.ITEM_PRICE</th>
									<th ng-if="checkoutCtrl.previewPricingEnabled && checkoutCtrl.areAnyPricesReturned()" class="width-10" translate>ORDER.PRICE</th>
									<!-- linked tech doc-->
							</tr>
							</thead>
							<tbody>
							<tr ng-repeat="item in checkoutCtrl.basket"
									ng-class="item.quantity > 0 ? '' : 'strike-through'">
									<td data-label="{{'ORDER.PART_NO' | translate}}">{{item.partNumber}}</td>
									<td data-label="{{'ORDER.PRODUCT' | translate}}">{{item.machineName}}</td>
									<td data-label="{{'ORDER.QUANTITY' | translate}}"><input ng-model="item.quantity" disabled>
									</td>
									<td class="disableWordBreak" data-label="{{'ORDER.ITEM_PRICE' | translate}}"
									ng-if="checkoutCtrl.areAnyPricesReturned() && checkoutCtrl.previewPricingEnabled && item.price">
											{{item.price | currency:checkoutCtrl.defaultCurrency.symbol:2}}
									</td>
									<td class="disableWordBreak" data-label="{{'ORDER.ITEM_PRICE' | translate}}"
									ng-if="checkoutCtrl.areAnyPricesReturned() && checkoutCtrl.previewPricingEnabled && !item.price">
											TBC
									</td>
									<td data-label="{{'ORDER.PRICE' | translate}}"
									ng-if="checkoutCtrl.areAnyPricesReturned() && checkoutCtrl.previewPricingEnabled && item.price">
											{{item.totalPrice | currency:checkoutCtrl.defaultCurrency.symbol:2}}
									</td>
									<td data-label="{{'ORDER.PRICE' | translate}}"
									ng-if="checkoutCtrl.areAnyPricesReturned() && checkoutCtrl.previewPricingEnabled && !item.price">
											TBC
									</td>
							</tr>

							<tr ng-if="!checkoutCtrl.basket.length > 0">
									<td class="flex-start noPartsBG" colspan="10" translate>CREATE_ORDER.NO_IDENTIFIED_PARTS</td>
							</tr>

							</tbody>
							</table>
					</div>
			</div>
			</section>



	</section>

</section>