(function () {
    'use strict';

    angular
        .module('app.orders')
        .controller('DPHistoricalOrdersController', DPHistoricalOrdersController);

    DPHistoricalOrdersController.$inject = ['dpOrdersService', '$scope', '$controller', 'headerBannerService', '$translate', '$window'];

    function DPHistoricalOrdersController(dpOrdersService, $scope, $controller, headerBannerService, $translate, $window) {
        var vm = this;
        angular.extend(vm, $controller('DPOrdersController', {$scope: $scope}));

        vm.stage = {};

        $translate(['HIST_ORDER.HIST_ORDERS', 'HIST_ORDER.HIST_ORDER'])
            .then(function (resp) {
                vm.stage.plural = resp['HIST_ORDER.HIST_ORDERS'];
                vm.stage.single = resp['HIST_ORDER.HIST_ORDER'];
            });
        vm.viewState = 'dpOrders.customerOrders.orders.historicalorder';
        vm.showPrice = true;
        vm.hideCancelOrderButton = true;
        vm.showBackToTopButton = false;
        vm.isFixedHeader = false;

        vm.scrollToTop = scrollToTop;
        vm.loadMoreInfiniteScroll = loadMoreInfiniteScroll;

        initialize();

        function initialize() {
            vm.loadingInfiniteScrollData = true;
            dpOrdersService.getHistoricalOrders()
                .then(getOrdersSuccess, serviceCallFailed);
        }

        function getOrdersSuccess(response) {
            vm.allOrders = response.data;
            vm.orders = vm.allOrders.slice(0, 100);  
            vm.totalItems = vm.allOrders.length;
            vm.isOrdersLoaded = true;
            vm.loadingInfiniteScrollData = false;
            handleInfiniteScroll();
        }

        function serviceCallFailed(resp) {
            headerBannerService.setNotification('ERROR', resp.data.error, 10000);
            vm.isOrdersLoaded = true;
            vm.loadingInfiniteScrollData = false;
        }

       var lastScrollTop = 0;
window.addEventListener('scroll', handleInfiniteScroll);

function handleInfiniteScroll() {
    var threshold = 250;
    var scrollTop = window.scrollY;

    if (scrollTop > lastScrollTop) {
        vm.isFixedHeader = scrollTop > threshold;
    } else if (scrollTop < threshold){
        vm.isFixedHeader = false;
    }
    lastScrollTop = scrollTop;  

    
    if (window.innerHeight + scrollTop >= document.body.offsetHeight - 2 && !vm.loadingInfiniteScrollData && !vm.initialLoadDone) {
        loadMoreInfiniteScroll();
    }
}

        function loadMoreInfiniteScroll() {
            vm.loadingInfiniteScrollData = true;

            var nextItems = vm.allOrders.slice(vm.orders.length, vm.orders.length + 100);
            if (nextItems.length === 0) {
                vm.initialLoadDone = true;
                vm.loadingInfiniteScrollData = false;
                return;
            }

            vm.orders = vm.orders.concat(nextItems);

            vm.loadingInfiniteScrollData = false;
            if (vm.orders.length >= vm.totalItems) {
                vm.initialLoadDone = true;
            }
        }

  function scrollToTop() {
      $window.scrollTo({ top: 0, behavior: "smooth" });
      $("html, body").animate({ scrollTop: 0 }, "slow", function () {
        $("#scrollToTop").removeClass("scrolled-past");
      });
    }

    angular.element($window).on("scroll", function () {
      vm.showBackToTopButton = this.pageYOffset > 100;
      $scope.$apply();
    });

    }
})();
