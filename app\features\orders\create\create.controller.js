(function () {
  "use strict";

  angular
    .module("app.orders")
    .controller("CreateController", CreateController)
    .directive("convertNumberToString", function () {
      return {
        require: "ngModel",
        link: function (scope, element, attrs, ngModel) {
          ngModel.$parsers.push(function (val) {
            return parseInt(val, 10);
          });
          ngModel.$formatters.push(function (val) {
            return "" + val;
          });
        },
      };
    });

  CreateController.$inject = [
    "basketService",
    "ordersService",
    "masterPartService",
    "$uibModal",
    "$scope",
    "headerBannerService",
    "$state",
    "$stateParams",
    "userService",
    "$translate",
    "$window",
    "globalPaymentService",
    "shippingEngineService",
    "avaTaxService",
  ];

  function CreateController(
    basketService,
    ordersService,
    masterPartService,
    $uibModal,
    $scope,
    headerBannerService,
    $state,
    $stateParams,
    userService,
    $translate,
    $window,
    globalPaymentService,
    shippingEngineService,
    avaTaxService
  ) {
    var vm = this;

    var NEW_ADDRESS_ID;
    var NEW_NAME_ID;
    var NEW_NUMBER_ID;
    var NEW_COURIER_DETAILS_ID;
    var nextWarehouse = null;
    var currentPurchaseOrderNumber;

    var manufacturerId = userService.getManufacturerId();

    vm.isPaymentEnabled = userService.getPaymentEnabled();
    vm.isTaxPaymentEnabled = userService.getTaxPayments();
    vm.onBehalfOf =
      $stateParams.onBehalfOf && $stateParams.onBehalfOf !== "null"
        ? JSON.parse(decodeURIComponent(atob($stateParams.onBehalfOf)))
        : undefined;
    vm.isOnBehalfOf = userService.isOnBehalfOf();
    vm.defaultCurrency =
      vm.onBehalfOf && vm.onBehalfOf.currency
        ? vm.onBehalfOf.currency
        : userService.getDefaultCurrency();
    vm.previewPricingEnabled = userService.getPreviewPricingEnabled();
    vm.hidePrice = hidePrice;
    vm.isManufacturer = userService.isManufacturer();
    vm.isPreviewStockLevelEnabled = userService.getPreviewStockLevelEnabled();
    vm.showPartSearch = userService.hasPartSearchRole();
    vm.isRequiredSerialNumber = userService.getRequiredSerialNumber();
    vm.enquiryPurchaseOrder = userService.getEnquiryPurchaseOrder();
    vm.enquiryPOToLiveEnabled = userService.getEnquiryPOToLiveOrderEnabled();
    vm.isCustomer = userService.isManufacturerSubEntity();
    vm.isDealerCustomer = userService.isDealerPlusCustomer();
    vm.isDealerPlusCustomer =
      userService.isManufacturerSubEntity() &&
      userService.isDealerPlusCustomer();
    vm.isCDE = userService.isCDE();
    vm.isFarmer = userService.isFarmer();
    vm.isSupreme = userService.isSupreme();
    vm.isStockWarehousesEnabled = userService.getStockWarehousesEnabled();
    vm.defaultWarehouseId = userService.getWarehouseId();
    vm.getUserInfo = userService.getUserInfo(userService.getOnBehalfOfUserId());
    vm.isAddressCreationEnabled = userService.getAddressCreationDefault();
    vm.defaultWarehouseStock = [];
    vm.showPartDescription = false;
    vm.showModelName = false;
    vm.showMachineName = false;
    vm.isVerifiedFarmer = vm.isFarmer && vm.isPaymentEnabled && !vm.onBehalfOf;
    vm.isPaymentEnabledNonDealerUser =
      vm.onBehalfOf &&
      vm.isPaymentEnabled &&
      vm.onBehalfOf.userType !== "MANUFACTURER_SUB_ENTITY_DEALER";
    vm.isManufacturerOnBehalfOf = userService.isManufacturer && vm.onBehalfOf;
    vm.shippingRequirementExpressDelivery = false;
    vm.selectedShippingRequirementId = null;
    vm.overrideForDirectOrders =
      userService.getOverrideForDirectOrdersEnabled();

    vm.submitDisabled = false;
    vm.submitting = false;
    vm.manualPartsEditable = false;
    vm.stockOrderSelected = false;
    vm.isSubEntityPaymentsEnabled = false;
    vm.isFarmerPaymentsEnabled = false;
    vm.ifPlaceOrderIsClicked = false;

    vm.shippingRequirements = [];
    vm.accordionStates = {};

    vm.submitEnquiry = submitEnquiry;
    vm.placeOrder = placeOrder;
    vm.addNewAddress = addNewAddress;
    vm.addNewName = addNewName;
    vm.addNewNumber = addNewNumber;
    vm.removeItem = removeItem;
    vm.removeKitItem = removeKitItem;
    vm.partUpdated = partUpdated;
    vm.kitUpdated = kitUpdated;
    vm.viewLinkedTechDocs = viewLinkedTechDocs;
    vm.addComment = addComment;
    vm.addPartComment = addPartComment;
    vm.addKitComment = addKitComment;
    vm.createManualPart = createManualPart;
    vm.editManualPart = editManualPart;
    vm.removeManualPart = removeManualPart;
    vm.saveManualParts = saveManualParts;
    vm.cancelCreateManualParts = cancelCreateManualParts;
    vm.openPartSearchModal = openPartSearchModal;
    vm.toggleStockOrder = toggleStockOrder;
    vm.areAllPricesReturned = areAllPricesReturned;
    vm.areAllKitPricesReturned = areAllKitPricesReturned;
    vm.areAnyPricesReturned = areAnyPricesReturned;
    vm.proceedToCheckout = proceedToCheckout;
    vm.updateBasketWarehouse = updateBasketWarehouse;
    vm.updateBasketWarehouseKit = updateBasketWarehouseKit;
    vm.showPurchaseOrderInput = showPurchaseOrderInput;
    vm.isStockAvailable = isStockAvailable;
    vm.isPartInformationAvailable = isPartInformationAvailable;
    vm.isStockHigh = isStockHigh;
    vm.isStockLow = isStockLow;
    vm.isStockEmptyOrNull = isStockEmptyOrNull;
    vm.getUniqueWarehouses = getUniqueWarehouses;
    vm.getUniqueKitWarehouses = getUniqueKitWarehouses;
    vm.addShippingRequirements = addShippingRequirements;
    vm.deleteShippingRequirements = deleteShippingRequirements;
    vm.setSelectedShippingRequirementId = setSelectedShippingRequirementId;
    vm.calculateDiscountedPrice = calculateDiscountedPrice;
    vm.toggleKitsAccordion = toggleKitsAccordion;
    vm.hasKits = hasKits;
    vm.viewPartNote = viewPartNote;
    vm.updateContactId = updateContactId;
    vm.updateDeliveryId = updateDeliveryId;

    vm.deliveryContacts = [];
    vm.orderContacts = [];

    initialize();

    $scope.$watch('createCtrl.deliveryAddress', function(newVal, oldVal) {
      if (newVal !== oldVal) {
        updateDeliveryAddressContacts();
      }
    });

    $scope.$watch('createCtrl.billingAddress', function(newVal, oldVal) {
      if (newVal !== oldVal) {
        updateBillingAddressContacts();
      }
    });

    function initialize() {
      // Hide Frice Function
      hidePrice();
      if ($stateParams.orderDetails) {
        vm.order = $stateParams.orderDetails;

        vm.basket = vm.order.orderItems;
        vm.deliveryAddress = JSON.stringify(vm.order.shippingAddress);
        vm.billingAddress = JSON.stringify(vm.order.billingAddress);
        vm.contactName = vm.order.contactName;
        vm.contactNumber = vm.order.contactNumber;
        vm.deliveryName = vm.order.deliveryName;
        vm.deliveryNumber = vm.order.deliveryNumber;
        vm.requestedDeliveryDate = new Date(vm.order.requestedDeliveryDate).toLocaleDateString('en-GB');
        vm.serialNumber = vm.order.serialNumber;
        vm.additionalParts = vm.order.additionalParts;
        vm.stockOrder = vm.order.stockOrder;

      }

      if (vm.onBehalfOf) {
        vm.manufacturerSubEntityId = vm.onBehalfOf.manufacturerSubEntityId;
      } else {
        vm.manufacturerSubEntityId = userService.getManufacturerSubEntityId();
      }

      fetchManualParts();
      updateBasket();
      getBasketComments();
      updateAddresses();
      updateDeliveryAddressContacts();
      updateBillingAddressContacts();
      updateNumbers();
      configureDatePicker();
      getTranslations();
      isCDEDisclaimerEnabled();
      vm.isStockWarehousesEnabled && getAllWarehouse(manufacturerId);
      isSubEntityPaymentsEnabled();
      isFarmerPaymentsEnabled();
      hideStockWarehouses();
      fetchShippingRequirements();
      tooltip();
      getDefaultDiscount();
    }

    var PLEASE_COMPLETE, SUBMITTED_ON_BEHALF, ERROR;

    function getTranslations() {
      $translate([
        "CREATE_ORDER.PLEASE_COMPLETE",
        "CREATE_ORDER.SUBMITTED_ON_BEHALF",
        "GENERAL.WENT_WRONG",
      ]).then(function (resp) {
        PLEASE_COMPLETE = resp["CREATE_ORDER.PLEASE_COMPLETE"];
        SUBMITTED_ON_BEHALF = resp["CREATE_ORDER.SUBMITTED_ON_BEHALF"];
        ERROR = resp["GENERAL.WENT_WRONG"];
      });
    }

    function updateBasket() {
      vm.basket = basketService.getBasket($stateParams);
      updateTotalItemPrices();
      vm.isStockWarehousesEnabled && updateWarehouseIdForBasket(vm.defaultWarehouseId);
      fetchMasterPartPaymentRequirements();
      vm.ifBasketContainsItems = (basketService.getBasket().length + basketService.getManualPartsCount()) > 0;
    }

    function fetchManualParts() {
      var additionalParts = basketService.getManualPartsBasket($stateParams);
      if (additionalParts.length > 0) {
        vm.savedAdditionalParts = additionalParts;
      } else {
        vm.savedAdditionalParts = [];
      }
    }

    function getBasketComments() {
      vm.comment = basketService.getBasketComment();
    }

    function configureDatePicker() {
      var d = new Date();
      setTimeout(function () {
        $("#datepicker").datepicker({
          dateFormat: "dd/mm/yy",
          minDate: new Date(d.setDate(d.getDate() + 1)),
          onSelect: function (dateText) {
            vm.requestedDeliveryDate = dateText;
            $scope.$apply();
          },
        });
      }, 200);
    }

    function updateAddresses() {
      var userId = vm.onBehalfOf ? vm.onBehalfOf.userId : null;
      ordersService.getAddresses(userId).then(function (resp) {
        getAddressesSuccess(resp);
        setDefaultBillingAddress(resp.data);
      });
    }

    function updateDeliveryAddressContacts() {
      var userId = vm.onBehalfOf ? vm.onBehalfOf.userId : null;
      var selectedAddress = null;
      try {
        selectedAddress = vm.deliveryAddress ? JSON.parse(vm.deliveryAddress) : null;
      } catch (e) {
        console.error("Error parsing delivery address:", e);
      }
      if (selectedAddress && selectedAddress.id) {
        ordersService.getAddressContacts(selectedAddress.id, userId).then(function (resp) {
          vm.deliveryContacts = resp.data.contacts || [];
          if (NEW_NAME_ID && resp.data.contacts) {
            var index = _.findIndex(resp.data.contacts, { id: NEW_NAME_ID });
            if (index > -1) {
              vm.deliveryName = resp.data.contacts[index].name;
              NEW_NAME_ID = undefined;
            }
          } else if (resp.data.contacts && resp.data.contacts.length === 1) {
            vm.deliveryName = resp.data.contacts[0].name;
            vm.deliveryId = resp.data.contacts[0].id;
          }
        });
      }
    }

    function updateBillingAddressContacts() {
      var userId = vm.onBehalfOf ? vm.onBehalfOf.userId : null;
      var selectedAddress = null;
      try {
        selectedAddress = vm.billingAddress ? JSON.parse(vm.billingAddress) : null;
      } catch (e) {
        console.error("Error parsing billing address:", e);
      }
      if (selectedAddress && selectedAddress.id) {
        ordersService.getAddressContacts(selectedAddress.id, userId).then(function (resp) {
          vm.orderContacts = resp.data.contacts || [];
          if (NEW_NAME_ID && resp.data.contacts) {
            var index = _.findIndex(resp.data.contacts, { id: NEW_NAME_ID });
            if (index > -1) {
              vm.contactName = resp.data.contacts[index].name;
              vm.contactId = resp.data.contacts[index].id;
              NEW_NAME_ID = undefined;
            }
          } else if (resp.data.contacts && resp.data.contacts.length === 1) {
            vm.contactName = resp.data.contacts[0].name;
            vm.contactId = resp.data.contacts[0].id;
          }
        });
      }
    }

    function updateNumbers() {
      var userId = vm.onBehalfOf ? vm.onBehalfOf.userId : null;
      ordersService.getNumbers(userId).then(function (resp) {
        getNumbersSuccess(resp);
        setDefaultNumbers(resp.data);
      });
    }

    function getAddressesSuccess(resp) {
      vm.addresses = resp.data;
      if (NEW_ADDRESS_ID) {
        var index = _.findIndex(vm.addresses, { id: NEW_ADDRESS_ID });
        if (index > -1) {
          vm.deliveryAddress = JSON.stringify(vm.addresses[index]);
          vm.billingAddress = JSON.stringify(vm.addresses[index]);
          NEW_ADDRESS_ID = undefined;
        }
      }
    }

    function getNumbersSuccess(resp) {
      vm.numbers = resp.data;
      if (NEW_NUMBER_ID) {
        var index = _.findIndex(vm.numbers, { id: NEW_NUMBER_ID });
        if (index > -1) {
          vm.contactNumber = vm.numbers[index].contactNumber;
          NEW_NUMBER_ID = undefined;
        }
      }
    }

    function submitEnquiry(purchaseOrderNumber, associatedOrderId) {
      $scope.detailsForm.$submitted = true;
      if (
        $scope.detailsForm.$valid &&
        (vm.savedAdditionalParts.length > 0 || vm.basket.length > 0)
      ) {
        if (vm.isStockWarehousesEnabled) {
          getNextWarehouse(vm.basket);
        }
        submitToServer(purchaseOrderNumber, associatedOrderId);
      } else {
        headerBannerService.setNotification("WARN", PLEASE_COMPLETE, 3000);
      }
    }

    function submitToServer(purchaseOrderNumber, associatedOrderId) {
      vm.submitDisabled = true;
      vm.submitting = true;
      currentPurchaseOrderNumber = purchaseOrderNumber;
      let orderDetails = getOrderDetails(currentPurchaseOrderNumber);
      if (vm.isStockWarehousesEnabled) {
        orderDetails = getSplittedOrder(orderDetails, associatedOrderId);
      }
      ordersService
        .createOrder(orderDetails)
        .then(submitEnquirySuccess, submitEnquiryFailed)
        .finally(function () {
          vm.ifPlaceOrderIsClicked = false;
          vm.submitting = false;
        });
    }

    function getOrderDetails(purchaseOrderNumber) {
      vm.basket.forEach(item => {
        if (item.kitPrice === 'TBC') {
          item.kitPrice = null;
        }
      });
      var orderDetails = {
        deliveryAddress: vm.deliveryAddress,
        billingAddress: vm.billingAddress,
        contactName: vm.contactName,
        deliveryName: vm.deliveryName,
        contactNumber: vm.contactNumber,
        deliveryNumber: vm.deliveryNumber,
        contactId: vm.contactId,
        deliveryId: vm.deliveryId,
        requestedDeliveryDate: vm.requestedDeliveryDate,
        purchaseOrder: purchaseOrderNumber
          ? purchaseOrderNumber
          : vm.purchaseOrder,
        serialNumber: vm.serialNumber,
        stockOrder: vm.stockOrder,
        basket: vm.basket,
        additionalParts: vm.savedAdditionalParts,
        currencyId: vm.defaultCurrency.id,
        comment: vm.comment,
        shippingRequirementExpressDelivery:
          vm.shippingRequirementExpressDelivery,
        shippingRequirementId: vm.shippingRequirementId,
      };

      if (vm.onBehalfOf) {
        orderDetails.manufacturerSubEntityId =
          vm.onBehalfOf.manufacturerSubEntityId;
        orderDetails.emailAddress = vm.onBehalfOf.emailAddress;
        orderDetails.userId = vm.onBehalfOf.userId;
      } else {
        orderDetails.manufacturerSubEntityId =
          userService.getManufacturerSubEntityId();
        orderDetails.emailAddress = userService.getEmailAddress();
        orderDetails.userId = userService.getUserId();
      }
      return orderDetails;
    }

    function proceedToCheckout() {
      $scope.detailsForm.$submitted = true;
      if ($scope.detailsForm.$invalid) {
        return headerBannerService.setNotification(
          "WARN",
          PLEASE_COMPLETE,
          3000
        );
      }

      vm.submitDisabled = true;
      vm.submitting = true;
      const orderDetails = getOrderDetails();
      getShippingRate(orderDetails);
      globalPaymentService.setCurrentOrderDetail(orderDetails);
    }

    function getShowCheckoutBtn() {
      if (!vm.isPaymentEnabled) return (vm.showCheckoutBtn = false);
      vm.showCheckoutBtn =
        validationToProceed() && vm.isCustomer && !vm.isDealerCustomer;
    }

    function isSubEntityPaymentsEnabled() {
      if (
        (vm.isPaymentEnabled && vm.isCustomer && !vm.isDealerCustomer) ||
        (vm.onBehalfOf && vm.isPaymentEnabled && !vm.isDealerCustomer)
      ) {
        vm.isSubEntityPaymentsEnabled = true;
      }
    }

    function isFarmerPaymentsEnabled() {
      if (
        (vm.isPaymentEnabled && vm.isFarmer && !vm.isDealerCustomer) ||
        (vm.onBehalfOf &&
          vm.isPaymentEnabled &&
          !vm.isDealerCustomer &&
          vm.onBehalfOf.userType !== "MANUFACTURER_SUB_ENTITY_DEALER")
      ) {
        vm.isFarmerPaymentsEnabled = true;
      }
    }

    function hideStockWarehouses() {
      if (
        vm.isStockWarehousesEnabled ||
        (vm.onBehalfOf && vm.isStockWarehousesEnabled)
      ) {
        vm.isPreviewStockLevelEnabled = false;
      } else if (
        vm.isPreviewStockLevelEnabled ||
        (vm.onBehalfOf && vm.isPreviewStockLevelEnabled)
      ) {
        vm.isStockWarehousesEnabled = false;
      } else {
        vm.isStockWarehousesEnabled = false;
      }
    }

    function fetchMasterPartPaymentRequirements() {
      vm.masterPartIds = [];
      if (vm.basket && vm.basket.length > 0) {
        vm.basket.forEach((item) => {
          if (item.masterPartId !== undefined && item.masterPartId !== null) {
            vm.masterPartIds.push(item.masterPartId);
          }
          if (item.masterPartKitId !== undefined && item.masterPartKitId !== null) {
            vm.masterPartIds.push(item.masterPartKitId);
          }
        });
      }
      if (vm.isStockWarehousesEnabled && vm.masterPartIds && vm.masterPartIds.length > 0) {
        masterPartService
          .getBasketWarehouseStock(manufacturerId, vm.masterPartIds)
          .then(getBasketWarehouseStockSuccess, getBasketWarehouseStockFailed);
      }
    }

    function getBasketWarehouseStockSuccess(response) {
      response.data.forEach((warehouseStock) => {
        var index = _.findIndex(vm.basket, {
          masterPartId: warehouseStock.masterPartId,
        });
        
        if (index === -1 && warehouseStock.masterPartId !== undefined) {
          index = _.findIndex(vm.basket, {
            masterPartKitId: warehouseStock.masterPartId
          });
        }
    
        if (index > -1) {
          vm.basket[index].warehouseStock = warehouseStock.warehouseStock;
          vm.basket[index].stock = getStockByWarehouse(
            vm.basket[index].warehouseId,
            warehouseStock.warehouseStock
          );
        }
      });
      masterPartService
        .getBasketPartsWeight(vm.masterPartIds)
        .then(getBasketWeightSuccess, getBasketWeightFailed);
    }

    function getBasketWarehouseStockFailed() {
      if (!vm.basket && !vm.basket.length) return;
      vm.basket.forEach((item) => {
        item.stock = 0;
        basketService.updatePart(item);
      });
      getShowCheckoutBtn();
    }

    function getBasketWeightSuccess(response) {
      if (!response.data) return;
      response.data.forEach((masterPart) => {
        var index = _.findIndex(vm.basket, {
          masterPartId: masterPart.masterPartId,
        });
        if (index > -1) {
          vm.basket[index].weight = masterPart.weight;
          vm.basket[index].massUnit = masterPart.massUnit;
        }
      });
      getShowCheckoutBtn();
    }

    function getBasketWeightFailed() {
      if (!vm.basket && !vm.basket.length) return;
      vm.basket.forEach((item) => {
        item.weight = 0;
        item.massUnit = null;
        basketService.updatePart(item);
      });
      getShowCheckoutBtn();
    }

    function validationToProceed() {
      if (vm.basket.length < 1) return false;
      if (vm.comment && vm.comment.length) return false;

      const result = vm.basket.every(
        (obj) =>
          obj.price &&
          obj.stock &&
          obj.quantity <= obj.stock &&
          obj.weight &&
          !isNaN(+obj.weight) &&
          obj.massUnit &&
          !(obj.comment && obj.comment.length)
      );
      if (!result) return false;

      return vm.savedAdditionalParts.length <= 0;
    }

    function submitEnquirySuccess(response) {
      var orderId = response.data;
      if (vm.onBehalfOf) {
        headerBannerService.setNotification(
          "SUCCESS",
          SUBMITTED_ON_BEHALF +
            vm.onBehalfOf.firstName +
            " " +
            vm.onBehalfOf.lastName +
            ".",
          5000
        );
      }
      if (
        (vm.basket && vm.basket.length) ||
        (vm.basket && vm.savedAdditionalParts.length)
      ) {
        if (nextWarehouse) {
          removeSubmittedItems();
          updateTotalItemPrices();
          if (vm.basket.length) {
            submitEnquiry(currentPurchaseOrderNumber, orderId);
            return;
          }
        }
        basketService.emptyBasket();
      }
      if (userService.isDealerPlusUser()) {
        if (vm.onBehalfOf) {
          $state.go("dpOrders.customerOrders.orders.enquiry", {
            orderId: orderId,
          });
        } else {
          $state.go("dpOrders.myOrders.orders.enquiry", { orderId: orderId });
        }
      } else {
        $state.go("orders.enquiry", { orderId: orderId });
      }
    }

    function placeOrder() {
      $scope.detailsForm.$submitted = true;
      if (
        $scope.detailsForm.$valid &&
        (vm.savedAdditionalParts.length > 0 || vm.basket.length > 0)
      ) {
        vm.ifPlaceOrderIsClicked = true;
        vm.submitting = true;
        $uibModal
          .open({
            templateUrl:
              "features/orders/create/purchaseOrder/purchaseOrderModal.html",
            controller: "PurchaseOrderController",
            size: "md",
            controllerAs: "purchaseOrderCtrl",
          })
          .result.then(submitToServer, modalClosed);
      } else {
        headerBannerService.setNotification("WARN", PLEASE_COMPLETE, 3000);
      }
    }

    function modalClosed() {
      vm.submitting = false;
      vm.ifPlaceOrderIsClicked = false;
    }

    function getShippingRate(orderDetails) {
      shippingEngineService
        .getShippingRate(orderDetails)
        .then(getShippingRateSuccess, getShippingRateFailed);
    }

    function hasKits() {
      return vm.basket.some(item => item.kitId !== undefined && item.kitId !== null);
    }

    function getTax(orderDetails) {
      const deliveryAddress = JSON.parse(vm.deliveryAddress);
      const countryList = ordersService.getCountryList();
      const addressCountry = countryList.find(
        (item) => item.name === deliveryAddress.country
      );
      const addressCountryCode = addressCountry.code;
      avaTaxService.setTaxInfo(null);

      if (
        vm.isTaxPaymentEnabled &&
        shippingEngineService.checkCountryRequireTax(addressCountryCode)
      ) {
        const shippingCarrier = shippingEngineService.getShippingCarrier();
        const orderExtraData = {
          shippingCost: shippingCarrier.totalAmount.amount,
        };
        if (orderExtraData) {
          orderDetails.orderExtraData = orderExtraData;
        }
        avaTaxService.getTax(orderDetails).then(getTaxSuccess, getTaxFailed);
        return;
      }
      $state.go("checkout");
    }

    function getTaxFailed(err) {
      vm.submitting = false;
      vm.submitDisabled = false;
      const errors = err.data && err.data.messages;
      const errorMsg = (errors.length > 0 && errors[0].details) || ERROR;
      headerBannerService.setNotification("ERROR", errorMsg, 5000);
    }

    function getTaxSuccess(res) {
      avaTaxService.setTaxInfo(res.data);
      $state.go("checkout");
    }

    function getShippingRateFailed(res) {
      vm.submitting = false;
      vm.submitDisabled = false;
      const errors = res.data && res.data.errors;
      const errorMsg =
        (errors && errors.length > 0 && errors[0].message) || ERROR;
      headerBannerService.setNotification("ERROR", errorMsg, 5000);
    }

    function getShippingRateSuccess(res) {
      vm.submitting = false;
      vm.submitDisabled = false;
      const data = res.data;
      if (
        data.rate_response.errors.length &&
        !data.rate_response.rates.length
      ) {
        headerBannerService.setNotification(
          "ERROR",
          data.rate_response.errors[0].message,
          5000
        );
      }
      const modalInstance = shippingEngineService.showShippingOptions(data);
      modalInstance.result.then((carrier) => {
        if (!carrier) return;

        const orderDetails = getOrderDetails();
        shippingEngineService.setShippingCarrier(carrier);
        getTax(orderDetails);
      });
    }

    function viewLinkedTechDocs(item) {
      if (item.linkedTechDocs.length == 1) {
        $window.open(item.linkedTechDocs[0].url, "_blank");
      } else {
        $uibModal.open({
          templateUrl: "features/orders/linkedTechDocs/orderTechDocModal.html",
          controller: "OrderTechDocController",
          controllerAs: "orderTechDocCtrl",
          size: "md",
          resolve: {
            techDocObject: function () {
              return {
                techDocs: item.linkedTechDocs,
                partNumber: item.partNumber,
              };
            },
          },
        });
      }
    }

    function submitEnquiryFailed() {
      vm.submitting = false;
      vm.submitDisabled = false;
      headerBannerService.setNotification("ERROR", ERROR, 10000);
    }

    function addNewAddress() {
      var customerUserId = null;
      $uibModal
        .open({
          templateUrl: "features/shared/createNewAddress/createNewAddress.html",
          controller: "CreateNewAddressController",
          controllerAs: "createNewAddressCtrl",
          resolve: {
            customerUserId: function () {
              return customerUserId;
            },
            isRegisterMode: function () {
              return false;
            },
            addressData: function () {
              return null;
            },
            showCompanyName: function () {
              return true;
            },
          },
        })
        .result.then(
          function (resp) {
            NEW_ADDRESS_ID = resp;
            updateAddresses();
          },
          function () {
            console.log("Modal Cancelled");
          }
        );
    }

    function deleteShippingRequirements(requirement) {
      var shippingRequirementId = requirement.shippingRequirementId;

      ordersService
        .deleteShippingRequirements(
          vm.manufacturerSubEntityId,
          requirement.shippingRequirementId
        )
        .then(fetchShippingRequirements, serviceFailed);
    }

    function serviceFailed() {
      console.log("Service failed");
    }

    function fetchShippingRequirements() {
      if (!vm.isFarmer) {
        ordersService.getShippingRequirements(vm.manufacturerSubEntityId).then(
          function (response) {
            vm.shippingRequirements = response.data.shippingRequirements;
          },
          function (error) {
            console.error("Error fetching shipping requirements:", error);
          }
        );
      }
    }

    function addShippingRequirements() {
      var modalInstance = $uibModal.open({
        templateUrl:
          "features/shared/createCourierDetails/createCourierDetails.html",
        controller: "CreateCourierDetailsController",
        controllerAs: "createCourierDetailsCtrl",
        size: "lg",
        resolve: {
          shippingRequirements: function () {
            return vm.shippingRequirements;
          },
          manufacturerSubEntityId: function () {
            if (vm.onBehalfOf) {
              return vm.onBehalfOf.manufacturerSubEntityId;
            } else {
              return userService.getManufacturerSubEntityId();
            }
          },
        },
      });

      modalInstance.result.then(
        function (shippingRequirement) {
          vm.shippingRequirements.push(shippingRequirement);
        },
        function () {
          console.log("Modal Cancelled");
        }
      );
    }

    function setSelectedShippingRequirementId(id) {
      vm.shippingRequirementId = id;
    }

    function addNewName() {
      var customerUserId = null;
      $uibModal
        .open({
          templateUrl: "features/shared/createNewName/createNewName.html",
          controller: "CreateNewNameController",
          controllerAs: "createNewNameCtrl",
          resolve: {
            customerUserId: function () {
              return customerUserId;
            },
          },
        })
        .result.then(
          function (resp) {
            NEW_NAME_ID = resp;
            updateDeliveryAddressContacts();
            updateBillingAddressContacts();
          },
          function () {
            console.log("Modal Cancelled");
          }
        );
    }

    function addNewNumber() {
      var customerUserId = null;
      $uibModal
        .open({
          templateUrl: "features/shared/createNewNumber/createNewNumber.html",
          controller: "CreateNewNumberController",
          controllerAs: "createNewNumberCtrl",
          resolve: {
            customerUserId: function () {
              return customerUserId;
            },
          },
        })
        .result.then(
          function (resp) {
            NEW_NUMBER_ID = resp;
            updateNumbers();
          },
          function () {
            console.log("Modal Cancelled");
          }
        );
    }

    function removeItem(part) {
      basketService.removePart(part);
      updateBasket();
      getShowCheckoutBtn();
    }

    function removeKitItem(kit) {
      basketService.removeKit(kit);
      updateBasket();
      getShowCheckoutBtn();
    }

    function updateBasketWarehouse(item, warehouse) {
      item.selectedWarehouse = warehouse.name;
      item.warehouseId = warehouse.warehouseId;
      item.stock = warehouse.stock;
      partUpdated(item);

      basketService.updatePart(item);
    }

    function updateBasketWarehouseKit(kit, warehouse) {

      kit.selectedWarehouse = warehouse.name;
      kit.warehouseId = warehouse.warehouseId;
      kit.stock = warehouse.stock; 

      kitUpdated(kit);

      basketService.updateKit(kit);
    }

    function getUniqueKitWarehouses(kit) {
      var uniqueWarehouses = Array.from(
        new Set(kit.warehouseStock.map((warehouse) => warehouse.warehouseId))
      ).map((warehouseId) => {
        return kit.warehouseStock.find((warehouse) => warehouse.warehouseId === warehouseId);
      });
      return uniqueWarehouses;
    }

    function partUpdated(part) {
      updateTotalItemPrices();
      basketService.updatePart(part);
      updateBasket();
    }

    function kitUpdated(kit) {
      var kitIndex = vm.basket.findIndex(basketItem => basketItem.kitId === kit.kitId);
      if (kitIndex !== -1) {
        vm.basket[kitIndex] = kit;

        var discountedPrice = calculateDiscountedPrice(vm.basket[kitIndex]);
        var quantity = vm.basket[kitIndex].quantity;

        if (discountedPrice !== null && quantity !== null) {
          vm.basket[kitIndex].totalPrice = (discountedPrice * quantity).toFixed(2);
        } else {
          vm.basket[kitIndex].totalPrice = null;
        }
      }

      updateTotalItemPrices();
      basketService.updateKit(kit);
      updateBasket();
    }

    function updateTotalItemPrices() {
      vm.estimatedTotal = 0;

      for (var i = 0; i < vm.basket.length; i++) {
        var item = vm.basket[i];
        var discountedPrice;

          if (item.kitId) {
              discountedPrice = calculateDiscountedPrice({ price: item.kitPrice});
          } else {
              discountedPrice = calculateDiscountedPrice(item);
          }

          item.totalPrice = parseFloat((discountedPrice * item.quantity).toFixed(2));
          vm.estimatedTotal += parseFloat(item.totalPrice).toFixed(2);
      }
      calculateEstimatedTotal();
      calculateEstimatedTotalForKits();
    }

    function createManualPart() {
      vm.manualPartsEditable = true;

      var emptyPart = {
        partNumber: "",
        partDescription: "",
        machineName: "",
        quantity: 1,
      };
      vm.savedAdditionalParts.push(emptyPart);
    }

    function editManualPart() {
      vm.manualPartsEditable = true;
    }

    function removeManualPart(manualPart) {
      var currentManualParts = vm.savedAdditionalParts;
      var itemToRemoveIndex = _.findIndex(currentManualParts, {
        partNumber: manualPart.partNumber,
        partDescription: manualPart.partDescription,
      });
      if (itemToRemoveIndex > -1) {
        currentManualParts.splice(itemToRemoveIndex, 1);
        vm.savedAdditionalParts = currentManualParts;
      }
    }

    function saveManualParts() {
      if (vm.manualPartsForm.$valid) {
        vm.manualPartsEditable = false;
        basketService.updateManualPartsBasketStorage(vm.savedAdditionalParts);
        vm.ifBasketContainsItems =
          basketService.getBasket().length +
            basketService.getManualPartsCount() >
          0;
      }
    }

    function cancelCreateManualParts() {
      vm.manualPartsEditable = false;
      fetchManualParts();
    }

    function calculateEstimatedTotal() {
      vm.estimatedTotal = 0;
      vm.basket.forEach(item => {
        if (!item.kitId || item.handledSeparately) {
          vm.estimatedTotal += parseFloat(item.totalPrice);
        }
      });
    }
    
    function calculateEstimatedTotalForKits() {
      vm.estimatedTotalForKits = 0;
      vm.basket.forEach(item => {
        if (item.kitId) {
          vm.estimatedTotalForKits += item.totalPrice;
        }
      });
    }

    function openPartSearchModal() {
      var orderDetails = {};
      orderDetails.orderId = $stateParams.orderId;
      if (vm.onBehalfOf) {
        orderDetails.userId = vm.onBehalfOf.userId;
      }

      var closeModal = $uibModal.open({
        keyboard: false,
        templateUrl:
          "features/orders/shared/partSearchModal/partSearchModal.html",
        controller: "BasketPartSearchModalController",
        size: "xl",
        controllerAs: "partSearchModalCtrl",
        resolve: {
          orderDetails: function () {
            return orderDetails;
          },
        },
      });
      closeModal.result.then(() => {
        updateBasket();
      });
      document.addEventListener("keyup", function (e) {
        if (e.keyCode === 27) {
          closeModal.close();
        }
      });
    }

    function addComment(comment) {
      $uibModal
        .open({
          templateUrl:
            "features/orders/shared/basketComment/basketComment.html",
          controller: "BasketCommentController",
          size: "lg",
          controllerAs: "basketCommentCtrl",
          resolve: {
            commentObject: function () {
              return comment;
            },
          },
        })
        .result.then(saveAddComment, function () {
          console.log("Modal Cancelled");
        });
    }

    function saveAddComment(commentToSave) {
      vm.comment = commentToSave;
      basketService.updateBasketComment(commentToSave);
      getShowCheckoutBtn();
    }

    function saveAddPartComment(commentToSave) {
      COMMENT_ITEM.comment = commentToSave;
      basketService.updatePart(COMMENT_ITEM);
      updateBasket();
    }

    var COMMENT_ITEM;
    var COMMENT_KIT;

    function addPartComment(comment, item) {
      COMMENT_ITEM = item;
      $uibModal
        .open({
          templateUrl:
            "features/orders/shared/basketComment/basketComment.html",
          controller: "BasketCommentController",
          size: "lg",
          controllerAs: "basketCommentCtrl",
          resolve: {
            commentObject: function () {
              return comment;
            },
          },
        })
        .result.then(saveAddPartComment);
    }

    function addKitComment(comment, kit) {
      COMMENT_KIT = kit;
      $uibModal
        .open({
          templateUrl: "features/orders/shared/basketComment/basketComment.html",
          controller: "BasketCommentController",
          size: "lg",
          controllerAs: "basketCommentCtrl",
          resolve: {
            commentObject: function () {
              return comment;
            },
          },
        })
        .result.then(function(commentToSave) {
          COMMENT_KIT.comment = commentToSave;
          basketService.updateKit(COMMENT_KIT);
          updateBasket();
        });
    }

    function toggleStockOrder() {
      if (vm.stockOrder) {
        vm.serialNumber = null;
        vm.stockOrderSelected = true;
      } else {
        vm.stockOrderSelected = false;
      }
    }

    function isStockAvailable() {
      for (var i = 0; i < vm.basket.length; i++) {
        if (vm.basket[i].stock < 1 || vm.basket[i].stock === null) {
          return false;
        }
      }
      return true;
    }

    function areAllPricesReturned() {
      if (vm.basket.length < 1) {
        return false;
      }
      for (var i = 0; i < vm.basket.length; i++) {
        if (vm.basket[i].price === 0 || vm.basket[i].price === null) {
          return false;
        }
      }
      return vm.savedAdditionalParts.length <= 0;
    }

    function areAllKitPricesReturned() {
      if (vm.basket.length < 1) {
        return false;
      }
      for (var i = 0; i < vm.basket.length; i++) {
        if (vm.basket[i].kitPrice === 0 || vm.basket[i].kitPrice === null) {
          return false;
        }
      }
      return vm.savedAdditionalParts.length <= 0;
    }

    function areAnyPricesReturned() {
      for (var i = 0; i < vm.basket.length; i++) {
        if (vm.basket[i].price !== 0 && vm.basket[i].price !== null) {
          return true;
        }
      }
      return false;
    }

    function isCDEDisclaimerEnabled() {
      if (vm.isCustomer) {
        vm.showDisclaimer = vm.isCDE;
      }
    }

    function showPurchaseOrderInput() {
      if (!vm.isPaymentEnabled) return true;
      return vm.isDealerCustomer;
    }

    function getAllWarehouse(manufacturerId) {
      shippingEngineService
        .getAllWarehouse(manufacturerId)
        .then(getAllWarehouseSuccess, getAllWarehouseFailed);
    }

    function getAllWarehouseFailed(res) {
      headerBannerService.setNotification("ERROR", ERROR, 5000);
    }

    function getAllWarehouseSuccess(res) {
      if (res && res.length > 0) {
        vm.warehouseList = res;
        if (vm.isOnBehalfOf) {
          vm.defaultWarehouseId =
            vm.onBehalfOf.manufacturerSubEntitySettings.wareHouseId;
        }
        if (vm.defaultWarehouseId === null) {
          vm.defaultWarehouseId = vm.warehouseList[0].id;
        }

        if (
          vm.isVerifiedFarmer ||
          vm.isPaymentEnabledNonDealerUser ||
          vm.isManufacturerOnBehalfOf
        ) {
          vm.enquiryPOToLiveEnabled = false;
          fetchMasterPartPaymentRequirements();
        }
        if (vm.defaultWarehouseStock.length == 0) {
          vm.warehouseList.forEach((warehouse) => {
            var warehouseStock = {
              warehouseId: warehouse.id,
              name: warehouse.name,
              stock: 0,
            };
            vm.defaultWarehouseStock.push(warehouseStock);
          });
        }
        updateWarehouseIdForBasket(vm.defaultWarehouseId);
      }
    }

    function updateWarehouseIdForBasket(defaultWarehouseId) {
      if (vm.basket && vm.basket.length > 0) {
        vm.basket.forEach((item) => {
          if (item.masterPartId != undefined && item.masterPartId != null) {
            if (item.warehouseId == undefined || item.warehouseId == null) {
              item.warehouseId = defaultWarehouseId;
            }
            var index = _.findIndex(vm.warehouseList, { id: item.warehouseId });
            if (index > -1) {
              item.selectedWarehouse = vm.warehouseList[index].name;
            }
          } else {
            if (item.warehouseId == undefined || item.warehouseId == null) {
              item.warehouseId = defaultWarehouseId;
            }
            //For non master parts give warehouseStock defaulting to 0 for all warehouses
            item.warehouseStock = vm.defaultWarehouseStock;
            var index = _.findIndex(vm.warehouseList, { id: item.warehouseId });
            if (index > -1) {
              item.selectedWarehouse = vm.warehouseList[index].name;
            }
          }
          basketService.updatePart(item);
        });
      }
    }

    function getUniqueWarehouses(item) {
      return Array.from(
        new Set(item.warehouseStock.map((warehouse) => warehouse.name))
      ).map((name) => {
        return item.warehouseStock.find((warehouse) => warehouse.name === name);
      });
    }

    function getSplittedOrder(orderDetails, associatedOrderId) {
      getNextWarehouse(orderDetails.basket);
      if (nextWarehouse) {
        const isSplit = orderDetails.basket.some(
          (item) => item.warehouseId !== nextWarehouse.id
        );
        if (isSplit) {
          orderDetails.basket = orderDetails.basket.filter(
            (item) => item.warehouseId == nextWarehouse.id
          );
        }
        orderDetails.warehouseId = nextWarehouse.id;
      }
      if (associatedOrderId) {
        orderDetails.associatedOrderId = associatedOrderId;
      }
      return orderDetails;
    }

    function removeSubmittedItems() {
      vm.basket.forEach((item) => {
        if (item.warehouseId == nextWarehouse.id) {
          basketService.removePart(item);
        }
      });
      vm.basket = basketService.getBasket();
    }

    function getNextWarehouse(nextBasket) {
      if (nextBasket && nextBasket.length > 0) {
        nextWarehouse = shippingEngineService.getWarehouseById(
          nextBasket[0].warehouseId
        );
      }
    }

    function getStockByWarehouse(warehouseId, warehouseStock) {
      if (!warehouseId || !warehouseStock || !warehouseStock.length) return 0;
      const basketItem = _.find(warehouseStock, { warehouseId: warehouseId });
      return basketItem.stock || 0;
    }

    function setDefaultBillingAddress(addresses) {
      vm.addresses = addresses;
      vm.deliveryContacts = addresses.contacts || [];
      vm.orderContacts = addresses.contacts || [];
      
      if (vm.addresses.length === 1) {
        vm.billingAddress = JSON.stringify(vm.addresses[0]);
        vm.deliveryAddress = JSON.stringify(vm.addresses[0]);
      }
    }

    function setDefaultNumbers(numbers) {
      vm.numbers = numbers;
      if (vm.numbers.length === 1) {
        vm.contactNumber = vm.numbers[0].contactNumber;
        vm.deliveryNumber = vm.numbers[0].contactNumber;
      }
    }

    function isPartInformationAvailable(item) {
      var partInformation = {
        showPartDescription: item.partDescription,
        showModelName: item.modelName,
        showMachineName: item.machineName,
      };
      vm.showPartDescription = partInformation.showPartDescription;
      vm.showModelName = partInformation.showModelName;
      vm.showMachineName = partInformation.showMachineName;
      return partInformation;
    }

    function isStockHigh(stock) {
      return stock >= 3;
    }

    function isStockLow(stock) {
      return stock < 3 && stock > 0;
    }

    function isStockEmptyOrNull(stock) {
      return stock === null || stock === undefined || stock < 1;
    }

    function tooltip() {
      $(function () {
        $('[data-toggle="tooltip"]').tooltip();
      });
    }

    function getDefaultDiscount() {
      userService
        .getManufacturerSubEntity(vm.manufacturerSubEntityId)
        .then(function (response) {
          vm.defaultDiscount = response.data.defaultDiscount;
          // Call updateTotalItemPrices after setting the default discount
          updateTotalItemPrices();
        })
        .catch(function (error) {
          console.error("Error fetching discount:", error);
        });
        
    }

    function calculateDiscountedPrice(item) {
      var itemPrice = (typeof item.kitPrice !== "undefined" && item.kitPrice !== null) 
        ? parseFloat(item.kitPrice) 
        : parseFloat(item.price);

      if (isNaN(itemPrice)) {
        itemPrice = 0;
      }

      if (
        vm.defaultDiscount &&
        !isNaN(itemPrice) &&
        itemPrice !== null
      ) {
        var discountAmount = itemPrice * (vm.defaultDiscount / 100);
        var discountedPrice = itemPrice - discountAmount;

        return parseFloat(discountedPrice.toFixed(2));
      }

      return !isNaN(itemPrice) ? parseFloat(itemPrice.toFixed(2)) : 0;
    }

    function hidePrice() {
      if (vm.isManufacturer) {
        vm.hidePrice = false;
      } else {
        vm.hidePrice = !userService.getPreviewPricingEnabled();
      }
      return vm.hidePrice;
    }

    function toggleKitsAccordion(kitId, $event) {
      if ($event.target.tagName !== 'INPUT' && $event.target.tagName !== 'BUTTON' && $event.target.tagName !== 'I') {
        if (vm.accordionStates[kitId] === undefined) {
            vm.accordionStates[kitId] = false;
        }
    
        vm.accordionStates[kitId] = !vm.accordionStates[kitId];
        fetchAndDisplayPurchaserKitDetails(kitId)
      }
    }

    function viewPartNote(masterPartId) {
      var that = this;
      $uibModal.open({
        templateUrl: "features/orders/shared/partNote/partNoteModal.html",
        controller: "PartNoteModalController",
        controllerAs: "PartNoteModalCtrl",
        size: "sm",
        resolve: {
          partNoteData: function () {
            vm.basket = basketService.getBasket();
            var matchingPartNote = vm.basket.find(function(item) {
                return item.masterPartId === masterPartId;
              });
      
              var partNote = matchingPartNote ? matchingPartNote.note : null;
              return {
                partNote: partNote, 
              };
        },
      },
    }).result.then(
      function () {
        console.log("Modal closed");
      },
      function () {
        console.log("Error closing modal");
      }
    );
  }

    function fetchAndDisplayPurchaserKitDetails(kitId) {
      masterPartService.getPurchaserKit(vm.manufacturerSubEntityId, kitId)
        .then(function (response) {
          if (response.data) {
            if (response.data.parts && response.data.parts.length > 0) {
              vm.basket.find(basketItem => basketItem.kitId === kitId).kitDetails = response.data.parts;
            } else {
              console.error('Parts array is empty or undefined:', response.data.parts);
            }
          } else {
            console.error('Unexpected data structure:', response.data);
          }
        });
    }

    function updateContactId() {
      if (vm.contactName && vm.orderContacts) {
        const selectedContact = vm.orderContacts.find(contact => contact.name === vm.contactName);
        if (selectedContact) {
          vm.contactId = selectedContact.id;
        }
      }
    }

    function updateDeliveryId() {
      if (vm.deliveryName && vm.deliveryContacts) {
        const selectedContact = vm.deliveryContacts.find(contact => contact.name === vm.deliveryName);
        if (selectedContact) {
          vm.deliveryId = selectedContact.id;
        }
      }
    }
  }
})();
