(function () {
    'use strict';

    angular
        .module('app.services')
        .factory('customerModelService', customerModelService);

    customerModelService.$inject = ['$http', 'apiConstants', '$q'];

    function customerModelService($http, apiConstants, $q) {

        return {
            fetchManualModels: fetchManualModels,

            setMachineImageUrl: setMachineImageUrl,
            getMachineImageUrl: getMachineImageUrl,
            getAutodeskViewerFiles: getAutodeskViewerFiles,
            getModelPartCount: getModelPartCount,
            getModelPartsChunked: getModelPartsChunked
        };

        function fetchManualModels(manualId) {
                return $http.get(apiConstants.url + '/manual/' + manualId + '/models', null);
        }

        function setMachineImageUrl(machineImageUrl) {
            localStorage.setItem("manualImageURL", machineImageUrl);
        }

        function getMachineImageUrl() {
            return localStorage.getItem("manualImageURL");
        }

        function getAutodeskViewerFiles(modelId){
            return $http.get(apiConstants.url + '/model/'+ modelId + '/autodeskresources');
        }

        function getModelPartCount(modelId){
            return $http.get(apiConstants.url + '/model/'+ modelId + '/partCount');
        }

        function getModelPartsChunked(modelId, start, chunkSize){
            return $http.get(apiConstants.url + '/model/'+ modelId + '/viewerDetails?start=' + start + '&size=' + chunkSize);
        }

    }

})();
