// =============================================================================
// Buttons
// =============================================================================

button {
    &:focus {
        outline: 0;
    }
}

.large-btns-pull-right {
    width: 100%;
    text-align: right;

    &.spacing-top {
        padding: $spacing * 4 0 0;
    }

    .btn {
        margin-left: $spacing;
    }

    &.align-bottom {
        position: absolute;
        bottom: 5%;
        padding-right: 5%;
    }
}

.btn {
    font-size: 0.85em;
    text-transform: uppercase;
    padding: $spacing $spacing * 2;
    border-radius: $border-radius;
    border: 1px solid;
    display: inline-block;
    text-decoration: none;
    text-align: center;
    font-weight: 700;
    line-height: 1.69em;

    &.btn-block {
        width: 100%;
        padding: $spacing * 2;
    }

    &.large {
        padding: $spacing;
    }

    &.small {
        padding: $spacing $spacing * 2;
        width: auto;
        height: auto;
        line-height: normal;
    }
    &.xsmall {
        width: auto;
        height: auto;
        font-size: 0.8em;
        line-height: 1.35em;
    }

    &.disabled {
        border-color: #acb2b8 !important;
        background: #f2f6f9 !important;
        color: #acb2b8 !important;
    }

    &.primary {
        color: $white;
        background: $blue;
        border-color: darken($blue, 5%);

        &:active,
        &:link,
        &:visited {
            color: $white;
            background: $blue;
            border-color: darken($blue, 5%);
        }

        &:hover,
        .active {
            color: $white;
            background: darken($blue, 5%);
        }
    }
    &.primary-outline {
        color: darken($blue, 5%);
        background: $white;
        border: solid 1px darken($blue, 5%);

        &:active,
        &:link,
        &:visited {
            color: darken($blue, 10%);
            border-color: darken($blue, 10%);
        }

        &:hover,
        .active {
            color: $white;
            background: darken($blue, 10%);
        }
    }

    &.secondary {
        color: $textdark;
        background: $grey;
        border-color: darken($grey, 5%);

        &:active,
        &:link,
        &:visited {
            color: $textdark;
            background: $grey;
            border-color: darken($grey, 3%);
        }

        &:hover,
        .active {
            color: $textdark;
            background: lighten($grey, 5%);
        }
    }

    &.success {
        color: $white;
        background: $green;
        border-color: darken($green, 5%);

        &:active,
        &:link,
        &:visited {
            color: $white;
            background: $green;
            border-color: darken($green, 5%);
        }

        &:hover,
        .active {
            color: $white;
            background: darken($green, 5%);
        }
    }

    &.info {
        color: $white;
        background: #17a2b8;
        border-color: darken(#17a2b8, 5%);

        &:active,
        &:link,
        &:visited {
            color: $white;
            background: #17a2b8;
            border-color: darken(#17a2b8, 5%);
        }

        &:hover,
        .active {
            color: $white;
            background: darken(#17a2b8, 5%);
        }
    }

    &.enable {
        border-radius: 20px;
        background-color: $grey;
        color: $textdark;
    }

    &.disable {
        border-radius: 20px;
        background-color: #3392fc;
        color: white;
    }

    &.danger {
        color: $danger;
        background: $white;
        border-color: $danger;
        &:active,
        &:link {
            color: $white;
            background: $danger;
            border-color: darken($danger, 20%);
        }

        &:hover,
        .active {
            color: $white;
            background: darken($danger, 10%);
            border-color: darken($danger, 30%);
        }
    }

    &.warning {
        color: $warning;
        background: $white;
        border-color: $warning;
        &:active,
        &:link {
            color: $white;
            background: $warning;
            border-color: darken($warning, 10%);
        }

        &:hover,
        .active {
            color: $white;
            background: $warning;
            border-color: darken($warning, 10%);
        }
    }

    &.comments {
        color: $blue;
        background: $white;
        border-color: $blue;
        &:active,
        &:link {
            color: $white;
            background: $blue;
            border-color: darken($blue, 20%);
        }

        &:hover,
        .active {
            color: $white;
            background: darken($blue, 10%);
            border-color: darken($blue, 30%);
        }
    }

    $customColor: #192f78;

    &.partnotes {
        color: $customColor;
        background: $white;
        border-color: $customColor;
        &:active,
        &:link {
            color: $white;
            background: $customColor;
            border-color: darken($customColor, 20%);
        }

        &:hover,
        .active {
            color: $white;
            background: darken($customColor, 10%);
            border-color: darken($customColor, 30%);
        }
    }
}

.btn-delivery {
    line-height: 25px;
}

.btn-group {
    position: relative;
    vertical-align: middle;
    display: inline-flex;
    word-break: keep-all;
    .btn {
        position: relative;
        padding: $spacing $spacing;
        float: left;
    }
    .btn:first-child:not(:last-child):not(.dropdown-toggle) {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        margin-left: 0;
    }
    .dropdown-toggle {
        padding-right: $spacing;
        padding-left: $spacing;
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
        cursor: pointer;
    }
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    z-index: 10;
    display: none;
    float: left;
    min-width: 160px;
    padding: $spacing 0;
    margin: 2px 0 0;
    font-size: 14px;
    text-align: left;
    list-style: none;
    background-color: #fff;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
    /*border: 1px solid #ccc;*/
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
    li {
        font-size: 1em;
        a {
            display: block;
            padding: $spacing $spacing * 2;
            clear: both;
            font-weight: 400;
            line-height: 1.42857143;
            color: #333;
            white-space: nowrap;
        }
    }
    &.open {
        display: block;
    }
}

.place-order-btn {
    display: block;
    text-align: center;
    margin: 0 auto;
    position: relative;
    bottom: 0;
    width: 100%;
    height: 75px;
    border-radius: 0;
    margin: 0 !important;
}

.part-details {
    height: 48px;
    background-color: #ffffff;
    border: none;
    text-align: left;
    width: 200px;
    font-weight: 800;

    > li {
        color: $blue;
    }
}

i.disabled {
    color: #acb2b8 !important;
    cursor: default !important;
}

.disabled {
    cursor: default;
}

.download-btn {
    width: 100%;
    text-align: center;
}

.footerAddToBasketBtn {
    float: right;
    text-align: center;
    margin: 8px 16px;
}

/* Bulk Upload Navigation Buttons */

.cadBulkHover {
    transition: opacity 0.2s ease-in-out, background-color 0.2s ease-in-out, border 0.2s ease-in-out;
    padding: 0.65em 1em;
    margin-bottom: 1em;
    background-color: transparent;
    border-left: transparent 5px solid;
}

.cadBulkHover:active,
.cadBulkHover:hover,
.cadBulkHover.active {
    cursor: pointer;
    opacity: 1;
    background-color: white;
    padding: 0.65em 1em;
    border-left: #2196f3 5px solid;
    margin-bottom: 1em;
    color: #000;
}

.cadBulkHover a,
.cadBulkHover a:visited {
    color: #000;
    text-decoration: none;
}

.cadBulkHover a:hover {
    color: inherit;
}

/* Bulk Upload Navigation Buttons Underline */

.cadBulkHoverAlt {
    padding: 0.65em 1em;
    margin-bottom: 1em;
}

.cadBulkHoverAlt:active,
.cadBulkHoverAlt:hover,
.cadBulkHoverAlt.active {
    cursor: pointer;
    opacity: 1;
    padding: 0.65em 1em;
    border-bottom: #2196f3 3px solid;
    color: #000;
}

.cadBulkHoverAlt a,
.cadBulkHoverAlt a:visited {
    color: #000;
    text-decoration: none;
}

.cadBulkHoverAlt a:hover {
    color: inherit;
}

/* Button Appear as a link */

.link-button {
    background: none;
    border: none;
    padding: 0;
    font: inherit;
    cursor: pointer;
    outline: inherit;
    color: inherit;
    text-decoration: none;
}
