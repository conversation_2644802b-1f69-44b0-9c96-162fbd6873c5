(function () {
    'use strict';

    angular
        .module('app.services')
        .factory('nonModeledPartService', nonModeledPartService);

    nonModeledPartService.$inject = ['$http', 'apiConstants'];

    function nonModeledPartService($http, apiConstants) {
        return {
            fetchNonModeledPart: fetchNonModeledPart,
            fetchNonModeledPartsForModel: fetchNonModeledPartsForModel,
            //fetchNonModeledPartsForPart: fetchNonModeledPartsForPart,
            deleteNonModeledPart: deleteNonModeledPart,
            createNonModeledPart: createNonModeledPart,
            editNonModeledPart: editNonModeledPart
        };

        function fetchNonModeledPart(nonModeledPartId) {
            return $http.get(apiConstants.url + '/nonmodelled/' + nonModeledPartId);
        }

        function fetchNonModeledPartsForModel(modelId) {
            return $http.get(apiConstants.url + '/nonmodelled/model/' + modelId);
        }

        function deleteNonModeledPart(nonModeledPartId) {
            return $http.delete(apiConstants.url + '/nonmodelled/' + nonModeledPartId);
        }
/*
        function fetchNonModeledPartsForPart(partId) {
            return $http.get(apiConstants.url + '/nonModeledPart/part/' + partId);
        }*/

        function createNonModeledPart(modelId, partId, parts, nonModeledPart) {
            var data = {
                "partId": partId,
                "parts": parts,
                "nonModeledPart": nonModeledPart
            };
            return $http.post(apiConstants.url + '/nonmodelled/model/' + modelId, data);
        }

        function editNonModeledPart(nonModeledPartId, partId, description, nonModeledPart) {
            var data = {
                "partId": partId,
                "parts": description,
                "nonModeledPart": nonModeledPart
            };
            return $http.put(apiConstants.url + '/nonmodelled/' + nonModeledPartId, data);
        }



    }
})();
