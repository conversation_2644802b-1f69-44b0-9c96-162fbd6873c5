(function () {
    "use strict";

    angular.module("app.parts").controller("PartsTabsController", PartsTabsController);

    PartsTabsController.$inject = ["$translate", "userService"];

    function PartsTabsController($translate, userService) {
        var vm = this;

        vm.isSupreme = userService.isSupreme();

        var MANAGE_PARTS, BULK_DATA, MANAGE_KITS;
        $translate(["PARTS_TAB.MANAGE_PARTS", "PARTS_TAB.BULK_DATA", "PARTS_TAB.MANAGE_KITS"]).then(function (resp) {
            MANAGE_PARTS = resp["PARTS_TAB.MANAGE_PARTS"];
            BULK_DATA = resp["PARTS_TAB.BULK_DATA"];
            MANAGE_KITS = resp["PARTS_TAB.MANAGE_KITS"];

            if (vm.isSupreme) {
                vm.tabs = [{ title: MANAGE_PARTS, route: "parts.partsSearch" }, { title: MANAGE_KITS, route: "parts.managekits", activeTab: "/managekits" }];
                
            } else {
                vm.tabs = [
                    { title: MANAGE_PARTS, route: "parts.partsSearch" },
                    { title: MANAGE_KITS, route: "parts.managekits", activeTab: "/managekits" },
                    { title: BULK_DATA, route: "parts.partsUpload" },
                ];
            }
        });
    }
})();
