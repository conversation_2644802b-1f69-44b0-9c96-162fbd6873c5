(function () {
    'use strict';

    angular
        .module('app.viewable')
        .controller('EditWorkInstructionsController', EditWorkInstructionsController);

    EditWorkInstructionsController.$inject = ['workInstructionsService', '$uibModalInstance', 'workInstructions'];

    function EditWorkInstructionsController(workInstructionsService, $uibModalInstance, workInstructions) {

        var vm = this;
        vm.cancel = $uibModalInstance.dismiss;
        vm.workInstructionsName = workInstructions.name;
        vm.workInstructionsDescription = workInstructions.description;
        vm.submitDisabled = false;

        vm.editWorkInstructions = editWorkInstructions;

        initialize();

        function initialize() {
        }

        function editWorkInstructions() {
            vm.submitDisabled = true;

            workInstructionsService.updateWorkInstructions(workInstructions.viewableId, vm.workInstructionsName, vm.workInstructionsDescription)
                .then(editWorkInstructionsSuccess, editWorkInstructionsFailure);
        }

        function editWorkInstructionsSuccess() {
            $uibModalInstance.close();
        }

        function editWorkInstructionsFailure(error) {
            console.log(error);
        }

    }
})();
