(function () {
  "use strict";

  angular
    .module("app.parts")
    .controller("MasterPartController", MasterPartController);

  MasterPartController.$inject = [
    "$stateParams",
    "masterPartService",
    "$state",
    "userService",
    "$uibModal",
    "$translate",
    "priceListService"
  ];

  function MasterPartController(
    $stateParams,
    masterPartService,
    $state,
    userService,
    $uibModal,
    $translate,
    priceListService
  ) {
    var vm = this;

    vm.masterPartId = $stateParams.masterPartId;
    vm.currency = userService.getDefaultCurrency();
    vm.isPriceListEnabled = userService.getPriceListsEnabled();
    vm.manufacturerId = userService.getManufacturerId();
    vm.price = null;
    vm.stock = null;
    vm.hasError = false;
    vm.isLoading = true;

    vm.translations = [];
    vm.kits = [];
    vm.optionSet = [];
    vm.additionalParts = [];
    vm.models = [];

    var DELETE_MASTER_PART, DELETE_MASTER_PART_MODAL;
    $translate([
      "MASTER_PART.DELETE_MASTER_PART",
      "MASTER_PART.DELETE_MASTER_PART_MODAL",
    ]).then(function (resp) {
      DELETE_MASTER_PART = resp["MASTER_PART.DELETE_MASTER_PART"];
      DELETE_MASTER_PART_MODAL = resp["MASTER_PART.DELETE_MASTER_PART_MODAL"];
    });

    vm.editStock = editStock;
    vm.editPrice = editPrice;
    vm.editPriceList = editPriceList;
    vm.editLinkedTechDocs = editLinkedTechDocs;
    vm.deleteMasterPart = deleteMasterPart;
    vm.createSupersede = createSupersede;
    vm.editSupersede = editSupersede;
    vm.deleteSupersede = deleteSupersede;
    vm.createLink = createLink;
    vm.editLink = editLink;
    vm.deleteLink = deleteLink;
    vm.editTranslation = editTranslation;
    vm.createNewTranslation = createNewTranslation;
    vm.deleteTranslation = deleteTranslation;
    vm.editKit = editKit;
    vm.createKit = createKit;
    vm.deleteKit = deleteKit;
    vm.addToKit = addToKit;
    vm.removePartFromKit = removePartFromKit;
    vm.removePartFromSupersession = removePartFromSupersession;
    vm.supersessionHistory = supersessionHistory;
    vm.expandSupersede = expandSupersede;

    vm.editOptionSet = editOptionSet;
    vm.createOptionSet = createOptionSet;
    vm.deleteOptionSet = deleteOptionSet;

    vm.editAdditionalPart = editAdditionalPart;
    vm.createAdditionalPart = createAdditionalPart;
    vm.deleteAdditionalPart = deleteAdditionalPart;

    vm.goToModel = goToModel;
    vm.hasExtensionAlreadyActive = hasExtensionAlreadyActive;

    vm.editPartNote = editPartNote;
    vm.createPartNote = createPartNote;
    vm.deletePartNote = deletePartNote;
    vm.cancelEdit = cancelEdit;
    vm.savePartNote = savePartNote;

    initialize();

    function initialize() {

      var masterPartRequests = [
        masterPartService
          .getMasterPartDetails(vm.masterPartId)
          .then(getMasterPartDetailsSuccess),

        masterPartService
          .getTranslations(vm.masterPartId)
          .then(getTranslationsSuccess)
      ];

      if (vm.isPriceListEnabled) {
        vm.isLoadingPriceList = true;
        masterPartRequests.push(
          priceListService
            .getMasterPartPriceList(vm.masterPartId)
            .then(getMasterPartPriceListSuccess)
        );
      }

      // Handling all requests together
      Promise.all(masterPartRequests)
        .catch(serviceFailed)
        .finally(() => {
          vm.isLoading = false;
          $('[data-toggle="tooltip"]').tooltip();
        });
    }

    function getMasterPartDetailsSuccess(response) {
      vm.partNumber = response.data.partNumber;
      vm.defaultDescription = response.data.description;

      vm.previewPricingEnabled = response.data.previewPricingEnabled;
      vm.previewStockLevelEnabled = response.data.previewStockLevelEnabled;
      vm.hasLinkedPart = response.data.linked;
      vm.hasLinkedTechDocs = response.data.hasLinkedTechDoc;
      vm.hasOptionSet = response.data.containsOptionsSet;
      vm.hasAdditionalPart = response.data.hasNonModelledPart;
      vm.inSupersession = response.data.inSupersession;
      vm.isSuperseded = response.data.superseded;
      vm.partNote = response.data.note;

      masterPartService
        .getPrice(vm.masterPartId)
        .then(getPriceSuccess, serviceFailed);

      masterPartService
        .getStock(vm.masterPartId)
        .then(getStockSuccess, serviceFailed);

      if (vm.inSupersession) {
        vm.isLoadingSupersessionHistory = true;
        masterPartService
          .getSupersessionHistory(vm.manufacturerId, vm.masterPartId)
          .then(getSupersessionHistorySuccess)
          .finally(() => vm.isLoadingSupersessionHistory = false);
      }

      if (vm.hasLinkedPart) {
        getLink();
      }
      if (response.data.inKit) {
        getKits();
      }
      if (vm.hasOptionSet) {
        getOptionSet();
      }
      if (vm.hasAdditionalPart) {
        getAdditionalParts();
      }
      if (vm.partNote) {
             savePartNote();
      }

      masterPartService
        .getModelsForParts(vm.masterPartId)
        .then(getModelsForPartsSuccess, serviceFailed);
    }

    function getStockSuccess(response) {
      vm.stock = response.data;
    }

    function getPriceSuccess(response) {
      vm.price = response.data;
    }

    function getOptionSetForMasterPartSuccess(response) {
      vm.optionSetId = response.data[0].id;
      vm.optionSetDescription = response.data[0].description;
      vm.optionSetParts = response.data[0].optionsSet;
    }

    function getLinkSuccess(response) {
      vm.linkedPart = response.data;
    }

    function getNonModeledPartsSuccess(response) {
      vm.additionalParts = response.data.nonModelledPart;
    }

    function getModelsForPartsSuccess(response) {
      vm.models = response.data;
    }

    function serviceFailed(error) {
      if (error.status === 404) {
        vm.supersessionHistoryResponse = [];
        console.log("No supersession history found for this part.");
        vm.supersessionHistoryResponseMessage = "No supersession history for this part.";
        vm.hasError = true;
      } else {
        console.error("Service failed with error:", error);
      }
    }

    function getKits() {
      vm.inKit = true;
      masterPartService
        .getKitsForMasterPart(vm.masterPartId)
        .then(getKitsSuccess, serviceFailed);
    }

    function getOptionSet() {
      masterPartService
        .getOptionSetForMasterPart(vm.masterPartId)
        .then(getOptionSetForMasterPartSuccess, serviceFailed);
    }

    function getAdditionalParts() {
      masterPartService
        .getAdditionalPart(vm.masterPartId)
        .then(getNonModeledPartsSuccess, serviceFailed);
    }

    function getKitsSuccess(response) {
      vm.kits = response.data;
    }

    function createKit() {
      $state.go('kitassembly', { kitId: null, origin: 'masterPart' });
    }

        function editKit(kitId) {
            $state.go('kitassembly', { kitId: kitId });
        }

    function deleteKit(kitId) {
      var deleteObject = {
        id: kitId,
        url: "/masterPart/kit/" + kitId,
      };

      /*            function deleteKit(kit) {
                vm.successMessage = "";
                var deleteObject = {
                    name: "Kit " + kit.title,
                    id: kit.id,
                    url: '/kit/' + kit.id
                };*/

      $uibModal
        .open({
          templateUrl: "features/shared/commonDelete/deleteDialogBox.html",
          controller: "DeleteController",
          controllerAs: "deleteCtrl",
          size: "sm",
          resolve: {
            deleteObject: function () {
              return deleteObject;
            },
          },
        })
        .result.then(
          function () {
            getKits();
          },
          function () {
            console.log("Modal Cancelled");
          }
        );
    }

    function removePartFromKit(kitId) {
      masterPartService
        .removePartFromKit(kitId, vm.masterPartId)
        .then(getKits, serviceFailed);
    }

    function createOptionSet() {
      $state.go("optionSet", { id: null, masterPartId: vm.masterPartId });
    }

    function editOptionSet() {
      $state.go("optionSet", {
        id: vm.optionSetId,
        masterPartId: vm.masterPartId,
      });
    }

    function deleteOptionSet() {
      var deleteObject = {
        name: vm.optionSetDescription,
        id: vm.optionSetId,
        url: "/masterPart/optionSet/" + vm.optionSetId,
      };

      $uibModal
        .open({
          templateUrl: "features/shared/commonDelete/deleteDialogBox.html",
          controller: "DeleteController",
          controllerAs: "deleteCtrl",
          size: "sm",
          resolve: {
            deleteObject: function () {
              return deleteObject;
            },
          },
        })
        .result.then(
          function () {
            getOptionSet();
            vm.hasOptionSet = false;
          },
          function () {
            console.log("Modal Cancelled");
          }
        );
    }

    function createAdditionalPart() {
      $state.go("AdditionalPart", {
        isEdit: false,
        masterPartId: vm.masterPartId,
      });
    }

    function editAdditionalPart() {
      $state.go("AdditionalPart", {
        isEdit: true,
        masterPartId: vm.masterPartId,
      });
    }

    function deleteAdditionalPart() {
      var deleteObject = {
        name: "BOM ",
        id: vm.masterPartId,
        url: "/masterPart/" + vm.masterPartId + "/nonModelled/",
      };

      $uibModal
        .open({
          templateUrl: "features/shared/commonDelete/deleteDialogBox.html",
          controller: "DeleteController",
          controllerAs: "deleteCtrl",
          size: "sm",
          resolve: {
            deleteObject: function () {
              return deleteObject;
            },
          },
        })
        .result.then(
          function () {
            getAdditionalParts();
            vm.hasAdditionalPart = false;
          },
          function () {
            console.log("Modal Cancelled");
          }
        );
    }

    function goToModel(modelIndex) {
      var model = vm.models[modelIndex];
      if (vm.models[modelIndex].is2d) {
        $state.go("pdfViewer", {
          productId: model.machineId,
          autodeskURN: model.autodeskUrn,
          modelId: model.modelId,
          machineName: model.machineName,
          viewableName: model.modelName,
        });
      } else {
        $state.go("manufacturerViewer", {
          productId: model.machineId,
          autodeskURN: model.autodeskUrn,
          modelId: model.modelId,
          machineName: model.machineName,
          viewableName: model.modelName,
          translateType: model.translateType,
        });
      }
    }

    function editStock() {
      $uibModal
        .open({
          templateUrl: "features/parts/extensions/stock/stockModal.html",
          controller: "StockController",
          controllerAs: "stockCtrl",
          size: "sm",
          resolve: {
            stockObject: function () {
              return {
                stock: vm.stock,
                masterPartId: vm.masterPartId,
                partNumber: vm.partNumber,
              };
            },
          },
        })
        .result.then(
          function (stock) {
            vm.stock = stock;
          },
          function () {
            console.log("Modal Cancelled");
          }
        );
    }

    function editPrice() {
      $uibModal
        .open({
          templateUrl: "features/parts/extensions/price/priceModal.html",
          controller: "PriceController",
          controllerAs: "priceCtrl",
          size: "sm",
          resolve: {
            priceObject: function () {
              return {
                price: vm.price,
                masterPartId: vm.masterPartId,
                partNumber: vm.partNumber,
              };
            },
          },
        })
        .result.then(
          function (price) {
            vm.price = price;
          },
          function () {
            console.log("Modal Cancelled");
          }
        );
    }

    function editPriceList(index) {
      var priceListObject = vm.priceLists[index];
      $uibModal
        .open({
          templateUrl:
            "features/parts/extensions/priceList/priceListModal.html",
          controller: "PriceListController",
          controllerAs: "priceListCtrl",
          size: "sm",
          resolve: {
            priceListObject: function () {
              return priceListObject;
            },
          },
        })
        .result.then(
          function () {
            priceListService
              .getMasterPartPriceList(vm.masterPartId)
              .then(getMasterPartPriceListSuccess, serviceFailed);
          },
          function () {
            console.log("Modal Cancelled");
          }
        );
    }

    function editLinkedTechDocs() {
      $uibModal
        .open({
          templateUrl:
            "features/parts/extensions/linkedTechDoc/linkTechDocModal.html",
          controller: "LinkedTechDocController",
          controllerAs: "techDocCtrl",
          size: "md",
          backdrop: "static",
          resolve: {
            linkTechDocObject: function () {
              return {
                masterPartId: vm.masterPartId,
                partNumber: vm.partNumber,
                hasLinkedTechDocs: vm.hasLinkedTechDocs,
              };
            },
          },
        })
        .result.then(
          function () {
            $state.reload();
          },
          function () {
            console.log("Modal Cancelled");
          }
        );
    }

    function deleteMasterPart() {
      var confirmObject = {
        titleText: DELETE_MASTER_PART,
        bodyText: DELETE_MASTER_PART_MODAL + vm.partNumber + "?",
      };
      $uibModal
        .open({
          templateUrl: "features/shared/confirmationModal/confirmModal.html",
          controller: "ConfirmModalController",
          controllerAs: "confirmModalCtrl",
          size: "sm",
          resolve: {
            confirmObject: function () {
              return confirmObject;
            },
          },
        })
        .result.then(deleteMasterPartConfirmed, doNothing);
    }

    function deleteMasterPartConfirmed() {
      masterPartService
        .deleteMasterPart(vm.masterPartId)
        .then(deleteMasterPartSuccess, serviceFailed);
    }

    function deleteMasterPartSuccess() {
      $state.go("parts.partsSearch");
    }

    function doNothing() {
      //do nothing
    }

    function createLink() {
      var linkObject = {
        masterPartId: vm.masterPartId,
        partNumber: vm.partNumber,
      };
      launchLinkModal(linkObject);
    }

    function editLink() {
      var linkObject = {
        masterPartId: vm.masterPartId,
        partNumber: vm.partNumber,
        rangeId: vm.linkedPart.rangeId,
        machineId: vm.linkedPart.machineId,
        viewableId: vm.linkedPart.modelId,
      };
      launchLinkModal(linkObject);
    }

    function launchLinkModal(linkObject) {
      $uibModal
        .open({
          templateUrl: "features/parts/extensions/link/linkModal.html",
          controller: "LinkController",
          controllerAs: "linkCtrl",
          resolve: {
            linkObject: function () {
              return linkObject;
            },
          },
        })
        .result.then(
          function () {
            vm.hasLinkedPart = true;
            masterPartService
              .getLink(vm.masterPartId)
              .then(getLinkSuccess, serviceFailed);
          },
          function () {
            console.log("Modal Cancelled");
          }
        );
    }

    function deleteLink() {
      var deleteObject = {
        name: vm.linkedPart.machineName + " - " + vm.linkedPart.modelName,
        id: vm.masterPartId,
        url: "/masterPart/" + vm.masterPartId + "/linkedPart",
      };

      $uibModal
        .open({
          templateUrl: "features/shared/commonDelete/deleteDialogBox.html",
          controller: "DeleteController",
          controllerAs: "deleteCtrl",
          size: "sm",
          resolve: {
            deleteObject: function () {
              return deleteObject;
            },
          },
        })
        .result.then(
          function () {
            getLink();
            vm.hasLinkedPart = false;
            vm.linkedPart = null;
          },
          function () {
            console.log("Modal Cancelled");
          }
        );
    }

    function getLink() {
      masterPartService
        .getLink(vm.masterPartId)
        .then(getLinkSuccess, serviceFailed);
    }

    function createSupersede() {
      var supersedeObject = {
        masterPartId: vm.masterPartId,
        partNumber: vm.partNumber,
      };
      launchSupersedeModal(supersedeObject);
    }

    function editSupersede() {
      var supersedeObject = {
        masterPartId: vm.masterPartId,
        partNumber: vm.partNumber,
        rangeId: vm.supersededPart.rangeId,
        machineId: vm.supersededPart.machineId,
        viewableId: vm.supersededPart.modelId,
      };
      launchSupersedeModal(supersedeObject);
    }

    function launchSupersedeModal(supersedeObject) {
      $uibModal
        .open({
          templateUrl:
            "features/parts/extensions/supersede/supersedeModal.html",
          controller: "SupersedeController",
          controllerAs: "supersedeCtrl",
          resolve: {
            supersedeObject: function () {
              return supersedeObject;
            },
          },
        })
        .result.then(
          function () {
            vm.inSupersession = true;
            getSuperseded();
          },
          function () {
            console.log("Modal Cancelled");
          }
        );
    }

    function deleteSupersede() {
      var deleteObject = {
        name:
          vm.supersededPart.machineName + " - " + vm.supersededPart.modelName,
        id: vm.masterPartId,
        url: "/masterPart/" + vm.masterPartId + "/superseded/",
      };

      $uibModal
        .open({
          templateUrl: "features/shared/commonDelete/deleteDialogBox.html",
          controller: "DeleteController",
          controllerAs: "deleteCtrl",
          size: "sm",
          resolve: {
            deleteObject: function () {
              return deleteObject;
            },
          },
        })
        .result.then(
          function () {
            getSuperseded();
            vm.inSupersession = false;
            vm.supersededPart = null;
          },
          function () {
            console.log("Modal Cancelled");
          }
        );
    }

    function getSupersedeSuccess(response) {
      vm.supersededPart = response.data;
    }

    function getSuperseded() {
      masterPartService
        .getSupersede(vm.masterPartId)
        .then(getSupersedeSuccess, serviceFailed);
    }

    function editTranslation(translation) {
      var translationObject = {
        isEdit: true,
        masterPartId: vm.masterPartId,
        partNumber: vm.partNumber,
        language: {
          displayText: translation.displayText,
          languageId: translation.languageId,
        },
        description: translation.description,
      };
      launchTranslationModal(translationObject);
    }

    function createNewTranslation(translation) {
      var translationObject = {
        isEdit: false,
        masterPartId: vm.masterPartId,
        partNumber: vm.partNumber,
        language: {
          displayText: translation.displayText,
          languageId: translation.languageId,
        },
        description: translation.description,
      };
      launchTranslationModal(translationObject);
    }

    function launchTranslationModal(translationObject) {
      $uibModal
        .open({
          templateUrl:
            "features/parts/extensions/translations/translationModal.html",
          controller: "TranslationController",
          controllerAs: "translationCtrl",
          resolve: {
            translationObject: function () {
              return translationObject;
            },
          },
        })
        .result.then(getTranslations, doNothing);
    }

    function deleteTranslation(languageId) {
      masterPartService
        .deleteTranslation(vm.masterPartId, languageId)
        .then(getTranslations, serviceFailed);
    }

    function getTranslations() {
      masterPartService
        .getTranslations(vm.masterPartId)
        .then(getTranslationsSuccess, serviceFailed);
    }

    function getTranslationsSuccess(response) {
      vm.translations = response.data;
      var allLanguages = userService.getUserLanguages();
      for (var i = 0; i < allLanguages.length; i++) {
        if (
          _.findIndex(vm.translations, {
            languageId: allLanguages[i].languageId,
          }) < 0
        ) {
          vm.translations.push({
            languageId: allLanguages[i].languageId,
            displayText: allLanguages[i].displayText,
            description: null,
          });
        }
      }
    }

    function getMasterPartPriceListSuccess(response) {
      vm.priceLists = response.data;
    }

    function addToKit() {
      $uibModal
        .open({
          templateUrl: "features/parts/extensions/addToKit/addToKitModal.html",
          controller: "AddToKitController",
          controllerAs: "addToKitCtrl",
          size: "sm",
          resolve: {
            addToKitObject: function () {
              return {
                masterPartId: vm.masterPartId,
                partNumber: vm.partNumber,
              };
            },
          },
        })
        .result.then(getKits, doNothing);
    }

    function hasExtensionAlreadyActive() {
      return (
        vm.isSuperseded ||
        vm.hasLinkedPart ||
        vm.hasOptionSet ||
        vm.hasAdditionalPart
      );
    }

    function createPartNote() {
      vm.isPartNoteAdded = false;
      vm.isPartNoteEdit = true;
      vm.partNote = "";
    }

    function editPartNote() {
      vm.isPartNoteEdit = true;
      vm.editedNote = vm.partNote;
    }

    function cancelEdit() {
      vm.isPartNoteEdit = false;
      vm.partNote = vm.editedNote;
    }

    function savePartNote() {
      masterPartService.updatePartNote(vm.masterPartId, vm.partNote).then(
        function (response) {
          doesPartNoteContainBulletPoints();
          vm.isPartNoteEdit = false;
        },
        function (error) {
          console.error("Failed to edit part note:", error);
        }
      );
    }

    function doesPartNoteContainBulletPoints() {
      var bulletPoint = /^\s*[\u2022\-]/; // added unicode for bullet point
      var lines = vm.partNote.split('\n');
      vm.partNoteContent = {
        isBulletList: lines.some(line => bulletPoint.test(line)),
        content: lines
      };
    }

    function deletePartNote() {
      masterPartService.deletePartNote(vm.masterPartId).then(
        function (response) {
          vm.partNote = "";
          vm.isPartNoteEdit = false;
          vm.isPartNoteAdded = false;
        },
        function (error) {
          console.error("Failed to delete part note:", error);
        }
      );
    }

    function supersessionHistory() {
      $uibModal
        .open({
          templateUrl: "features/parts/extensions/supersessionHistory/supersessionHistoryModal.html",
          controller: "SupersessionHistoryController",
          controllerAs: "supersessionHistoryCtrl",
          size: "sm",
          resolve: {
            masterPartId: function () {
              return vm.masterPartId;
            },
            partNumber: function () {
              return vm.partNumber;
            },
          },
        })
        .result.then(
          function (result) {
            getSupersessionHistory();
            vm.hasError = false;
            vm.isLoadingSupersessionHistory = true;
            masterPartService
              .getMasterPartDetails(vm.masterPartId)
              .then(getMasterPartDetailsSuccess)
          },
          function () {
            console.log("Supersession modal dismissed");
          }
        );
    }

    function getSupersessionHistory() {
      vm.isLoadingSupersessionHistory = true;
      return masterPartService
        .getSupersessionHistory(vm.manufacturerId, vm.masterPartId)
        .then(getSupersessionHistorySuccess)
        .catch(serviceFailed)
        .finally(() => {
          vm.isLoadingSupersessionHistory = false;
        });
    }

    function getSupersessionHistorySuccess(response) {
      if (response.data.supersessionHistory) {
          var currentLanguage = $translate.use();
  
          vm.inSupersession = true;

          response.data.supersessionHistory.sort((a, b) => b.supersessionIndex - a.supersessionIndex);
  
          vm.supersessionHistoryResponse = response.data.supersessionHistory.map(function (item) {
              var matchingDescription = "";
              
              if (item.languageDescriptions && item.languageDescriptions.length > 0) {
                  for (let desc of item.languageDescriptions) {
                      if (desc.code === currentLanguage) {
                          matchingDescription = desc.description;
                          break;
                      }
                  }
              }
  
              return {
                  masterPartId: item.masterPartId,
                  partNumber: item.partNumber,
                  partDescription: matchingDescription,
                  supersedingPartNumber: item.supersedingPartNumber
              };
          });
          vm.hasError = false;
      } else {
          console.error("Supersession history is not in the expected array format.");
          vm.supersessionHistoryResponse = [];
          vm.hasError = true;
      }
  }  

    function removePartFromSupersession() {
      var historyLength = vm.supersessionHistoryResponse.length;

      return $uibModal.open({
        templateUrl: "features/parts/extensions/supersessionHistory/removeSupersessionModal.html",
        controller: "RemoveSupersessionController",
        controllerAs: "removeSupersessionCtrl",
        size: "sm",
        resolve: {
          masterPartId: function () {
            return vm.masterPartId;
          },
          isSuperseded: function () {
            return vm.isSuperseded;
          },
          supersessionHistoryResponse: function () {
            return vm.supersessionHistoryResponse;
          }
        }
      }).result.then(
        function (result) {
          getSupersessionHistory();
          return masterPartService.getMasterPartDetails(vm.masterPartId)
            .then(getMasterPartDetailsSuccess);
        },
        function () {
          console.log("Supersession modal dismissed");
        }
      );
    }

    function expandSupersede() {
      $uibModal.open({
        templateUrl: "features/parts/extensions/supersessionHistory/expandSupersessionModal.html",
        controller: "ExpandSupersessionController",
        controllerAs: "expandSupersessionCtrl",
        size: "sm",
        resolve: {
          supersessionHistory: function () {
            return vm.supersessionHistoryResponse;
          },
          masterPartId: function () {
            return vm.masterPartId;
          },
          partNumber: function () {
            return vm.partNumber;
          },
          removePartFromSupersession: function () {
            return removePartFromSupersession;
          },
          isLoadingSupersessionHistory: function () {
            return vm.isLoadingSupersessionHistory;
          }
        }
      }).result.then(
      ).catch(function () {
        console.error("Supersession modal dismissed");
      });
    }


  }
})();
