// *******************************************
// Sharing Viewer Extension
// *******************************************

var _this = null;

function SharingViewer(viewer, options) {
    this.socket = null;
    this.lastSend = (new Date()).getTime();
    this.isPresenting = options.isHost;
    this.notificationService = null;
    this.liveMeetingData = {
        users: []
    };
    this.isHost = options.isHost;
    _this = this;

    // 900000 msinterval  to check if users are active in meeting
    if (_this.isHost) {
        _this.isMeetingActiveInterval = setInterval(function () {
            if (_this.lastSend + 900000 < (new Date()).getTime()) {
                localStorage.removeItem("liveMeetingInfo");
                _this.leaveSharing();
                _this.leaveMeeting();
            }
        }, 300000);
    }
    Autodesk.Viewing.Extension.call(this, viewer, options);
}

SharingViewer.prototype = Object.create(Autodesk.Viewing.Extension.prototype);
SharingViewer.prototype.constructor = SharingViewer;

SharingViewer.prototype.load = function () {
    return true;
};

SharingViewer.prototype.setSocket = function (socket, fullName, roomId) {
    this.fullName = fullName;
    this.socket = socket;
    this.roomId = roomId;
}

SharingViewer.prototype.leaveSharing = function () {
    if (_this.isHost)
        clearInterval(_this.isMeetingActiveInterval);
    document.getElementById('MyViewerDiv').removeEventListener('click', _this.setPresenter);
    viewer.removeEventListener(Autodesk.Viewing.CAMERA_CHANGE_EVENT, _this.onStateChanged);
    viewer.removeEventListener(Autodesk.Viewing.ISOLATE_EVENT, _this.onStateChanged);
    viewer.removeEventListener(Autodesk.Viewing.HIDE_EVENT, _this.onStateChanged);
    viewer.removeEventListener(Autodesk.Viewing.SHOW_EVENT, _this.onStateChanged);
    _this.isPresenting = false;
    _this.roomId = null;
    _this.notificationService.removeNotification();
    _this.unload();
};

SharingViewer.prototype.joinSharing = function () {
    _this.socket.on(
        "updateState", this.onNewState);
    _this.socket.on(
        "updateSnapshot", this.onNewSnapshot);

    document.getElementById('MyViewerDiv').addEventListener('click', _this.setPresenter);

    var viewer = this.viewer;

    viewer.addEventListener(Autodesk.Viewing.CAMERA_CHANGE_EVENT, _this.onStateChanged);
    viewer.addEventListener(Autodesk.Viewing.ISOLATE_EVENT, _this.onStateChanged);
    viewer.addEventListener(Autodesk.Viewing.HIDE_EVENT, _this.onStateChanged);
    viewer.addEventListener(Autodesk.Viewing.SHOW_EVENT, _this.onStateChanged);
    viewer.addEventListener(Autodesk.Viewing.SELECTION_CHANGED_EVENT, _this.onStateChanged);
}

SharingViewer.prototype.setPresenter = function () {
    console.log('taking control')
    _this.liveMeetingData.presenter = _this.fullName;
    _this.notificationService.setNotification(_this.liveMeetingData);
    _this.isPresenting = true;
}

SharingViewer.prototype.onStateChanged = function (e) {
    if (!_this.isPresenting) return;
    // this 200 ms latency should reduce traffic, but still good
    if (_this.lastSend + 200 > (new Date()).getTime()) return;
    _this.lastSend = (new Date()).getTime();

    var data = {
        state: viewer.getState(),
        visibleIds: getVisibleIds()
    };
    _this.socket.emit("stateChanged", JSON.stringify(data));
}

SharingViewer.prototype.onSnapshotChanged = function (snapshotId) {
    if (!_this.isPresenting) return;
    // this 200 ms latency should reduce traffic, but still good
    if (_this.lastSend + 200 > (new Date()).getTime()) return;
    _this.lastSend = (new Date()).getTime();

    _this.socket.emit("snapshotChanged", JSON.stringify(snapshotId));
}

function getVisibleIds(){
    return angular.element(document.getElementById("manufacturerViewerId")).scope() ?
        angular.element(document.getElementById("manufacturerViewerId")).scope().manufacturerViewerCtrl.getVisibleIds() :
        angular.element(document.getElementById("customerViewerId")).scope().customerViewerCtrl.getVisibleIds();
}

SharingViewer.prototype.setNotificationService = function (notificationService) {
    this.notificationService = notificationService;
    this.notificationService.setMethodToRemoveEvents(_this.leaveSharing);
}

SharingViewer.prototype.updateRooms = function () {
    _this.socket.emit("updateRooms", _this.roomId, function (isRoomJoined) {
        if (isRoomJoined) {
        } else {
            _this.leaveMeeting();
            _this.leaveSharing();
            console.log("Host is yet to join the call");
        }
    });
    _this.socket.on("leaveMeeting", function () {
        _this.leaveSharing();
    });
    _this.liveMeetingData.presenter = _this.fullName;
    _this.notificationService.setNotification(_this.liveMeetingData);
}

SharingViewer.prototype.setUsers = function (invitedUsers) {
    this.socket.emit("setInvitedUsers", invitedUsers);
    this.socket.on("updateUsersRoom", function (username, room, status) {
        if (room === _this.roomId) {
            if ((username !== null) && (status === "join")) {
                _this.liveMeetingData.users.push(username);
            } else {
                var i = _this.liveMeetingData.users.length;
                while (i--) {
                    if (_this.liveMeetingData.users[i] === username) {
                        _this.liveMeetingData.users.splice(i, 1);
                    }
                }
            }
            _this.notificationService.setNotification(_this.liveMeetingData);
        }
    });
}

SharingViewer.prototype.onNewSnapshot = function (username, snapshotId) {
    _this.lastSend = (new Date()).getTime();
    _this.liveMeetingData.presenter = username;
    _this.notificationService.setNotification(_this.liveMeetingData);
    if (username !== _this.fullName) {
        _this.isPresenting = false;

        if (angular
            .element(document.getElementById("manufacturerViewerId")).scope()) {
            angular
                .element(document.getElementById("manufacturerViewerId"))
                .scope()
                .manufacturerViewerCtrl.loadSnapshot(JSON.parse(snapshotId), true);
        } else {
            angular
                .element(document.getElementById("customerViewerId"))
                .scope()
                .customerViewerCtrl.loadSnapshot(JSON.parse(snapshotId), true);
        }
    }
}

SharingViewer.prototype.onNewState = function (username, data) {
    _this.lastSend = (new Date()).getTime();
    _this.liveMeetingData.presenter = username;
    _this.notificationService.setNotification(_this.liveMeetingData);
    if (username !== _this.fullName) {
        if(_this.isHost && (_this.liveMeetingData.users.indexOf(username) === -1))
            _this.liveMeetingData.users.push(username);
        _this.isPresenting = false;
        var parsedData = JSON.parse(data);

        if (angular
            .element(document.getElementById("manufacturerViewerId")).scope()) {
            angular
                .element(document.getElementById("manufacturerViewerId"))
                .scope()
                .manufacturerViewerCtrl.remoteUpdateViewer(parsedData.state, parsedData.visibleIds);
        } else {
            angular
                .element(document.getElementById("customerViewerId"))
                .scope()
                .customerViewerCtrl.remoteUpdateViewer(parsedData.state, parsedData.visibleIds);
        }
    }
}

SharingViewer.prototype.leaveMeeting = function () {
    _this.socket.emit("deleteRoom", false, function (isRoomDeleted) {
        if (isRoomDeleted) {
            _this.socket.emit("disconnect");
            console.log("Meeting is stopped");
        } else {
            console.log("Error to delet the room");
        }
    });
}

SharingViewer.prototype.unload = function () {
    clearInterval(_this.isMeetingActiveInterval);
    _this.leaveMeeting();
    // socket.disconnect();
    return true;
};

function GLOBAL_SHARING_EVENT(eventType, id) {
    if (eventType === "SNAPSHOT_CHANGED") {
        SharingViewer.prototype.onSnapshotChanged(id);
    } else {
        SharingViewer.prototype.onStateChanged();
    }
}

Autodesk.Viewing.theExtensionManager.registerExtension('SharingViewer', SharingViewer);