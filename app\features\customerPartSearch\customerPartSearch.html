
<div>
  <h1 translate>CUST_PART_SEARCH.PART_SEARCH</h1>
</div>

<form class="search_contents mb-4 p-5"
    ng-submit="customerPartSearchCtrl.search()">
    <div class="d-flex w-75 search-area">
      <div class="input-group mb-0 pr-3">
        <input class="form-control mr-0" type="search" ng-model="customerPartSearchCtrl.searchValue"
          placeholder="{{customerPartSearchCtrl.isSearchOnlyPartNumbers ? 'PART_SEARCH.SEARCH_BY_PART' : 'PART_SEARCH.SEARCH_BY_PART_AND_DESC'  | translate}}" />
        <div class="input-group-append">
          <button type="button" ng-click="customerPartSearchCtrl.search()" ng-hide="customerPartSearchCtrl.searching" ng-disabled="!customerPartSearchCtrl.searchValue || customerPartSearchCtrl.searchValue.trim() === ''" ng-class="{'disabledElement': !customerPartSearchCtrl.searchValue || customerPartSearchCtrl.searchValue.trim() === ''}"
            class="input-group-text input-group-text-btn btn-anim">
            <i class="pr-0 pr-md-3 fa fa-search"></i>
            <span class="pl-3 search_mobile_disable">{{'GENERAL.SEARCH' | translate}}</span>
          </button>
          <button type="button" ng-show="customerPartSearchCtrl.searching"
            class="input-group-text input-group-text-btn btn-anim">
            <span class="pr-3 search_mobile_disable">{{'GENERAL.SEARCHING' | translate}}</span>
            <span class="spinner-border text-light" role="status" aria-hidden="true"></span>
          </button>
        </div>
      </div>
    </div>

    <div class="d-flex justify-content-center align-content-center flex-wrap mt-4"
      ng-hide="customerPartSearchCtrl.isSearchOnlyPartNumbers">
      <span class="pr-3" translate>CUST_PART_SEARCH.SEARCH_BY</span>
      <label class="pr-3 radio-inline">
        <input type="radio" ng-model="customerPartSearchCtrl.searchBy" value="partNumber"
          style="white-space: nowrap" /><span style="white-space: nowrap" translate>GENERAL.PART_NUMBER</span>
      </label>
      <label class="radio-inline">
        <input type="radio" ng-model="customerPartSearchCtrl.searchBy" value="partDescription"
          style="white-space: nowrap" /><span style="white-space: nowrap" translate>CUST_PART_SEARCH.PART_DESC</span>
      </label>
    </div>

    <div ng-show="!customerPartSearchCtrl.masterParts.length > 0 && !customerPartSearchCtrl.kits.length > 0 && customerPartSearchCtrl.resultsReturned && !customerPartSearchCtrl.searchError">
      <h2 class="center-align" translate>CUST_PART_SEARCH.NO_PARTS</h2>
    </div>
    <div ng-show="customerPartSearchCtrl.searchError">
      <h3 class="error-alert center-align" translate>PART_SEARCH.SEARCH_ERROR</h3>
    </div>
  </form>
  
  <div ng-if="!customerPartSearchCtrl.isLoading">
    <div class="mb-4" ng-include="'features/customerPartSearch/purchaserSearchTables/purchaserSearchKitsTable.html'">
    </div>
    <div ng-include="'features/customerPartSearch/purchaserSearchTables/purchaserSearchPartsTable.html'"></div>
  </div>