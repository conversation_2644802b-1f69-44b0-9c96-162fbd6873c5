(function () {
    'use strict';

    angular
        .module('app.orders')
        .controller('QuotationsController', QuotationsController);

    QuotationsController.$inject = ['ordersService', '$scope', '$controller', 'headerBannerService', '$translate', '$window', 'userService', '$uibModal', '$state'];

    function QuotationsController(ordersService, $scope, $controller, headerBannerService, $translate, $window, userService, $uibModal, $state) {
        var vm = this;
        var CONFIRM_ORDER_ARCHIVED, ARCHIVE_MODAL, CONFIRM_ORDER_BANNER;
        angular.extend(vm, $controller('OrdersController', {$scope: $scope}));

        vm.stage = {};

        $translate(['QUOTATION.QUOTATIONS', 'QUOTATION.QUOTATION', 'ORDERS.CONFIRM_ORDER_ARCHIVED', 'ORDERS.ARCHIVE_MODAL', 'ORDERS.CONFIRM_ORDER_BANNER'])
            .then(function (resp) {
                CONFIRM_ORDER_ARCHIVED = resp["ORDERS.CONFIRM_ORDER_ARCHIVED"];
                ARCHIVE_MODAL = resp["ORDERS.ARCHIVE_MODAL"];
                CONFIRM_ORDER_BANNER = resp["ORDERS.CONFIRM_ORDER_BANNER"];
                vm.stage.plural = resp['QUOTATION.QUOTATIONS'];
                vm.stage.single = resp['QUOTATION.QUOTATION'];
            });
        vm.viewState = 'orders.quotation';
        vm.showPrice = true;
        vm.isFixedHeader = false;
        vm.selectAll = false;
        vm.isManufacturer = userService.isManufacturer();

         vm.loadingInfiniteScrollData = false;
        vm.isOrdersLoaded = false;
        vm.showBackToTopButton = false;
        vm.isManufacturer = userService.isManufacturer()

         vm.scrollToTop = scrollToTop;
         vm.toggleSelectAll = toggleSelectAll;
        vm.loadMoreInfiniteScroll = loadMoreInfiniteScroll;
        vm.bulkArchiveOrder = bulkArchiveOrder;
        vm.isAnyOrderSelected = isAnyOrderSelected;
        
        vm.previewPricingEnabled = userService.getPreviewPricingEnabled();

        initialize();

        function initialize() {
            if (vm.isManufacturer && ('QUOTE')) {
                vm.isArchiveActive = true;
            } else {
                vm.isArchiveActive = false;
            }

            $(function () {
                $('[data-toggle="tooltip"]').tooltip();
            });

            vm.loadingInfiniteScrollData = true;
            ordersService.getQuotations()
                .then(function (resp) {
                    getOrdersSuccess(resp);
                }, serviceCallFailed);
        }

             function getOrdersSuccess(resp) {
            vm.allOrders = resp.data.orders; ;
            vm.totalItems = vm.allOrders.length;
            vm.orders = vm.allOrders.slice(0, 100); 
            vm.isOrdersLoaded = true;
            vm.loadingInfiniteScrollData = false;
            handleInfiniteScroll();
        }

        function serviceCallFailed(resp) {
            headerBannerService.setNotification('ERROR', resp.data.error, 10000);
            vm.isOrdersLoaded = true;
            vm.loadingInfiniteScrollData = false;
        }

       var lastScrollTop = 0;
window.addEventListener('scroll', handleInfiniteScroll);

function handleInfiniteScroll() {
    var threshold = 250;
    var scrollTop = window.scrollY;

    if (scrollTop > lastScrollTop) {
        vm.isFixedHeader = scrollTop > threshold;
    } else if (scrollTop < threshold){
        vm.isFixedHeader = false;
    }
    lastScrollTop = scrollTop;  

    
    if (window.innerHeight + scrollTop >= document.body.offsetHeight - 2 && !vm.loadingInfiniteScrollData && !vm.initialLoadDone) {
        loadMoreInfiniteScroll();
    }
}

        function loadMoreInfiniteScroll() {
            vm.loadingInfiniteScrollData = true;

            var nextItems = vm.allOrders.slice(vm.orders.length, vm.orders.length + 100);
            if (nextItems.length === 0) {
                vm.initialLoadDone = true;
                vm.loadingInfiniteScrollData = false;
                return;
            }

            vm.orders = vm.orders.concat(nextItems);

            vm.loadingInfiniteScrollData = false;
            if (vm.orders.length >= vm.totalItems) {
                vm.initialLoadDone = true;
            }
        }

        function bulkArchiveOrder() {
            var confirmObject = {
                titleText: CONFIRM_ORDER_ARCHIVED,
                bodyText: ARCHIVE_MODAL
            };

            $uibModal.open({
                templateUrl: 'features/shared/confirmationModal/confirmModal.html',
                controller: 'ConfirmModalController',
                controllerAs: 'confirmModalCtrl',
                size: 'sm',
                resolve: {
                    confirmObject: function () {
                        return confirmObject;
                    }
                }
            }).result.then(function () {
                var selectedOrderIds = vm.orders.filter(order => order.selected).map(order => order.orderId);
                ordersService.bulkArchiveOrder(selectedOrderIds)
                    .then(function () {
                        headerBannerService.setNotification('SUCCESS', CONFIRM_ORDER_BANNER, 2000);
                        $state.reload();
                    });
            }, function () {
                console.log('Modal Cancelled');
            });
        }

        function isAnyOrderSelected() {
            return vm.orders.some(order => order.selected);
        }

        function toggleSelectAll() {
            vm.orders.forEach(function (order) {
                order.selected = vm.selectAll;
            });
        }

  function scrollToTop() {
      $window.scrollTo({ top: 0, behavior: "smooth" });
      $("html, body").animate({ scrollTop: 0 }, "slow", function () {
        $("#scrollToTop").removeClass("scrolled-past");
      });
    }

    angular.element($window).on("scroll", function () {
      vm.showBackToTopButton = this.pageYOffset > 100;
      $scope.$apply();
    });

          vm.actions = [
            {
                title: "List Viewables",
                onClick: function (entity) { vm.viewOrder(entity.orderId); },
                icon: "fa-eye",
                label: function () { return $translate.instant("ORDERS.VIEW") + " " + vm.stage.single; }
            }
        ];

        if (vm.isManufacturer) {
            vm.actions.push({
                title: "Cancel",
                onClick: function (entity) { vm.cancelOrder(entity.orderId); },
                icon: "fa-window-close",
                label: function () { return $translate.instant("ORDERS.CANCEL_ORDER") + " " + vm.stage.single; }
            });
        }
    }
})();
