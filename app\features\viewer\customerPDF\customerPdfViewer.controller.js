(function () {
    'use strict';

    angular
        .module('app.viewer')
        .controller('CustomerPdfViewerController', CustomerPdfViewerController);

    CustomerPdfViewerController.$inject = ['viewerService', '$stateParams', '$scope', 'basketService', '$state', 'viewerHelperService',
        '$rootScope', 'viewerBannerService', 'userService', '$timeout', 'securityService', '$translate', 'masterPartService', '$window'];

    function CustomerPdfViewerController(viewerService, $stateParams, $scope, basketService, $state, viewerHelperService,
                                         $rootScope, viewerBannerService, userService, $timeout, securityService, $translate, masterPartService, $window) {
        var vm = this;

        var viewerApp;
        var viewables;
        var viewerLoaded = false;
        var CURRENT_SELECTED_PART = 0;
        var autodeskURN = $stateParams.autodeskURN;
        var documentId = "urn:" + autodeskURN;
        var options = {};
        vm.modelId = $stateParams.modelId;

        viewerHelperService.showSpinner();
        viewerHelperService.setMachineName(vm.machineName);
        viewerHelperService.setCurrentSelectedPart(0);

        vm.partsList = [];
        vm.isVisible = false;
        vm.isPreviewMode = userService.isManufacturer() && !$stateParams.onBehalfOf;
        vm.hasOrdersAccess = userService.hasOrderRole();

        vm.machineName = $stateParams.machineName;
        vm.viewableName = $stateParams.viewableName;
        vm.manualId = $stateParams.manualId;
        vm.isTwoD = false;
        vm.manualPartsCount = 0;
        vm.manualPart = {quantity: 1};
        vm.showWatermark = false;
        vm.fullName = userService.getFullName();
        vm.emailAddress = userService.getEmailAddress();
        vm.isAddBasketButtonClicked = false;
        vm.hasOrdersAccess = userService.hasOrderRole();
        vm.isSupreme = userService.isSupreme();
        vm.isAdditionalPartsEnabled = userService.getAdditionalPartsEnabled();
        vm.fromWhereUsedModal = $window.sessionStorage.getItem('fromWhereUsedModal') === 'true';
        $window.sessionStorage.removeItem("fromWhereUsedModal");

        vm.addPartsToBasket = addPartsToBasket;
        vm.cancel = cancel;
        vm.isQuantityZero = isQuantityZero;
        vm.addPartToBasket = addPartToBasket;
        vm.goToPage = goToPage;
        vm.backToViewables = backToViewables;
        vm.goBackToPartSearch = goBackToPartSearch;
        vm.goToCreateEnquiry = goToCreateEnquiry;
        vm.quantityChanged = quantityChanged;
        vm.openBasket = openBasket;
        vm.closeBasket = closeBasket;
        vm.removePart = removePart;
        vm.partUpdated = partUpdated;
        vm.addManualPartToBasket = addManualPartToBasket;
        vm.closeTab = closeTab;
        vm.onBehalfOf = $stateParams.onBehalfOf && $stateParams.onBehalfOf !== "null" ? JSON.parse(decodeURIComponent(atob($stateParams.onBehalfOf))) : undefined;
        vm.isPreviewMode = userService.isManufacturer() && !$stateParams.onBehalfOf;

        options = viewerService.getAutodeskToken()
            .then(function (resp) {
                options = resp;
                options.useADP = false;
                initialize();
            });

        var PART_ADD_SUCCESS, PREVIEW_NO_ADD;
        $translate(['CUST_PDF_VIEWER.PART_ADD_SUCCESS', 'BUY_PART.PREVIEW_NO_ADD'])
            .then(function (resp) {
                PART_ADD_SUCCESS = resp["CUST_PDF_VIEWER.PART_ADD_SUCCESS"];
                PREVIEW_NO_ADD = resp["BUY_PART.PREVIEW_NO_ADD"];
            });



        function initialize() {
            updateBasket();
            autodeskViewerInitialize();

            vm.showCloseButton = localStorage.getItem('navigatedFrom') === 'customerViewer';
            localStorage.removeItem('navigatedFrom'); // Clear after checking

            viewerService.getPDFStates(vm.modelId)
                .then(function (data) {
                    if (data) {
                        vm.pdfPages = data;
                        if (vm.pdfPages && vm.pdfPages[0]) {
                            for (var i = 0; i < vm.pdfPages.length; i++) {
                                vm.pdfPages[i].selected = (i === 0);
                            }
                        }
                    }
                });
            if (!userService.isManufacturer() && !vm.onBehalfOf) {
                securityService.getCustomerWatermarkSettings($stateParams.modelId)
                    .then(getWatermarkSettingsSuccess);
            }

        }

        function autodeskViewerInitialize() {
            $scope.$on('$locationChangeStart', function (event) {
                if (!viewerLoaded) {
                    event.preventDefault();
                }
            });

            Autodesk.Viewing.Initializer(options, function onInitialized() {
                viewerApp = new Autodesk.Viewing.ViewingApplication('MyViewerDiv');
                viewerApp.registerViewer(viewerApp.k3D, Autodesk.Viewing.Viewer3D);
                viewerApp.loadDocument(documentId, onDocumentLoadSuccess, loadFailure);
            });

        }

        function onDocumentLoadSuccess(doc) {
            viewables = viewerApp.bubble.search({
                'type': 'geometry'
            });
            if (viewables.length === 0) {
                console.error('Document contains no viewables.');
                return;
            }

            viewerApp.selectItem(viewables[0].data, onItemLoadSuccess, onItemLoadFail);
            viewerHelperService.setViewerApp(viewerApp);
        }

        function loadFailure(viewerErrorCode) {
            console.log('onDocumentLoadFailure() - errorCode:' + viewerErrorCode);
            viewerHelperService.hideSpinner();
        }

        function closeTab() {
            try {
                window.close();
                // After a short delay, check if the window is still open
                setTimeout(function() {
                    if (!window.closed) {
                        alert("Please close this tab manually.");
                    }
                }, 200);
            } catch (e) {
                alert("An error occurred while trying to close this tab. Please close it manually.");
            }
        }


        function onDocumentLoadFailure(viewerErrorCode) {
            console.log('onDocumentLoadFailure() - errorCode:' + viewerErrorCode);
            viewerHelperService.hideSpinner();
        }

        function onItemLoadSuccess(viewer) {
            var bgColour = viewerHelperService.getBackgroundColour();
            viewer.setBackgroundColor(bgColour.r, bgColour.g, bgColour.b, bgColour.r, bgColour.g, bgColour.b);
            viewer.setTheme("light-theme");
            viewer.setEnvMapBackground(false);
            viewer.setContextMenu(null);
            var firstTime = !vm.isTwoD;
            vm.isTwoD = true;
            viewerHelperService.setIsTwoD(true);
            if (firstTime) {
                goToPage(vm.pdfPages[0].stateId);
                $scope.$digest();
            }
            viewerLoaded = true;
            viewerHelperService.hideSpinner();
        }

        function onItemLoadFail(errorCode) {
            console.log('onItemLoadFail() - errorCode:' + errorCode);
            viewerHelperService.hideSpinner();
        }

        function shutDownViewer() {
            if (viewerApp && viewerApp.getCurrentViewer() != null) {
                viewerApp.getCurrentViewer().finish();
            }
        }

        $scope.$on('$destroy', function () {
            shutDownViewer();
        });

        function goToPage(stateId) {
            $rootScope.$broadcast("Hide-Non-Modeled-Parts");

            viewerLoaded = false;
            viewerHelperService.showSpinner();
            viewerApp.selectItem(viewables[stateId].data, onItemLoadSuccess, onItemLoadFail);
            viewerHelperService.setCurrentSelectedPart(stateId);
            for (var i = 0; i < vm.pdfPages.length; i++) {
                vm.pdfPages[i].selected = (vm.pdfPages[i].stateId === stateId);
            }

            CURRENT_SELECTED_PART = stateId;
            viewerService.getNonModeledParts(CURRENT_SELECTED_PART, vm.modelId)
                .then(getNonModeledPartsSuccess, getNonModeledPartsFailed);
        }

        function getNonModeledPartsSuccess(resp) {
            if (resp.data) {
                closeBasket();
                $rootScope.$broadcast("Show-Non-Modeled-Parts", resp.data);
            }
        }

        function getNonModeledPartsFailed(error) {
            console.log(error);
        }

        function quantityChanged() {
            if (!vm.part.quantity) {
                vm.part.quantity = 1;
            }

        }

        function backToViewables() {
            if (vm.isPreviewMode) {
                $state.go("productsModels", {
                    productId: $stateParams.productId,
                    machineName: $stateParams.machineName
                });
            } else {
                goToViewables()
            }
        }

        function goBackToPartSearch() {
            $window.sessionStorage.setItem('fromWhereUsedModal', 'false');
            $state.go('customerPartSearch');
        }

        function getManualParams() {
            var params = {
                manualId: vm.manualId,
                onBehalfOf: vm.onBehalfOf ? btoa(encodeURIComponent(JSON.stringify(vm.onBehalfOf))) : "null"
            };
            return params;
        }
        function goToViewables() {
            $state.go("customerManual.viewables", getManualParams());
        }

        function removePart(part) {
            basketService.removePart(part);
            updateBasket();
        }

        function partUpdated(part) {
            basketService.updatePart(part);
            updateBasket();
        }

        $scope.$on("Basket-Updated", function (evt, data) {
            updateBasket();
            if (data && data.action !== 'addPartToBasket') {
                vm.isBasketOpen = true;
            }
        });

        angular.element($window).on('storage', function(event) {
            if (event.key === 'basketUpdate') {
                $scope.$apply(function() {
                    updateBasket();
                });
            }
        });

        function updateBasket() {
            vm.basket = basketService.getBasket();
            vm.basketSize = 0;
            localStorage.setItem('basketUpdate', new Date().toISOString());
            for (var i = 0; i < vm.basket.length; i++) {
                vm.basketSize = vm.basketSize + vm.basket[i].quantity;
            }
            vm.manualPartsCount = basketService.getManualPartsCount();
            vm.basketSize = vm.basketSize + vm.manualPartsCount;
        }

        function goToCreateEnquiry() {
            $state.go('create',
                {
                    onBehalfOf: $stateParams.onBehalfOf
                });
        }

        function openBasket() {
            vm.isBasketOpen = true;
            $rootScope.$broadcast("Hide-Non-Modeled-Parts");
        }

        function closeBasket() {
            vm.isBasketOpen = false;
        }

        function addManualPartToBasket() {
            if ($scope.addManualPartForm.$valid) {
                vm.manualPart.machineName = vm.machineName;
                basketService.addManualPartToBasket(vm.manualPart);
                vm.manualPart = {quantity: 1};
                viewerBannerService.setNotification('SUCCESS', PART_ADD_SUCCESS, 2000);
                vm.isAddBasketButtonClicked = true;

                $timeout(function () {
                    vm.isAddBasketButtonClicked = false;
                }, 500);
            }
        }

        function getWatermarkSettingsSuccess(response) {
            vm.showWatermark = response.data.enabled;
            vm.watermarkImage = response.data.imageUrl;
            if (vm.showWatermark) {
                createWatermark();
            }
        }

        function createWatermark() {
            if (document.getElementById("watermark-image").complete) {

                $timeout(function () {
                    for (var i = 0; i < 500; i++) {
                        var parent = document.getElementById("watermark-container");
                        var watermark = document.getElementById("watermark-tile");
                        var newWatermark = watermark.cloneNode(true);
                        parent.appendChild(newWatermark);
                    }
                }, 500);
            } else {
                createWatermark();
            }
        }

        function cancel() {
            hideBuyParts();
        }

        $scope.$on("Show-Non-Modeled-Parts", setNonModelledParts);
        $scope.$on("Hide-Non-Modeled-Parts", hideBuyParts);

        function setNonModelledParts(event, nonModelled) {
            vm.isVisible = true;
            vm.partsList = nonModelled;

            for (var i = 0; i < nonModelled.length; i++) {
                vm.partsList[i].quantity = 0;
                vm.partsList[i].clicked = false;
            }

        }

        function hideBuyParts() {
            vm.isVisible = false;
        }

        function addPartsToBasket() {
            if (vm.isPreviewMode) {
                alert(PREVIEW_NO_ADD);
            } else {
                var partAdded = false;
                var machineName = viewerHelperService.getMachineName();
                var manufacturerId = userService.getManufacturerId();
                var manufacturerSubEntityId = vm.onBehalfOf ? vm.onBehalfOf.manufacturerSubEntityId : userService.getManufacturerSubEntityId();
                var onBehalfOfUserId = vm.onBehalfOf ? vm.onBehalfOf.userId : null;
                var isExactMatch = true;
                vm.partsList.forEach(function (part) {
                    if (part.quantity > 0) {
                        masterPartService.partSearch(
                            manufacturerId,
                            manufacturerSubEntityId,
                            part.partNumber,
                            'partNumber',
                            onBehalfOfUserId,
                            isExactMatch
                        )
                            .then(function (resp) {
                                vm.isVisible = false;
                                part.machineName = machineName;
                                
                                if(resp.data.totalResults > 0)
                                    console.log (resp.data)
                                     if (resp.data.masterParts && resp.data.masterParts.length > 0) {
                                         part.masterPartId = resp.data.masterParts[0].masterPartId;
                                         part.price = resp.data.masterParts[0].price;
                                         part.stock = resp.data.masterParts[0].stock;
                                    }
                                basketService.addPart(part);
                                partAdded = true;
                                part.quantity = 0;
                            })
                    }
                });
                $rootScope.$broadcast("Basket-Updated", {action: 'addPartsToBasket'});
            }
        }

        function addPartToBasket(part) {
            if (vm.isPreviewMode) {
                alert(PREVIEW_NO_ADD);
            } else {
                var partCopy = angular.copy(part);

                if (partCopy.quantity === 0) {
                    partCopy.quantity = 1;
                }
                var manufacturerId = userService.getManufacturerId();
                var manufacturerSubEntityId = vm.onBehalfOf ? vm.onBehalfOf.manufacturerSubEntityId : userService.getManufacturerSubEntityId();
                var onBehalfOfUserId = vm.onBehalfOf ? vm.onBehalfOf.userId : null;
                var isExactMatch = true;

                masterPartService.partSearch(
                    manufacturerId,
                    manufacturerSubEntityId,
                    partCopy.partNumber,
                    'partNumber',
                    onBehalfOfUserId,
                    isExactMatch
                )
                .then(function (resp) {
                    partCopy.machineName = viewerHelperService.getMachineName();
                    if(resp.data.totalResults > 0 && resp.data.masterParts && resp.data.masterParts.length > 0) {
                        partCopy.masterPartId = resp.data.masterParts[0].masterPartId;
                        partCopy.price = resp.data.masterParts[0].price;
                        partCopy.stock = resp.data.masterParts[0].stock;
                    }
                    basketService.addPart(partCopy);
                    part.clicked = true; // This operates on the original 'part' to give UI feedback
                    $rootScope.$broadcast("Basket-Updated", {action: 'addPartToBasket'});

                    $timeout(function() {
                        part.clicked = false;
                    }, 500);
                });
            }
        }


        function isQuantityZero() {
            var isQuantityZero = true;
            for (var i = 0; i < vm.partsList.length; i++) {
                var part = vm.partsList[i];
                if (part.quantity >= 1) {
                    isQuantityZero = false;
                }
            }
            return isQuantityZero;
        }

    }

})();