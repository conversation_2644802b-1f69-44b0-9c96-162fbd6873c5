// =============================================================================
// Forms
// =============================================================================
select,
input{
  margin: 0 8px 16px 0;
  width: 100%;
  background-color: white;
  border: 1px solid $divider-color;
  height: 34px;
  padding-left: 8px;
  @include border-radius(2px);

}
input,
textarea {

  @include border-radius(2px);
    padding: $spacing/2 $spacing;

  &:focus {
    outline: 0;
  }
}

input[type="date"] {
  -webkit-appearance: none;
}

.error-alert {
  color: $red;
}

.success-alert {
  color: $green;
}

.warning-alert {
  color: $orange;
}

.form {
  @extend %clearfix;

  .not-required {
    opacity       : 0.3;
    pointer-events: none;
  }


  &.panel-dark {
    h3 {
      &.use-shipping {
        display: inline-block;

        span {
          font-size  : 0.9em;
          margin-left: $spacing;

          input {
            width: auto;
          }
        }
      }
    }

    .select-box,
    input,
    textarea
    {
      &:not([type="checkbox"]){
        width        : 100%;
        margin-bottom: $spacing;
      }
    }

    .input-wrap {
      @extend %clearfix;

      .select-box,
      input {
        width: calc(50% - 8px);
        float: left;

        &:not(:nth-child(2n)) {
          margin-right: $spacing;
        }
      }
    }
  }


  .select-box,
  input,
  textarea{
    display    : inline-block;
    font-family: $body-font;
    padding    : $spacing;
    border       : 1px solid $divider-color;
    @include border-radius($border-radius);
    background   : $lightback;
    color      : $textdark;
  }

  .select-box {
    background   : $lightback;
    line-height: normal;
    display    : block;

    select {
      padding: 0 0 0 0.3em;
      margin:0;
    }

    .select-arrow {
      //top: 21px;
    }
  }
}

h3.group-title {
  border-bottom: 1px solid #D2DAE5;
  padding-bottom: 4px;
}

.input-icon-wrap {
  @extend %clearfix;
  border: 1px solid $divider-color;
  @include border-radius($border-radius);
  background: $lightback;
  overflow: hidden;
  position: relative;

  > * {
    float: left;
  }

  i {
    width: 38px;
    height: 38px;
    line-height: 38px;
    text-align: center;
    color: $textdark2;
    position: absolute;
    top: 0px;
    right: 0px;
  }

  .select-box {

    select {
    }

    .select-arrow {
      top: 15px;
    }
  }

  input[type="search"] {
    background: transparent;
    height: $spacing*5 - 2px;
    border: 0;
    font-family: $body-font;
    padding: $spacing $spacing*2;
    width: 90%;
    margin: 0;
  }
}

.select-box,
.advancedFilters{
  width          : 180px;
  padding        : 0;
  position       : relative;
  display        : block;
  overflow       : hidden;
  text-overflow  : ellipsis;
  white-space    : nowrap;
  height: 100%;
  text-align: left;
  height: 38px;



  select,
  button{
    display           : inline-block;
    width             : 100%;
    padding           : 0 $spacing;
    cursor            : pointer;
    color             : $textdark;
    font-family       : $body-font;
    font-size         : 1em;
    border            : 0;
    @include border-radius(0);
    outline           : 0;
    background        : $lightback;
    overflow          : hidden;
    white-space       : nowrap;
    text-overflow     : ellipsis;
    appearance        : none;
    -webkit-appearance: none;
    -moz-appearance   : none;
    text-align: left;
    //height: 38px;

    &:focus,
    &:hover {
      ~ .select-arrow {
        border-top-color: $textdark2;
      }
    }

    &:disabled {
      pointer-events: none;
      opacity       : 0.5;
    }
  }

  select::-ms-expand {
    display: none;
  }

  .select-arrow {
    position      : absolute;
    top           : 15px;
    right         : 15px;
    width         : 0;
    height        : 0;
    pointer-events: none;
    border-width  : 5px 5px 0 5px;
    border-style  : solid;
    border-color  : $textdark2 transparent transparent transparent;
  }
}

.input-group {
  font-size: 0.9em;
  line-height:1.4em;
  margin-bottom: 16px;

  .addition-form-info{
    width: calc(100% - 32px);
    line-height: 32px;
  }

  label {
    font-weight: bold;
    margin-bottom: $spacing;
    display: block;
    width:100%;
  }

  input[type="text"],
  input[type="email"],
  input[type="password"],
  textarea {font-weight:400;}
}

.search-panel {
  display: inline-block;
  width: auto;
  &.nosidepad {
    padding-left: 0;
    padding-right: 0;
  }

}
.part-search-panel {
  /*height: 40px;*/
  display: inline-block;
  width: 50%;
  margin-left: 16px;
  &.nosidepad {
    padding-left: 0;
    padding-right: 0;
  }

}

.part-search-btn {
  margin-bottom: 32px;
  width: 160px;
}

.search-panel > div > div{
  display:inline-block;
}

.filter-panel {
  float: left;
  width: 100%;
  margin-top: $spacing;
  padding:$spacing $spacing*2;
  border-top: 1px solid $divider-color;

  .filter-header {
    font-size: 0.8em;
    text-transform: uppercase;
    font-weight: 700;
  }
  .first-filter {
  }

  .filter-option {
    display: inline-block;
    margin-right: $spacing*2;
    margin-top: $spacing;
  }
  .filter-buttons {
    margin-top: $spacing*2;

    .btn:first-child{
      margin-right: $spacing;
    }
  }
}


.tab-search {
  width: 50%;
  float: right;
  padding-bottom: 0px;
  //margin-right: -62px;
  padding-top: 0px;
  margin-bottom: 8px;
}

.searchgroup {
  width: 340px;
  display:inline-block;
}

.orderssearchgroup {
  width: 450px;
  margin-left: $spacing*2;
  display:inline-block;
}

.single-search-field{
  width:100%;
  margin-right:0;
}

.sortgroup-orders,
.sortgroup{
  height: 40px;
  display: inline-block;
  margin-left: $spacing*2;
}
.sortgroup-users {
  height: 40px;
  display: inline-block;
  //margin-left: $spacing*2;
}

.products-filter {
  height: 40px;
  display: inline-block;
  margin-left: $spacing*2;

  div:last-child{
    float:right!important;
  }

  .create-machine {

  }
}

.publication-filter {
  width: 420px;

  .create-manual {
    position: relative;
    top: 6px;
  }
}

.badge-pill {
  border: 1px solid lighten($textdark2,10%);
  color: $textdark2;
  display: inline-block;
  font-size: 0.8em;
  padding: 4px 8px;
  min-width: 10px;
  background:$white;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  @include border-radius(999999px);
  text-transform: uppercase;
  font-weight:700;
  &.primary {
    border-color:$blue;
    color: $blue;
  }
  &.success {
    border-color:$green;
    color: $green;
  }
  &.badge-lg {
    font-size: 0.7em;
    padding: 6px 12px;
  }
}
.badge-small {
  color: $textdark;
  display: inline-block;
  font-size: 11px;
  padding: 3px 4px;
  min-width: 0px;
  background:lighten($blue,32%);
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  @include border-radius(5px);
  text-transform: uppercase;
  font-weight:700;
}
.small-label {
  font-size: 0.9em;
  font-weight:600;
}

.group-space {
  display: block;
  height:$spacing;
}


.switch-lbl { position: relative; display: block; height: 18px; width: 36px; margin: 2px 0 0 0; background: #898989; @include border-radius(140px); cursor: pointer; transition: all 0.3s ease;
  &:after { position: absolute; left: -6px; top: -3px; display: block; width: 24px; height: 24px; @include border-radius(100px); background: $white; box-shadow: 0px 1px 5px rgba(0,0,0,0.45); content: ''; transition: all 0.3s ease; }
  &:active:after { transform: scale(1.1, 0.85); }
  span {position: absolute; left: 50px; top:-5px; font-size:1.2em; font-weight:600;}
}

.switch-cbx {
  &:checked ~ label { background: lighten($green,30%);
    &:after { left: 20px; background: $green; }
  }
  &:disabled ~ label { background: #d5d5d5; pointer-events: none;
    &:after { background: #bcbdbc; }
  }
  &.hidden { display: none;}
}

.not-bold{
  font-weight: normal;
}

.notes{
  width: 100%;
}

.full-width-element{
  width: 100%;
}

.well {
  width: 100%;
  background: lighten($blue, 32%);
  @include border-radius(10px);
  border: 2px solid $blue;
  padding: $spacing $spacing*2;
  margin-bottom:$spacing*2;
}

.error-well {
  width: 100%;
  background: lighten($red, 40%);
  @include border-radius(10px);
  border: 2px solid $red;
  padding: 10px;
  color: $red;
  text-align: center;
}
.success-well {
  width: 100%;
  background: lighten($green, 60%);
  @include border-radius(10px);
  border: 2px solid $green;
  padding: 10px;
  color: darken($green, 20%);
  text-align: center;
}

.alert-banner{
  background-color: #FFEBD3 !important;
  color: #C5771C;
  border-bottom: solid 2px #C57712;

  a {
    color: #C5771C !important;

    &:hover{
      color: darken(#C5771C, 10%)!important;
    }
  }


  .badge-small{
    background-color: lighten(#C5771C, 20%);
    color: #FFFFFF;

  }
}

//checkbox styles

input[type="checkbox"]{
  -webkit-appearance: none;
  padding: 3px;
  @include border-radius(3px);
  flex: none !important;
  display: inline-block;
  position: relative;
  font-family: "FontAwesome";
  border: 1px solid #D2DAE5;
  background: #F2F6F9;
  color: #373D45;
  height: 24px;
  width: 24px!important;
}

input[type="checkbox"]:checked {
  border: 1px solid darken($blue, 10);
  background: $blue;
  color: #fff;
}

input[type="checkbox"]:checked:after {
  content: '\f00c';
  font-size: 16px;
  position: absolute;
}

//Fix for product page
.single-search-field{
  width:100%;
  margin-right:0;
}

.required-field{
  font-style: italic;
  text-transform:lowercase;
  font-weight: normal;
  color:$red;
}