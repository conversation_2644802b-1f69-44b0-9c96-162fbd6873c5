.body-content{

  min-width:100%;

}

.bg-white span{

    background-color: white;

}

.flex-row {
  flex-direction: row;
  display: flex;
}

.flex-column {
  flex-direction: column;
  display: flex;
}

.flex-body {
  display: flex;
}

.flex-body div:not([class*="flex"]) {
  border: 1px solid white;
  flex: 1 1 200px;
  width: 300px;
}

.dashboard {
  width: 100%;
  clear: both;
  //display: inline-block;

  .panel {
    //margin-left: $spacing;
    display: flex;
    margin-bottom: $spacing;
    padding: 18px;
    flex-wrap: wrap;

    /*  &:first-child {
        margin-left: 0;
      }*/

    p {
      padding: 0 24px;
    }

    .heading {
      border-bottom: 1px solid $divider-color;
      margin-bottom: 16px;
      padding: 0 24px;

      i {
        color: $blue;
      }
    }

    .no-margin {
      margin-bottom: 0;
    }

    .body {
      .decreased {
        color: $red;
      }

      .increased {
        color: $green;
      }

      .maintained {
        color: $textdark;
      }

      table {
        width: 100%;
      }

      tr {
        border-bottom: 1px solid $divider-color;

        &:first-child {
          margin-top: -4px;
        }
      }

      td {
        padding: 6px 0;
        font-size: 0.9em;

        &:first-child {
          padding-left: 16px;
          width: 40px;
        }
      }

    }

    .total-amount {
      font-size: 26px !important;
      font-weight: bold;
      margin-bottom: 4px;
    }
  }

  .small {
    width: calc(25% - 12px);
  }

  .third {
    width: calc(33% - 8px);
    margin-left: 8px;
    background-color: $lightback;
    border: 1px solid $grey;
    text-align: center;
  }

  .quarter {
    background-color: $lightback;
    border: 1px solid $grey;
    text-align: center;
  }

  .quarter_column{

    width: calc(100% - 100px);

  }

  .half {
    background-color: $lightback;
    border: 1px solid $grey;
    text-align: center;
  }

  .threequarter {

    background-color: $lightback;
    border: 1px solid $grey;
    text-align: center;
  }

  .full {
    background-color: $lightback;
    border: 1px solid $grey;
  }

  .two-third {

    background-color: $lightback;
    border: 1px solid $grey;
    float: right;
  }

  .pieChart {
    position: relative;
    height: 100%;
  }

  #mostOrderedPartsChart{

    width:100%;

  }

  .medium {
    width: calc(50% - 8px);
  }

  .large {
    width: calc(75% - 6px);
  }

  .x-large {
    width: 100%;
  }
}

.dashboard-full-width-holder {
  vertical-align: top;
  height: auto;
  width: 100%;
  margin-left: 8px;
}

.dashboard-large-holder {
  vertical-align: top;
  //height: 620px;
  width: 100%;
  margin-left: 8px;
}

.dashboard-small-holder {
  vertical-align: top;
  //height: 620px;
  width: 25%;
}

.table-holder{
  width: calc(100% - 18px);
  //padding: 8px;
  background-color: $lightback;
  border: 1px solid $grey;
  //margin-top: 8px;
}

.dropdown-holder {
  vertical-align: top;
  width: calc(50% - 24px);
  margin: 0px 8px 8px 8px;
  display: inline-block;
}

.time-dropdown-holder {
  width: calc(100% - 16px);
  margin: 0px 8px 8px 8px;
}

#myChart {
  padding: 0 24px;
  display: block;
  margin: 0 auto;
  height: auto;
  width: 100%;

}

#container1 {
  width: auto;
  height: 236px;
  position: relative;
}

#orders-by-product {
  padding: 16px 40px 24px 24px;
}

.status {
  font-size: 0.9em;
  margin-bottom: 12px;
}

.order-value-chart {
  width: 75%;
  //height: 40vh;
}

.bar-chart {
  width: calc(100% - 16px);
}

.mostOrderedPartNoData {
  height: 300px;
  text-align: center;
}

.dashboard-select{
  height: 40px;
}