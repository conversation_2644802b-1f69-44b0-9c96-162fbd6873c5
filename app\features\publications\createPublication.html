<section class="container-fluid py-4">
    <h2 class="mb-4" translate ng-if="!createPublicationCtrl.isEditing">CREATE_PUBLICATION.CREATE_NEW</h2>
    <h2 class="mb-4" translate ng-if="createPublicationCtrl.isEditing">CREATE_PUBLICATION.EDIT_PUBLICATION</h2>

    <form name="createPublicationCtrl.createPublicationForm">
        <!-- Publication Details -->
        <div class="card mb-4">
            <div class="card-header bg-light">
                <h3 class="mb-0 section-title" translate>CREATE_PUBLICATION.MANUAL_DETAILS</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="publicationName">{{'CREATE_PUBLICATION.MANUAL_NAME' | translate}} <span class="text-danger">*</span></label>
                            <input
                                type="text"
                                class="form-control"
                                placeholder="{{'CREATE_PUBLICATION.ENTER_PUBLICATION_NAME' | translate}}"
                                required
                                ng-model="createPublicationCtrl.data.name"
                                id="publicationName"
                            />
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="serialNumber" translate>CREATE_PUBLICATION.SERIAL_NUMBER</label>
                            <input 
                                type="text" 
                                class="form-control" 
                                placeholder="{{'CREATE_PUBLICATION.ENTER_SERIAL' | translate}}" 
                                ng-model="createPublicationCtrl.serialNumber" 
                                id="serialNumber"
                            />
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-4">
                        <label for="categorySelect" class="d-block">{{'CREATE_PUBLICATION.SELECT_CATEGORY' | translate}} <span class="text-danger">*</span></label>
                    </div>
                </div>
                <div class="row align-items-center">
                    <div class="col-md-4">
                        <select 
                            id="categorySelect" 
                            class="mb-0 form-control"
                            required
                            ng-model="createPublicationCtrl.data.publicationCategoryId"
                            ng-options="category.id as category.name for category in createPublicationCtrl.publicationCategories">
                            <option value="" translate>CREATE_PUBLICATION.SELECT_CATEGORY_PLACEHOLDER</option>
                        </select>
                    </div>
                    <div class="col-md-4 mt-2 mt-md-0">
                        <button class="btn btn-primary" ng-click="createPublicationCtrl.manageCategoriesModal()" translate>CREATE_PUBLICATION.MANAGE_CATEGORIES</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Viewables -->
        <div class="card mb-4">
            <div class="card-header bg-light">
                <h3 class="mb-0 section-title" translate>CREATE_PUBLICATION.VIEWABLES</h3>
            </div>
            <div class="card-body">
                <div class="row px-4">
                    <div class="col-md py-3 border rounded">

                        <div class="d-flex mb-4 justify-content-between align-items-center pb-2 border-bottom">
                          <h2 class="mb-0">{{ 'CREATE_PUBLICATION.AVAILABLE_VIEWABLES' | translate }}</h2>
                          <div class="form-check flex">
                            <input class="form-check-input my-0 mr-3" type="checkbox" id="selectAllAvailableViewables" ng-model="createPublicationCtrl.selectAllAvailableModel" ng-change="createPublicationCtrl.toggleSelectAllAvailableViewables(createPublicationCtrl.selectAllAvailableModel)" ng-checked="createPublicationCtrl.isAllAvailableViewablesSelected()" ng-disabled="!createPublicationCtrl.isSelectAllAvailableEnabled()">
                            <label class="form-check-label" for="selectAllAvailableViewables">{{ 'CREATE_PUBLICATION.SELECT_ALL' | translate }}</label>
                          </div>
                        </div>

                        <ul class="nav nav-tabs mb-3">
                            <li class="nav-item">
                                <a class="nav-link" ng-class="{ 'active': createPublicationCtrl.viewableManager.activeViewableTab === 'find' }" href="#find-tab" data-toggle="tab" ng-click="createPublicationCtrl.changeViewableTab('find'); $event.preventDefault()" translate>CREATE_PUBLICATION.FIND_BY_RANGE</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" ng-class="{ 'active': createPublicationCtrl.viewableManager.activeViewableTab === 'search' }" href="#search-tab" data-toggle="tab" ng-click="createPublicationCtrl.changeViewableTab('search'); $event.preventDefault()" translate>CREATE_PUBLICATION.SEARCH</a>
                            </li>
                        </ul>
                        <div class="tab-content">
                            <div class="tab-pane" ng-class="{ 'active': createPublicationCtrl.viewableManager.activeViewableTab === 'find' }" id="find-tab">
                                <div ng-if="createPublicationCtrl.viewableManager.activeViewableTab === 'find'">
                                        <div class="row mb-3 align-items-end">
                                            <div class="col">
                                                <label for="rangeSelect" translate>CREATE_PUBLICATION.SELECT_RANGE</label>
                                                <div>
                                                    <select
                                                        id="rangeSelect"
                                                        class="form-control m-0"
                                                        ng-options="rangeValue.id as rangeValue.name for rangeValue in createPublicationCtrl.viewableManager.rangeValues | orderBy:'name'"
                                                        ng-model="createPublicationCtrl.viewableManager.data.rangeId"
                                                        ng-change="createPublicationCtrl.viewableManager.rangeChanged(createPublicationCtrl.viewableManager.data.rangeId)"
                                                    >
                                                        <option value="" disabled>{{ 'CREATE_PUBLICATION.SELECT_RANGE' | translate }}</option>
                                                    </select>
                                                    <div class="select-arrow"></div>
                                                </div>
                                            </div>
                                            <div class="col-auto mb-2">
                                                <i class="fa fa-chevron-right"></i>
                                            </div>
                                            <div class="col">
                                                <label for="productSelect" translate>CREATE_PUBLICATION.SELECT_PRODUCT</label>
                                                <div>
                                                    <select
                                                        id="productSelect"
                                                        class="form-control m-0"
                                                        ng-options="product.id as product.name for product in createPublicationCtrl.viewableManager.products | orderBy: 'name'"
                                                        ng-model="createPublicationCtrl.viewableManager.data.productId"
                                                        ng-change="createPublicationCtrl.viewableManager.productChanged()"
                                                        ng-disabled="createPublicationCtrl.viewableManager.isProductDropdownDisabled"
                                                    >
                                                        <option value="" disabled selected>
                                                           {{ ((createPublicationCtrl.viewableManager.data.rangeId && createPublicationCtrl.viewableManager.products.length === 0) ? 'CREATE_PUBLICATION.NO_PRODUCTS_AVAILABLE' : 'CREATE_PUBLICATION.SELECT_PRODUCT') | translate }}
                                                       </option>
                                                    </select>
                                                    <div class="select-arrow"></div>
                                                </div>
                                            </div>
                                        </div>
                                </div>
                                <!-- The range/product content will go here -->
                            </div>
                            <div class="tab-pane" ng-class="{ 'active': createPublicationCtrl.viewableManager.activeViewableTab === 'search' }" id="search-tab">
                                        <div class="input-group mb-3">
                                            <div class="search-input-wrapper"> 
                                                <input type="text" 
                                                       class="form-control" 
                                                       id="publicationSearchInput" 
                                                       placeholder="{{'CREATE_PUBLICATION.SEARCH' | translate}}" 
                                                       ng-model="createPublicationCtrl.viewableManager.searchTerm"
                                                       ng-keydown="$event.keyCode === 13 && createPublicationCtrl.handleSearchKeydown($event)">
                                                    <span class="clear-search-icon" 
                                                      ng-if="createPublicationCtrl.viewableManager.searchTerm && createPublicationCtrl.viewableManager.searchTerm.length > 0" 
                                                      ng-click="createPublicationCtrl.viewableManager.clearSearch()" 
                                                      title="Clear search">
                                                    &times; 
                                                </span>
                                            </div>
                                            <button class="btn btn-primary" type="button" ng-click="createPublicationCtrl.performSearch()" translate>CREATE_PUBLICATION.SEARCH</button>
                                        </div>
                                </div>
                        </div>

                        <div class="viewable-list-container">
                            <div class="viewable-list d-flex flex-column cadGap">
                                <!-- Find Tab: click to select -->
                                <div ng-if="createPublicationCtrl.viewableManager.activeViewableTab === 'find' && (!createPublicationCtrl.viewableManager.data.rangeId || !createPublicationCtrl.viewableManager.data.productId)" class="text-muted text-center py-3">
                                    {{ 'CREATE_PUBLICATION.PROMPT_SELECT_RANGE_PRODUCT' | translate }}
                                </div>
                                <!-- Find Tab: No results -->
                                <div ng-if="createPublicationCtrl.viewableManager.activeViewableTab === 'find' && createPublicationCtrl.viewableManager.data.rangeId && createPublicationCtrl.viewableManager.data.productId && !createPublicationCtrl.viewableManager.getDisplayedViewables().length" class="text-muted text-center py-3">
                                    {{ 'CREATE_PUBLICATION.NO_VIEWABLES_FOUND_FOR_RANGE_PRODUCT' | translate }}
                                </div>
                                <!-- Search Tab: start typing to search -->
                                <div ng-if="createPublicationCtrl.viewableManager.activeViewableTab === 'search' && !createPublicationCtrl.viewableManager.searchPerformed" class="text-muted text-center py-3">
                                     {{ 'CREATE_PUBLICATION.SEARCH_VIEWABLES' | translate }}
                                </div>
                                <!-- Search Tab: No results -->
                                 <div ng-if="createPublicationCtrl.viewableManager.activeViewableTab === 'search' && createPublicationCtrl.viewableManager.searchPerformed && !createPublicationCtrl.viewableManager.getDisplayedViewables().length" class="text-muted text-center py-3">
                                      {{ 'CREATE_PUBLICATION.NO_VIEWABLES_FOUND_MATCHING_SEARCH' | translate }}
                                 </div>
    
                                <!-- Find tab viewables list -->
                                <div class="d-flex flex-column cadGap" ng-if="createPublicationCtrl.viewableManager.activeViewableTab === 'find' && createPublicationCtrl.viewableManager.shouldShowAvailableViewablesList()">
                                    <div ng-repeat="viewable in createPublicationCtrl.viewableManager.getDisplayedViewables() track by viewable.id"
                                         ng-class="{'viewable-item-assigned': createPublicationCtrl.viewableManager.tempSelectedViewableIds.indexOf(viewable.id) !== -1, 'viewable-item': createPublicationCtrl.viewableManager.tempSelectedViewableIds.indexOf(viewable.id) === -1, 'viewable-item-disabled': createPublicationCtrl.viewableManager.isViewableSelected(viewable.id)}"
                                         class="d-flex justify-content-between align-items-center">
                                        <div class="d-flex align-items-center">
                                            <input type="checkbox"
                                                   ng-checked="createPublicationCtrl.viewableManager.tempSelectedViewableIds.indexOf(viewable.id) !== -1"
                                                   ng-click="createPublicationCtrl.viewableManager.toggleTempSelectedViewable(viewable.id)"
                                                   ng-disabled="createPublicationCtrl.viewableManager.isViewableSelected(viewable.id)"
                                                   class="my-0 ml-3 mr-4" />
                                            <div ng-class="{'text-muted': createPublicationCtrl.viewableManager.isViewableSelected(viewable.id)}">
                                                <div class="font-weight-bold">{{ viewable.name }}</div>
                                                <div class="text-muted small">
                                                    <span class="px-2" style="background-color: #E9ECEF;">{{ viewable.range.name }}</span>
                                                    <span class="mx-1"><i class="fa fa-chevron-right"></i></span>
                                                    <span class="px-2" style="background-color: #E9ECEF;">{{ viewable.product.name }}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div ng-if="createPublicationCtrl.viewableManager.isViewableSelected(viewable.id)" class="text-muted small">
                                            <i class="fa fa-check-circle text-success"></i> Selected
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Search tab viewables list -->
                                <div class="d-flex flex-column cadGap" ng-if="createPublicationCtrl.viewableManager.activeViewableTab === 'search' && createPublicationCtrl.viewableManager.searchPerformed && createPublicationCtrl.viewableManager.getDisplayedViewables().length > 0">
                                    <div ng-repeat="viewable in createPublicationCtrl.viewableManager.getDisplayedViewables() track by viewable.id"
                                         ng-class="{'viewable-item-assigned': createPublicationCtrl.viewableManager.tempSelectedViewableIds.indexOf(viewable.id) !== -1, 'viewable-item': createPublicationCtrl.viewableManager.tempSelectedViewableIds.indexOf(viewable.id) === -1, 'viewable-item-disabled': createPublicationCtrl.viewableManager.isViewableSelected(viewable.id)}"
                                         class="d-flex justify-content-between align-items-center">
                                        <div class="d-flex align-items-center">
                                            <input type="checkbox"
                                                   ng-checked="createPublicationCtrl.viewableManager.tempSelectedViewableIds.indexOf(viewable.id) !== -1"
                                                   ng-click="createPublicationCtrl.viewableManager.toggleTempSelectedViewable(viewable.id)"
                                                   ng-disabled="createPublicationCtrl.viewableManager.isViewableSelected(viewable.id)"
                                                   class="my-0 ml-3 mr-4" />
                                            <div ng-class="{'text-muted': createPublicationCtrl.viewableManager.isViewableSelected(viewable.id)}">
                                                <div class="font-weight-bold">{{ viewable.name }}</div>
                                                <div class="text-muted small">
                                                    <span class="px-2" style="background-color: #E9ECEF;">{{ viewable.range.name }}</span>
                                                    <span class="mx-1"><i class="fa fa-chevron-right"></i></span>
                                                    <span class="px-2" style="background-color: #E9ECEF;">{{ viewable.product.name }}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div ng-if="createPublicationCtrl.viewableManager.isViewableSelected(viewable.id)" class="text-muted small">
                                            <i class="fa fa-check-circle text-success"></i> Selected
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="sticky-buttons d-flex justify-content-end mt-3"
                             ng-if="(createPublicationCtrl.viewableManager.activeViewableTab === 'find' && createPublicationCtrl.viewableManager.shouldShowAvailableViewablesList()) || (createPublicationCtrl.viewableManager.activeViewableTab === 'search' && createPublicationCtrl.viewableManager.searchPerformed && createPublicationCtrl.viewableManager.getDisplayedViewables().length > 0)">
                            <button class="btn small secondary ng-scope mr-2"
                                    ng-click="createPublicationCtrl.clearTempSelectedViewables()"
                                    ng-disabled="!createPublicationCtrl.viewableManager.tempSelectedViewableIds.length"
                                    translate="GENERAL.CLEAR"></button>
                            <button class="btn btn-primary align-self-end d-flex align-items-center"
                                    ng-disabled="!createPublicationCtrl.viewableManager.tempSelectedViewableIds.length"
                                    ng-click="createPublicationCtrl.viewableManager.addSelectedViewables()">
                                <span translate>CREATE_PUBLICATION.ASSIGN_SELECTED</span>
                                <span class="badge badge-light ml-2" ng-if="createPublicationCtrl.viewableManager.tempSelectedViewableIds.length">
                                    {{ createPublicationCtrl.viewableManager.tempSelectedViewableIds.length }}
                                </span>
                            </button>
                        </div>
                    </div>

                    <div class="col-auto d-flex flex-column justify-content-center align-items-center" style="min-width:48px;">
                        <i class="fa fa-chevron-right cadBlue"></i>
                    </div>
                    <div class="col-md py-3 border rounded selected-viewables-container"> 

                        <div class="d-flex mb-4 align-items-center pb-2 border-bottom">
                            <h2 class="mb-0">{{ 'CREATE_PUBLICATION.ASSIGNED_VIEWABLES' | translate }}</h2>
                        </div>

                        <div class="selected-viewables-scroll-container">
                            <div class="viewable-list">
                                <!-- empty list -->
                                <div ng-if="!createPublicationCtrl.viewableManager.selectedViewables.length" class="text-muted text-center py-3">
                                    {{ 'CREATE_PUBLICATION.NO_VIEWABLES_SELECTED' | translate }}
                                </div>

                               

                                <!-- Selected Viewables List (only show if not empty) -->
                                <div class="d-flex flex-column cadGap" ng-if="createPublicationCtrl.viewableManager.selectedViewables.length">
                                    <div ng-repeat="selected in createPublicationCtrl.viewableManager.selectedViewables"
                                        class="viewable-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <div class="font-weight-bold">{{ selected.name }}</div>
                                            <div class="text-muted small">
                                                <span class="px-2" style="background-color: #E9ECEF;">{{ selected.range.name }}</span>
                                                <span class="mx-1"><i class="fa fa-chevron-right"></i></span>
                                                <span class="px-2" style="background-color: #E9ECEF;">{{ selected.product.name }}</span>
                                            </div>
                                        </div>
                                        <div class="d-flex flex-wrap cadGap">
                                            <button class="btn btn-sm btn-outline-primary mr-1" 
                                                    ng-if="selected.id !== createPublicationCtrl.viewableManager.data.featuredModelId"
                                                    ng-click="createPublicationCtrl.viewableManager.setAsFeatured(selected)" translate>
                                                CREATE_PUBLICATION.SET_AS_FEATURED
                                            </button>
                                            <button class="btn btn-sm btn-primary mr-1" 
                                                    ng-if="selected.id === createPublicationCtrl.viewableManager.data.featuredModelId"
                                                    ng-click="createPublicationCtrl.viewableManager.removeFeatured(selected)" translate>
                                                CREATE_PUBLICATION.FEATURED
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger mt-2 mt-md-0"
                                                ng-click="createPublicationCtrl.viewableManager.removeViewable(selected)">
                                                <i class="fa fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

                <!-- Images -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h3 class="mb-0" translate>CREATE_PUBLICATION.ADD_COVER</h3>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <div class="img-thumbnail-container" style="width: 375px; height: 220px; overflow: hidden; display: flex; align-items: center; justify-content: center; border: 1px solid #dee2e6; border-radius: 4px;">
                                        <img ng-if="createPublicationCtrl.data.coverImage && createPublicationCtrl.data.coverImage.url" 
                                             ng-src="{{createPublicationCtrl.data.coverImage.url}}?t={{createPublicationCtrl.coverImageTimestamp}}" 
                                             class="img-fluid" 
                                             style="width: 100%; height: 100%; object-fit: cover;"
                                             alt="Cover publication image" />
                                        <img ng-if="!createPublicationCtrl.data.coverImage || !createPublicationCtrl.data.coverImage.url" 
                                             ng-src="images/placeholder.jpg" 
                                             class="img-fluid" 
                                             style="width: 100%; height: 100%; object-fit: cover;"
                                             alt="Cover publication placeholder" />
                                    </div>
                                </div>
                                <div class="d-flex align-items-center">
                                    <div class="pr-3 mr-3"> 
                                        <button type="button" class="btn btn-sm btn-outline-primary" ng-click="createPublicationCtrl.addCoverImage()" translate>
                                            GENERAL.EDIT
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h3 class="mb-0" translate>CREATE_PUBLICATION.ADD_FEATURED</h3>
                            </div>
                            <div class="card-body">
                                <div class="mb-3"> 
                                    <div class="img-thumbnail-container" style="width: 375px; height: 220px; overflow: hidden; display: flex; align-items: center; justify-content: center; border: 1px solid #dee2e6; border-radius: 4px;">
                                        <div ng-if="createPublicationCtrl.viewableManager.featuredViewableImage">
                                            <img ng-src="{{createPublicationCtrl.viewableManager.featuredViewableImage.url}}?t={{createPublicationCtrl.featuredImageTimestamp}}" 
                                                 class="img-fluid" 
                                                 style="width: 100%; height: 100%; object-fit: cover;"
                                                 alt="Featured publication image" /> 
                                        </div>
                                        <div ng-if="!createPublicationCtrl.viewableManager.featuredViewableImage" 
                                             class="d-flex align-items-center justify-content-center text-muted"
                                             style="width: 100%; height: 100%; background-color: #f8f9fa;">
                                            <div class="text-center">
                                                <i class="fa fa-image fa-3x mb-2"></i>
                                                <div>{{'CREATE_PUBLICATION.NO_IMAGE_ADDED' | translate}}</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center">
                                    <button type="button" class="btn btn-sm btn-outline-primary" ng-click="createPublicationCtrl.addFeaturedImage()"> 
                                        <span ng-if="!createPublicationCtrl.viewableManager.featuredViewableImage" translate>GENERAL.ADD</span>
                                        <span ng-if="createPublicationCtrl.viewableManager.featuredViewableImage" translate>GENERAL.EDIT</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Supplementary Info and Customers -->
        <div class="row">
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h3 class="m-0" translate>CREATE_PUBLICATION.TECH_DOCS</h3>
                    </div>
                    <div class="card-body">
                        <div class="form-group mb-0">
                            <div ng-if="!createPublicationCtrl.techDocSelect || createPublicationCtrl.techDocSelect.length === 0" class="text-muted text-center py-2">
                                {{ 'CREATE_PUBLICATION.NO_TECH_DOCS_UPLOADED' | translate }}
                            </div>
                            <div
                                ng-if="createPublicationCtrl.techDocSelect && createPublicationCtrl.techDocSelect.length > 0"
                                class="select-check-box multicheckbox"
                                ng-dropdown-multiselect
                                options="createPublicationCtrl.techDocSelect"
                                selected-model="createPublicationCtrl.techDocs"
                                extra-settings="createPublicationCtrl.multiCheckdownSettings"
                                translation-texts="createPublicationCtrl.techDocTextSettings"
                                ng-click="createPublicationCtrl.techDocsSelected()"
                            ></div>
                        </div>
                    </div>
                </div>
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h3 class="m-0" translate>CREATE_PUBLICATION.VIDEOS</h3>
                    </div>
                    <div class="card-body">
                        <div class="form-group mb-0">
                            <div ng-if="!createPublicationCtrl.videoSelect || createPublicationCtrl.videoSelect.length === 0" class="text-muted text-center py-2">
                                {{ 'CREATE_PUBLICATION.NO_VIDEOS_UPLOADED' | translate }}
                            </div>
                            <div
                                ng-if="createPublicationCtrl.videoSelect && createPublicationCtrl.videoSelect.length > 0"
                                class="select-check-box multicheckbox"
                                ng-dropdown-multiselect
                                options="createPublicationCtrl.videoSelect"
                                selected-model="createPublicationCtrl.videos"
                                extra-settings="createPublicationCtrl.multiCheckdownSettings"
                                translation-texts="createPublicationCtrl.videoTextSettings"
                                ng-click="createPublicationCtrl.videosSelected()"
                            ></div>
                        </div>
                    </div>
                </div>
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h3 class="m-0" translate>CREATE_PUBLICATION.KITS</h3>
                    </div>
                    <div class="card-body">
                        <div class="form-group mb-0">
                            <div ng-if="!createPublicationCtrl.kitSelect || createPublicationCtrl.kitSelect.length === 0" class="text-muted text-center py-2">
                                {{ 'CREATE_PUBLICATION.NO_KITS_CREATED' | translate }}
                            </div>
                            <div
                                ng-if="createPublicationCtrl.kitSelect && createPublicationCtrl.kitSelect.length > 0"
                                class="select-check-box multicheckbox"
                                ng-dropdown-multiselect
                                options="createPublicationCtrl.kitSelect"
                                selected-model="createPublicationCtrl.kits"
                                extra-settings="createPublicationCtrl.multiCheckdownSettings"
                                translation-texts="createPublicationCtrl.kitTextSettings"
                                ng-click="createPublicationCtrl.kitsSelected()"
                            ></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card mb-4 customer-assignment-section">
                    <div class="card-header bg-light">
                        <h3 class="m-0" translate>CREATE_PUBLICATION.ASSIGN_TO_CUST</h3>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <div class="dropdown w-100">
                                <button class="btn btn-outline-secondary dropdown-toggle text-center" type="button" id="customerSelect" data-toggle="dropdown"
                                        aria-haspopup="true" aria-expanded="false" style="width: 100%;">
                                    <span ng-if="createPublicationCtrl.selectedCustomers.length === 0" translate>CREATE_PUBLICATION.SELECT_CUSTOMERS</span>
                                    <span ng-if="createPublicationCtrl.selectedCustomers.length > 0 && createPublicationCtrl.selectedCustomers.length <= 3">
                                        {{ createPublicationCtrl.getSelectedCustomerLabels() }}
                                    </span>
                                    <span ng-if="createPublicationCtrl.selectedCustomers.length > 3">
                                        {{ createPublicationCtrl.selectedCustomers.length }} <span translate>CREATE_PUBLICATION.CUSTOMERS</span>
                                    </span>
                                </button>
                                <div class="dropdown-menu" aria-labelledby="customerSelect"
                                    style="width: 100%; height: 200px; overflow-y: auto;">
                                    <button class="d-flex align-items-center dropdown-item" type="button" ng-click="createPublicationCtrl.toggleSelectAll($event)">
                                        <input class="mb-0 mr-3" type="checkbox" ng-checked="createPublicationCtrl.isAllSelected()">
                                        {{'CREATE_PUBLICATION.SELECT_ALL' | translate}}
                                    </button>
                                    <div class="dropdown-divider"></div>
                                    <div class="d-flex align-items-center dropdown-item"
                                        ng-repeat="customer in createPublicationCtrl.allCustomers track by customer.id"
                                        ng-click="createPublicationCtrl.handleCustomerClick(customer, $event)">
                                        <input class="mb-0 mr-3" type="checkbox" ng-checked="createPublicationCtrl.isSelected(customer)">
                                        <label class="cursorPointer mb-0">{{ customer.label }}</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Buttons -->

        <div class="d-flex justify-content-end cadSplitDropdown dropup sticky">
            <button class="btn small secondary" ng-click="createPublicationCtrl.cancel()" translate>GENERAL.CANCEL</button> &nbsp;
            <div class="btn-group btn-hover">
                <button type="button" class="btn primary main-action ng-binding" 
                        ng-click="createPublicationCtrl.savePublication()"
                        ng-disabled="createPublicationCtrl.isSubmitDisabled()"
                        translate>GENERAL.SAVE</button>
                <div class="dropdown-split" >
                    <button ng-disabled="createPublicationCtrl.isSubmitDisabled()" type="button" class="btn primary dropdown-toggle dropdown-toggle-split" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <span class="sr-only">Toggle Dropdown</span>
                    </button>
                    <div class="dropdown-menu">
                        <ul class="list-unstyled m-0 p-0">
                            <li ng-repeat="action in createPublicationCtrl.actions" title="{{action.labelKey | translate}}" class="ng-scope">
                                <a href="" class="px-3 py-2 dark-secondary ng-binding" ng-click="action.onClick()">
                                    <i class="fa fa-fw {{action.icon}}"></i> {{action.labelKey | translate}}
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </form>
</section>