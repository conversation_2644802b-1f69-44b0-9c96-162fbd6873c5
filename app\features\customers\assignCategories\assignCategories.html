<section class="m-5">

<div class="mb-5">

    <div>

        <h1 class="">{{'ASSIGN_CATEGORIES.ASSIGNED_FOR' | translate}} {{assignCategoriesCtrl.customerName}}</h1>
        <p class="mb-0 " translate>ASSIGN_CATEGORIES.DESCRIPTION</p>

    </div>

</div>

<div class="d-flex justify-content-between align-items-center flex-wrap listBoxContainer">

    <div class="col-12 col-md-5 p-5 listBoxLeft d-flex flex-column">

        <h3 class="" translate>ASSIGN_CATEGORIES.AVAILABLE</h3>

        <ul class="list-unstyled">

            <div ng-click="assignCategoriesCtrl.areAllAvailableSelected" ng-class="assignCategoriesCtrl.areAllAvailableSelected ? 'assignlistboxHoverCustom' : 'assignlistboxStyleCustom'" class="px-3 px-lg-5">

                <div class="col-12">

                    <div class="d-flex justify-content-between align-items-center text-container">

                        <input title="Select All" type="checkbox" ng-model="assignCategoriesCtrl.areAllAvailableSelected" ng-click="assignCategoriesCtrl.toggleSelectAllAvailable()" class="mb-0 mr-5 checkbox">

                        <div class="input-group col mb-0">
                            <input ng-model="assignCategoriesCtrl.availableSearchValue" type="search"
                                   class="form-control p-3 h-100 mr-0 ng-pristine ng-valid ng-empty ng-touched" name="SearchDualList"
                                   placeholder="{{'ASSIGN_CATEGORIES.SEARCH' | translate}}">
                            <span ng-if="assignCategoriesCtrl.availableSearchValue != null" ng-click="assignCategoriesCtrl.clearAvailableSearch()" class="search-clear"><i class="fas fa-times"></i></span>
                            <div class="input-group-append">
                                <span class="input-group-text"><i class="fa fa-search"></i></span>
                            </div>
                        </div>

                    </div>

                </div>

            </div>

            </label>

        </ul>

        <ul class="list-group list-unstyled">

            <div href="" ng-class="category.selected ? 'assignlistboxHover' : 'assignlistboxStyle'" ng-click="category.selected = !category.selected; assignCategoriesCtrl.updateAllAvailableCheckbox()" class="assignlistboxStyle py-3 px-3 py-lg-4 px-lg-5 mb-3" ng-repeat="category in assignCategoriesCtrl.availableCategories | filter : assignCategoriesCtrl.availableSearchValue">

                <div class="d-flex col-12 justify-content-between align-items-center">

                    <div class="d-flex justify-content-between align-items-center text-container">

                        <input type="checkbox" ng-model="category.selected" class="mb-0 mr-5 checkbox">

                        <p class="mb-0"><strong> {{category.name}} </strong></p>

                    </div>
                </div>

            </div>

            </label>

        </ul>

    </div>

    <div class="my-4 my-md-0 col-md-1 col-12 listbox_items d-flex justify-content-center text-center flex-md-column flex-row align-items-center">

        <button title="Move selected item from available to assigned." class="m-2 item btn assignlistboxLeft"
                ng-click="assignCategoriesCtrl.moveSelectedFromAvailableToAssigned()">
            <i class="fas fa-angle-right"></i>
        </button>

        <button title="Move selected item from assigned to available." class="m-2 item btn assignlistboxRight"
                ng-click="assignCategoriesCtrl.moveSelectedFromAssignedToAvailable()">
            <i class="fas fa-angle-left"></i>
        </button>

    </div>

    <div class="col-12 col-md-5 listBoxRight p-5 ">

        <h3 class="" translate>ASSIGN_CATEGORIES.ASSIGNED</h3>

        <ul class="list-unstyled">

            <div ng-click="assignCategoriesCtrl.areAllAssignedSelected" ng-class="assignCategoriesCtrl.areAllAssignedSelected ? 'assignlistboxHoverCustom' : 'assignlistboxStyleCustom'" class="px-3 px-lg-5">

                <div class="col-12">

                    <div class="d-flex justify-content-between align-items-center text-container">

                        <input title="Select All" type="checkbox" ng-model="assignCategoriesCtrl.areAllAssignedSelected" ng-click="assignCategoriesCtrl.toggleSelectAllAssigned()" class="mb-0 mr-5 checkbox">

                        <div class="input-group col mb-0">
                            <input ng-model="assignCategoriesCtrl.assignedSearchValue" type="search"
                                   class="form-control p-3 h-100 mr-0 ng-pristine ng-valid ng-empty ng-touched" name="SearchDualList"
                                   placeholder="{{'ASSIGN_CATEGORIES.SEARCH' | translate}}">
                            <span ng-if="assignCategoriesCtrl.assignedSearchValue != null" ng-click="assignCategoriesCtrl.clearAssignedSearch()" class="search-clear"><i class="fas fa-times"></i></span>
                            <div class="input-group-append">
                                <span class="input-group-text"><i class="fa fa-search"></i></span>
                            </div>
                        </div>

                    </div>

                </div>

            </div>

            </label>

        </ul>

        <ul class="list-group list-unstyled">
            <li ng-class="category.selected ? 'assignlistboxHover' : 'assignlistboxStyle'" ng-click="category.selected = !category.selected; assignCategoriesCtrl.updateAllAssignedCheckbox()" class="assignlistboxStyle py-3 px-3 py-lg-4 px-lg-5 mb-3"
                ng-repeat="category in assignCategoriesCtrl.assignedCategories | filter : assignCategoriesCtrl.assignedSearchValue">


                <div class="d-flex col-12 justify-content-between align-items-center">

                    <div class="d-flex justify-content-between align-items-center text-container">

                        <input type="checkbox" ng-model="category.selected" class="mb-0 mr-5  checkbox">
                        <p class="mb-0"><strong> {{category.name}} </strong></p>

                    </div>

                </div>

            </li>

        </ul>

    </div>

</div>

<div class="flex justify-content-end listboxButtons my-5">

    <button ng-click="assignCategoriesCtrl.cancel()" class="btn primary px-5" translate>
        ASSIGN_CATEGORIES.RESET
    </button>

    <button ng-class="assignCategoriesCtrl.isPageEdited ? 'btn primary' : 'btn btn-cancel'" ng-click="assignCategoriesCtrl.save()" class="btn primary ml-3" translate>
        ASSIGN_CATEGORIES.SAVE_CHANGES
    </button>

</div>

</section>