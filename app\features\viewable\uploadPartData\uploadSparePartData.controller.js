(function () {
    "use strict";

    angular.module("app.viewable").controller("UploadSparePartDataController", UploadSparePartDataController);

    UploadSparePartDataController.$inject = [
        "$uibModalInstance",
        "model",
        "uploadPartService",
        "headerBannerService",
        "$scope",
        "$translate",
    ];

    function UploadSparePartDataController($uibModalInstance, model, uploadPartService, headerBannerService, $scope, $translate) {
        var vm = this;
        vm.uploadDetails = uploadDetails;
        vm.fileChanged = fileChanged;
        vm.cancel = $uibModalInstance.dismiss;
        vm.loading2 = false; // Changed to false initially
        vm.isFileSelected = false;
        vm.filename = "";
        vm.model = model;
        vm.isValidFile = false;

        var SELECT_CSV, ADD_SPARE_PART_DATA, FAILED_SPARE_UPLOAD;
        $translate(["UPLOAD_PART.SELECT_CSV", "UPLOAD_PART.ADD_SPARE_PART_DATA", "UPLOAD_PART.FAILED_SPARE_UPLOAD"]).then(function (resp) {
            SELECT_CSV = resp["UPLOAD_PART.SELECT_CSV"];
            ADD_SPARE_PART_DATA = resp["UPLOAD_PART.ADD_SPARE_PART_DATA"];
            FAILED_SPARE_UPLOAD = resp["UPLOAD_PART.FAILED_SPARE_UPLOAD"];
        });

        function fileChanged(obj) {
            vm.loading2 = true;
            var elem = obj.target || obj.srcElement;
            if (elem.files.length > 0) {
                vm.file = elem.files[0];
                vm.filename = vm.file.name;
                vm.isFileSelected = true;
                readFile(vm.file);
            } else {
                vm.isFileSelected = false;
                vm.loading2 = false; // Set to false here if no file is selected
            }
            $scope.$apply(); // Changed to $apply to ensure view updates
        }

        function readFile(file) {
            var reader = new FileReader();
            reader.onload = function (e) {
                var contents = e.target.result;
                vm.isValidFile = checkFileValidity(contents);
                vm.loading2 = false; // Set loading2 to false once the file is read
                $scope.$apply(); // Ensure view is updated
            };
            reader.readAsText(file);
        }

        function checkFileValidity(csvData) {
            var firstLine = csvData.split("\n")[0].toLowerCase();
            var columns = firstLine.split(",").map((column) => column.trim());
            return columns.includes("part number") && columns.includes("spare part identifier");
        }

        function uploadDetails() {
            if (!vm.isFileSelected) {
                headerBannerService.setNotification("ERROR", SELECT_CSV, 5000);
                vm.loading2 = false; // Ensure loading2 is set to false on error
                return;
            }
            vm.loading2 = true;
            uploadPartService.uploadSparePartDetailsFile(vm.model.modelId, vm.file).then(detailsUploadSuccess, detailsUploadFailed);
        }

        function detailsUploadSuccess(response) {
            vm.loading2 = false;
            $uibModalInstance.close();
            headerBannerService.setNotification("SUCCESS", ADD_SPARE_PART_DATA, 5000);
        }

        function detailsUploadFailed(error) {
            vm.loading2 = false;
            headerBannerService.setNotification("ERROR", FAILED_SPARE_UPLOAD, 5000);
        }
    }
})();
