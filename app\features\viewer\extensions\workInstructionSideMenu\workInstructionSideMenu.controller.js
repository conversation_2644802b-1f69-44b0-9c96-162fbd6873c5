(function () {
    'use strict';

    angular
        .module('app.viewer')
        .controller('WorkInstructionSideMenuController', WorkInstructionSideMenuController);

    WorkInstructionSideMenuController.$inject = ['$rootScope'];

    function WorkInstructionSideMenuController($rootScope) {
        var vm = this;

        vm.isMinimized = false;
        vm.status = {
            isModelBrowserOpen : true,
            isViewerSettingsOpen: false
        };

        vm.minimizeSideMenu = minimizeSideMenu;
        vm.maximizeSideMenu = maximizeSideMenu;

        initialize();

        function initialize() {

        }

        function minimizeSideMenu() {
            vm.isMinimized = true;
            $rootScope.$broadcast("side-menu-minimized");
        }

        function maximizeSideMenu() {
            vm.isMinimized = false;
            $rootScope.$broadcast("side-menu-maximized");
        }

    }


})();