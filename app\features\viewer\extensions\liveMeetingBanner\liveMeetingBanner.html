<div ng-controller="LiveMeetingBannerController as liveMeetingBannerCtrl" ng-show="liveMeetingBannerCtrl.notificationDisplay"
     class="viewerBanner">
    <div class="d-flex flex-column align-items-center justify-content-center meeting-banner row"
        style="box-shadow: 0px 4px 6px 0px rgb(0 0 0 / 16%);">
        <div class="p-2">
            <div>
            <lable translate> LIVE_MEETING_BANNER.PRESENTER </lable>
            {{liveMeetingBannerCtrl.presenter}}
            </div>
        </div>
        <div class="p-2">
            <div ng-show="liveMeetingBannerCtrl.isHost">
                <div class="btn-group">
                    <div class="btn secondary main-action ng-scope">{{liveMeetingBannerCtrl.hostName}}</div>
                    <div href="" class="btn secondary dropdown-toggle-window" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <div class="sub-popupwindow">
                            <ul class="more-options">
                                <li ng-repeat="user in liveMeetingBannerCtrl.users">
                                    {{user.name}}
                                    <span ng-show="user.isActive" class="dot  pull-right" style="background-color: #00ff00;"></span>
                                    <span ng-show="!user.isActive" class="dot  pull-right" style="background-color: #ff0000;"></span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="p-2">
            <button ng-show="(!liveMeetingBannerCtrl.isHost)" type="button" class="btn btn-danger  pull-right" style="border: 0px;"
            ng-click="liveMeetingBannerCtrl.endMeeting()" translate>
            LIVE_MEETING_BANNER.LEAVE_MEETING
            </button>
            <button ng-show="liveMeetingBannerCtrl.isHost" type="button" class="btn btn-danger  pull-right" style="border: 0px;"
            ng-click="liveMeetingBannerCtrl.endMeeting()" translate>
            LIVE_MEETING_BANNER.END_MEETING
            </button>
        </div>
    </div>
</div>