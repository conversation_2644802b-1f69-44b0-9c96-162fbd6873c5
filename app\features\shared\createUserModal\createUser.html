<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" ng-click="createUserCtrl.cancel()" aria-label="Close">
        <i class="fa fa-close" aria-hidden="true"></i>
    </button>

    <h2 class="modal-title">{{"CREATE_USER.CREATE_NEW" | translate}} {{createUserCtrl.type | lowercase}} {{"CREATE_USER.USER" | translate}}</h2>

</div>


<div class="modal-body scrollBehaviourSmooth" id="scrollToTop">
    <h3 ng-if="createUserCtrl.hasErrorMessage" class="error-alert">
        {{createUserCtrl.errorMessage}}
    </h3>

    <form class="form" name="createCompanyForm">

        <div class="input-group">
            <label translate>CREATE_USER.FIRST_NAME</label>
            <input type="text" placeholder="{{'CREATE_USER.FIRST_NAME' | translate}}" ng-click="createUserCtrl.onlyLetters" ng-model="createUserCtrl.firstName" ng-required="true">
        </div>

        <div class="input-group">
            <label translate>CREATE_USER.LAST_NAME</label>
            <input type="text" placeholder="{{'CREATE_USER.LAST_NAME' | translate}}" ng-model="createUserCtrl.lastName" ng-required="true">
        </div>

        <div class="input-group">
            <label translate>CREATE_USER.EMAIL_ADDRESS</label>
            <input type="email" name="txtEmail"  placeholder="{{'CREATE_USER.EMAIL_ADDRESS' | translate}}" ng-model="createUserCtrl.emailAddress"
                   ng-required="true" ng-pattern='/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/'>
            <div ng-show="createCompanyForm.txtEmail.$dirty && createCompanyForm.txtEmail.$invalid" style="color: red" translate>CREATE_USER.INVALID_EMAIL</div>
        </div>
        <div class="input-group" ng-if="createUserCtrl.isDealer && createUserCtrl.isSupreme">
            <label>Visibility Contact ID</label>
            <input type="number" placeholder="Visibility Contact ID" ng-model="createUserCtrl.visContactId" ng-required="true">
        </div>

        <div ng-if="createUserCtrl.type === 'Internal'">
            <h3 class="group-title" translate>CREATE_USER.USER_PERMISSIONS</h3>

            <table class="simple-table">
                <thead>
                <tr>
                    <th translate>CREATE_USER.PERMISSION</th>
                    <th translate>CREATE_USER.DESCRIPTION</th>
                    <th></th>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <td><strong translate>CREATE_USER.ORDERS</strong></td>
                    <td translate>CREATE_USER.ORDERS_SUBTEXT</td>
                    <td><input type="checkbox" ng-model="createUserCtrl.permissions.orders" class="checkbox"></td>

                </tr>
                <tr>
                    <td><strong translate>CREATE_USER.PRODUCTS</strong></td>
                    <td translate>CREATE_USER.PRODUCTS_SUBTEXT</td>
                    <td><input type="checkbox" ng-model="createUserCtrl.permissions.products" class="checkbox"></td>

                </tr>
                <tr>
                    <td><strong translate>CREATE_USER.PUBLICATIONS</strong></td>
                    <td translate>CREATE_USER.PUBLICATIONS_SUBTEXT</td>
                    <td><input type="checkbox" ng-model="createUserCtrl.permissions.publications" class="checkbox"></td>

                </tr>
                <tr>
                    <td><strong translate>CREATE_USER.CUSTOMERS</strong></td>
                    <td translate>CREATE_USER.CUSTOMERS_SUBTEXT</td>
                    <td><input type="checkbox" ng-model="createUserCtrl.permissions.customers" class="checkbox"></td>
                </tr>
                <tr>
                    <td><strong translate>CREATE_USER.ADMIN</strong></td>
                    <td translate>CREATE_USER.ADMIN_SUBTEXT</td>
                    <td><input type="checkbox" ng-model="createUserCtrl.permissions.admin" class="checkbox"></td>
                </tr>
                <tr>
                    <td><strong translate>CREATE_USER.SECURITY</strong></td>
                    <td translate>CREATE_USER.SECURITY_SUBTEXT</td>
                    <td><input type="checkbox" ng-model="createUserCtrl.permissions.security" class="checkbox"></td>
                </tr>
                <tr>
                    <td><strong translate>CREATE_USER.PARTS</strong></td>
                    <td translate>CREATE_USER.PARTS_SUBTEXT</td>
                    <td><input type="checkbox" ng-model="createUserCtrl.permissions.parts" class="checkbox"></td>
                </tr>
                <tr ng-show="createUserCtrl.dashboardEnabled">
                    <td><strong translate>CREATE_USER.DASHBOARD</strong></td>
                    <td translate>CREATE_USER.DASHBOARD_SUBTEXT</td>
                    <td><input type="checkbox" ng-model="createUserCtrl.permissions.dashboard" class="checkbox"></td>
                </tr>

                </tbody>
            </table>
        </div>

		<div class="mt-2" ng-if="createUserCtrl.type === 'Internal'" ng-show="createUserCtrl.isManufacturer">
			  <h3 class="group-title" translate>CREATE_USER.USER_SETTINGS</h3>

			  <table class="simple-table">
				    <thead>
				    <tr>
						<th translate>CREATE_USER.PERMISSION</th>
						<th translate>CREATE_USER.DESCRIPTION</th>
						<th></th>
				    </tr>
				    </thead>
				    <tbody>

				    <tr ng-show="createUserCtrl.isManufacturer">
						<td><strong translate>CREATE_USER.DISCOUNT</strong></td>
						<td translate>CREATE_USER.DISCOUNT_SUBTEXT</td>
						<td><input type="checkbox" ng-model="createUserCtrl.isDiscountEditable" class="checkbox"></td>
				    </tr>

				    </tbody>
			  </table>
		</div>

        <div ng-if="createUserCtrl.type !== 'Internal'">
            <h3 class="group-title" translate>CREATE_USER.USER_PERMISSIONS</h3>

            <table class="simple-table">
                <thead>
                <tr>
                    <th translate>CREATE_USER.PERMISSION</th>
                    <th translate>CREATE_USER.DESCRIPTION</th>
                    <th></th>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <td><strong translate>CREATE_USER.CUST_ORDERS</strong></td>
                    <td translate>CREATE_USER.CUST_ORDERS_SUBTEXT</td>
                    <td><input type="checkbox" ng-model="createUserCtrl.permissions.orders" class="checkbox"></td>

                </tr>
                <tr>
                    <td><strong translate>CREATE_USER.PUBLISHED_PRODUCTS</strong></td>
                    <td translate>CREATE_USER.PUBLISHED_PRODUCTS_SUBTEXT</td>
                    <td><input type="checkbox" ng-model="createUserCtrl.permissions.publishedProducts" class="checkbox"></td>

                </tr>
                <tr ng-if="createUserCtrl.hasPartSearchEnabled">
                    <td><strong translate>CREATE_USER.PART_SEARCH</strong></td>
                    <td translate>CREATE_USER.PART_SEARCH_SUBTEXT</td>
                    <td><input type="checkbox" ng-model="createUserCtrl.permissions.partSearch" class="checkbox"></td>

                </tr>

                </tbody>
            </table>
        </div>


        <div class="modal-actions">
            <button type="button" class="btn small secondary" data-dismiss="modal"
                    ng-click="createUserCtrl.cancel()" translate>
                GENERAL.CANCEL
            </button>

            <button type="button" class="btn small primary"
                    ng-disabled="!createCompanyForm.$valid"
                    ng-click="createUserCtrl.createNewUser()">
                {{'CREATE_USER.CREATE' | translate}} {{createUserCtrl.type}} {{"CREATE_USER.USER" | translate}}
            </button>
        </div>
    </form>
</div>


