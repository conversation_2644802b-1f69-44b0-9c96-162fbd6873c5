(function () {
    'use strict';

    angular
        .module('app.base')
        .controller('LockAccountController', LockAccountController);

    LockAccountController.$inject = ['$stateParams', 'lockAccountService'];

    function LockAccountController($stateParams, lockAccountService) {
        var vm = this;

        vm.showSuccessMessage = false;
        vm.showErrorMessage = false;

        initialize();

        function initialize () {
            var userId = $stateParams.userId ? $stateParams.userId : "";
            lockAccountService
                .lockUser(userId)
                .then(lockUserSuccess, lockUserFailure);
        }

        function lockUserSuccess (response) {
            if (response.data) {
                vm.showSuccessMessage = true;
            } else {
                vm.showErrorMessage = true;
            }
        }

        function lockUserFailure(err) {
            vm.showErrorMessage = true;
            console.log("Failed to lock user account");
        }

    }
})();