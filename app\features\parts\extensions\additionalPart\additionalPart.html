<section class="body-content">
    <div class="order-details-holder">
        <h1 translate>ADDITIONAL_PARTS.TITLE</h1>
        <p class="page-desc" translate>ADDITIONAL_PARTS.SUBTITLE</p>
        <br>
        <p translate>ADDITIONAL_PARTS.USE_SEARCH</p>
    </div>
</section>
<section class="body-content page-body">
    <div class="row">
        <div class="col-lg-6">
            <div class="panel">
                <form class="form" name="optionForm">

                    <table class="table table-bordered">
                        <thead>
                        <tr>
                            <th translate>ADDITIONAL_PARTS.PART_NUMBER</th>
                            <th translate>ADDITIONAL_PARTS.DESCRIPTION</th>
                            <th></th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr ng-repeat="part in additionalPartCtrl.parts track by part.partNumber">
                            <td>{{part.partNumber}}</td>
                            <td>{{part.description ? part.description : part.partDescription}}</td>
                            <td>
                                <a href="" ng-click="additionalPartCtrl.removePart($index)"
                                   class="delete fa fa-trash"></a>
                            </td>
                        </tr>

                        <tr ng-show="!additionalPartCtrl.parts.length > 0">
                            <td class="emptytable" colspan="3" translate>ADDITIONAL_PARTS.NO_PARTS </td>
                        </tr>
                        </tbody>
                    </table>
                    <div class="error-well" ng-if="additionalPartCtrl.noPartsSelectedError" translate>
                        ADDITIONAL_PARTS.AT_LEAST_ONE
                    </div>

                    <div class="btn-actions">
                        <button class="btn small secondary" type="button" ng-click="additionalPartCtrl.cancel()" translate>GENERAL.CANCEL</button>
                        <button class="btn small primary mr-16" type="submit"
                                ng-click="optionForm.$valid && additionalPartCtrl.save()" translate>ADDITIONAL_PARTS.SAVE
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <div class="col-lg-6 py-lg-0 py-5 mb-5">
            <div class="bg-white pb-2 pt-4 px-4">
                <inline-parts-search on-add-clicked="additionalPartCtrl.onAddClicked(masterPart)"></inline-parts-search>
            </div>
        </div>

    </div>
</section>



