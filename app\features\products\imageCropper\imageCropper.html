<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" ng-click="imageCropperCtrl.close()" aria-label="Close"><i
            class="fa fa-close" aria-hidden="true"></i></button>
    <h2 class="modal-title" translate>IMG_CROPPER.CHOOSE_IMG</h2>
  </div>
  <div class="modal-body">
   <!-- <input type="file" img-cropper-fileread image="imageCropperCtrl.cropper.sourceImage"/>-->

    <div class="upload-box">
      <div>
        <i class="fa fa-upload"></i>
        <h4 class="file-uploader" translate>IMG_CROPPER.CHOOSE_FILE</h4>
        <input type="file" class="fileupload" img-cropper-fileread image="imageCropperCtrl.cropper.sourceImage" accept=".png, jpg, jpeg, tif, gif"/>
      </div>
    </div>
	    <div class="d-flex flex-wrap no-gutters">
      <div class="col-12 col-md-6 pb-2 pb-md-0">
        <p translate>IMG_CROPPER.UPLOADED_IMG</p>
        <canvas max-width="750" max-height="440" id="canvas" image-cropper image="imageCropperCtrl.cropper.sourceImage"
                cropped-image="imageCropperCtrl.cropper.croppedImage" crop-width="imageCropperCtrl.cropWidth" crop-height="imageCropperCtrl.cropHeight"
                keep-aspect="true" touch-radius="30" crop-area-bounds="imageCropperCtrl.bounds"></canvas>
    </div>

      <div class="col-12 col-md-6">

    <p translate>IMG_CROPPER.CROPPED_IMG</p>

    <div ng-show="imageCropperCtrl.cropper.croppedImage!=null"><img ng-src="{{imageCropperCtrl.cropper.croppedImage}}"/>
    </div>

      </div>
	    </div>
    <div class="modal-actions mt-2">
      <button class="btn small secondary" type="button" ng-click="imageCropperCtrl.close()" translate>GENERAL.CANCEL</button>
      <button class="btn small primary" type="button" ng-click="imageCropperCtrl.ok()" translate>IMG_CROPPER.OK</button>
    </div>
  </div>