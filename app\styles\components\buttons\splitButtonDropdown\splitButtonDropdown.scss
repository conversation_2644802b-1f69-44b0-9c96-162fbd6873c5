/* cadSplitDropdown */

.cadSplitDropdown {
  min-width: 96px;
}

.cadSplitDropdown .dropdown-menu {
  margin-left: -12.365rem;
  transition: visibility 0s, opacity 0.3s linear;
  position: absolute;
  transform: translate3d(0px, 33px, 0px) !important;
  top: 0;
  left: 0;
  overflow-y: visible;
}

.cadSplitDropdown .dropdown-menu a {
  font-size: 0.9em;
}

.cadSplitDropdown .dropdown-menu a:hover {
  color: #0477f8;
  text-decoration: underline;
}

.btn-group > .dropdown-toggle-split:active {
  /*Without this, clicking will make it sticky*/
  pointer-events: none;
}

.cadSplitDropdown .dropdown-menu a.delete:hover {
  color: #a94442;
  text-decoration: underline;
}

.cadSplitDropdown .dropdown-toggle-split:hover + .dropdown-menu,
.cadSplitDropdown .dropdown-menu:hover {
  display: block;
}

.cadSplitDropdown .dropdown-menu::before {
  content: "";
  display: inline-block;
  width: 0;
  height: 0;
  border-bottom: 12px solid #fff;
  border-right: 12px solid transparent;
  border-left: 12px solid transparent;
  position: absolute;
  top: -12px;
  right: 6.5%;
}

.cadSplitDropdown .dropdown-split {

  position: relative;

}

@media (max-width: 992px) {
  .cadSplitDropdown .dropdown-menu {
    transform: translate3d(0px, 32px, 0px);
  }
  .cadSplitDropdown .dropdown-toggle-split:hover + .dropdown-menu,
  .cadSplitDropdown .dropdown-menu:hover {
    display:none;
  }
}