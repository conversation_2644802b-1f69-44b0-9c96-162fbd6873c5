(function () {
    "use strict";

    angular.module("app.viewer").controller("ManufacturerViewerController", ManufacturerViewerController);

    ManufacturerViewerController.$inject = [
        "viewerSettingsService",
        "viewerService",
        "$q",
        "$uibModal",
        "$stateParams",
        "$state",
        "$scope",
        "$window",
        "viewerHelperService",
        "$timeout",
        "viewerVariablesService",
        "$rootScope",
        "viewerBannerService",
        "tokenService",
        "userService",
        "apiConstants",
        "modelService",
        "$translate",
        "createMeetingService",
        "$location",
        "liveMeetingBannerService",
        "viewableService",
        "LockStateService",
        "headerBannerService",
    ];

    function ManufacturerViewerController(
        viewerSettingsService,
        viewerService,
        $q,
        $uibModal,
        $stateParams,
        $state,
        $scope,
        $window,
        viewerHelperService,
        $timeout,
        viewerVariablesService,
        $rootScope,
        viewerBannerService,
        tokenService,
        userService,
        apiConstants,
        modelService,
        $translate,
        createMeetingService,
        $location,
        liveMeetingBannerService,
        viewableService,
        LockStateService,
        headerBannerService
    ) {
        var vm = this;
        var viewerApp = null;

        var options = viewerService.getAutodeskToken().then(function (resp) {
            options = resp;
            options.useADP = false;
            if ($stateParams.translateType === "SVF2") {
                options.env = "AutodeskProduction2";
                options.api = "streamingV2";
            } else {
                options.env = "AutodeskProduction";
                options.api = "derivativeV2";
            }
            initialize();
        });

        var viewableId = "";
        var parentId = "ROOT";
        var BASE_STEP = { stateName: "Reset to Top Level", stateId: "ROOT" };
        var leafNodes = [];
        var priorSelection = [];
        var customWeldments = [];
        var statesReturned = false;
        var viewerLoaded = false;
        var modelTreeLoaded = false;
        var isSetupComplete = false;
        var translateToolBtn;
        var isGhosted = false;
        var isOverwriteSnapshot = false;
        var viewer = {};
        var customsSettingsAppliedFlag = false;
        var currentSnapshot = {};
        var isCtrlPressed = false;
        var selectionWindowActive = false;
        var isMetadataActive = false;
        var isInitialViewableSaved = false;

        vm.isLinkModelPartActive = false;
        vm.showLinkPartModel = false;
        vm.modelId = $stateParams.modelId;
        vm.model = {};
        vm.isNonModeledPartsActive = false;
        vm.currentList = [];
        vm.snapshots = [];
        vm.steps = [BASE_STEP];
        vm.viewerMessage = "";
        vm.showCreateNewPart = false;
        vm.machineName = $stateParams.machineName;
        vm.viewableName = $stateParams.viewableName;
        vm.isExplodeSliderVisible = false;
        vm.viewerSettings = {};
        vm.selectedParts = [];
        vm.partDetailsOpen = true;
        vm.isPartEditMode = false;
        vm.isPartsListLoading = false;
        vm.partEdits = {};
        vm.isSideMenuOpen = window.window.innerWidth > 680;
        vm.SharingViewer = null;
        vm.arrayOfLockedEntities = [];
        vm.fromWhereUsedModal = $window.sessionStorage.getItem("fromWhereUsedModal") === "true";
        $window.sessionStorage.removeItem("fromWhereUsedModal");
        vm.fromWhereUsedModal = $window.sessionStorage.getItem("fromWhereUsedModal") === "true";
        $window.sessionStorage.removeItem("fromWhereUsedModal");
        vm.isAutoPublishable = userService.getAutoPublishable();

        vm.saveSnapshot = saveSnapshot;
        vm.loadSnapshot = loadSnapshot;
        vm.editSnapshotName = editSnapshotName;
        vm.deleteSnapshot = deleteSnapshot;
        vm.startNonModeledPartsFlow = startNonModeledPartsFlow;
        vm.hideCreateNewPart = hideCreateNewPart;
        vm.isCreateNewPartVisible = isCreateNewPartVisible;
        vm.updateNonModeledParts = updateNonModeledParts;
        vm.getNonModeledParts = getNonModeledParts;
        vm.weldmentsUpdated = weldmentsUpdated;
        vm.openCustomerPreview = openCustomerPreview;
        vm.toggleLinkPartFlow = toggleLinkPartFlow;
        vm.overwriteSnapshot = overwriteSnapshot;
        vm.backToViewables = backToViewables;
        vm.showSpinner = showSpinner;
        vm.hideSpinner = hideSpinner;
        vm.setPartDetailsOpen = setPartDetailsOpen;
        vm.resetPartEdits = resetPartEdits;
        vm.savePartEdits = savePartEdits;
        vm.isPartChanges = isPartChanges;
        vm.disableTranslate = disableTranslate;
        vm.deactivateExplodeTool = deactivateExplodeTool;
        vm.toggleSelectionWindow = toggleSelectionWindow;
        vm.deactivateWindowSelection = deactivateWindowSelection;
        vm.remoteUpdateViewer = remoteUpdateViewer;
        vm.getVisibleIds = getVisibleIds;
        vm.edgingEnabledDefault = userService.getEdgingEnabledDefault();
        vm.isEntitiesLocked = true;

        var WENT_WRONG,
            RESET_TO_TOP,
            DELETE_SNAPSHOT,
            IF_YOU_CONFIRM,
            CHILDREN_DELETED,
            SELECT_PART_ASSOCIATED,
            NON_MODEL_SUCCESS,
            SELECT_LINK,
            OVERWRITE,
            IF_CONFIRM,
            WILL_BE_OVERWRITTEN,
            PART_EDIT_SUCCESS,
            PART_EDIT_FAIL,
            ACCESS_DENIED_ERROR,
            NO_CRITICAL_PARTS,
            PUBLICATION_SUCCESS;
        $translate([
            "GENERAL.WENT_WRONG",
            "MANUFACTURER_VIEWER.RESET_TO_TOP",
            "MANUFACTURER_VIEWER.DELETE_SNAPSHOT",
            "MANUFACTURER_VIEWER.IF_YOU_CONFIRM",
            "MANUFACTURER_VIEWER.CHILDREN_DELETED",
            "MANUFACTURER_VIEWER.SELECT_PART_ASSOCIATED",
            "MANUFACTURER_VIEWER.NON_MODEL_SUCCESS",
            "MANUFACTURER_VIEWER.SELECT_LINK",
            "MANUFACTURER_VIEWER.OVERWRITE",
            "MANUFACTURER_VIEWER.IF_CONFIRM",
            "MANUFACTURER_VIEWER.WILL_BE_OVERWRITTEN",
            "MANUFACTURER_VIEWER.PART_EDIT_SUCCESS",
            "MANUFACTURER_VIEWER.PART_EDIT_FAIL",
            "MANUFACTURER_VIEWER.ACCESS_DENIED_ERROR",
            "MANUFACTURER_VIEWER.NO_CRITICAL_PARTS",
            "MANUFACTURER_VIEWER.PUBLICATION_SUCCESS",
        ]).then(function (resp) {
            WENT_WRONG = resp["GENERAL.WENT_WRONG"];
            RESET_TO_TOP = resp["MANUFACTURER_VIEWER.RESET_TO_TOP"];
            DELETE_SNAPSHOT = resp["MANUFACTURER_VIEWER.DELETE_SNAPSHOT"];
            IF_YOU_CONFIRM = resp["MANUFACTURER_VIEWER.IF_YOU_CONFIRM"];
            CHILDREN_DELETED = resp["MANUFACTURER_VIEWER.CHILDREN_DELETED"];
            SELECT_PART_ASSOCIATED = resp["MANUFACTURER_VIEWER.SELECT_PART_ASSOCIATED"];
            NON_MODEL_SUCCESS = resp["MANUFACTURER_VIEWER.NON_MODEL_SUCCESS"];
            SELECT_LINK = resp["MANUFACTURER_VIEWER.SELECT_LINK"];
            OVERWRITE = resp["MANUFACTURER_VIEWER.OVERWRITE"];
            IF_CONFIRM = resp["MANUFACTURER_VIEWER.IF_CONFIRM"];
            WILL_BE_OVERWRITTEN = resp["MANUFACTURER_VIEWER.WILL_BE_OVERWRITTEN"];
            PART_EDIT_SUCCESS = resp["MANUFACTURER_VIEWER.PART_EDIT_SUCCESS"];
            PART_EDIT_FAIL = resp["MANUFACTURER_VIEWER.PART_EDIT_FAIL"];
            ACCESS_DENIED_ERROR = resp["MANUFACTURER_VIEWER.ACCESS_DENIED_ERROR"];
            NO_CRITICAL_PARTS = resp["MANUFACTURER_VIEWER.NO_CRITICAL_PARTS"];
            PUBLICATION_SUCCESS = resp["MANUFACTURER_VIEWER.PUBLICATION_SUCCESS"];
        });

        viewerVariablesService.setModelId($stateParams.modelId);

        function getModel() {
            return modelService
                .fetchModel(vm.modelId)
                .then(getModelSuccess)
                .catch(function (error) {
                    if (error.status === 403) {
                        vm.isModelFetchError = true;
                        vm.errorMessage = ACCESS_DENIED_ERROR;
                        throw error;
                    }
                });
        }

        function getModelSuccess(response) {
            vm.model = response.data;
            leafNodes = JSON.parse(response.data.leafNodes) || [];
            isSetupComplete = response.data.isSetupComplete;
            if (isSetupComplete) {
                var retrieval = getLockedParts();
                console.log("Attempting to get the locked entities ", retrieval);
            }
        }

        function getSnapshots() {
            viewerService.getViewableByModelId(vm.modelId).then(geViewableSuccess);
        }

        function initialize() {
            getModel()
                .then(function () {
                    viewerHelperService.showSpinner();
                    autodeskViewerInitialize();
                    getSnapshots();
                })
                .catch(function (error) {
                    hideSpinner();
                    console.error(error);
                });
        }

        function autodeskViewerInitialize() {
            $scope.$on("$locationChangeStart", function (event) {
                if (!viewerLoaded) {
                    event.preventDefault();
                    console.log("Viewable loading back navigation disabled");
                } else {
                    console.log("Viewer Shutdown On Navigation");
                }
            });

            Autodesk.Viewing.Initializer(options, function onInitialized() {
                var config3d = {
                    extensions: ["CADShareExtension", "Autodesk.NPR"],
                };
                viewerApp = new Autodesk.Viewing.ViewingApplication("MyViewerDiv");
                viewerApp.registerViewer(viewerApp.k3D, Autodesk.Viewing.GuiViewer3D, config3d);

                var documentId = "urn:" + $stateParams.autodeskURN;
                viewerApp.loadDocument(documentId, onDocumentLoadSuccess, loadFailure);
            });
        }

        function getInitialStateDetails() {
            getStateDetailPlusChildren("ROOT").then(function (data) {
                addReturnedStateDetails(data);
                getStateDetailsSuccess();
            });
        }

        function getStateDetailPlusChildren(stateId) {
            return viewerService.getStateDetails(vm.modelId, stateId, 1).then(addReturnedStateDetails);
        }

        function addReturnedStateDetails(stateDetails) {
            if (stateDetails) {
                for (var i = 0; i < stateDetails.length; i++) {
                    if (!findSnapshotByStateId(stateDetails[i].stateId)) {
                        vm.snapshots.push(stateDetails[i]);
                    }
                }
                refreshSnapshotList();
            }
        }

        function getStateDetailsSuccess() {
            statesReturned = true;
            $rootScope.$broadcast("loading-complete");
        }

        function setLocalViewerSettings(data) {
            if (data) {
                vm.viewerSettings.lineDrawingEnabled = data.lineDrawingEnabled;
                vm.viewerSettings.edgingEnabled = data.edgingEnabled;
                vm.viewerSettings.viewLocked = data.viewLocked;
                vm.viewableId = data.id;

                $rootScope.$broadcast("viewer-settings-loaded", vm.viewerSettings);
            }
        }

        function geViewableSuccess(data) {
            setLocalViewerSettings(data);
            getInitialStateDetails();
        }

        function onDocumentLoadSuccess() {
            var viewables = viewerApp.bubble.search({ type: "geometry" });

            if (viewables.length === 0) {
                console.error("Document contains no viewables.");
                return;
            }

            viewerApp.selectItem(viewables[0].data);
            viewerApp.getCurrentViewer().addEventListener(Autodesk.Viewing.SELECTION_CHANGED_EVENT, onSelectionChanged);
            viewerApp.getCurrentViewer().addEventListener(Autodesk.Viewing.GEOMETRY_LOADED_EVENT, onViewerLoadedSuccess);
            viewerApp.getCurrentViewer().addEventListener(Autodesk.Viewing.OBJECT_TREE_CREATED_EVENT, geometobjectTreeCreated);
            viewerApp.getCurrentViewer().addEventListener(Autodesk.Viewing.TOOLBAR_CREATED_EVENT, (toolbar) =>
                setTimeout(() => {
                    console.log("TOOLBAR_CREATED_EVENT");
                    configureViewerAndToolbar(toolbar.target);
                }, 2500)
            );

            viewerHelperService.setViewerApp(viewerApp.getCurrentViewer());
            viewerApp.getCurrentViewer()._hotkeyManager.popHotkeys("Autodesk.Escape");
        }

        $scope.$on("model-tree-initialized", function (event, modelTree) {
            vm.modelTree = modelTree;
            viewerHelperService.setPartTreeTopId(vm.modelTree[0].objectId);
            modelTreeLoaded = true;
            $rootScope.$broadcast("loading-complete");
            $rootScope.$broadcast("side-menu-maximized");
        });

        $scope.$on("toggle-all-locked-nodes", function () {
            vm.isEntitiesLocked = !vm.isEntitiesLocked;
            $rootScope.$broadcast("viewer-part-selected", []);
            viewerApp.getCurrentViewer().clearSelection();
        });

        function loadFailure(viewerErrorCode, errorMsg, errors) {
            console.error("onDocumentLoadFailure() - errorCode:" + viewerErrorCode);
            console.log("onDocumentLoadFailure() - errorMsg:" + errorMsg);
            console.log("onDocumentLoadFailure() - errors:" + errors);
            viewerHelperService.hideSpinner();
        }

        function getSnapshotViewport(snapshot) {
            return JSON.parse(snapshot.state).viewport;
        }

        function viewportsAreEquivalent(currentViewport, snapshotViewport) {
            var currentViewportDefiningFields = {
                distanceToOrbit: currentViewport.distanceToOrbit,
                eye: currentViewport.eye,
                target: currentViewport.target,
            };

            var snapshotViewportDefiningFields = {
                distanceToOrbit: snapshotViewport.distanceToOrbit,
                eye: snapshotViewport.eye,
                target: snapshotViewport.target,
            };

            return _.isEqual(snapshotViewportDefiningFields, currentViewportDefiningFields);
        }

        let finalRenderSpinnerHide = null;
        function onFinalRender(event) {
            if(finalRenderSpinnerHide !== null)
                clearTimeout(finalRenderSpinnerHide);
            if (!customsSettingsAppliedFlag) {
                var currentViewport = viewerApp.getCurrentViewer().getState().viewport;
                var snapshotViewport = getSnapshotViewport(currentSnapshot);

                if (viewportsAreEquivalent(currentViewport, snapshotViewport)) {
                    viewerHelperService.applyCustomViewerSettings(viewerApp.getCurrentViewer(), vm.viewerSettings);
                    customsSettingsAppliedFlag = true;
                    viewerApp.getCurrentViewer().removeEventListener(Autodesk.Viewing.FINAL_FRAME_RENDERED_CHANGED_EVENT, onFinalRender);
                }
            }
            finalRenderSpinnerHide = setTimeout(function() {
                viewerHelperService.hideSpinner();
            }, 1000);
            if(event.value.finalFrame) {
                viewerHelperService.hideSpinner();
                clearTimeout(finalRenderSpinnerHide);
            }
        }

        function applyDefaultSettings(viewer) {
            var bgColour = viewerHelperService.getBackgroundColour();
            viewer.setBackgroundColor(bgColour.r, bgColour.g, bgColour.b, bgColour.r, bgColour.g, bgColour.b);
            viewer.setTheme("light-theme");
            viewer.setEnvMapBackground(false);
            viewer.setGroundReflection(false);
            viewer.setGroundShadow(false);
            viewer.setOptimizeNavigation(false);
            viewer.hidePoints(true);
            viewer.hideLines(true);
            viewer.setProgressiveRendering(true);
            viewer.setQualityLevel(false, false);
            viewer.setGhosting(isGhosted);
            viewer.impl.selectionMaterialTop.opacity = 0;
        }

        function toggleMetadata() {
            isMetadataActive = !isMetadataActive;
        }

        function configureViewerAndToolbar(viewer) {
            applyDefaultSettings(viewer);

            var toolbar = viewer.toolbar;

            var navTools = toolbar.getControl("navTools");
            navTools.removeControl("toolbar-bimWalkTool");

            var modelTools = toolbar.getControl("modelTools");
            modelTools.removeControl("toolbar-measurementSubmenuTool");
            modelTools.removeControl("toolbar-explodeTool");

            toolbar.removeControl("settingsTools");

            if (!translateToolBtn) {
                translateToolBtn = new Autodesk.Viewing.UI.Button("translate-tool");
                translateToolBtn.onClick = function (e) {
                    txTool = _translateTool.getName();

                    if (viewer.toolController.getActiveTool().activeName === txTool) {
                        viewer.toolController.deactivateTool(txTool);
                        translateToolBtn.removeClass("active");
                        translateToolBtn.addClass("inactive");
                    } else {
                        deactivateWindowSelection();
                        deactivateExplodeTool();
                        viewer.toolController.activateTool(txTool);
                        translateToolBtn.removeClass("inactive");
                        translateToolBtn.addClass("active");
                    }
                };
                translateToolBtn.addClass("translate-tool");
                translateToolBtn.setToolTip("Translate");

                var fragToolBtn = new Autodesk.Viewing.UI.Button("selection-window");
                fragToolBtn.onClick = selectionWindowClicked;
                fragToolBtn.addClass("selection-window");
                fragToolBtn.setToolTip("Selection Window");

                var explodeToolBtn = new Autodesk.Viewing.UI.Button("explode-tool");
                explodeToolBtn.onClick = function (e) {
                    toggleExplodePartVisibility();
                };
                explodeToolBtn.addClass("adsk-icon-explode");
                explodeToolBtn.setToolTip("Explode parts");

                var ghostToolBtn = new Autodesk.Viewing.UI.Button("ghosting-tool");
                ghostToolBtn.onClick = function (e) {
                    toggleGhosting();

                    if (isGhosted) {
                        ghostToolBtn.removeClass("inactive");
                        ghostToolBtn.addClass("active");
                    } else {
                        ghostToolBtn.removeClass("active");
                        ghostToolBtn.addClass("inactive");
                    }
                };
                ghostToolBtn.addClass("ghosting-tool");
                ghostToolBtn.setToolTip("Toggle ghosting on or off");

                var reverseViewBtn = new Autodesk.Viewing.UI.Button("reverse-view");
                reverseViewBtn.onClick = function () {
                    flip180();
                };
                reverseViewBtn.addClass("reverse-view");
                reverseViewBtn.setToolTip("Reverse angle");

                var subToolbar = new Autodesk.Viewing.UI.ControlGroup("cadshare-toolbar");
                subToolbar.addControl(explodeToolBtn);
                subToolbar.addControl(translateToolBtn);
                subToolbar.addControl(fragToolBtn);
                /* subToolbar.addControl(nonModeledPartToolBtn);
                subToolbar.addControl(partLinkToolBtn);*/
                subToolbar.addControl(ghostToolBtn);
                subToolbar.addControl(reverseViewBtn);
                addMetadataButton(subToolbar);
                toolbar.addControl(subToolbar, { index: 0 });

                var explodeToolContainer = document.querySelector(".manufacturer-explode-tool-container");
                document.getElementById("cadshare-toolbar").appendChild(explodeToolContainer);
            }
        }

        function createMetadataIconDiv() {
            const iconDiv = document.createElement("div");
            iconDiv.style.fontSize = "23px";
            iconDiv.style.paddingTop = "4px";
            iconDiv.classList.add("fas", "fa-clone");
            return iconDiv;
        }

        function findEntitiesByProperty(treeData, propertyName) {
            let results = [];
            traverseTree(treeData, propertyName, results);
            return results;
        }

        function traverseTree(treeData, propertyName, results) {
            for (let obj of treeData) {
                if (obj[propertyName]) {
                    results.push(obj.objectId);
                }

                if (obj.childParts && obj.childParts.length) {
                    traverseTree(obj.childParts, propertyName, results);
                }
            }
        }

        function handleMetadataButtonClick() {
            var viewer = viewerApp.getCurrentViewer();

            if (isMetadataActive) {
                loadSnapshot("ROOT");
                console.log("ROOT Snapshot was loaded");
                toggleMetadata();
            } else {
                let modelTree = JSON.parse(JSON.stringify(vm.modelTree));

                let criticalSparePart = findEntitiesByProperty(modelTree, "criticalSparePart");

                if (criticalSparePart && criticalSparePart.length > 0) {
                    viewerHelperService.isolateParts(JSON.stringify(criticalSparePart));
                    viewer.setGhosting(true);
                    toggleMetadata();
                } else {
                    alert(NO_CRITICAL_PARTS);
                }
            }
        }

        function addMetadataButton(toolGroup) {
            const metadataButton = new Autodesk.Viewing.UI.Button("metadata-retrieval");
            vm.metadataBtn = metadataButton;
            const iconDiv = createMetadataIconDiv();
            metadataButton.icon.style.display = "none";
            metadataButton.icon.parentNode.appendChild(iconDiv);
            metadataButton.container.appendChild(iconDiv);

            metadataButton.setToolTip("Show Critical Spare Parts");
            metadataButton.onClick = handleMetadataButtonClick;
            toolGroup.addControl(metadataButton);

            // Get the current CSV processing status from viewableService
            var isCsvProcessing = viewableService.getIsCsvProcessing();
            updateCriticalSparePartsButtonState(isCsvProcessing, metadataButton);
        }

        $rootScope.$on("csvProcessingStatusChanged", function (event, isCsvProcessing) {
            if (vm.metadataBtn) {
                updateCriticalSparePartsButtonState(isCsvProcessing, vm.metadataBtn);
            }
        });

        function updateCriticalSparePartsButtonState(Processing, metadataBtn) {
            if (Processing) {
                metadataBtn.container.classList.add("disabled-button");
            } else {
                metadataBtn.container.classList.remove("disabled-button");
                viewerService.getModelTree(vm.modelId).then(getModelTreeSuccess, failureMsg);
            }
        }

        function failureMsg() {}

        function getModelTreeSuccess(response) {
            vm.modelTree = [response.data];
            $rootScope.$broadcast("model-tree-initialized", vm.modelTree);
        }

        function deactivateExplodeTool() {
            $timeout(function () {
                vm.isExplodeSliderVisible = false;
            });
        }

        function selectionWindowClicked(e) {
            deactivateExplodeTool();
            deactivateTranslateTool();
            var fragToolBtn = viewer.toolbar.getControl("cadshare-toolbar").getControl("selection-window");
            if (selectionWindowActive) {
                fragToolBtn.removeClass("active");
                fragToolBtn.addClass("inactive");
            } else {
                fragToolBtn.removeClass("inactive");
                fragToolBtn.addClass("active");
            }
            toggleSelectionWindow();
        }

        function deactivateWindowSelection() {
            var fragToolBtn = viewer.toolbar.getControl("cadshare-toolbar").getControl("selection-window");
            selectionWindowActive = false;
            disableSelectionWindow();
            fragToolBtn.removeClass("active");
            fragToolBtn.addClass("inactive");
        }

        function deactivateExplodeTool() {
            vm.isExplodeSliderVisible = false;
            $scope.$apply();
        }

        function deactivateTranslateTool() {
            var translateToolBtn = viewer.toolbar.getControl("cadshare-toolbar").getControl("translate-tool");
            txTool = _translateTool.getName();
            if (viewer.toolController.getActiveTool().activeName === txTool) {
                viewer.toolController.deactivateTool(txTool);
            }
            translateToolBtn.removeClass("active");
            translateToolBtn.addClass("inactive");
        }

        function flip180() {
            var bbox = viewer.impl.getVisibleBounds(false, false);
            var pivot = bbox.center();
            var oldTarget = viewer.navigation.getTarget();
            var oldPosition = viewer.navigation.getPosition();

            var newPosition = {
                x: oldPosition.x + 2.0 * (pivot.x - oldPosition.x),
                y: oldPosition.y + 2.0 * (pivot.y - oldPosition.y),
                z: oldPosition.z + 2.0 * (pivot.z - oldPosition.z),
            };

            var newTarget = {
                x: oldTarget.x + 2.0 * (pivot.x - oldTarget.x),
                y: oldTarget.y + 2.0 * (pivot.y - oldTarget.y),
                z: oldTarget.z + 2.0 * (pivot.z - oldTarget.z),
            };

            viewer.navigation.setView(newPosition, newTarget);

            if (vm.SharingViewer !== null) {
                GLOBAL_SHARING_EVENT("REVERSE_ANGLE");
            }
        }

        function toggleExplodePartVisibility() {
            deactivateWindowSelection();
            deactivateTranslateTool();
            vm.isExplodeSliderVisible = !vm.isExplodeSliderVisible;
            $scope.$apply();
        }

        function toggleGhosting() {
            isGhosted = !isGhosted;
            viewer.setGhosting(isGhosted);
            if (vm.SharingViewer !== null) {
                GLOBAL_SHARING_EVENT("GHOST_TOGGLED");
            }
        }

        function backToViewables() {
            $state.go("productsModels", {
                productId: $stateParams.productId,
                machineName: $stateParams.machineName,
            });
        }

        function onViewerLoadedSuccess(response) {
            viewer = response.target;
            viewerHelperService.buildCustomContextMenu(viewer);
            configureViewerAndToolbar(viewer);
            viewerHelperService.setIsTwoD(false);

            $(document).bind("keyup", onKeyUp);
            $(document).bind("keydown", onKeyDown);

            viewerLoaded = true;

            //Start of CDE trsnparent part hotfix
            var cdeId = apiConstants.cdeId ? apiConstants.cdeId[0] : 0;
            console.log(apiConstants.cdeId[0]);
            if (userService.getManufacturerId() === cdeId) {
                var interval = setInterval(() => {
                    var matman = NOP_VIEWER.impl.matman();
                    if (matman !== undefined) {
                        clearInterval(interval);
                        // matman._materials["model:1|mat:5"].opacity = 1;
                        Object.keys(matman._materials).forEach((key) => {
                            matman._materials[key].transparent = false;
                        });
                        NOP_VIEWER.impl.invalidate(true, false, true);
                    }
                }, 500);
            }
            //End of CDE trsnparent part hotfix

            $rootScope.$broadcast("loading-complete");

            var meetingGuid = $location.search().guid;

            if (meetingGuid !== undefined) {
                vm.socket = createMeetingService.socketInit();
                var isHost = false;
                createMeetingService.getInvitedUsers(meetingGuid).then((users) => {
                    if (users && users.host === userService.getFullName() && meetingGuid === users.roomId) {
                        isHost = true;
                        createMeetingService.createRoom(meetingGuid);
                    }
                    viewer.loadExtension("SharingViewer", { isHost: isHost }).then(function (SharingViewer) {
                        vm.SharingViewer = SharingViewer;
                        SharingViewer.setSocket(vm.socket, userService.getFullName(), meetingGuid);
                        SharingViewer.setNotificationService(liveMeetingBannerService);
                        if (isHost) SharingViewer.setUsers(users);
                        else SharingViewer.updateRooms();
                        SharingViewer.joinSharing();
                    });
                });
            }
        }

        function saveInitialViewable() {
            var viewer = viewerApp.getCurrentViewer();
            viewer.fitToView();
            var modelState = {
                modelId: vm.modelId,
                leafNodes: JSON.stringify(leafNodes),
                isSetupComplete: isSetupComplete,
            };

            viewerService
                .createViewable(modelState)
                .then(function (resp) {
                    viewableId = resp.data;
                    if (vm.edgingEnabledDefault) {
                        viewerHelperService.setEdging(viewer);
                    }
                    return viewerService.getViewableByModelId(vm.modelId);
                })
                .then(setLocalViewerSettings)
                .then(() => takeScreenShot())
                .then(screenShotSuccess)
                .catch(function (error) {
                    console.error('An error occurred:', error);
                });
        }

        function geometobjectTreeCreated(evt) {
            //load the extension MySelectionWindow and call initialization
            viewerApp.getCurrentViewer().loadExtension("MySelectionWindow");
        }

        $scope.$on("loading-complete", function () {
            if (viewerLoaded && statesReturned && modelTreeLoaded) {
                if (viewerApp.getCurrentViewer().model.getData().instanceTree) {
                    if (!isSetupComplete) {
                        viewerHelperService.clearOldLeafNodes();
                        viewerHelperService.getAllLeafDbIds(viewerApp).then(function (res) {
                            leafNodes = res;
                            customWeldments = viewerHelperService.calculateWeldments(viewerApp);

                            var groupedEntities = [];
                            var lockedItems = [];
                            for (var x = 0; x < customWeldments.length; x++) {
                                if (leafNodes.indexOf(customWeldments[x]) < 0) {
                                    groupedEntities.push([customWeldments[x]]);
                                    lockedItems.push(customWeldments[x]);
                                }
                            }
                            for (var y = 0; y < leafNodes.length; y++) {
                                var leafsParent = viewerHelperService.getParentId(leafNodes[y]);
                                groupedEntities.push([leafsParent]);
                                lockedItems.push(leafsParent);
                            }
                            bulkAddIdToExplodeAsSingleEntity(groupedEntities);
                            $rootScope.$broadcast("locked-model-tree-updated", lockedItems);
                            viewerHelperService.showAllParts();

                            if (!isInitialViewableSaved) {
                                isInitialViewableSaved = true;
                                saveInitialViewable();
                            }
                        });
                    } else {
                        loadSnapshot("ROOT");
                    }
                } else {
                    $timeout(function () {
                        console.log("looping");
                        $rootScope.$broadcast("loading-complete");
                    }, 1000);
                }
            }
        });

        function setupComplete() {
            isSetupComplete = true;
            viewerService.modelSetupComplete({
                modelId: vm.modelId,
                leafNodes: JSON.stringify(leafNodes),
                isSetupComplete: isSetupComplete,
            });
            viewerHelperService.hideSpinner();

            loadSnapshot("ROOT");
            
            var modalShownKey = 'modalShown-' + vm.modelId;

            if (vm.isAutoPublishable && !localStorage.getItem(modalShownKey)) {
                publishPublication();
                localStorage.setItem(modalShownKey, 'true');
            }
            
        }

        function getLockedParts() {
            var lockedArray = [];
            for (var i = 0; i < nodeIdsSetsExplodeAsSingleEntity.length; i++) {
                lockedArray.push(nodeIdsSetsExplodeAsSingleEntity[i][0]);
            }
            return lockedArray;
        }

        function findEntitiesBydbId(modelTree, dbId, data) {
            for (let i = 0; i < modelTree.length; i++) {
                let x = modelTree[i];
                if (x.objectId === dbId) {
                    data.push(x);
                    return;
                }
                if (x.childParts && x.childParts.length > 0)
                    findEntitiesBydbId(x.childParts, dbId, data);
            }
        }

        function calculateSelectionIds(idArray, lockedArray) {
            { // select current node if that is sellable
                var id = idArray[0];
                var x = [];
                while(x.length === 0 && id !== 0) {
                    findEntitiesBydbId(vm.modelTree, id, x)
                    id = viewerHelperService.getParentId(id);
                }
                if(x.length > 0 && x[0].sellablePart && x[0].sparePart) {
                    return [x[0].objectId];
                }
            }
            var idsToSelect = [];
            for (var k = 0; k < idArray.length; k++) {
                var dbId = idArray[k];

                var parentId = dbId;
                while (parentId !== vm.modelTree[0].objectId) {
                    parentId = viewerHelperService.getParentId(parentId);
                    if (lockedArray.indexOf(parentId) > -1) {
                        dbId = parentId;
                    }
                }
                if (idsToSelect.indexOf(dbId) === -1) {
                    idsToSelect.push(dbId);
                }
            }
            return idsToSelect;
        }

        function getWeldmentId(dbId) {
            var lockedArray = getLockedParts();
            var parentId = dbId;
            while (parentId !== vm.modelTree[0].objectId) {
                parentId = viewerHelperService.getParentId(parentId);
                if (lockedArray.indexOf(parentId) > -1) {
                    dbId = parentId;
                }
            }
            return dbId;
        }
        var DOING_AGG_EVENT = false;
        function onSelectionChanged(data) {
            if (DOING_AGG_EVENT) {
                return true;
            }
            if (data.dbIdArray.length > 0) {
                if (isCtrlPressed && _.difference(data.dbIdArray, priorSelection).length === 1) {
                    var newObjectId = _.difference(data.dbIdArray, priorSelection);
                    var weldmentId = getWeldmentId(newObjectId);
                    if (priorSelection.indexOf(weldmentId) >= 0) {
                        priorSelection.splice(priorSelection.indexOf(weldmentId), 1);
                        viewerHelperService.selectParts(priorSelection);
                        return true;
                    }
                }

                priorSelection = viewerHelperService.getSelectedParts();
                var lockedArray = getLockedParts();
                vm.arrayOfLockedEntities = lockedArray;
                var idsToSelect = calculateSelectionIds(data.dbIdArray, lockedArray);
                var visibleNodes = viewerVariablesService.getVisibleNodes();
                var visibleIdsToSelect = _.intersection(visibleNodes, idsToSelect);
                if (_.difference(priorSelection, idsToSelect).length > 0) {
                    viewerHelperService.selectParts(visibleIdsToSelect);
                } else {
                    DOING_AGG_EVENT = true;
                    viewerApp.getCurrentViewer().clearSelection();
                    var selections = [];
                    var selection = {};
                    selection.model = data.model;
                    selection.dbIdArray = Array.isArray(visibleIdsToSelect) ? visibleIdsToSelect : [visibleIdsToSelect];
                    selections.push(selection);

                    viewerApp.getCurrentViewer().setAggregateSelection(
                        selections.map((selection) => ({
                            model: selection.model,
                            ids: selection.dbIdArray,
                            selectionType: Autodesk.Viewing.SelectionType.OVERLAYED,
                        }))
                    );

                    DOING_AGG_EVENT = false;
                    onValidatedSelectionChange(visibleIdsToSelect);
                }
            } else {
                if (viewerApp.getCurrentViewer().getSelection().length === 0) {
                    $rootScope.$broadcast("viewer-part-selected", []);
                }
            }
            return true;
        }

        $scope.$on("viewer-part-selected", function (event, partViewerDetails) {
            resetPartEdits();
            if (partViewerDetails.length === 1) {
                vm.selectedParts = [partViewerDetails[0].part];
                resetPartEdits();
            } else if (partViewerDetails.length === 0) {
                vm.selectedParts = [];
                vm.partDetailsOpen = true;
            } else if (partViewerDetails.length > 0) {
                vm.selectedParts = partViewerDetails;
                vm.partDetailsOpen = false;
            }
        });

        function onValidatedSelectionChange() {
            if (vm.isNonModeledPartsActive) {
                $timeout(function () {
                    viewerBannerService.removeNotification();
                    vm.showCreateNewPart = true;
                });
            }

            if (vm.SharingViewer !== null) {
                $timeout(function () {
                    GLOBAL_SHARING_EVENT("SELECTION_CHANGED");
                }, 700);
            }
        }

        $scope.$on("activateNonModeledParts", function (event, args) {
            //May need to remove this line
            priorSelection = [];
            vm.isNonModeledPartsActive = true;
            var idsArray = Array.isArray(args.ids) ? args.ids : [args.ids];
            viewerHelperService.selectParts(idsArray);
        });

        $scope.$on("$destroy", function () {
            shutDownViewer();
        });

        function shutDownViewer() {
            if (viewerApp && viewerApp.getCurrentViewer() != null) {
                viewerApp.getCurrentViewer().removeEventListener(Autodesk.Viewing.SELECTION_CHANGED_EVENT, onSelectionChanged);
                viewerApp.getCurrentViewer().removeEventListener(Autodesk.Viewing.GEOMETRY_LOADED_EVENT, onViewerLoadedSuccess);
                viewerApp.getCurrentViewer().removeEventListener(Autodesk.Viewing.OBJECT_TREE_CREATED_EVENT, geometobjectTreeCreated);
                viewerApp.getCurrentViewer().finish();
                Autodesk.Viewing.shutdown();
            }

            $(document).unbind("keyup");
            $(document).unbind("keydown");
        }

        function refreshSnapshotList() {
            vm.currentList = findChildSnapshots(parentId);
        }

        function findSnapshotByStateId(id) {
            return _.findWhere(vm.snapshots, { stateId: id });
        }

        function findChildSnapshots(id) {
            return _.filter(vm.snapshots, function (snapshot) {
                return snapshot.parentId === id;
            });
        }

        function transitionViewLocked(currentViewer) {
            currentViewer.navigation.setIsLocked(true);
            currentViewer.navigation.setLockSettings({
                pan: true,
                orbit: true,
                roll: true,
                fov: true,
                gotoview: true,
            }); //zoom is the only one not enabled to prevent user zooming in transition
        }

        function transitionToSnapshotViewLockSettings() {
            customsSettingsAppliedFlag = false;
            transitionViewLocked(viewerApp.getCurrentViewer());
        }

        function applyNewStateDetail(stateId) {
            var snapshot = findSnapshotByStateId(stateId);
            currentSnapshot = snapshot;
            parentId = stateId;
            viewerApp.getCurrentViewer().addEventListener(Autodesk.Viewing.FINAL_FRAME_RENDERED_CHANGED_EVENT, onFinalRender);

            var jsonState = JSON.parse(snapshot.state);

            if (snapshot.explodeAxis) {
                document.getElementById("explode_axis").value = snapshot.explodeAxis;
            }

            // Clear Section Analysis
            var sectionExt = viewer.getExtension('Autodesk.Section');
            if (sectionExt) {
                sectionExt.deactivate();
                if (sectionExt.tool) {
                    sectionExt.tool.clearSection();
                }
            }

            viewerApp.getCurrentViewer().restoreState(jsonState, true);
            viewerHelperService.isolateParts(snapshot.visibleDbIds);
            vm.steps = [];
            updateSteps(stateId);
        }

        function loadSnapshot(stateId, isSharingEvent) {
            if (vm.SharingViewer !== null && !isSharingEvent) {
                GLOBAL_SHARING_EVENT("SNAPSHOT_CHANGED", stateId);
            }

            transitionToSnapshotViewLockSettings();
            disableTranslate();
            getStateDetailPlusChildren(stateId).then(applyNewStateDetail(stateId));
            LockStateService.checkLockState(vm.modelTree);
            vm.isEntitiesLocked = LockStateService.isAnyNodeLocked(vm.modelTree[0]);
        }

        // $rootScope.$on("lock-state-updated-broadcast", function (event) {
        //     LockStateService.checkLockState(vm.modelTree);
        // });

        function updateSteps(stateId) {
            var snapshot = findSnapshotByStateId(stateId);
            if (stateId !== "ROOT") {
                vm.steps.unshift(snapshot);
                updateSteps(snapshot.parentId);
            } else {
                snapshot.stateName = RESET_TO_TOP;
                vm.steps.unshift(snapshot);
            }
        }

        var removedSnapshots = [];

        function deleteConfirmed() {
            var snapshotToDelete = removedSnapshots[0];
            fetchChildSnapshots(snapshotToDelete);

            //remove this from parents child list
            var parentSnapshot = findSnapshotByStateId(snapshotToDelete.parentId);
            var parentIndex = vm.snapshots.indexOf(parentSnapshot);
            var parentsChildIds = JSON.parse(parentSnapshot.childrenIds);
            var childIndex = parentsChildIds.indexOf(snapshotToDelete.stateId);

            var snapshotsJSON = JSON.parse(vm.snapshots[parentIndex].childrenIds);
            var childRemoved = snapshotsJSON.splice(childIndex, 1);
            vm.snapshots[parentIndex].childrenIds = JSON.stringify(snapshotsJSON);

            removeChildSnapshots();
            refreshSnapshotList();
            deleteStateDetail(snapshotToDelete.stateId);
        }

        function deleteSnapshot(id) {
            removedSnapshots = [];
            var snapshotToDelete = findSnapshotByStateId(id);
            removedSnapshots.push(snapshotToDelete);

            var confirmObject = {
                titleText: DELETE_SNAPSHOT,
                bodyText: IF_YOU_CONFIRM + ' "' + snapshotToDelete.stateName + '" ' + CHILDREN_DELETED,
            };
            $uibModal
                .open({
                    templateUrl: "features/shared/confirmationModal/confirmModal.html",
                    controller: "ConfirmModalController",
                    controllerAs: "confirmModalCtrl",
                    size: "sm",
                    resolve: {
                        confirmObject: function () {
                            return confirmObject;
                        },
                    },
                })
                .result.then(deleteConfirmed, doNothing);
        }

        function fetchChildSnapshots(snapshot) {
            var childIds = JSON.parse(snapshot.childrenIds);
            if (childIds.length > 0) {
                for (var i = 0; i < childIds.length; i++) {
                    var childSnapshot = findSnapshotByStateId(childIds[i]);

                    removedSnapshots.push(childSnapshot);

                    if (childSnapshot) {
                        fetchChildSnapshots(childSnapshot);
                    }
                }
            }
            return removedSnapshots;
        }

        function removeChildSnapshots() {
            if (removedSnapshots.length > 0) {
                for (var i = 0; i < removedSnapshots.length; i++) {
                    var index = vm.snapshots.indexOf(removedSnapshots[i]);
                    vm.snapshots.splice(index, 1);
                }
            }
        }

        function saveSnapshot() {
            takeScreenShot().then(screenShotSuccess);
        }

        function addIdToParentsChildList(childId, parentId) {
            var parentSnapshot = findSnapshotByStateId(parentId);
            var index = vm.snapshots.indexOf(parentSnapshot);
            var childIds = JSON.parse(vm.snapshots[index].childrenIds);
            childIds.push(childId);

            vm.snapshots[index].childrenIds = JSON.stringify(childIds);
            updateStateDetail(parentSnapshot);
        }

        function createStateDetail(currentState) {
            return viewerService.createStateDetail(currentState, vm.modelId);
        }

        function updateStateDetail(snapshot) {
            return viewerService.updateStateDetail(snapshot, vm.modelId);
        }

        function deleteStateDetail(id) {
            return viewerService.deleteStateDetail(id, vm.modelId);
        }

        function updateImageUrl(currentState, isOverwriteSnapshot) {
            viewerService
                .uploadThumbnailToAWS(currentState.imgUrl)
                .then(
                    (function (stateId) {
                        return function (newUrl) {
                            var snapshot = findSnapshotByStateId(stateId);
                            var index = vm.snapshots.indexOf(snapshot);
                            vm.snapshots[index].imgUrl = newUrl;
                            if (isOverwriteSnapshot) {
                                vm.steps[vm.steps.length - 1].imgUrl = snapshot.imgUrl;
                            }
                            updateStateDetail(snapshot);
                            viewerHelperService.hideSpinner();
                            if (!isSetupComplete) {
                                setupComplete();
                            }
                        };
                    })(currentState.stateId)
                )
                ["catch"](function (error) {
                    viewerHelperService.hideSpinner();
                    uploadThumbnailToAWSFailed();
                });
        }

        function screenShotSuccess(imgUrl) {
            viewerHelperService.showSpinner();
            var currentState = {};

            var explodeAxis = document.getElementById("explode_axis").value;

            currentState.childrenIds = "[]";
            currentState.state = JSON.stringify(viewerApp.getCurrentViewer().getState());

            currentState.imgUrl = imgUrl;
            currentState.viewableId = vm.viewableId;

            currentState.explodeAxis = explodeAxis;

            if (isOverwriteSnapshot) {
                var snapshot = findSnapshotByStateId(parentId);
                currentState.id = snapshot.id ? snapshot.id : "";
                currentState.stateId = parentId;
                currentState.childrenIds = snapshot.childrenIds;
                currentState.visibleDbIds = JSON.stringify(viewerVariablesService.getVisibleNodes());
                currentState.stateName = snapshot.stateName;
                currentState.parentId = snapshot.parentId;
                currentState.notes = snapshot.notes ? snapshot.notes : "";
                var index = vm.snapshots.findIndex(function (obj) {
                    return obj.stateId === parentId;
                });
                vm.snapshots.splice(index, 1, currentState);
                updateStateDetail(currentState).then(function () {
                    updateImageUrl(currentState, true);
                });
                isOverwriteSnapshot = false;
            } else {
                currentState.stateId = isSetupComplete ? viewerService.guid() : "ROOT";
                currentState.visibleDbIds = JSON.stringify(viewerVariablesService.getVisibleNodes());
                currentState.stateName = currentState.stateId;
                currentState.parentId = isSetupComplete ? parentId : "NOT_ROOT";
                currentState.notes = "";
                currentState.viewableId = vm.viewableId;
                vm.snapshots.push(currentState);
                if (isSetupComplete) {
                    addIdToParentsChildList(currentState.stateId, parentId);
                }
                createStateDetail(currentState).then(function (resp) {
                    currentState.id = resp.data;
                    updateImageUrl(currentState, false);
                });
            }

            refreshSnapshotList();
        }

        function uploadThumbnailToAWSFailed(error) {
            console.error("Upload of thumbnail to aws failed!");
        }

        function takeScreenShot() {
            return $q(function (resolve, reject) {
                viewerHelperService.selectParts([]);

                $timeout(function () {
                    var viewer = viewerApp.getCurrentViewer();
                    viewer.getScreenShot(400, 400, resolve);
                }, 100);
            });
        }

        function editSnapshotName(stateId) {
            var snapshot = findSnapshotByStateId(stateId);
            var index = vm.snapshots.indexOf(snapshot);
            var name = vm.snapshots[index].stateName ? vm.snapshots[index].stateName : "";
            var notes = vm.snapshots[index].notes ? vm.snapshots[index].notes : "";
            var snapshotDetails = { name: name, notes: notes };
            var modalInstance = $uibModal.open({
                templateUrl: "features/viewer/extensions/editNameAndNotes/editNameAndNotesModal.html",
                controller: "EditNameAndNotesModalController as editNameAndNotesCtrl",
                resolve: {
                    snapshotDetails: function () {
                        return snapshotDetails;
                    },
                },
            });

            modalInstance.result.then(function (newSnapshotDetails) {
                if (newSnapshotDetails.name !== "" && newSnapshotDetails.name !== undefined) {
                    var snapshot = findSnapshotByStateId(stateId);
                    var index = vm.snapshots.indexOf(snapshot);
                    vm.snapshots[index].stateName = newSnapshotDetails.name;
                    vm.snapshots[index].notes = newSnapshotDetails.notes ? newSnapshotDetails.notes : "";
                    refreshSnapshotList();
                    updateStateDetail(vm.snapshots[index]);
                }
            });
        }

        function toggleSelectionWindow() {
            selectionWindowActive = !selectionWindowActive;
            refreshFragProxyLocation();

            //If active turn on the selection extension without S key press
            if (selectionWindowActive) {
                activateSelectionWindow();
            } else {
                //If inactive implement same functionality as pressing Q to prevent selection
                disableSelectionWindow();
            }
        }

        function refreshFragProxyLocation() {
            calculateFragBounding(viewerApp.getCurrentViewer());
        }

        function startNonModeledPartsFlow() {
            disableTranslate();
            deactivateExplodeTool();

            viewerHelperService.selectParts([]);
            viewerBannerService.setNotification("INFO", SELECT_PART_ASSOCIATED);

            $scope.$digest();
            vm.isNonModeledPartsActive = true;
        }

        function hideCreateNewPart() {
            viewerHelperService.selectParts([]);
            vm.showCreateNewPart = false;
            vm.isNonModeledPartsActive = false;
        }

        function isCreateNewPartVisible() {
            return vm.showCreateNewPart;
        }

        function updateNonModeledParts() {
            $timeout(function () {
                var selectedPart = viewerApp.getCurrentViewer().getSelection()[0];
                viewerService.addPartToNonModeledParts(vm.createdParts, selectedPart, vm.modelId).then(function (response) {
                    viewerHelperService.selectParts([]);
                    if (response.data) {
                        viewerBannerService.setNotification("SUCCESS", "NON_MODEL_SUCCESS", 2000);
                        hideCreateNewPart();
                        if (vm.createdParts && vm.createdParts.length !== 0) {
                            $rootScope.$broadcast("non-modeled-part-added", selectedPart);
                        } else {
                            $rootScope.$broadcast("non-modeled-part-deleted", selectedPart);
                        }
                    } else {
                        addNonModeledPartFailed();
                    }
                }, addNonModeledPartFailed);
            }, 1);
        }

        function addNonModeledPartFailed() {
            viewerBannerService.setNotification("WARN", WENT_WRONG, 5000);
            console.error("Failed to create non modeled parts. ");
            hideCreateNewPart();
        }

        function getNonModeledParts() {
            var selectedPart = viewerApp.getCurrentViewer().getSelection();
            return viewerService.getNonModeledParts(selectedPart, vm.modelId);
        }

        //called from cadshare_extension
        function weldmentsUpdated(weldmentArray) {
            var lockedItems = [];
            for (var x = 0; x < weldmentArray.length; x++) {
                var nonArrayId = weldmentArray[x][0];
                lockedItems.push(nonArrayId);
            }
            customWeldments = lockedItems;
            $rootScope.$broadcast("locked-model-tree-updated", lockedItems);
        }

        function openCustomerPreview() {
            var openUrlNW = $state.href(
                "customerViewer",
                {
                    machineName: $stateParams.machineName,
                    autodeskURN: $stateParams.autodeskURN,
                    viewableName: $stateParams.viewableName,
                    translateType: $stateParams.translateType,
                    modelId: vm.modelId,
                    manualId: null,
                    productId: $stateParams.productId,
                },
                { absolute: true }
            );
            $window.open(openUrlNW, "_blank");
        }

        function disableTranslate() {
            var txTool = _translateTool.getName();
            viewer.toolController.deactivateTool(txTool);
            translateToolBtn.removeClass("translate-tool-active");
            translateToolBtn.removeClass("active");
            translateToolBtn.addClass("translate-tool");
        }

        function toggleLinkPartFlow() {
            viewerHelperService.selectParts([]);

            disableTranslate();
            deactivateExplodeTool();

            if (!vm.isLinkModelPartActive) {
                viewerBannerService.setNotification("INFO", SELECT_LINK);
                $scope.$digest();
                vm.isLinkModelPartActive = true;
                vm.isNonModeledPartsActive = false;
            } else {
                viewerBannerService.removeNotification();
                vm.isLinkModelPartActive = false;
                vm.isNonModeledPartsActive = false;
            }
        }

        function overwriteSnapshot($event) {
            $event.stopPropagation();
            var snapshot = findSnapshotByStateId(parentId);
            var snapshotName = snapshot.stateName;
            var confirmObject = {
                titleText: OVERWRITE,
                bodyText: IF_CONFIRM + ' "' + snapshotName + '" ' + WILL_BE_OVERWRITTEN,
            };
            $uibModal
                .open({
                    templateUrl: "features/shared/confirmationModal/confirmModal.html",
                    controller: "ConfirmModalController",
                    controllerAs: "confirmModalCtrl",
                    size: "sm",
                    resolve: {
                        confirmObject: function () {
                            return confirmObject;
                        },
                    },
                })
                .result.then(overwriteConfirmed, doNothing);
        }

        function showSpinner() {
            viewerHelperService.showSpinner();
        }

        function hideSpinner() {
            viewerHelperService.hideSpinner();
        }

        function doNothing() {
            //do nothing
        }

        function overwriteConfirmed() {
            isOverwriteSnapshot = true;
            takeScreenShot().then(screenShotSuccess);
        }

        $scope.$on("viewer-settings-changed", function (event, viewerSettings) {
            vm.viewerSettings = viewerSettings;
            viewerHelperService.applyCustomViewerSettings(viewer, vm.viewerSettings);
        });

        function setPartDetailsOpen(value) {
            if (value && vm.selectedParts.length > 0 && vm.selectedParts[0]) {
                console.log("Part details are being opened.");
                resetPartEdits();
                vm.partDetailsOpen = true;
            } else {
                console.log("Part details are being closed.");
                vm.partDetailsOpen = false;
            }
        }

        function resetPartEdits() {
            if (vm.selectedParts[0] && isPartChanges()) {
                vm.partEdits.partNumber = vm.selectedParts[0].partNumber;
                vm.partEdits.partDescription = vm.selectedParts[0].partDescription;
                vm.partEdits.sellPrice = vm.selectedParts[0].sellPrice;
                vm.partEdits.masterPartFound = vm.selectedParts[0].masterPartFound;
                vm.partEdits.alternatePartNumber = vm.selectedParts[0].alternatePartNumber;
                vm.partEdits.inSupersession = vm.selectedParts[0].inSupersession;
                vm.partEdits.supersessionPartNumber = vm.selectedParts[0].supersessionPartNumber;
            }
        }

        function savePartEdits() {
            vm.isPartsListLoading = true;
            vm.selectedParts[0].partNumber = vm.partEdits.partNumber;
            vm.selectedParts[0].partDescription = vm.partEdits.partDescription;
            vm.selectedParts[0].sellPrice = vm.partEdits.sellPrice;
            var partList = vm.selectedParts;
            viewerService.updateParts(partList).then(updatePartSuccess, updatePartFailed);
        }

        function updatePartSuccess() {
            viewerService.getPartViewerDetails(vm.modelId, priorSelection).then(response => {
                var partList = response.data.part;
                partList.inSupersession = !!partList.supersessionPartNumber;
                vm.selectedParts = [partList];
                $scope.$broadcast("viewer-part-selected", response.data.part);
                vm.isPartsListLoading = false;
            }, error => {
                console.error("Failed to retrieve part viewer details:", error);
                vm.isPartsListLoading = false;
            });
        }

        function updatePartFailed() {
            viewerBannerService.setNotification("ERROR", PART_EDIT_FAIL, 2000);
        }

        function isNotNullOrEmpty(value) {
            return value !== null && value !== "" && value !== undefined;
        }

        function isPartChanges() {
            if (!vm.selectedParts[0]) {
                return false;
            }

            var partNumberIsChanged =
                vm.selectedParts[0].partNumber !== vm.partEdits.partNumber &&
                (isNotNullOrEmpty(vm.selectedParts[0].partNumber) || isNotNullOrEmpty(vm.partEdits.partNumber));
            var partDescriptionIsChanged =
                vm.selectedParts[0].partDescription !== vm.partEdits.partDescription &&
                (isNotNullOrEmpty(vm.selectedParts[0].partDescription) || isNotNullOrEmpty(vm.partEdits.partDescription));
            var sellPriceIsChanged =
                vm.selectedParts[0].sellPrice !== vm.partEdits.sellPrice &&
                (isNotNullOrEmpty(vm.selectedParts[0].sellPrice) || isNotNullOrEmpty(vm.partEdits.sellPrice));
            var inSupersessionIsChanged =
                vm.selectedParts[0].inSupersession !== vm.partEdits.inSupersession &&
                (isNotNullOrEmpty(vm.selectedParts[0].inSupersession) || isNotNullOrEmpty(vm.partEdits.inSupersession));

            return partNumberIsChanged || partDescriptionIsChanged || sellPriceIsChanged || inSupersessionIsChanged;
        }

        function onKeyUp(evt) {
            isCtrlPressed = evt.keyCode === 17 ? false : isCtrlPressed;
        }

        function onKeyDown(evt) {
            isCtrlPressed = evt.keyCode === 17 ? true : isCtrlPressed;
        }

        $scope.$on("side-menu-minimized", function () {
            vm.isSideMenuOpen = false;
            $timeout(function () {
                viewer.resize();
            });
        });

        $scope.$on("side-menu-maximized", function () {
            vm.isSideMenuOpen = true;
            $timeout(function () {
                viewer.resize();
            });
        });

        function getVisibleIds() {
            return JSON.stringify(viewerVariablesService.getVisibleNodes());
        }

        function remoteUpdateViewer(state, ids) {
            // viewerApp.getCurrentViewer().restoreState(state, true);
            viewerApp.getCurrentViewer().restoreState(state, true);
            if (ids) {
                var parsedIds = JSON.parse(ids);
                showHideNodes(parsedIds);
            }
        }

        function showHideNodes(visibleDbIds) {
            if (visibleDbIds && visibleDbIds.length > 0) {
                viewerHelperService.hideParts(viewerHelperService.getPartTreeTopId);
                viewerHelperService.showParts(visibleDbIds);
            } else {
                theViewer.showAll();
            }
        }

        function publishPublication() {
            var viewableId = vm.modelId;
            $uibModal.open({
                templateUrl: 'features/viewer/manufacturerViewer/publishPublicationModal/publishPublication.html',
                controller: 'PublishPublicationController',
                controllerAs: 'publishPublicationCtrl',
                size: 'lg',
                resolve: {
                    manufacturerId: function () {
                        return userService.getManufacturerId();
                    },
                    viewableId: function () {
                        return viewableId;
                    }
                }
            }).result.then(function () {
                headerBannerService.setNotification('SUCCESS', PUBLICATION_SUCCESS, 5000);
            }, function () {
                console.log('Modal Cancelled');
            });
        }
    }
})();
