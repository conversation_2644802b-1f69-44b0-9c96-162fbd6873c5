(function () {
    'use strict';

    angular
        .module('app.shared')
        .controller('EditUserController', EditUserController);

    EditUserController.$inject = ['createUserService', '$uibModalInstance', 'createObject', 'userService', 'dpUserService', 'dpCreateUserService', '$translate'];

    function EditUserController(createUserService, $uibModalInstance, createObject, userService, dpUserService, dpCreateUserService, $translate) {
        var vm = this;

        vm.type = createObject.type;
        vm.permissions = {};
        vm.dashboardEnabled = userService.getDashboardEnabled();
        vm.isDealerPlus = userService.isDealerPlusUser();
        vm.isManufacturer = userService.isManufacturer();

        vm.cancel = $uibModalInstance.dismiss;
        vm.editUser = editUser;
        vm.isDealer = createObject.type === 'DEALER';
        vm.isSupreme = userService.isSupreme();
        initialize();

        var SELECT_CHECKBOX;
        $translate(['CREATE_USER.SELECT_CHECKBOX'])
            .then(function (resp) {
                SELECT_CHECKBOX = resp["CREATE_USER.SELECT_CHECKBOX"];
            });

        function initialize() {
            vm.firstName = createObject.firstName;
            vm.lastName = createObject.lastName;
            vm.emailAddress = createObject.emailAddress;
            vm.visContactId = createObject.visContactId;
            if (createObject.type === "Internal") {
                vm.permissions.orders = createObject.permissionsArray.indexOf('Order') > -1;
                vm.permissions.products = createObject.permissionsArray.indexOf('Products') > -1;
                vm.permissions.publications = createObject.permissionsArray.indexOf('Publication') > -1;
                vm.permissions.parts = createObject.permissionsArray.indexOf('Parts') > -1;
                vm.permissions.customers = createObject.permissionsArray.indexOf('Customer') > -1;
                vm.permissions.admin = createObject.permissionsArray.indexOf('Admin') > -1;
                vm.permissions.security = createObject.permissionsArray.indexOf('Security') > -1;
                vm.permissions.dashboard = createObject.permissionsArray.indexOf('Dashboard') > -1;

                vm.isDiscountEditable = !createObject.userSettings.disableDiscountEditing;
            } else {
                vm.permissions.orders = createObject.permissionsArray.indexOf('Order') > -1;
                vm.permissions.publishedProducts = createObject.permissionsArray.indexOf('PublishedProducts') > -1;
                vm.permissions.partSearch = createObject.permissionsArray.indexOf('PartSearch') > -1;
                checkPartSearchEnabled();
            }
        }

        function checkPartSearchEnabled() {
            if (createObject.type !== 'Internal') {
                if (vm.isManufacturer) {
                    userService.getManufacturerSubEntitySettings(createObject.manufactuerSubEntityId)
                        .then(getManufacturerSubEntitySettingsSuccess);
                } else if (vm.isDealerPlus) {
                    dpUserService.getManufacturerSubEntitySettings(createObject.manufactuerSubEntityId)
                        .then(getManufacturerSubEntitySettingsSuccess);
                }
            }
        }

        function getManufacturerSubEntitySettingsSuccess(response) {
            vm.hasPartSearchEnabled = response.data.partSearchEnabled;
        }

        function editUser() {
            vm.hasErrorMessage = false;
            vm.isDisabled = true;
            var userObject = {
                emailAddress: vm.emailAddress,
                firstName: vm.firstName,
                lastName: vm.lastName,
                active: createObject.active,
                userStatus: createObject.userStatus,
                permissionsArray: createPermissionArray(),
                userSettings: createUserSettings(),
                visContactId: vm.visContactId
            };
            if (userObject.permissionsArray.length > 0) {
                if (createObject.type === 'Internal') {
                    var manufacturerId = userService.getManufacturerId();
                    createUserService.editManufacturerUser(userObject, manufacturerId, createObject.userId)
                        .then(createUserSuccess, createUserFailed);
                } else if (createObject.type === 'DEALER') {
                    if (vm.isDealerPlus) {
                        dpCreateUserService.editDealerUser(userObject, createObject.manufactuerSubEntityId, createObject.userId)
                            .then(createUserSuccess, createUserFailed);
                    } else {
                        createUserService.editDealerUser(userObject, createObject.manufactuerSubEntityId, createObject.userId)
                            .then(createUserSuccess, createUserFailed);
                    }
                } else {
                    if (vm.isDealerPlus) {
                        dpCreateUserService.editCustomerUser(userObject, createObject.manufactuerSubEntityId, createObject.userId)
                            .then(createUserSuccess, createUserFailed);
                    } else {
                        createUserService.editCustomerUser(userObject, createObject.manufactuerSubEntityId, createObject.userId)
                            .then(createUserSuccess, createUserFailed);
                    }
                }
            } else {
                vm.errorMessage = SELECT_CHECKBOX;
                vm.hasErrorMessage = true;
                vm.isDisabled = false;
            }
        }

        function createUserSuccess() {
            $uibModalInstance.close();
        }

        function createUserFailed(response) {
            vm.isDisabled = false;
            vm.hasErrorMessage = true;
            vm.errorMessage = response.data.message;
        }

        function createPermissionArray() {
            var permissionsArray = [];
            if (vm.permissions.orders) {
                permissionsArray.push('Order');
            }
            if (vm.permissions.products) {
                permissionsArray.push('Products');
            }
            if (vm.permissions.publications) {
                permissionsArray.push('Publication');
            }
            if (vm.permissions.parts) {
                permissionsArray.push('Parts');
            }
            if (vm.permissions.customers) {
                permissionsArray.push('Customer');
            }
            if (vm.permissions.admin) {
                permissionsArray.push('Admin');
            }
            if (vm.permissions.security) {
                permissionsArray.push('Security');
            }
            if (vm.permissions.dashboard) {
                permissionsArray.push('Dashboard');
            }
            if (vm.permissions.publishedProducts) {
                permissionsArray.push('PublishedProducts');
            }
            if (vm.permissions.partSearch) {
                permissionsArray.push('PartSearch');
            }
            return permissionsArray;
        }

        function createUserSettings() {
            var userSettings = {disableDiscountEditing : !vm.isDiscountEditable};
            return userSettings;
        }

    }
})();