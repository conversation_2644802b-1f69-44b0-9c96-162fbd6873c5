(function () {
    'use strict';

    angular
        .module('app.orders')
        .controller('QuotationController', QuotationController);

    QuotationController.$inject = ['ordersService', '$rootScope', '$scope', '$controller', 'userService', '$state', 'headerBannerService', '$uibModal', '$translate'];

    function QuotationController(ordersService, $rootScope, $scope, $controller, userService, $state, headerBannerService, $uibModal, $translate) {
        var vm = this;

        angular.extend(vm, $controller('OrderController', {$scope: $scope}));

        vm.isManufacturer = userService.isManufacturer();

        vm.nextStep = nextStep;
        vm.goToSplitOrder = goToSplitOrder;
        vm.openComment = openComment;
        vm.reloadOrder = reloadOrder;
        vm.altStep = altStep;
        vm.archiveOrder = archiveOrder;
        vm.duplicateOrder = duplicateOrder;
        vm.onlyKitsPresent = onlyKitsPresent;
        vm.hasVisibleDiscountedPrices = hasVisibleDiscountedPrices;
        vm.isFarmer = userService.isFarmer();
        vm.previewPricingEnabled = userService.getPreviewPricingEnabled();

        var WENT_WRONG, PLACE_ORDER, QUOTE_SENT, ENTER_PO, QUOTATION_SUCCESS, CONFIRM_ORDER_BANNER, CONFIRM_ORDER_CLOSED, ARCHIVE_ORDER, CONFIRM_ORDER_ARCHIVED, ARCHIVE_MODAL, CONFIRM_ORDER_BANNER;
        $translate(['GENERAL.WENT_WRONG', 'QUOTATION.PLACE_ORDER', 'QUOTATION.QUOTE_SENT', 'QUOTATION.ENTER_PO', 'QUOTATION.QUOTATION_SUCCESS', 'QUOTATION.UPDATE_QUOTE', 'ORDERS.QUOTE', 'ORDER.CONFIRM_ORDER_ARCHIVED', 'ORDER.CONFIRM_ORDER_CLOSED', 'ORDER.ARCHIVE_ORDER', 'ORDERS.CONFIRM_ORDER_ARCHIVED', 'ORDERS.ARCHIVE_MODAL', 'ORDERS.CONFIRM_ORDER_BANNER' ])
            .then(function (resp) {
                WENT_WRONG = resp["GENERAL.WENT_WRONG"];
                PLACE_ORDER = resp["QUOTATION.PLACE_ORDER"];
                QUOTE_SENT = resp["QUOTATION.QUOTE_SENT"];
                ENTER_PO = resp["QUOTATION.ENTER_PO"];
                QUOTATION_SUCCESS = resp["QUOTATION.QUOTATION_SUCCESS"];
                CONFIRM_ORDER_ARCHIVED = resp["ORDERS.CONFIRM_ORDER_ARCHIVED"];
                ARCHIVE_MODAL = resp["ORDERS.ARCHIVE_MODAL"];
                CONFIRM_ORDER_BANNER = resp["ORDERS.CONFIRM_ORDER_BANNER"];
                CONFIRM_ORDER_CLOSED = resp["ORDER.CONFIRM_ORDER_CLOSED"];
                ARCHIVE_ORDER = resp["ORDER.ARCHIVE_ORDER"];
                vm.altStepText = resp["QUOTATION.UPDATE_QUOTE"];
                vm.STAGE = resp["ORDERS.QUOTE"];

                vm.orderStatusPill = QUOTE_SENT;
                if (!vm.isManufacturer) {
                    vm.nextStepText = PLACE_ORDER;
                }
            });


        initialize();

        function initialize() {
            if (vm.isManufacturer && 'QUOTE') {
                vm.showPurchaseOrder = false;
                vm.isArchiveActive = true;
            } else {
                vm.showPurchaseOrder = true;
                vm.isArchiveActive = false;
            }
            vm.hidePartSearch = true;
            vm.showPartDescription = false;
            vm.showModelName = false;
            vm.showProductName = false;
            vm.isDetailsEditable = false;
            vm.isPriceEditable = false;
            vm.showAltStepBtn = false;
            vm.showEstimatedDelivery = false;
            vm.isNotesEditable = false;
            vm.displayNotes = false;
            vm.isDiscountEditable = false;
            vm.isDiscountVisible = false;
            vm.isDiscountEnabled = false;
            vm.exportPartDataCsvVisible = true;
            vm.showSplitOrderBtn = false;
            vm.splitOrders = [];
            vm.showCancelOrderButton = true;

            if (vm.isManufacturer) {
                vm.showAltStepBtn = true;
                vm.showNextStep = false;
                vm.isActionActive = false;
                vm.isPurchaseOrderEditable = false;
                vm.deleteOrderItem = true;
                vm.isShippingEditable = true;
                vm.isQuantityEditable = true;
            } else {
                vm.showNextStep = true;
                vm.isActionActive = true;
                vm.isPurchaseOrderEditable = true;
                vm.deleteOrderItem = false;
                vm.isShippingEditable = false;
                vm.showShippingPriceDisclaimer = true;
            }

            getOrder()
        }

        function getOrder() {
            ordersService.getOrder(vm.orderId)
                .then(getOrdersSuccess, serviceCallFailed);
        }

        function getOrdersSuccess(response) {

            $rootScope.$broadcast("Update-Unread-Order-Tabs");

            if (response.data.orderStatus === 'QUOTE') {
                vm.data = response.data;
                vm.orderLists = response.data.orderItems;
                vm.totalItems = vm.orderLists.length;
                vm.billingAddress = vm.createReadableAddress(response.data.billingAddress);
                vm.shippingAddress = vm.createReadableAddress(response.data.shippingAddress);
                vm.data.originalShippingPrice = vm.data.shippingPrice;
                vm.data.shippingPrice = vm.data.shippingPrice ? vm.data.shippingPrice : 0;
                vm.isOrderItemsLoaded = true;
                vm.displayNotes = vm.data.notes !== null && vm.data.notes !== undefined;
                vm.displayWarehouseName = vm.data.warehouseName !== null && vm.data.warehouseName !== undefined;
                vm.parentOrderId = vm.data.parentOrderId;
                vm.parentCustomOrderDisplay = vm.data.parentCustomOrderDisplay;
                vm.associatedOrderId = vm.data.associatedOrderId;
                vm.associatedCustomOrderDisplay = vm.data.associatedCustomOrderDisplay;
                vm.showERPRefVisible = userService.isManufacturer() && vm.data.visSalesOrderNoX != null;
                vm.selectedCurrency = userService.getCurrencyData(vm.data.currency);
                vm.isPartInformationAvailable = isPartInformationAvailable;
                fetchShippingRequirements();
                if (vm.data.percentageDiscount > 0) {
                    vm.isDiscountVisible = true;
                }


                var manufacturerSubEntityId = vm.data.manufacturerSubEntityId;
                vm.orderLists.forEach(function (item) {
                    if (item.kitId) {
                        var id = vm.isManufacturer ? userService.getManufacturerId() : manufacturerSubEntityId;
                        ordersService.getKit(id, item.kitId, vm.isManufacturer)
                            .then(kitResponse => {
                                item.kitDetails = kitResponse;
                            });
                    }
                });

                vm.updateItemTotals(true, this, true);
                calculateSplitOrders();
                vm.isPreviewStockLevelEnabled = userService.getPreviewStockLevelEnabled();
                vm.isStockWarehousesEnabled = userService.getStockWarehousesEnabled();
            } else {
                vm.redirectToOrder(response.data.orderId, response.data.orderStatus);
            }
        }

        function serviceCallFailed(error) {
            vm.isOrderItemsLoaded = true;
            headerBannerService.setNotification('ERROR', error.data.error, 10000);
        }

        function nextStep() {
            if (vm.data.purchaseOrder === null || vm.data.purchaseOrder === '' || vm.data.purchaseOrder === undefined) {
                headerBannerService.setNotification('WARN', ENTER_PO, 10000);
            } else {
                vm.data.orderStatus = "PROCESSED";
                vm.data.price = vm.total;
                delete vm.data.associatedCustomOrderDisplay;
                ordersService.updateOrder(vm.data)
                    .then(updateOrderSuccess, updateOrderFailed);
            }
        }

        function updateOrderSuccess() {
            headerBannerService.setNotification('SUCCESS', QUOTATION_SUCCESS, 10000);
            var viewState = 'orders.liveorder';

            $state.go(viewState, {
                orderId: vm.data.orderId
            });
        }

        function updateOrderFailed() {
            headerBannerService.setNotification('ERROR', WENT_WRONG, 10000);
        }


        function goToSplitOrder(splitOrderId) {
            $state.go('orders.enquiry', {orderId: splitOrderId});
        }

        function calculateSplitOrders() {
            vm.splitOrders = [];
            if (vm.orderLists) {
                for (var i = 0; i < vm.orderLists.length; i++) {
                    if (vm.orderLists[i].splitOrderIds && vm.orderLists[i].splitOrderIds.length > 0) {
                        for (var j = 0; j < vm.orderLists[i].splitOrderIds.length; j++) {
                            if (!_.findWhere(vm.splitOrders, {orderId: vm.orderLists[i].splitOrderIds[j]})) {
                                var splitOrder = {
                                    orderId: vm.orderLists[i].splitOrderIds[j],
                                    displayId: vm.orderLists[i].splitOrderCustomDisplayNumbers[j] ? vm.orderLists[i].splitOrderCustomDisplayNumbers[j] : vm.orderLists[i].splitOrderIds[j]
                                }
                                vm.splitOrders.push(splitOrder);
                            }
                        }
                    }
                }
            }
            if (vm.additionalParts) {
                for (var k = 0; k < vm.additionalParts.length; k++) {
                    if (vm.additionalParts[k].splitOrderIds && vm.additionalParts[k].splitOrderIds.length > 0) {
                        for (var l = 0; l < vm.additionalParts[k].splitOrderIds.length; l++) {
                            if (!_.findWhere(vm.splitOrders, {orderId: vm.additionalParts[k].splitOrderIds[l]})) {
                                var splitOrder = {
                                    orderId: vm.additionalParts[k].splitOrderIds[l],
                                    displayId: vm.additionalParts[k].splitOrderCustomDisplayNumbers[l] ? vm.additionalParts[k].splitOrderCustomDisplayNumbers[l] : vm.additionalParts[k].splitOrderIds[l]
                                }
                                vm.splitOrders.push(splitOrder);
                            }
                        }
                    }
                }
            }

        }


        function openComment(commentThread, orderItemId) {
            var commentObject = {
                threadId: commentThread,
                orderId: vm.orderId,
                orderItemId: orderItemId
            };

            $uibModal.open({
                templateUrl: 'features/shared/comments/comments.html',
                controller: 'CommentController',
                controllerAs: 'commentsCtrl',
                resolve: {
                    commentObject: function () {
                        return commentObject;
                    }
                }
            }).result.then(function (commentObject) {

                if (commentObject.orderItemId) {
                    var index = _.findIndex(vm.data.orderItems, {orderItemId: commentObject.orderItemId});
                    vm.data.orderItems[index].commentThread = {};
                    vm.data.orderItems[index].commentThread.orderItemId = commentObject.orderItemId;
                    vm.data.orderItems[index].commentThread.id = commentObject.threadId;
                    vm.data.orderItems[index].commentThread.orderId = commentObject.orderId;
                } else {
                    vm.data.commentThread = {};
                    vm.data.commentThread.id = commentObject.threadId;
                    vm.data.commentThread.orderId = commentObject.orderId;
                }
            }, function () {
                console.log('Modal Cancelled');
            });

        }

        function reloadOrder() {
            getOrder();
        }

        function altStep() {
            updateData();
            updateOrder();
        }

        function updateData() {
            vm.data.price = vm.total;
            vm.data.additionalParts = vm.additionalParts;
            vm.data.currency = vm.selectedCurrency;
            delete vm.data.associatedCustomOrderDisplay;
            if (vm.isCustDetailEdit) {
                vm.data.contactName = vm.newContactName;
                vm.data.deliveryName = vm.deliveryName;
                vm.data.contactNumber = vm.newContactNumber;
                vm.data.deliveryNumber = vm.newDeliveryNumber;
                vm.data.shippingAddress = JSON.parse(vm.newDeliveryAddress);
                vm.data.billingAddress = JSON.parse(vm.newBillingAddress);
            }
        }

        function updateOrder() {
            ordersService.updateOrder(vm.data)
                .then(updateOrderSuccessful, updateOrderFailed);
        }

        function updateOrderSuccessful() {
            headerBannerService.setNotification('SUCCESS', QUOTATION_SUCCESS, 10000);
            vm.isOrderEdited = false;
            getOrder();
        }

        $scope.$on("Order-Edited", orderEdited);

        function orderEdited() {
            vm.isOrderEdited = true;
        }

        function isPartInformationAvailable(item) {
            var partInformation = {
                showPartDescription: item.partDescription,
                showModelName: item.modelName,
                showMachineName: item.machineName
            };
            vm.showPartDescription = partInformation.showPartDescription;
            vm.showModelName = partInformation.showModelName;
            vm.showMachineName = partInformation.showMachineName;
            return partInformation;
        }

        function fetchShippingRequirements() {
            if (!vm.isFarmer) {
                ordersService.getShippingRequirements(vm.data.manufacturerSubEntityId).then(function(response) {
                    var shippingRequirements = response.data.shippingRequirements;
                    vm.orderShippingRequirement = shippingRequirements.find(req => req.shippingRequirementId === vm.data.shippingRequirementId);
                    vm.isShippingRequirementsValid = !!vm.orderShippingRequirement && !!vm.orderShippingRequirement.preferredCourier && !!vm.orderShippingRequirement.courierNumber;
                }, function(error) {
                    console.error("Error fetching shipping requirements:", error);
                    vm.isShippingRequirementsValid = false;
                });
              }
            }

        function archiveOrder() {
            var confirmObject = {
                titleText: CONFIRM_ORDER_ARCHIVED,
                bodyText: ARCHIVE_MODAL
            };

            $uibModal.open({
                templateUrl: 'features/shared/confirmationModal/confirmModal.html',
                controller: 'ConfirmModalController',
                controllerAs: 'confirmModalCtrl',
                size: 'sm',
                resolve: {
                    confirmObject: function () {
                        return confirmObject;
                    }
                }
            }).result.then(function () {
                vm.data.orderStatus = "CLOSED";
                ordersService.archiveOrder(vm.orderId)
                    .then(function () {
                        headerBannerService.setNotification('SUCCESS', CONFIRM_ORDER_BANNER, 2000);
                        $state.go('orders.historicalorders');
                    });
            }, function () {
                console.log('Modal Cancelled');
            });
        }

        function duplicateOrder() {
            ordersService.duplicateOrder(vm.orderId)
                .then(function (response) {
                    var duplicatedOrder = response.data;
                    $state.go('create', {
                        orderDetails: duplicatedOrder
                    });
                }, function (error) {
                    console.error('Failed to duplicate order:', error);
                });
        }

        function onlyKitsPresent() {
            if (!vm.data || !vm.data.orderItems) {
                return false;
            }
            return vm.data.orderItems.every(item => item.kitId && !item.partId);
        }

        function hasVisibleDiscountedPrices() {

            if (!vm.data || !vm.data.orderItems) {
                return false;
            }

            var orderItems = vm.data.orderItems;

            var hasDiscountedPrices = orderItems.some(item => item.discountedPrice && item.discountedPrice !== 0);

            return hasDiscountedPrices;
        }

    }
})();
