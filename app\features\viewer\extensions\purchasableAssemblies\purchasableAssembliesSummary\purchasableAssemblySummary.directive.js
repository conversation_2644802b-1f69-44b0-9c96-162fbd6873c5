(function () {
    'use strict';

    angular
        .module('app.viewer')
        .directive('purchasableAssemblySummary', purchasableAssemblySummary);

    function purchasableAssemblySummary() {
        var directive = {
            restrict: 'E',
            templateUrl: 'features/viewer/extensions/purchasableAssemblies/purchasableAssembliesSummary/purchasableAssemblySummary.html',
            controller: 'PurchasableAssemblySummaryController',
            controllerAs: 'purchasableAssemblySummaryCtrl',
            bindToController: true
        };
        return directive;
    }

})();