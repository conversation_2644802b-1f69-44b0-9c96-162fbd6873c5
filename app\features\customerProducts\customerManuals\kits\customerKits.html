<div ng-repeat="kit in customerKitsCtrl.kitList">

    <uib-accordion>
        <uib-accordion-group is-open="kit.status" class="kit-panel">
            <uib-accordion-heading>

            <div class="kit-container card">
                <div class="card-header py-0 d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">{{kit.title}} <span class="badge badge-pill badge-primary">{{kit.parts.length}}
                            {{'CUST_KITS.PARTS' | translate}}</span></h4>
                    <a href="javascript:void(0);" ng-click="kit.status = !kit.status" class="p-3 toggle-icon" ng-disabled="kit.clicked">
                            <i ng-click="kit.status = !kit.status" ng-class="{'fa fa-angle-up': kit.status, 'fa fa-angle-down': !kit.status}"></i>
                    </a>
                </div>
                <div class="p-3">
                    <div class="row">
                        <div class="col-md-9">
                            <h2 class="text-dark mb-2">
                                {{kit.masterPartNumber}}
                                <span ng-if="customerKitsCtrl.isStockWarehousesEnabled" ng-class="{'text-success': kit.stock >= 3, 'text-warning': kit.stock < 3 && kit.stock > 0, 'text-danger': kit.stock === null || kit.stock < 1}">
                                {{'BUY_KIT.STOCK' | translate}} {{kit.stock || 0}}
                                </span>
                                <span ng-if="!customerKitsCtrl.isStockWarehousesEnabled" ng-class="{'text-success': kit.stock >= 3, 'text-warning': kit.stock < 3 && kit.stock > 0, 'text-danger': kit.stock === null || kit.stock < 1}">
                                    <span title="{{'ORDER.IN_STOCK' | translate}}" tooltip-trigger="outsideClick"
                                        uib-tooltip="{{'ORDER.IN_STOCK' | translate}}" class="success-alert" ng-if="kit.stock >= 3">
                                        <i class="fas fa-layer-group text-success pointer"></i>
                                    </span>
                                    <span title="{{'ORDER.LOW_STOCK' | translate}}" tooltip-trigger="outsideClick"
                                        uib-tooltip="{{'ORDER.LOW_STOCK' | translate}}" class="warning-alert"
                                        ng-if="kit.stock < 3 && kit.stock > 0">
                                        <i class="fas fa-layer-group text-warning pointer"></i>
                                    </span>
                                    <span title="{{'ORDER.STOCK_SUBJECT' | translate}}" tooltip-trigger="outsideClick"
                                        uib-tooltip="{{'ORDER.STOCK_SUBJECT' | translate}}" class="warning-alert"
                                        ng-if="kit.stock === null || kit.stock < 1">
                                        <i class="fas fa-layer-group text-danger pointer"></i>
                                    </span>
                                </span>
                            </h2>
                            <p class="text-dark m-0">{{kit.masterPartDescription}}</p>
                        </div>
                        <div class="col-md-3 text-right">
                            <button class="btn btn-primary" ng-click="customerKitsCtrl.handleAddKitClick($event, kit)"
                                ng-if="customerKitsCtrl.hasOrderRole">
                                <span ng-hide="kit.clicked" translate>CUST_KITS.ADD_KIT</span>
                                <span ng-show="kit.clicked"><i class="fa fa-check"></i></span>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="kit-contents" ng-show="kit.status">
                    <div class="table-responsive p-2">
                        <table class="table table-striped-columns table-bordered">
                            <thead>
                                <tr>
                                    <th>{{'CUST_KITS.PART_NUMBER' | translate}}</th>
                                    <th>{{'CUST_KITS.PART_DESCRIPTION' | translate}}</th>
                                    <th>{{'CUST_KITS.QUANTITY' | translate}}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr ng-repeat="part in kit.parts">
                                    <td>{{part.partNumber}}</td>
                                    <td>{{part.partDescription}}</td>
                                    <td>{{part.quantity}}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            </uib-accordion-heading>
        </uib-accordion-group>
    </uib-accordion>

</div>
<div ng-if="customerKitsCtrl.kitList.length === 0 && customerKitsCtrl.initialized">
    <h2 translate>CUST_KITS.NO_KITS</h2>
</div>
<div ng-if="!customerKitsCtrl.initialized" class="center-align">
    <i class="fas fa-sync-alt fa-spin fa-5x"></i>
</div>
