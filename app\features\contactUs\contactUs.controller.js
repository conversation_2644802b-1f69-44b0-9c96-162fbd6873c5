(function () {
    'use strict';

    angular
        .module('app.contactUs')
        .controller('ContactUsController', ContactUsController);

    ContactUsController.$inject = ['contactUsService', '$location', 'manufacturerService'];

    function ContactUsController(contactUsService, $location, manufacturerService) {
        var vm = this;

        vm.phoneNumber = '';

        vm.submit = submit;

        initialize();

        function initialize() {
            var host = $location.host();
            manufacturerService.fetchManufacturerDetailsByDomain(host)
                .then(fetchManufacturerSuccess, fetchManufacturerFailed);
        }

        function fetchManufacturerSuccess(response) {
            var data = response.data;
            vm.phoneNumber = data.phone;
        }

        function fetchManufacturerFailed() {
            console.error("Failed to fetch manufacturer details");
        }

        function submit() {
            vm.failed = false;
            vm.success = false;
            vm.submitting = true;
            contactUsService.submitMessage(vm.subject, vm.message)
                .then(submitMessageSuccess, submitMessageFailed);
        }

        function submitMessageSuccess() {
            vm.subject ="";
            vm.message = "";
            vm.submitting = false;
            vm.success = true;
        }

        function submitMessageFailed() {
            vm.submitting = false;
            vm.failed = true;
        }
    }
})();
