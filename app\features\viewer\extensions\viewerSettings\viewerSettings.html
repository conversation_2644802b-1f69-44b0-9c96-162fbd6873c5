<div class="sidebar-content" ng-show="viewerSettingsCtrl.isOpen">


    <p translate>VIEWER_SETTINGS.TITLE_DESC</p>


    <div class="accordion-column">
        <p class="pull-left" translate>VIEWER_SETTINGS.LINE_DRAWING</p>

        <p class="pull-right">
            <input type="checkbox" id="lineDrawingEnabled" class="switch-cbx hidden"
                   ng-model="viewerSettingsCtrl.data.lineDrawingEnabled" ng-change="viewerSettingsCtrl.toggleLineDrawingEnabled()"/>
            <label for="lineDrawingEnabled" class="switch-lbl"> </label>
        </p>
    </div>

    <div class="accordion-column">
        <p class="pull-left" translate>VIEWER_SETTINGS.EDGING</p>

        <p class="pull-right">
            <input type="checkbox" id="edgingEnabled" class="switch-cbx hidden"
                   ng-model="viewerSettingsCtrl.data.edgingEnabled" ng-change="viewerSettingsCtrl.toggleEdgingEnabled()"/>
            <label for="edgingEnabled" class="switch-lbl"> </label>
        </p>
    </div>

    <div class="accordion-column">
        <p class="pull-left" translate>VIEWER_SETTINGS.VIEW_LOCKED</p>

        <p class="pull-right">
            <input type="checkbox" id="viewLocked" class="switch-cbx hidden"
                   ng-model="viewerSettingsCtrl.data.viewLocked" ng-change="viewerSettingsCtrl.toggleViewLocked()"/>
            <label for="viewLocked" class="switch-lbl"></label>
        </p>
    </div>

</div>