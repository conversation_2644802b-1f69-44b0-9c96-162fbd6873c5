(function () {
    'use strict';

    angular
        .module('app.orders')
        .controller('DPOrdersTabsController', DPOrdersTabsController);

    DPOrdersTabsController.$inject = ['$state', '$rootScope', 'dpOrdersService', 'userService', 'headerBannerService', '$translate', '$scope', '$location'];

    function DPOrdersTabsController($state, $rootScope, dpOrdersService, userService, headerBannerService, $translate, $scope, $location) {
        var vm = this;

        vm.enquiryCount = 0;
        vm.quoteCount = 0;
        vm.liveCount = 0;

        var ENQUIRIES;
        var QUOTATIONS;
        var LIVE_ORDERS;
        var ORDER_HISTORY;

        vm.enquiriesOnly = userService.getEnquiriesOnly();

        vm.isActive = isActive;

        $rootScope.$on("Update-Unread-DP-Order-Tabs", refreshCounts);

        initialize();

        function initialize() {
            $translate([ 'ORDERS_TABS.ENQUIRIES', 'ORDERS_TABS.QUOTATIONS', 'ORDERS_TABS.LIVE_ORDERS', 'ORDERS_TABS.ORDER_HISTORY'])
                .then(function (resp) {
                    ENQUIRIES = resp["ORDERS_TABS.ENQUIRIES"];
                    QUOTATIONS = resp["ORDERS_TABS.QUOTATIONS"];
                    LIVE_ORDERS = resp["ORDERS_TABS.LIVE_ORDERS"];
                    ORDER_HISTORY = resp["ORDERS_TABS.ORDER_HISTORY"];
                    createOrderTabs();

                });

            if ($state.current.url === '/orders') {
                $state.go('dpOrders.customerOrders.orders.enquiries');
            }
        }

        function createOrderTabs() {
            dpOrdersService.getUnreadCounts()
                .then(getUnreadCountSuccess, serviceCallFailed);
        }

        function getUnreadCountSuccess(response) {
            var data = response.data;
            vm.enquiryCount = data.enquiryCount;
            vm.quoteCount = data.quoteCount;
            vm.liveCount = data.liveCount;

            if (vm.enquiriesOnly) {
                vm.tabs = [
                    {title: ENQUIRIES, route: 'dpOrders.customerOrders.orders.enquiries', unreadCount: vm.enquiryCount},
                    {title: ORDER_HISTORY, route: 'dpOrders.customerOrders.orders.historicalorders', unreadCount: 0}];
            } else {
                vm.tabs = [
                    {title: ENQUIRIES, route: 'dpOrders.customerOrders.orders.enquiries', unreadCount: vm.enquiryCount},
                    {title: QUOTATIONS, route: 'dpOrders.customerOrders.orders.quotations', unreadCount: vm.quoteCount},
                    {title: LIVE_ORDERS, route: 'dpOrders.customerOrders.orders.liveorders', unreadCount: vm.liveCount},
                    {title: ORDER_HISTORY, route: 'dpOrders.customerOrders.orders.historicalorders', unreadCount: 0}];
            }

        }

        function refreshCounts() {
            dpOrdersService.getUnreadCounts()
                .then(refreshUnreadCountSuccess, serviceCallFailed);
        }

        function refreshUnreadCountSuccess(response) {
            var data = response.data;
            vm.enquiryCount = data.enquiryCount;
            vm.quoteCount = data.quoteCount;
            vm.liveCount = data.liveCount;

            vm.tabs.forEach(function (tab) {
                if (tab.title === ENQUIRIES) {
                    tab.unreadCount = vm.enquiryCount;
                } else if (tab.title === QUOTATIONS) {
                    tab.unreadCount = vm.quoteCount;
                } else if (tab.title === LIVE_ORDERS) {
                    tab.unreadCount = vm.liveCount;
                }
            });
        }

        function serviceCallFailed(error) {
            headerBannerService.setNotification('ERROR', error.data.error, 5000);
        }

        function isActive(route) {
            var location = $location.path();
            var locationSplit = location.split("/");
            var routeSplit = route.split(".");
            return locationSplit[2] === "customerOrders" && locationSplit[4].substring(0,6) === routeSplit[3].substring(0,6);
        }
    }
})();
