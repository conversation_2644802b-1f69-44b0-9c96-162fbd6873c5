(function () {
        'use strict';

        angular
            .module('app.services')
            .factory('customerService', customerService);

        customerService.$inject = ['$http', 'apiConstants', 'userService'];

        function customerService($http, apiConstants, userService) {
            return {
                getCustomers: getCustomers
            };

            function getCustomers() {
                var manufacturerId = userService.getManufacturerId();
                return $http.get(apiConstants.url + '/manufacturer/' + manufacturerId + '/manufacturersubentities')
            }

        }

    }

)();