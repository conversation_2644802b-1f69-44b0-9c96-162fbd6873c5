.products_container {
    border-radius: 10px;
}

.products {
    border: 1px solid #ccc;
    border-collapse: collapse;
    margin: 0;
    padding: 0;
    width: 100%;
    table-layout: fixed;
}

.products caption {
    font-size: 1.5em;
    margin: 0.5em 0 0.75em;
}

.products_heading {
    background: #f2f6f9;
}

.products_buttons {
    background: white;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}

.products_body tr {
    border: 1px solid #ddd;
    padding: 0.35em;
    background: white;
}

.products th,
.products td {
    padding: 1em;
    word-break: break-word;
}

.products th {
    font-size: 0.85em;
    letter-spacing: 0.1em;
    text-transform: uppercase;
}

.search-panel {
    display: inline-block;
    width: auto;
}

.searchgroup {
    width: 340px;
    display: inline-block;
}

.products_pageNumber {
    background: white;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}

.products-filter-panel {
    width: 100%;
    padding: 1.5em;
    background: white;

    filter-header {
        font-size: 0.8em;
        text-transform: uppercase;
        font-weight: 700;
    }
    .first-filter {
    }

    .filter-option {
        display: inline-block;
        margin-right: $spacing * 2;
        margin-top: $spacing;
    }
    .filter-buttons {
        margin-top: $spacing * 2;

        .btn:first-child {
            margin-right: $spacing;
        }
    }
}

.filter-panel {
    float: none;
    margin-top: 8px;
    padding: 8px 0px;
    background: white;
}

.input-icons i {
    position: absolute;
}

.input-icons {
    width: 100%;
    margin-bottom: 10px;
}

.icon {
    padding: 10px;
    min-width: 40px;
}

.input-field {
    width: 100%;
    padding: 10px;
    text-align: center;
}

.input-group-text {
    padding: 0.375rem 1.5rem;
    color: black;
    background-color: #f2f6f9;
}

.product-catalogue-table {
    table-layout: fixed;
    width: 100%;

    th,
    td {
        width: 25%;
    }

    th:last-child,
    td:last-child {
        width: 20%;
    }
}

@media screen and (max-width: 800px) {
    .products {
        border: 0;
    }

    .products caption {
        font-size: 1.3em;
    }

    .products thead {
        border: none;
        clip: rect(0 0 0 0);
        height: 1px;
        margin: -1px;
        overflow: hidden;
        padding: 0;
        position: absolute;
        width: 1px;
    }

    .products tr {
        border-bottom: 3px solid #ddd;
        display: block;
        margin-bottom: 0.625em;
    }

    .products td {
        border-bottom: 1px solid #ddd;
        display: block;
        font-size: 1em;
    }

    .products td::before {
        /*
    * aria-label has no advantage, it won't be read inside a table
    content: attr(aria-label);
    */
        content: attr(data-label);
        font-weight: bold;
        text-transform: uppercase;
        padding-right: 10px;
    }

    .products td:last-child {
        border-bottom: 0;
    }

    .products-filter {
        margin-left: auto;
    }

    .products_buttons .searchgroup {
        width: 100%;
    }

    .products_buttons .search-panel {
        display: inline-block;
        width: 100%;
    }

    .sub-tabs ul li {
        margin: 0 40px 0px 0;
    }
}

.product-range {
    flex: 0 0 200px;
    max-width: 200px;
}

.scrollable-list {
    // max-height: 95vh;
    overflow-y: auto;
    overflow-x: hidden;
}

.main-content {
    flex: 1;
    padding-left: 20px;
}

.truncate-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 150px;
    display: inline-block;
}

.product-range a {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.scrollable-list::-webkit-scrollbar {
    width: 8px;
}

.scrollable-list::-webkit-scrollbar-track {
    border-radius: 8px;
    background-color: #e7e7e7;
    border: 1px solid #cacaca;
}

.scrollable-list::-webkit-scrollbar-thumb {
    border-radius: 8px;
    background-color: #767676;
}

// Custom scrollbar for browsers which don't support webkit
.scrollable-list {
    scrollbar-width: thin;
    scrollbar-color: #767676 #e7e7e7;
}

.menu-title {
    font-size: 1.4em;
    margin-bottom: 4px;
}

.product-range ul li {
    display: block;
    width: 100%;
    margin: 0 15px 0px 0;
}

.table {
    width: 100%;
}

.productsListTable th:first-child {
    text-align: center !important;
}

.productsListTable .customerProductsImage img {
    display: block;
    margin: 0 auto;
    width: 42%;
}

.sub-tabs ul {
    margin-top: 15px;
}
