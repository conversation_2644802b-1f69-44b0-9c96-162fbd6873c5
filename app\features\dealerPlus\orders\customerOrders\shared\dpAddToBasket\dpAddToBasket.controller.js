(function () {
    'use strict';

    angular
        .module('app.orders')
        .controller('DPAddToBasketController', DPAddToBasketController);

    DPAddToBasketController.$inject = ['$uibModalInstance', 'partsObject', 'basketService'];

    function DPAddToBasketController($uibModalInstance, partsObject, basketService) {
        var vm = this;

        vm.partsList = JSON.parse(JSON.stringify(partsObject.orderItems));
        vm.manualPartsList = JSON.parse(JSON.stringify(partsObject.manualItems));
        vm.orderId = partsObject.orderId;
        vm.displayId = partsObject.displayId ? partsObject.displayId : partsObject.orderId;

        vm.cancel = cancel;
        vm.addToBasket = addToBasket;
        vm.addAllToBasket = addAllToBasket;

        initialize();

        function initialize() {
            for (var i = 0; i < vm.partsList.length; i++) {
                vm.partsList[i].oldQuantity = vm.partsList[i].quantity;
                vm.partsList[i].price = null;
            }
            for (var i = 0; i < vm.manualPartsList.length; i++) {
                vm.manualPartsList[i].oldQuantity = vm.manualPartsList[i].quantity;
                vm.manualPartsList[i].price = null;
            }
        }

        function cancel() {
            $uibModalInstance.dismiss();
        }

        function addToBasket() {
            vm.errorSplittingOrder = false;
            var parts = [];
            var addParts = [];

            for (var i = 0; i < vm.partsList.length; i++) {
                if (vm.partsList[i].isSelected) {
                    parts.push(vm.partsList[i])
                }
            }
            for (var i = 0; i < vm.manualPartsList.length; i++) {
                if (vm.manualPartsList[i].isSelected) {
                    addParts.push(vm.manualPartsList[i]);
                }
            }

            if (validateSplitOrderData(parts, addParts)) {
                addIdentifiedItemsToBasket(parts)
                addManualPartsToBasket(addParts);
                $uibModalInstance.close();
            }
        }

        function addAllToBasket(){
            addIdentifiedItemsToBasket(vm.partsList)
            addManualPartsToBasket(vm.manualPartsList);
            $uibModalInstance.close();
        }

        function addManualPartsToBasket(additionalParts) {
            for (var j = 0; j < additionalParts.length; j++) {
                var addPart = additionalParts[j];
                basketService.addManualPartToBasket(addPart);
            }
        }

        function addIdentifiedItemsToBasket(orderItems) {
            for (var i = 0; i < orderItems.length; i++) {
                var part = {
                    partNumber: orderItems[i].partNumber,
                    partId: orderItems[i].partId,
                    masterPartId: orderItems[i].masterPartId,
                    quantity: orderItems[i].quantity,
                    partDescription: orderItems[i].description ? orderItems[i].description : orderItems[i].partDescription,
                    modelId: orderItems[i].modelId ? orderItems[i].modelId.toString() : null,
                    price: orderItems[i].price,
                    stock: orderItems[i].stock
                };
                basketService.addPart(part);
            }
        }

        function validateSplitOrderData(parts, addParts) {
            vm.nothingSelectedError = false;
            vm.quantityMismatchError = false;

            if (parts.length + addParts.length > 0) {
                for (var i = 0; i < parts.length; i++) {
                    if (parts[i].quantity === undefined || parts[i].quantity > parts[i].oldQuantity) {
                        vm.quantityMismatchError = true;
                        break;
                    }
                }
                for (var i = 0; i < addParts.length; i++) {
                    if (addParts[i].quantity === undefined || addParts[i].quantity > addParts[i].oldQuantity) {
                        vm.quantityMismatchError = true;
                        break;
                    }
                }
            } else {
                vm.nothingSelectedError = true;
            }
            return !vm.nothingSelectedError && !vm.quantityMismatchError;
        }

    }
})();