(function () {
    'use strict';

    angular
        .module('app.viewer')
        .controller('CreateKitController', CreateKitController);

    CreateKitController.$inject = ['kitService', '$stateParams', '$scope', 'viewerService', 'viewerBannerService', '$rootScope', '$translate'];

    function CreateKitController(kitService, $stateParams, $scope, viewerService, viewerBannerService, $rootScope, $translate) {
        var vm = this;

        vm.isOpen = false;
        vm.title = "";
        vm.description = "";
        vm.kitParts = [];
        vm.selectedPart = [];
        vm.isEdit = false;
        vm.modelId = $stateParams.modelId;

        vm.saveKit = saveKit;
        vm.cancel = cancel;
        vm.removePartFromKit = removePartFromKit;
        vm.addToKit = addToKit;

        var KIT_SUCCESS;
        $translate(['CREATE_KIT.KIT_SUCCESS'])
            .then(function (resp) {
                KIT_SUCCESS = resp["CREATE_KIT.KIT_SUCCESS"];
            });

        initialize();

        function initialize(kitId) {
            if (kitId) {
                vm.isEdit = true;
                fetchKit(kitId);
            } else {
                vm.isEdit = false;
                vm.title = "";
                vm.description = "";
                vm.kitParts = [];
            }
        }

        function fetchKit(kitId) {
            kitService.fetchKit(kitId)
                .then(fetchKitSuccess, serviceCallFailed);
        }

        function fetchKitSuccess(response) {
            vm.title = response.data.title;
            vm.description = response.data.description;
            vm.kitParts = response.data.parts;
        }

        function removePartFromKit(index) {
            vm.kitParts.splice(index, 1);
        }

        function addToKit(part) {
            if (undefined !== part.partNumber) {
                var isPartInList = _.findWhere(vm.kitParts, {partNumber: part.partNumber});
                if (isPartInList === undefined) {
                    part.quantity = 1;
                    vm.kitParts.push(part);
                }
            }
        }

        function cancel() {
            vm.title = "";
            vm.description = "";
            vm.kitParts = [];
            vm.isOpen = false;
            $rootScope.$broadcast("create-kit-closed");
        }

        function saveKit() {
            vm.errors = {};
            vm.errors.notEnoughParts = vm.kitParts.length < 1;

            if (vm.errors.noPartSelected || vm.errors.noDescription || vm.errors.notEnoughParts) {
                return;
            }

            if (vm.isEdit) {
                kitService.editKit(vm.title, vm.description, vm.kitParts, vm.kitId)
                    .then(saveKitSuccess, serviceCallFailed);
            } else {
                kitService.createKit(vm.modelId, vm.title, vm.description, vm.kitParts)
                    .then(saveKitSuccess, serviceCallFailed);
            }
        }

        function saveKitSuccess() {
            viewerBannerService.setNotification("SUCCESS", KIT_SUCCESS);
            vm.isOpen = false;
            $rootScope.$broadcast("create-kit-closed");
        }

        function serviceCallFailed(error) {
            viewerBannerService.setNotification("ERROR", error);
        }

        $scope.$on("viewer-part-selected", function (event, partViewerDetails) {
            vm.hasNonModeledPart = !!partViewerDetails.nonModelled;
            if (partViewerDetails.length === 1) {
                vm.selectedPart = partViewerDetails[0].part;
            } else if (partViewerDetails.length > 1) {
                vm.selectedPart = partViewerDetails;
            } else {
                vm.selectedPart = [];
            }
        });

        $scope.$on("create-kit-opened", function (event, kitId) {
            vm.isOpen = true;
            vm.kitId = kitId;
            initialize(vm.kitId);
        });
    }

})();
