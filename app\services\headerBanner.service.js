(function () {
    'use strict';

    angular
        .module('app.services')
        .factory('headerBannerService', headerBannerService);

    headerBannerService.$inject = ['$rootScope'];

    function headerBannerService($rootScope) {

        var notification = [];

        return {
            setNotification: setNotification,
            getNotification: getNotification,
            removeNotification: removeNotification
        };

        /**
         * @param {string} level - values are ERROR, WARN, SUCCESS and INFO
         **/
        function setNotification(level, text, timeout) {
            notification = {"level": level, "text": text, "timeout": timeout};
            $rootScope.$broadcast("bannerNotification");
        }

        function getNotification() {
            return notification;
        }

        function removeNotification() {
            notification = [];
            $rootScope.$broadcast("bannerNotification");
        }
    }
})();
