(function () {
    'use strict';

    angular
        .module('app.services')
        .factory('optionsSetService', optionsSetService);

    optionsSetService.$inject = ['$http', 'apiConstants', '$q'];

    function optionsSetService($http, apiConstants, $q) {
        return {
            fetchOptionsSet: fetchOptionsSet,
            fetchOptionsSetsForModel: fetchOptionsSetsForModel,
            //fetchOptionsSetsForPart: fetchOptionsSetsForPart,
            deleteOptionsSet: deleteOptionsSet,
            createOptionsSet: createOptionsSet,
            editOptionsSet: editOptionsSet
        };

        function fetchOptionsSet(optionsSetId) {
            return $http.get(apiConstants.url + '/optionsSet/' + optionsSetId);
        }

        function fetchOptionsSetsForModel(modelId) {
                return $http.get(apiConstants.url + '/optionsSet/model/' + modelId);
        }
/*
        function fetchOptionsSetsForPart(partId) {
            return $http.get(apiConstants.url + '/optionsSet/part/' + partId);
        }*/

        function deleteOptionsSet(optionsSetId) {
            return $http.delete(apiConstants.url + '/optionsSet/' + optionsSetId);
        }

        function createOptionsSet(modelId, partId, description, optionsSet) {
            var data = {
                "partId": partId,
                "description": description,
                "optionsSet": optionsSet
            };
            return $http.post(apiConstants.url + '/optionsSet/model/' + modelId, data);
        }

        function editOptionsSet(optionsSetId, partId, description, optionsSet) {
            var data = {
                "optionsSetId": optionsSetId,
                "partId": partId,
                "description": description,
                "optionsSet": optionsSet
            };
            return $http.put(apiConstants.url + '/optionsSet/' + optionsSetId, data);
        }



    }
})();
