(function () {
    'use strict';

    angular
        .module('app.services')
        .factory('customerMachineService', customerMachineService);

    customerMachineService.$inject = ['$http', 'apiConstants', 'userService'];

    function customerMachineService($http, apiConstants, userService) {

        return {
            fetchMachines: fetchMachines
        };

        function fetchMachines(manufacturerSubEntityId) {
            return $http.get(apiConstants.url + '/manufacturersubentity/' + manufacturerSubEntityId + '/machines', null);
        }

    }
})();
