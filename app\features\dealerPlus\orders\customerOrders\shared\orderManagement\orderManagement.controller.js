(function () {
    'use strict';

    angular
        .module('app.orders')
        .controller('OrderManagementController', OrderManagementController);

    OrderManagementController.$inject = ['$uibModalInstance', 'orderObject', 'ordersService', '$uibModal', 'userService', 'dpOrdersService', '$scope'];

    function OrderManagementController($uibModalInstance, orderObject, ordersService, $uibModal, userService, dpOrdersService, $scope) {
        var vm = this;

        vm.partsList = JSON.parse(JSON.stringify(orderObject.orderItems));
        vm.manualPartsList = JSON.parse(JSON.stringify(orderObject.additionalParts));
        vm.orderId = orderObject.orderId;
        vm.displayId = orderObject.customOrderDisplay ? orderObject.customOrderDisplay : orderObject.orderId;
        vm.isDealerPlus = userService.isDealerPlusUser();
        vm.isRequiredSerialNumber = userService.getRequiredSerialNumber();
        vm.enquiryPurchaseOrder = userService.getEnquiryPurchaseOrder();

        vm.submitDisabled = false;
        vm.submitting = false;
        vm.stockOrderSelected = false;
        vm.validationError = false;

        vm.cancel = cancel;
        vm.addNewAddress = addNewAddress;
        vm.addNewNumber = addNewNumber;
        vm.addNewName = addNewName;
        vm.toggleStockOrder = toggleStockOrder;
        vm.submitEnquiry = submitEnquiry;

        initialize();

        function initialize() {
            configureDatePicker();
            updateAddresses();
            updateNumbers();
            updateNames();
            for (var i = 0; i < vm.partsList.length; i++) {
                vm.partsList[i].oldQuantity = vm.partsList[i].quantity;
            }
            for (var j = 0; j < vm.manualPartsList.length; j++) {
                vm.manualPartsList[j].oldQuantity = vm.manualPartsList[j].quantity;
            }
        }

        function cancel() {
            $uibModalInstance.dismiss();
        }

        function updateAddresses() {
            ordersService.getAddresses()
                .then(function(resp) {
                    getAddressesSuccess(resp);
                    setDefaultBillingAddress(resp.data);
                });
        }

        function updateNames() {
            ordersService.getNames()
                .then(function(resp) {
                    getNamesSuccess(resp);
                    setDefaultNames(resp.data);
                });
        }

        function updateNumbers() {
            ordersService.getNumbers()
                .then(function(resp) {
                    getNumbersSuccess(resp);
                    setDefaultNumbers(resp.data);
                });
        }

        function getAddressesSuccess(resp) {
            vm.addresses = resp.data;
        }

        function getNamesSuccess(resp) {
            vm.names = resp.data;
        }

        function getNumbersSuccess(resp) {
            vm.numbers = resp.data;
        }

        function addNewAddress() {
            $uibModal.open({
                templateUrl: 'features/shared/createNewAddress/createNewAddress.html',
                controller: 'CreateNewAddressController',
                controllerAs: 'createNewAddressCtrl',
                resolve: {
                    customerUserId: function () {
                        return null;
                    },
                    isRegisterMode: function () {
                        return false;
                    },
                    addressData: function() {
                        return null;
                    },
                    showCompanyName: function() {
                        return true;
                    }
                }
            }).result.then(function () {
                updateAddresses();
            });
        }

        function addNewName() {
            $uibModal.open({
                templateUrl: 'features/shared/createNewName/createNewName.html',
                controller: 'CreateNewNameController',
                controllerAs: 'createNewNameCtrl',
                resolve: {
                    customerUserId: function () {
                        return null;
                    }
                }
            }).result.then(function () {
                updateNames();
            });
        }

        function addNewNumber() {
            $uibModal.open({
                templateUrl: 'features/shared/createNewNumber/createNewNumber.html',
                controller: 'CreateNewNumberController',
                controllerAs: 'createNewNumberCtrl',
                resolve: {
                    customerUserId: function () {
                        return null;
                    }
                }
            }).result.then(function () {
                updateNumbers();
            });
        }

        function toggleStockOrder() {
            if (vm.stockOrder) {
                vm.serialNumber = null;
                vm.stockOrderSelected = true;
            } else {
                vm.stockOrderSelected = false;
            }
        }

        function configureDatePicker() {
            var d = new Date();
            setTimeout(function () {
                $('#datepicker').datepicker({
                    dateFormat: 'dd/mm/yy',
                    minDate: new Date(d.setDate(d.getDate() + 1)),
                    onSelect: function (dateText) {
                        vm.requestedDeliveryDate = dateText;
                        $scope.$apply();
                    }
                });
            }, 200);
        }

        function submitEnquiry() {
            $scope.detailsForm.$submitted = true;
            if ($scope.detailsForm.$valid) {
                vm.validationError = false;
                vm.submitDisabled = true;
                vm.submitting = true;
                var orderDetails = {
                    shippingAddress: JSON.parse(vm.deliveryAddress),
                    billingAddress: JSON.parse(vm.billingAddress),
                    contactName: vm.contactName,
                    deliveryName: vm.deliveryName,
                    contactNumber: vm.contactNumber,
                    deliveryNumber: vm.deliveryNumber,
                    requestedDeliveryDate: createDateObject(vm.requestedDeliveryDate),
                    purchaseOrder: vm.purchaseOrder,
                    serialNumber: vm.serialNumber,
                    stockOrder: vm.stockOrder,
                    orderItems: convertBasketToOrderItems(vm.partsList),
                    additionalParts: vm.manualPartsList,
                    currencyId: userService.getDefaultCurrency().id,
                    manufacturerSubEntityId: userService.getManufacturerSubEntityId(),
                    emailAddress: userService.getEmailAddress(),
                    userId: userService.getUserId(),
                    associatedDPOrderId: vm.orderId
                };
                var payload = {
                    order: orderDetails,
                    warehouseId: null
                };
                dpOrdersService.createOrder(payload)
                    .then(submitEnquirySuccess, submitEnquiryFailed);
            } else {
                vm.validationError = true;
            }
        }

        function submitEnquirySuccess(resp){
            $uibModalInstance.close(resp.data);
        }

        function submitEnquiryFailed(){
            vm.submitting = false;
            vm.submitDisabled = false;
            console.log("Failed submission of order");
        }

        function convertBasketToOrderItems(basket) {
            var orderItems = [];
            angular.forEach(basket, function (value, key) {
                if(value.quantity > 0) {
                    orderItems.push({
                        "partId": value.partId,
                        "masterPartId": value.masterPartId,
                        "quantity": value.quantity,
                        "partNumber": value.partNumber,
                        "machineName": value.machineName,
                        "partDescription": value.partDescription,
                        "comment": value.comment
                    })
                }
            });
            return orderItems;
        }

        function createDateObject(requestedDeliveryDate) {
            var dateParts = requestedDeliveryDate.split("/");
            var dateObject = new Date(dateParts[2], dateParts[1] - 1, dateParts[0], 12); // month is 0-based
            return dateObject;
        }

        function setDefaultBillingAddress(addresses) {
            vm.addresses = addresses;
            if (vm.addresses.length === 1) {
                vm.billingAddress = JSON.stringify(vm.addresses[0]);
                vm.deliveryAddress = JSON.stringify(vm.addresses[0]);
            }
        }

        function setDefaultNames(names) {
            vm.names = names;
            if (vm.names.length === 1) {
                vm.contactName = vm.names[0].contactName;
                vm.deliveryName = vm.names[0].contactName;
            }
        }

        function setDefaultNumbers(numbers) {
            vm.numbers = numbers;
            if (vm.numbers.length === 1) {
                vm.contactNumber = vm.numbers[0].contactNumber;
                vm.deliveryNumber = vm.numbers[0].contactNumber;
            }
        }

    }
})();