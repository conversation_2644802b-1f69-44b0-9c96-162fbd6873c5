(function () {
  'use strict';

  angular
  .module('app.shared')
  .controller('ExitOrderOnBehalfOfController', ExitOrderOnBehalfOfController);

  ExitOrderOnBehalfOfController.$inject = ['$uibModalInstance', '$state', 'basketService', 'userService'];

  function ExitOrderOnBehalfOfController($uibModalInstance, $state, basketService, userService) {

    var vm = this;

    vm.close = close;
    vm.cancel = $uibModalInstance.dismiss;
    vm.exitWithoutSaving = exitWithoutSaving;
    vm.saveAndExit = saveAndExit;

    function close() {
      $uibModalInstance.close();
    }

    function exitWithoutSaving() {
      if (userService.isDealerPlusUser()) {
        basketService.emptyBasket();
        $uibModalInstance.close();
        $state.go('dpCustomers');
      } else {
        basketService.emptyBasket();
        $uibModalInstance.close();
        $state.go('customers');
      }
    }

    function saveAndExit() {
      if (userService.isDealerPlusUser()) {
        $uibModalInstance.close();
        $state.go('dpCustomers');
      } else {
        $uibModalInstance.close();
        $state.go('customers');
      }
    }
  }
})();
