(function () {
  "use strict";

  angular
    .module("app.parts")
    .controller("editKitSinglePriceController", editKitSinglePriceController);

  editKitSinglePriceController.$inject = ["$uibModalInstance", "masterPartService", "data"];

  function editKitSinglePriceController($uibModalInstance, masterPartService, data) {
    var vm = this;

    vm.update = update;
    vm.cancel = $uibModalInstance.dismiss;

    initialize();

    function initialize() {
      masterPartService.getPrice(data.masterPartId).then(function (response) {
        vm.editSinglePrice = data.price
      }).catch(function (error) {
        console.error("Error fetching price:", error);
      });
    }

    function update() {
      var priceToUpdate = parseFloat(vm.editSinglePrice);
      if (isNaN(priceToUpdate)) {
        return;
      }
    
      var formattedPrice = priceToUpdate.toFixed(2);
      masterPartService.updatePrice(data.masterPartId, formattedPrice)
        .then(function(response) {
          $uibModalInstance.close(formattedPrice);
        })
        .catch(function(error) {
          console.error("Error updating price:", error);
        });
    }
  }
})();