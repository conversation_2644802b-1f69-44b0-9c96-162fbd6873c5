(function () {
    'use strict';

    angular
        .module('app.base')
        .controller('RegisterMachineController', RegisterMachineController);

    RegisterMachineController.$inject = ['$uibModalInstance', 'registerService', 'manufacturerService', '$scope', 'machines'];

    function RegisterMachineController($uibModalInstance, registerService, manufacturerService, $scope, machines) {
        var vm = this;

        $scope.multiSelectEvent = { onItemSelect: onItemSelect, onItemDeselect: onItemDeselect };

        vm.rangeSelected = null;
        vm.originalMachines = angular.copy(machines, vm.originalMachines);
        vm.machines = machines.length ? machines : [];
        vm.manualSelected = [];
        vm.disableSelectRange = true;

        vm.manualDropdownSetting = {
            checkBoxes: true,
            showCheckAll: false,
            showUncheckAll: false,
            buttonClasses: "btn btn-default multiCheckbox",
            styleActive: true,
            closeOnBlur: true,
            displayProp: "manualName",
        };

        vm.manualDropdownTextSettings = {
            buttonDefaultText: 'Select machine',
            dynamicButtonTextSuffix: 'Machine selected'
        };

        vm.manufacturerId = manufacturerService.getManufacturerId();
        vm.cancel = cancel;
        vm.onRangeChanged = onRangeChanged;
        vm.addMachine = addMachine;
        vm.removeMachine = removeMachine;
        vm.save = save;

        initialize();

        function initialize() {
            getRanges();
        }

        function getRanges() {
            registerService.getRangesByManufacturerId(vm.manufacturerId).then((res) => {
                vm.ranges = res.data;
            })
        }

        function onRangeChanged() {
            vm.manualSelected = [];
            registerService.getManualsByRangeId(vm.rangeSelected.rangeId).then(getManualsByRangeIdSuccess, getManualsByRangeIdFailed)
        }

        function getManualsByRangeIdSuccess(res) {
            const data = res.data;
            data.forEach(item => item.id = item.manualId);
            vm.manuals = data;
            vm.disableSelectRange = false;
            vm.manualSelected = vm.machines.filter(item => item.rangeId === vm.rangeSelected.rangeId).map(item => ({ id: item.id }));
        }

        function getManualsByRangeIdFailed(res) {
            vm.manuals = [];
            vm.manualSelected = [];
            vm.disableSelectRange = false;
        }

        function addMachine() {
            this.machines.push();
        }

        function cancel() {
            $uibModalInstance.close(vm.originalMachines.length ? vm.originalMachines : null);
        }

        function save() {
            $uibModalInstance.close(vm.machines);
        }

        function onItemSelect(item) {
            const manual = vm.manuals.find(el => el.manualId === item.id);
            const machine = {
                id: manual.id,
                rangeId: vm.rangeSelected.rangeId,
                rangeName: vm.rangeSelected.name,
                manualId: manual.id,
                manualName: manual.manualName
            }
            vm.machines.push(machine);
        }

        function onItemDeselect(item) {
            const index = vm.machines.findIndex(el => el.manualId === item.id);
            if (index >= 0) vm.machines.splice(index, 1);
        }

        function removeMachine(item, isRemoveInDropDown) {
            const index = vm.machines.findIndex(el => el.manualId === item.id);
            if (index >= 0) vm.machines.splice(index, 1);

            if (vm.rangeSelected && isRemoveInDropDown && item.rangeId === vm.rangeSelected.rangeId) {
                const dropIndex = vm.manualSelected.findIndex(el => el.id === item.id);
                if (dropIndex >= 0) vm.manualSelected.splice(dropIndex, 1);
            }
        }
    }
})();
