(function () {
    'use strict';

    angular
        .module('app.security')
        .controller('SecurityController', SecurityController);

    SecurityController.$inject = ['$state', 'securityService', '$uibModal', 'headerBannerService', '$translate'];

    function SecurityController($state, securityService, $uibModal, headerBannerService, $translate) {
        var vm = this;

        vm.editIPTrackingEmail = false;
        vm.editAccessReportEmail = false;
        vm.logoResponseUrl = "";
        vm.watermarkId = null;
        var UNABLE_LOGIN_LOC, PLEASE_SELECT, UNABLE_USER_ACCESS, UNABLE_WATERMARK, PLEASE_UPLOAD;
        $translate(['SECURITY.UNABLE_LOGIN_LOC', 'SECURITY.PLEASE_SELECT', 'SECURITY.UNABLE_USER_ACCESS', 'SECURITY.UNABLE_WATERMARK', 'SECURITY.PLEASE_UPLOAD', 'SECURITY.ENABLED', 'SECURITY.DISABLED'])
            .then(function (resp) {
                UNABLE_LOGIN_LOC = resp["SECURITY.UNABLE_LOGIN_LOC"];
                PLEASE_SELECT = resp["SECURITY.PLEASE_SELECT"];
                UNABLE_USER_ACCESS = resp["SECURITY.UNABLE_USER_ACCESS"];
                UNABLE_WATERMARK = resp["SECURITY.UNABLE_WATERMARK"];
                PLEASE_UPLOAD = resp["SECURITY.PLEASE_UPLOAD"];
                vm.ENABLED = resp["SECURITY.ENABLED"];
                vm.DISABLED = resp["SECURITY.DISABLED"];
            });

        vm.toggleMFA = toggleMFA;
        vm.toggleIPTrack = toggleIPTrack;
        vm.ipTrackingEmailChange = ipTrackingEmailChange;
        vm.accessReportEmailChange = accessReportEmailChange;
        vm.addLogoImage = addLogoImage;

        vm.toggleAccessReport = toggleAccessReport;
        vm.toggleWatermark = toggleWatermark;

        initialize();

        function initialize() {
            securityService.getPermissions()
                .then(getPermissionsSuccess, serviceFailed);

            securityService.getManufacturerUsers()
                .then(getManufacturerUsersSuccess, serviceFailed);

            securityService.getWatermarkSettings()
                .then(getWatermarkSettingsSuccess, getWatermarkSettingsFailed)

        }

        function getPermissionsSuccess(response) {
            vm.data = response.data;
        }

        function getManufacturerUsersSuccess(response) {
            vm.users = response.data;
        }

        function serviceFailed(error) {
            headerBannerService.setNotification('ERROR', error.data.error, 10000);
        }

        function toggleMFA() {
            updateSecurityPermissions();
        }

        function toggleIPTrack() {
            if (vm.data.ipTrackingEnabled) {
                if (vm.data.ipTrackingEmail === undefined || vm.data.ipTrackingEmail === null) {
                    vm.data.ipTrackingEnabled = false;
                    alert(UNABLE_LOGIN_LOC + "\n" + PLEASE_SELECT)
                } else {
                    updateSecurityPermissions();
                }
            } else {
                updateSecurityPermissions();
            }
        }


        function ipTrackingEmailChange() {
            updateSecurityPermissions();
        }

        function accessReportEmailChange() {
            updateSecurityPermissions();
        }


        function toggleAccessReport() {
            if (vm.data.accessReportEnabled) {
                if (vm.data.accessReportEmail === undefined || vm.data.accessReportEmail === null) {
                    vm.data.accessReportEmail = false;
                    alert(UNABLE_USER_ACCESS + " \n" + PLEASE_SELECT)
                } else {
                    updateSecurityPermissions();
                }
            } else {
                updateSecurityPermissions();
            }
        }


        function updateSecurityPermissions() {
            securityService.updatePermissions(vm.data);
        }

        function addLogoImage() {
            $uibModal.open({
                templateUrl: 'features/products/imageCropper/imageCropper.html',
                controller: 'ImageCropperController',
                controllerAs: 'imageCropperCtrl',
                size: 'xl',
                backdrop: 'static',
                resolve: {
                    cropType: function () {
                        return 'WATERMARK';
                    }
                }
            }).result.then(function (response) {
                if (response) {
                    vm.logoResponseUrl = response;
                    saveWatermarkSettings();
                }
            });
        }

        function toggleWatermark() {
            if (vm.watermarkEnabled) {
                if (vm.logoResponseUrl === undefined || vm.logoResponseUrl === null || vm.logoResponseUrl === "") {
                    vm.watermarkEnabled = false;
                    alert(UNABLE_WATERMARK + " \n " + PLEASE_UPLOAD)
                } else {
                    saveWatermarkSettings();
                }
            } else {
                saveWatermarkSettings();
            }
        }

        function saveWatermarkSettings() {
            var watermarkSettings = {
                imageUrl: vm.logoResponseUrl,
                enabled: vm.watermarkEnabled
            };
            if (vm.watermarkId !== null) {
                watermarkSettings.id = vm.watermarkId;
            }
            securityService.saveWatermarkSettings(watermarkSettings)
                .then(saveWatermarkSuccess, saveWatermarkFailed);
        }

        function saveWatermarkSuccess(response) {
            vm.watermarkId = response.data;
        }

        function saveWatermarkFailed(error) {
            headerBannerService.setNotification('ERROR', error.data.error, 10000);
        }

        function getWatermarkSettingsSuccess(response) {
            vm.logoResponseUrl = response.data.imageUrl;
            vm.watermarkEnabled = response.data.enabled;
            vm.watermarkId = response.data.id;
        }

        function getWatermarkSettingsFailed() {
            vm.logoResponseUrl = "";
            vm.watermarkEnabled = false;
            vm.watermarkId = null;
        }
    }
})();