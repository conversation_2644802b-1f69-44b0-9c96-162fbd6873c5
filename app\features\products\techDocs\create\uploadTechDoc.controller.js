(function () {
    'use strict';

    angular
        .module('app.shared')
        .controller('UploadTechDocController', UploadTechDocController);

    UploadTechDocController.$inject = ['$uibModalInstance', 'dataObject', '$scope', 'awsS3Service', 'manufacturerProductService', '$translate'];

    function UploadTechDocController($uibModalInstance, dataObject, $scope, awsS3Service, manufacturerProductService, $translate) {
        var vm = this;

        vm.save = save;
        vm.techDocAttached = techDocAttached;
        vm.cancel = $uibModalInstance.dismiss;
        var ERROR_MSG;
        $translate(['IMG_CROPPER.ERROR_MSG'])
            .then(function (resp) {
                ERROR_MSG = resp["IMG_CROPPER.ERROR_MSG"];
            });

        if (dataObject) {
            vm.name = dataObject.name ? dataObject.name : null;
            vm.description = dataObject.description ? dataObject.description : null;
            vm.id = dataObject.id ? dataObject.id : null;
            vm.filename = dataObject.filename ? dataObject.filename : null;
            vm.originalFilename = dataObject.filename ? dataObject.filename : null;
            //vm.originalURL = dataObject.url ? dataObject.url : null;
            vm.url = dataObject.url ? dataObject.url : null;
            vm.pageCount = dataObject.pageCount ? dataObject.pageCount : null;
            vm.isEdit = dataObject.id ? true : false;
        }

        function save() {
            vm.isSaving = true;
            if(vm.isExcelFile) {
                // Directly upload Excel files without page count
                awsS3Service.uploadTechDoc(vm.techDocFile)
                    .then(uploadTechDocSuccess, serviceFailed);
            } else {
                // Existing logic for PDF files
                if (vm.id && vm.originalFilename === vm.filename) {
                    uploadTechDocSuccess(vm.url);
                } else {
                    awsS3Service.uploadTechDoc(vm.techDocFile)
                        .then(uploadTechDocSuccess, serviceFailed);
                }
            }
        }


        function serviceFailed(error) {
            vm.error = ERROR_MSG;
            console.error("Upload tec doc failed: " + error.data);
        }

        function uploadTechDocSuccess(resp) {
            if(vm.isExcelFile) {
                vm.techDocURL = resp;
                saveToBackend();;
            } else if(!vm.isExcelFile) {
                vm.techDocURL = resp;
                getPageCount(vm.techDocURL);
            }
        }

        function saveTechDocSuccess() {
            $uibModalInstance.close();
        }

        function techDocAttached(obj) {
            var elem = obj.target || obj.srcElement;
            if (elem.files.length > 0) {
                vm.techDocFile = elem.files[0];
                vm.filename = vm.techDocFile.name;

                if(vm.techDocFile.name.endsWith('.xlsx') || vm.techDocFile.name.endsWith('.xls')) {
                    vm.isExcelFile = true;
                } else {
                    vm.isExcelFile = false;
                }
                $scope.$digest();
            }
        }

        function getPageCount(path) {
            var pdfjsLib = window['pdfjs-dist/build/pdf'];
            var loadingTask = pdfjsLib.getDocument(path);
            loadingTask.promise.then(setPageNumberAndSave);
        }

        function setPageNumberAndSave(doc) {
            vm.pageCount = doc.numPages;
            saveToBackend();
        }

        function saveToBackend() {
            if (vm.id) {
                manufacturerProductService.editTechDoc(vm.name, vm.description, vm.techDocURL, vm.filename, vm.pageCount, vm.id)
                    .then(saveTechDocSuccess, serviceFailed);
            } else {
                manufacturerProductService.createTechDoc(vm.name, vm.description, vm.techDocURL, vm.filename, vm.pageCount)
                    .then(saveTechDocSuccess, serviceFailed);
            }
        }
    }
})();
