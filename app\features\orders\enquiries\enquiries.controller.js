(function () {
    'use strict';

    angular
        .module('app.orders')
        .controller('EnquiriesController', EnquiriesController);

    EnquiriesController.$inject = ['ordersService', '$scope', '$controller', 'headerBannerService', '$translate', '$filter', '$window', '$uibModal', '$state', 'userService'];

    function EnquiriesController(ordersService, $scope, $controller, headerBannerService, $translate, $filter, $window, $uibModal, $state, userService) {
        var vm = this;
        var CONFIRM_ORDER_ARCHIVED, ARCHIVE_MODAL, CONFIRM_ORDER_BANNER;
        angular.extend(vm, $controller('OrdersController', { $scope: $scope }));
        vm.stage = {};

        vm.loadingInfiniteScrollData = false;
        vm.isOrdersLoaded = false;
        vm.showBackToTopButton = false;

        $translate(['ENQUIRY.ENQUIRIES', 'ENQUIRY.ENQUIRY', 'ORDERS.CONFIRM_ORDER_ARCHIVED', 'ORDERS.ARCHIVE_MODAL', 'ORDERS.CONFIRM_ORDER_BANNER'])
            .then(function (resp) {
                CONFIRM_ORDER_ARCHIVED = resp["ORDERS.CONFIRM_ORDER_ARCHIVED"];
                ARCHIVE_MODAL = resp["ORDERS.ARCHIVE_MODAL"];
                CONFIRM_ORDER_BANNER = resp["ORDERS.CONFIRM_ORDER_BANNER"];
                vm.stage.plural = resp['ENQUIRY.ENQUIRIES'];
                vm.stage.single = resp['ENQUIRY.ENQUIRY'];
            });
        vm.viewState = 'orders.enquiry';
        vm.showPrice = false;
        vm.isFixedHeader = false;
        vm.selectAll = false;
        vm.isManufacturer = userService.isManufacturer();
        vm.previewPricingEnabled = userService.getPreviewPricingEnabled();
        vm.searchFilterChange = searchFilterChange;
        vm.scrollToTop = scrollToTop;
        vm.loadMoreInfiniteScroll = loadMoreInfiniteScroll;
        vm.bulkArchiveOrder = bulkArchiveOrder;
        vm.isAnyOrderSelected = isAnyOrderSelected;
        vm.toggleSelectAll = toggleSelectAll;
        vm.isManufacturer = userService.isManufacturer()
        vm.filterValue = {};
        vm.searchValue = "";
        vm.hidePrice = hidePrice;

        initialize();

        function initialize() {
            hidePrice();
            if (vm.isManufacturer && ('SUBMITTED' || 'PREPARING')) {
                vm.isArchiveActive = true;
            } else {
                vm.isArchiveActive = false;
            }

            $(function () {
                $('[data-toggle="tooltip"]').tooltip();
            });

            getOrders()
        }


        function getOrders() {
            vm.loadingInfiniteScrollData = true;

            ordersService.getEnquiries()
                .then(getOrdersSuccess, serviceCallFailed);
        }

        function getOrdersSuccess(response) {
            vm.allOrders = response.data.orders;
            vm.orders = vm.allOrders.slice(0, 100);
            vm.totalItems = vm.allOrders.length;
            vm.isOrdersLoaded = true;
            vm.loadingInfiniteScrollData = false;
            handleInfiniteScroll();
        }

        function searchFilterChange() {
            updateTotalItemCount();
        }

        function updateTotalItemCount() {
    var filteredItems = $filter('filter')(vm.allOrders, vm.searchValue);
    vm.totalItems = filteredItems.length;
}

        function serviceCallFailed(resp) {
            headerBannerService.setNotification('ERROR', resp.data.error, 10000);
            vm.isOrdersLoaded = true;
            vm.loadingInfiniteScrollData = false;
        }

       var lastScrollTop = 0;
window.addEventListener('scroll', handleInfiniteScroll);

function handleInfiniteScroll() {
    var threshold = 250;
    var scrollTop = window.scrollY;

    if (scrollTop > lastScrollTop) {
        vm.isFixedHeader = scrollTop > threshold;
    } else if (scrollTop < threshold){
        vm.isFixedHeader = false;
    }
    lastScrollTop = scrollTop;  

    
    if (window.innerHeight + scrollTop >= document.body.offsetHeight - 2 && !vm.loadingInfiniteScrollData && !vm.initialLoadDone) {
        loadMoreInfiniteScroll();
    }
}

        function loadMoreInfiniteScroll() {
            vm.loadingInfiniteScrollData = true;

            var nextItems = vm.allOrders.slice(vm.orders.length, vm.orders.length + 100);
            if (nextItems.length === 0) {
                vm.initialLoadDone = true;
                vm.loadingInfiniteScrollData = false;
                return;
            }

            vm.orders = vm.orders.concat(nextItems);

            vm.loadingInfiniteScrollData = false;
            if (vm.orders.length >= vm.totalItems) {
                vm.initialLoadDone = true;
            }
        }

        function scrollToTop() {
            $window.scrollTo({ top: 0, behavior: "smooth" });
            $("html, body").animate({ scrollTop: 0 }, "slow", function () {
                $("#scrollToTop").removeClass("scrolled-past");
            });
        }

        angular.element($window).on("scroll", function () {
            vm.showBackToTopButton = this.pageYOffset > 100;
            $scope.$apply();
        });

        function bulkArchiveOrder() {
            var confirmObject = {
                titleText: CONFIRM_ORDER_ARCHIVED,
                bodyText: ARCHIVE_MODAL
            };

            $uibModal.open({
                templateUrl: 'features/shared/confirmationModal/confirmModal.html',
                controller: 'ConfirmModalController',
                controllerAs: 'confirmModalCtrl',
                size: 'sm',
                resolve: {
                    confirmObject: function () {
                        return confirmObject;
                    }
                }
            }).result.then(function () {
                var selectedOrderIds = vm.orders.filter(order => order.selected).map(order => order.orderId);
                ordersService.bulkArchiveOrder(selectedOrderIds)
                    .then(function () {
                        headerBannerService.setNotification('SUCCESS', CONFIRM_ORDER_BANNER, 2000);
                        $state.reload();
                    });
            }, function () {
                console.log('Modal Cancelled');
            });
        }

        function isAnyOrderSelected() {
            return vm.orders.some(order => order.selected);
        }

        function toggleSelectAll() {
            vm.orders.forEach(function (order) {
                order.selected = vm.selectAll;
            });
        }

          vm.actions = [
            {
                title: "List Viewables",
                onClick: function (entity) { vm.viewOrder(entity.orderId); },
                icon: "fa-eye",
                label: function () { return $translate.instant("ORDERS.VIEW") + " " + vm.stage.single; }
            }
        ];

        if (vm.isManufacturer) {
            vm.actions.push({
                title: "Cancel",
                onClick: function (entity) { vm.cancelOrder(entity.orderId); },
                icon: "fa-window-close",
                label: function () { return $translate.instant("ORDERS.CANCEL_ORDER") + " " + vm.stage.single; }
            });
        }

        function hidePrice() {
            if (vm.isManufacturer) {
                vm.hidePrice = false;
            } else {
                vm.hidePrice = !userService.getPreviewPricingEnabled();
            }
            return vm.hidePrice;
        }
    }
})();
