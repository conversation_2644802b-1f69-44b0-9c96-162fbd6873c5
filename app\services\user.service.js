(function () {
        'use strict';

        angular
            .module('app.services')
            .factory('userService', userService);

        userService.$inject = ['$http', 'apiConstants', 'tokenService', '$rootScope', '$location', '$state', '$q', '$stateParams'];

        function userService($http, apiConstants, tokenService, $rootScope, $location, $state, $q, $stateParams) {
            return {
                getUserInfo: getUserInfo,
                getUserLoginInfo: getUserLoginInfo,
                removeUserInfoFromStorage: removeUserInfoFromStorage,
                setRememberMe: setRememberMe,
                clearRememberMe: clearRememberMe,
                getRememberMeUsername: getRememberMeUsername,
                isUserLoggedIn: isUserLoggedIn,
                forgotPassword: forgotPassword,
                resetPassword: resetPassword,
                resendPassword: resendPassword,
                getUserId: getUserId,
                getFirstName: getFirstName,
                getLastName: getLastName,
                getFullName: getFullName,
                getEmailAddress: getEmailAddress,
                getUserType: getUserType,
                getUserPermissions: getUserPermissions,
                getManufacturerId: getManufacturerId,
                getManufacturerSubEntityId: getManufacturerSubEntityId,
                isManufacturer: isManufacturer,
                isManufacturerSubEntity: isManufacturerSubEntity,
                isDealerPlusUser: isDealerPlusUser,
                isDealerPlusCustomer: isDealerPlusCustomer,
                hasOrderRole: hasOrderRole,
                hasProductsRole: hasProductsRole,
                hasPublishedProductsRole: hasPublishedProductsRole,
                hasPublicationRole: hasPublicationRole,
                hasCustomerRole: hasCustomerRole,
                hasAdminRole: hasAdminRole,
                hasSecurityRole: hasSecurityRole,
                hasManufacturerInformationRole: hasManufacturerInformationRole,
                hasPartsRole: hasPartsRole,
                hasPartSearchRole: hasPartSearchRole,
                hasDashboardRole: hasDashboardRole,
                hasSupportRole: hasSupportRole,
                getInternalUsers: getInternalUsers,
                getCustomerUsers: getCustomerUsers,
                sendResetPassword: sendResetPassword,
                getUserLanguages: getUserLanguages,
                getPreviewStockLevelEnabled: getPreviewStockLevelEnabled,
                getPreviewPricingEnabled: getPreviewPricingEnabled,
                getDashboardEnabled: getDashboardEnabled,
                getPartSearchEnabled: getPartSearchEnabled,
                getCanHideIsolate: getCanHideIsolate,
                getCanExplode: getCanExplode,
                getDefaultCurrency: getDefaultCurrency,
                getDPCustomerDefaultCurrency: getDPCustomerDefaultCurrency,
                getBackgroundColour: getBackgroundColour,
                setBackgroundColour: setBackgroundColour,
                getEdgingEnabledDefault: getEdgingEnabledDefault,
                setEdgingEnabledDefault: setEdgingEnabledDefault,
                getAddressCreationDefault: getAddressCreationDefault,
                getContactUsPageEnabled: getContactUsPageEnabled,
                setContactUsPageEnabled: setContactUsPageEnabled,
                getRequiredSerialNumber: getRequiredSerialNumber,
                setRequiredSerialNumber: setRequiredSerialNumber,
                getTermsConditions: getTermsConditions,
                setTermsConditions: setTermsConditions,
                getEnquiryPurchaseOrder: getEnquiryPurchaseOrder,
                setEnquiryPurchaseOrder: setEnquiryPurchaseOrder,
                getEnquiriesOnly: getEnquiriesOnly,
                getExternallyProcessed: getExternallyProcessed,
                getDisplayManufacturerSubEntityId: getDisplayManufacturerSubEntityId,
                setDisplayManufacturerSubEntityId: setDisplayManufacturerSubEntityId,
                getCustomerSearchManualsOnly: getCustomerSearchManualsOnly,
                getSearchOnlyPartNumbers: getSearchOnlyPartNumbers,
                setCustomerSearchManualsOnly: setCustomerSearchManualsOnly,
                getManufacturerSubEntitySettings: getManufacturerSubEntitySettings,
                getSoftCopyEnabled: getSoftCopyEnabled,
                getPartialShippingEnabled: getPartialShippingEnabled,
                getPriceListsEnabled: getPriceListsEnabled,
                getDisableDiscountEditing: getDisableDiscountEditing,
                getCurrencyForUser: getCurrencyForUser,
                getCurrencyForSubEntity: getCurrencyForSubEntity,
                isOnBehalfOf: isOnBehalfOf,
                getOnBehalfOfUserId: getOnBehalfOfUserId,
                getEnquiryPOToLiveOrderEnabled: getEnquiryPOToLiveOrderEnabled,
                getPaymentEnabled: getPaymentEnabled,
                getStockWarehousesEnabled: getStockWarehousesEnabled,
                getAdditionalPartsEnabled: getAdditionalPartsEnabled,
                getWarehouseId: getWarehouseId,
                getTaxPayments: getTaxPayments,
                getCurrencyList: getCurrencyList,
                getCurrencyData: getCurrencyData,
                getOnBehalfOfUser: getOnBehalfOfUser,
                isCDE: isCDE,
                isFarmer: isFarmer,
                isSupreme: isSupreme,
                getOverrideForDirectOrdersEnabled: getOverrideForDirectOrdersEnabled,
                getManufacturerSubEntity: getManufacturerSubEntity,
                getHideUnpublishedParts: getHideUnpublishedParts,
                getSingleManufacturerUser: getSingleManufacturerUser,
                getManufacturerSubEntities: getManufacturerSubEntities,
                getManufacturerUsers: getManufacturerUsers,
                getNotificationSubscriptions: getNotificationSubscriptions,
                updateNotificationSubscriptions: updateNotificationSubscriptions,
                getAutoPublishable : getAutoPublishable
            };


            function getUserInfo(userId) {
                return new Promise(function(resolve) {
                    return $http.get(apiConstants.url + '/user').then((response)=>{
                        setUserInfoInLocalStorage(response);
                        resolve();
                    });
                });
            }

            function getUserLoginInfo() {
                return new Promise(function(resolve) {
                    var siteUrl = $location.protocol() + '://' + $location.host();
                    var config = {headers: {'Site-Url': siteUrl}};
                    tokenService.getOauthToken().then((token)=>{
                        config.headers.Authorization = 'Bearer ' + token;
                        resolve($http.get(apiConstants.url + '/user/login', config));
                    });
                });
            }

            function setUserInfoInLocalStorage(response) {
                localStorage.setItem("User Info", angular.toJson(response.data));
                $rootScope.$broadcast("User-Info-Updated");
            }

            function removeUserInfoFromStorage() {
                localStorage.removeItem("User Info");
                $rootScope.$broadcast("User-Info-Updated");
            }

            function getStoredUserInfo() {
                return angular.fromJson(localStorage.getItem("User Info"));
            }

            function getRememberMeUsername() {
                var email = localStorage.getItem('Remember Me Email');
                return email;
            }

            function setRememberMe(email) {
                localStorage.setItem('Remember Me Email', email);
            }

            function clearRememberMe() {
                localStorage.removeItem('Remember Me Email');
            }

            function isUserLoggedIn() {
                return new Promise(function(resolve) {
                    if (tokenService.isOauthTokenValid()) 
                        resolve(true);
                    else {
                        tokenService.refreshOauthToken().then(()=>{
                            var valid = tokenService.isOauthTokenValid();
                            resolve(valid);
                        }, () => {
                            resolve(false);
                        });
                    }
                });
            }

            function getUserId() {
                var userInfo = getStoredUserInfo();
                return userInfo.userId;
            }

            function getOnBehalfOfUser(userId) {
                return $http.get(apiConstants.url + '/user/' + userId.toString())
            }

            function getFirstName() {
                var userInfo = getStoredUserInfo();
                return userInfo.firstName;
            }

            function getLastName() {
                var userInfo = getStoredUserInfo();
                return userInfo.lastName;
            }

            function getFullName() {
                var userInfo = getStoredUserInfo();
                return userInfo.firstName + " " + userInfo.lastName;
            }

            function getEmailAddress() {
                var userInfo = getStoredUserInfo();
                return userInfo.emailAddress;
            }

            function getUserType() {
                var userInfo = getStoredUserInfo();
                return userInfo ? userInfo.userType : null;
            }

            function getUserPermissions() {
                var userInfo = getStoredUserInfo();
                if(userInfo !== null)
                    return userInfo.userPermissions;
                else
                    return null;
            }

            function getManufacturerId() {
                var userInfo = getStoredUserInfo();
                    if (isManufacturer()) {
                        return userInfo.manufacturerId;
                    } else {
                        return userInfo.associatedManufacturerId;
                    }
            }


            function getManufacturerSubEntityId() {
                var userInfo = getStoredUserInfo();
                return userInfo.manufacturerSubEntityId;
            }

            function isManufacturer() {
                var userType = getUserType();
                return userType === "MANUFACTURER";
            }

            function isManufacturerSubEntity() {
                var userType = getUserType();
                return userType === "MANUFACTURER_SUB_ENTITY_DEALER" || userType === "MANUFACTURER_SUB_ENTITY_CUSTOMER" || userType === "MANUFACTURER_SUB_ENTITY_DEALER_PLUS";
            }

            function isDealerPlusUser() {
                var userType = getUserType();
                return userType === "MANUFACTURER_SUB_ENTITY_DEALER_PLUS";
            }

            function isFarmer() {
                var userType = getUserType();
                return userType === "MANUFACTURER_SUB_ENTITY_CUSTOMER";
            }

            function isDealerPlusCustomer() {
                var userInfo = getStoredUserInfo();
                return isManufacturerSubEntity() && userInfo.parentSubEntityId !== undefined;
            }

            function hasRole(role) {
                var permissions = getUserPermissions();
                return(permissions !== null && permissions.indexOf(role) > -1);
            }

            function hasOrderRole() {
                return hasRole("Order");
            }

            function hasProductsRole() {
                return hasRole("Products");
            }

            function hasPublishedProductsRole() {
                return hasRole("PublishedProducts");
            }

            function hasPublicationRole() {
                return hasRole("Publication");
            }

            function hasCustomerRole() {
                return hasRole("Customer");                                    
            }

            function hasAdminRole() {
                return hasRole("Admin");
            }

            function hasSecurityRole() {
                return hasRole("Security");
            }

            function hasManufacturerInformationRole() {
                return hasRole("ManufacturerInformation");
            }

            function hasPartsRole() {
                return hasRole("Parts");
            }

            function hasPartSearchRole() {
                return hasRole("PartSearch");
            }

            function hasDashboardRole() {
                return hasRole("Dashboard");
            }

            function hasSupportRole() {
                return hasRole("Support");
            }

            function forgotPassword(emailId, manufacturerId) {
                var emailData = {"emailAddress": emailId, "manufacturerId": manufacturerId};
                var siteUrl = $location.protocol() + '://' + $location.host();
                var config = {headers: {'Site-Url': siteUrl}};
                return $http.post(apiConstants.url + '/user/password/forgot', emailData, config);
            }

            function resetPassword(newPassword, recoveryCode) {
                var resetData = {
                    newPassword: newPassword,
                    resetCode: recoveryCode
                };
                return $http.post(apiConstants.url + '/user/password/reset', resetData);
            }

            function resendPassword(recoveryCode) {
                var siteUrl = $location.protocol() + '://' + $location.host();
                var config = {
                    headers: {'Site-Url': siteUrl}
                }
                var resendData = {
                    resetCode: recoveryCode
                };
                return $http.post(apiConstants.url + '/user/password/resend', resendData, config);
            }

            function getInternalUsers() {
                return $http.get(apiConstants.url + '/user/manufacturer/' + getManufacturerId())
            }
            function getManufacturerUsers() {
                return $http.get(apiConstants.url + '/manufacturers/' + getManufacturerId() + '/users');
            }
            function getSingleManufacturerUser(manufacturerId, userId) {
                return $http.get(apiConstants.url + '/manufacturers/' + manufacturerId + '/users/' + userId)
            }
            function getNotificationSubscriptions(manufacturerId, userId) {
                return $http.get(apiConstants.url + '/manufacturers/' + manufacturerId + '/users/' + userId + '/notification-subscriptions')
                    .then(function(response) {
                        return response.data.notificationSubscriptions;
                    })
                    .catch(function(error) {
                        throw error;
                    });
            }

            function updateNotificationSubscriptions(manufacturerId, userId, subscriptions) {
                return $http.patch(apiConstants.url + '/manufacturers/' + manufacturerId + '/users/' + userId, {
                    notificationSubscriptions: subscriptions
                });
            }

            function getManufacturerSubEntities() {
                return $http.get(apiConstants.url + '/manufacturer/' + getManufacturerId() + '/manufacturersubentities')
            }

            function getCustomerUsers(subEntityId) {
                return $http.get(apiConstants.url + '/user/manufacturerSubEntity/' + subEntityId)
            }

            function sendResetPassword(userId) {
                var siteUrl = $location.protocol() + '://' + $location.host();
                var config = {headers: {'Site-Url': siteUrl}};

                var theme = localStorage.getItem("theme");
                if (theme && theme != null) {
                    return $http.get(apiConstants.url + '/user/' + userId + '/password/reset?theme=' + theme.toLowerCase(), config);
                } else {
                    return $http.get(apiConstants.url + '/user/' + userId + '/password/reset', config);
                }
            }

            function getUserLanguages() {
                var userInfo = getStoredUserInfo();
                return (userInfo !== null && userInfo.languages) ? userInfo.languages : null;
            }

            function getPreviewStockLevelEnabled() {
                var userInfo = getStoredUserInfo();
                if (isManufacturer() || isOnBehalfOf) {
                    return userInfo.manufacturerSettings ? userInfo.manufacturerSettings.previewStockLevelEnabled : false;
                } else {
                    return userInfo.manufacturerSubEntitySettings ? userInfo.manufacturerSubEntitySettings.previewStockLevelEnabled : false;
                }

            }

            function getPreviewPricingEnabled() {
                var userInfo = getStoredUserInfo();
                if (isManufacturer() && isOnBehalfOf) {
                    return userInfo.manufacturerSettings ? userInfo.manufacturerSettings.previewPricingEnabled : false;
                } else {
                    return userInfo.manufacturerSubEntitySettings ? userInfo.manufacturerSubEntitySettings.previewPricingEnabled : false;
                }
            }

            function getDashboardEnabled() {
                var userInfo = getStoredUserInfo();
                return userInfo.manufacturerSettings ? userInfo.manufacturerSettings.dashboardEnabled : false;
            }

            function getPartSearchEnabled() {
                var userInfo = getStoredUserInfo();
                return userInfo.manufacturerSettings ? userInfo.manufacturerSettings.partSearchEnabled : false;
            }

            function getCanHideIsolate() {
                var userInfo = getStoredUserInfo();
                return userInfo.manufacturerSettings ? userInfo.manufacturerSettings.hideIsolateEnabled : false;
            }

            function getCanExplode() {
                var userInfo = getStoredUserInfo();
                return userInfo.manufacturerSettings ? userInfo.manufacturerSettings.customerExplodeEnabled : false;
            }

            function getBackgroundColour() {
                var userInfo = getStoredUserInfo();
                return userInfo.manufacturerSettings ? userInfo.manufacturerSettings.viewerColour : "rgb(135, 206, 250)";
            }

            function setBackgroundColour(colour) {
                var userInfo = getStoredUserInfo();
                userInfo.manufacturerSettings.viewerColour = colour;
                localStorage.setItem('User Info', angular.toJson(userInfo));
            }

            function getContactUsPageEnabled() {
                var userInfo = getStoredUserInfo();
                return userInfo.manufacturerSettings.contactUsPageEnabled;
            }

            function setContactUsPageEnabled(enabled) {
                var userInfo = getStoredUserInfo();
                userInfo.manufacturerSettings.contactUsPageEnabled = enabled;
                localStorage.setItem('User Info', angular.toJson(userInfo));
            }

            function getEdgingEnabledDefault() {
                var userInfo = getStoredUserInfo();
                return userInfo.manufacturerSettings.edgingEnabledDefault;
            }

            function setEdgingEnabledDefault(enabled) {
                var userInfo = getStoredUserInfo();
                userInfo.manufacturerSettings.edgingEnabledDefault = enabled;
                localStorage.setItem('User Info', angular.toJson(userInfo));
            }

            function getAddressCreationDefault() {
                var userInfo = getStoredUserInfo();
                return userInfo.manufacturerSettings.addressCreationEnabled;
            }

            function getRequiredSerialNumber() {
                var userInfo = getStoredUserInfo();
                return userInfo.manufacturerSettings.requiredSerialNumber;
            }

            function setRequiredSerialNumber(requiredSerialNumber) {
                var userInfo = getStoredUserInfo();
                userInfo.manufacturerSettings.requiredSerialNumber = requiredSerialNumber;
                localStorage.setItem('User Info', angular.toJson(userInfo));
            }

            function getTermsConditions() {
                var userInfo = getStoredUserInfo();
                return userInfo.manufacturerSettings.termsAndConditionsUrl;
            }

            function setTermsConditions(termsConditions) {
                var userInfo = getStoredUserInfo();
                userInfo.manufacturerSettings.termsAndConditionsUrl = termsConditions;
                localStorage.setItem('User Info', angular.toJson(userInfo));
            }

            function getEnquiryPurchaseOrder() {
                var userInfo = getStoredUserInfo();
                return userInfo.manufacturerSettings.enquiryPurchaseOrder;
            }

            function setEnquiryPurchaseOrder(enquiryPurchaseOrder) {
                var userInfo = getStoredUserInfo();
                userInfo.manufacturerSettings.enquiryPurchaseOrder = enquiryPurchaseOrder;
                localStorage.setItem('User Info', angular.toJson(userInfo));
            }

            function getEnquiriesOnly() {
                var userInfo = getStoredUserInfo();
                return (userInfo !== null && userInfo.manufacturerSettings.enquiriesOnly);
            }

            function getExternallyProcessed() {
                var userInfo = getStoredUserInfo();
                return userInfo.manufacturerSettings.externallyProcessed;
            }

            function getDisplayManufacturerSubEntityId() {
                var userInfo = getStoredUserInfo();
                return userInfo.manufacturerSettings.displayManufacturerSubEntityId;
            }

            function setDisplayManufacturerSubEntityId(displayManufacturerSubEntityId) {
                var userInfo = getStoredUserInfo();
                userInfo.manufacturerSettings.displayManufacturerSubEntityId = displayManufacturerSubEntityId;
                localStorage.setItem('User Info', angular.toJson(userInfo));
            }

            function getCustomerSearchManualsOnly() {
                var userInfo = getStoredUserInfo();
                return userInfo.manufacturerSettings.customerSearchManualsOnly;
            }

            function getSearchOnlyPartNumbers() {
                var userInfo = getStoredUserInfo();
                return userInfo.manufacturerSettings.searchOnlyPartNumbers && isManufacturerSubEntity();
            }

            function setCustomerSearchManualsOnly(customerSearchManualsOnly) {
                var userInfo = getStoredUserInfo();
                userInfo.manufacturerSettings.customerSearchManualsOnly = customerSearchManualsOnly;
                localStorage.setItem('User Info', angular.toJson(userInfo));
            }

            function getDefaultCurrency() {
                var userInfo = getStoredUserInfo();
                var defaultCurrency = userInfo.manufacturerSettings ? userInfo.manufacturerSettings.defaultCurrency : null;

                if(userInfo.manufacturerSubEntitySettings && userInfo.parentSubEntityId !== null){
                    defaultCurrency = userInfo.manufacturerSubEntitySettings.defaultCurrency ? userInfo.manufacturerSubEntitySettings.defaultCurrency : defaultCurrency
                }
                return defaultCurrency;
            }

            function getDPCustomerDefaultCurrency(){
                var userInfo = getStoredUserInfo();

                var defaultCurrency = userInfo.manufacturerSettings ? userInfo.manufacturerSettings.defaultCurrency : null;
                defaultCurrency = userInfo.manufacturerSubEntitySettings.childCurrency ? userInfo.manufacturerSubEntitySettings.childCurrency : defaultCurrency;
                return defaultCurrency;
            }

            function getManufacturerSubEntitySettings(subEntityId) {
                return $http.get(apiConstants.url + '/manufacturersubentity/' + subEntityId + '/settings')
            }

            function getManufacturerSubEntity(manufacturerSubEntityId) {
                return $http.get(apiConstants.url + '/manufacturersubentity/' + manufacturerSubEntityId);
            }

            function getSoftCopyEnabled() {
                var userInfo = getStoredUserInfo();
                return userInfo.manufacturerSettings.softCopyEnabled;
            }

            function isCDE() {
                return (apiConstants.cdeId).includes(getManufacturerId());
            }

            function isSupreme() {
                    return (apiConstants.supremeId).includes(getManufacturerId());
            }

            function getOverrideForDirectOrdersEnabled() {
                var userType = getUserType();
                if (isSupreme() && userType === "MANUFACTURER_SUB_ENTITY_DEALER" || isSupreme() && isManufacturer() && isOnBehalfOf()) {
                    var userInfo = getStoredUserInfo();
                    return userInfo.manufacturerSettings.overrideForDirectOrders;
                }
            }

            function getPartialShippingEnabled() {
                var userInfo = getStoredUserInfo();
                return userInfo.manufacturerSettings.partialShippingEnabled;
            }

            function getPriceListsEnabled() {
                var userInfo = getStoredUserInfo();
                return userInfo.manufacturerSettings.priceListsEnabled;
            }

            function getCurrencyForUser(userId, isDealerPlus) {
                if (isDealerPlus) {
                    return $http.get(apiConstants.url + '/dealerplus/user/' + userId + '/currency');
                } else {
                    return $http.get(apiConstants.url + '/user/' + userId + '/currency');
                }
            }

            function getCurrencyForSubEntity(subEntityId, isDealerPlus) {
                if(isDealerPlus){
                    return $http.get(apiConstants.url + '/dealerplus/manufacturersubentity/' + subEntityId + '/currency');
                }else {
                    return $http.get(apiConstants.url + '/manufacturersubentity/' + subEntityId + '/currency');
                }
            }

            function isOnBehalfOf() {
                return $state.params.onBehalfOf && $state.params.onBehalfOf !== "null"
            }

            function getOnBehalfOfUserId() {
                if ($state.params.onBehalfOf && $state.params.onBehalfOf !== "null") {
                    return JSON.parse(decodeURIComponent(atob($stateParams.onBehalfOf))).userId;
                }
            }

            function getEnquiryPOToLiveOrderEnabled() {
                var userInfo = getStoredUserInfo();
                return userInfo.manufacturerSettings.enquiryPOToLiveEnabled;
            }

            function getPaymentEnabled() {
                var userInfo = getStoredUserInfo();
                if (isManufacturer() && isOnBehalfOf) {
                    return userInfo.manufacturerSettings.paymentEnabled;
                } else {
                    return userInfo.manufacturerSettings.paymentEnabled && isManufacturerSubEntity();
                }
            }

            function getStockWarehousesEnabled() {
                var userInfo = getStoredUserInfo();
                if (isManufacturer() && isOnBehalfOf) {
                    return userInfo.manufacturerSettings.stockWarehousesEnabled;
                } else {
                    return userInfo.manufacturerSettings.stockWarehousesEnabled && isManufacturerSubEntity();
                }
            }

            function getAdditionalPartsEnabled() {
                var userInfo = getStoredUserInfo();
                return userInfo.manufacturerSettings.additionalPartsEnabled && isManufacturerSubEntity();
            }

            function getTaxPayments() {
                var userInfo = getStoredUserInfo();
                return userInfo.manufacturerSubEntitySettings ? userInfo.manufacturerSubEntitySettings.taxPayments : false;
            }

            function getDisableDiscountEditing(){
                var userInfo = getStoredUserInfo();
                return userInfo.userSettings ? userInfo.userSettings.disableDiscountEditing : true;
            }

            function getWarehouseId() {
                var userInfo = getStoredUserInfo();
                return userInfo.manufacturerSubEntitySettings ? userInfo.manufacturerSubEntitySettings.wareHouseId : null;
            }

            function getCurrencyList() {
                var userInfo = getStoredUserInfo();
                return userInfo.currencies || [];
            }

            function getCurrencyData(data) {
                const currency = data;
                console.log(currency);
                const currencyList = getCurrencyList();
                const result = currencyList.find(item => item.id === data.id);
                currency.display = result.display;
                return currency;
            }

            function getHideUnpublishedParts() {
                var userInfo = getStoredUserInfo();
                return userInfo.manufacturerSettings.hideUnpublishedParts;
            }

            function getAutoPublishable() {
                var userInfo = getStoredUserInfo();
                return userInfo.manufacturerSettings.autoPublishable;
            }

        }
    }

)();