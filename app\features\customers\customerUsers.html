<section class="body-content">
  <div class="order-details-holder">
    <p class="mb-4"><a href="" class="dark-secondary" ng-click="customerUserCtrl.goToCustomers()"><i class="fa fa-caret-left"></i> {{"CUSTOMERS.BACK_TO_ALL_CUSTOMERS" | translate}}</a></p>
    <h1>{{customerUserCtrl.subEntityname}} {{"CUST_USER.TITLE" | translate}}</h1>
    <div translate>CUST_USER.SUBTITLE</div>
  </div>
</section>

  <section class="responsiveContainer m-5">
    <div class="">

      <div class="success-alert" ng-if="customerUserCtrl.successMessage != ''">
        {{customerUserCtrl.successMessage}}
      </div>

    <div id="{{customerUserCtrl.isFixedHeader ? 'infiniteScrollFixedHeader' : 'infiniteScrollStaticHeader'}}"
      class="flex p-4 p-md-0">
      <search-filter class="col-12 col-md-3" state-name="'customersUsers'" value="customerUserCtrl.searchValue"
        placeholder-key="CUST_USER.SEARCH_BY_NAME"></search-filter>
      
      <button class="btn primary ml-auto mr-4 col-12 col-md-auto create-machine mt-3 mt-md-0"
        ng-click="customerUserCtrl.createUser()" ng-if="!customerUserCtrl.isFarmerUser" translate>CUST_USER.ADD_USER
      </button>
    </div>

  <table class="table table-bordered">

    <thead>
    <tr>
      <th ng-class="customerUserCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'" ng-click="customerUserCtrl.customer_sort='firstName'; customerUserCtrl.sortReverse = !customerUserCtrl.sortReverse" translate>CUST_USER.NAME</th>
      <th ng-class="customerUserCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'"
          ng-click="customerUserCtrl.customer_sort='emailAddress'; customerUserCtrl.sortReverse = !customerUserCtrl.sortReverse" translate>CUST_USER.EMAIL</th>
      <th ng-class="customerUserCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'"
          ng-click="customerUserCtrl.customer_sort='createdDate'; customerUserCtrl.sortReverse = !customerUserCtrl.sortReverse" translate>CUST_USER.CREATED_DATE</th>

      <th ng-class="customerUserCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'"
          ng-click="customerUserCtrl.customer_sort='passwordSet'; customerUserCtrl.sortReverse = !customerUserCtrl.sortReverse" translate>CUST_USER.USER_STATUS</th>
      <th translate>CUST_USER.ACTIONS</th>
    </tr>
    </thead>

    <tbody infinite-scroll="customerUserCtrl.loadMoreInfiniteScroll()" infinite-scroll-distance="3" infinite-scroll-disabled="customerUserCtrl.loadingInfiniteScrollData">
    <tr class="p-4" ng-repeat="user in customerUserCtrl.userList | orderBy:customerUserCtrl.customer_sort:customerUserCtrl.sortReverse | filter : customerUserCtrl.searchValue" ng-class="{'last-item': $last}"
        ng-show="customerUserCtrl.userList.length > 0">
      <td data-label="{{'CUST_USER.NAME' | translate}}">{{user.firstName}} {{user.lastName}}</td>
      <td data-label="{{'CUST_USER.EMAIL' | translate}}">{{user.emailAddress}}</td>
      <td data-label="{{'CUST_USER.CREATED_DATE' | translate}}">{{user.createdDate | date : "d MMMM y"}}</td>
      <td data-label="{{'CUST_USER.USER_STATUS' | translate}}">{{user.userStatus}}</td>
      
      <td class="has-dropdown mobile-right-aligned-btn" ng-if="!customerUserCtrl.isCDEUser">
        <div class="btn-group" ng-hide="user.userId === adminCtrl.userId">
          <a href="" class="btn xsmall secondary main-action" ng-click="customerUserCtrl.editUser(user)" translate>CUST_USER.EDIT_USER</a>
          <div href="" class="btn xsmall secondary dropdown-toggle" data-toggle="dropdown"
               aria-haspopup="true" aria-expanded="false">
            <div class="sub-popup">
              <ul class="more-options">
                <li title="Edit User">
                  <a href="" class="dark-secondary" ng-click="customerUserCtrl.editUser(user)"><i class="fa fa-fw fa-pencil"></i> {{"CUST_USER.EDIT" | translate}}</a>
                </li>
                <li title="Reset Password">
                  <a href="" class="dark-secondary" ng-click="customerUserCtrl.resetPassword(user)"><i class="fa fa-fw fa-key"></i> {{"CUST_USER.RESET_PASS" | translate}}</a>
                </li>
                <li title="Create Order">
                  <a href="" class="dark-secondary" ng-if="customerUserCtrl.userList[0].userPermissions.indexOf('Order') !== -1" ng-click="customerUserCtrl.createOrder(user)"><i class="fa fa-fw fa-user"></i> {{"CUST_USER.CREATE_ORDER" | translate}}</a>
                </li>
                <li title="Delete">
                  <a href="" class="delete" ng-click="customerUserCtrl.deleteUser(user)"><i class="fa fa-fw fa-trash"></i> {{"CUST_USER.DELETE" | translate}}</a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </td>
      <td ng-if="customerUserCtrl.isCDEUser">
        <button class="btn primary" ng-click="customerUserCtrl.createOrder(user)" translate>
          CUST_USER.CREATE_ORDER
        </button>
      </td>



    </tr>

    <tr ng-show="!customerUserCtrl.userList.length > 0">
      <td colspan="5" translate>CUST_USER.NO_USERS</td>
    </tr>

    <tr ng-hide="customerUserCtrl.areUsersLoaded" align="center">
      <td class="preloader" colspan="4"><img ng-src="images/cadpreloader.gif" class="ajax-loader" height="60" width="60"/></td>
    </tr>
    </tbody>
  </table>

  <span ng-click="customerUserCtrl.scrollToTop()" id="backToTopBtn" title="Go to top" class="fas fa-arrow-alt-circle-up"
    ng-show="customerUserCtrl.showBackToTopButton"></span>


  </div>
</section>

