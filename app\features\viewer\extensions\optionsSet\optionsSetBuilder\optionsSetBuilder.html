<div class="sidebar-content" ng-show="optionSetBuilderCtrl.isOpen">
    <p translate>OPTION_SET_BUILDER.TITLE_DESC</p>

    <button class="btn primary" ng-click="optionSetBuilderCtrl.createOptionSet()" translate>OPTION_SET_BUILDER.CREATE_NEW</button>

    <h4 translate>OPTION_SET_BUILDER.MANAGE</h4>

    <table ng-show="optionSetBuilderCtrl.existingOptionsSets.length > 0" class="tableViewer table-bordered w-100 bg-white ml-0">
        <tbody>
        <tr ng-repeat="optionsSet in optionSetBuilderCtrl.existingOptionsSets">
            <td class="side-menu-table-name">
                {{optionsSet.partNumber}}

                <small><strong>{{optionsSet.description}}</strong></small>
            </td>
            <td class="has-dropdown">
                <div class="btn-group">
                    <a href="" class="btn xsmall secondary main-action"
                       ng-click="optionSetBuilderCtrl.editOptionSet(optionsSet)" translate>
                        OPTION_SET_BUILDER.EDIT
                    </a>
                    <div href="" class="btn xsmall secondary dropdown-toggle" data-toggle="dropdown"
                         aria-haspopup="true" aria-expanded="false">
                        <div class="sub-popup">
                            <ul class="more-options">
                                <li title="Edit">
                                    <a href="" class="dark-secondary"
                                       ng-click="optionSetBuilderCtrl.editOptionSet(optionsSet)"><i
                                            class="fa fa-fw fa-pencil"></i> {{"OPTION_SET_BUILDER.EDIT" | translate}}</a>
                                </li>
                                <li title="Delete">
                                    <a href="" class="delete"
                                       ng-click="optionSetBuilderCtrl.deleteOptionSet(optionsSet)"><i
                                            class="fa fa-fw fa-trash"></i> {{"OPTION_SET_BUILDER.DELETE" | translate}}</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </td>
        </tr>
        </tbody>
    </table>

    <p ng-hide="optionSetBuilderCtrl.existingOptionsSets.length > 0" translate>
        OPTION_SET_BUILDER.NO_OPTIONS
    </p>

</div>