<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" ng-click="createCourierDetailsCtrl.cancel()" aria-label="Close">
        <i class="fa fa-close" aria-hidden="true"></i>
    </button>

    <h2 class="modal-title">{{'CREATE_ORDER.COURIER_DETAILS' | translate}}</h2>

    <h3 ng-if="createCourierDetailsCtrl.hasErrorMessage" class="error-alert">
        {{createCourierDetailsCtrl.errorMessage}}
    </h3>
</div>

<div class="modal-body">
    <form class="form" name="createCompanyForm">

        <div class="input-group">
            <label translate>CREATE_ORDER.PREFERRED_COURIER</label>
            <input type="text" placeholder="{{'CREATE_ORDER.ENTER_COURIER_NAME' | translate}}" ng-model="createCourierDetailsCtrl.preferredCourier" ng-required="true" required>
        </div>

        <div class="input-group">
            <label translate>CREATE_ORDER.ACCOUNT_NUMBER</label>
            <input type="text" placeholder="{{'CREATE_ORDER.ENTER_ACCOUNT_NUMBER' | translate}}" ng-model="createCourierDetailsCtrl.courierNumber" ng-required="true">
        </div>

        <div class="modal-actions my-4 mr-4">
            <button type="button" class="btn secondary ml-0 ml-lg-2" data-dismiss="modal"
                    ng-click="createCourierDetailsCtrl.cancel()" translate>
                GENERAL.CANCEL
            </button>
            <button type="button" class="btn primary ml-0 mt-lg-0"
                    ng-disabled="!createCompanyForm.$valid || createCourierDetailsCtrl.isDisabled"
                    ng-click="createCourierDetailsCtrl.saveCourierDetails()" translate>
                CREATE_ORDER.SAVE_CHANGES
            </button>
        </div>

    </form>
</div>
