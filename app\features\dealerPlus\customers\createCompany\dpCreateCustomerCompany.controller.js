(function () {
    'use strict';

    angular
        .module('app.customer')
        .controller('DPCreateCustomerCompanyController', DPCreateCustomerCompanyController);

    DPCreateCustomerCompanyController.$inject = ['dpCreateUserService', '$uibModalInstance'];

    function DPCreateCustomerCompanyController(dpCreateUserService, $uibModalInstance) {
        var vm = this;

        vm.cancel = $uibModalInstance.dismiss;

        vm.createNewCompany = createNewCompany;

        function createNewCompany() {
            vm.isDisabled = true;

            if (vm.type === "Dealer") {
                dpCreateUserService.createManufacturerSubEntityDealer(vm.newCompanyName, vm.defaultDiscount)
                    .then(createCustomerSuccess, createCustomerFailed);
            } else {
                dpCreateUserService.createManufacturerSubEntityCustomer(vm.newCompanyName, vm.defaultDiscount)
                    .then(createCustomerSuccess, createCustomerFailed);
            }
        }

        function createCustomerSuccess() {
            $uibModalInstance.close();
        }

        function createCustomerFailed(response) {
            vm.companyFailure = true;
            vm.isDisabled = false;
            vm.internalFailureMessage = response.data.message;
        }

    }
})();