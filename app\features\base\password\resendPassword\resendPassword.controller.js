(function () {
    'use strict';

    angular
        .module('app.base')
        .controller('ResendPasswordController', ResendPasswordController);

    ResendPasswordController.$inject = ['$stateParams', 'userService', '$rootScope', '$location', '$sce', '$translate'];

    function ResendPasswordController($stateParams, userService, $rootScope, $location, $sce, $translate) {
        var vm = this;

        vm.showSuccessMessage = false;
        vm.recoveryCode = "";
        vm.errorMessage = "";

        var INVALID_CODE = "";
        $translate(['PASSWORD.INVALID_CODE'])
            .then(function (resp) {
                INVALID_CODE = resp["PASSWORD.INVALID_CODE"];
            });

        initialize();

        function initialize() {
            checkBrowserCompatibility();
            vm.recoveryCode = $stateParams.code ? $stateParams.code : "";
            if (vm.recoveryCode != null && vm.recoveryCode != "") {
                userService.resendPassword(vm.recoveryCode)
                    .then(resendPasswordSuccess, resendPasswordFailed);
            }
        }

        function resendPasswordSuccess(response) {
            vm.showAsAlert = response.data;
            vm.showSuccessMessage = response.data;
        }

        function resendPasswordFailed(response) {
            vm.showSuccessMessage = false;
            vm.errorMessage = response.data.message === "Invalid reset code" ? $sce.trustAsHtml(INVALID_CODE) : response.data.message;
        }

        function checkBrowserCompatibility() {
            var ua = window.navigator.userAgent;
            var msie = ua.indexOf("MSIE ");

            vm.browserIE = msie > 0 || !!navigator.userAgent.match(/Trident.*rv\:11\./);
        }
    }
})();
