(function () {
    'use strict';

    angular
        .module('app.viewer')
        .controller('PartSearchModalController', PartSearchModalController);

    PartSearchModalController.$inject = ['$uibModalInstance', 'orderDetails', 'ordersService', 'userService', 'dpOrdersService', '$state'];

    function PartSearchModalController($uibModalInstance, orderDetails, ordersService, userService, dpOrdersService, $state) {
        var vm = this;

        vm.onAddClicked = onAddClicked;
        vm.cancel = cancel;

        vm.subEntityId = orderDetails.manufacturerSubEntityId;
        vm.userId = orderDetails.userId;

        function onAddClicked(masterPart, masterKit) {
            if (!masterPart && !masterKit) {
                console.error('Both masterPart and masterKit are null or undefined');
                return;
            }

            var masterKitId = masterKit ? masterKit.kitId : null;
            var masterPartId = masterPart ? masterPart.masterPartId : null;
            var quantity = masterPart ? masterPart.quantity : (masterKit ? masterKit.quantity : null);

            if (userService.isDealerPlusUser() && $state.current.name.includes("customerOrders")) {
                dpOrdersService.addOrderItemToOrder(orderDetails.orderId, masterPartId, masterKitId, quantity);
            } else {
                ordersService.addOrderItemToOrder(orderDetails.orderId, masterPartId, masterKitId, quantity);
            }
        }

        function cancel() {
            $uibModalInstance.close();
        }


    }

})();

