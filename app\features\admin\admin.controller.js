(function () {
    'use strict';

    angular
        .module('app.admin')
        .controller('AdminController', AdminController);

    AdminController.$inject = ['$uibModal', '$state', 'headerBannerService', 'userService', 'manufacturerService', 'viewerSettingsService', '$translate', '$filter', '$window', '$scope'];

    function AdminController($uibModal, $state, headerBannerService, userService, manufacturerService, viewerSettingsService, $translate, $filter, $window, $scope) {
        var vm = this;

        vm.isEdit = false;
        vm.sortReverse = false;
        vm.endRecord = vm.itemPerPage;
        vm.areUsersLoaded = false;
        vm.successMessage = "";
        vm.filter_range = "";
        vm.ranges = [];
        vm.sortReverse = true;
        vm.admin_sort = 'id';
        vm.phone = "";
        vm.email = "";
        vm.emailSignature = "";
        vm.logoResponseUrl = "";
        vm.filterValue = {};
        vm.searchValue = "";
        vm.loadingInfiniteScrollData = false;
        vm.showBackToTopButton = false;
        vm.isFixedHeader = false;

        vm.viewerColour = userService.getBackgroundColour();
        vm.edgingEnabledDefault = userService.getEdgingEnabledDefault();
        vm.ContactUsPageEnabled = userService.getContactUsPageEnabled();
        vm.userId = userService.getUserId();

        vm.deleteUser = deleteUser;
        vm.resetPassword = resetPassword;
        vm.editUser = editUser;
        vm.createUser = createUser;
        vm.addLogoImage = addLogoImage;
        vm.saveDetails = saveDetails;
        vm.editDetails = editDetails;
        vm.cancelDetails = cancelDetails;
        vm.searchFilterChange = searchFilterChange;
        vm.scrollToTop = scrollToTop;
        vm.loadMoreInfiniteScroll = loadMoreInfiniteScroll;

        var selectedUser = {};

        var MACHINE, RANGE, CREATED_DATE, UPDATED_SUCCESS, USER, BY_CONFIRMING, EMAIL_LINK, SEND_RESET;
        function getTranslations(){
            $translate(['ADMIN.MACHINE', 'ADMIN.RANGE', 'ADMIN.CREATED_DATE', 'ADMIN.UPDATED_SUCCESS', 'ADMIN.USER', 'ADMIN.EMAIL_LINK', 'ADMIN.BY_CONFIRMING', 'ADMIN.SEND_RESET'])
                .then(function (resp) {
                    MACHINE = resp["ADMIN.MACHINE"];
                    RANGE = resp["ADMIN.RANGE"];
                    CREATED_DATE = resp["ADMIN.CREATED_DATE"];
                    UPDATED_SUCCESS = resp["ADMIN.UPDATED_SUCCESS"];
                    USER = resp["ADMIN.USER"];
                    EMAIL_LINK = resp["ADMIN.EMAIL_LINK"];
                    BY_CONFIRMING = resp["ADMIN.BY_CONFIRMING"];
                    SEND_RESET = resp["ADMIN.SEND_RESET"];
                    vm.sortBy = [
                        {name: 'name', value: MACHINE},
                        {name: 'rangeName', value: RANGE},
                        {name: 'createdDate', value: CREATED_DATE}
                    ];
                });
        }

        getTranslations();
        initialize();


        function initialize() {
            fetchManufacturerDetails();
            fetchUsers();

        }

        function fetchManufacturerDetails() {
            manufacturerService.fetchManufacturerDetails()
                .then(fetchManufacturerDetailsSuccess, fetchManufacturerDetailsFailed)
        }

        function fetchManufacturerDetailsSuccess(response) {
            var details = response.data;
            if (details) {
                vm.phone = details.phone;
                vm.email = details.supportEmail;
                vm.emailSignature = details.emailSignature;
                vm.logoResponseUrl = details.logoUrl;
            }
        }

        function fetchManufacturerDetailsFailed(error) {
            headerBannerService.setNotification('ERROR', error.data.error, 10000);
        }

        function saveDetails() {
            manufacturerService.updateManufacturerDetails(vm.edit.phone, vm.edit.email, vm.edit.emailSignature, vm.logoResponseUrl, vm.edit.viewerColour, vm.edit.edgingEnabledDefault, vm.edit.contactUsPageEnabled)
                .then(updateManufacturerDetailsSuccess, updateManufacturerDetailsFailed)
        }

        function editDetails() {
            vm.edit = {phone: vm.phone, email: vm.email, emailSignature: vm.emailSignature, viewerColour: vm.viewerColour, edgingEnabledDefault: vm.edgingEnabledDefault, contactUsPageEnabled: vm.contactUsPageEnabled };
            vm.isEdit = true;
        }

        function cancelDetails() {
            vm.edit = {};
            vm.isEdit = false;
        }

        function searchFilterChange() {
            updateTotalItemCount();
        }

        function updateTotalItemCount() {
            var textFilter = $filter('filter')(vm.userList, vm.searchValue);
            vm.totalItems = $filter('filter')(textFilter, vm.filterValue, true).length;
        }

        function updateManufacturerDetailsSuccess() {
            headerBannerService.setNotification('SUCCESS', UPDATED_SUCCESS, 5000);
            vm.isEdit = false;
            vm.phone = vm.edit.phone;
            vm.email = vm.edit.email;
            vm.emailSignature = vm.edit.emailSignature;
            vm.viewerColour = vm.edit.viewerColour;
            vm.edgingEnabledDefault = vm.edit.edgingEnabledDefault;
            vm.contactUsPageEnabled = vm.edit.contactUsPageEnabled;
            userService.setBackgroundColour(vm.edit.viewerColour)
            userService.setEdgingEnabledDefault(vm.edit.edgingEnabledDefault)
            userService.setContactUsPageEnabled(vm.edit.contactUsPageEnabled)
            vm.edit = {};
        }

        function updateManufacturerDetailsFailed(error) {
            headerBannerService.setNotification('ERROR', error.data.error, 10000);
        }

        function fetchUsers() {
            vm.loadingInfiniteScrollData = true;
            userService.getManufacturerUsers()
                .then(getManufacturerUsersSuccess, getManufacturerUsersFailed)
        }

        function toPascalCase(str) {
            return str
            .toLowerCase()
            .match(/[A-Z]{2,}(?=[A-Z][a-z]+[0-9]*|\b)|[A-Z]?[a-z]+[0-9]*|[A-Z]|[0-9]+/g)
            .map(x => x.charAt(0).toUpperCase() + x.slice(1).toLowerCase())
            .join(' ');
        }

        function getManufacturerUsersSuccess(response) {
            vm.allUsers = response.data.users;
            vm.userList = vm.allUsers.slice(0, 100);  
            vm.userList.forEach(element => {
                element.userStatus = toPascalCase(element.userStatus);
            });
            vm.totalItems = vm.allUsers.length;
            vm.areUsersLoaded = true;
            vm.loadingInfiniteScrollData = false;
            handleInfiniteScroll();
        }

        function getManufacturerUsersFailed(error) {
            vm.areUsersLoaded = false;
            headerBannerService.setNotification('ERROR', error.data.error, 10000);
        }

        function deleteUser(user) {
            vm.successMessage = "";
            var deleteObject = {
                name: USER + user.name,
                id: user.id,
                url: '/user/' + user.id
            };

            $uibModal.open({
                templateUrl: 'features/shared/commonDelete/deleteDialogBox.html',
                controller: 'DeleteController',
                controllerAs: 'deleteCtrl',
                size: 'sm',
                resolve: {
                    deleteObject: function () {
                        return deleteObject;
                    }
                }
            }).result.then(function () {
                fetchUsers();
            }, function () {
                console.log('Modal Cancelled');
            });
        }

        function resetPassword(user) {
            selectedUser = user;
            var confirmObject = {
                titleText: SEND_RESET,
                bodyText: BY_CONFIRMING + " \"" + user.firstName + " " + user.lastName + "\" " + EMAIL_LINK
            };
            $uibModal.open({
                templateUrl: 'features/shared/confirmationModal/confirmModal.html',
                controller: 'ConfirmModalController',
                controllerAs: 'confirmModalCtrl',
                size: 'sm',
                resolve: {
                    confirmObject: function () {
                        return confirmObject;
                    }
                }
            }).result
                .then(sendPasswordEmailConfirmed, doNothing);
        }

        function sendPasswordEmailConfirmed() {
            userService.sendResetPassword(selectedUser.id);
        }

        function doNothing() {
            //do nothing
        }

        function editUser(user) {
            $state.go('userSettings', {userId: user.id});
        }

        function createUser() {
            $state.go('userSettings', { userId: null, mode: 'create' });
        }

        function addLogoImage() {
            var cropType = 'LOGO';

            $uibModal.open({
                templateUrl: 'features/products/imageCropper/imageCropper.html',
                controller: 'ImageCropperController',
                controllerAs: 'imageCropperCtrl',
                size: 'xl',
                backdrop: 'static',
                resolve: {
                    cropType: function () {
                        return cropType;
                    }
                }
            }).result.then(function (response) {
                if (response) {
                    vm.logoResponseUrl = response;
                }
            }, function () {
                console.log('Modal Cancelled');
            });
        }

           var lastScrollTop = 0;
window.addEventListener('scroll', handleInfiniteScroll);

function handleInfiniteScroll() {
    var threshold = 250;
    var scrollTop = window.scrollY;

    if (scrollTop > lastScrollTop) {
        vm.isFixedHeader = scrollTop > threshold;
    } else if (scrollTop < threshold){
        vm.isFixedHeader = false;
    }
    lastScrollTop = scrollTop;  

    
    if (window.innerHeight + scrollTop >= document.body.offsetHeight - 2 && !vm.loadingInfiniteScrollData && !vm.initialLoadDone) {
        loadMoreInfiniteScroll();
    }
}

        function loadMoreInfiniteScroll() {
            vm.loadingInfiniteScrollData = true;

            var nextItems = vm.allUsers.slice(vm.userList.length, vm.userList.length + 100);
            if (nextItems.length === 0) {
                vm.initialLoadDone = true;
                vm.loadingInfiniteScrollData = false;
                return;
            }

            vm.userList = vm.userList.concat(nextItems);

            vm.loadingInfiniteScrollData = false;
            if (vm.userList.length >= vm.totalItems) {
                vm.initialLoadDone = true;
            }
        }

  function scrollToTop() {
      $window.scrollTo({ top: 0, behavior: "smooth" });
      $("html, body").animate({ scrollTop: 0 }, "slow", function () {
        $("#scrollToTop").removeClass("scrolled-past");
      });
    }

    angular.element($window).on("scroll", function () {
      vm.showBackToTopButton = this.pageYOffset > 100;
      $scope.$apply();
    });

        vm.actions = [
            {
                title: "Edit User",
                onClick: function (entity) { vm.editUser(entity); },
                icon: "fa-pencil",
                label: function () { return $translate.instant('GENERAL.EDIT'); }
            },
            {
                title: "Reset Password",
                onClick: function (entity) { vm.resetPassword(entity); },
                icon: "fa-key",
                label: function () { return $translate.instant('ADMIN.RESET_PASSWORD'); }
            },
            {
                title: "Delete",
                onClick: function (entity) { vm.deleteUser(entity); },
                icon: "fa-trash",
                label: function () { return $translate.instant('GENERAL.DELETE'); }
            }
        ];


    }
})();
