(function () {
    'use strict';

    angular
        .module('app.products')
        .controller('CustomerVideosController', CustomerVideosController);

    CustomerVideosController.$inject = ['publicationService', '$stateParams', 'headerBannerService', '$window', '$sce', '$location'];

    function CustomerVideosController(publicationService, $stateParams, headerBannerService, $window, $sce, $location) {
        var vm = this;

        var manualId = $stateParams.manualId;
        vm.count = 8;
        vm.currentPage = 1;
        vm.itemPerPage = 8;
        vm.start = 0;
        vm.sortReverse = false;
        vm.endRecord = vm.itemPerPage;

        vm.pageChanged = pageChanged;
        vm.playVideo = playVideo;

        initialize();

        function initialize() {
            getVideos();
        }

        function getVideos() {
            publicationService.getVideos(manualId)
                .then(getVideosSuccess)
                .catch(getVideosFailed);
        }

        function getVideosSuccess(response) {
            vm.videosList = response.data;
            if (vm.videosList.length > 0) {
                playVideo(vm.videosList[0]);
            }
            vm.totalItems = vm.videosList.length;
        }

        function getVideosFailed(error) {
            //vm.videosList = [];
            headerBannerService.setNotification('ERROR', error.data.error, 10000);
        }

        function playVideo(video) {
            vm.nowPlaying = video;
            vm.playingVideoURL = $sce.trustAsResourceUrl(video.url);
            $location.hash("videoBox");
        }

        function pageChanged() {
            vm.start = ((vm.currentPage - 1) * vm.itemPerPage);
        }

    }
})();
