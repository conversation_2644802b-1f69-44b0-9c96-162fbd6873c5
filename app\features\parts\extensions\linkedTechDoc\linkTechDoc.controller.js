(function () {
    'use strict';

    angular
        .module('app.shared')
        .controller('LinkedTechDocController', LinkedTechDocController);

    LinkedTechDocController.$inject = ['$uibModalInstance', 'linkTechDocObject', 'userService', 'masterPartService', 'manufacturerProductService'];

    function LinkedTechDocController($uibModalInstance, linkTechDocObject, userService, masterPartService, manufacturerProductService) {
        var vm = this;

        vm.save = save;
        vm.cancel = $uibModalInstance.dismiss;

        vm.linkedTechDocIds = [];

        if (linkTechDocObject) {
            vm.masterPartId = linkTechDocObject.masterPartId;
            vm.partNumber = linkTechDocObject.partNumber;
            vm.hasLinkedTechDocs = linkTechDocObject.hasLinkedTechDocs;
        }

        manufacturerProductService.getTechDocs()
            .then(getTechDocsSuccess, serviceFailure)

        function getTechDocsSuccess(response) {
            vm.techDocs = response.data;
            for (var i = 0; i < vm.techDocs.length; i++) {
                vm.techDocs[i].selected = false;
                vm.techDocs[i].pageRange = Array(vm.techDocs[i].pageCount).fill(0).map((e,i)=>i+1);
            }
            //Only clear if not an edit or else load the existing selected techDocs
            if (vm.hasLinkedTechDocs) {
                masterPartService.getLinkedTechDocsForPart(vm.masterPartId).then(getLinkedTechDocsSuccess, serviceFailure)
            }
        }

        function getLinkedTechDocsSuccess(response) {
            for (var i = 0; i < response.data.length; i++) {
                var techDocIdToSelect = response.data[i].id;
                var index = _.findIndex(vm.techDocs, {id: techDocIdToSelect});
                vm.techDocs[index].selected = true;
                vm.techDocs[index].linkedPage = response.data[i].linkedPage;
            }
        }

        function save() {
            vm.hasError = false;
            var linkedTechDocs = [];
            for (var i = 0; i < vm.techDocs.length; i++) {
                if (vm.techDocs[i].selected) {
                    var pageNum = vm.techDocs[i].linkedPage ? vm.techDocs[i].linkedPage : 1;
                    linkedTechDocs.push({"id": vm.techDocs[i].id, "linkedPage": pageNum});
                }
            }
            masterPartService.updateLinkedTechDocsForPart(vm.masterPartId, linkedTechDocs)
                .then(saveSuccess, serviceFailure);
        }

        function saveSuccess() {
            $uibModalInstance.close(vm.masterPartId);
        }

        function serviceFailure(error) {
            vm.hasError = true;
            console.log("Error: " + error)
        }
    }
})();
