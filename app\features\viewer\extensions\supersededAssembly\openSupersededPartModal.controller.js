(function () {
    'use strict';

    angular
        .module('app.viewer')
        .controller('OpenSupersededPartController', OpenSupersededPartController);

    OpenSupersededPartController.$inject = ['$uibModalInstance', '$state', '$window', '$rootScope', 'supersededModel'];

    function OpenSupersededPartController($uibModalInstance, $state, $window, $rootScope, supersededModel) {
        var vm = this;

        vm.openSupersededViewable = openSupersededViewable;
        vm.cancel = cancel;
        vm.viewableName = supersededModel.modelName.toUpperCase();


        function openSupersededViewable() {
            var params = {
                machineName: supersededModel.machineName,
                autodeskURN: supersededModel.urn,
                modelId: supersededModel.modelId,
                viewableName: supersededModel.modelName,
                translateType: supersededModel.translateType,
                manualId: supersededModel.manualId,
                productId: supersededModel.productId
            };
            var openUrlNW;
            if (supersededModel.is2d) {
                 openUrlNW = $state.href("customerPdfViewer", params, {absolute: true});
            } else {
                 openUrlNW = $state.href("customerViewer", params, {absolute: true});
            }

            $window.open(openUrlNW, '_blank');

            $uibModalInstance.close();
        }


        function cancel() {
            $uibModalInstance.close();
        }


    }

})();
