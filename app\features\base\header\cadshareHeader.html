<header ng-controller="HeaderController as headerCtrl" ng-hide="headerCtrl.hideHeader">
    <div class="site-header" ng-hide="headerCtrl.onBehalfOf">

        <div class="mobile-site-header">

        <a class="logo" href="#" >
        <img alt="CadShareLogo" ng-src="{{appCtrl.logoUrl}}">
        </a>

            <nav class="site-nav" ng-show="headerCtrl.isUserLoggedIn">
                <ul>
                    <li ui-sref-active="active" ng-if="headerCtrl.showDashboard" >
                        <a ui-sref="dashboard" translate>HEADER.DASHBOARD</a>
                    </li>
                    <li ui-sref-active="active" ng-if="headerCtrl.showPublishedProducts">
                        <a ui-sref="publishedProducts" translate>HEADER.PRODUCTS</a>
                    </li>
                    <li ui-sref-active="active" ng-if="headerCtrl.showOrders && !headerCtrl.isDealerPlusUser">
                        <a ui-sref="orders.enquiries" ng-show="headerCtrl.enquiriesOnly" translate>HEADER.ENQUIRIES</a>
                        <a ui-sref="orders.enquiries" ng-hide="headerCtrl.enquiriesOnly" translate>HEADER.ORDERS</a>
                    </li>
                    <li ui-sref-active="active" ng-if="headerCtrl.showOrders && headerCtrl.isDealerPlusUser">
                        <a ui-sref="dpOrders.myOrders.orders.enquiries" translate>HEADER.ORDERS</a>
                    </li>
                    <li ui-sref-active="active" ng-if="headerCtrl.showProducts">
                        <a ui-sref="products.catalogue" translate>HEADER.PRODUCTS</a>
                    </li>
                    <li ui-sref-active="active" ng-if="headerCtrl.showPublications">
                        <a ui-sref="publications" translate>HEADER.PUBLICATIONS</a>
                    </li>
                    <li ui-sref-active="active" ng-if="headerCtrl.showParts">
                        <a ui-sref="parts.partsSearch" translate>HEADER.PARTS</a>
                    </li>
                    <li ui-sref-active="active" ng-if="headerCtrl.showPartSearch">
                        <a ui-sref="customerPartSearch.search" translate>HEADER.PART_SEARCH</a>
                    </li>
                    <li ui-sref-active="active" ng-if="headerCtrl.isDealerPlusUser">
                        <a ui-sref="priceManagement" translate>HEADER.PRICE_MANAGEMENT</a>
                    </li>
                    <li ui-sref-active="active" ng-if="headerCtrl.isDealerPlusUser">
                        <a ui-sref="dpCustomers" translate>HEADER.CUSTOMERS</a>
                    </li>
                    <li ui-sref-active="active"  ng-if="headerCtrl.showCustomers">
                        <a ui-sref="customers" translate>HEADER.CUSTOMERS</a>
                    </li>

                    <li ng-if="headerCtrl.showAdmin || headerCtrl.showSecurity" class="nav-item dropdown">
                        <a href="javascript:void(0)" class="dropdown-toggle" data-toggle="dropdown" translate>HEADER.SETTINGS</a>
                        <div class="dropdown-menu dropdown-link">
                            <a class="dropdown-item" ng-if="headerCtrl.showAdmin" ui-sref-active="active"  ui-sref="admin" translate>HEADER.ADMIN</a>
                            <a class="dropdown-item" ng-if="headerCtrl.showSecurity" ui-sref-active="active"  ui-sref="security" translate>HEADER.SECURITY</a>
                            <a class="dropdown-item" ng-if="headerCtrl.showAdmin" ui-sref-active="active"  ui-sref="manufacturerInformation" translate>HEADER.MANUFACTURER_INFORMATION</a>
                        </div>
                    </li>

                    <li ui-sref-active="active" ng-if="headerCtrl.isUserLoggedIn && headerCtrl.showBasket && headerCtrl.showOrders">
                        <a ui-sref="create" translate>HEADER.CREATE_ENQUIRY</a>
                    </li>

                    <li ui-sref-active="active" ng-if="headerCtrl.showContactUs">
                        <a ui-sref="contactUs" translate>HEADER.CONTACT_US</a>
                    </li>
                </ul>
            </nav>

            <nav class="site-nav mobile-sub-site-header nav-right" >

                <div class="pr-4" ng-if="headerCtrl.availableLanguages.length > 1">
                    <a class="dropdown-toggle dropdown_additional" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" translate>TRANSLATION.LANGUAGE_NAME
                    </a>
                    <div class="dropdown-menu">
                        <a class="dropdown-item" href="#" ng-click="headerCtrl.dropdownChangeLanguage($index)" ng-repeat="language in headerCtrl.availableLanguages">{{language.displayText}}</a>
                    </div>
                </div>

                <nav class="navigation_custom" role="navigation_custom">
                    <ul>
                        <li><a class="nameInitials user-avatar">
                            {{headerCtrl.firstNameInitial}}{{headerCtrl.secondNameInitial}}</a>
                            <ul class="User-Dropdown U-open">
						<p>{{headerCtrl.firstName}} {{headerCtrl.lastName}}</p>
                                <li ng-if="headerCtrl.showCustomerSubscriptions"><a ng-click="headerCtrl.goToCustomerAccountSubscriptions()" href="javascript:void(0)" ui-sref-active="active" translate>HEADER.CUSTOMER_SUBSCRIPTIONS</a></li>
                                <li><a ng-click="headerCtrl.logOut()" href="#" translate>HEADER.SIGN_OUT</a></li>
                            </ul>
                        </li>
                    </ul>
                </nav>
            </nav>

            <!-- <nav class="site-nav nav-right" ng-show="headerCtrl.isUserLoggedIn && headerCtrl.showBasket && headerCtrl.showOrders">
                <ul>
                    <li>
                        <a class="dark-secondary profile" href="" ng-click="headerCtrl.goToCreateEnquiry()">
                            <i class="fa fa-shopping-cart" aria-hidden="true"></i> {{HEADER.BASKET | translate}} <span
                                class="badge-small ng-binding basketCount">{{headerCtrl.basketQuatity}}</span>
                        </a>
                       
                    </li>
                </ul>
            </nav> -->

            <nav class="site-nav nav-right navigation_custom" role="navigation_custom" ng-show="headerCtrl.isUserLoggedIn && headerCtrl.showBasket && headerCtrl.showOrders">
                <ul>
                    <li>
                    <a class="dark-secondary profile" href="" ng-click="headerCtrl.goToCreateEnquiry()">
                        <i class="fa fa-shopping-cart" aria-hidden="true"></i> {{HEADER.BASKET | translate}} <span
                            class="badge-small ng-binding basketCount">{{headerCtrl.basketQuatity}}</span>
                    </a>
                        <ul class="User-Dropdown User-Dropdown-Positon U-open">
                            <li><a ng-click="headerCtrl.goToCreateEnquiry()" href="javascript:void(0)"
                                    ui-sref-active="active" translate>HEADER.GO_TO_BASKET</a></li>
                            <li><a ng-click="headerCtrl.clearLocalStorage()" href="javascript:void(0)" translate>HEADER.CLEAR_BASKET</a></li>
                        </ul>
                    </li>
                </ul>
            </nav>

        <div id="sidebarContainer" ng-show="headerCtrl.isUserLoggedIn">

            <nav id="mySidenav" class="sidenav">

                <div class="User-area user-area-mobile">
                    <div class="nameInitials user-avatar">
                        {{headerCtrl.firstNameInitial}}{{headerCtrl.secondNameInitial}}
                    </div>
                </div>

                <nav class="py-4" ng-show="headerCtrl.isUserLoggedIn && headerCtrl.showBasket && headerCtrl.showOrders">
                    <ul class="list-unstyled">
                        <li>
                            <a class="dark-secondary profile" href="" ng-click="headerCtrl.goToCreateEnquiry()">
                                <i class="fa fa-shopping-cart" aria-hidden="true"></i>{{HEADER.BASKET | translate}} <span
                                    class="badge-small ng-binding basketCount">{{headerCtrl.basketQuatity}}</span>
                            </a>
                            <!--<span style="margin-top: 18px;" ng-hide="headerCtrl.basketQuantity == 0" class="badge-small">
                            {{headerCtrl.basketQuantity}}
                                </span>-->
                        </li>
                    </ul>
                </nav>

                <a class="closeBtn" ng-click="headerCtrl.closeNav()">&times;</a>

                <ul class="m-0 list-unstyled">
				<p>{{headerCtrl.firstName}} {{headerCtrl.lastName}}</p>
                    <li ng-click="headerCtrl.closeNav()" ui-sref-active="active" ng-if="headerCtrl.showDashboard">
                        <a ui-sref="dashboard" translate>HEADER.DASHBOARD</a>
                    </li>
                    <li ng-click="headerCtrl.closeNav()" ui-sref-active="active" ng-if="headerCtrl.showPublishedProducts">
                        <a ui-sref="publishedProducts" translate>HEADER.PRODUCTS</a>
                    </li>
                    <li ng-click="headerCtrl.closeNav()" ui-sref-active="active" ng-if="headerCtrl.showOrders && !headerCtrl.isDealerPlusUser">
                        <a ui-sref="orders.enquiries" translate ng-show="headerCtrl.enquiriesOnly">HEADER.ENQUIRIES</a>
                        <a ui-sref="orders.enquiries" translate ng-hide="headerCtrl.enquiriesOnly">HEADER.ORDERS</a>
                    </li>
                    <li ui-sref-active="active" ng-if="headerCtrl.showOrders && headerCtrl.isDealerPlusUser">
                        <a ui-sref="dpOrders.myOrders.orders.enquiries" translate>HEADER.ORDERS</a>
                    </li>
                    <li ng-click="headerCtrl.closeNav()" ui-sref-active="active" ng-if="headerCtrl.showProducts">
                        <a ui-sref="products.catalogue" translate>HEADER.PRODUCTS</a>
                    </li>
                    <li ng-click="headerCtrl.closeNav()" ui-sref-active="active" ng-if="headerCtrl.showPublications">
                        <a ui-sref="publications" translate>HEADER.PUBLICATIONS</a>
                    </li>
                    <li ng-click="headerCtrl.closeNav()" ui-sref-active="active" ng-if="headerCtrl.showParts">
                        <a ui-sref="parts.partsSearch" translate>HEADER.PARTS</a>
                    </li>
                    <li ng-click="headerCtrl.closeNav()" ui-sref-active="active" ng-if="headerCtrl.showPartSearch">
                        <a ui-sref="customerPartSearch.search" translate>HEADER.PART_SEARCH</a>
                    </li>
                    <li ui-sref-active="active"ui-sref-active="active" ng-if="headerCtrl.isDealerPlusUser">
                        <a ui-sref="priceManagement" translate>HEADER.PRICE_MANAGEMENT</a>
                    </li>
                    <li ui-sref-active="active" ui-sref-active="active"ng-if="headerCtrl.isDealerPlusUser">
                        <a ui-sref="dpCustomers" translate>HEADER.CUSTOMERS</a>
                    </li>
                    <li ng-click="headerCtrl.closeNav()" ui-sref-active="active" ng-if="headerCtrl.showCustomers">
                        <a ui-sref="customers" translate>HEADER.CUSTOMERS</a>
                    </li>
                    <li ng-click="headerCtrl.closeNav()" ui-sref-active="active" ng-if="headerCtrl.showAdmin">
                        <a ui-sref="admin" translate>HEADER.ADMIN</a>
                    </li>
                    <li ng-click="headerCtrl.closeNav()" ui-sref-active="active" ng-if="headerCtrl.showSecurity">
                        <a ui-sref="security" translate>HEADER.SECURITY</a>
                    </li>
                    <li ng-click="headerCtrl.closeNav()" ui-sref-active="active" ng-if="headerCtrl.showAdmin">
                        <a ui-sref="manufacturerInformation" translate>HEADER.MANUFACTURER_INFORMATION</a>
                    </li>
                    <li class="sideBar-maxWidth" ng-click="headerCtrl.closeNav()" ui-sref-active="active" ng-if="headerCtrl.isUserLoggedIn && headerCtrl.showBasket && headerCtrl.showOrders"><a ui-sref="create" translate>HEADER.CREATE_ENQUIRY</a>
                    </li>
                    <li ng-click="headerCtrl.closeNav()" ui-sref-active="active" ng-if="headerCtrl.showContactUs">
                        <a ui-sref="contactUs" translate>HEADER.CONTACT_US</a>
                    </li>
                </ul>

                <nav class="pt-5" >

                    <div ng-if="headerCtrl.availableLanguages.length > 1">
                        <a class="dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="pr-3 fas fa-globe-americas"></i>{{'TRANSLATION.LANGUAGE_NAME' | translate}}
                        </a>
                        <div class="dropdown-menu">
                            <a class="dropdown-item" href="#" ng-click="headerCtrl.dropdownChangeLanguage($index)" ng-repeat="language in headerCtrl.availableLanguages">{{language.displayText}}</a>
                        </div>
                    </div>

                    <div class="list-unstyled">

                            <li ng-click="headerCtrl.closeNav()"><a value="Refresh" ng-click="headerCtrl.reloadPage()" href="#" ui-sref-active="active"><i class="pr-3 fas fa-sync-alt"></i>{{'HEADER.HARD_RELOAD' | translate}}</a></li>

                        <li class="d-none" ng-click="headerCtrl.closeNav()"><a value="Reset" accesskey="r" ng-click="headerCtrl.resetPage()" href="#" ui-sref-active="active"></a></li>

                    </div>

                    <div>
                        <a ng-click="headerCtrl.logOut()">
                            <i class="pr-3 fas fa-sign-out-alt"></i>{{'HEADER.SIGN_OUT' | translate}}
                        </a>
                    </div>

                </nav>

            </nav>

            </div>

            <span class="openNav" ng-click="headerCtrl.openNav()"><i id="openBtn" class="fas fa-bars openbtn"></i></span>

        </div>

    </div>

    </div>

   <div ng-show="headerCtrl.onBehalfOf" class="on-behalf-of-banner site-header alert-banner vertical-align">
        <div class="mb-0 alert alert-warning d-none d-lg-inline-block">{{'HEADER.CREATING_ONBEHALF_OF' | translate}}
            {{headerCtrl.onBehalfOf.firstName}} {{headerCtrl.onBehalfOf.lastName}}
        </div>
       <div class="mb-0 alert alert-warning d-lg-none">
           {{headerCtrl.onBehalfOf.firstName}} {{headerCtrl.onBehalfOf.lastName}}
       </div>

        <div class="flex-right vertical-align">
            <a style="margin-right: 16px" class="dark-secondary profile" href=""
               ng-click="headerCtrl.goToCreateEnquiry()" ng-show="headerCtrl.isUserLoggedIn && headerCtrl.showBasket">
                <i class="fa fa-shopping-cart" aria-hidden="true"></i> {{'HEADER.BASKET' | translate}} <span
                    class="badge-small ng-binding basketCount">{{headerCtrl.basketQuatity}}</span>
            </a>

            <div class="cadGap">
            <button onClick="javascript:history.go(-1);" class="btn btn-outline-warning flex-right">
                {{'HEADER.GO_BACK' | translate}}
            </button>
            <button ng-click="headerCtrl.exitCustomerOrder()" class="btn btn-outline-warning flex-right">
                {{'HEADER.EXIT_CUSTOMER' | translate}}
            </button>
            </div>
        </div>
    </div>

</header>