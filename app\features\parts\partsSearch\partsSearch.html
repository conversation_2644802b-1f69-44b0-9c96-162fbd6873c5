<!-- Heading Section -->

<section>
    <p class="page-desc" ng-show="partsSearchCtrl.isCustomer" translate>PART_SEARCH.FIND_BY</p>
    <div class="d-flex justify-content-between flex-wrap" ng-hide="partsSearchCtrl.isCustomer">
        <div class="w-75">
            <p class="page-desc">{{"PART_SEARCH.MANAGE_MASTER" | translate}}
                <br>
                {{"PART_SEARCH.USE_SEARCH" | translate}}
        </div>
        <div>
            <button class="btn btn-primary" ng-click="partsSearchCtrl.addNewPart()" translate>PART_SEARCH.ADD_NEW</button>
        </div>
    </div>
</section>

<!-- Search Section -->

<div class="py-5">

    <form class="d-flex justify-content-center align-items-center flex-column search_contents p-5" ng-submit="partsSearchCtrl.search()">

        <div class="d-flex w-75 search-area">

            <div class="input-group mb-0 pr-3">
                <input  class="form-control mr-0" type="search" ng-model="partsSearchCtrl.searchValue" placeholder="{{'PART_SEARCH.SEARCH_BY_PART' | translate}}">
                <div class="input-group-append">
                    <button type="button" ng-click="partsSearchCtrl.search()" ng-hide="partsSearchCtrl.searching" ng-disabled="!partsSearchCtrl.searchValue || partsSearchCtrl.searchValue.trim() === ''" ng-class="{'disabledElement': !partsSearchCtrl.searchValue || partsSearchCtrl.searchValue.trim() === ''}"
                        class="input-group-text input-group-text-btn input-group-text-btn btn-anim"><i
                            class="pr-0 pr-md-3 fa fa-search"></i>
                        <span class="search_mobile_disable">{{'PART_SEARCH.SEARCH' | translate}}</span>
                        </button>
                    <button type="button" ng-show="partsSearchCtrl.searching" class="input-group-text input-group-text-btn btn-anim"></i><span class="search_mobile_disable">{{'PART_SEARCH.SEARCHING' | translate}}</span>
                        <span class="spinner-border text-light" role="status" aria-hidden="true"></span>
                    </button>
                </div>
            </div>

        </div>

        <div class="d-flex justify-content-center align-content-center flex-wrap mt-4">
            <span class="pr-3" translate>PART_SEARCH.SEARCH_BY</span>
            <label class="pr-3 radio-inline">
                <input type="radio" ng-model="partsSearchCtrl.searchBy" value="partNumber"
                       style="white-space:nowrap"><span style="white-space:nowrap" translate>PART_SEARCH.PART_NUM</span>
            </label>
            <label class="radio-inline">
                <input type="radio" ng-model="partsSearchCtrl.searchBy" value="partDescription"
                       style="white-space:nowrap"><span style="white-space:nowrap" translate>PART_SEARCH.PART_DESC</span>
            </label>
        </div>

        <div ng-show="!partsSearchCtrl.masterParts.length > 0 && partsSearchCtrl.resultsReturned">
            <h2 class="center-align" translate>PART_SEARCH.NO_PART</h2>
        </div>
        <div ng-show="partsSearchCtrl.searchError">
            <h3 class="error-alert center-align" translate>PART_SEARCH.SEARCH_ERROR</h3>
        </div>

    </form>
</div>

<div ng-include="'features/parts/partsSearch/manufacturerSearchTables/manufacturerSearchPartsTable.html'"></div>

</section>

