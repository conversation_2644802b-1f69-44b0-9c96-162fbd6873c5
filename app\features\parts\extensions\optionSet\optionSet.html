<section class="body-content">
    <div class="order-details-holder">
        <h1 translate>OPTION_SET.TITLE</h1>
        <p class="page-desc" translate>OPTION_SET.SUBTITLE</p>
        <br>
        <p translate>OPTION_SET.USE_SEARCH</p>
    </div>
</section>
<section class="body-content page-body">
    <div class="row">
        <div class="col-lg-6">
            <div class="panel">
                <form class="form" name="optionForm">

                    <div class="m-16">
                        <label translate>OPTION_SET.DESCRIBE_CHOICE</label>
                        <input type="text"
                               ng-model="optionSetCtrl.description"
                               required="required">
                    </div>

                    <table class="table table-bordered">
                        <thead>
                        <tr>
                            <th translate>OPTION_SET.PART_NUMBER</th>
                            <th translate>OPTION_SET.DESCRIPTION</th>
                            <th></th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr ng-repeat="part in optionSetCtrl.parts track by part.partNumber">
                            <td>{{part.partNumber}}</td>
                            <td>{{part.description ? part.description : part.partDescription}}</td>
                            <td>
                                <a href="" ng-click="optionSetCtrl.removePart($index)"
                                   class="delete fa fa-trash"></a>
                            </td>
                        </tr>

                        <tr ng-show="!optionSetCtrl.parts.length > 0">
                            <td class="emptytable" colspan="3" translate>OPTION_SET.NO_PARTS</td>
                        </tr>
                        </tbody>
                    </table>
                    <div class="error-well" ng-if="optionSetCtrl.noPartsSelectedError" translate>OPTION_SET.AT_LEAST_ONE
                    </div>

                    <div class="btn-actions">
                        <button class="btn small secondary" type="button" ng-click="optionSetCtrl.cancel()" translate>GENERAL.CANCEL</button>
                        <button class="btn small primary mr-16" type="submit"
                                ng-click="optionForm.$valid && optionSetCtrl.save()" translate>OPTION_SET.SAVE
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <div class="col-lg-6 py-lg-0 py-5 mb-5">
            <div class="bg-white pb-2 pt-4 px-4">
                <inline-parts-search on-add-clicked="optionSetCtrl.onAddClicked(masterPart)"></inline-parts-search>
            </div>
        </div>

    </div>
</section>



