// ==========================================================================
// Typography
// ==========================================================================

h1,
h2,
h3,
h4,
h5,
p,
small {
    margin     : 0 0 $spacing*2 0;
    font-family: $body-font;
    font-style : normal;
    line-height: normal;
    font-weight: 800;
}

h1 {
    font-size  : 1.5em;
}

h2 {
    font-size  : 1.3em;
    font-weight: 700;

    &.feature-header {
        margin: $spacing*4 0;
    }
}

h3 {
    font-size  : 1.125em;
    font-weight: 700;
}

p,
.form-control{
    font-size  : 1em!important;
    font-weight: 400;
}

li {
  margin:0;
}

.text-overflow {
    text-overflow: ellipsis;
    overflow     : hidden;
    white-space  : nowrap;

  &.mouse-pointer {
    cursor:pointer;
  }
}

.pdf-title{
    cursor:pointer;
}

.text-left {text-align: left}
.text-center {text-align: center}
.text-right {text-align: right}

.lightweight {font-weight:400;}

.titletail {
  text-transform: uppercase;
  font-size:0.95em;
  position: relative;
  font-weight:600;
  margin:0 0 $spacing*2 0;
  margin:0 0 20px 0;
  z-index: 0;

  span {
      background: $lightback;
      padding: 0 $spacing*2 0 0;
  }
  &:before {
      border-top: 2px solid lighten($divider-color,3%);
      content: "";
      margin: 0 auto;
      position: absolute;
      top: 50%;
      left: 0;
      right: 0;
      bottom: 0;
      width: 100%;
      z-index: -1;
  }
}

h1 {
  font-size: 2.5em; /* = 40px/16px */
  line-height: 1.1em; /* = 44px/40px */
  margin-bottom: 22px;
}
p,
a,
td,
small{
  font-size: 1em; /* 16px is the default em size */
  line-height: 1.5714285714285714em; /* = 22px/14px */
  font-weight: normal;
  //margin-bottom: 22px;
}

h1 {
  font-size: 1.5em;
  line-height: 32px;
  margin-bottom: 16px;
}
h2 {
  font-size  : 1.3em;
  line-height: 32px;
  margin-bottom: 16px;
}
p {
  font-size: 1em;
  line-height: 1.5714285714285714em; /* = 22px/14px */
  margin-bottom: 16px;
}
p.intro {
  font-size: 1.125em; /* 18px */
  line-height: 1.5714285714285714em; /* = 22px/14px */
  padding-top: Xpx;
  margin-bottom: 11 px-Xpx;
}

small{
  font-size: 0.875em;
}

td{

  p {
    margin-bottom: 0;
  }
}

.upper{
  text-transform: uppercase;
}