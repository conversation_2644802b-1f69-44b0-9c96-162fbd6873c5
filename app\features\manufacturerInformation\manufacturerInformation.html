<section class="ml-5 mt-5 pl-4">
    <h1 translate>ADMIN.MANUFACTURER_DETAILS</h1>
    <div translate>ADMIN.MANUFACTURER_INFORMATION</div>
</section>

<div class="d-flex">
    <div class="col-12">
        <div class="m-5 p-5 panel">
            <div class="col-12 col-md-4">
                <form class="form" ng-submit="manufacturerInformationCtrl.saveDetails()">
                    <div class="manufacturer-details">
                        <p translate>ADMIN.PHONE</p>
                        <p ng-hide="manufacturerInformationCtrl.isEdit">{{manufacturerInformationCtrl.phone}}</p>
                        <p ng-show="manufacturerInformationCtrl.isEdit"><input type="text" pattern="^(\+)?[0-9\s]*(\([0-9]\))?[0-9\s]*$"
                                                                               ng-model="manufacturerInformationCtrl.edit.phone"></p>
                    </div>
                    <div class="manufacturer-details">
                        <p translate>ADMIN.SUPPORT_EMAIL</p>
                        <!-- Display mode - show primary email and additional emails -->
                        <p ng-hide="manufacturerInformationCtrl.isEdit">{{manufacturerInformationCtrl.email}}</p>
                        <p ng-hide="manufacturerInformationCtrl.isEdit" ng-repeat="additionalEmail in manufacturerInformationCtrl.additionalEmails">{{additionalEmail}}</p>
                        <!-- Edit mode - dynamic email list -->
                        <p ng-show="manufacturerInformationCtrl.isEdit">
                            <!-- Primary email -->
                            <div class="mb-2">
                                <label class="small text-muted" translate>ADMIN.PRIMARY_EMAIL</label>
                                <input type="email" ng-model="manufacturerInformationCtrl.edit.email"
                                       placeholder="<EMAIL>" required class="form-control">
                            </div>
                            <!-- Additional emails -->
                            <div class="mb-2">
                                <label class="small text-muted" translate>ADMIN.ADDITIONAL_EMAILS</label>
                                <div ng-repeat="additionalEmail in manufacturerInformationCtrl.edit.additionalEmails track by $index" class="d-flex mb-1">
                                    <input type="email" ng-model="manufacturerInformationCtrl.edit.additionalEmails[$index]"
                                           placeholder="<EMAIL>" class="form-control mr-2">
                                    <button type="button" class="btn btn-sm btn-outline-danger"
                                            ng-click="manufacturerInformationCtrl.removeAdditionalEmail($index)">
                                        <i class="fa fa-trash"></i>
                                    </button>
                                </div>
                                <button type="button" class="btn btn-sm btn-outline-primary mt-1"
                                        ng-click="manufacturerInformationCtrl.addAdditionalEmail()">
                                    <i class="fa fa-plus"></i> <span translate>ADMIN.ADD_EMAIL</span>
                                </button>
                            </div>
                        </p>
                    </div>
                    <div class="manufacturer-details">
                        <p translate>ADMIN.EMAIL_SIGNATURE</p>
                        <p ng-hide="manufacturerInformationCtrl.isEdit">{{manufacturerInformationCtrl.emailSignature}}</p>
                        <p ng-show="manufacturerInformationCtrl.isEdit">
                            <textarea class="col-12" type="text" ng-model="manufacturerInformationCtrl.edit.emailSignature"
                                      placeholder="{{'ADMIN.EMAIL_PLACEHOLDER' | translate}}"></textarea></p>
                    </div>

                    <div class="manufacturer-details pb-4">
                        <p translate>ADMIN.EDGING_BY_DEFAULT</p>
                        <div ng-hide="manufacturerInformationCtrl.isEdit">
                            <p class="font-weight-normal" ng-show="manufacturerInformationCtrl.edgingEnabledDefault" translate>ADMIN.ENABLED</p>
                            <p ng-hide="manufacturerInformationCtrl.edgingEnabledDefault" translate>ADMIN.DISABLED</p>
                        </div>
                        <div class="toggle"  ng-show="manufacturerInformationCtrl.isEdit">
                            <input type="checkbox" id="edgingEnabled" class="switch-cbx hidden" ng-model="manufacturerInformationCtrl.edit.edgingEnabledDefault"/>
                            <label for="edgingEnabled" class="switch-lbl">
                                <span ng-show="manufacturerInformationCtrl.edit.edgingEnabledDefault" translate>ADMIN.ENABLED</span>
                                <span ng-hide="manufacturerInformationCtrl.edit.edgingEnabledDefault" translate>ADMIN.DISABLED</span>
                            </label>
                        </div>
                    </div>

                    <div class="manufacturer-details">
                        <p translate>ADMIN.DISABLE_CONTACT_US</p>
                        <div ng-hide="manufacturerInformationCtrl.isEdit">
                            <p class="text-uppercase font-weight-normal" ng-show="manufacturerInformationCtrl.contactUsPageEnabled" translate>ADMIN.YES</p>
                            <p class="text-uppercase" ng-hide="manufacturerInformationCtrl.contactUsPageEnabled" translate>ADMIN.NO</p>
                        </div>
                        <div class="toggle"  ng-show="manufacturerInformationCtrl.isEdit">
                            <input type="checkbox" id="contactUsEnabled" class="switch-cbx hidden" ng-model="manufacturerInformationCtrl.edit.contactUsPageEnabled"/>
                            <label for="contactUsEnabled" class="switch-lbl">
                                <span ng-show="manufacturerInformationCtrl.edit.contactUsPageEnabled" translate>ADMIN.YES</span>
                                <span ng-hide="manufacturerInformationCtrl.edit.contactUsPageEnabled" translate>ADMIN.NO</span>
                            </label>
                        </div>
                    </div>

                    <div class="manufacturer-details pt-4">
                        <p translate>ADMIN.BACKGROUND_COLOUR</p>
                        <p  ng-hide="manufacturerInformationCtrl.isEdit" ng-style="{'background-color': manufacturerInformationCtrl.viewerColour}" class="viewer-colour-box">&nbsp;</p>
                        <p ng-if="manufacturerInformationCtrl.isEdit"><input color-picker color-picker-model="manufacturerInformationCtrl.edit.viewerColour" color-picker-output-format="'rgba'" ng-style="{background:manufacturerInformationCtrl.edit.viewerColour}" style="cursor:pointer;" readonly/></p>
                    </div>
                    <div class="manufacturer-details">
                        <p translate>ADMIN.LOGO</p>
                        <p ng-hide="manufacturerInformationCtrl.isEdit">
                            <img ng-src="{{manufacturerInformationCtrl.logoResponseUrl}}"/>
                        </p>
                        <div ng-show="manufacturerInformationCtrl.isEdit">
                            <div ng-show="manufacturerInformationCtrl.logoResponseUrl === '' || manufacturerInformationCtrl.logoResponseUrl === null">
                                <button ng-show="manufacturerInformationCtrl.isEdit" type="button" class="btn xsmall primary-outline"
                                        ng-click="manufacturerInformationCtrl.addLogoImage()" translate>ADMIN.UPLOAD_LOGO
                                </button>
                            </div>
                            <div ng-show="manufacturerInformationCtrl.logoResponseUrl != null && manufacturerInformationCtrl.logoResponseUrl != ''">
                                <img class="company-logo" ng-src="{{manufacturerInformationCtrl.logoResponseUrl}}"/>
                                <button ng-show="manufacturerInformationCtrl.isEdit" type="button" class="btn xsmall primary-outline"
                                        ng-click="manufacturerInformationCtrl.addLogoImage()" translate>ADMIN.REPLACE_LOGO
                                </button>
                            </div>
                        </div>
                    </div>

				<div class="small-panel-content text-right">
					  <button ng-show="manufacturerInformationCtrl.isEdit" type="button" class="btn small secondary mr-8"
						    ng-click="manufacturerInformationCtrl.cancelDetails()" translate>GENERAL.CANCEL
					  </button>
					  <button ng-show="manufacturerInformationCtrl.isEdit" type="submit" class="btn small primary" translate>GENERAL.SAVE
					  </button>
					  <button ng-hide="manufacturerInformationCtrl.isEdit" type="button" class="btn small primary"
						    ng-click="manufacturerInformationCtrl.editDetails()" translate>GENERAL.EDIT
					  </button>
				</div>

                </form>


            </div>
        </div>
    </div>
</div>