(function () {
    'use strict';

    angular
        .module('app.parts')
        .controller('SupersedeController', SupersedeController);

    SupersedeController.$inject = ['supersedeObject', 'masterPartService', '$uibModalInstance', 'manufactureManualService'];

    function SupersedeController(supersedeObject, masterPartService, $uibModalInstance, manufactureManualService) {
        var vm = this;
        var isEdit = false;
        var isEditSetup = false;

        vm.masterPartId = supersedeObject.masterPartId;
        vm.partNumber = supersedeObject.partNumber;
        vm.errors = {};
        vm.isMachineDropdownDisabled = true;
        vm.isViewableDropdownDisabled = true;

        vm.rangeChanged = rangeChanged;
        vm.machineChanged = machineChanged;
        vm.save = save;
        vm.cancel = $uibModalInstance.dismiss;

        initialize();

        function initialize() {
            vm.ranges = null;
            vm.rangeId = null;
            vm.machines = null;
            vm.machineId = null;
            vm.viewables = null;
            vm.viewableId = null;
            isEdit = !!supersedeObject.rangeId;
            isEditSetup = isEdit;

            manufactureManualService.getRangeByManufacturer()
                .then(getRangeSuccess, serviceFailure);
        }

        function getRangeSuccess(response) {
            vm.ranges = response.data;
            vm.isRangeDropdownDisabled = false;

            if (isEditSetup) {
                rangeChanged(supersedeObject.rangeId);
            }
        }

        function serviceFailure(err) {
            console.log("Service error: " + err);
            vm.errors.serviceError = true;
        }

        function rangeChanged(rangeId) {
            if (rangeId) {
                vm.isMachineDropdownDisabled = true;
                vm.isViewableDropdownDisabled = true;
                vm.rangeId = rangeId;
                vm.machineId = null;
                vm.machines = null;
                vm.viewableId = null;
                vm.viewables = null;
                manufactureManualService.getMachineByRange(rangeId)
                    .then(machineRangeSuccess, serviceFailure);
            }
        }

        function machineRangeSuccess(response) {
            vm.machines = response.data;
            vm.isMachineDropdownDisabled = false;

            if (isEditSetup) {
                machineChanged(supersedeObject.machineId);
            }
        }

        function machineChanged(machineId) {
            if (machineId) {
                vm.isViewableDropdownDisabled = true;
                vm.machineId = machineId;
                vm.viewables = null;
                vm.viewableId = null;
                manufactureManualService.getCompletedModelUploadsByMachine(machineId)
                    .then(getViewablesByMachineSuccess, serviceFailure);
            }
        }

        function getViewablesByMachineSuccess(response) {
            if (response && response.data.length > 0) {
                vm.isViewableDropdownDisabled = false;
                vm.viewables = response.data;

                if (isEditSetup) {
                    vm.viewableId = supersedeObject.viewableId;
                    isEditSetup = false;
                }
            }
        }

        function save() {
            if (isEdit) {
                masterPartService.editSupersede(vm.masterPartId, vm.viewableId)
                    .then(createEditSupersedeSuccess, serviceFailure);
            } else {
                masterPartService.createSupersede(vm.masterPartId, vm.viewableId)
                    .then(createEditSupersedeSuccess, serviceFailure);
            }
        }

        function createEditSupersedeSuccess() {
            $uibModalInstance.close();
        }
    }
})();
