(function () {
    'use strict';

    angular
        .module('app.parts')
        .controller('CustomerPartSearchTabsController', CustomerPartSearchTabsController);

    CustomerPartSearchTabsController.$inject = ['$state', '$translate'];

    function CustomerPartSearchTabsController($state, $translate) {
        var vm = this;

        vm.isActive = isActive;

        initialize();

        function initialize() {
            $translate(['CUST_PART_SEARCH.PART_SEARCH', 'CUST_PART_SEARCH.MOST_RECENTLY', 'CUST_PART_SEARCH.MOST_FREQUENTLY']).then(function (resp) {
                vm.tabs = [
                    { title: resp['CUST_PART_SEARCH.PART_SEARCH'], route: 'customerPartSearch.search' },
                    { title: resp['CUST_PART_SEARCH.MOST_RECENTLY'], route: 'customerPartSearch.recent' },
                    { title: resp['CUST_PART_SEARCH.MOST_FREQUENTLY'], route: 'customerPartSearch.frequent' }
                ];
            });

            if ($state.current.name === 'customerPartSearch' || $state.current.name === 'customerPartSearch.home') {
                $state.go('customerPartSearch.search');
            }
        }

        function isActive(route) {
            return $state.is(route);
        }
    }
})();