<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" ng-click="editProductCtrl.cancel()" aria-label="Close">
        <i class="fa fa-close" aria-hidden="true"></i>
    </button>

    <h2 ng-if="editProductCtrl.isEditMachine" class="modal-title">
        {{editProductCtrl.isCreateMode ? editProductCtrl.createModal : editProductCtrl.editModal}} {{editProductCtrl.isCreateMode ?
        editProductCtrl.newProduct : editProductCtrl.product}}
    </h2>
    <h2 ng-if="!editProductCtrl.isEditMachine" class="modal-title">
        {{editProductCtrl.isCreateMode ? editProductCtrl.createModal : editProductCtrl.editModal}} {{"PRODUCTS_CATALOG.NEW_RANGE" |
        translate}}
    </h2>
</div>
<div class="modal-body">
    <form name="createProductForm" class="form">
        <div ng-if="editProductCtrl.isEditMachine">
            <div class="input-group">
                <label translate>PRODUCTS_CATALOG.RANGE</label>
                <div class="d-flex w-100 align-items-center">
                    <div class="select-box">
                        <small ng-if="!data.rangeId" translate>PRODUCTS_CATALOG.SELECT_RANGE</small>
                        <select
                            ng-options="rangeValue.id as rangeValue.name for rangeValue in editProductCtrl.rangeValues | orderBy:'name'"
                            ng-model="data.rangeId"
                            ng-change="editProductCtrl.rangeChanged(data.rangeId)"
                        ></select>
                        <div class="select-arrow"></div>
                    </div>
                </div>

                <button class="btn xsmall secondary mt-3" href="" ng-click="editProductCtrl.createNewRangeField()" translate>
                    PRODUCTS_CATALOG.CREATE_NEW_RANGE
                </button>
            </div>

            <div class="input-group">
                <label translate>PRODUCTS_CATALOG.PRODUCT_NAME</label>
                <input
                    type="text"
                    placeholder="{{'PRODUCTS_CATALOG.ENT_PRODUCT_NAME' | translate}}"
                    ng-required="true"
                    ng-model="editProductCtrl.productName"
                />
            </div>
        </div>

        <div ng-if="editProductCtrl.isEditMachine">
            <div class="input-group">
                <label translate>PRODUCTS_CATALOG.ADD_PRODUCT_IMAGE</label>
                <div ng-show="editProductCtrl.productThumbnailUrl != null">
                    <img ng-src="{{editProductCtrl.productThumbnailUrl}}" />
                </div>
            </div>

            <button type="button" class="btn small secondary" ng-click="editProductCtrl.addProductImage()" translate>
                EDIT_PRODUCT.ADD_IMAGE
            </button>
        </div>

        <div class="input-group" ng-if="!editProductCtrl.isEditMachine">
            <label translate>EDIT_PRODUCT.CREATE_RANGE</label>
            <h3 ng-if="editProductCtrl.rangeFailure" class="error-alert">{{editProductCtrl.internalFailureMessage}}</h3>
            <input
                type="text"
                placeholder="{{'EDIT_PRODUCT.ENTER_RANGE' | translate}}"
                ng-model="editProductCtrl.newRange"
                ng-required="true"
            />
        </div>

        <div class="modal-actions" ng-if="editProductCtrl.isEditMachine">
            <button type="button" class="btn small secondary" ng-click="editProductCtrl.cancel()" translate>GENERAL.CANCEL</button>
            <button
                ng-hide="editProductCtrl.isCreateMode"
                type="button"
                class="btn small primary"
                ng-disabled="!createProductForm.$valid"
                ng-click="editProductCtrl.editProduct()"
                translate
            >
                EDIT_PRODUCT.EDIT
            </button>
            <button
                ng-show="editProductCtrl.isCreateMode"
                type="button"
                class="btn small primary"
                ng-disabled="!createProductForm.$valid"
                ng-click="editProductCtrl.createProduct()"
                translate
            >
                PRODUCTS_CATALOG.CREATE_NEW
            </button>
        </div>

        <div class="modal-actions">
            <div ng-if="!editProductCtrl.isEditMachine">
                <button type="button" class="btn small secondary" ng-click="editProductCtrl.cancelRange()" translate>GENERAL.CANCEL</button>
                <button
                    type="button"
                    class="btn small primary"
                    ng-disabled="!createProductForm.$valid"
                    ng-click="editProductCtrl.createNewRange()"
                    translate
                >
                    EDIT_PRODUCT.CREATE_RANGE
                </button>
            </div>
        </div>
    </form>
</div>
