(function () {
    'use strict';

    angular
        .module('app.publications')
        .controller('PublicationsController', PublicationsController);

    PublicationsController.$inject = ['manufactureManualService', '$scope', '$uibModal', '$filter', '$translate', 'publicationService', '$transitions' , '$window'];

    function PublicationsController(manufactureManualService, $scope, $uibModal, $filter, $translate, publicationService, $transitions, $window) {
        var vm = this;
        var dropdownStateKey = 'publicationsDropdownState';

        vm.isManualsLoaded = false;

            vm.loadingInfiniteScrollData = false;
        vm.isOrdersLoaded = false;
        vm.showBackToTopButton = false;

        vm.endRecord = vm.itemPerPage;
        vm.createManualPopUp = createManualPopUp;
        vm.editManual = editManual;
        vm.openDeletePopup = openDeletePopup;
        vm.publishUnpublishManual = publishUnpublishManual;
        vm.filterViewableDropdown = filterViewableDropdown;

        vm.displayAdvancedFilter = false;
        vm.filterValue = {};

        vm.customers = [''];
        vm.filter_customer = "";
        vm.status = ['', 'PUBLISHED', 'UNPUBLISHED'];
        vm.filter_status = "";
        vm.ranges = [''];
        vm.filter_range = "";
        vm.viewables = [''];
        vm.filter_viewable = "";
        vm.filterViewableValue = "";

        vm.sortReverse = true;
        vm.isFixedHeader = false;
        vm.publication_sort = 'createdDate';
        vm.fetchPublications = publicationService.fetchPublications;
        vm.publishUnpublishManual = publicationService.publishUnpublishManual;
        vm.publishManual = publicationService.publishManual;

        vm.toggleAdvancedFilter = toggleAdvancedFilter;
        vm.searchFilterChange = searchFilterChange;
        vm.applyFilter = applyFilter;
        vm.clearFilter = clearFilter;
        vm.manualNameFilter = manualNameFilter;
        vm.combinedModelStatusFilter = combinedModelStatusFilter;
        vm.scrollToTop = scrollToTop;
        vm.loadMoreInfiniteScroll = loadMoreInfiniteScroll;

        initialize();

        function initialize() {

            var savedFilters = JSON.parse(localStorage.getItem('publicationsFilters') || '{}');
            vm.filter_status = savedFilters.filter_status || vm.status[0];
            vm.filter_viewable = savedFilters.filter_viewable || vm.viewables[0];

            var savedDropdownState = localStorage.getItem(dropdownStateKey);

            if (savedDropdownState === 'open' || vm.filter_status || vm.filter_viewable) {
                vm.displayAdvancedFilter = true;
            } else {
                vm.displayAdvancedFilter = false;
            }

            vm.loadingInfiniteScrollData = true;
            fetchModels(); // fetch the models

            fetchPublications() // fetch the publications
                .then(() => applyFilter()) // apply the filters
                .catch(error => { // if there's an error, log it and continue
                    console.error("An error occurred:", error);
                });

        }


        function fetchModels() {
            manufactureManualService.fetchModels().then(function(response) {
                vm.modelsList = response.data;
                vm.viewables = vm.modelsList.map(model => model.modelName);
            });
            // applyFilter();
        }

        function fetchPublications() {
            if (vm.isListFiltered) {
                return Promise.resolve(); // Return a resolved Promise when filtered
            }
            return manufactureManualService.fetchPublications()  // fetch the publications
                .then(fetchPublicationsSuccess)
                .catch(fetchPublicationsFailed);
        }

        function fetchPublicationsSuccess(response) {
            vm.originalManualList = response.data.publications;  // store the original list
            vm.ManualList = angular.copy(vm.originalManualList.slice(0, 100)); // start with the full list
            vm.totalItems = vm.originalManualList.length;
            vm.isManualsLoaded = true;
            handleInfiniteScroll();

            for (var n = 0; n < vm.totalItems; n++) {
                var customer = vm.ManualList[n].customers;
                var coverImage = vm.ManualList[n].coverImage;

                if (!vm.customers.includes(customer)) {
                    vm.customers.push(customer);
                }
            }
        }

        function fetchPublicationsFailed(error) {
            vm.ManualsuccessMessage = false;
            vm.isManualsLoaded = true;
        }

        function createManualPopUp() {
            $uibModal.open({
                templateUrl: 'features/publications/createManual.html',
                controller: 'CreateManualController',
                controllerAs: 'createManualCtrl',
                size: 'lg',
                resolve: {
                    modalObject: function () {
                        return null;
                    }
                }
            }).result.then(function () {
                fetchPublications();
            }, function () {
                console.log('Modal Cancelled');
            });
        }

        function editManual(publication) {

            manufactureManualService.fetchManuals(publication.id).then(function(manualResponse) {
                var manual = manualResponse.data;
                if (!manual) {
                    console.error(`Manual with the id ${publication.id} not found`);
                    return;
                }

                var models = manual.modelId.map(id => {
                    return vm.modelsList.find(model => model.modelId === id);
                });
                // Wait for all the models to be fetched before opening the modal
                Promise.all(models).then(function(resolvedModels) {
                    manual.modelNames = resolvedModels.map(model => model.modelName);
                    // Set the isEdit flag
                    manual.isEdit = true;
                    $uibModal.open({
                        templateUrl: 'features/publications/createManual.html',
                        controller: 'CreateManualController',
                        controllerAs: 'createManualCtrl',
                        size: 'md',
                        resolve: {
                            modalObject: function() {
                                return manual;
                            }
                        }
                    }).result.then(function() {
                        fetchPublications();
                    }, function() {
                        console.log('Modal Cancelled');
                    });
                });
            }).catch(function(error) {
                console.error(`An error occurred while fetching the manual: ${error}`);
            });
        }

        function handleFilterChange() {
            var filters = {
                filter_status: vm.filter_status,
                filter_viewable: vm.filter_viewable
            };
            localStorage.setItem('publicationsFilters', JSON.stringify(filters));
            localStorage.setItem(dropdownStateKey, vm.displayAdvancedFilter ? 'open' : 'closed');
        }

        function openDeletePopup(publications) {
            var deleteObject = {
                name: publications.name,
                id: publications.id,
                url: '/manual/' + publications.id
            };
            $uibModal.open({
                templateUrl: 'features/shared/commonDelete/deleteDialogBox.html',
                controller: 'DeleteController',
                controllerAs: 'deleteCtrl',
                size: 'sm',
                resolve: {
                    deleteObject: function () {
                        return deleteObject;
                    }
                }
            }).result.then(function () {
                fetchPublications();
            }, function () {
                console.log('Modal Cancelled');
            });
        }

        function publishUnpublishManual(publications) {
            publicationService.publishUnpublishManual(publications.id, publications.status)
                .then(function (response) {
                    fetchPublications();
                })
                .catch(function (error) {
                    console.log(error);
                });
        }


        function toggleAdvancedFilter() {
            vm.displayAdvancedFilter = !vm.displayAdvancedFilter;

            if (!vm.displayAdvancedFilter) {
                clearClosedFilter();
            }

            $window.localStorage.setItem(dropdownStateKey, vm.displayAdvancedFilter ? 'open' : 'closed');
        }

        function searchFilterChange() {
            updateTotalItemCount();
        }

        function updateTotalItemCount() {
            var textFilter = $filter('filter')(vm.ManualList, vm.searchValue);
            vm.totalItems = $filter('filter')(textFilter, vm.filterValue, true).length;
        }

        function filterViewableDropdown() {
            var value = vm.filterViewableValue.toLowerCase();

            var dropdownItems = document.querySelectorAll(".dropdown-menu li a");
            dropdownItems.forEach(function(item) {
                if (item.textContent.toLowerCase().indexOf(value) > -1) {
                    item.parentElement.style.display = "";
                } else {
                    item.parentElement.style.display = "none";
                }
            });
        }


        function combinedModelStatusFilter(publication) {
            return manualNameFilter(publication) && statusFilter(publication) && modelFilter(publication);
        }

        function modelFilter(publication) {
            // If there's no model filter applied, return true for all publications
            if (!vm.filterValue.modelName || vm.filterValue.modelName.length === 0) {
                return true;
            }

            // Check if any of the publication's modelNames matches the selected model's modelName
            return publication.modelName.some(model => vm.filterValue.modelName.includes(model));
        }

        function manualNameFilter(publication) {
            if (vm.filterValue.name && vm.filterValue.name.length) {
                return vm.filterValue.name.includes(publication.name);
            }
            return true;
        }

        function statusFilter(publication) {
            if (vm.filterValue.status) {
                return publication.status === vm.filterValue.status;
            }
            return true;
        }

        function applyFilter() {
            vm.filterValue = {};
            vm.ManualList = angular.copy(vm.originalManualList);  // Reset to the original list before filtering

            if (vm.filter_customer) {
                vm.filterValue.customers = vm.filter_customer;
            }
            if (vm.filter_status) {
                vm.filterValue.status = vm.filter_status;
            }
            if (vm.filter_viewable) {
                vm.filterValue.viewable = vm.filter_viewable;

                vm.ManualList = vm.ManualList.filter(publication => {
                    return publication.viewables && publication.viewables.includes(vm.filterValue.viewable);
                });
            }
            handleFilterChange();
            updateTotalItemCount();
            vm.isListFiltered = true;
            vm.isFixedHeader = false;
        }

        function clearFilter() {
            vm.filterValue = {};
            vm.ManualList = angular.copy(vm.originalManualList);
            vm.totalItems = vm.ManualList.length;

            vm.filter_customer = vm.customers[0];
            vm.filter_status = vm.status[0];
            vm.filter_viewable = '';
            vm.filterViewableValue = '';

            var searchBox = document.getElementById("searchInput");
            if(searchBox) {
                searchBox.value = "";
            }
            vm.searchValue = "";

            vm.displayAdvancedFilter = false;

            localStorage.removeItem(dropdownStateKey);
            localStorage.removeItem('publicationsFilters');
            sessionStorage.removeItem("searchValue-'publications'");
            sessionStorage.removeItem("searchValue-publications");

            vm.isListFiltered = true;
            fetchPublications();
        }

        function clearClosedFilter() {
            vm.filterValue = {};
            vm.ManualList = angular.copy(vm.originalManualList);
            vm.totalItems = vm.ManualList.length;

            vm.filter_customer = vm.customers[0];
            vm.filter_status = vm.status[0];
            vm.filter_viewable = '';
            vm.filterViewableValue = '';

            vm.displayAdvancedFilter = false;

            localStorage.removeItem(dropdownStateKey);
            localStorage.removeItem('publicationsFilters');
            sessionStorage.removeItem("searchValue-'publications'");
            sessionStorage.removeItem("searchValue-publications");

            vm.isListFiltered = true;
            fetchPublications();
        }

        var lastScrollTop = 0;
window.addEventListener('scroll', handleInfiniteScroll);

function handleInfiniteScroll() {
    var threshold = 250;
    var scrollTop = window.scrollY;

    if (scrollTop > lastScrollTop) {
        vm.isFixedHeader = scrollTop > threshold;
    } else if (scrollTop < threshold){
        vm.isFixedHeader = false;
    }
    lastScrollTop = scrollTop;  

    
    if (window.innerHeight + scrollTop >= document.body.offsetHeight - 2 && !vm.loadingInfiniteScrollData && !vm.initialLoadDone) {
        loadMoreInfiniteScroll();
    }
}

        function loadMoreInfiniteScroll() {
            vm.loadingInfiniteScrollData = true;

            var nextItems = vm.originalManualList.slice(vm.manualList.length, vm.manualList.length + 100);
            if (nextItems.length === 0) {
                vm.initialLoadDone = true;
                vm.loadingInfiniteScrollData = false;
                return;
            }

            vm.manualList = vm.manualList.concat(nextItems);

            vm.loadingInfiniteScrollData = false;
            if (vm.manualList.length >= vm.totalItems) {
                vm.initialLoadDone = true;
            }
        }


  function scrollToTop() {
      $window.scrollTo({ top: 0, behavior: "smooth" });
      $("html, body").animate({ scrollTop: 0 }, "slow", function () {
        $("#scrollToTop").removeClass("scrolled-past");
      });
    }

    angular.element($window).on("scroll", function () {
      vm.showBackToTopButton = this.pageYOffset > 100;
      $scope.$apply();
    });

        vm.actions = [
            {
                title: "Edit",
                onClick: function (entity) { vm.editManual(entity); },
                icon: "fa-check-square-o",
                label: function () { return $translate.instant("PUBLICATIONS.EDIT_MANUAL"); }
            },
            {
                title: "Unpublish",
                onClick: function (entity) { vm.publishUnpublishManual(entity); },
                icon: "fa-check-square-o",
                label: function () { return $translate.instant("PUBLICATIONS.UNPUBLISH"); },
                show: function (publications) { return publications.status === 'PUBLISHED'; }
            },
            {
                title: "Publish",
                onClick: function (entity) { vm.publishUnpublishManual(entity); },
                icon: "fa-check-square-o",
                label: function () { return $translate.instant("PUBLICATIONS.PUBLISH"); },
                show: function (publications) { return publications.status === 'UNPUBLISHED'; }
            },
            {
                title: "Delete",
                onClick: function (entity) { vm.openDeletePopup(entity); },
                icon: "fa-trash",
                label: function () { return $translate.instant("PUBLICATIONS.DELETE"); }
            }
        ];



    }
})();