(function () {
    'use strict';

    angular
        .module('app.publications')
        .controller('PublicationsController', PublicationsController);

    PublicationsController.$inject = ['manufacturerPublicationService', '$scope', '$uibModal', '$filter', '$translate', 'publicationService', '$state' , '$window', '$rootScope'];

    function PublicationsController(manufacturerPublicationService, $scope, $uibModal, $filter, $translate, publicationService, $state, $window, $rootScope) {
        var vm = this;
        var dropdownStateKey = 'publicationsDropdownState';

        vm.isPublicationsLoaded = false;

            vm.loadingInfiniteScrollData = false;
        vm.isOrdersLoaded = false;
        vm.showBackToTopButton = false;

        vm.endRecord = vm.itemPerPage;
        vm.createPublication = createPublication;
        vm.editPublication = editPublication;
        vm.openDeletePopup = openDeletePopup;
        vm.publishUnpublishManual = publishUnpublishManual;
        vm.filterViewableDropdown = filterViewableDropdown;

        vm.displayAdvancedFilter = false;
        vm.filterValue = {};

        vm.customers = [''];
        vm.filter_customer = "";
        vm.status = ['', 'PUBLISHED', 'UNPUBLISHED'];
        vm.filter_status = "";
        vm.ranges = [''];
        vm.filter_range = "";
        vm.viewables = [''];
        vm.filter_viewable = "";
        vm.filterViewableValue = "";

        vm.sortReverse = true;
        vm.isFixedHeader = false;
        vm.publication_sort = 'createdDate';
        vm.fetchPublications = publicationService.fetchPublications;
        vm.publishUnpublishManual = publicationService.publishUnpublishManual;
        vm.publishManual = publicationService.publishManual;

        vm.toggleAdvancedFilter = toggleAdvancedFilter;
        vm.searchFilterChange = searchFilterChange;
        vm.applyFilter = applyFilter;
        vm.clearFilter = clearFilter;
        vm.manualNameFilter = manualNameFilter;
        vm.combinedModelStatusFilter = combinedModelStatusFilter;
        vm.scrollToTop = scrollToTop;
        vm.loadMoreInfiniteScroll = loadMoreInfiniteScroll;

        initialize();

        function initialize() {

            var savedFilters = JSON.parse(localStorage.getItem('publicationsFilters') || '{}');
            vm.filter_status = savedFilters.filter_status || vm.status[0];
            vm.filter_viewable = savedFilters.filter_viewable || vm.viewables[0];

            var savedDropdownState = localStorage.getItem(dropdownStateKey);

            if (savedDropdownState === 'open' || vm.filter_status || vm.filter_viewable) {
                vm.displayAdvancedFilter = true;
            } else {
                vm.displayAdvancedFilter = false;
            }

            vm.loadingInfiniteScrollData = true;
            
            fetchPublications() // fetch the publications
                .then(() => {
                    applyFilter(); // apply the filters
                })
                .catch(error => { // if there's an error, log it and continue
                    console.error("An error occurred:", error);
                });
                
            // Listen for publication status changes
            var statusChangedListener = $scope.$on('publication:statusChanged', function(event, data) {
                console.log('Received publication:statusChanged event', data);
                
                // Update the publication status in both local lists
                if (data.publicationId) {
                    var newStatus = data.newStatus !== undefined ? data.newStatus : data.published;
                    
                    // Update in the main publication list
                    var publicationInList = vm.publicationList.find(p => p.id === data.publicationId);
                    if (publicationInList) {
                        publicationInList.published = newStatus;
                    }
                    
                    // Update in the original publication list
                    var publicationInOriginalList = vm.originalPublicationList.find(p => p.id === data.publicationId);
                    if (publicationInOriginalList) {
                        publicationInOriginalList.published = newStatus;
                    }
                }
                
                // Reapply filters to ensure filtered views update immediately
                if (vm.filter_status || vm.filter_viewable || vm.searchValue) {
                    applyFilter();
                }
                // Ensure Angular updates the UI
                if (!$scope.$$phase) {
                    $scope.$apply();
                }
            });
            
            // Listen for publication deletion
            var deletedListener = $scope.$on('publication:deleted', function(event, data) {
                console.log('Received publication:deleted event', data);
                if (vm.originalPublicationList) {
                    vm.originalPublicationList = vm.originalPublicationList.filter(p => p.id !== data.publicationId);
                }
                if (vm.publicationList) {
                    vm.publicationList = vm.publicationList.filter(p => p.id !== data.publicationId);
                }
                vm.totalItems = vm.originalPublicationList ? vm.originalPublicationList.length : 0;
                if (!$scope.$$phase) {
                    $scope.$apply();
                }
            });
            
            // Clean up the listeners when the scope is destroyed
            $scope.$on('$destroy', function() {
                statusChangedListener();
                deletedListener();
            });
        }


        function fetchPublications() {
            if (vm.isListFiltered) {
                return Promise.resolve(); // Return a resolved Promise when filtered
            }
            return manufacturerPublicationService.fetchPublications()  // fetch the publications
                .then(fetchPublicationsSuccess)
                .catch(fetchPublicationsFailed);
        }

        function fetchPublicationsSuccess(response) {
            vm.originalPublicationList = response.data.publications;  // store the original list
            vm.publicationList = angular.copy(vm.originalPublicationList.slice(0, 100)); // start with the full list
            
            // Process customer names for display
            processPublicationsCustomerNames(vm.originalPublicationList);
            processPublicationsCustomerNames(vm.publicationList);
            
            // Extract unique viewable names from all publications for the dropdown
            var allViewableNames = [];
            vm.originalPublicationList.forEach(function(publication) {
                if (publication.viewables && publication.viewables.length > 0) {
                    publication.viewables.forEach(function(viewable) {
                        if (viewable.name && allViewableNames.indexOf(viewable.name) === -1) {
                            allViewableNames.push(viewable.name);
                        }
                    });
                }
            });
            vm.viewables = [''].concat(allViewableNames.sort());
            
            vm.totalItems = vm.originalPublicationList.length;
            vm.isPublicationsLoaded = true;
            handleInfiniteScroll();
            
            for (var n = 0; n < vm.publicationList.length; n++) {
                var customer = vm.publicationList[n].customers;
                var coverImage = vm.publicationList[n].coverImage;

                if (!vm.customers.includes(customer)) {
                    vm.customers.push(customer);
                }
            }
        }
        
        function formatCustomerNames(customers) {
            if (!customers || customers.length === 0) return '';
            
            var names = [];
            for (var i = 0; i < Math.min(customers.length, 3); i++) {
                if (customers[i] && customers[i].name) {
                    names.push(customers[i].name);
                }
            }
            return names.join(', ');
        }
        
        function processPublicationsCustomerNames(publications) {
            if (!publications) return;
            
            for (var i = 0; i < publications.length; i++) {
                if (publications[i].customers && publications[i].customers.length > 0) {
                    publications[i].customerNamesString = formatCustomerNames(publications[i].customers);
                }
            }
            return publications;
        }

        function fetchPublicationsFailed(error) {
            vm.publicationSuccessMessage = false;
            vm.isPublicationsLoaded = true;
        }

        function createPublication() {
            $state.go('createPublication');
        }

        function editPublication(publication) {
            $state.go('editPublication', { id: publication.id });
        }

        function handleFilterChange() {
            var filters = {
                filter_status: vm.filter_status,
                filter_viewable: vm.filter_viewable
            };
            localStorage.setItem('publicationsFilters', JSON.stringify(filters));
            localStorage.setItem(dropdownStateKey, vm.displayAdvancedFilter ? 'open' : 'closed');
        }

        function openDeletePopup(publications) {
            var deleteObject = {
                name: publications.name,
                id: publications.id,
                url: '/manual/' + publications.id
            };
            $uibModal.open({
                templateUrl: 'features/shared/commonDelete/deleteDialogBox.html',
                controller: 'DeleteController',
                controllerAs: 'deleteCtrl',
                size: 'sm',
                resolve: {
                    deleteObject: function () {
                        return deleteObject;
                    }
                }
            }).result.then(function () {
                // Broadcast the deletion event
                $rootScope.$broadcast('publication:deleted', {
                    publicationId: publications.id
                });
                fetchPublications();
            }, function () {
                console.log('Modal Cancelled');
            });
        }

        function publishUnpublishManual(publications) {
            publicationService.publishUnpublishManual(publications)
                .then(function (response) {
                    console.log('Publication status updated successfully');
                    // The service already handles status updates and broadcasts the event
                    // No need to duplicate that logic here
                })
                .catch(function (error) {
                    console.log('Error updating publication status:', error);
                });
        }


        function toggleAdvancedFilter() {
            vm.displayAdvancedFilter = !vm.displayAdvancedFilter;

            if (!vm.displayAdvancedFilter) {
                clearClosedFilter();
            }

            $window.localStorage.setItem(dropdownStateKey, vm.displayAdvancedFilter ? 'open' : 'closed');
        }

        function searchFilterChange() {
            updateTotalItemCount();
        }

        function updateTotalItemCount() {
            var textFilter = $filter('filter')(vm.publicationList, vm.searchValue);
            vm.totalItems = $filter('filter')(textFilter, vm.filterValue, true).length;
        }

        function filterViewableDropdown() {
            var value = vm.filterViewableValue.toLowerCase();

            var dropdownItems = document.querySelectorAll(".dropdown-menu li a");
            dropdownItems.forEach(function(item) {
                if (item.textContent.toLowerCase().indexOf(value) > -1) {
                    item.parentElement.style.display = "";
                } else {
                    item.parentElement.style.display = "none";
                }
            });
        }


        function combinedModelStatusFilter(publication) {
            return manualNameFilter(publication) && statusFilter(publication) && modelFilter(publication);
        }

        function modelFilter(publication) {
            // If there's no model filter applied, return true for all publications
            if (!vm.filterValue.modelName || vm.filterValue.modelName.length === 0) {
                return true;
            }

            // Check if any of the publication's modelNames matches the selected model's modelName
            return publication.modelName.some(model => vm.filterValue.modelName.includes(model));
        }

        function manualNameFilter(publication) {
            if (vm.filterValue.name && vm.filterValue.name.length) {
                return vm.filterValue.name.includes(publication.name);
            }
            return true;
        }

        function statusFilter(publication) {
            if (vm.filterValue.status) {
                if (vm.filterValue.status === 'PUBLISHED') {
                    return publication.published === true;
                } else if (vm.filterValue.status === 'UNPUBLISHED') {
                    return publication.published === false;
                }
            }
            return true;
        }

        function applyFilter() {
            vm.filterValue = {};
            vm.publicationList = angular.copy(vm.originalPublicationList);  // Reset to the original list before filtering
            
            // Ensure customer names are processed after resetting the list
            processPublicationsCustomerNames(vm.publicationList);

            if (vm.filter_customer) {
                vm.filterValue.customers = vm.filter_customer;
            }
            if (vm.filter_status) {
                vm.filterValue.status = vm.filter_status;
            }
            if (vm.filter_viewable) {
                vm.filterValue.viewable = vm.filter_viewable;

                vm.publicationList = vm.publicationList.filter(publication => {
                    return publication.viewables && publication.viewables.some(viewable => viewable.name === vm.filterValue.viewable);
                });
            }
            handleFilterChange();
            updateTotalItemCount();
            vm.isListFiltered = true;
            vm.isFixedHeader = false;
        }

        function clearFilter() {
            vm.filterValue = {};
            vm.publicationList = angular.copy(vm.originalPublicationList);
            processPublicationsCustomerNames(vm.publicationList);
            vm.totalItems = vm.publicationList.length;

            vm.filter_customer = vm.customers[0];
            vm.filter_status = vm.status[0];
            vm.filter_viewable = '';
            vm.filterViewableValue = '';

            var searchBox = document.getElementById("searchInput");
            if(searchBox) {
                searchBox.value = "";
            }
            vm.searchValue = "";

            vm.displayAdvancedFilter = false;

            localStorage.removeItem(dropdownStateKey);
            localStorage.removeItem('publicationsFilters');
            sessionStorage.removeItem("searchValue-'publications'");
            sessionStorage.removeItem("searchValue-publications");

            vm.isListFiltered = true;
            fetchPublications();
        }

        function clearClosedFilter() {
            vm.filterValue = {};
            vm.publicationList = angular.copy(vm.originalPublicationList);
            processPublicationsCustomerNames(vm.publicationList);
            vm.totalItems = vm.publicationList.length;

            vm.filter_customer = vm.customers[0];
            vm.filter_status = vm.status[0];
            vm.filter_viewable = '';
            vm.filterViewableValue = '';

            vm.displayAdvancedFilter = false;

            localStorage.removeItem(dropdownStateKey);
            localStorage.removeItem('publicationsFilters');
            sessionStorage.removeItem("searchValue-'publications'");
            sessionStorage.removeItem("searchValue-publications");

            vm.isListFiltered = true;
            fetchPublications();
        }

        var lastScrollTop = 0;
window.addEventListener('scroll', handleInfiniteScroll);

function handleInfiniteScroll() {
    var threshold = 250;
    var scrollTop = window.scrollY;

    if (scrollTop > lastScrollTop) {
        vm.isFixedHeader = scrollTop > threshold;
    } else if (scrollTop < threshold){
        vm.isFixedHeader = false;
    }
    lastScrollTop = scrollTop;  

    
    if (window.innerHeight + scrollTop >= document.body.offsetHeight - 2 && !vm.loadingInfiniteScrollData && !vm.initialLoadDone) {
        loadMoreInfiniteScroll();
    }
}

        function loadMoreInfiniteScroll() {
            vm.loadingInfiniteScrollData = true;

            var nextItems = vm.originalPublicationList.slice(vm.publicationList.length, vm.publicationList.length + 100);
            if (nextItems.length === 0) {
                vm.initialLoadDone = true;
                vm.loadingInfiniteScrollData = false;
                return;
            }

            vm.publicationList = vm.publicationList.concat(nextItems);

            vm.loadingInfiniteScrollData = false;
            if (vm.publicationList.length >= vm.totalItems) {
                vm.initialLoadDone = true;
            }
        }


  function scrollToTop() {
      $window.scrollTo({ top: 0, behavior: "smooth" });
      $("html, body").animate({ scrollTop: 0 }, "slow", function () {
        $("#scrollToTop").removeClass("scrolled-past");
      });
    }

    angular.element($window).on("scroll", function () {
      vm.showBackToTopButton = this.pageYOffset > 100;
      $scope.$apply();
    });

        vm.actions = [
            {
                title: "Edit",
                onClick: function (entity) { vm.editPublication(entity); },
                icon: "fa-check-square-o",
                label: function () { return $translate.instant("PUBLICATIONS.EDIT_MANUAL"); }
            },
            {
                title: "Unpublish",
                onClick: function (entity) { vm.publishUnpublishManual(entity); },
                icon: "fa-check-square-o",
                label: function () { return $translate.instant("PUBLICATIONS.UNPUBLISH"); },
                show: function (publications) { return publications.published === true; }
            },
            {
                title: "Publish",
                onClick: function (entity) { vm.publishUnpublishManual(entity); },
                icon: "fa-check-square-o",
                label: function () { return $translate.instant("PUBLICATIONS.PUBLISH"); },
                show: function (publications) { return publications.published === false; }
            },
            {
                title: "Delete",
                onClick: function (entity) { vm.openDeletePopup(entity); },
                icon: "fa-trash",
                label: function () { return $translate.instant("PUBLICATIONS.DELETE"); }
            }
        ];

    }
})();