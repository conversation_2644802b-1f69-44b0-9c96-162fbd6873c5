(function () {
    'use strict';

    angular
        .module('app.services')
        .factory('viewerSettingsService', viewerSettingsService);

    viewerSettingsService.$inject = ['$http', 'apiConstants'];

    function viewerSettingsService($http, apiConstants) {
        return {
            updateViewerSettings: updateViewerSettings
        };

        function updateViewerSettings(modelId, viewerSettingsData) {
            return $http.put(apiConstants.url + '/model/' + modelId + '/settings', viewerSettingsData);
        }
    }

})();