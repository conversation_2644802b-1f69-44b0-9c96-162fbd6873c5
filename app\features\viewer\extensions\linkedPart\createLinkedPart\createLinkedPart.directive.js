(function () {
    'use strict';

    angular
        .module('app.viewer')
        .directive('createLinkedPart', createLinkedPart);

    function createLinkedPart() {
        var directive = {
            restrict: 'E',
            templateUrl: 'features/viewer/extensions/linkedPart/createLinkedPart/createLinkedPart.html',
            controller: 'CreateLinkedPartController',
            controllerAs: 'createLinkedPartCtrl',
            bindToController: true
        };
        return directive;
    }

})();