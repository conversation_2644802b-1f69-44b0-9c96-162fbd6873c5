(function () {
    'use strict';

    angular
        .module('app.parts')
        .controller('RemoveSupersessionController', RemoveSupersessionController);

    RemoveSupersessionController.$inject = ['$uibModalInstance', 'masterPartService', 'userService', '$stateParams', 'masterPartId', 'isSuperseded', 'supersessionHistoryResponse'];

    function RemoveSupersessionController($uibModalInstance, masterPartService, userService, $stateParams, masterPartId, isSuperseded, supersessionHistoryResponse) {
        var vm = this;

        vm.cancel = cancel;
        vm.remove = remove;
        vm.isSuperseded = isSuperseded;
        vm.removeOption = 'option1';
        vm.masterPartId = masterPartId;
        vm.isDeleteSupersession = false;

        var manufacturerId = userService.getManufacturerId();
        var masterpartId = vm.masterPartId;

        vm.isLastItem = supersessionHistoryResponse && supersessionHistoryResponse.length > 0 &&
            String(supersessionHistoryResponse[supersessionHistoryResponse.length - 1].masterPartId) === String(vm.masterPartId);

        function removePartFromSupersession() {
            masterPartService.removePartFromSupersession(manufacturerId, masterpartId).then(
                function (response) {
                    vm.isDeleteSupersession = false;
                    $uibModalInstance.close(response);
                },
                function (error) {
                    vm.isDeleteSupersession = false;
                    console.error("Failed to remove part from supersession:", error);
                }
            );
        }

        function splitPartFromSupersession() {
            masterPartService.splitPartFromSupersession(manufacturerId, masterpartId).then(
                function (response) {
                    vm.isDeleteSupersession = false;
                    $uibModalInstance.close(response);
                },
                function (error) {
                    vm.isDeleteSupersession = false;
                    console.error("Failed to split part from supersession:", error);
                }
            );
        }

        function remove() {
            vm.isDeleteSupersession = true;

            if (!vm.isSuperseded) {
                splitPartFromSupersession();
            } else {
                var isLastItem = supersessionHistoryResponse && supersessionHistoryResponse.length > 0 &&
                    String(supersessionHistoryResponse[supersessionHistoryResponse.length - 1].masterPartId) === String(vm.masterPartId);

                if (isLastItem) {
                    splitPartFromSupersession();
                } else {
                    if (vm.removeOption === 'option1') {
                        console.log('Removing part from supersession');
                        removePartFromSupersession();
                    } else if (vm.removeOption === 'option2') {
                        console.log('Splitting part from supersession');
                        splitPartFromSupersession();
                    }
                }
            }
        }

        function cancel() {
            $uibModalInstance.dismiss();
        }
    }
})();