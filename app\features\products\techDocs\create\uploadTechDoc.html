<div class="modal-header info">
    <button type="button" class="close" data-dismiss="modal" ng-click="uploadTechDocCtrl.cancel()"
            aria-label="Close">
        <i class="fa fa-close" aria-hidden="true"></i>
    </button>

	  <h2 ng-if="!uploadTechDocCtrl.isEdit" class="modal-title" translate>UPLOAD_TECH_DOC.TITLE</h2>
	  <h2 ng-if="uploadTechDocCtrl.isEdit" class="modal-title" translate>UPLOAD_TECH_DOC.EDIT</h2>
</div>

<form class="form" ng-submit="uploadTechDocCtrl.save()">
<div class="modal-body">

    <div class="input-group">
        <label translate>UPLOAD_TECH_DOC.NAME</label>
        <input ng-model="uploadTechDocCtrl.name" type="text" required
               placeholder="{{'UPLOAD_TECH_DOC.NAME' | translate}}">
    </div>

    <div class="input-group">
        <label translate>UPLOAD_TECH_DOC.DESCRIPTION</label>
        <input ng-model="uploadTechDocCtrl.description" type="text" required
               placeholder="{{'UPLOAD_TECH_DOC.DESCRIPTION' | translate}}">
    </div>

    <h4 translate>UPLOAD_TECH_DOC.TECH_DOC</h4>
    <div class="upload-box">
        <div>
            <i class="fa fa-upload"></i>
            <h4 class="file-uploader" translate>UPLOAD_TECH_DOC.CHOOSE_FILE</h4>
            <input ng-required="!uploadTechDocCtrl.isEdit" type="file" class="fileupload" ng-click="$event = $event"
                   onchange="angular.element(this).scope().uploadTechDocCtrl.techDocAttached(event)"
                   accept=".pdf, .xlsx"/>
        </div>
    </div>
    <p>
        <emphasis>{{uploadTechDocCtrl.filename}}</emphasis>
    </p>

    <p class="modal-message" style="color: red" ng-if="uploadTechDocCtrl.error">
        {{uploadTechDocCtrl.error}}
    </p>

    <div class="modal-actions">
        <button class="btn small secondary" href="" ng-click="uploadTechDocCtrl.cancel()" translate>GENERAL.CANCEL</button>
        <button class="btn small primary" href="" ng-hide="uploadTechDocCtrl.isSaving" translate>UPLOAD_TECH_DOC.SAVE</button>
        <button class="btn small primary" href="" ng-show="uploadTechDocCtrl.isSaving">
            <span class="spinner-border text-light" role="status" aria-hidden="true"></span> {{"UPLOAD_TECH_DOC.SAVING" | translate}}
        </button>
    </div>
</div>
</form>