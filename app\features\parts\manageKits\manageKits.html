<div class="manage-kits-header">
  <h1 translate>MANAGE_KITS.HEADING</h1>
  <p translate>MANAGE_KITS.DESCRIPTION</p>
</div>

<section class="responsiveContainer">

    <div id="{{manageKitsCtrl.isFixedHeader ? 'infiniteScrollFixedHeader' : 'infiniteScrollStaticHeader'}}"
      class="flex p-4 p-md-0">
    <search-filter class="col-12 col-md-3" state-name="'manageKits'" value="manageKitsCtrl.searchValue"
      placeholder-key="MANAGE_KITS.FILTER_BY"></search-filter>
    
    <button class="btn primary ml-auto mt-3 mt-md-0 mr-4 col-12 col-md-auto" href="" ng-click="manageKitsCtrl.createKit()"
      translate>
      MANAGE_KITS.CREATE_KIT
    </button>
    </div>

  <table class="table table-bordered">
    <thead>
      <tr>
        <th ng-click="manageKitsCtrl.sort('partNumber')">
          <span translate>MANAGE_KITS.KITS_TABLE.PART_NUMBER</span>
          <span ng-class="manageKitsCtrl.getSortClass('partNumber')"></span>
        </th>
        <th ng-click="manageKitsCtrl.sort('name')">
          <span translate>MANAGE_KITS.KITS_TABLE.NAME</span>
          <span ng-class="manageKitsCtrl.getSortClass('name')"></span>
        </th>
        <th ng-click="manageKitsCtrl.sort('description')">
          <span translate>MANAGE_KITS.KITS_TABLE.DESCRIPTION</span>
          <span ng-class="manageKitsCtrl.getSortClass('description')"></span>
        </th>
        <th>
          <span translate>MANAGE_KITS.KITS_TABLE.ACTIONS</span>
        </th>
      </tr>
    </thead>
    <tbody infinite-scroll="manageKitsCtrl.loadMoreInfiniteScroll()" infinite-scroll-distance="3" infinite-scroll-disabled="manageKitsCtrl.loadingInfiniteScrollData">
      <tr
        ng-repeat="kit in manageKitsCtrl.kits | filter: manageKitsCtrl.searchValue | orderBy:manageKitsCtrl.sortType:manageKitsCtrl.sortReverse"
      >
        <td data-label="{{'MANAGE_KITS.KITS_TABLE.PART_NUMBER' | translate}}">
          {{ kit.partNumber }}
        </td>
        <td data-label="{{'MANAGE_KITS.KITS_TABLE.NAME' | translate}}">
          {{ kit.displayName }}
        </td>
        <td
          class="text-truncate-kits"
          data-label="{{'MANAGE_KITS.KITS_TABLE.DESCRIPTION' | translate}}"
        >
          {{ kit.description }}
        </td>
        <td data-label="{{'MANAGE_KITS.KITS_TABLE.ACTIONS' | translate}}">
          <split-button-dropdown
            main-action="manageKitsCtrl.editKit(kit.id)"
            main-action-label="{{ 'MANAGE_KITS.EDIT_KIT' | translate }}"
            actions="manageKitsCtrl.kitActions"
            entity="kit"
          >
          </split-button-dropdown>
        </td>
      </tr>
      <tr
        ng-show="!manageKitsCtrl.kits.length > 0 && manageKitsCtrl.isKitsLoaded"
      >
        <td class="emptytable" colspan="5" translate>MANAGE_KITS.NO_KITS</td>
      </tr>
    </tbody>
  </table>

    <span ng-click="manageKitsCtrl.scrollToTop()" id="backToTopBtn" title="Go to top" class="fas fa-arrow-alt-circle-up"
      ng-show="manageKitsCtrl.showBackToTopButton"></span>
</section>
