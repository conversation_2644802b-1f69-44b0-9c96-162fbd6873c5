<div class="sidebar-content overflow-sidebar-fix" ng-show="kitBuilderCtrl.isOpen">
    <p translate>KIT_BUILDER.CREATE_KITS</p>

    <button class="btn primary" ng-click="kitBuilderCtrl.createKit()" translate>KIT_BUILDER.CREATE_NEW</button>

    <h4 translate>KIT_BUILDER.MANAGE_EXISTING</h4>

    <table ng-show="kitBuilderCtrl.existingKits.length > 0" class="tableViewer table-bordered w-100 bg-white ml-0">
        <tbody>
        <tr ng-repeat="kit in kitBuilderCtrl.existingKits">
            <td class="side-menu-table-name">{{kit.title}}</td>
            <td class="has-dropdown">
                <div class="btn-group">
                    <a href="" class="btn xsmall secondary main-action" ng-click="kitBuilderCtrl.editKit(kit)" translate>
                        KIT_BUILDER.EDIT_KIT
                    </a>
                    <div href="" class="btn xsmall secondary dropdown-toggle" data-toggle="dropdown"
                         aria-haspopup="true" aria-expanded="false">
                        <div class="sub-popup">
                            <ul class="more-options">
                                <li title="Edit Kit">
                                    <a href="" class="dark-secondary" ng-click="kitBuilderCtrl.editKit(kit)"><i
                                            class="fa fa-fw fa-pencil"></i> {{"KIT_BUILDER.EDIT" | translate}}</a>
                                </li>
                                <li title="Delete">
                                    <a href="" class="delete" ng-click="kitBuilderCtrl.deleteKit(kit)"><i
                                            class="fa fa-fw fa-trash"></i> {{"KIT_BUILDER.DELETE" | translate}}</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </td>
        </tr>
        </tbody>
    </table>

    <p ng-hide="kitBuilderCtrl.existingKits.length > 0" translate>
       KIT_BUILDER.NO_KITS
    </p>

</div>