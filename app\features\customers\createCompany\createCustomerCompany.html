<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" ng-click="createCustomerCompanyCtrl.cancel()" aria-label="Close">
        <i class="fa fa-close" aria-hidden="true"></i>
    </button>
    <h2 class="modal-title" translate>CREATE_CUSTOMER.TITLE</h2>
    <h3 ng-if="createCustomerCompanyCtrl.companyFailure" class="error-alert">{{createCustomerCompanyCtrl.internalFailureMessage}}</h3>
</div>

<div class="modal-body" ng-submit="createCustomerCompanyCtrl.createNewCompany()">
    <form class="form" name="createCompanyForm">
        <div class="input-group">
            <label translate>CREATE_CUSTOMER.COMPANY_NAME</label>
            <input
                type="text"
                placeholder="{{'CREATE_CUSTOMER.ENTER_COMPANY' | translate}}"
                ng-model="createCustomerCompanyCtrl.newCompanyName"
                ng-required="true"
            />
        </div>

        <div class="input-group">
            <label translate>CREATE_CUSTOMER.COMPANY_TYPE</label>
            <div class="select-box">
                <select
                    ng-model="createCustomerCompanyCtrl.type"
                    id="typeSelection"
                    ng-required="true"
                    ng-disabled="createCustomerCompanyCtrl.isSupreme"
                >
                    <option ng-if="!createCustomerCompanyCtrl.isSupreme" value="" selected disabled translate>
                        CREATE_CUSTOMER.SELECT_COMPANY
                    </option>
                    <option ng-if="!createCustomerCompanyCtrl.isSupreme" value="Customer" translate>CREATE_CUSTOMER.CUSTOMER</option>
                    <option value="Dealer" translate>CREATE_CUSTOMER.DEALER</option>
                </select>
                <div class="select-arrow"></div>
            </div>
        </div>

        <div class="input-group">
            <label translate>CREATE_CUSTOMER.DEFAULT_DISCOUNT</label>
            <input
                class="mb-0"
                type="number"
                step="1"
                placeholder="{{'CREATE_CUSTOMER.ENTER_DISCOUNT' | translate}}"
                ng-model="createCustomerCompanyCtrl.defaultDiscount"
                ng-required="false"
            />
        </div>

        <div class="input-group" ng-show="createCustomerCompanyCtrl.isPriceListEnabled">
            <label translate>CREATE_CUSTOMER.PRICE_LIST</label>
            <div class="d-flex w-100 align-items-center">
                <div class="select-box">
                    <select ng-model="createCustomerCompanyCtrl.priceListId"
                        ng-required="createCustomerCompanyCtrl.isPriceListEnabled">
                        <option value="" disabled selected translate>CREATE_CUSTOMER.SELECT_PRICE_LIST</option>
                        <option value="none" translate>{{'EDIT_COMPANY.SELECT_NONE' | translate}}</option>
                        <option ng-repeat="priceList in createCustomerCompanyCtrl.priceLists" ng-value="priceList.id">{{
                            priceList.identifier }}</option>
                    </select>
                    <div class="select-arrow"></div>
                </div>
            </div>
        </div>

        <div class="input-group" ng-show="createCustomerCompanyCtrl.isSupreme">
            <label translate>CREATE_CUSTOMER.WAREHOUSE</label>
            <div class="d-flex w-100 align-items-center">
                <div class="select-box">
                    <small ng-if="!createCustomerCompanyCtrl.warehouseSelected" translate>CREATE_CUSTOMER.SELECT_WAREHOUSE</small>
                    <select
                        ng-model="createCustomerCompanyCtrl.warehouseSelected"
                        ng-options="warehouse.name for warehouse in createCustomerCompanyCtrl.warehouses"
                        ng-required="createCustomerCompanyCtrl.isSupreme"
                    ></select>
                    <div class="select-arrow"></div>
                </div>
            </div>
        </div>

        <div class="input-group" ng-if="createCustomerCompanyCtrl.isSupreme">
            <label translate>CREATE_CUSTOMER.VISIBILITY_ID</label>
            <input
                type="text"
                placeholder="{{'CREATE_CUSTOMER.ENTER_VISIBILITY_ID' | translate}}"
                ng-model="createCustomerCompanyCtrl.visCustomerCode"
                ng-required="true"
            />
        </div>

        <div class="modal-actions">
            <button type="button" class="btn small secondary" data-dismiss="modal" ng-click="createCustomerCompanyCtrl.cancel()" translate>
                GENERAL.CANCEL
            </button>

            <button
                type="submit"
                class="btn small primary"
                ng-disabled="!createCompanyForm.$valid || createCustomerCompanyCtrl.isDisabled"
                translate
            >
                CREATE_CUSTOMER.CREATE_COMPANY
            </button>
        </div>
    </form>
</div>
