.orders_container {
    border-radius: 10px;
}

.orders {
    border: 1px solid #ccc;
    border-collapse: collapse;
    margin: 0;
    padding: 0;
    width: 100%;
    table-layout: fixed;
}

.orders caption {
    font-size: 1.5em;
    margin: 0.5em 0 0.75em;
}

.orders_heading {
    background: #f2f6f9;
}

.orders_buttons {
    background: white;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}

.orders th,
.orders td {
    padding: 1em;
    word-break: break-word;
}

.orders th {
    font-size: 0.85em;
    letter-spacing: 0.1em;
    text-transform: uppercase;
}

.search-panel {
    display: inline-block;
    width: auto;
}

.heading_table {
    background: white;
    border-top: 1px solid #ccc;
    border-left: 1px solid #ccc;
    border-right: 1px solid #ccc;
}

.searchgroup {
    width: 340px;
    display: inline-block;
}

.orders_pageNumber {
    background: white;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}

.kits_pageNumber {
    background: white;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}

.orders-filter-panel {
    width: 100%;
    padding: 1.5em;
    background: white;

    filter-header {
        font-size: 0.8em;
        text-transform: uppercase;
        font-weight: 700;
    }
    .first-filter {
    }

    .filter-option {
        display: inline-block;
        margin-right: $spacing * 2;
        margin-top: $spacing;
    }
    .filter-buttons {
        margin-top: $spacing * 2;

        .btn:first-child {
            margin-right: $spacing;
        }
    }
}

.dropdown-toggle::after {
    display: inline-flex;
    align-items: center;
    content: "\f107";
    font-family: "FontAwesome";
    margin-left: initial;
    vertical-align: initial;
    border: none;
}

@media screen and (max-width: 800px) {
    .orders {
        border: 0;
    }

    .orders caption {
        font-size: 1.3em;
    }

    .orders thead {
        border: none;
        clip: rect(0 0 0 0);
        height: 1px;
        margin: -1px;
        overflow: hidden;
        padding: 0;
        position: absolute;
        width: 1px;
    }

    .orders tr {
        border-bottom: 3px solid #ddd;
        display: block;
        margin-bottom: 0.625em;
    }

    .orders td {
        border-bottom: 1px solid #ddd;
        display: block;
        font-size: 1em;
    }

    .orders td::before {
        /*
  * aria-label has no advantage, it won't be read inside a table
  content: attr(aria-label);
  */
        content: attr(data-label);
        font-weight: bold;
        text-transform: uppercase;
        padding-right: 10px;
    }

    .orders td:last-child {
        border-bottom: 0;
    }

    .products-filter {
        margin-left: auto;
    }

    .order-details-holder {
        width: 100%;
        float: left;
    }
}
