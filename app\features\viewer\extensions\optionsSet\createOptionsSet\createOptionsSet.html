<div class="sidebar-content no-padding" ng-show="createOptionsSetCtrl.isOpen">
    <form class="form">

        <div>

            <div class="well clearfix">
                <div class="selected-part-text" ng-hide="createOptionsSetCtrl.selectedPart.length > 1">
                    <h4 translate>CREATE_OPTION_SET.SELECTED_PART</h4>
                    <span ng-hide="createOptionsSetCtrl.selectedPart.length === 0">
                    {{createOptionsSetCtrl.selectedPart.partNumber}} &nbsp;<small><strong>{{createOptionsSetCtrl.selectedPart.partDescription}}</strong></small>
                </span>
                    <span ng-show="createOptionsSetCtrl.selectedPart.length === 0" translate>CREATE_OPTION_SET.NONE</span>
                </div>

                <div class="selected-part-error" ng-show="createOptionsSetCtrl.selectedPart.length > 1">
                    <h4 translate>CREATE_OPTION_SET.MULTI_PARTS</h4>
                    <p translate>CREATE_OPTION_SET.INDIVIDUAL_ONLY</p>
                </div>
            </div>
            <div class="warning-well" ng-show="createOptionsSetCtrl.hasOptionsSet">
                <p>{{"CREATE_OPTION_SET.THIS_PART" | translate}}: ({{createOptionsSetCtrl.optionsSetDescription}}).
                    {{"CREATE_OPTION_SET.CREATING" | translate}}</p>
            </div>

            <div class="input-group mb-0">
                <label translate>CREATE_OPTION_SET.OPTION_DESC</label>
                    <p translate>CREATE_OPTION_SET.ENTER_DESC</p>
                    <input type="text" placeholder="{{'CREATE_OPTION_SET.ENTER_DESCRIPTION' | translate}}" class="full-width-element"
                           ng-model="createOptionsSetCtrl.description"
                           required="required">
            </div>

        </div><!-- /side-menu-content -->


        <div class="option-set-scrollable side-forms no-padding">
            <table class="tableViewer table-bordered w-100 bg-white ml-0">
                <thead>
                <tr>
                    <th translate>CREATE_OPTION_SET.OP_NUM</th>
                    <th translate>CREATE_OPTION_SET.DESC</th>
                    <th></th>
                </tr>
                </thead>
                <tbody>
                <tr ng-repeat="option in createOptionsSetCtrl.optionsSet">
                    <td class="option-set-cell">
                        <input type="text" placeholder="{{'CREATE_OPTION_SET.PART_NUM' | translate }}" ng-model="option.partNumber" required="required">
                    </td>
                    <td class="option-set-cell">
                        <input type="text" placeholder="{{'CREATE_OPTION_SET.DESCRIPTION' | translate }}" ng-model="option.partDescription" required="required">
                    </td>
                    <td><a href="" class="fa fa-trash delete" ng-click="createOptionsSetCtrl.removeOption($index)"></a>
                    </td>
                </tr>

                <tr>
                    <td colspan="4">
                        <div class="add-another-part">
                            <button class="btn primary-outline pull-right" type="button"
                                    ng-click="createOptionsSetCtrl.addAnotherOption()">
                                <i class="fa fa-plus-circle"></i> &nbsp;{{"CREATE_OPTION_SET.ADD_ANOTHER" | translate}}
                            </button>
                        </div>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>


        <div class="side-menu-content">

            <div class="error-well"
                 ng-show="createOptionsSetCtrl.errors.noPartSelected || createOptionsSetCtrl.errors.noDescription || createOptionsSetCtrl.errors.notEnoughParts || createOptionsSetCtrl.alreadyHasOptions">
                <p ng-show="createOptionsSetCtrl.errors.noPartSelected" translate>
                    CREATE_OPTION_SET.SELECT_PART_ERROR
                </p>
                <p ng-show="createOptionsSetCtrl.errors.noDescription" translate>
                    CREATE_OPTION_SET.DESC_ERROR
                </p>
                <p ng-show="createOptionsSetCtrl.errors.notEnoughParts" translate>
                    CREATE_OPTION_SET.AT_LEAST_ERROR
                </p>
                <p ng-show="createOptionsSetCtrl.alreadyHasOptions" translate>
                    CREATE_OPTION_SET.ALREADY_CONTAINS_ERROR
                </p>
            </div>

            <div class="kit-actions pt-3">
                <button class="btn small secondary" type="button" ng-click="createOptionsSetCtrl.cancel()" translate>
                    GENERAL.CANCEL
                </button>
                <button class="btn small primary" ng-click="createOptionsSetCtrl.saveOptionsSet()" translate>
                    CREATE_OPTION_SET.SAVE
                </button>
            </div>

        </div><!-- /side-menu-content -->

    </form>


</div>