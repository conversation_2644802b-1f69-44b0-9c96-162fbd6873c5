<div class="modal-header overlay-modal">
    <button type="button" class="close" data-dismiss="modal" ng-click="kitModalCtrl.cancel()" aria-label="Close">
        <i class="fa fa-close" aria-hidden="true"></i>
    </button>
    <h2 class="modal-title" translate>BUY_KIT.TITLE</h2>
</div>
<div class="modal-body overlay-modal">

    <h4 class="lightweight">{{"BUY_KIT.THE_PART" | translate}} <strong>{{kitModalCtrl.partNumber}}</strong> {{"BUY_KIT.AVAILABLE_IN" | translate}}:</h4>

    <div ng-repeat="kit in kitModalCtrl.kitList">
        <uib-accordion>
            <uib-accordion-group is-open="kit.status" class="kit-panel">
                <uib-accordion-heading>
                    <div class="kit-container card">
                        <div class="card-header py-0 d-flex justify-content-between align-items-center">
                            <h4 class="mb-0">{{kit.title}} <span class="badge badge-pill badge-primary">{{kit.totalPartsQuantity}} {{"BUY_KIT.PARTS" | translate}}</span></h4>
                            <a href="javascript:void(0);" ng-click="kit.status = !kit.status" class="p-3 toggle-icon" ng-disabled="kit.clicked">
                                <i ng-click="kit.status = !kit.status" ng-class="{'fa fa-angle-up': kit.status, 'fa fa-angle-down': !kit.status}"></i>
                            </a>
                        </div>
                        <div class="p-3">
                            <div class="row">
                                <div class="col-md-9">
                                    <span class="flex">
                                    <h2 ng-if="kit.masterPartNumber" class="text-dark mb-0 mr-2">
                                        {{kit.masterPartNumber}}</h2>
                                        <span ng-if="kitModalCtrl.isStockWarehousesEnabled" ng-class="{'text-success': kit.stock >= 3, 'text-warning': kit.stock < 3 && kit.stock > 0, 'text-danger': kit.stock === null || kit.stock < 1}" 
                                            >{{'BUY_KIT.STOCK' | translate}} {{kit.stock || 0}}
                                        </span>
                                        <span ng-if="!kitModalCtrl.isStockWarehousesEnabled" ng-class="{'text-success': kit.stock >= 3, 'text-warning': kit.stock < 3 && kit.stock > 0, 'text-danger': kit.stock === null || kit.stock < 1}">
                                            <span title="{{'ORDER.IN_STOCK' | translate}}" tooltip-trigger="outsideClick"
                                                uib-tooltip="{{'ORDER.IN_STOCK' | translate}}" class="success-alert" ng-if="kit.stock >= 3">
                                                <i class="fas fa-layer-group text-success pointer"></i>
                                            </span>
                                            <span title="{{'ORDER.LOW_STOCK' | translate}}" tooltip-trigger="outsideClick"
                                                uib-tooltip="{{'ORDER.LOW_STOCK' | translate}}" class="warning-alert"
                                                ng-if="kit.stock < 3 && kit.stock > 0">
                                                <i class="fas fa-layer-group text-warning pointer"></i>
                                            </span>
                                            <span title="{{'ORDER.STOCK_SUBJECT' | translate}}" tooltip-trigger="outsideClick"
                                                uib-tooltip="{{'ORDER.STOCK_SUBJECT' | translate}}" class="warning-alert"
                                                ng-if="kit.stock === null || kit.stock < 1">
                                                <i class="fas fa-layer-group text-danger pointer"></i>
                                            </span>
                                        </span>
                                    </span>
                                    <p class="text-dark m-0">{{kit.masterPartDescription }}</p>
                                </div>
                                <div class="col-md-3 text-right">
                                    <button class="btn btn-primary" ng-click="kitModalCtrl.addKitToBasket(kit)" ng-if="kitModalCtrl.hasOrderRole">
                                        <span ng-hide="kit.clicked" translate>BUY_KIT.ADD_BASKET</span>
                                        <span ng-show="kit.clicked"><i class="fa fa-check"></i></span>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="kit-contents" ng-show="kit.status">
                            <div class="table-responsive p-2">
                                <table class="table table-striped-columns table-bordered">
                                    <thead>
                                        <tr>
                                            <th>{{'BUY_KIT.PART_NUMBER' | translate}}</th>
                                            <th>{{'BUY_KIT.DESCRIPTION' | translate}}</th>
                                            <th>{{'BUY_KIT.QUANTITY' | translate}}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr ng-repeat="part in kit.parts">
                                            <td>{{part.partNumber}}</td>
                                            <td>{{part.partDescription}}</td>
                                            <td>{{part.quantity}}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </uib-accordion-heading>
            </uib-accordion-group>
        </uib-accordion>
    </div>

    <div class="modal-actions">
        <button class="btn small secondary" type="button" ng-click="kitModalCtrl.cancel()" translate>GENERAL.CANCEL
        </button>
    </div>
</div>