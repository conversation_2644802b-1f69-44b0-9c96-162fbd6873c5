(function () {
    'use strict';

    angular
        .module('app.customer')
        .controller('EditCustomerCompanyController', EditCustomerCompanyController);

    EditCustomerCompanyController.$inject = ['createUserService', '$uibModalInstance', 'companyDetails', 'userService', 'priceListService', 'warehouses'];

    function EditCustomerCompanyController(createUserService, $uibModalInstance, companyDetails, userService, priceListService, warehouses) {
        var vm = this;
        
        vm.cancel = $uibModalInstance.dismiss;
        vm.newCompanyName = companyDetails.name;
        vm.newDefaultDiscount = companyDetails.defaultDiscount;
        vm.isDealer = companyDetails.type === 'DEALER';
        vm.visCustomerCode = companyDetails.visCustomerCode;
        vm.warehouses = warehouses;
        vm.warehouseSelected = findSelectedWarehouse(companyDetails.warehouseId);
        vm.isPriceListEnabled = userService.getPriceListsEnabled();
        vm.isSupreme = userService.isSupreme();

        vm.editCompany = editCompany;
        vm.priceListChanged = priceListChanged;

        initialize();

        function initialize(){
            if(vm.isPriceListEnabled){
                priceListService.getPriceLists()
                    .then(getPriceListsSuccess, getPriceListFailed)
            }
        }

        function getPriceListsSuccess(resp){
            vm.priceLists = resp.data;
            var index = _.findIndex(vm.priceLists, {id: companyDetails.priceListIdentifierId});
            vm.priceList = vm.priceLists[index];
        }

        function getPriceListFailed(response){
            vm.isDisabled = false;
            vm.internalFailureMessage = response.data.message;
        }

        function editCompany() {
            vm.isDisabled = true;
            var pricelistId = vm.priceList ? vm.priceList.id : null;
            const warehouseId = vm.warehouseSelected ? vm.warehouseSelected.id : companyDetails.warehouseId;
            createUserService.editManufacturerSubEntityCustomer(vm.newCompanyName, companyDetails.subEntityId, vm.newDefaultDiscount, pricelistId, vm.visCustomerCode, warehouseId)
                .then(createCustomerSuccess, createCustomerFailed);
        }

        function priceListChanged(newPriceListId) {
            vm.newPriceListId = newPriceListId;
        }

        function createCustomerSuccess() {
            $uibModalInstance.close();
        }

        function createCustomerFailed(response) {
            vm.companyFailure = true;
            vm.isDisabled = false;
            vm.internalFailureMessage = response.data.message;
        }

        function findSelectedWarehouse(warehouseId) {
            if(!vm.warehouses || !warehouseId) return null;
            return vm.warehouses.find(item => item.id === warehouseId);
        }

    }
})();