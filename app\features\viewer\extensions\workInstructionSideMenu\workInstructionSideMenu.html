<div class="side-menu" ng-hide="workInstructionSideMenuCtrl.isMinimized">

    <button class="btn-side-menu" ng-click="workInstructionSideMenuCtrl.minimizeSideMenu()">
         {{"SIDE_MENU.CLOSE_MENU" | translate}} &nbsp
        <i class="pull-right fa fa-close"></i>
    </button>

    <uib-accordion class="side-menu-accordion-2-panel">
        <uib-accordion-group is-open="workInstructionSideMenuCtrl.status.isModelBrowserOpen">
            <uib-accordion-heading>
                <i class="fa fa-fw fa-sitemap "></i>&nbsp; {{"SIDE_MENU.MODEL_BROWSER" | translate}}
                <span ng-show="workInstructionSideMenuCtrl.status.isModelBrowserOpen" class="pull-right"><i class="fa fa-angle-down"></i>&nbsp;</span>
                <span ng-hide="workInstructionSideMenuCtrl.status.isModelBrowserOpen" class="pull-right"><i class="fa fa-angle-right"></i>&nbsp;</span>
            </uib-accordion-heading>
            <model-browser></model-browser>
        </uib-accordion-group>


        <uib-accordion-group is-open="workInstructionSideMenuCtrl.status.isViewerSettingsOpen">
            <uib-accordion-heading>
                <i class="fas fa-sliders"></i>&nbsp; {{"SIDE_MENU.SETTINGS" | translate}}
                <span ng-show="workInstructionSideMenuCtrl.status.isViewerSettingsOpen" class="pull-right"><i class="fa fa-angle-down"></i>&nbsp;</span>
                <span ng-hide="workInstructionSideMenuCtrl.status.isViewerSettingsOpen" class="pull-right"><i class="fa fa-angle-right"></i>&nbsp;</span>
            </uib-accordion-heading>
            <viewer-settings></viewer-settings>
        </uib-accordion-group>

    </uib-accordion>

</div>

<div class="expand-model-browser-button" ng-show="workInstructionSideMenuCtrl.isMinimized">
    <button class="btn-model" ng-click="workInstructionSideMenuCtrl.maximizeSideMenu()">
        <i class="fa fa-bars "></i> {{"SIDE_MENU.OPEN_MENU" | translate}}
    </button>
</div>