<div class="dpGreenOrder" id="order-page-identifier">

    <section class="flex justify-content-between">

        <div class="">
            <h1 class="mb-0">{{dpViewOrderCtrl.STAGE}} {{"ORDER.DETAILS" | translate}}:
                #{{dpViewOrderCtrl.data.customOrderDisplay}} &nbsp;
                <a href="" ng-show="dpViewOrderCtrl.showSplitOrderBtn"
                   ng-click="dpViewOrderCtrl.editCustomOrderNumber()"><i
                        class="fa fa-fw fa-pencil"></i></a>
                <span
                        class="badge-pill badge-lg primary">{{dpViewOrderCtrl.orderStatusPill}}</span>
            </h1>
        </div>

        <div class="d-flex flex-wrap mt-4 cadGap ml-auto" ng-hide="dpViewOrderCtrl.enquiriesOnly" style="grid-gap: 0.3em;">

		    <button ng-if="dpViewOrderCtrl.data.commentThread === undefined"
				ng-hide="dpViewOrderCtrl.data.orderStatus == 'CLOSED' || dpViewOrderCtrl.data.orderStatus == 'CANCELLED'
                        || dpViewOrderCtrl.data.orderStatus == 'SHIPPED' || dpViewOrderCtrl.data.orderStatus == 'EXTERNAL' || dpViewOrderCtrl.enquiriesOnly"
				class="btn primary-outline"
				ng-click="dpViewOrderCtrl.openComment(dpViewOrderCtrl.data.commentThread.id, null)"
				translate>
				ORDER.ADD_COMMENT
				<i class="fa fa-comments ng-scope" ng-if="dpViewOrderCtrl.data.unreadComment"></i>
		    </button>

		    <button ng-if="dpViewOrderCtrl.data.commentThread !== undefined"
				ng-hide="dpViewOrderCtrl.data.unreadComment || dpViewOrderCtrl.data.orderStatus == 'CANCELLED'
                     || dpViewOrderCtrl.data.orderStatus == 'EXTERNAL' || dpViewOrderCtrl.data.orderStatus == 'SHIPPED'"
				class="btn primary-outline"
				ng-click="dpViewOrderCtrl.openComment(dpViewOrderCtrl.data.commentThread.id, null)"
				translate>
				ORDER.VIEW_COMMENTS
				<i class="fa fa-comments ng-scope" ng-if="dpViewOrderCtrl.data.unreadComment"></i>
		    </button>

		    <button ng-class="dpViewOrderCtrl.data.unreadComment ? 'unreadcomment' : '' "
				ng-if="dpViewOrderCtrl.data.commentThread !== undefined"
				ng-hide="!dpViewOrderCtrl.data.unreadComment || dpViewOrderCtrl.data.orderStatus == 'CANCELLED'
                     || dpViewOrderCtrl.data.orderStatus == 'EXTERNAL' || dpViewOrderCtrl.data.orderStatus == 'SHIPPED'"
				class="btn primary-outline customersGreen"
				ng-click="dpViewOrderCtrl.openComment(dpViewOrderCtrl.data.commentThread.id, null)">
                        <span> {{"ORDERS.NEW_COMMENTS" | translate}} <i class="ng-scope blob_order fas fa-bell"
												ng-if="dpViewOrderCtrl.data.unreadComment"></i></span>
		    </button>

            <button class="m-2 m-md-0 btn primary-outline"
                    ng-hide="!dpViewOrderCtrl.showPartSearch || dpViewOrderCtrl.hidePartSearch"
                    ng-click="dpViewOrderCtrl.openPartSearchModal()">
                <i class="fa fa-search"></i>
                {{"ORDER.PART_SEARCH" | translate}}
            </button>

            <button class="m-2 m-md-0 btn btn-outline-danger" ng-if="dpViewOrderCtrl.isDealerPlus && dpViewOrderCtrl.archived !== true"
                    ng-click="dpViewOrderCtrl.cancelOrder(dpViewOrderCtrl.data.orderId, dpViewOrderCtrl.data.customOrderDisplay)">
                {{"GENERAL.CANCEL" | translate}} {{dpViewOrderCtrl.STAGE}}
            </button>

            <button class="m-2 m-md-0 btn btn-primary"
                    ng-show="dpViewOrderCtrl.showNextStep && dpViewOrderCtrl.isNextStepDisabled"
                    ng-disabled="dpViewOrderCtrl.isNextStepDisabled">
                <span class="spinner-border text-light" role="status" aria-hidden="true"></span>
                {{"ORDER.GENERATING" | translate}}
            </button>

            <button class="m-2 m-md-0 btn btn-outline-dark"
                    ng-click="dpViewOrderCtrl.splitOrder(dpViewOrderCtrl.data.orderItems, dpViewOrderCtrl.data.additionalParts, dpViewOrderCtrl.data.orderId, dpViewOrderCtrl.data.customOrderDisplay)"
                    ng-show="dpViewOrderCtrl.showSplitOrderBtn" ng-disabled="(dpViewOrderCtrl.data.orderItems.length === 1 && dpViewOrderCtrl.hasQuantityMissing) && (dpViewOrderCtrl.data.additionalParts.length !== 1 && !dpViewOrderCtrl.hasManuallyAddedQuantityMissing) || (dpViewOrderCtrl.data.orderItems.length !== 1 && !dpViewOrderCtrl.hasQuantityMissing) && (dpViewOrderCtrl.data.additionalParts.length === 1 && dpViewOrderCtrl.hasManuallyAddedQuantityMissing)" translate>
                ORDER.SPLIT_ENQUIRY
            </button>

            <button class="m-2 m-md-0 btn secondary" ng-click="dpViewOrderCtrl.altStep()"
                    ng-show="dpViewOrderCtrl.showAltStepBtn && dpViewOrderCtrl.isOrderEdited">
                {{dpViewOrderCtrl.altStepText}}
            </button>

            <button class="m-2 m-md-0 btn btn-outline-dark" ng-show="dpViewOrderCtrl.data.invoiceUrl.length === 1"
                    ng-click="dpViewOrderCtrl.viewInvoice()" translate>
                ORDER.VIEW_INVOICE
            </button>

            <div class="customDropdown" ng-show="dpViewOrderCtrl.data.invoiceUrl.length > 1">
                <button class="m-2 m-md-0 btn btn-outline-dark dropdown-toggle" data-toggle="dropdown"
                        data-hover="dropdown">{{'ORDER.VIEW_INVOICES' | translate}} <span class="caret"></span></button>
                <ul class="dropdown-menu">
                    <li title="{{'ORDER.DOWNLOAD' | translate}}" ng-repeat="invoice in dpViewOrderCtrl.data.invoiceUrl">
                        <a href="" class="dark-secondary" ng-click="dpViewOrderCtrl.viewInvoice($index)">
                            {{'ORDER.DOWNLOAD' | translate}} {{'ORDER.INVOICE' | translate}} {{$index + 1 }}
                        </a>
                    </li>
                </ul>
            </div>

            <button class="m-2 m-md-0 btn primary" ng-click="dpViewOrderCtrl.nextStep()"
                    ng-show="dpViewOrderCtrl.showNextStep && !dpViewOrderCtrl.isNextStepDisabled"
                    ng-disabled="dpViewOrderCtrl.isNextStepDisabled">
                {{dpViewOrderCtrl.nextStepText}}
            </button>

            <span ng-show="!dpViewOrderCtrl.showNextStep && !dpViewOrderCtrl.showAltStepBtn"
                  style="line-height: 40px; ">&nbsp;</span>
        </div>

        <div class="" ng-show="dpViewOrderCtrl.showExternallyProcessButton">
            <button class="btn primary"
                    ng-click="dpViewOrderCtrl.externallyProcessOrder(dpViewOrderCtrl.data)">
                {{"ORDER.EXTERNAL_ACCEPT" | translate}} {{dpViewOrderCtrl.STAGE}}
            </button>
        </div>

    </section>

    <hr class="underline_order">

    <section>

        <div class="">
            <p class="mb-0" ng-if="dpViewOrderCtrl.parentOrderId"><strong>{{"ORDER.ORDER_BEEN_CREATED" | translate}}
                <a href=""
                   ng-click="dpViewOrderCtrl.goToSplitOrder(dpViewOrderCtrl.parentOrderId)">#{{dpViewOrderCtrl.parentCustomOrderDisplay}}</a>
            </strong></p>
            <p class="mb-0" ng-if="dpViewOrderCtrl.splitOrders.length > 0">
                <strong>{{"ORDER.ORDER_BEEN_SPLIT" | translate}}:
                    <a ng-repeat="splitOrder in dpViewOrderCtrl.splitOrders" href=""
                       ng-click="dpViewOrderCtrl.goToSplitOrder(splitOrder.orderId)">#{{splitOrder.displayId}}<span
                            ng-if="!$last">, </span></a>
                </strong></p>

            <p class="mb-0" ng-if="dpViewOrderCtrl.data.associatedDPOrders.length > 0"
               ng-repeat="createdOrder in dpViewOrderCtrl.data.associatedDPOrders">
                <strong ng-show="createdOrder.containsAllItems">{{"ORDER.FORWARDED_TO_ALL" | translate}} </strong>
                <strong ng-hide="createdOrder.containsAllItems">{{"ORDER.FORWARDED_TO_SOME" | translate}} </strong>
                <strong>
                    {{createdOrder.manufacturerName}} {{"ORDER.ON" | translate}}
                    {{createdOrder.createdDate | date: 'dd MMM yyyy'}} {{"ORDER.ON_ENQUIRY" | translate}}
                    <a href=""
                       ng-click="dpViewOrderCtrl.goToCreatedOrder(createdOrder.associatedDPOrderIds)">
                        #{{createdOrder.associatedDPCustomDisplayNumbers}}
                        <span ng-if="!$last">, </span>
                    </a>
                </strong>
            </p>
        </div>

    </section>

    <!---->

    <section class="my-3">

        <div class="row">

            <div class="col-12 col-md-12 col-lg-6 customDivContainer">

                <div class="customDivStyling">
                    <h2 class=""><span translate>ORDER.ORDER_DETAILS</span> - <strong>{{dpViewOrderCtrl.data.manufacturerSubEntityName}}</strong></h2>

                    <div class="row" ng-hide="dpViewOrderCtrl.isCustDetailEdit">

                        <div class="col-md-6">

					  <label><strong translate>ORDER.DELIVERY_ADDRESS</strong></label>
                            <p class="py-2">{{dpViewOrderCtrl.shippingAddress}}</p>

                        </div>

                        <div class="col-md-6">

					  <label><strong translate>ORDER.DELIVERY_DETAILS</strong></label>
                            <p class="mb-2">
                                <strong><i class="fa fa-truck"></i></strong>&nbsp; {{dpViewOrderCtrl.data.deliveryName}}
                            </p>
                            <p>
                                <strong><i class="fa fa-phone"></i></strong>&nbsp;&nbsp;
                                {{dpViewOrderCtrl.data.deliveryNumber}}
                            </p>

                        </div>
				    <hr class="col-11 hr-text">

                        <div class="col-md-6">

					  <label><strong translate>ORDER.BILLING_ADDRESS</strong></label>

                            <p>{{dpViewOrderCtrl.billingAddress}}</p>

                        </div>

                        <div class="col-md-6">

					  <label><strong translate>ORDER.CONTACT_DETAILS</strong></label>
					  <p class="mb-2">
						    <strong><i class="fa fa-user"></i></strong>&nbsp;&nbsp; {{dpViewOrderCtrl.data.contactName}}
					  </p>
					  <p>
						    <strong><i class="fa fa-phone"></i></strong>&nbsp;&nbsp;
														{{dpViewOrderCtrl.data.contactNumber}}
					  </p>

				</div>

					  <hr class="col-11 hr-text">

					  <div class="col-md-6" ng-if="dpViewOrderCtrl.data.requestedDeliveryDate !== undefined">
						    <label><strong translate>ORDER.REQUESTED_DELIVERY_DATE</strong></label>
						    <p>{{dpViewOrderCtrl.data.requestedDeliveryDate | date: 'dd MMM yyyy'}}</p>
					  </div>

					  <div class="col-md-6" ng-if="dpViewOrderCtrl.showEstimatedDelivery && dpViewOrderCtrl.data.estimatedDeliveryDate !== null">

						    <label><strong translate>ORDER.ESTIMATED_DELIVERY_DATE</strong></label>

						    <div ng-show="dpViewOrderCtrl.isEstimatedDeliveryDateEditable">
								<input class="dark-text" type="text" id="datepicker"
									 ng-model="dpViewOrderCtrl.data.estimatedDeliveryDate"/>
								<button class="btn success" ng-click="dpViewOrderCtrl.saveEstimatedDeliveryDate()">
									  <i
										    class="fa fa-floppy-o"></i> {{"ORDER.SAVE_DELIVERY_DATE" | translate}}
								</button>
						    </div>

						    <div ng-hide="dpViewOrderCtrl.isEstimatedDeliveryDateEditable">
								<div ng-show="dpViewOrderCtrl.data.estimatedDeliveryDate"><p>
									  {{dpViewOrderCtrl.data.estimatedDeliveryDate | date: 'dd MMM yyyy'}}</p></div>
								<div ng-hide="dpViewOrderCtrl.data.estimatedDeliveryDate"><p translate>
									  ORDER.TO_BE_CONFIRMED</p></div>
								<button ng-show="dpViewOrderCtrl.isManufacturer && dpViewOrderCtrl.archived != true"
									  class="btn primary" ng-click="dpViewOrderCtrl.editEstimatedDeliveryDate()">
									  <i class="fa fa-pencil"></i> {{"ORDER.EDIT_DELIVERY_DATE" | translate}}
								</button>
						    </div>

					  </div>

				</div>

                    <div class="button_row flex justify-content-end">

                        <button class="btn primary " ng-click="dpViewOrderCtrl.editCustomerDetails()"
                                ng-show="dpViewOrderCtrl.showEditDetailsButton" ng-hide="dpViewOrderCtrl.enquiriesOnly"
                                translate>ORDER.EDIT_DETAILS
                        </button>


                    </div>

                </div>

            </div>

            <div class="col-12 col-md-6 col-lg-3 customDivContainer">

                <div class="customDivStyling">

				<h2 class=""><span translate>ORDER.ORDER_REFERENCES</span></h2>

                    <div ng-if="dpViewOrderCtrl.showPurchaseOrder">
				    <label>
						<strong translate>ORDER.PURCHASE_ORDER_NUMBER</strong>
				    </label>
                        <input class="dark-text " ng-model="dpViewOrderCtrl.data.purchaseOrder"
                               ng-show="dpViewOrderCtrl.isPurchaseOrderEditable"
                               placeholder="{{'ORDER.ENTER_PO_NUM' | translate}}">
                        <p class="order-field" ng-hide="dpViewOrderCtrl.isPurchaseOrderEditable">
                            {{dpViewOrderCtrl.data.purchaseOrder || "N/A"}}</p>
                    </div>

                    <div ng-if="dpViewOrderCtrl.showRequiredSerialNumber">
                        <div ng-hide="dpViewOrderCtrl.data.stockOrder">
					  <label><strong translate>ORDER.SERIAL_NUMBER</strong></label>
                            <p class="order-field">{{dpViewOrderCtrl.data.serialNumber || "N/A"}}</p>
                        </div>
                        <div class="d-flex" ng-show="dpViewOrderCtrl.data.stockOrder">
					  <label><strong class="pr-2" translate>ORDER.STOCK_ORDER</strong></label>
                            <input type="checkbox" id="stockOrder" class="checkbox" ng-disabled="true"
                                   ng-model="dpViewOrderCtrl.data.stockOrder"/>
                        </div>
                    </div>

                    <h4><strong translate>ORDER.REQUESTED_DELIVERY_DATE</strong></h4>
                    <p class="order-field">{{dpViewOrderCtrl.data.requestedDeliveryDate | date: 'dd MMM yyyy'}}</p>


                    <div ng-if="dpViewOrderCtrl.showEstimatedDelivery && dpViewOrderCtrl.data.estimatedDeliveryDate !== null">

                        <strong translate>ORDER.ESTIMATED_DELIVERY_DATE</strong>

                        <div ng-show="dpViewOrderCtrl.isEstimatedDeliveryDateEditable">
                            <input class="dark-text" type="text" id="datepicker"
                                   ng-model="dpViewOrderCtrl.data.estimatedDeliveryDate"/>
                            <button class="btn success mb-3 mt-2"
                                    ng-click="dpViewOrderCtrl.saveEstimatedDeliveryDate()"><i
                                    class="fa fa-floppy-o"></i> {{"ORDER.SAVE_DELIVERY_DATE" | translate}}
                            </button>
                        </div>

                        <div ng-hide="dpViewOrderCtrl.isEstimatedDeliveryDateEditable">
                            <div ng-show="dpViewOrderCtrl.data.estimatedDeliveryDate"><p>
                                {{dpViewOrderCtrl.data.estimatedDeliveryDate | date: 'dd MMM yyyy'}}</p></div>
                            <div ng-hide="dpViewOrderCtrl.data.estimatedDeliveryDate"><p translate>
                                ORDER.TO_BE_CONFIRMED</p></div>
                            <button ng-show="dpViewOrderCtrl.archived != true"
                                    class="btn primary mt-2" ng-click="dpViewOrderCtrl.editEstimatedDeliveryDate()">
                                <i class="fa fa-pencil"></i> {{"ORDER.EDIT_DELIVERY_DATE" | translate}}
                            </button>
                            </p>
                        </div>
                    </div>

                </div>
            </div>

            <div class="col-12 col-md-6 col-lg-3 customDivContainer">

                <div class="customDivStyling">
                    <h2 class=""><span>{{dpViewOrderCtrl.STAGE}} {{'ORDER.TIMELINE' | translate}}</span></h2>

				<p class="order-field" ng-repeat="step in dpViewOrderCtrl.data.orderHistory">{{step.date | date : 'dd MMM yyyy HH:mm'}} ({{step.orderStatus }} <span ng-show="step.firstName.length > 0"> {{"ORDER.BY" | translate}} {{step.firstName}} {{step.lastName}})</span></p>

                    <button class="btn primary-outline" ng-click="dpViewOrderCtrl.viewPartiallyShipped()" ng-show="dpViewOrderCtrl.isPartiallyShipped" translate>ORDER.VIEW_PARTIALLY_SHIPPED_INFO</button>
                </div>
            </div>

            <div class="col-12 col-md-6 col-lg-9 customDivContainer" ng-show="dpViewOrderCtrl.displayNotes">

                <div class="customDivStyling">

                    <h2 class=""><span translate>ORDER.ENQUIRY_NOTES</span></h2>
                    <pre ng-hide="dpViewOrderCtrl.isNotesEditable">{{dpViewOrderCtrl.data.notes}}</pre>

                    <div ng-show="dpViewOrderCtrl.isNotesEditable" class="customDivContainer flex-column">
                        <div class="form-group flex-grow-1 customDivContainer flex-column full-width-comment">
                            <textarea class="form-control flex-grow-1" ng-model="dpViewOrderCtrl.data.notes"
                                      ng-change="dpViewOrderCtrl.orderEdited()" rows="8"
                                      placeholder="{{'ORDER.PLEASE_ENTER_NOTES_HERE' | translate}}"></textarea>
                        </div>
                    </div>

				<div ng-show="" class="customDivContainer flex-column">
					  <div class="form-group flex-grow-1 customDivContainer flex-column full-width-comment">
                            <textarea class="form-control flex-grow-1" ng-model="dpViewOrderCtrl.data.notes"
						  ng-change="dpViewOrderCtrl.orderEdited()" rows="8"
						  placeholder="{{'ORDER.PLEASE_ENTER_NOTES_HERE' | translate}}"></textarea>
					  </div>
				</div>

                </div>

            </div>

            <div class="col-12 col-md-6 col-lg-3 customDivContainer pricingSummary"
                 ng-class="!dpViewOrderCtrl.displayNotes ? 'priceSummary' : ''">
                <div class="customDivStyling">
                    <div class="card-body p-1">
                        <h2 class="card-title pl-2 pb-2" translate>ORDER.PRICING_SUMMARY</h2>
                        <table class="table">
                            <tbody>
                            <tr>
                                <th scope="row" data-label="{{'ORDER.ITEMS_TOTAL' | translate}}"><b translate>ORDER.ITEMS_TOTAL</b>
                                </th>
                                <td>
                                    {{dpViewOrderCtrl.combinedTotalPrice | currency:dpViewOrderCtrl.selectedCurrency.symbol:2}}
                                </td>
                            </tr>
                            <tr ng-show="dpViewOrderCtrl.isDiscountVisible">
                                <th scope="row" data-label="{{'ORDER.DISCOUNT_PERCENTAGE' | translate}}"><b>
                                        {{'ORDER.DISCOUNT_PERCENTAGE' | translate}} <span ng-hide="dpViewOrderCtrl.isDiscountEditable">%</span></b>
                                </th>
                                <td class="" ng-hide="dpViewOrderCtrl.isDiscountEditable">
                                    {{dpViewOrderCtrl.data.percentageDiscount}}
                                </td>

                                <td ng-show="dpViewOrderCtrl.isDiscountEditable">

                                    <div class="inputWithIconWrap">
                                        <span class="input-icon"><span>%&nbsp;</span></span>
                                        <input onclick="this.select()"
                                               ng-model="dpViewOrderCtrl.data.percentageDiscount"
                                               ng-change="dpViewOrderCtrl.updateItemTotals(true)" type="number"
                                               class="input-with-icon">
                                    </div>

                                </td>


                            <tr>
                                <th scope="row" data-label="{{'ORDER.SHIPPING' | translate}}"><b translate>ORDER.SHIPPING</b>
                                </th>
                                <td class="" ng-hide="dpViewOrderCtrl.isShippingEditable">
                                    {{dpViewOrderCtrl.data.shippingPrice
                                | currency:dpViewOrderCtrl.selectedCurrency.symbol:2}}
                                </td>
                                <td ng-show="dpViewOrderCtrl.isShippingEditable">

                                    <div class="inputWithIconWrap">
                                        <span class="input-icon">{{dpViewOrderCtrl.selectedCurrency.symbol}}&nbsp;</span></span>
                                        <input onclick="this.select()" ng-model="dpViewOrderCtrl.data.shippingPrice"
                                               ng-change="dpViewOrderCtrl.updateItemTotals(true)" type="number"
                                               class="input-with-icon">
                                    </div>

                                </td>
                            </tr>
                            <tr>
                                <th data-label="{{'ORDER.TOTAL' | translate}}"><b translate>ORDER.TOTAL</b></th>
                                <td>{{dpViewOrderCtrl.total | currency:dpViewOrderCtrl.selectedCurrency.symbol:2}}</td>
                            </tr>

                            </tbody>
                        </table>
                    </div>
                    <div class="card-footer text-center">
                        <div ng-hide="dpViewOrderCtrl.hidePrice">
                            <span ng-hide="dpViewOrderCtrl.showCurrencySelection">{{"ORDER.PRICES_SHOWN_IN" | translate}} <strong>{{dpViewOrderCtrl.selectedCurrency.display}}</strong></span>
                            <span ng-show="dpViewOrderCtrl.showCurrencySelection">{{"ORDER.PRICES_SHOWN_IN" | translate}}
        <select class="currency-selector"
                ng-options="currency as currency.display for currency in dpViewOrderCtrl.currencies track by currency.code"
                ng-model="dpViewOrderCtrl.selectedCurrency" ng-change="dpViewOrderCtrl.orderEdited()"></select>
    </span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-12 orderManagement">

                <div class="flex customDivStyling">

                    <div class="col-12 col-md-auto">
                        <h2 class=""><span translate>ORDER.ORDER_MANAGEMENT</span></h2>
                        <p class="mb-0" translate>ORDER.ORDER_MANAGEMENT_DESC</p>
                    </div>

                    <div class="mobileBtnFullWidth col-12 col-md-auto ml-auto mt-4 mt-lg-0">
                        <button class="btn btn-primary primary-outline"
                                ng-click="dpViewOrderCtrl.addToBasket(dpViewOrderCtrl.data.orderItems, dpViewOrderCtrl.data.additionalParts, dpViewOrderCtrl.data.orderId, dpViewOrderCtrl.data.customOrderDisplay)">
                            {{'ORDER.ADD_TO_BASKET' | translate}}
                            <i ng-show="dpViewOrderCtrl.isBasketClicked" class="fas fa-check"></i>
                        </button>
                        <button class="btn btn-primary" ng-click="dpViewOrderCtrl.createEnquiry()" translate>
                            ORDER.CREATE_ENQUIRY
                        </button>
                    </div>

                </div>

            </div>

            <div class="col customDivContainer order_partsList identifiedParts">

                <div class="customDivStyling">

                    <h2 class="">
                        <span translate>ORDER.PARTS</span>
                    </h2>
				<p translate>ORDER.EX_WORKS_DISCLAIMER</p>

                    <table class="table table-bordered">
                        <thead>
                        <tr>
                            <th translate>ORDER.PART_DETAILS</th>
                            <th translate>ORDER.QTY</th>
                            <th ng-hide="dpViewOrderCtrl.hidePrice" translate>ORDER.ITEM_PRICE</th>
                            <th ng-show="dpViewOrderCtrl.hasVisibleDiscountedPrices()" translate>ORDER.DISCOUNTED_PRICE</th>
                            <th ng-hide="dpViewOrderCtrl.hidePrice" translate>ORDER.TOTAL_PRICE</th>
                            <th class="width-5" ng-show="dpViewOrderCtrl.showPartialShipped" translate>
                                ORDER.QTY_SHIPPED
                            </th>
                            <th ng-if="dpViewOrderCtrl.isPreviewStockLevelEnabled && !dpViewOrderCtrl.isCustomerOrder()" translate>
                                ORDER.STOCK
                            </th>
                            <th translate>
                                GENERAL.ACTIONS
                            </th>
                        </tr>

                        </thead>
                        <tbody>
                        <tr ng-repeat="item in dpViewOrderCtrl.data.orderItems"
                            ng-class="item.quantity>0 ? '' : 'strike-through'">
                            <td data-label="{{'ORDER.PART_DETAILS' | translate}}">
                                <p>
                                        <span class="font-weight-bold" ng-if="!item.masterPartId">{{item.partNumber}}</span>

                                        <a ng-if="item.masterPartId"
                                           ng-click="dpViewOrderCtrl.goToMasterPart(item.masterPartId)"
                                           href="">{{item.partNumber}}</a>

                                    </strong> <span ng-if="dpViewOrderCtrl.isPartInformationAvailable(item).showPartDescription">-</span> {{item.partDescription}}
                                </p>
                                <p ng-if="dpViewOrderCtrl.isPartInformationAvailable(item).showMachineName || dpViewOrderCtrl.isPartInformationAvailable(item).showModelName">From {{item.machineName}} <span ng-if="dpViewOrderCtrl.isPartInformationAvailable(item).showModelName">-</span> {{item.modelName}}</p>
                            </td>

                            <td data-label="{{'ORDER.QTY' | translate}}" ng-show="dpViewOrderCtrl.isQuantityEditable">
                        <span ng-show="item.quantity > 0">
                            <input onclick="this.select()" class="priceInput inputButtonWidth" type="number"
                                   ng-model="item.quantity"
                                   ng-change="dpViewOrderCtrl.updateItemTotals(true)">
                        </span>
                                <span ng-hide="item.quantity > 0">{{item.quantity}}</span>
                            </td>

                            <td data-label="{{'ORDER.QTY' | translate}}"
                                ng-hide="dpViewOrderCtrl.isQuantityEditable && !dpViewOrderCtrl.hidePrice">
                                {{item.quantity}}
                            </td>
                            <td class="disableWordBreak" data-label="{{'ORDER.ITEM_PRICE' | translate}}"
                                ng-hide="dpViewOrderCtrl.isPriceEditable || dpViewOrderCtrl.hidePrice">
                                {{item.price | currency:
                                    dpViewOrderCtrl.selectedCurrency.symbol :2}}
                            </td>

                            <td class="disableWordBreak" ng-show="dpViewOrderCtrl.isPriceEditable && !dpViewOrderCtrl.hidePrice"
                                data-label="{{'ORDER.ITEM_PRICE' | translate}}">

                                <div class="inputWithIconWrap" ng-show="item.quantity > 0">
                                    <span ng-show="item.quantity > 0"
                                          class="input-icon">{{dpViewOrderCtrl.selectedCurrency.symbol}}&nbsp;</span>
                                    <input onclick="this.select()" ng-model="item.price"
                                           ng-change="dpViewOrderCtrl.updateItemTotals(true)" type="number"
                                           class="input-with-icon">
                                    <span ng-hide="item.quantity > 0"
                                          class="input-icon">{{item.price | currency:dpViewOrderCtrl.selectedCurrency.symbol:2}}</span>
                                </div>

                            </td>

                            <td ng-show="dpViewOrderCtrl.hasVisibleDiscountedPrices()" class="disableWordBreak" data-label="{{'ORDER.DISCOUNTED_PRICE' | translate}}">
                                {{ item.discountedPrice | currency:dpViewOrderCtrl.selectedCurrency.symbol:2 }}
                            </td>

                            <td class="disableWordBreak" data-label="{{'ORDER.TOTAL_PRICE' | translate}}" ng-hide="dpViewOrderCtrl.hidePrice">
                                {{item.totalPrice | currency:dpViewOrderCtrl.selectedCurrency.symbol:2}}
                            </td>
                            <td data-label="{{'ORDER.QTY_SHIPPED' | translate}}" ng-show="dpViewOrderCtrl.showPartialShipped">
                                {{item.shippedQuantity}}
                            </td>

					  <td class="disableWordBreak" data-label="{{'ORDER.STOCK' | translate}}"
						ng-if="dpViewOrderCtrl.isPreviewStockLevelEnabled && !dpViewOrderCtrl.isCustomerOrder()">
						    <span title="{{'ORDER.IN_STOCK' | translate}}" tooltip-trigger="outsideClick" uib-tooltip="{{'ORDER.IN_STOCK' | translate}}" class="success-alert" ng-if="item.stock >= 3"><i class="fas fa-layer-group text-success pointer" ></i></span>
						    <span title="{{'ORDER.LOW_STOCK' | translate}}" tooltip-trigger="outsideClick" uib-tooltip="{{'ORDER.LOW_STOCK' | translate}}" class="warning-alert" ng-if="item.stock < 3 && item.stock > 0 "><i class="fas fa-layer-group text-warning pointer" ></i></span>
						    <span title="{{'ORDER.STOCK_SUBJECT' | translate}}" tooltip-trigger="outsideClick" uib-tooltip="{{'ORDER.STOCK_SUBJECT' | translate}}" class="warning-alert " ng-if="item.stock === null || item.stock < 1"><i class="fas fa-layer-group text-danger pointer" ></i></span>
					  </td>

					  <td data-label="{{'GENERAL.ACTIONS' | translate}}">

						    <div class="d-flex justify-content-center align-items-center cadGap">

								<button type="button" ng-class="{'disableButton': !item.techDocs.length > 0}" ng-disabled="!item.techDocs.length > 0" uib-tooltip="{{'ORDER.VIEW_TECH_INFO' | translate}}" ng-click="dpViewOrderCtrl.viewLinkedTechDocs(item)" class="btn secondary warning">
									  <i class="fa fa-file-pdf fa-lg fa-fw"></i>
								</button>

								<button type="button" ng-class="item.commentThread && item.unreadComment ? 'newCommentAnim_order' : '' || item.unreadComment ? 'unreadcomment' : '' || {'disableButton': (dpViewOrderCtrl.data.orderStatus == 'CLOSED' && item.commentThread === null) ||
                            (dpViewOrderCtrl.data.orderStatus == 'CANCELLED' && item.commentThread === null) ||
                            (dpViewOrderCtrl.data.orderStatus == 'SHIPPED' && item.commentThread === null) ||
                            (dpViewOrderCtrl.data.orderStatus == 'EXTERNAL' && item.commentThread === null) ||
                          (dpViewOrderCtrl.enquiriesOnly && item.commentThread === null)}" ng-disabled="(dpViewOrderCtrl.data.orderStatus == 'CLOSED' && item.commentThread === null) ||
								(dpViewOrderCtrl.data.orderStatus == 'CANCELLED' && item.commentThread === null) ||
								(dpViewOrderCtrl.data.orderStatus == 'SHIPPED' && item.commentThread === null) ||
								(dpViewOrderCtrl.data.orderStatus == 'EXTERNAL' && item.commentThread === null) ||
								(dpViewOrderCtrl.enquiriesOnly && item.commentThread === null)" uib-tooltip="{{'ORDER.COMMENTS_TOOLTIP' | translate}}" ng-click="dpViewOrderCtrl.openComment(item.commentThread.id, item.orderItemId)" class="btn primary primary-outline comments">

								<i ng-show="item.commentThread === null" class="far fa-comments fa-lg fa-fw"></i>

								<i  ng-show="!item.unreadComment && item.commentThread !== null" class="fa fa-comments fa-lg fa-fw"></i>
								<i ng-if="item.commentThread && item.unreadComment" class="fa fa-comments fa-lg fa-fw"></i>

								</button>

								<span ng-show="dpViewOrderCtrl.isActionActive">

						<button ng-show="!item.archived" uib-tooltip="{{'ORDER.REMOVE' | translate}}" ng-click="dpViewOrderCtrl.removeItem($index)" class="btn secondary danger">
    <i class="fa fa-trash-o fa-lg fa-fw"></i>
  </button>

						<button ng-show="item.archived" uib-tooltip="{{'ORDER.UNDO' | translate}}" ng-click="dpViewOrderCtrl.removeItem($index)" class="btn secondary danger">
    <i class="fa fa-undo fa-lg fa-fw"></i>
  </button>

							  </span>

								<span ng-show="dpViewOrderCtrl.deleteOrderItem" uib-tooltip="{{'ORDER.REMOVE' | translate}}">

									  <button ng-show="item.quantity > 0" ng-click="dpViewOrderCtrl.deleteItem($index)" class="btn secondary danger">
    <i class="fa fa-trash-o fa-lg fa-fw"></i>
  </button>

							  </span>

						    </div>

					  </td>
				</tr>

                        <tr ng-hide="dpViewOrderCtrl.isOrderItemsLoaded" align="center">
                            <td class="preloader" colspan="6"><img ng-src="images/cadpreloader.gif" class="ajax-loader"
                                                                   height="60"
                                                                   width="60"/>
                            </td>
                        </tr>
                        </tbody>
                    </table>

                </div>

            </div>

            <div class="col-md-12 customDivContainer order_partsList manuallyAddedOrder"
                 ng-show="dpViewOrderCtrl.displayAdditionalParts">

                <div class="customDivStyling">
                    <h2 class=""><span translate>ORDER.MANUALLY_ADDED_PARTS</span></h2>
                    <div class="">
                        <table class="table table-bordered">
                            <thead>
                            <tr>
                                <th translate>ORDER.PART_DETAILS</th>
                                <th translate>ORDER.QTY</th>
                                <th ng-hide="dpViewOrderCtrl.hidePrice" translate>ORDER.ITEM_PRICE
                                </th>
                                <th ng-hide="dpViewOrderCtrl.hidePrice" translate>ORDER.PRICE</th>
                                <th ng-show="dpViewOrderCtrl.isActionActive" translate>ORDER.REMOVE
                                </th>
                            </tr>
                            </thead>

                            <tbody>
                            <tr ng-repeat="manualPart in dpViewOrderCtrl.additionalParts"
                                ng-class="manualPart.quantity>0 ? '' : 'strike-through'">
                                <td data-label="{{'ORDER.PART_DETAILS' | translate}}">
                                    <strong>{{"ORDER.PART" | translate}}# {{manualPart.partNumber}}</strong> -
                                    {{manualPart.partDescription}}
                                    {{"ORDER.BASED_ON_PRODUCT" | translate}} -
                                    <strong>{{manualPart.machineName}}</strong>
                                </td>

                                <td data-label="{{'ORDER.QTY' | translate}}"
                                    ng-show="dpViewOrderCtrl.isQuantityEditable">
                        <span ng-show="manualPart.quantity > 0">
                            <input onclick="this.select()" class="priceInput inputButtonWidth" type="number" min="0"
                                   ng-model="manualPart.quantity"
                                   ng-change="dpViewOrderCtrl.updateManualPartTotals(true)">
                        </span>
                                    <span ng-hide="manualPart.quantity > 0">{{manualPart.quantity}}</span>
                                </td>

                                <td data-label="{{'ORDER.QTY' | translate}}"
                                    ng-hide="dpViewOrderCtrl.isQuantityEditable">{{manualPart.quantity}}
                                </td>
                                <td class="disableWordBreak" data-label="{{'ORDER.ITEM_PRICE' | translate}}"
                                    ng-show="!dpViewOrderCtrl.isPriceEditable && !dpViewOrderCtrl.hidePrice">
                                    {{manualPart.price |
                                currency:dpViewOrderCtrl.selectedCurrency.symbol:2}}
                                </td>

                                <td class="disableWordBreak" data-label="{{'ORDER.ITEM_PRICE' | translate}}"
                                    ng-show="dpViewOrderCtrl.isPriceEditable && !dpViewOrderCtrl.hidePrice">
                                    <div ng-show="manualPart.quantity > 0" class="inputWithIconWrap">
                                        <span class="input-icon">{{dpViewOrderCtrl.selectedCurrency.symbol}}&nbsp;</span>
                                        <input class="input-with-icon" type="number"
                                               ng-model="manualPart.price"
                                               ng-change="dpViewOrderCtrl.updateManualPartTotals(true)">
                                        <span ng-hide="manualPart.quantity > 0"
                                              class="input-icon">{{item.price | currency:dpViewOrderCtrl.selectedCurrency.symbol:2}}</span>
                                    </div>
                                </td>

                                <td class="disableWordBreak" data-label="{{'ORDER.PRICE' | translate}}" ng-hide="dpViewOrderCtrl.hidePrice">
                                    {{manualPart.totalPrice | currency:dpViewOrderCtrl.selectedCurrency.symbol:2}}
                                </td>

                                <td data-label="{{'ORDER.REMOVE' | translate}}"
                                    ng-show="dpViewOrderCtrl.isActionActive">

						    <button ng-show="!manualPart.archived" uib-tooltip="{{'ORDER.REMOVE' | translate}}" ng-click="dpViewOrderCtrl.removeManualPart($index)" class="btn secondary danger">
								<i class="fa fa-trash-o fa-lg fa-fw"></i>
						    </button>

						    <button ng-show="manualPart.archived" uib-tooltip="{{'ORDER.UNDO' | translate}}" ng-click="dpViewOrderCtrl.removeManualPart($index)" class="btn secondary danger">
								<i class="fa fa-undo fa-lg fa-fw"></i>
						    </button>


                                </td>

                            </tr>

                            <tr data-label="{{'ORDER.REMOVE' | translate}}" ng-hide="dpViewOrderCtrl.isOrderItemsLoaded"
                                align="center">
                                <td class="preloader" colspan="6"><img ng-src="images/cadpreloader.gif"
                                                                       class="ajax-loader"
                                                                       height="60"
                                                                       width="60"/>
                                </td>
                            </tr>

                            </tbody>
                        </table>
                    </div>
                </div>

            </div>

            <div class="col-md-12 customDivContainer addPartsOrder" ng-show="dpViewOrderCtrl.isAddPartsVisible">

                <div class="customDivStyling">
                    <h2 class=""><span translate>ORDER.ADD_PARTS_TO_ORDER</span></h2>
                    <form class="form" id="addManualPartsForm" name="dpViewOrderCtrl.addManualPartsForm">
                        <div class="">
                            <table class="table table-bordered">
                                <thead>
                                <tr>
                                    <th>{{"ORDER.PART_NO" | translate}} <span
                                            class="required-field">&ast;{{"ORDER.REQUIRED" | translate}}</span></th>
                                    <th>{{"ORDER.PART_DESC" | translate}} <span
                                            class="required-field">&ast;{{"ORDER.REQUIRED" | translate}}</span></th>
                                    <th>{{"ORDER.PRODUCT" | translate}}</th>
                                    <th>{{"ORDER.QTY" | translate}} <span
                                            class="required-field">&ast;{{"ORDER.REQUIRED" | translate}}</span>
                                    </th>
                                    <th><span>{{"ORDER.REMOVE" | translate}}</span></th>
                                </tr>
                                </thead>

                                <tbody>

                                <tr ng-repeat="manualPart in dpViewOrderCtrl.addManualParts">
                                    <td>
                                        <input maxlength="200" data-label="{{'ORDER.PART_NUMBER' | translate}}"
                                               class="w-100 mw-100" placeholder="{{'ORDER.PART_NUMBER' | translate}}" type="text"
                                               ng-model="manualPart.partNumber" required ng-class="" name="partNumber">
                                    </td>
                                    <td>
                                        <input maxlength="200" data-label="{{'ORDER.PART_DESCRIPTION_EXAMPLE' | translate}}"
                                               class="w-100 mw-100" placeholder="{{'ORDER.PART_DESCRIPTION_EXAMPLE' | translate}}"
                                               type="text" ng-model="manualPart.partDescription" required ng-class=""
                                               name="partDescription">
                                    </td>
                                    <td>
                                        <input maxlength="80" data-label="{{'ORDER.PRODUCT_NAME' | translate}}"
                                               class="w-100 mw-100" placeholder="{{'ORDER.PRODUCT_NAME' | translate}}" type="text"
                                               ng-model="manualPart.machineName" name="machineName">
                                    </td>
                                    <td>
                                        <input data-label="{{'ORDER.QUANTITY' | translate}}"
                                               class="w-100 mw-100" placeholder="{{'ORDER.QUANTITY' | translate}}"
                                               ng-model="manualPart.quantity" type="number"
                                               ng-model-options="">
                                    </td>
                                    <td>
                                        <button class="btn secondary small danger" uib-tooltip="{{'ORDER.REMOVE' | translate}}"
                                                ng-click="dpViewOrderCtrl.removeAddManualPart(manualPart)">
                                            <i class="fa fa-trash-o"></i>
                                        </button>
                                    </td>
                                </tr>

                                </tbody>

                            </table>
                            <div class="table-footer">

                                <div class="left">
                                    <a href="" class="light" ng-click="dpViewOrderCtrl.createAddManualPart()"><i
                                            class="fa fa-plus"></i>
                                        {{"ORDER.ADD_NEW_ITEM" | translate}}</a>
                                </div>
                                <div ng-show="dpViewOrderCtrl.addManualPartsActive" class="d-flex justify-content-end">
                                    <button class="btn secondary"
                                            ng-click="dpViewOrderCtrl.cancelCreateAddManualParts()" translate>
                                        GENERAL.CANCEL
                                    </button>

                                    <button class="btn primary ml-2" type="submit"
                                            ng-click="dpViewOrderCtrl.saveAddManualParts()"
                                            translate>
                                        ORDER.ADD_TO_ORDER
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>

            </div>

            <div class="col-md-12 csvOrder" ng-class="!dpViewOrderCtrl.displayNotes ? 'csvDownload' : ''"
                 ng-show="dpViewOrderCtrl.exportPartDataCsvVisible">

                <div class="flex customDivStyling">

                    <div class="col-12 col-md-auto">
                        <h2 class=""><span translate>ORDER.DOWNLOAD_CSV</span></h2>
                        <p class="mb-0" translate>ORDER.CSV_DESCRIPTION</p>
                    </div>

                    <div class="col-12 col-md-auto ml-auto">
                        <button class="btn btn-primary" ng-click="dpViewOrderCtrl.exportOrderPartsToCSV()" translate>
                            ORDER.DOWNLOAD_CSV
                        </button>
                    </div>

                </div>

            </div>

        </div>

    </section>

</div>