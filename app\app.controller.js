(function () {
    'use strict';

    angular
        .module('cadshareApp')
        .controller('AppController', AppController);

    AppController.$inject = ['$location', 'manufacturerService', '$sce'];

    function AppController($location, manufacturerService, $sce) {
        var vm = this;

        vm.logoUrl = '';
        vm.supportEmail = '';
        vm.favicon = './images/favicon.ico';
        vm.userRegistrationEnabled = false;
        initialize();

        function initialize() {
            var host = $location.host();
            manufacturerService.fetchManufacturerDetailsByDomain(host)
                .then(fetchManufacturerSuccess, fetchManufacturerFailed);
        }

        function fetchManufacturerSuccess(response) {
            var data = response.data;
            manufacturerService.setManufacturer(data);
            vm.logoUrl = data.logoUrl ? data.logoUrl : './images/CADshare-logo-dark.svg';
            vm.supportEmail = data.supportEmail ? data.supportEmail : '<EMAIL>';
            vm.favicon = (data.manufacturerSettings && data.manufacturerSettings.faviconUrl) ? $sce.trustAsResourceUrl(data.manufacturerSettings.faviconUrl) : "./images/favicon.ico";
            vm.title = (data.manufacturerSettings && data.manufacturerSettings.tabTitle) ? data.manufacturerSettings.tabTitle : "CADshare";
            vm.userRegistrationEnabled = (data.manufacturerSettings && typeof(data.manufacturerSettings.userRegistrationEnabled) !== "undefined") ? data.manufacturerSettings.userRegistrationEnabled : false
        }

        function fetchManufacturerFailed() {
            vm.logoUrl = './images/CADshare-logo-dark.svg';
            vm.supportEmail = '<EMAIL>';
            vm.favicon = "./images/favicon.ico";
            vm.userRegistrationEnabled = false
        }

    }
})();