(function () {
    'use strict';

    angular
        .module('app.services')
        .factory('dpCreateUserService', dpCreateUserService);

    dpCreateUserService.$inject = ['$http', 'apiConstants', 'userService', '$location'];

    function dpCreateUserService($http, apiConstants, userService, $location) {
        var dealerPlusPrefix = "/dealerplus";
        return {
            createManufacturerSubEntityCustomer: createManufacturerSubEntityCustomer,
            editManufacturerSubEntityCustomer: editManufacturerSubEntityCustomer,
            deleteManufacturerSubEntity: deleteManufacturerSubEntity,
            createManufacturerSubEntityDealer: createManufacturerSubEntityDealer,
            createCustomerUser: createCustomerUser,
            createDealerUser: createDealerUser,
            createManufacturerUser: createManufacturerUser,
            editCustomerUser: editCustomerUser,
            editDealerUser: editDealerUser,
            editManufacturerUser: editManufacturerUser,
            createManufacturer: createManufacturer
        };

        function createManufacturerSubEntityCustomer(customerName, defaultDiscount) {
            var manufacturerId = userService.getManufacturerId();
            var customerData = {
                "name": customerName,
                "description": "",
                "defaultDiscount": defaultDiscount,
                "manufacturerId": manufacturerId,
                "parentSubEntityId": manufacturerId,
                "manufacturerSubEntityType": "CUSTOMER"
            };
            return $http.post(apiConstants.url + dealerPlusPrefix + '/manufacturersubentity', customerData);
        }

        function editManufacturerSubEntityCustomer(customerName, subEntityId, defaultDiscount) {
            var customerData = {
                "name": customerName,
                "defaultDiscount": defaultDiscount,
                "manufacturerSubEntityId": subEntityId
            };
            return $http.put(apiConstants.url + dealerPlusPrefix + '/manufacturersubentity', customerData);
        }

        function deleteManufacturerSubEntity(manufacturerSubEntityId) {
            return $http.delete(apiConstants.url + dealerPlusPrefix + '/manufacturersubentity/' + manufacturerSubEntityId);
        }

        function createManufacturerSubEntityDealer(dealerName, defaultDiscount) {
            var manufacturerId = userService.getManufacturerId();
            var customerData = {
                "name": dealerName,
                "description": "",
                "defaultDiscount": defaultDiscount,
                "manufacturerId": manufacturerId,
                "parentSubEntityId": manufacturerId,
                "manufacturerSubEntityType": "DEALER"
            };
            return $http.post(apiConstants.url + dealerPlusPrefix + '/manufacturersubentity', customerData);
        }

        function createCustomerUser(userObject, manufacturerSubEntityId) {
            var userData = {
                "emailAddress": userObject.emailAddress,
                "firstName": userObject.firstName,
                "lastName": userObject.lastName,
                "userType": "MANUFACTURER_SUB_ENTITY_CUSTOMER",
                "manufacturerSubEntityId": manufacturerSubEntityId,
                "userPermissions": userObject.permissionsArray
            };
            var siteUrl = $location.protocol() + '://' + $location.host();
            var config = {headers: {'Site-Url': siteUrl}};

            var theme = localStorage.getItem("theme");
            if (theme && theme != null) {
                return $http.post(apiConstants.url + dealerPlusPrefix + '/user?theme=' + theme.toLowerCase(), userData, config);
            } else {
                return $http.post(apiConstants.url + dealerPlusPrefix + '/user', userData, config);
            }
        }

        function createDealerUser(userObject, manufacturerSubEntityId) {
            var userData = {
                "emailAddress": userObject.emailAddress,
                "firstName": userObject.firstName,
                "lastName": userObject.lastName,
                "userType": "MANUFACTURER_SUB_ENTITY_DEALER",
                "manufacturerSubEntityId": manufacturerSubEntityId,
                "userPermissions": userObject.permissionsArray
            };
            var siteUrl = $location.protocol() + '://' + $location.host();
            var config = {headers: {'Site-Url': siteUrl}};

            var theme = localStorage.getItem("theme");
            if (theme && theme != null) {
                return $http.post(apiConstants.url + dealerPlusPrefix + '/user?theme=' + theme.toLowerCase(), userData, config);
            } else {
                return $http.post(apiConstants.url + dealerPlusPrefix + '/user', userData, config);
            }
        }

        function createManufacturerUser(userObject, manufacturerId) {
            var userData = {
                "emailAddress": userObject.emailAddress,
                "firstName": userObject.firstName,
                "lastName": userObject.lastName,
                "userType": "MANUFACTURER",
                "manufacturerId": manufacturerId,
                "userPermissions": userObject.permissionsArray
            };
            var siteUrl = $location.protocol() + '://' + $location.host();
            var config = {headers: {'Site-Url': siteUrl}};

            var theme = localStorage.getItem("theme");
            if (theme && theme != null) {
                return $http.post(apiConstants.url + dealerPlusPrefix + '/user?theme=' + theme.toLowerCase(), userData, config);
            } else {
                return $http.post(apiConstants.url + dealerPlusPrefix + '/user', userData, config);
            }
        }

        function editCustomerUser(userObject, manufacturerSubEntityId, userId) {
            var userData = {
                "emailAddress": userObject.emailAddress,
                "firstName": userObject.firstName,
                "lastName": userObject.lastName,
                "userType": "MANUFACTURER_SUB_ENTITY_CUSTOMER",
                "manufacturerSubEntityId": manufacturerSubEntityId,
                "active": userObject.active,
                "userPermissions": userObject.permissionsArray
            };
            var siteUrl = $location.protocol() + '://' + $location.host();
            var config = {headers: {'Site-Url': siteUrl}};

            var theme = localStorage.getItem("theme");
            if (theme && theme != null) {
                return $http.put(apiConstants.url + dealerPlusPrefix + '/user/' + userId +'?theme=' + theme.toLowerCase(), userData, config);
            } else {
                return $http.put(apiConstants.url + dealerPlusPrefix + '/user/' + userId, userData, config);
            }
        }

        function editDealerUser(userObject, manufacturerSubEntityId, userId) {
            var userData = {
                "emailAddress": userObject.emailAddress,
                "firstName": userObject.firstName,
                "lastName": userObject.lastName,
                "userType": "MANUFACTURER_SUB_ENTITY_DEALER",
                "manufacturerSubEntityId": manufacturerSubEntityId,
                "active": userObject.active,
                "userPermissions": userObject.permissionsArray
            };
            var siteUrl = $location.protocol() + '://' + $location.host();
            var config = {headers: {'Site-Url': siteUrl}};
            return $http.put(apiConstants.url + dealerPlusPrefix + '/user/' + userId, userData, config);
        }

        function editManufacturerUser(userObject, manufacturerId, userId) {
            var userData = {
                "emailAddress": userObject.emailAddress,
                "firstName": userObject.firstName,
                "lastName": userObject.lastName,
                "userType": "MANUFACTURER",
                "manufacturerId": manufacturerId,
                "userPermissions": userObject.permissionsArray,
                "active": userObject.active
            };
            var siteUrl = $location.protocol() + '://' + $location.host();
            var config = {headers: {'Site-Url': siteUrl}};
            return $http.put(apiConstants.url + dealerPlusPrefix + '/user/' + userId, userData, config);
        }

        function createManufacturer(manufacturerName) {
            var data = {
                "name": manufacturerName,
                "description": ""
            };
            return $http.post(apiConstants.url + dealerPlusPrefix + '/manufacturer', data);
        }

    }
})();
