.pallet-container {
    background-color: #FFFFFF;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 1rem;
    border: 1px solid #DDDDDD;
    width: 100%;
    max-width: 30vw;
    min-width: 30vw;
    max-height: 45vh;
    overflow-y: auto;

    @media (max-width: 992px) {
        max-width: 50vw;
        min-width: 50vw;
        max-height: 80vh;
    }

    .keep-width {
        min-width: 140px;
      }

    .eye-button {
        background-color: white; // White background
        border: 1px solid #ccc; // Light grey border
        width: 25px; // Square shape
        height: 25px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 5px; // Slightly rounded corners

        i {
            font-size: 15px; // Size of the eye icon
            color: #333; // Icon color
        }
    }

        .options-container {
    
            .options-header {
                display: flex;
                align-items: center;
                margin-bottom: 1rem;
    
                i {
                    font-size: 1em;
                }
    
                .options-title {
                    font-size: 1em;
                    font-weight: bold;
                    margin-right: 1rem;
                    font-weight: bold;
                }
    
                .options-description {
                    font-size: 1em;
                    color: #666;
                }
            }
    
            .option-set-wrapper {
                max-height: 225px;
                overflow-y: auto;
                overflow-x: hidden;
                margin-bottom: 10px;
                border-bottom: 1px solid #ccc;
                border-top: 1px solid #ccc;
                position: relative;

                @media (max-width: 992px) {
                    max-height: 150px;
                }
            }
    
            .option-set-container {
                display: flex;
                flex-direction: row;
                align-items: center;
                border: 1px solid #ccc;
                padding-left: 5px;
                margin-bottom: 5px;
                padding-right: 5px;
    
                &:hover {
                    background-color: #f9f9f9;
                }
    
                &.selected {
                    border: 2px solid #3392FC;
                }
    
                &:last-child {
                    margin-bottom: 0;
                }
    
                &:last-child:not(.selected) {
                    margin-bottom: 0;
                    border-bottom: 0;
                }
    
                &:first-child:not(.selected) {
                    border-top: 0;
                }
    
                input[type="radio"] {
                    appearance: none;
                    position: absolute;
                    opacity: 0;
                    width: 0;
                    height: 0;
                }
    
                .option-content {
                    display: flex;
                    flex-direction: column;
                    cursor: pointer;
                    padding: 0;
                    margin: 0;
                    border-radius: 5px;
                    width: 100%;
                }
    
                .option-details {
                    display: flex;
                    flex-direction: column;
                    width: 100%;
    
                    .top-row,
                    .bottom-row {
                        display: flex;
                        justify-content: space-between;
                        margin-bottom: 5px;
    
                        .truncate-description {
                            display: -webkit-box;
                            -webkit-line-clamp: 2;
                            -webkit-box-orient: vertical;
                            overflow: hidden;
                        }
                    }
    
                    .part-number,
                    .description,
                    .price,
                    .stock {
                        flex: 1;
    
                        strong {
                            color: #9BA9B7;
                            font-weight: lighter;
                            font-size: 14px;
                            margin-bottom: 0.2em;
                        }
                    }
    
                    .description,
                    .stock {
                        align-self: flex-start;
                    }
                }
            }
        }
    .part-number,
    .description,
    .price,
    .stock {
        flex: 1;

        strong {
            color: #9BA9B7;
            font-weight: lighter;
            font-size: 14px;
            margin-bottom: 0.2em;
        }
    }

    .description,
    .stock {
        align-self: flex-start;
    }

    small{

        font-size:0.75em;
        
    }

    .price {
        grid-area: price;
    }

    .quantity-box {
        height: 40px;
        width: 80px;
    }

    .add-item {
        @extend %clearlist;
        height: auto;
        width: 100%;
        background-color: #d4e8fe;
        padding: 1rem;

        small.label {
            font-size: 0.7em;
            font-weight: 600;
            text-transform: uppercase;
        }
    }

    .parts-pallet-info {
        display: grid;
        grid-template-areas:
            "partNumber description"
            "altPartNumber price";
        grid-template-columns: 0.8fr 1fr;
    }

    .alt-part-number {
        grid-area: altPartNumber;
    }

    .pallet-heading {
        background-color: #F1F1F1;
    }

    .optionSetIcon {
        width: 35px;
        height: 35px;
        background-color: #007bff;
        border-radius: 50%;
        color: white;
    }
}