(function () {
    'use strict';

    angular
        .module('app.services')
        .factory('publicationService', publicationService);

    publicationService.$inject = ['$http', 'apiConstants', 'manufacturerPublicationService', 'userService', '$rootScope'];

    function publicationService($http, apiConstants, manufacturerPublicationService, userService, $rootScope) {
        return {
            getManual: getManual,
            getPublication: getPublication,
            getKits: getKits,
            getTechDocs: getTechDocs,
            getVideos: getVideos,
            getAssignedPublicationIdsForSubEntity: getAssignedPublicationIdsForSubEntity,
            assignPublicationsToPurchaser: assignPublicationsToPurchaser,
            fetchPublications: fetchPublications,
            publishUnpublishManual: publishUnpublishManual,
            publishManual: publishManual,
            getPublicationCategories: getPublicationCategories,
            createPublicationCategory: createPublicationCategory,
            deletePublicationCategory: deletePublicationCategory,
            updatePublicationCategory: updatePublicationCategory
        };

        function getManual(manualId) {
            return $http.get(apiConstants.url + '/manual/' + manualId);
        }

        function getPublication(publicationId) {
            var purchaserId = userService.getManufacturerSubEntityId();
            return $http.get(apiConstants.url + '/purchasers/' + purchaserId + '/publications/' + publicationId);
        }

        function getKits(manualId) {
            var isStockWarehousesEnabled = userService.getStockWarehousesEnabled();
            var warehouseId = userService.getWarehouseId();
            var url = apiConstants.url + '/manual/' + manualId + '/kits';
            if (warehouseId && isStockWarehousesEnabled) {
                url += '?warehouseId=' + warehouseId;
            }
            return $http.get(url);
        }

        function getTechDocs(manualId) {
            return $http.get(apiConstants.url + '/manual/' + manualId + '/techDocs');
        }

        function getVideos(manualId) {
            return $http.get(apiConstants.url + '/manual/' + manualId + '/video');
        }

        function fetchPublications() {
            var manufacturerId = userService.getManufacturerId();
            return $http.get(apiConstants.url + '/manufacturers/' + manufacturerId + '/publications');
        }

        function getAssignedPublicationIdsForSubEntity(manufacturerSubEntityId) {
            return $http.get(apiConstants.url + '/manufacturersubentity/' + manufacturerSubEntityId + '/assignedManualIds?published=false');
        }

        function assignPublicationsToPurchaser(manufacturerId, purchaserId, publicationIds) {
            var url = apiConstants.url + '/manufacturers/' + manufacturerId + '/publications/assign-to-purchaser';
            var payload = {
                purchaserId: purchaserId,
                publications: publicationIds
            };
            return $http.post(url, payload);
        }

        function fetchPublications() {
            return manufacturerPublicationService.fetchPublications();
        }

        function publishManual(manualId) {
            return $http.put(apiConstants.url + '/manual/' + manualId + '/status', { status: 'PUBLISHED' });
        }

        function publishUnpublishManual(publicationOrId, targetStatus) {
            var publicationId, statusToSend, isPublication;
            
            if (typeof publicationOrId === 'object') {
                isPublication = true;
                publicationId = publicationOrId.id;
                statusToSend = publicationOrId.published ? 'UNPUBLISHED' : 'PUBLISHED';
            } else {
                isPublication = false;
                publicationId = publicationOrId;
                statusToSend = targetStatus ? 'PUBLISHED' : 'UNPUBLISHED';
            }
            
            console.log('Publishing/Unpublishing publication', publicationId, 'to status', statusToSend);
            
            return manufacturerPublicationService.publishUnpublishManual(publicationId, statusToSend)
                .then(function (response) {
                    if (isPublication) {
                        publicationOrId.published = statusToSend === 'PUBLISHED';
                        
                        // Broadcast the change to update all UI components
                        $rootScope.$broadcast('publication:statusChanged', {
                            publicationId: publicationId,
                            newStatus: publicationOrId.published
                        });
                    }
                    
                    return response;
                })
                .catch(function (error) {
                    console.error('Error in publishUnpublishManual:', error);
                    throw error;
                });
        }

        function getPublicationCategories() {
            var manufacturerId = userService.getManufacturerId();
            var url = apiConstants.url + '/manufacturers/' + manufacturerId + '/publication-categories';
            return $http.get(url);
        }

        function createPublicationCategory(categoryName) {
            var manufacturerId = userService.getManufacturerId();
            var url = apiConstants.url + '/manufacturers/' + manufacturerId + '/publication-categories';
            var payload = { name: categoryName };
            return $http.post(url, payload);
        }

        function deletePublicationCategory(categoryId) {
            var manufacturerId = userService.getManufacturerId();
            if (!manufacturerId || !categoryId) {
                console.error("Manufacturer ID or Category ID missing for deletion.");
                return Promise.reject("Missing required IDs for deletion.");
            }
            var url = apiConstants.url + '/manufacturers/' + manufacturerId + '/publication-categories/' + categoryId;
            return $http.delete(url);
        }

        function updatePublicationCategory(categoryId, newCategoryName) {
            var manufacturerId = userService.getManufacturerId();
            if (!manufacturerId || !categoryId || !newCategoryName) {
                console.error("Manufacturer ID, Category ID, or new Category Name missing for update.");
                return Promise.reject("Missing required information for update.");
            }
            var url = apiConstants.url + '/manufacturers/' + manufacturerId + '/publication-categories/' + categoryId;
            var payload = { name: newCategoryName };
            return $http.put(url, payload);
        }
    }
})();
