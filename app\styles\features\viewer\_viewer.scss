// =============================================================================
// Viewer
// =============================================================================

.product-viewer {
    position: absolute;

    &.horizontal {
        width: 100%;
        min-width: $min-width;
        height: calc(100% - 315px) !important;
    }

    &.vertical {
        width: calc(100% - 260px);
        height: calc(100% - 60px); // 120 = header height + breadcrumb height
        right: 0px;
    }

    .viewer {
        width: calc(100vw - 260px);
        height: calc(100vh - 60px);
        position: absolute;
        z-index: 0;
    }
    .viewerSideMenuOpen {
        width: calc(100vw - 680px);
        height: calc(100vh - 60px);
        position: absolute;
        z-index: 0;
    }
    .softCopyViewer {
        width: calc(100vw - 680px);
        height: calc(100vh - 138px);
        position: absolute;
        z-index: 0;
    }
    .viewerBasketOpen {
        width: calc(100vw - 620px);
        height: calc(100vh - 60px);
        position: absolute;
        z-index: 0;
    }

    .btn.reset {
        position: absolute;
        top: $spacing;
        left: $spacing;
        z-index: 1;
        border: none;
        padding: $spacing/2;
        @include box-shadow-spread(0, 0, 8px, 0, rgba($midnight, 0.45), false);
    }

    .enableWeldment {
        position: absolute;
        top: $spacing;
        left: $spacing * 9;
        z-index: 1;
        border: none;
        padding: $spacing/2;
        color: #000;
    }

    .btn.translateButton {
        position: absolute;
        top: $spacing;
        left: $spacing;
        z-index: 1;
        border: none;
        padding: $spacing/2;
        @include box-shadow-spread(0, 0, 8px, 0, rgba($midnight, 0.45), false);
    }

    .btn.modelLinkButton {
        position: absolute;
        top: $spacing * 7;
        left: $spacing * 7;
        z-index: 1;
        border: none;
        padding: $spacing/2;
        @include box-shadow-spread(0, 0, 8px, 0, rgba($midnight, 0.45), false);
    }

    .activePartLink {
        background: rgb(92, 184, 92);
    }

    .manufacturer-explode-tool-container {
        z-index: 10;
        position: absolute;
        margin-left: 50px;
        bottom: 157px;
        text-align: center;
    }

    .customer-explode-tool-container {
        z-index: 10;
        position: absolute;
        left: 45px;
        bottom: 181px;
        text-align: center;
    }

    .softcopy-explode-tool-container {
        z-index: 10;
        position: absolute;
        left: 186px;
        bottom: 266px;
        text-align: center;
    }

    .newExplodeRangeInput {
        position: absolute;
        text-align: center;
        transform: translateX(-50%);
        background-color: #fff;
        color: #484848;
        padding: 0px 0px 0px 0px;
        border-radius: 5px;
        opacity: 1;
    }

    .softExplodeRangeInput {
        position: absolute;
        text-align: center;
        transform: translateX(-50%);
        background-color: #fff;
        color: #484848;
        padding: 0px 0px 0px 0px;
        border-radius: 5px;
        opacity: 1;
    }

    .customerExplodeRangeInput {
        position: absolute;
        text-align: center;
        transform: translateX(-50%);
        background-color: #fff;
        color: #484848;
        padding: 0px 0px 0px 0px;
        border-radius: 5px;
        opacity: 1;
    }

    input[type="range"] {
        -webkit-appearance: none;
        width: 70px;
        height: 13px;
        border-radius: 5px;
    }

    input[type="range"]::-webkit-slider-runnable-track {
        -webkit-appearance: none;
        background: #ced0d3;
        outline: none;
        opacity: 0.7;
        margin-left: -10px;
        margin-right: -10px;
    }

    .newExplodeRangeInput input,
    .newExplodeRangeInput select {
        margin: 7px 9px;
        width: 70px;
    }
    .softExplodeRangeInput input,
    .softExplodeRangeInput select {
        margin: 12px 10px;
    }
    .customerExplodeRangeInput input,
    .customerExplodeRangeInput select {
        margin: 8px 10px;
    }

    select,
    input {
        width: 100%;
        height: 34px;
        padding-left: 7px;
        -webkit-border-radius: 2px;
        border-radius: 2px;
        background-clip: padding-box;
    }

    input[type="range"]::-webkit-slider-thumb {
        -webkit-appearance: none;
        border: none;
        background-color: $blue;
        width: 10px;
        height: 24px;
    }

    input[type="range"]:focus {
        outline: none;
    }

    input[type="range"]:focus::-webkit-slider-runnable-track {
        background: #ccc;
    }

    .fa-chevron-up:before {
        position: relative;
        top: -2px;
    }

    .fa-chevron-right:before {
        position: relative;
        right: -2px;
    }

    .viewerMessage {
        position: absolute;
        top: $spacing;
        left: $spacing * 24;
        z-index: 1;
        border: none;
        padding: $spacing/2;
        color: #000;
    }

    .return-to-parent {
        position: absolute;
        bottom: $spacing;
        left: $spacing * 2;
        z-index: 1;
    }

    .btn {
        //width: 100%;
        //border: none;
        //border-radius: 0;
        //line-height: 40px;

        &:disabled {
            opacity: 0.5;
        }
    }
}

.addpart-table {
    th {
        padding: 8px 16px !important;
    }
}

.add-part {
    @extend %clearfix;
    background: $lightback;
    width: 420px;
    height: 100%;
    position: fixed;
    top: 0;
    z-index: 12;
    right: 0;
    @include box-shadow(0, 4px, 6px, rgba($black, 0.16), false);

    .add-part-header {
        font-size: 1em;
        padding: 20px $spacing * 1.5;
        border: none;
        background: $textdark;
        color: $white;
        text-align: left;
        height: 60px;
        font-weight: 600;
        width: 100%;

        i.pull-right {
            cursor: pointer;
        }
    }

    .overflowPadding {
        padding-top: 4px;
        padding-bottom: 4px;
    }
}

.add-part-button-group {
    .btn {
        margin: 5px;
    }
}

.viewer-header {
    padding: 0 $spacing;

    h1 {
        font-size: 1em;
        font-weight: 600;
        margin: 8px 0 0 0;
        line-height: 14px;
    }

    p {
        line-height: 16px;
    }

    small {
        color: $textdark;
        margin: 0;
        line-height: 16px;
    }

    .view-preview {
        float: right;
        margin: 11px 370px 0 0;
    }
}

.btn-back {
    float: left;
    margin-top: 12px;
    margin-right: 12px;
    font-size: 1.2em;
    height: 35px;
    width: 35px;
    border: none;
    background: none;
    color: $textdark;
    text-align: left;
    line-height: 35px;
}

.side-menu {
    background: $lightback;
    width: 420px;
    height: calc(100%);
    position: absolute;
    overflow-y: auto;
    top: 0;
    z-index: 10;
    right: 0;
    @include box-shadow(0, 4px, 6px, rgba($black, 0.16), false);

    .input-icon-wrap {
        border-radius: 0;

        i.fa-search {
            top: 4px;
            right: auto;
            left: 0;
        }

        input[type="search"] {
            width: 80%;
            height: 40px;
            margin-left: 25px;
        }

        button {
            background: none;
            border: none;
            width: 40px;
            height: 40px;
            float: right;
        }
    }
}

.soft-side-menu {
    height: 100%;
    border-left: 2px solid $divider-color;
}

.uib-accordion-group {
    i {
        text-align: center;
    }
}

.ivh-treeview-node-content {
    border-top: 1px solid $divider-color;
    font-size: 14px;
    float: left;
    width: 100%;

    .ivh-treeview-checkbox-wrapper {
        border-right: 1px solid $divider-color;
        padding: 2px;
        height: 23px;
    }

    .ivh-other-items {
        border-left: 1px solid $divider-color;
        padding: 2px;
        float: right;
        height: 23px;
        width: 22px;

        .faded {
            color: darken($lightback, 8%);
        }
    }

    .labelrow {
        float: left;
        padding-top: 2px;
        padding-left: 5px;
        height: 23px;
        //width:270px;
        .ivh-treeview-node-label {
            font-size: 12px;
        }

        .ivh-treeview-toggle {
            font-size: 9px;
        }
    }
}

.critical-spare-part {
    font-weight: bold;
    color: green;
}

.openBasket {
    width: 360px;
}

.pnote-header {
    background-color: #f2f2f2;
}

.part-note {
    background-color: #ffffc4;
    overflow-y: auto;
    max-height: 12vh;
}

.btn-model,
.btn-side-menu {
    font-size: 1em;
    padding: $spacing $spacing * 1.5;
    border: none;
    background: $textdark;
    color: $white;
    text-align: left;
    height: 56px;
    font-weight: 600;
    width: 100%;

    i.pull-right {
        margin-top: 3px;
    }
}

.product-thumbnails-carousel-wrap {
    background: $lightback;
    position: absolute;
    @include box-shadow(0, 4px, 6px, rgba($black, 0.16), false);

    &.horizontal {
        width: 100%;
        min-width: $min-width;
        bottom: 0;

        .product-thumbnails-carousel {
            max-width: $max-width;
            height: 210px;
            margin: 0 auto;
            padding: $spacing $spacing * 4;

            &.slick-dotted {
                padding: $spacing $spacing * 4 $spacing * 2;
                height: 240px;
            }

            .product-thumb-cell {
                margin-right: $spacing;
            }
        }
    }

    &.vertical {
        width: 260px;

        &.customer {
            height: calc(100% - 60px);
        }

        &.pdf {
            //height: calc(98%);// 120 = header height + breadcrumb height
        }

        &.manufacturer {
            height: calc(100% - 60px); // 600 = header height
            top: 60px;
        }

        position: absolute;
        //top: 20px;
        left: 0;

        .product-thumbnails-carousel {
            height: calc(100vh - 60px); //this needs checked
            padding: $spacing $spacing 56px;
            overflow: scroll;
            overflow-x: hidden;
            -ms-overflow-style: none;

            /*&::-webkit-scrollbar {
          display: none;
      }*/

            .product-thumb-cell {
                width: 100%;
                margin: $spacing 0 $spacing/2 0;
            }
        }
    }

    .product-thumbnails-carousel {
        @extend %clearfix;
        width: 100%;
        position: relative;

        &.slick-dotted {
            &.slick-slider {
                margin-bottom: 0;
            }
        }

        %selected-product-snap-defaults {
            float: left;
            padding-right: 10px;
            background: $white;
            position: relative;
            margin: $spacing 0 20px 0;
            cursor: pointer;
            font-size: 0.85em;
            font-weight: 600;
        }

        %product-thumb-defaults {
            float: left;
            padding-right: 10px;
            background: $white;
            position: relative;
            margin: $spacing 0 20px 0;
            cursor: pointer;
            font-size: 0.85em;
            font-weight: 600;

            &:after {
                content: "\f107";
                font-family: FontAwesome;
                font-size: 1.1em;
                color: rgba($textdark, 0.5);
                position: absolute;
                bottom: -25px;
                left: 12px;
            }

            img {
                float: left;
                margin-right: 8px;
            }
        }

        .product-thumb-step {
            @extend %product-thumb-defaults;
            width: 100%;
            @include box-shadow(0, 4px, 6px, rgba($black, 0.16), false);

            &:hover {
                color: darken($blue, 20%);
                @include box-shadow(0, 1px, 6px, rgba($blue, 1), false);
            }

            span {
                text-overflow: ellipsis;
            }
        }

        .product-thumb-step-selected {
            @extend %product-thumb-defaults;
            width: calc(100% + 5px);
            border-radius: 0px 30px 30px 0px;
            @include box-shadow(0, 1px, 6px, rgba($blue, 0.9), false);
        }

        .product-snap-step-selected {
            @extend %selected-product-snap-defaults;
            width: calc(100% + 5px);
            border-radius: 0px 30px 30px 0px;
            @include box-shadow(0, 1px, 6px, rgba($blue, 0.9), false);
        }

        .product-thumb-step-selected-last {
            @extend %product-thumb-defaults;
            width: calc(100% + 5px);
            border-radius: 0px 30px 30px 0px;
            @include box-shadow(0, 1px, 6px, rgba($blue, 0.9), false);

            &:after {
                display: none;
            }
        }

        .product-thumb-cell {
            //width        : calc(20% - 16px);
            float: left;
            overflow: hidden;
            @include box-shadow(0, 4px, 6px, rgba($black, 0.16), false);
            position: relative;

            &:focus {
                outline: 0;
            }

            .img-wrap {
                @extend %bg-size;
                background-position: center center;
                background-repeat: no-repeat;
                overflow: hidden;
                position: relative;
                height: 140px;
                cursor: pointer;
            }

            .snapshot-work-instruction {
                @extend %bg-size;
                background-position: center center;
                background-repeat: no-repeat;
                overflow: hidden;
                position: relative;
                height: 56px;
                cursor: pointer;
            }

            .product-info {
                @extend %clearfix;
                padding: 10px;
                background: rgba($white, 0.8);
                position: absolute;
                bottom: 0;
                width: 100%;

                .inner-shadow {
                    width: 20px;
                    height: 100%;
                    position: absolute;
                    top: 0;
                    z-index: 1;

                    &.left {
                        right: 78px;
                        @include box-shadow-spread(10px, 0, 10px, 2px, rgba(255, 255, 255, 1), true);
                    }

                    &.right {
                        right: 8px;
                        @include box-shadow-spread(-10px, 0, 10px, -2px, rgba(255, 255, 255, 1), true);
                    }
                }

                h4 {
                    width: calc(100% - 30px);
                    float: left;
                    font-weight: 700;
                    margin: 0;
                }

                div {
                    display: flow-root;
                }

                .children {
                    float: right;
                    width: 30px;
                    overflow: hidden;
                    height: 20px;
                    position: relative;
                    direction: rtl;
                    -ms-overflow-style: none;

                    &::-webkit-scrollbar {
                        display: none;
                    }

                    ul {
                        @extend %clearlist;
                        white-space: nowrap;
                        height: 20px;

                        li {
                            width: 20px;
                            height: 20px;
                            line-height: 20px;
                            margin-left: 4px;
                            overflow: hidden;
                            display: inline-block;
                            cursor: -webkit-zoom-in;

                            &.view-more {
                                text-align: center;
                                font-size: 0.9em;
                                font-weight: 700;

                                &:hover {
                                    cursor: pointer;
                                }
                            }

                            img {
                                width: 100%;
                                min-width: 100%;
                                min-height: 100%;
                                vertical-align: inherit;
                            }
                        }
                    }
                }
            }
        }
    }
}

.take-snapshot-container {
    margin: 0 0 50px 0 !important;
}

.viewer-notes-opened {
    z-index: 10;
    position: absolute;
    bottom: 20px;
    left: 280px;
    width: 400px;
    background: $blue;
    color: $white;
    border-radius: $border-radius;

    .notes-header {
        padding: $spacing/1.5;

        h4 {
            margin: 0;
        }

        i.fa {
            &:hover {
                cursor: pointer;
            }
        }
    }

    .notes-body {
        padding: 0 $spacing/1.5 $spacing/1.5 $spacing/1.5;
        min-height: 160px;
        white-space: pre-line;
    }
}

.viewer-notes-closed {
    z-index: 10;
    position: absolute;
    bottom: 20px;
    left: 280px;
    background: $blue;
    width: 200px;
    padding: $spacing/1.5;
    color: $white;
    border-radius: $border-radius;

    h4 {
        margin: 0;
    }

    i.fa {
        margin-top: 3px;

        &:hover {
            cursor: pointer;
        }
    }
}

.hyphenate {
    overflow-wrap: break-word;
    word-wrap: break-word;
    -webkit-hyphens: auto;
    -ms-hyphens: auto;
    -moz-hyphens: auto;
    hyphens: auto;
}

.add-part-cell {
    padding: 2px;
}

.option-set-cell {
    padding: 2px;
}

.scrollable {
    height: calc(100% - 120px);
    max-height: calc(100% - 120px);
    width: 100%;
    margin-top: -1px;
    overflow: auto;
}

.add-item {
    @extend %clearlist;
    height: auto;
    width: 100%;
    background-color: #d4e8fe;
    padding: 10px;

    small.label {
        font-size: 0.7em;
        font-weight: 600;
        text-transform: uppercase;
    }

    h2 {
        margin-bottom: 10px;
    }

    h3 {
        margin-bottom: 10px;
    }

    p {
        font-size: 14px;
        margin-bottom: 10px;
    }

    .quantity-box {
        margin-right: 10px;
    }

    .select-box {
        width: 100%;
        height: 38px;
    }
}

.quantity-box {
    height: 30px;
    width: 50px;
}

.stock-level {
    display: inline-block;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
    opacity: 1;
}

/*.tree-container {
 border-bottom: 1px solid $divider-color;
}*/

.expand-model-browser-button {
    height: 56px;
    position: absolute;
    top: 0px;
    z-index: 10;
    right: 0;
}

.minimize-basket-button {
    height: 60px;
    position: absolute;
    top: 0px;
    z-index: 10;
    right: 0;
    width: 100%;

    button {
        border: 0px;
        height: 100%;
    }
}

.settingsTools {
    visibility: hidden;
}

.add-part-scrollable {
    height: calc(100% - 128px);
    overflow: scroll;
    overflow-x: hidden;
}

.add-part-button-group {
    padding: $spacing $spacing;
    background: white;
    border-top: 1px solid $divider-color;
    margin-top: -1px;
}

.add-another-part {
    padding: $spacing $spacing/2;
    float: right;
}

.selected-pdf {
    background: #4696d1;
    color: white;
    padding: 8px;
}

.unselected-pdf {
    background: white;
    color: black;
    padding: 8px;
}

.selected-browser-part {
    background-color: $blue;
    color: white;
}

.blue {
    color: $blue;
}

.grey {
    color: #7a7f8e;
}

.fancy-checkbox input[type="checkbox"],
.fancy-checkbox .checked {
    display: none;
}

.fancy-checkbox input[type="checkbox"]:checked ~ .checked {
    display: inline-block;
}

.fancy-checkbox input[type="checkbox"]:checked ~ .unchecked {
    display: none;
}

.viewer-overlay {
    /*width: 100%;
  height: 100%;
  max-height: 100%;
  max-width: 100%;
  background: #000000!important;
  opacity: 0.1;*/
    background-color: rgba(0, 0, 0, 0.6);
    width: 100vw;
    height: 100vh;
    text-align: center;
    display: table-cell;
    vertical-align: middle;
    position: absolute;
    overflow: hidden;
    padding: 0 !important;

    p {
        color: #ffffff;
    }
}

.btn.snapshot {
    position: fixed;
    z-index: 1;
    left: 0;
    top: $header-height;
    border: none;
    border-radius: 0;
    height: auto;
    width: 260px;
}

.snapshot-container {
    position: fixed;
    z-index: 1;
    left: 0;
    top: 56px;
    border: none;
    border-radius: 0;
    padding: 2px 1px 2px 0px;
    width: 245px;
}

/*.right:after {
  content: '\25BA';
}

.left:after {
  content: '\25C4';
}*/

.right_arrow:after {
    content: "\25BA\2758";
}
.left_arrow:before {
    content: "\2758\25C4";
}

.rotate-tool:before {
    font-family: FontAwesome;
    content: "";
    font-size: 25px;
}

.rotate-tool-active:before {
    font-family: FontAwesome;
    content: "";
    color: white;
    font-size: 25px;
    padding: 4px;
    margin: 0px;
}

.translate-tool:before {
    font-family: FontAwesome;
    content: "\f047";
    font-size: 25px;
}

.translate-tool-active:before {
    font-family: FontAwesome;
    content: "\f047";
    color: white;
    font-size: 25px;
    padding: 4px;
    margin: 0px;
}

.selection-window:before {
    font-family: FontAwesome;
    content: "\f125";
    font-size: 25px;
}

.non-modeled-part-tool:before {
    font-family: FontAwesome;
    content: "\f196";
    font-size: 25px;
}

.reverse-view:before {
    font-family: FontAwesome;
    content: "\f0e2";
    font-size: 25px;
}

.part-link-tool:before {
    font-family: FontAwesome;
    content: "\f0c6";
    font-size: 25px;
}

.explode-tool:before {
    font-family: FontAwesome;
    content: "\f065";
    font-size: 25px;
}

.ghosting-tool:before {
    font-family: FontAwesome;
    content: "\f042";
    font-size: 25px;
}

.adsk-button-icon {
    padding-top: 0px;
    line-height: 0;
}

.adsk-button.active {
    border: 1px solid rgba($blue, 0) !important;
    background: lighten($blue, 32%);
    color: darken($blue, 20%) !important;
}

ul.ivh-treeview {
    list-style-type: none;
    padding-left: 0 !important;
    margin: 0;
    float: left;
    width: 100%;
}

ul.ivh-treeview ul.ivh-treeview {
    padding-left: 0 !important;
}

ul.ivh-treeview .ivh-treeview-toggle {
    cursor: pointer;
}

ul.ivh-treeview .ivh-treeview-node-leaf .ivh-treeview-toggle {
    cursor: auto;
}

.adsk-control.adsk-button.inactive.adsk-icon-explode::before {
    font-size: 25px;
}

.adsk-viewing-viewer * {
    -webkit-box-sizing: content-box !important;
    -moz-box-sizing: content-box !important;
    box-sizing: content-box !important;
    margin-bottom:10px;
}

.adsk-control-group {
    margin: 0 0;
    padding: 2px 2px;
    color: $textdark;
    background-color: $lightback;
    @include box-shadow-spread(0, 4px, 6px, 0, rgba($black, 0.16), false);
}

.adsk-button > .toolbar-vertical-group {
    left: -15px;
}

.adsk-control.adsk-button:hover {
    color: $blue !important;
}

.adsk-button-arrow {
    right: 0px !important;

    .adsk-button-icon:before {
        content: "";
        float: right;
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 0 8px 8px 0;
        border-color: transparent #95989a transparent transparent;
    }
}

.basket-browser {
    @extend %clearfix;
    background: $lightback;
    display: flex;
    flex-direction: column;
    width: 360px;
    height: 100vh;
    position: absolute;
    padding-top: 60px;
    top: 0;
    z-index: 10;
    right: 0;
    @include box-shadow-spread(0, 4px, 6px, 0, rgba($black, 0.16), false);
}

.side-forms {
    .select-box,
    input,
    textarea {
        display: inline-block;
        font-family: $body-font;
        font-size: 1em;
        line-height: 1.1;
        border: 1px solid $divider-color;
        border-radius: $border-radius;
        background: $lightback;
        color: $textdark;
        height: 38px;
    }
}

// .non-modeled-parts {
//     @extend %clearfix;
//     background: $lightback;
//     max-width: 30vw;
//     min-width: 30vw;
//     max-height: 47vh;
//     height: auto;
//     position: absolute;
//     top: 56px;
//     z-index: 10;
//     right: 0;
//     top:0;
//     margin: 0.5rem;
//     @include box-shadow-spread(0, 4px, 6px, 0, rgba($black, 0.16), false);
// }

.width2DViewer {
    width: 400px;
}

// .non-modeled-contents {
//     .basket-box {
//         max-height: calc(100% - 30px);
//         overflow: auto;
//     }

//     h2 {
//         padding: 15px 10px 0px 10px;
//     }
// }

.viewer-basket-only {
    @extend %clearlist;
    width: 100%;
    display: flex;
    height: 20vh;
    flex: 3;
    flex-direction: column;

    .basket-box {
        max-height: calc(100% - 63px);
        overflow: auto;
    }

    h2 {
        padding: 15px 10px 0px 10px;
    }
}

.viewer-basket-plus {
    @extend %clearlist;
    width: 100%;
    display: flex;
    min-height: 20vh;
    flex: 3;
    flex-direction: column;

    .basket-box {
        max-height: 100%;
        overflow: auto;
    }

    h2 {
        padding: 1rem 1rem 0rem 1rem;
    }
}

.place-order-btn {
    margin: 0px 10px;
}

.place-order-btn-margins {
    margin-top: 8vh;
}

.empty-header {
    margin: 15px;
}

.overwrite-button {
    padding-top: 9px;
    width: 35px;
    overflow: hidden;
    height: 35px;
    position: absolute;
    top: 0;
    right: 0;
    text-align: center;
    transform-origin: center center;

    &:hover {
        color: $blue;
        transform: scale(1.2);
        font-size: 1.1em;
    }
}

.edit-snap-button {
    padding-top: 9px;
    width: 35px;
    overflow: hidden;
    height: 35px;
    position: absolute;
    top: 0;
    right: 0;
    text-align: center;
    transform-origin: center center;

    &:hover {
        color: $blue;
        transform: scale(1.2);
        font-size: 1.1em;
    }
}

.delete-snap-button {
    padding-top: 9px;
    width: 35px;
    overflow: hidden;
    height: 35px;
    position: absolute;
    top: 0;
    right: 18px;
    text-align: center;
    transform-origin: center center;

    &:hover {
        color: $blue;
        transform: scale(1.2);
        font-size: 1.1em;
    }
}

.viewer-header {
    padding: 0;
    overflow: hidden;

    .back-to-products {
        .viewer-back-arrow {
            float: left;
            width: 60px;
            height: 60px;
            padding-top: 19px;
            text-align: center;
            font-size: 18px;
        }

        h3 {
            margin-bottom: 0px;
            padding-top: 9px;
            display: inline-block;
            line-height: 1.2;
            /*max-width: calc(100% - 700px);*/

            small {
                font-weight: 400;
            }
        }

        &:hover {
            small {
                text-decoration: underline;
                color: darken($blue, 10%);
            }

            .viewer-back-arrow {
                color: $blue;
            }
        }
    }
}

body {
    .previewButton {
        position: absolute;
        top: 14px;
        right: 180px;
        text-transform: none;
        z-index: 1;
        border: none;
    }
    .previewButtonSideMenuOpen {
        position: absolute;
        top: 14px;
        right: 460px;
        text-transform: none;
        z-index: 1;
        border: none;
    }

    .adsk-toolbar {
        left: 0px;
        bottom: 8vh !important;
        transform: none !important;
        position: fixed !important;
      }
    
}

.spinner {
    display: none !important;
}

.side-menu-accordion,
.side-menu-accordion-2-panel {
    .panel-group {
        height: calc(100% - 56px);
        overflow-y: auto;

        .panel-open {
            height: calc(100% - (38px * 6)); // multiplied by one less than number of items in side menu
            //max-height: calc(100% - (37px * 6)); // multiplied by one less than number of items in side menu
        }

        .panel {
            -webkit-box-shadow: 0;
            box-shadow: 0;
            border-radius: 0;
            padding: 0;

            .panel-heading {
                border-bottom: 1px solid white;
                background-color: $grey;

                &:last-of-type {
                    border: none;
                }

                h4 {
                    padding: 6.3px 10px;
                    margin: 0;
                    font-weight: 400;

                    a {
                        color: black;
                    }
                }
            }
        }
    }
}

.soft-copy-side-menu-accordion-panel {
    .panel-group {
        height: 100%;
        overflow-y: auto;

        .panel-open {
            height: calc(100% - 38px); // multiplied by one less than number of items in side menu
            //max-height: calc(100% - (37px * 6)); // multiplied by one less than number of items in side menu
        }

        .sidebar-content {
            height: calc(100vh - 114px);
            overflow-y: auto;
            overflow-x: hidden;
            float: left;
            width: 100%;
        }

        .panel {
            -webkit-box-shadow: 0;
            box-shadow: 0;
            border-radius: 0;
            padding: 0;

            .panel-heading {
                border-bottom: 1px solid white;
                background-color: $grey;

                &:last-of-type {
                    border: none;
                }

                h4 {
                    padding: 7px 10px;
                    margin: 0;
                    font-weight: 400;

                    a {
                        color: black;
                    }
                }
            }
        }
    }
}

.parts-accordion {
    .panel-group {
        height: calc(100% - 56px);
        overflow-y: auto;
        border-radius: 5px;

        .panel-open {
            height: calc(100% - (38px * 6)); // multiplied by one less than number of items in side menu
            //max-height: calc(100% - (37px * 6)); // multiplied by one less than number of items in side menu
        }

        .panel {
            padding: 0;

            .panel-heading {
                border-bottom: 1px solid white;
                background-color: $grey;

                &:last-of-type {
                    border: none;
                }

                h4 {
                    padding: 7px 10px;
                    margin: 0;
                    font-weight: 400;

                    a {
                        color: black;
                    }
                }
            }
        }
    }
}

.viewer-colour-box {
    border-radius: $border-radius;
}

div.manufacturer-details {
    .badge-pill {
        margin: 4px 0 16px 0;
    }

    p:first-child {
        font-size: 0.8em;
        text-transform: uppercase;
        font-weight: 700;
        margin-bottom: 2px !important;
    }

    p {
        margin-bottom: 14px;
    }

    input {
        width: 100%;
        margin: 2px 0 4px 0;
    }

    .select-box,
    .advancedFilters {
        width: 100%;
        display: flex;
        align-items: center;
    }

    .form .select-box {
        display: flex;
        align-items: center;
    }
}

.company-logo {
    display: block;
    margin-bottom: 8px;
}

.uib-accordion {
    padding: 16px 20px;
    margin-bottom: 10px;
    &:focus {
        //this might not be the fix, added during CORS issue.
        outline: 0 !important;
    }
}

.panel-title {
    a:focus {
        outline: none;
        outline-offset: -2px;
    }
}

.kit-panel {
    padding: 16px;
    margin-bottom: 8px;

    .panel-title {
         margin:0;
        }

    h2{
        font-size: initial;
    }
}

.kit-panel .fa-angle-up, .kit-panel .fa-angle-down {
    font-size: 24px;
    font-weight: bold;
}

.panel-group .kit-panel .card-body p {
    font-size: initial;
}

.arrow-spacing {
    margin: 8px 0 0 32px;
}

.kit-contents {
    height: auto;
}

.kit-contents > div {
    margin-bottom: 10px;
}

.kit-contents > div:last-child {
    margin-bottom: 0;
}

.seperator {
    margin: 0 8px;
}

.create-kit {
    padding-bottom: 7px;
    margin-bottom: 7px;
    background: white;
}

.btn-actions {
    padding: $spacing * 2 0 0 0;
    text-align: right;

    .btn {
        margin-left: 1rem;
    }
}

.sidebar-content {
    height: calc(100vh - 220px - 103px);
    overflow-y: auto;
    overflow-x: hidden;
    float: left;
    width: 100%;
    padding: 1rem;

    .product-table,
    table {
        width: calc(100% + 30px);
        margin-left: -1rem;
    }

    h4 {
        margin-top: $spacing * 2;
    }
}

.tree-container,
.sidebar-content-search-field {
    height: calc(100vh - 220px - 99px - 45px);
    padding: 0;
}

.purchasable-assembly-button {
    padding: 8px;
}

.purchasable-assembly-actions {
    padding: $spacing * 2 0 0 0;
    text-align: right;

    .btn {
        margin-left: 5px;
    }
}

.selected-part-text-with-btn {
    width: 60%;
    float: left;

    h4 {
        margin: 0;
        color: $blue;
    }
}

.panel-body {
    height: auto;
    overflow-y: auto;
    overflow-x: hidden;
    width: 100%;
}

.selected-part-text {
    h4 {
        margin: 0;
        color: $blue;
    }
}

.selected-part-error {
    width: 100%;
    float: left;

    h4 {
        margin: 0;
        color: $blue;
    }
}

.error-text {
    color: $red;
}

.selected-part-button {
    width: 40%;
    float: right;
}

.side-menu-table {
    width: 100%;
    font-family: $body-font;

    td,
    th {
        padding: $spacing;
        font-size: 1em;
        line-height: 1.5;

        &:first-of-type {
            padding-left: $spacing * 2;
            border-top: 1px solid $divider-color;
        }

        &:last-of-type {
            padding-right: $spacing * 3;
            text-align: right;
            border-top: 1px solid $divider-color;
        }
    }

    tbody {
        tr {
        }

        tr:hover {
            .btn.secondary {
                color: $white;
                background: $blue;
                border-color: darken($blue, 5%);

                &:hover {
                    background: darken($blue, 10%);
                }
            }
        }
    }

    td {
        border-bottom: 1px solid $divider-color;
        position: relative;

        h3 {
            margin-bottom: 0;
        }

        h4 {
            margin-bottom: 0;
            font-weight: 400;
        }
    }
}

.accordion-column {
    display: block;
    width: calc(100% - 10px);
    margin-top: 20px !important;
    float: left;
}

.accordion-column:first-of-type {
    margin-top: 5px !important;
}

.side-menu-table-name {
    max-width: 210px;
}

.watermark-parent {
    margin-left: 5px;
    overflow: hidden;
    z-index: 9;
    position: absolute;
    pointer-events: none;
    max-height: calc(100vh - 60px) !important;
}

.watermark {
    float: left;
    padding: 2px;
    width: 14.2%;
    opacity: 0.1;

    p {
        margin: 0;
        color: $white;
        max-width: 125px;
        word-wrap: break-word;
        white-space: normal;
    }

    img {
        height: auto;
        width: 100%;
        margin: 0 auto;
        -webkit-filter: grayscale(100%); /*Safari 6.0 - 9.0 */
        filter: grayscale(100%);
        margin-bottom: 2px;
    }

    div {
        word-break: break-word;
        font-size: 14px;
    }
}

/* Medium Devices, Desktops */
@media only screen and (max-width: 1600px) {
    .watermark {
        p {
            font-size: 12px;
        }
    }
}

.powered-label {
    width: auto;
    height: 28px;
    margin: 10px;
    bottom: 10px;
    left: 10px;
    position: fixed;
    background-color: rgba(255, 255, 255, 0.9);
    color: black;
    border-radius: 10px;
    padding: 0 12px;
    font-size: 0.9em;

    p,
    img {
        display: inline-block !important;
        float: left;
    }

    p {
        vertical-align: middle;
        margin: 0;
        line-height: 28px;
        font-weight: bold;
    }

    img {
        height: 14px;
        margin-left: 4px;
        margin-top: 5px;
    }
}

// .buy-part-overflow {
//     max-height: calc(100% - 131px);
//     overflow: auto;
//     overflow-x: hidden;
//     bottom: 0;

//     button.small {
//         margin: 16px 0 24px 16px;
//     }

//     h2 {
//         position: fixed;
//         z-index: 100;
//         margin-top: -56px;
//     }
// }

.viewerFooterContainer {
    position: fixed;
    bottom: 0;
    width: auto;
    background-color: white;
}

.pdfViewerFooterContainerMax {
    position: fixed;
    bottom: 0;
    left: 260px;
    width: calc(100% - 260px);
    background-color: white;
}

.pdfViewerFooterContainerMin {
    position: absolute;
    bottom: 0;
    width: 100%;
    background-color: white;
    z-index: 10;
    -webkit-box-shadow: 0px 4px 6px 0px rgba(0, 0, 0, 0.06);
    -moz-box-shadow: 0px 4px 6px 0px rgba(0, 0, 0, 0.06);
    box-shadow: 0px 4px 6px 0px rgba(0, 0, 0, 0.06);
}

.manualPartHolder {
    padding: $spacing;
    float: left;
}

.manualBtn {
    vertical-align: middle;
}

//overwrites

.on-behalf-of-shrink {
    height: calc(100vh - 5vh - 56px) !important;
}

//-------- DEV STYLES ---------

.leaders-tool:before {
    font-family: FontAwesome;
    content: "\f176 ";
    font-size: 25px;
}

.leaders-tool-active:before {
    font-family: FontAwesome;
    content: "\f176 ";
    color: blue;
    font-size: 25px;
    padding: 4px;
    margin: 0px;
}

.leaders-type-a:before {
    font-family: FontAwesome;
    content: "\f031";
    font-size: 25px;
}

.leaders-type-b:before {
    font-family: FontAwesome;
    content: "\f0cb";
    font-size: 25px;
}

.softCopyTable {
    z-index: 11;
    position: fixed;
    bottom: 0;
    background-color: white;
    width: calc(100% - 680px);

    a {
        color: $textdark;

        &:hover {
            color: darken($textdark, 10);
        }
    }

    h3 {
        padding: 8px 16px;
        margin: 0;
        border-bottom: 1px solid #ffffff;
        background-color: #d2dae5;
    }
}

.softcopy-table {
    max-height: 210px;
    min-height: 52px;
    width: calc(100vw - 678px);
    overflow-y: auto;
    // display: block;
}

table.softcopy-table {
    max-height: 210px;
    min-height: 36px;
    width: calc(100vw - 678px);
    overflow-y: auto;
    height: auto;
    display: inline-block;
    overflow-y: scroll;
    max-height: 210px;
    position: relative;
    //display: table;
    display: table-caption;

    td,
    th {
        padding: 8px 0px 8px 16px;
        border-bottom: 1px solid #d2dae5;

        &:last-child {
            padding: 8px 16px;
        }
    }
}

.side-menu-accordion-2-panel {
    .panel-group {
        .panel-open {
            height: calc(100% - 38px); // multiplied by one less than number of items in side menu
            //max-height: calc(100% - (37px * 6)); // multiplied by one less than number of items in side menu
        }
    }

    .sidebar-content {
        height: calc(100vh - 132px);
    }

    .tree-container,
    .sidebar-content-search-field {
        height: calc(100vh - 174px);
        padding: 0;
    }
}

.sidebar-actions {
    padding: 0 15px;
}

.work-instruction-snapshot {
    display: inline-block;
    width: 100%;
    margin: $spacing 0 $spacing/2 0;
    background: white;
    padding: 5%;
}

.sharingViewerButton {
    background-size: 24px;
    background-repeat: no-repeat;
    background-position: center;
}

.sharingViewerButtonJoin {
    background-image: url(images/slideshare.png);
}

.sharingViewerButtonExit {
    background-image: url(images/sign-out.png);
}

.snapshot-active {
    -webkit-box-shadow: 0 2px 6px rgba($white, 0.2);
    box-shadow: 0 2px 6px rgba($white, 0.2);
    border: #3392fc 3px solid;
}

.btn-play {
    background: white;
    border: solid 3px #5a5a5a;
}

.snapshot-overflow {
    overflow-y: scroll;
}

.clickable {
    cursor: pointer;
}

.no-access-error-message {
    position: fixed; /* Full-screen absolute positioning */
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center; /* This will center the text vertically */
    justify-content: center; /* This will center the text horizontally */
    background-color: rgba(0, 0, 0, 0.9); /* Semi-transparent black background */
    color: #fff; /* White text */
    z-index: 1000; /* Ensure the message appears above other content */
    overflow: hidden;
}

.error-message-text {
    padding: 20px;
    background-color: #fff;
    color: #000;
    border-radius: 5px;
    text-align: center;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.5); /* Shadow for 3D effect */
}

.disabled-button {
    opacity: 0.5;
    pointer-events: none;
    cursor: not-allowed;
}
