(function () {
  "use strict";

  angular
    .module("app.parts")
    .controller("KitCreateEditController", KitCreateEditController);

  KitCreateEditController.$inject = [
    "$state",
    "manageKitService",
    "$stateParams",
    "$uibModal",
    "masterPartService",
    "userService",
    "headerBannerService",
  ];

  function KitCreateEditController(
    $state,
    manageKitService,
    $stateParams,
    $uibModal,
    masterPartService,
    userService,
    headerBannerService
  ) {
    var vm = this;

    vm.searchResults = [];
    vm.translations = [];
    vm.searchQuery = "";

    vm.partNumberExists;
    vm.partsInKit = false;
    vm.searching = false;
    vm.isEditMode;

    vm.searchParts = searchParts;
    vm.addToKit = addToKit;
    vm.removeFromKit = removeFromKit;
    vm.isPartInKit = isPartInKit;
    vm.assignPartNumber = assignPartNumber;
    vm.editDescription = editDescription;
    vm.editSinglePrice = editSinglePrice;
    vm.editTranslation = editTranslation;
    vm.createTranslation = createTranslation;
    vm.editPriceList = editPriceList;
    vm.getDescription = getDescription;
    vm.createKit = createKit;
    vm.saveKit = saveKit;
    vm.close = close;

    vm.userLanguage;

    init();

    function init() {
      vm.partNumberExists = !!$stateParams.kitId;
      vm.isEditMode = !!$stateParams.kitId;
      if (vm.isEditMode) {
        manageKitService.editKit($stateParams.kitId).then(
          function (response) {
            vm.kit = response.data;
            ensureAllLanguagesPresent();
            vm.userLanguage = localStorage.getItem("NG_TRANSLATE_LANG_KEY") || "EN";
            vm.kit.parts.forEach((part) => {
              part.description = getDescription(part.descriptions, vm.userLanguage);
            });
            vm.partsInKit = vm.kit.parts.length > 0;

            if (vm.kit.price) {
              var cleanPrice = vm.kit.price.toString().replace(/[^\d.-]/g, '');
              vm.kit.price = parseFloat(cleanPrice) || 0;
            }
            if (!vm.kit.partNumber) {
              assignPartNumber();
            }
          },
          function (error) {
            console.error("Error fetching kit: ", error);
          }
        );
      } else {
        ensureAllLanguagesPresent();
        assignPartNumber();
      }
    }

    $(function () {
      $('[data-toggle="tooltip"]').tooltip();
    });

    function ensureAllLanguagesPresent() {
      if (!vm.kit) {
        vm.kit = {
          id: null,
          partNumber: null,
          description: null,
          titles: [],
          prices: null,
          price: null,
          parts: [],
        };
      } else if (!vm.kit.titles) {
        vm.kit.titles = [];
      }
        getTranslations(vm.kit.kitMasterPartId);
      }

    function addToKit(part) {
      var existingPartIndex = vm.kit.parts.findIndex(
        (p) => p.partNumber === part.partNumber
      );
      if (existingPartIndex !== -1) {
        vm.kit.parts[existingPartIndex].quantity++;
      } else {
        var partToAdd = transformPartToKitSchema(part);
        vm.kit.parts.push(partToAdd);
      }

      vm.partsInKit = vm.kit.parts.length > 0;
    }

    function removeFromKit(part) {
      vm.kit.parts = vm.kit.parts.filter(
        (p) => p.partNumber !== part.partNumber
      );
      vm.partsInKit = vm.kit.parts.length > 0;
    }

    function isPartInKit(part) {
      return vm.kit.parts.some(
        (kitPart) => kitPart.partNumber === part.partNumber
      );
    }

    function transformPartToKitSchema(part) {
      return {
        id: null,
        partNumber: part.partNumber,
        masterPartId: part.masterPartId,
        descriptions: createDescriptionsArray(part),
        quantity: 1,
      };
    }

    function createDescriptionsArray(part) {
      return [
        {
          id: null,
          languageCode: "EN",
          languageDisplay: "English",
          translation: part.englishDescription || "",
        },
        {
          id: null,
          languageCode: "FR",
          languageDisplay: "Français",
          translation: part.frenchDescription || "",
        },
        {
          id: null,
          languageCode: "DE",
          languageDisplay: "Deutsch",
          translation: part.germanDescription || "",
        },
        {
          id: null,
          languageCode: "RU",
          languageDisplay: "Россия",
          translation: part.russianDescription || "",
        },
      ];
    }

    function getDescription(descriptions, languageCode) {
      var description = descriptions.find(
        (d) => d.languageCode === languageCode
      );
      return description ? description.translation : "";
    }
    function searchParts() {
      vm.searching = true;
      var manufacturerId = userService.getManufacturerId();
      masterPartService
        .manufacturerPartSearch(manufacturerId, null, vm.searchQuery)
        .then(
          function (res) {
            vm.userLanguage =
              localStorage.getItem("NG_TRANSLATE_LANG_KEY") || "EN";
            vm.searchResults = res.data.masterParts
              .filter(function (part) {
                return part.partNumber !== vm.kit.partNumber;
              })
              .map(function (part) {
                part.descriptions = createDescriptionsArray(part);
                part.filteredDescription = getDescription(
                  part.descriptions,
                  vm.userLanguage
                );
                return part;
              });
          },
          function (error) {
            partSearchFailed(error);
          }
        )
        .finally(function () {
          vm.searching = false;
        });
    }

    function searchSuccessful(res) {
      vm.searchResults = res.data.masterParts.map((part) => {
        vm.userLanguage = localStorage.getItem("NG_TRANSLATE_LANG_KEY") || "EN";
        part.descriptions = createDescriptionsArray(part);
        part.filteredDescription = getDescription(
          part.descriptions,
          vm.userLanguage
        );
        return part;
      });
      console.log("data: ", res);
    }

    function partSearchFailed(error) {
      console.log("partSearchFailed", error);
    }

    function assignPartNumber() {
      var modalInstance = $uibModal.open({
        templateUrl:
          "features/parts/manageKits/assignNumber/assignPartNum.html",
        controller: "assignMPNumberController",
        controllerAs: "assignMPCtrl",
        size: "md",
      });

      modalInstance.result
        .then(function (result) {
          if (result) {
            vm.kit.partNumber = result.partNumber;
            vm.kit.kitMasterPartId = result.kitMasterPartId;
            vm.kit.price = result.price;
            vm.partNumberExists = true;

            vm.kit.prices = result.prices || [];
            getTranslations(vm.kit.kitMasterPartId)
          }
        })
        .catch(function () {
          console.log("Modal dismissed");
          if (!vm.kit.partNumber) {
            console.log("No part number assigned, navigating back to manageKits.");
            $state.go("parts.managekits");
          }
        });
    }

    function editDescription(masterPartDesc) {
      var modalInstance = $uibModal.open({
        templateUrl: "features/parts/manageKits/editCreate/editKitDetail.html",
        controller: "editKitDetailController",
        controllerAs: "editKitDetailCtrl",
        size: "sm",
        resolve: {
          data: function () {
            return {
              masterPartDesc: masterPartDesc,
            };
          },
        },
      });

      modalInstance.result
        .then(function (updatedPartDesc) {
          if (updatedPartDesc) {
            vm.kit.description = updatedPartDesc;
          }
        })
        .catch(function () {
          console.log("Modal dismissed");
        });
    }

    function editSinglePrice() {
      var modalInstance = $uibModal.open({
        templateUrl: "features/parts/manageKits/editCreate/editKitSinglePrice.html",
        controller: "editKitSinglePriceController",
        controllerAs: "editKitSinglePriceCtrl",
        size: "sm",
        resolve: {
          data: function () {
         
            var cleanPrice = vm.kit.price != null ? vm.kit.price.toString().replace(/[^\d.-]/g, '') : '0';
            return {
              masterPartId: vm.kit.kitMasterPartId,
              price: vm.kit.price
            };
          },
        },
      });

      modalInstance.result
        .then(function (updatedPartPrice) {
          if (updatedPartPrice !== null) {
    
            var cleanUpdatedPrice = updatedPartPrice.toString().replace(/[^\d.-]/g, '');
            vm.kit.price = parseFloat(cleanUpdatedPrice);
          }
        })
        .catch(function () {
          console.log("Modal dismissed");
        });
    }

    function getTranslations() {
      if (!vm.kit.kitMasterPartId) {
        console.error("Attempted to get translations without a valid kitMasterPartId");
        return;
      }
      masterPartService
        .getTranslations(vm.kit.kitMasterPartId)
        .then(getTranslationsSuccess)
        .catch(serviceFailed);
      console.log('getTranslations', vm.kit.kitMasterPartId);
    }

    function getTranslationsSuccess(response) {
      vm.translations = response.data;
      var allLanguages = userService.getUserLanguages();
      for (var i = 0; i < allLanguages.length; i++) {
        if (
          _.findIndex(vm.translations, {
            languageId: allLanguages[i].languageId,
          }) < 0
        ) {
          vm.translations.push({
            languageId: allLanguages[i].languageId,
            displayText: allLanguages[i].displayText,
            description: allLanguages[i].description,
          });
        }
      }
    }

    function serviceFailed() {
      console.log("Failed to get translations");
    }

    function manageTranslations(translation, isEdit) {
      var modalInstance = $uibModal.open({
        templateUrl: "features/parts/manageKits/editCreate/editTranslation.html",
        controller: "editTranslationController",
        controllerAs: "editTranslationCtrl",
        resolve: {
          translationObject: function () {
            return {
              isEdit: isEdit,
              masterPartId: vm.kit.kitMasterPartId,
              partNumber: vm.kit.partNumber,
              language: {
                displayText: translation.displayText,
                languageId: translation.languageId,
              },
              description: translation.description
            };
          },
        },
      });

      modalInstance.result.then(getTranslations, function () {
        console.log("Modal dismissed");
      });
    }

    function createTranslation(translation) {
      manageTranslations(translation, false);
    }

    function editTranslation(translation, isEdit) {
      manageTranslations(translation, isEdit);
    }

function editPriceList(currencyIdentifier) {
  var priceList = vm.kit.prices.find(
    (price) => price.currencyIdentifier === currencyIdentifier
  );

  if (priceList) {
    var modalInstance = $uibModal.open({
          templateUrl:
            "features/parts/manageKits/editCreate/editPriceList.html",
      controller: "editPriceListController",
      controllerAs: "editPriceListCtrl",
      size: "sm",
      resolve: {
        data: function () {
          return {
            id: priceList.id,
            currencyIdentifier: priceList.currencyIdentifier,
            price: priceList.price,
          };
        },
      },
    });

    modalInstance.result
      .then(function (updatedData) {
        var index = vm.kit.prices.findIndex(
              (price) =>
                price.currencyIdentifier === updatedData.currencyIdentifier
        );
        if (index !== -1) {
          vm.kit.prices[index].id = updatedData.id;
          vm.kit.prices[index].price = updatedData.price;

          var priceListIndex = vm.priceLists.findIndex(
                (price) =>
                  price.currencyIdentifier === updatedData.currencyIdentifier
          );
          if (priceListIndex !== -1) {
            vm.priceLists[priceListIndex].id = updatedData.id;
            vm.priceLists[priceListIndex].price = updatedData.price;
          } else {
            vm.priceLists.push({
              id: updatedData.id,
              price: updatedData.price,
              currencyIdentifier: updatedData.currencyIdentifier,
            });
          }
        }
      })
      .catch(function () {
        console.log("Modal dismissed");
      });
  }
}

    function createKit() {
      let kitToCreate = {
        kitMasterPartId: vm.kit.kitMasterPartId,
        partNumber: vm.kit.partNumber,
        kitMasterPartDescription: vm.kit.description,
        parts: vm.kit.parts.map((part) => ({
          masterPartId: part.masterPartId,
          quantity: part.quantity,
        })),
      };

      const isMasterPartInParts = vm.kit.parts.some(
        (part) => vm.kit.partNumber === part.partNumber
      );
      if (isMasterPartInParts) {
        var errorMsg = "Master part kit number cannot be included in parts.";
        console.error("Master part kit number cannot be included in parts.");
        headerBannerService.setNotification("ERROR", errorMsg, 10000);
        window.scroll({
          top: 0,
          left: 0,
          behavior: "smooth",
        });
        return;
      }

      var createKitPromise = manageKitService.createKit(kitToCreate);

      createKitPromise.then(
        function (response) {
          console.log(
            "Kit created successfully with ID: ",
            response.data.masterKitId
          );
          $state.go("parts.managekits");
        },
        function (error) {
          console.error("Error creating kit:", error);
        }
      );
    }

    function saveKit() {

      let kitToSave = Object.assign({}, vm.kit, {
        kitMasterPartId: vm.kit.kitMasterPartId,
        partNumber: vm.kit.partNumber,
        kitMasterPartDescription: vm.kit.description,
        masterParts: vm.kit.parts,
        prices: vm.kit.prices.map((price) => ({
          id: price.id,
          price: Number(Number(price.price.replace(/[^\d.-]/g, '')).toFixed(2)),
          currencyIdentifier: price.currencyIdentifier,
        })),
      });

      const isMasterPartInMasterParts = kitToSave.masterParts.some(
        (part) => kitToSave.partNumber === part.partNumber
      );
      if (isMasterPartInMasterParts) {
        var errorMsg = "Master part kit number cannot be included in parts.";
        console.error("Master part kit number cannot be included in parts.");
        headerBannerService.setNotification("ERROR", errorMsg, 10000);
        window.scroll({
          top: 0,
          left: 0,
          behavior: "smooth",
        });
        return;
      }

      delete kitToSave.id;
      delete kitToSave.parts;

      var savePromise = manageKitService.updateKit(kitToSave, vm.kit.id);

      savePromise.then(
        function (response) {
          console.log("Kit Saved ", response.data);
          $state.go("parts.managekits");
        },
        function (error) {
          console.error("Error saving kit:", error);
        }
      );
    }

    function close() {
      $state.go("parts.managekits");
    }
  }
})();
