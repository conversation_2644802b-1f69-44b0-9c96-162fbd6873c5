(function () {
    'use strict';

    angular
        .module('app.viewable')
        .controller('CreateWorkInstructionsController', CreateWorkInstructionsController);

    CreateWorkInstructionsController.$inject = ['workInstructionsService',  '$uibModalInstance','model'];
    function CreateWorkInstructionsController(workInstructionsService, $uibModalInstance, model) {

        var vm = this;
        vm.createWorkInstructions = createWorkInstructions;
        vm.cancel = $uibModalInstance.dismiss;
        vm.model = model;
        vm.workInstructionsName = "";
        vm.workInstructionsName = "";
        vm.submitDisabled = false;

        initialize();

        function initialize() {
        }

        function createWorkInstructions() {
            vm.submitDisabled = true;
            workInstructionsService.createWorkInstructions(vm.model.modelId, vm.workInstructionsName, vm.workInstructionsName)
                .then(createWorkInstructionsSuccess, createWorkInstructionsFailure);
        }

        function createWorkInstructionsSuccess() {
            $uibModalInstance.close();
        }

        function createWorkInstructionsFailure(error) {
            console.log(error);
        }


       }
})();
