(function () {
  'use strict';

  angular
    .module('app.services')
    .factory('shippingEngineService', shippingEngineService);

  shippingEngineService.$inject = ['$http', 'apiConstants', 'ordersService', '$location', '$uibModal', '$state', '$translate'];

  function shippingEngineService($http, apiConstants, ordersService, $location, $uibModal, $state, $translate) {
    var CURRENT_WAREHOUSE = null;

    var splitWarehouse = false;
    var shippingCarrier = null;
    var warehouseList = null;
    var currentOrder = null;

    return {
      setShippingCarrier: setShippingCarrier,
      getShippingRate: getShippingRate,
      getShippingCarrier: getShippingCarrier,
      getCurrentWarehouse: getCurrentWarehouse,
      getWarehouseById: getWarehouseById,
      getSplitWarehouse: getSplitWarehouse,
      showShippingOptions: showShippingOptions,
      splitWarehouseItems: splitWarehouseItems,
      checkCountryRequireTax: checkCountryRequireTax,
      getWarehouseList: getWarehouseList,
      getAllWarehouse: getAllWarehouse,
      getAllWarehouseFailed: getAllWarehouseFailed,
      getAllWarehouseSuccess: getAllWarehouseSuccess,
      getCurrentOrder: getCurrentOrder,
      setCurrentOrder: setCurrentOrder,
    };

    function getCurrentWarehouse() {
      return CURRENT_WAREHOUSE;
    }

    function getWarehouseById(wareHouseId) {
      return warehouseList.length > 0 ? warehouseList.find(wareHouse => wareHouse.id == wareHouseId) : null
    }
    
    function getSplitWarehouse() {
      return splitWarehouse;
    }

    function setShippingCarrier(obj) {
      shippingCarrier = obj;
      if(!obj) return;
      shippingCarrier.totalAmount = getTotalShippingPrice(obj);
    }

    function getShippingCarrier() {
      return shippingCarrier;
    }

    function getCurrentOrder() {
      return currentOrder;
    }

    function setCurrentOrder(obj) {
      currentOrder = obj;
    }

    function getWarehouseList() {
      return warehouseList;
    }

    function getShippingRate(order) {
      if (order && order.basket.length > 0) {
        CURRENT_WAREHOUSE = warehouseList.find(warehouse => warehouse.id == order.basket[0].warehouseId);
      }
      const orderMapped = ordersService.mapOrderToPayload(order) || null;
      if (!orderMapped || !orderMapped.orderItems.length) return;
      
      splitWarehouseItems(orderMapped);

      const payload = {
        order: orderMapped,
        wareHouseId: CURRENT_WAREHOUSE.id
      }

      payload.order.orderItems.forEach(obj => {
        delete obj.warehouseId
      });
      var config = {
        params: { userId: order.userId }
      };
      
      return $http.post(apiConstants.url + '/shipment/rates', payload, config);
    }

    function splitWarehouseItems(orderMapped) {
      splitWarehouse = orderMapped.orderItems.some(item => item.warehouseId !== CURRENT_WAREHOUSE.id);
      orderMapped.orderItems = orderMapped.orderItems.filter(item => {
        return item.warehouseId === CURRENT_WAREHOUSE.id;
      });

      setCurrentOrder(orderMapped.orderItems);

    }

    function getTotalShippingPrice(rate) {
      const shipping = rate.shipping_amount.amount || 0; 
      const insurance = rate.insurance_amount.amount || 0;
      const confirmation = rate.confirmation_amount.amount || 0;
      const other = rate.other_amount.amount || 0;
      return {
        amount: shipping + insurance + confirmation + other,
        currency: rate.shipping_amount.currency
      };
    }

    function showShippingOptions(shippingRate) {
      const modalInstance = $uibModal.open({
        templateUrl: 'features/orders/shippingOptions/shippingOptions.html',
        controller: 'ShippingOptionsController',
        size: 'md',
        controllerAs: 'shipOpsCtrl',
        resolve: {
          shipInfo: shippingRate
        }
      })
      return modalInstance;
    }

    function checkCountryRequireTax(countryCode) {
      if (countryCode === "US" || countryCode === "CA") {
        return true;
      }
      return false;
    }

    function getAllWarehouse(manufacturerId) {
      return $http.get(apiConstants.url + '/manufacturers/' + manufacturerId + '/warehouses')
          .then(getAllWarehouseSuccess, getAllWarehouseFailed);
    }

    function getAllWarehouseFailed(res) {
      return new Promise((resolve, reject) => {
        resolve(res);
    });
    }

    function getAllWarehouseSuccess(res) {
        return new Promise((resolve, reject) => {
          warehouseList = res.data || []
          resolve(warehouseList)
        })
    }
  }
})();
