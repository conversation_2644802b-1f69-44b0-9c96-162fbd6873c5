image: node:10.16.0

stages:
  - build-branch
  - deploy-branch
  - build-dev
  - deploy-dev
  - build-test-env
  - deploy-test-env
  - build-production
  - deploy-production

cache:
  paths:
  - node_modules/
  key: "$CI_JOB_NAME"

build-branch:
  stage: build-branch
  before_script:
    - chmod +x ./before_script.sh
    - ./before_script.sh
  script:
    - npm install
    - npm install -g bower
    - bower install --allow-root
    - npm install gulp-cli -g
    - npm install gulp -D
    - gulp clean-build-dev
  artifacts:
    paths:
    - dev/
  environment:
    name: review/$CI_COMMIT_REF_NAME
  only:
    - branches
  except:
    - master
    - dev
  when: manual

deploy-branch:
  stage: deploy-branch
  image:
    name: mesosphere/aws-cli
    entrypoint:
      - '/usr/bin/env'
      - 'PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'
  script:
    - aws s3 cp dev/ s3://webapp.dev1.cadshare.com/review/$CI_ENVIRONMENT_SLUG/dev --recursive
  environment:
    name: review/$CI_COMMIT_REF_NAME
    url: http://webapp.dev1.cadshare.com.s3-website-eu-west-1.amazonaws.com/review/$CI_ENVIRONMENT_SLUG/dev/index.html
    on_stop: stop-branch
  only:
    - branches
  except:
    - master
    - dev
  when: manual

stop-branch:
  stage: deploy-branch
  image:
    name: mesosphere/aws-cli
    entrypoint:
      - '/usr/bin/env'
      - 'PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'
  variables:
    GIT_STRATEGY: none
  script:
    - aws s3 rm s3://webapp.dev1.cadshare.com/review/$CI_ENVIRONMENT_SLUG/ --recursive
  environment:
    name: review/$CI_COMMIT_REF_NAME
    action: stop
  only:
    - branches
  except:
    - master
  when: manual

build-dev:
  stage: build-dev
  script:
    - npm install
    - npm install -g bower
    - bower install --allow-root
    - npm install gulp-cli -g
    - npm install gulp -D
    - cp constants/dev-constants.js app/features/base/config/app.constants.js
    - gulp clean-build-dev
  artifacts:
    paths:
    - dev/
  only:
    - customer-app
    - master
  when: manual

deploy-dev:
  stage: deploy-dev
  image:
    name: mesosphere/aws-cli
    entrypoint:
      - '/usr/bin/env'
      - 'PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'
  script:
    - aws s3 cp dev/ s3://webapp.dev1.cadshare.com/dev --recursive
  environment:
    name: development
    url: http://webapp.dev1.cadshare.com.s3-website-eu-west-1.amazonaws.com/dev/index.html
  only:
    - customer-app
    - master
  when: manual

build-test:
  stage: build-test-env
  script:
  - npm install
  - npm install -g bower
  - bower install --allow-root
  - npm install gulp-cli -g
  - npm install gulp -D
  - cp constants/test-constants.js app/features/base/config/app.constants.js
  - gulp clean-build-dev
  - gulp clean-build-prod
  artifacts:
    paths:
    - prod/
  when: manual

deploy-test:
  stage: deploy-test-env
  image:
    name: mesosphere/aws-cli
    entrypoint:
    - '/usr/bin/env'
    - 'PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'
  script:
  - aws s3 cp prod/ s3://webapp.test.cadshare.com/ --recursive
  when: manual
  environment:
    name: test
    url: http://webapp.test.cadshare.com.s3-website-eu-west-1.amazonaws.com

build-test2:
  stage: build-test-env
  script:
  - npm install
  - npm install -g bower
  - bower install --allow-root
  - npm install gulp-cli -g
  - npm install gulp -D
  - cp constants/test2-constants.js app/features/base/config/app.constants.js
  - gulp clean-build-prod
  artifacts:
    paths:
    - prod/
  when: manual

deploy-test2:
  stage: deploy-test-env
  image:
    name: mesosphere/aws-cli
    entrypoint:
    - '/usr/bin/env'
    - 'PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'
  script:
  - aws s3 cp prod/ s3://webapp.test2.cadshare.com/ --recursive
  when: manual
  environment:
    name: test-2
    url: http://webapp.test2.cadshare.com.s3-website-eu-west-1.amazonaws.com

build-test3:
  stage: build-test-env
  script:
  - npm install
  - npm install -g bower
  - bower install --allow-root
  - npm install gulp-cli -g
  - npm install gulp -D
  - cp constants/test3-constants.js app/features/base/config/app.constants.js
  - gulp clean-build-prod
  artifacts:
    paths:
    - prod/
  when: manual

deploy-test3:
  stage: deploy-test-env
  image:
    name: mesosphere/aws-cli
    entrypoint:
    - '/usr/bin/env'
    - 'PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'
  script:
  - aws s3 cp prod/ s3://webapp.test3.cadshare.com/ --recursive
  when: manual
  environment:
    name: test-3
    url: http://webapp.test3.cadshare.com.s3-website-eu-west-1.amazonaws.com

build-production:
  stage: build-production
  script:
  - npm install
  - npm install -g bower
  - bower install --allow-root
  - npm install gulp-cli -g
  - npm install gulp -D
  - cp constants/prod-constants.js app/features/base/config/app.constants.js
  - gulp clean-build-prod
  artifacts:
    paths:
    - prod/
  when: manual

deploy-production:
  stage: deploy-production
  image:
    name: mesosphere/aws-cli
    entrypoint:
    - '/usr/bin/env'
    - 'PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'
  script:
  - aws s3 cp prod/ s3://webapp.production.cadshare.com/ --recursive
  when: manual
  environment:
    name: production
    url:  http://webapp.production.cadshare.com.s3-website-eu-west-1.amazonaws.com
