(function () {
    "use strict";

    angular.module("app.viewer").controller("PublishPublicationController", PublishPublicationController);

    PublishPublicationController.$inject = [
        "$uibModalInstance",
        "viewerService",
        "manufacturerId",
        "viewableId"
    ];

    function PublishPublicationController(
        $uibModalInstance,
        viewerService,
        manufacturerId,
        viewableId
    ) {
        var vm = this;

        vm.confirm = confirm;

        vm.cancel = $uibModalInstance.dismiss;

        function confirm() {
            viewerService.createAndPublishPublication(manufacturerId, viewableId)
                .then(function (response) {
                    $uibModalInstance.close();
                })
                .catch(function (error) {
                    alert('Failed to create and publish publication. Please try again.');
                });
        }

    }
})();
