// Variables
$shadow-color: rgba(0, 0, 0, 0.16);
$stock-indicator-size: 24px;

.non-modeled-parts {
  background: $lightback;
  top: 0.5rem;
  right: 0.5rem;
  z-index: 10;
  box-shadow: 0 4px 6px 0 $shadow-color;

  .non-modeled-contents {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
}

.basket-box {
  max-height: 25vh;
  overflow: auto;
  border-bottom: solid 1px lightgrey;

  .table {
    table-layout: auto;
    width: 100%;
    
    // Part Number column (priority width)
    th:nth-child(1),
    td:nth-child(1) {
      width: 45%;
      min-width: 120px;
      white-space: nowrap; // Prevent number wrapping
    }
    
    // Part Description column
    th:nth-child(2),
    td:nth-child(2) {
      width: 55%;
      min-width: 0;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

.stock-indicator,
.stock-value {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: $stock-indicator-size;
}