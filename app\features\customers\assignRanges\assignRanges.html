<section class="m-5">

<div class="mb-5">

    <div>

        <h1 class="">{{'ASSIGN_RANGES.ASSIGNED_FOR' | translate}} {{assignRangesCtrl.customerName}}</h1>
        <p class="mb-0 " translate>ASSIGN_RANGES.DESCRIPTION</p>

    </div>

</div>

<div class="d-flex justify-content-between align-items-center flex-wrap listBoxContainer">

    <div class="col-12 col-md-5 p-5 listBoxLeft d-flex flex-column">

        <h3 class="" translate>ASSIGN_RANGES.AVAILABLE</h3>

        <ul class="list-unstyled">

            <div ng-click="assignRangesCtrl.areAllAvailableSelected" ng-class="assignRangesCtrl.areAllAvailableSelected ? 'assignlistboxHoverCustom' : 'assignlistboxStyleCustom'" class="px-3 px-lg-5">

                <div class="col-12">

                    <div class="d-flex justify-content-between align-items-center text-container">

                        <input title="Select All" type="checkbox" ng-model="assignRangesCtrl.areAllAvailableSelected" ng-click="assignRangesCtrl.toggleSelectAllAvailable()" class="mb-0 mr-5 checkbox">

                        <div class="input-group col mb-0">
                            <input ng-model="assignRangesCtrl.availableSearchValue" type="search"
                                   class="form-control p-3 h-100 mr-0 ng-pristine ng-valid ng-empty ng-touched" name="SearchDualList"
                                   placeholder="{{'ASSIGN_RANGES.SEARCH' | translate}}">
                            <span ng-if="assignRangesCtrl.availableSearchValue != null" ng-click="assignRangesCtrl.clearAvailableSearch()" class="search-clear"><i class="fas fa-times"></i></span>
                            <div class="input-group-append">
                                <span class="input-group-text"><i class="fa fa-search"></i></span>
                            </div>
                        </div>

                    </div>

                </div>

            </div>

            </label>

        </ul>

        <ul class="list-group list-unstyled">

            <div href="" ng-class="range.selected ? 'assignlistboxHover' : 'assignlistboxStyle'" ng-click="range.selected = !range.selected; assignRangesCtrl.updateAllAvailableCheckbox()" class="assignlistboxStyle py-3 px-3 py-lg-4 px-lg-5 mb-3" ng-repeat="range in assignRangesCtrl.availableRanges | filter : assignRangesCtrl.availableSearchValue">

                <div class="d-flex col-12 justify-content-between align-items-center">

                    <div class="d-flex justify-content-between align-items-center text-container">

                        <input type="checkbox" ng-model="range.selected" class="mb-0 mr-5 checkbox">

                        <p class="mb-0"><strong> {{range.name}} </strong></p>

                    </div>
                </div>

            </div>

            </label>

        </ul>

    </div>

    <div class="my-4 my-md-0 col-md-1 col-12 listbox_items d-flex justify-content-center text-center flex-md-column flex-row align-items-center">

        <button title="Move selected item from available to assigned." class="m-2 item btn assignlistboxLeft"
                ng-click="assignRangesCtrl.moveSelectedFromAvailableToAssigned()">
            <i class="fas fa-angle-right"></i>
        </button>

        <button title="Move selected item from assigned to available." class="m-2 item btn assignlistboxRight"
                ng-click="assignRangesCtrl.moveSelectedFromAssignedToAvailable()">
            <i class="fas fa-angle-left"></i>
        </button>

    </div>

    <div class="col-12 col-md-5 listBoxRight p-5 ">

        <h3 class="" translate>ASSIGN_RANGES.ASSIGNED</h3>

        <ul class="list-unstyled">

            <div ng-click="assignRangesCtrl.areAllAssignedSelected" ng-class="assignRangesCtrl.areAllAssignedSelected ? 'assignlistboxHoverCustom' : 'assignlistboxStyleCustom'" class="px-3 px-lg-5">

                <div class="col-12">

                    <div class="d-flex justify-content-between align-items-center text-container">

                        <input title="Select All" type="checkbox" ng-model="assignRangesCtrl.areAllAssignedSelected" ng-click="assignRangesCtrl.toggleSelectAllAssigned()" class="mb-0 mr-5 checkbox">

                        <div class="input-group col mb-0">
                            <input ng-model="assignRangesCtrl.assignedSearchValue" type="search"
                                   class="form-control p-3 h-100 mr-0 ng-pristine ng-valid ng-empty ng-touched" name="SearchDualList"
                                   placeholder="{{'ASSIGN_RANGES.SEARCH' | translate}}">
                            <span ng-if="assignRangesCtrl.assignedSearchValue != null" ng-click="assignRangesCtrl.clearAssignedSearch()" class="search-clear"><i class="fas fa-times"></i></span>
                            <div class="input-group-append">
                                <span class="input-group-text"><i class="fa fa-search"></i></span>
                            </div>
                        </div>

                    </div>

                </div>

            </div>

            </label>

        </ul>

        <ul class="list-group list-unstyled">
            <li ng-class="range.selected ? 'assignlistboxHover' : 'assignlistboxStyle'" ng-click="range.selected = !range.selected; assignRangesCtrl.updateAllAssignedCheckbox()" class="assignlistboxStyle py-3 px-3 py-lg-4 px-lg-5 mb-3"
                ng-repeat="range in assignRangesCtrl.assignedRanges | filter : assignRangesCtrl.assignedSearchValue">


                <div class="d-flex col-12 justify-content-between align-items-center">

                    <div class="d-flex justify-content-between align-items-center text-container">

                        <input type="checkbox" ng-model="range.selected" class="mb-0 mr-5  checkbox">
                        <p class="mb-0"><strong> {{range.name}} </strong></p>

                    </div>

                </div>

            </li>

        </ul>

    </div>

</div>

<div class="flex justify-content-end listboxButtons my-5">

    <button ng-click="assignRangesCtrl.cancel()" class="btn primary px-5" translate>
        ASSIGN_RANGES.RESET
    </button>

    <button ng-class="assignRangesCtrl.isPageEdited ? 'btn primary' : 'btn btn-cancel'" ng-click="assignRangesCtrl.save()" class="btn primary ml-3" translate>
        ASSIGN_RANGES.SAVE_CHANGES
    </button>

</div>

</section>