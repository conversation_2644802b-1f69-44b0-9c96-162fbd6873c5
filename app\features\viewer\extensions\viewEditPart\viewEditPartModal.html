
<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" ng-click="viewEditPartCtrl.cancel()" aria-label="Close"><i class="fa fa-close" aria-hidden="true"></i></button>
    <h2 class="modal-title" translate>VIEW_EDIT_PART.PROPERTIES</h2>
</div>

  <div class="modal-body">
    <form class="form">

        <h4><b translate>
            VIEW_EDIT_PART.PART_NUMBER
        </b></h4>
        <p ng-hide="viewEditPartCtrl.isEditMode">{{viewEditPartCtrl.partNumber}}</p>
        <input type="text" ng-model="viewEditPartCtrl.edits.partNumber" ng-show="viewEditPartCtrl.isEditMode">
        <h4 translate>
            VIEW_EDIT_PART.PART_DESCRIPTION
        </h4>
        <p ng-hide="viewEditPartCtrl.isEditMode">{{viewEditPartCtrl.partDescription}}</p>
        <input type="text" ng-model="viewEditPartCtrl.edits.partDescription" ng-show="viewEditPartCtrl.isEditMode">

    </form>

    <div class="modal-actions">
        <button class="btn small primary" type="button" ng-click="viewEditPartCtrl.enterEditMode()"
                ng-hide="viewEditPartCtrl.isEditMode" translate>
            VIEW_EDIT_PART.EDIT
        </button>
        <button class="btn small secondary" type="button" ng-click="viewEditPartCtrl.cancel()"
                ng-hide="viewEditPartCtrl.isEditMode" translate>
            VIEW_EDIT_PART.CLOSE
        </button>
        <button class="btn small secondary" type="button" ng-click="viewEditPartCtrl.exitEditMode()"
                ng-show="viewEditPartCtrl.isEditMode" translate>
            GENERAL.CANCEL
        </button>
        <button class="btn small primary" type="button" ng-click="viewEditPartCtrl.saveEdits()"
                ng-show="viewEditPartCtrl.isEditMode" translate>
            VIEW_EDIT_PART.SAVE
        </button>
    </div>
  </div>