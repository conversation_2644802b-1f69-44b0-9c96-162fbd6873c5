(function () {
    'use strict';

    angular
        .module('app.products')
        .controller('EditMachineController', EditMachineController);

    EditMachineController.$inject = ['manufacturerMachineService', '$uibModalInstance', 'machine', '$scope', '$uibModal', 'headerBannerService', '$translate'];
    function EditMachineController(manufacturerMachineService, $uibModalInstance, machine, $scope, $uibModal, headerBannerService, $translate) {

        var vm = this;

        vm.cancel = $uibModalInstance.dismiss;
        vm.machineThumbnailUrl = "images/placeholder.jpg";
        vm.isEditMachine = true;
        vm.isCreateMode = true;
        $scope.data = {rangeId: ''};

        vm.loading = false;

        if (machine !== null) {
            vm.isCreateMode = false;
            vm.machineName = machine.name;
            vm.rangeId = machine.rangeId;
            vm.machineId = machine.machineId;
            vm.machineThumbnailUrl = machine.thumbnailUrl || "images/placeholder.jpg";
            $scope.data.rangeId = machine.rangeId;
        }

        vm.createNewRangeField = createNewRangeField;
        vm.cancelRange = cancelRange;
        vm.createNewRange = createNewRange;
        vm.editMachine = editMachine;
        vm.createMachine = createMachine;
        vm.addMachineImage = addMachineImage;
        vm.rangeChanged = rangeChanged;

        $translate(['GENERAL.CREATE', 'GENERAL.EDIT','PRODUCTS_CATALOG.NEW_PRODUCT','PRODUCTS_CATALOG.PRODUCT'])
            .then(function (resp) {
                vm.createModal = resp["GENERAL.CREATE"];
                vm.editModal= resp["GENERAL.EDIT"];
                vm.newProduct = resp["PRODUCTS_CATALOG.NEW_PRODUCT"];
                vm.product = resp["PRODUCTS_CATALOG.PRODUCT"];
            });

        initialize();
        function initialize() {
            getRange();
        }

        function editMachine() {
            vm.loading = true;
            manufacturerMachineService.editMachine(vm.rangeId, vm.machineName, vm.machineId, vm.machineThumbnailUrl)
                .then(editMachineSuccess, editMachineFailure);
        }

        function editMachineSuccess() {
            vm.machineCreated = true;
            vm.loading=false;
            $uibModalInstance.close();
        }

        function editMachineFailure(error) {
            console.log(error);
        }

        function createMachine() {
            vm.loading = true;
            manufacturerMachineService.createMachine(vm.rangeId, vm.machineName, vm.machineThumbnailUrl)
                .then(editMachineSuccess, editMachineFailure);
        }

        function rangeChanged(rangeId) {
            vm.rangeId = rangeId;
        }

        function getRange(value) {
            manufacturerMachineService.getRangeByManufacturer()
                .then(getRangeSuccess, getRangeFailure);
        }

        function getRangeSuccess(response) {
            vm.rangeValues = response.data;
        }

        function getRangeFailure(error) {
            console.log(error);
        }

        function createNewRangeField() {
            vm.isEditMachine = false;
        }

        function cancelRange() {
            vm.isEditMachine = true;
        }

        function createNewRange() {
            vm.rangeFailure = false;

            manufacturerMachineService.createNewRange(vm.newRange)
                .then(createNewRangeSuccess, createNewRangeFailed);
        }

        function createNewRangeSuccess(response) {
            getRange();
            vm.rangeId = response;
            $scope.data = {rangeId: response};
            cancelRange();
        }

        function createNewRangeFailed(error) {
            vm.rangeFailure = true;
            vm.internalFailureMessage = error.data.message;
        }

        function addMachineImage() {
            var cropType = 'MACHINE';
            $uibModal.open({
                templateUrl: 'features/products/imageCropper/imageCropper.html',
                controller: 'ImageCropperController',
                controllerAs: 'imageCropperCtrl',
                size: 'xl',
                backdrop: 'static',
                resolve: {
                    cropType: function () {
                        return cropType;
                    }
                }
            }).result.then(function (response) {
                if (response) {
                    vm.machineThumbnailUrl = response;
                }
            });
        }

    }
})();
