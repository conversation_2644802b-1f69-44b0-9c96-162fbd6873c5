// =============================================================================
// Links
// =============================================================================

a {
    text-decoration: none;
      &:active,
      &:link,
      &:visited {
        color: darken($blue,15%);
        
        &:hover {
            color:  darken($blue,25%);
        }
      }

    &.dark {
        &:active,
        &:link,
        &:visited {
            color: $blue;
        }

        &:hover {
            color: darken($blue,15%);
        }
    }

    &.dark-secondary {
        &:active,
        &:link,
        &:visited {
            color: $textdark;
        }

        &:hover {
            color:  darken($textdark,15%);
        }
    }

  &.delete {
    &:active,
    &:link,
    &:visited {
      color: $textdark;
    }

    &:hover {
      color:  $red
    }
  }

    &.light {
        &:active,
        &:link,
        &:visited {
            color: $blue;
        }

        &:hover {
            color: darken($blue,15%);
        }
    }
}

header {
  a {
  text-decoration: none;
    &:active,
    &:link,
    &:visited {
      color: $textdark;
      
      &:hover {
          color:  darken($textdark,15%);
      }
    }
  }
}
