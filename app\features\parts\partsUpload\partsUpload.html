
<div class="container-fluid">
    <div class="row justify-content-center mt-4">

        <!-- Navigation section starts -->

    <section class="col-md-auto">

        <h1 translate>PARTS_UPLOAD.TITLE</h1>

        <ul class="list-unstyled">
            <li ng-click="partsUploadCtrl.showLanguagesTab()" class="cadBulkHover" ng-class="{active: partsUploadCtrl.activeTab === 'languages'}">
                <button class="link-button" href="javascript:void(0)" translate>PARTS_UPLOAD.DESC_IN_MULTI</button>
            </li>
            <li ng-click="partsUploadCtrl.showInventoryTab()" class="cadBulkHover" ng-class="{active: partsUploadCtrl.activeTab === 'inventory'}" ng-hide="partsUploadCtrl.isPriceListEnabled">
                <button class="link-button" href="javascript:void(0)" translate>PARTS_UPLOAD.INVENTORY_MANG</button>
            </li>
            <li ng-click="partsUploadCtrl.showPriceTab()" class="cadBulkHover" ng-class="{active: partsUploadCtrl.activeTab === 'price'}" translate>
                <button class="link-button" href="javascript:void(0)">PARTS_UPLOAD.PRICE_LIST</button></li>
            <li ng-click="partsUploadCtrl.showConfigurationsTab()" class="cadBulkHover" ng-show="false" ng-class="{active: partsUploadCtrl.activeTab === 'configurations'}" translate>
                <button class="link-button" href="javascript:void(0)">PARTS_UPLOAD.CONFIGURATIONS</button></li>
        </ul>

    </section>

        <!-- Multi-language section starts -->

                <section class="col-md col-12" ng-show="partsUploadCtrl.activeTab === 'languages'">
        <p translate>PARTS_UPLOAD.BY_DEFAULT</p>
        <p translate>PARTS_UPLOAD.MP_INFO</p>

            <div class="bg-white p-4">
                <p translate>PARTS_UPLOAD.ADD_PART_DESC</p>
                <div>
                    <p><span class="font-weight-bold">{{"PARTS_UPLOAD.NOTE" | translate}}</span> {{"PARTS_UPLOAD.NOTE_DESCRIPTION" | translate}}</p>
                </div>

                <div class="upload-box">
                    <div>
                        <i class="fa" ng-class="{'fa-upload': !partsUploadCtrl.languageFilename, 'fa-check-circle text-success': partsUploadCtrl.languageFilename}"></i>
                        <h4 class="file-uploader" ng-if="!partsUploadCtrl.languageFilename" translate>PARTS_UPLOAD.CHOOSE_FILE</h4>
                        <h4 class="file-uploader file-uploader-selected" ng-show="partsUploadCtrl.languageFilename">
                            {{partsUploadCtrl.languageFilename}}
                        </h4>
                        <input type="file" class="fileupload" ng-click="$event = $event"
                               onchange="angular.element(this).scope().partsUploadCtrl.languageFileChanged(event)"
                               accept=".csv"/>
                    </div>
                </div>



                <p class="error-alert" ng-show="partsUploadCtrl.languageErrorMessage !== ''">
                    {{partsUploadCtrl.languageErrorMessage}}</p>
                <p class="success-alert" ng-show="partsUploadCtrl.languageSuccessMessage !== ''">
                    {{partsUploadCtrl.languageSuccessMessage}}</p>

                <div class="mobileBtnFullWidth">
                    <button class="btn btn-primary" ng-disabled="!partsUploadCtrl.languageFilename" ng-click="partsUploadCtrl.uploadLanguages()"
                            ng-hide="partsUploadCtrl.languageUploading" translate>
                        PARTS_UPLOAD.UPLOAD
                    </button>
                    <button class="btn btn-primary" ng-show="partsUploadCtrl.languageUploading">
                        <span class="spinner-border text-light" role="status" aria-hidden="true"></span>
                        {{"PARTS_UPLOAD.UPLOADING" | translate}}
                    </button>

                    <button class="btn primary-outline" ng-click="partsUploadCtrl.exportLanguages()"
                            ng-hide="partsUploadCtrl.languageExporting" translate>
                        PARTS_UPLOAD.EXPORT
                    </button>
                    <button class="btn primary-outline" ng-show="partsUploadCtrl.languageExporting">
                        <span class="spinner-border text-primary" role="status" aria-hidden="true"></span>
                        {{"PARTS_UPLOAD.EXPORTING" | translate}}
                    </button>

                    <button class="btn secondary" ng-click="partsUploadCtrl.downloadLanguageTemplate()"
                            ng-hide="partsUploadCtrl.languageDownloading" translate>
                        PARTS_UPLOAD.DOWNLOAD_TEMPLATE
                    </button>
                    <button class="btn secondary" ng-show="partsUploadCtrl.languageDownloading">
                        <span class="spinner-border text-dark" role="status" aria-hidden="true"></span>
                        {{"PARTS_UPLOAD.DOWNLOADING" | translate}}
                    </button>
                </div>
                <div class="mt-4 pb-2" ng-show="partsUploadCtrl.displayLanguageProgress">
                    <table class="table table-bordered">
                        <thead>
                        <tr>
                            <th>{{"PARTS_UPLOAD.PROCESS" | translate}}</th>
                            <th>{{"PARTS_UPLOAD.TIME" | translate}}</th>
                            <th>{{"PARTS_UPLOAD.STATUS" | translate}}</th>
                            <th>{{"PARTS_UPLOAD.FILE" | translate}}</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr ng-repeat="languageProcess in partsUploadCtrl.languageProcesses">
                            <td data-label="{{'PARTS_UPLOAD.PROCESS' | translate}}">{{languageProcess.processDisplayName}}</td>
                            <td data-label="{{'PARTS_UPLOAD.TIME' | translate}}">{{languageProcess.createdDate | date : "d MMMM y h:mm a"}}</td>
                            <td data-label="{{'PARTS_UPLOAD.STATUS' | translate}}">{{languageProcess.status}}</td>
                            <td>
                                <button class="btn btn-primary" ng-show="languageProcess.status === 'COMPLETE' && languageProcess.process === 'MASTERPART_LANGUAGE_EXPORT'"
                                        ng-click="partsUploadCtrl.downloadCSV(languageProcess, 'language')" translate>
                                    PARTS_UPLOAD.DOWNLOAD
                                </button>
                            </td>
                    </table>
                </div>

            </div>
    </section>

        <!-- Inventory section starts -->

    <section class="col-md col-12" ng-show="partsUploadCtrl.activeTab === 'inventory'">
        <p translate>PARTS_UPLOAD.BY_DEFAULT</p>
        <p translate>PARTS_UPLOAD.MP_INFO</p>

            <div class="bg-white p-4">
                <p translate>PARTS_UPLOAD.UPLOAD_STOCK</p>
                <p translate>PARTS_UPLOAD.PARTS_WITH</p>
                <p translate>PARTS_UPLOAD.MULTI_PRICE_STOCK_INFO</p>

           <div class="upload-box">
                    <div>
                        <i class="fa" ng-class="{'fa-upload': !partsUploadCtrl.inventoryFilename, 'fa-check-circle text-success': partsUploadCtrl.inventoryFilename}"></i>
                        <h4 class="file-uploader" ng-if="!partsUploadCtrl.inventoryFilename" translate>PARTS_UPLOAD.CHOOSE_FILE</h4>
                        <h4 class="file-uploader file-uploader-selected" ng-show="partsUploadCtrl.inventoryFilename">
                            {{partsUploadCtrl.inventoryFilename}}
                        </h4>
                        <input type="file" class="fileupload" ng-click="$event = $event"
                               onchange="angular.element(this).scope().partsUploadCtrl.inventoryFileChanged(event)"
                               accept=".csv"/>
                    </div>
                </div>

                <p class="error-alert" ng-show="partsUploadCtrl.inventoryErrorMessage !== ''">
                    {{partsUploadCtrl.inventoryErrorMessage}}</p>
                <p class="success-alert" ng-show="partsUploadCtrl.inventorySuccessMessage !== ''">
                    {{partsUploadCtrl.inventorySuccessMessage}}</p>

                <div class="mobileBtnFullWidth">
                    <button class="btn btn-primary" ng-disabled="!partsUploadCtrl.inventoryFilename" ng-click="partsUploadCtrl.uploadInventory()"
                            ng-hide="partsUploadCtrl.inventoryUploading"  translate>
                        PARTS_UPLOAD.UPLOAD
                    </button>
                    <button class="btn btn-primary" ng-show="partsUploadCtrl.inventoryUploading">
                        <span class="spinner-border text-light" role="status" aria-hidden="true"></span>
                        {{"PARTS_UPLOAD.UPLOADING" | translate}}
                    </button>

                    <button class="btn primary-outline" ng-click="partsUploadCtrl.exportInventory()"
                            ng-hide="partsUploadCtrl.inventoryExporting" translate>
                        PARTS_UPLOAD.EXPORT
                    </button>
                    <button class="btn primary-outline" ng-show="partsUploadCtrl.inventoryExporting">
                        <span class="spinner-border text-primary" role="status" aria-hidden="true"></span>
                        {{"PARTS_UPLOAD.EXPORTING" | translate}}
                    </button>

                    <button class="btn secondary" ng-click="partsUploadCtrl.downloadInventoryTemplate()"
                            ng-hide="partsUploadCtrl.inventoryDownloading" translate>
                        PARTS_UPLOAD.DOWNLOAD_TEMPLATE
                    </button>
                    <button class="btn secondary" ng-show="partsUploadCtrl.inventoryDownloading">
                        <span class="spinner-border text-dark" role="status" aria-hidden="true"></span>
                        {{"PARTS_UPLOAD.DOWNLOADING" | translate}}
                    </button>
                </div>
                <div class="" ng-show="partsUploadCtrl.displayInventoryProgress">
                    <table class="table table-bordered mt-4">
                        <thead>
                        <tr>
                            <th>{{"PARTS_UPLOAD.PROCESS" | translate}}</th>
                            <th>{{"PARTS_UPLOAD.TIME" | translate}}</th>
                            <th>{{"PARTS_UPLOAD.STATUS" | translate}}</th>
                            <th>{{"PARTS_UPLOAD.FILE" | translate}}</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr ng-repeat="inventoryProcess in partsUploadCtrl.inventoryProcesses">
                            <td data-label="{{'PARTS_UPLOAD.PROCESS' | translate}}">{{inventoryProcess.processDisplayName}}</td>
                            <td data-label="{{'PARTS_UPLOAD.TIME' | translate}}"> {{inventoryProcess.createdDate | date : "d MMMM y h:mm a"}}</td>
                            <td data-label="{{'PARTS_UPLOAD.STATUS' | translate}}">{{inventoryProcess.status}}</td>
                            <td>
                                <button class="btn btn-primary" ng-show="inventoryProcess.status === 'COMPLETE' && inventoryProcess.process === 'MASTERPART_INVENTORY_EXPORT'"
                                        ng-click="partsUploadCtrl.downloadCSV(inventoryProcess, 'inventory')" translate>
                                    PARTS_UPLOAD.DOWNLOAD
                                </button>
                            </td>
                    </table>
                </div>
            </div>
    </section>

        <!-- Parts Upload section starts -->

    <section class="col-md col-12" ng-show="partsUploadCtrl.activeTab === 'price'">
        <p translate>PARTS_UPLOAD.BY_DEFAULT</p>
        <p translate>PARTS_UPLOAD.MP_INFO</p>

        <div class="bg-white p-4">
            <p translate>PARTS_UPLOAD.UPLOAD_PRICE_LIST</p>

            <div class="upload-box">
                <div>
                    <i class="fa" ng-class="{'fa-upload': !partsUploadCtrl.priceListFilename, 'fa-check-circle text-success': partsUploadCtrl.priceListFilename}"></i>
                    <h4 class="file-uploader" ng-if="!partsUploadCtrl.priceListFilename" translate>PARTS_UPLOAD.CHOOSE_FILE</h4>
                    <h4 class="file-uploader file-uploader-selected" ng-show="partsUploadCtrl.priceListFilename">
                        {{partsUploadCtrl.priceListFilename}}
                    </h4>
                    <input type="file" class="fileupload" ng-click="$event = $event"
                           onchange="angular.element(this).scope().partsUploadCtrl.priceListFileChanged(event)"
                           accept=".csv"/>
                </div>
            </div>

            <p class="error-alert" ng-show="partsUploadCtrl.priceListErrorMessage !== ''">
                {{partsUploadCtrl.priceListErrorMessage}}</p>
            <p class="success-alert" ng-show="partsUploadCtrl.priceListSuccessMessage !== ''">
                {{partsUploadCtrl.priceListSuccessMessage}}</p>

            <p ng-if="!partsUploadCtrl.priceListUploadTime">
                {{"PARTS_UPLOAD.NO_DATA_UPLOADED" | translate}}
            </p>

            <p ng-if="partsUploadCtrl.displayPriceListHistory && partsUploadCtrl.priceListUploadTime">
                {{"PARTS_UPLOAD.PRICE_UPDATED" | translate}}
                <span class="font-weight-bold">{{partsUploadCtrl.priceListHistory.firstName}}</span>
                <span class="font-weight-bold">{{partsUploadCtrl.priceListHistory.lastName}}</span>
                {{"PARTS_UPLOAD.ON" | translate}}
                <span class="font-weight-bold">{{partsUploadCtrl.priceListUploadTime | date: "d MMMM y h:mm a"}}</span>
            </p>

            <button class="btn btn-primary" ng-disabled="!partsUploadCtrl.priceListFilename" ng-click="partsUploadCtrl.uploadPriceList()" ng-disabled="partsUploadCtrl.priceListExporting"
                    ng-hide="partsUploadCtrl.priceListUploading"  translate>
                PARTS_UPLOAD.UPLOAD
            </button>
            <button class="btn btn-primary" ng-show="partsUploadCtrl.priceListUploading">
                <span class="spinner-border text-light" role="status" aria-hidden="true"></span>
                {{"PARTS_UPLOAD.UPLOADING" | translate}}
            </button>

            <div class="" ng-show="partsUploadCtrl.displayPriceListProgress">
                <table class="table table-bordered mt-4">
                    <thead>
                    <tr>
                        <th>{{"PARTS_UPLOAD.PROCESS" | translate}}</th>
                        <th>{{"PARTS_UPLOAD.TIME" | translate}}</th>
                        <th>{{"PARTS_UPLOAD.STATUS" | translate}}</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr ng-repeat="priceListProcess in partsUploadCtrl.priceListProcesses">
                        <td data-label="{{'PARTS_UPLOAD.PROCESS' | translate}}">{{priceListProcess.processDisplayName}}</td>
                        <td data-label="{{'PARTS_UPLOAD.TIME' | translate}}"> {{priceListProcess.createdDate | date : "d MMMM y h:mm a"}}</td>
                        <td data-label="{{'PARTS_UPLOAD.STATUS' | translate}}">{{priceListProcess.status}}</td>
                </table>
            </div>
        </div>

    </section>

        <!-- Configurations Section starts -->

        <section class="col-md col-12" ng-show="partsUploadCtrl.activeTab === 'configurations'">
            <p translate>PARTS_UPLOAD.BY_DEFAULT</p>
            <p translate>PARTS_UPLOAD.MP_INFO</p>

            <div class="bg-white p-4">
                <p translate>PARTS_UPLOAD.CONFIGURATIONS</p>

                <div class="upload-box">
                    <div>
                        <i class="fa" ng-class="{'fa-upload': !partsUploadCtrl.configurations, 'fa-check-circle text-success': partsUploadCtrl.configurations}"></i>
                        <h4 class="file-uploader" ng-if="!partsUploadCtrl.configurations" translate>PARTS_UPLOAD.CHOOSE_FILE</h4>
                        <h4 class="file-uploader file-uploader-selected" ng-show="partsUploadCtrl.configurations">
                            {{partsUploadCtrl.configurations}}
                        </h4>
                        <input type="file" class="fileupload" ng-click="$event = $event"
                               onchange="angular.element(this).scope().partsUploadCtrl.configurationsFileChanged(event)"
                               accept=".csv"/>
                    </div>
                </div>

                <button class="btn btn-primary" ng-disabled="!partsUploadCtrl.configurations" ng-click="partsUploadCtrl.uploadPriceList()"
                        ng-hide="partsUploadCtrl.configurationsUploading"  translate>
                    PARTS_UPLOAD.UPLOAD
                </button>
                <button class="btn btn-primary" ng-show="partsUploadCtrl.configurationsUploading">
                    <span class="spinner-border text-light" role="status" aria-hidden="true"></span>
                    {{"PARTS_UPLOAD.UPLOADING" | translate}}
                </button>

            </div>

        </section>

        </div>

    </div>

</div>