(function () {
    'use strict';

    angular
        .module('app.services')
        .factory('priceListService', priceListService);

    priceListService.$inject = ['$http', 'apiConstants', 'userService'];

    function priceListService($http, apiConstants, userService) {
        return {
            getPriceLists: getPriceLists,
            getMasterPartPriceList: getMasterPartPriceList,
            updatePriceList: updatePriceList
        };

        function getPriceLists() {
            var manufacturerId = userService.getManufacturerId();
            return $http.get(apiConstants.url + '/manufacturer/'+ manufacturerId + '/priceListIdentifiers');
        }

        function getMasterPartPriceList(masterPartId) {
            return $http.get(apiConstants.url + '/masterPart/' + masterPartId + '/priceList');
        }

        function updatePriceList(masterPartId, priceListId, newPrice) {
            return $http.put(apiConstants.url + '/masterPart/' + masterPartId + '/priceList/identifier/' + priceListId,  {price: newPrice});
        }
    }
})();
