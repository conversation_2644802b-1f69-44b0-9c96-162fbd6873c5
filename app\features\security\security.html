<section class="body-content small-content">
    <div ng-controller="SecurityController as securityCtrl">
        <h1 translate>SECURITY.TITLE</h1>

        <div class="panel security-panel d-none">
            <h2 translate>SECURITY.MULTI_FACTOR</h2>
            <p translate>SECURITY.MULTI_PARA</p>
            <div class="toggle">
                <input
                    type="checkbox"
                    id="mfa"
                    class="switch-cbx hidden"
                    ng-model="securityCtrl.data.mfaEnabled"
                    ng-change="securityCtrl.toggleMFA()"
                />
                <label for="mfa" class="switch-lbl">
                    <span>{{securityCtrl.data.mfaEnabled ? securityCtrl.ENABLED : securityCtrl.DISABLED}}</span></label
                >
            </div>
        </div>

        <div class="panel security-panel">
            <h2 translate>SECURITY.ALERTS</h2>
            <p translate>SECURITY.ALERTS_PARA</p>
            <p>{{"SECURITY.TO_UNLOCK" | translate}} <a href="mailto:<EMAIL>"><EMAIL></a></p>
            <form>
                <label class="small-label" translate>SECURITY.SELECT_USER</label>
                <select
                    ng-model="securityCtrl.data.ipTrackingEmail"
                    class="select-box-new"
                    ng-change="securityCtrl.ipTrackingEmailChange()"
                >
                    <option ng-hide="true" value="" disabled="" translate>SECURITY.SELECT_USER</option>
                    <option ng-repeat="user in securityCtrl.users | orderBy: 'firstName'" value="{{user.emailAddress}}">
                        {{user.firstName}} {{user.lastName}} - ({{user.emailAddress}})
                    </option>
                </select>
            </form>

            <div class="toggle">
                <input
                    type="checkbox"
                    id="ipTracking"
                    class="switch-cbx hidden"
                    ng-model="securityCtrl.data.ipTrackingEnabled"
                    ng-change="securityCtrl.toggleIPTrack()"
                />
                <label for="ipTracking" class="switch-lbl">
                    <span>{{securityCtrl.data.ipTrackingEnabled ? securityCtrl.ENABLED : securityCtrl.DISABLED}}</span></label
                >
            </div>
        </div>

        <div class="panel security-panel">
            <h2 translate>SECURITY.USER_ACCESS</h2>
            <p translate>SECURITY.USER_ACCESS_PARA</p>
            <form>
                <label class="small-label" translate>SECURITY.SELECT_USER</label>
                <select
                    ng-model="securityCtrl.data.accessReportEmail"
                    class="select-box-new"
                    ng-change="securityCtrl.accessReportEmailChange()"
                    placeholder="Email Address for Alert"
                >
                    <option ng-hide="true" value="" disabled="" translate>SECURITY.SELECT_USER</option>
                    <option ng-repeat="user in securityCtrl.users | orderBy: 'firstName'" value="{{user.emailAddress}}">
                        {{user.firstName}} {{user.lastName}} - ({{user.emailAddress}})
                    </option>
                </select>
            </form>
            <div class="toggle">
                <input
                    type="checkbox"
                    id="accessReport"
                    class="switch-cbx hidden"
                    ng-model="securityCtrl.data.accessReportEnabled"
                    ng-change="securityCtrl.toggleAccessReport()"
                />
                <label for="accessReport" class="switch-lbl">
                    <span>{{securityCtrl.data.accessReportEnabled ? securityCtrl.ENABLED : securityCtrl.DISABLED}}</span></label
                >
            </div>
        </div>

        <div class="panel security-panel">
            <h2 translate>SECURITY.WATERMARK</h2>
            <p translate>SECURITY.WATERMARK_PARA</p>

            <label class="small-label" translate>SECURITY.UPLOAD_PARA</label>
            <div ng-show="securityCtrl.logoResponseUrl != null && securityCtrl.logoResponseUrl != ''" class="watermark-image">
                <img ng-src="{{securityCtrl.logoResponseUrl}}" />
            </div>

            <button type="button" class="btn small primary" ng-click="securityCtrl.addLogoImage()">
                {{"SECURITY.UPLOAD" | translate}}
            </button>

            <div class="toggle">
                <input
                    type="checkbox"
                    id="watermark"
                    class="switch-cbx hidden"
                    ng-model="securityCtrl.watermarkEnabled"
                    ng-change="securityCtrl.toggleWatermark()"
                />
                <label for="watermark" class="switch-lbl">
                    <span>{{securityCtrl.watermarkEnabled ? securityCtrl.ENABLED : securityCtrl.DISABLED}}</span></label
                >
            </div>
        </div>
    </div>
</section>
