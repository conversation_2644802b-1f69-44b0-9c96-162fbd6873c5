<div class="sidebar-content" ng-show="nonModeledPartSummaryCtrl.isOpen">
    <p translate>ADDITIONAL_PARTS_SUMMARY.TITLE_DESC</p>

    <button class="btn primary" ng-click="nonModeledPartSummaryCtrl.createNonModeledPart()" translate>ADDITIONAL_PARTS_SUMMARY.CREATE_NEW</button>

    <h4 translate>ADDITIONAL_PARTS_SUMMARY.MANAGE</h4>

    <table ng-show="nonModeledPartSummaryCtrl.existingNonModeledParts.length > 0" class="tableViewer table-bordered w-100 bg-white ml-0">
        <tbody>
        <tr ng-repeat="nonModeledPart in nonModeledPartSummaryCtrl.existingNonModeledParts">
            <td class="side-menu-table-name">
                {{nonModeledPart.partNumber}}
                <small><strong>{{nonModeledPart.partDescription}}</strong></small>
            </td>
            <td class="has-dropdown">
                <div class="btn-group">
                    <a href="" class="btn xsmall secondary main-action"
                       ng-click="nonModeledPartSummaryCtrl.editNonModeledPart(nonModeledPart)" translate>ADDITIONAL_PARTS_SUMMARY.EDIT</a>
                    <div href="" class="btn xsmall secondary dropdown-toggle" data-toggle="dropdown"
                         aria-haspopup="true" aria-expanded="false">
                        <div class="sub-popup">
                            <ul class="more-options">
                                <li title="Edit">
                                    <a href="" class="dark-secondary"
                                       ng-click="nonModeledPartSummaryCtrl.editNonModeledPart(nonModeledPart)"><i
                                            class="fa fa-fw fa-pencil"></i> {{"ADDITIONAL_PARTS_SUMMARY.EDIT_NON" | translate}}</a>
                                </li>
                                <li title="Delete">
                                    <a href="" class="dark-secondary"
                                       ng-click="nonModeledPartSummaryCtrl.deleteNonModeledPart(nonModeledPart)"><i
                                            class="fa fa-fw fa-trash"></i> {{"ADDITIONAL_PARTS_SUMMARY.DELETE_NON" | translate}}</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </td>
        </tr>
        </tbody>
    </table>

    <p ng-hide="nonModeledPartSummaryCtrl.existingNonModeledParts.length > 0" translate>
        ADDITIONAL_PARTS_SUMMARY.NO_PARTS
    </p>

</div>