<section class="responsiveContainer dpGreenOrders">

<div id="{{dpOrdersListCtrl.isFixedHeader ? 'infiniteScrollFixedHeader' : 'infiniteScrollStaticHeader'}}"
    class="flex p-4 p-md-0">
    <search-filter class="col-12 col-md-3" state-name="'dpOrders'" value="dpOrdersListCtrl.searchValue"
        placeholder-key="ORDERS.SEARCH_BY_ORDER"></search-filter>
</div>

        <table class="table table-bordered tableFixedWidth">
            <thead>
            <tr>
                <th ng-class="dpOrdersListCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'" ng-click="dpOrdersListCtrl.order_item='orderId'; dpOrdersListCtrl.sortReverse = !dpOrdersListCtrl.sortReverse" translate>ORDERS.ORDER_DETAILS</th>
                <th ng-class="dpOrdersListCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'" ng-click="dpOrdersListCtrl.order_item='orderStatus'; dpOrdersListCtrl.sortReverse = !dpOrdersListCtrl.sortReverse" translate>ORDERS.STATUS</th>
                <th ng-class="dpOrdersListCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'" ng-click="dpOrdersListCtrl.order_item='newComments'; dpOrdersListCtrl.sortReverse = !dpOrdersListCtrl.sortReverse" translate>ORDERS.NEW_COMMENTS</th>
                <th translate>GENERAL.ACTIONS</th>
            </tr>
            </thead>

            <tbody class="orders_clickable" infinite-scroll="dpOrdersListCtrl.loadMoreInfiniteScroll()" infinite-scroll-distance="3" infinite-scroll-disabled="dpOrdersListCtrl.loadingInfiniteScrollData">
            <tr ng-class="order.readOrder?'clickableRow' : 'clickableRow unread'"
                ng-repeat="order in dpOrdersListCtrl.orders | orderBy:dpOrdersListCtrl.orderBySettings:dpOrdersListCtrl.sortReverse | filter :dpOrdersListCtrl.searchValue">

                <td data-label="{{'ORDERS.ORDER_DETAILS' | translate}}" ng-click="dpOrdersListCtrl.viewOrder(order.orderId)" class="tableColumn">
                    <h4 class="mb-0"><strong>{{dpOrdersListCtrl.stage.single}}# {{order.customOrderDisplay}}</strong> {{"ORDERS.FOR" | translate}}
                        <strong>{{order.manufacturerSubEntityName}}</strong></h4>
                    <p> {{"ORDERS.CREATED_ON" | translate}}
                        <strong>{{order.createdDate | date: "d MMM y"}}</strong></p>
                    <p>{{"ORDERS.DELIVERY_REQUESTED_ON" | translate}}
                        <strong>{{order.requestedDeliveryDate | date: "d MMM y"}}</strong></p>
                    <p>{{order.numberOfOrderItems}} {{"ORDERS.ITEMS_ORDERED" | translate}}<span ng-if="dpOrdersListCtrl.showPrice">. {{"ORDERS.VALUE" | translate}} <strong>{{order.price | currency:order.currency.symbol:2}}</strong></span></p>
                </td>
                <td data-label="{{'ORDERS.STATUS' | translate}}" ng-click="dpOrdersListCtrl.viewOrder(order.orderId)"><span
                        ng-class="order.readOrder?'primary' : 'success'"
                        class="dpGreenOrders badge-pill">{{order.orderStatusDisplay}}</span>
                    <p>{{"ORDERS.ON" | translate}}
                        {{order.statusUpdatedDate | date: "d MMM y"}}</p>
                </td>

                <td class="customersGreen" data-label="{{'ORDERS.NEW_COMMENTS' | translate}}" ng-click="dpOrdersListCtrl.viewOrder(order.orderId)">

                    <p class="blob_unreadEnquiries blobs-container" ng-if="order.unreadComment" ng-translate>
                        <i class="blob_orders grey_pulse mr-3 fas fa-bell"></i>{{"ORDERS.NEW_COMMENTS" | translate}}</p></td>

                <td class="has-dropdown mobile-right-aligned-btn" ng-hide="dpOrdersListCtrl.hideCancelOrderButton">
                    <div class="btn-group">
                        <a href="" class="btn xsmall secondary main-action"
                           ng-click="dpOrdersListCtrl.viewOrder(order.orderId)">{{"ORDERS.VIEW" | translate}}
                            {{dpOrdersListCtrl.stage.single}}</a>
                        <div href="" class="btn xsmall secondary dropdown-toggle" data-toggle="dropdown"
                             aria-haspopup="true" aria-expanded="false">
                            <div class="sub-popup">
                                <ul class="more-options">
                                    <li title="List Viewables">
                                        <a href="" class="dark-secondary"
                                           ng-click="dpOrdersListCtrl.viewOrder(order.orderId)"><i
                                                class="fa fa-fw fa-eye "></i>{{"ORDERS.VIEW" | translate}}
                                            {{dpOrdersListCtrl.stage.single}}</a>
                                    </li>
                                    <li title="Cancel">
                                        <a href="" class="dark-secondary delete"
                                           ng-click="dpOrdersListCtrl.cancelOrder(order.orderId)"><i
                                                class="fa fa-fw fa-window-close"></i> {{"ORDERS.CANCEL_ORDER" | translate}}
                                            {{dpOrdersListCtrl.stage.single}}</a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </td>
                <td ng-show="dpOrdersListCtrl.hideCancelOrderButton">
                    <button type="button" class="btn secondary" ng-click="dpOrdersListCtrl.viewOrder(order.orderId)">
                        {{"ORDERS.VIEW" | translate}} {{dpOrdersListCtrl.stage.single}}
                    </button>
                </td>

            </tr>

            <tr ng-show="!dpOrdersListCtrl.orders.length > 0 && dpOrdersListCtrl.isOrdersLoaded">
                <td class="emptytable" colspan="6">No {{dpOrdersListCtrl.stage.plural}}</td>
            </tr>

            <tr ng-hide="dpOrdersListCtrl.isOrdersLoaded">
                <td class="preloader" colspan="6"><img ng-src="images/cadpreloader.gif" class="ajax-loader" height="60"
                                                       width="60"/></td>
            </tr>

            </tbody>
        </table>

        <span ng-click="dpOrdersListCtrl.scrollToTop()" id="backToTopBtn" title="Go to top" class="fas fa-arrow-alt-circle-up"
            ng-show="dpOrdersListCtrl.showBackToTopButton"></span>

</section>