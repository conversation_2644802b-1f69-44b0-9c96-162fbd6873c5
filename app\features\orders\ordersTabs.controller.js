(function () {
    "use strict";

    angular.module("app.orders").controller("OrdersTabsController", OrdersTabsController);

    OrdersTabsController.$inject = [
        "$state",
        "$rootScope",
        "ordersService",
        "userService",
        "headerBannerService",
        "$translate",
        "$scope",
        "$location",
    ];

    function OrdersTabsController($state, $rootScope, ordersService, userService, headerBannerService, $translate, $scope, $location) {
        var vm = this;

        vm.enquiryCount = 0;
        vm.quoteCount = 0;
        vm.liveCount = 0;

        vm.isActive = isActive;

        var ENQUIRIES;
        var QUOTATIONS;
        var LIVE_ORDERS;
        var ORDER_HISTORY;

        vm.enquiriesOnly = userService.getEnquiriesOnly();

        $rootScope.$on("Update-Unread-Order-Tabs", refreshCounts);

        initialize();

        function initialize() {
            $translate(["ORDERS_TABS.ENQUIRIES", "ORDERS_TABS.QUOTATIONS", "ORDERS_TABS.LIVE_ORDERS", "ORDERS_TABS.ORDER_HISTORY"]).then(
                function (resp) {
                    ENQUIRIES = resp["ORDERS_TABS.ENQUIRIES"];
                    QUOTATIONS = resp["ORDERS_TABS.QUOTATIONS"];
                    LIVE_ORDERS = resp["ORDERS_TABS.LIVE_ORDERS"];
                    ORDER_HISTORY = resp["ORDERS_TABS.ORDER_HISTORY"];
                    createOrderTabs();
                }
            );

            if ($state.current.url === "/orders") {
                $state.go("orders.enquiries");
            }
        }

        function createOrderTabs() {
            ordersService.getUnreadCounts().then(getUnreadCountSuccess, serviceCallFailed);
        }

        function getUnreadCountSuccess(response) {
            var data = response.data;
            vm.enquiryCount = data.enquiryCount;
            vm.quoteCount = data.quoteCount;
            vm.liveCount = data.liveCount;

            if (vm.enquiriesOnly) {
                vm.tabs = [
                    { title: ENQUIRIES, route: "orders.enquiries", unreadCount: vm.enquiryCount, activeTab: "/enquir" },
                    { title: ORDER_HISTORY, route: "orders.historicalorders", unreadCount: 0, activeTab: "/historicalorder" },
                ];
            } else {
                vm.tabs = [
                    { title: ENQUIRIES, route: "orders.enquiries", unreadCount: vm.enquiryCount, activeTab: "/enquir" },
                    { title: QUOTATIONS, route: "orders.quotations", unreadCount: vm.quoteCount, activeTab: "/quotation" },
                    { title: LIVE_ORDERS, route: "orders.liveorders", unreadCount: vm.liveCount, activeTab: "/liveorder" },
                    { title: ORDER_HISTORY, route: "orders.historicalorders", unreadCount: 0, activeTab: "/historicalorder" },
                ];
            }
        }

        function refreshCounts() {
            ordersService.getUnreadCounts().then(refreshUnreadCountSuccess, serviceCallFailed);
        }

        function refreshUnreadCountSuccess(response) {
            var data = response.data;
            vm.enquiryCount = data.enquiryCount;
            vm.quoteCount = data.quoteCount;
            vm.liveCount = data.liveCount;

            vm.tabs.forEach(function (tab) {
                if (tab.title === ENQUIRIES) {
                    tab.unreadCount = vm.enquiryCount;
                } else if (tab.title === QUOTATIONS) {
                    tab.unreadCount = vm.quoteCount;
                } else if (tab.title === LIVE_ORDERS) {
                    tab.unreadCount = vm.liveCount;
                }
            });
        }

        function serviceCallFailed(error) {
            headerBannerService.setNotification("ERROR", error.data.error, 5000);
        }

        function isActive(route) {
            return $location.path().includes(route);
        }
    }
})();
