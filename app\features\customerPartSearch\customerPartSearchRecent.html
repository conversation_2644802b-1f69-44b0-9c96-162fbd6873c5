<div>
    <h2 class="pb-4 mb-0" translate>CUST_PART_SEARCH.MOST_RECENTLY</h2>
        <div class="responsiveContainer py-0">
            <div class="">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th translate>GENERAL.PART_NUMBER</th>
                            <th ng-class="customerPartSearchCtrl.isPreviewStockLevelEnabled" translate>
                                ORDER.PART_DESC
                            </th>
                            <th ng-if="customerPartSearchCtrl.isPreviewStockLevelEnabled && !customerPartSearchCtrl.isDealerPlusCustomer"
                                translate>
                                ORDER.STOCK
                            </th>
                            <th translate>ORDER.QUANTITY</th>
                            <th ng-if="customerPartSearchCtrl.previewPricingEnabled" translate>
                                ORDER.ITEM_PRICE
                            </th>
                            <th ng-if="customerPartSearchCtrl.previewPricingEnabled" translate>
                                ORDER.TOTAL_PRICE
                            </th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr ng-repeat="recentPart in customerPartSearchCtrl.recentParts"
                            ng-show="customerPartSearchCtrl.recentParts.length > 0">
                            <td data-label="{{'ORDER.PART_NUMBER' | translate}}">
                                {{recentPart.partNumber}}
                            </td>
                            <td data-label="{{'ORDER.PART_DESC' | translate}}">
                                {{recentPart.partDescription}}
                            </td>

                            <td class="disableWordBreak" data-label="{{'ORDER.STOCK' | translate}}"
                                ng-if="customerPartSearchCtrl.isPreviewStockLevelEnabled && !customerPartSearchCtrl.isDealerPlusCustomer">
                                <!-- Show stock number if isStockWarehousesEnabled is true -->
                                <span ng-if="customerPartSearchCtrl.isStockWarehousesEnabled">
                                    <span class="text-success" ng-if="recentPart.stock >= 3">{{ recentPart.stock
                                        }}</span>
                                    <span class="text-warning" ng-if="recentPart.stock < 3 && recentPart.stock > 0">{{
                                        recentPart.stock }}</span>
                                    <span class="text-danger"
                                        ng-if="recentPart.stock === null || recentPart.stock < 1">{{ recentPart.stock ||
                                        '0'
                                        }}</span>
                                </span>
                                <!-- Show icons if isStockWarehousesEnabled is false -->
                                <span ng-if="!customerPartSearchCtrl.isStockWarehousesEnabled">
                                    <span title="{{'ORDER.IN_STOCK' | translate}}" tooltip-trigger="outsideClick"
                                        uib-tooltip="{{'ORDER.IN_STOCK' | translate}}" class="success-alert"
                                        ng-if="recentPart.stock >= 3"><i
                                            class="fas fa-layer-group text-success pointer"></i></span>
                                    <span title="{{'ORDER.LOW_STOCK' | translate}}" tooltip-trigger="outsideClick"
                                        uib-tooltip="{{'ORDER.LOW_STOCK' | translate}}" class="warning-alert"
                                        ng-if="recentPart.stock < 3 && recentPart.stock > 0"><i
                                            class="fas fa-layer-group text-warning pointer"></i></span>
                                    <span title="{{'ORDER.STOCK_SUBJECT' | translate}}" tooltip-trigger="outsideClick"
                                        uib-tooltip="{{'ORDER.STOCK_SUBJECT' | translate}}" class="warning-alert"
                                        ng-if="recentPart.stock === null || recentPart.stock < 1"><i
                                            class="fas fa-layer-group text-danger pointer"></i></span>
                                </span>
                            </td>

                            <td data-label="{{'ORDER.QUANTITY' | translate}}">
                                <input class="priceInput" type="number" min="0" ng-model="recentPart.quantity"
                                    ng-change="customerPartSearchCtrl.recentQuantityUpdated()" />
                            </td>
                            <td class="disableWordBreak" data-label="{{'ORDER.ITEM_PRICE' | translate}}"
                                ng-if="customerPartSearchCtrl.previewPricingEnabled && recentPart.price !== null">
                                {{recentPart.price |
                                currency:customerPartSearchCtrl.defaultCurrency.symbol:2}}
                            </td>
                            <td data-label="{{'ORDER.ITEM_PRICE' | translate}}"
                                ng-if="customerPartSearchCtrl.previewPricingEnabled && recentPart.price === null">
                                TBC
                            </td>
                            <td class="disableWordBreak" data-label="{{'ORDER.TOTAL_PRICE' | translate}}"
                                ng-if="customerPartSearchCtrl.previewPricingEnabled && recentPart.totalPrice !== 0">
                                {{recentPart.totalPrice |
                                currency:customerPartSearchCtrl.defaultCurrency.symbol:2}}
                            </td>
                            <td data-label="{{'ORDER.TOTAL_PRICE' | translate}}"
                                ng-if="customerPartSearchCtrl.previewPricingEnabled && recentPart.totalPrice === 0">
                                TBC
                            </td>
                            <td>
                                <button class="btn primary mr-0" ng-if="customerPartSearchCtrl.hasOrdersAccess"
                                    ng-click="customerPartSearchCtrl.addToBasketRecent($index)">
                                    <div ng-hide="recentPart.clicked">
                                        <i class="fas fa-cart-plus"></i>
                                    </div>
                                    <div ng-show="recentPart.clicked">
                                        <i class="fa fa-check"></i>
                                    </div>
                                </button>
                            </td>
                        </tr>

                        <tr ng-show="!customerPartSearchCtrl.recentParts.length > 0">
                            <td colspan="7" translate>CUST_PART_SEARCH.NO_PARTS_MOST</td>
                        </tr>

                        <tr ng-show="customerPartSearchCtrl.searching" align="center">
                            <td class="preloader text-center" colspan="7">
                                <img ng-src="images/cadpreloader.gif" class="ajax-loader" height="60" width="60" />
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>