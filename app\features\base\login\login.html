<section class="d-flex justify-content-center auth-section" ng-if="loginCtrl.browserIE">
    <div class="auth-box">
        <img alt="CadShareLogo" class="width-90 center-align" ng-src="{{appCtrl.logoUrl}}">
    </div>
</section>

<section>
    <div class="error-well" ng-if="loginCtrl.browserIE">
        <h2 translate>LOGIN.UNSUPPORTED_BROWSER</h2>
        <p translate>LOGIN.UNSUPPORTED_BROWSER_TEXT</p>
        <p translate>LOGIN.UNSUPPORTED_BROWSER_RECOMMENDATION</p>
    </div>
</section>

<section class="d-flex justify-content-center align-items-center flex-column vh-100" ng-hide="loginCtrl.browserIE">

    <div class="d-flex align-items-center flex-column">

        <form ng-submit="loginCtrl.signIn()">

            <div class="sign-panels">
                <img alt="CadShareLogo" class="col-8 col-md-7 center-align" ng-src="{{appCtrl.logoUrl}}">
                <h2 class="text-center font-weight-normal mb-3 mt-8" translate>LOGIN.LOGIN_TITLE</h2>
                <p class="text-center mb-3" translate>LOGIN.LOGIN</p>

                <div ng-if="loginCtrl.isEmailEntry">
                    <p class="text-danger" ng-if="loginCtrl.serverError">
                        {{'LOGIN.SERVER_ERROR' | translate}} <a href="mailto:<EMAIL>" target="_top"><EMAIL></a>
                    </p>

                    <p ng-show="loginCtrl.emailNotRecognisedError" class="text-danger">
                        {{'LOGIN.EMAIL_NOT_RECOGNISED' | translate}} <a href="mailto:{{appCtrl.supportEmail}}">{{appCtrl.supportEmail}}</a>
                    </p>
                    <div class="input-group mb-0">
                        <label class="" translate>LOGIN.EMAIL</label>
                        <input class="m-0" type="text" ng-model="loginCtrl.email"
                               placeholder="{{'LOGIN.ENTER_EMAIL' | translate}}" ng-required/>
                    </div>
                </div>

                <div ng-if="!loginCtrl.isEmailEntry">
                    <p class="text-danger" ng-if="loginCtrl.credentialsError" translate>
                        LOGIN.EMAIL_PASSWORD_INCORRECT
                    </p>

                    <p class="text-danger" ng-if="loginCtrl.accountLocked">
                        {{'LOGIN.ACCOUNT_LOCKED' | translate}} <a href="mailto:<EMAIL>" target="_top"><EMAIL></a>
                    </p>

                </div>

                <div ng-hide="loginCtrl.isEmailEntry">

                    <p class="text-danger" ng-if="loginCtrl.serverError">
                        {{'LOGIN.SERVER_ERROR' | translate}} <a href="mailto:<EMAIL>" target="_top"><EMAIL></a>
                    </p>

                    <p class="mb-0">{{'LOGIN.LOGGING_IN_AS' | translate}} <strong>{{loginCtrl.email}}</strong></p>
                    <p class="">
                        <a href="" ng-click="loginCtrl.backToEmailEntry()">
                            <i class="fas fa-chevron-left"></i>
                            {{'LOGIN.RETURN_TO_EMAIL' | translate}}
                        </a>
                    </p>

                    <div ng-hide="loginCtrl.onAppSubdomain">
                        <!--Start of normal email entry section-->
                        <p class="text-danger" ng-show="loginCtrl.emailPasswordIncorrect">{{'LOGIN.IF_FORGOTTEN' | translate}}
                            <a class="" href="" ui-sref="forgotPassword" >{{'LOGIN.RESET_NOW' | translate}}</a>
                        </p>

                        <div class=" input-group mb-1">
                            <label translate>LOGIN.PASSWORD</label>
                            <input class=" mb-1" type="password" ng-model="loginCtrl.password"
                                   placeholder="{{'LOGIN.ENTER_PASSWORD' | translate}}" ng-required/>
                        </div>

                        <div class=" input-group mb-1">

                            <input class="inp-chBox" id="chBox" type="checkbox" ng-model="loginCtrl.doRememberMe" ng-click="loginCtrl.doRememberMe=true" style="display: none"/>
                            <label class="chBox" for="chBox"><span>
    <svg width="12px" height="10px" viewbox="0 0 12 10">
      <polyline points="1.5 6 4.5 9 10.5 1"></polyline>
    </svg></span><span translate>LOGIN.REMEMBER_ME</span></label>

                        </div>



                        <a href="" ng-click="loginCtrl.goToForgotPassword()" translate>LOGIN.FORGOT_PASSWORD</a>
                    </div><!--End of normal email entry section-->
                    <div ng-if="loginCtrl.onAppSubdomain">
                        <p class="mb-1">LOGIN.CHOOSE_SUBDOMAIN</p>
                        <table class="simple-table">
                            <tbody>
                            <tr ng-repeat="domain in loginCtrl.altDomains">
                                <td width="30%"><img alt="{{domain.subdomain}}" ng-src="{{domain.logoUrl}}"></td>
                                <td width="40%">{{domain.name}}</td>
                                <td width="30%">
                                    <button class="btn-signin"
                                            ng-click="loginCtrl.redirectToSubDomain(domain.subdomain)" translate>LOGIN.LOGIN
                                    </button>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>

                </div>

                <button class="btn-signin mt-3 width-100"
                        ng-hide="loginCtrl.isCDECustomer || loginCtrl.onAppSubdomain" type="submit" translate>
                    LOGIN.SIGN_IN
                </button>
                <div class="text-center grey-text mt-2" ng-show="appCtrl.userRegistrationEnabled && loginCtrl.isEmailEntry">
                    {{'LOGIN.DONT_HAVE_ACCOUNT' | translate}}
                   <a href="" ng-click="loginCtrl.goToRegistrationForm()">{{'LOGIN.REGISTER_HERE' | translate}}</a>
                </div>
            </div>

        </form>

    <div class="d-flex">

    <h5 class="text-center mr-3 font-weight-normal grey-text" translate>LOGIN.TROUBLE</h5>
    <h5 class="text-center grey-text"><a href="mailto:{{appCtrl.supportEmail}}">{{'LOGIN.CONTACT' | translate}} {{appCtrl.supportEmail}}</a></h5>

    </div>

    </div>

</section>
