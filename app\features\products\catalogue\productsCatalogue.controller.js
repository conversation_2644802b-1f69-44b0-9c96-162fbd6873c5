(function () {
    'use strict';

    angular
        .module('app.products')
        .controller('ProductsCatalogueController', ProductsCatalogueController);

    ProductsCatalogueController.$inject = ['manufacturerProductService', '$uibModal', '$state', 'uploadModelService', 'headerBannerService', '$translate', '$window', '$transitions', '$scope'];
    function ProductsCatalogueController(manufacturerProductService, $uibModal, $state, uploadModelService, headerBannerService, $translate, $window, $transitions, $scope) {
        var vm = this;
        var filterRangeKey = 'filterRange';

        var dropdownStateKey = 'dropdownState';

        vm.sortReverse = false;
        vm.isProductsLoaded = false;

        vm.loadingInfiniteScrollData = false;
        vm.showBackToTopButton = false;
        vm.isFixedHeader = false;

        vm.displayAdvancedFilter = false;
        vm.filter_range = $window.localStorage.getItem(filterRangeKey) || '';
        vm.ranges = [];

        vm.sortReverse = true;
        vm.viewable_sort = 'id';

        var MACHINE_SUCCESS, EDIT_SUCCESS, WENT_WRONG;
        $translate(['PRODUCTS_CATALOG.MACHINE_SUCCESS', 'PRODUCTS_CATALOG.EDIT_SUCCESS', 'GENERAL.WENT_WRONG'])
            .then(function (resp) {
                MACHINE_SUCCESS = resp["PRODUCTS_CATALOG.MACHINE_SUCCESS"];
                EDIT_SUCCESS = resp["PRODUCTS_CATALOG.EDIT_SUCCESS"];
                WENT_WRONG = resp["GENERAL.WENT_WRONG"];
            });

        vm.createProductPopUp = createProductPopUp;
        vm.openViewablePage = openViewablePage;
        vm.openDeletePopup = openDeletePopup;
        vm.editProductPopup = editProductPopup;

        vm.toggleAdvancedFilter = toggleAdvancedFilter;
        vm.applyFilter = applyFilter;
        vm.clearFilter = clearFilter;

        vm.fileChange = fileChange;
        vm.uploadModal = uploadModal;
        vm.handleFilterChange = handleFilterChange;

        vm.scrollToTop = scrollToTop;
        vm.loadMoreInfiniteScroll = loadMoreInfiniteScroll;

        var originalProductList = [];

        initialize();

        function initialize() {
            var savedFilters = JSON.parse(localStorage.getItem('productFilters') || '{}');
            vm.filter_range = savedFilters.filter_range || '';
            
            var savedDropdownState = localStorage.getItem(dropdownStateKey);
            
            if (savedDropdownState === 'open' || vm.filter_range) {
                vm.displayAdvancedFilter = true;
            } else {
                vm.displayAdvancedFilter = false;
            }
            
            getProductsByManufacturer();
        
            $transitions.onStart({}, (transition) => {
                var fromStateUrl = transition.from().url.toString();
                var toStateUrl = transition.to().url.toString();
                var productRoutes = [
                    '/products/',
                    '/productsCatalogue',
                    '/techDocs',
                    '/videos',
                    '/manufacturerViewer/',
                    '/adminViewer/'
                ];
        
                var isFromProduct = productRoutes.some(route => fromStateUrl.includes(route));
                var isToProduct = productRoutes.some(route => toStateUrl.includes(route));
        
                if (isFromProduct && !isToProduct) {
                    vm.searchValue = '';
                }
            });
        }

        function fileChange(file, type, productId) {
            vm.isFileSelected = true;
            vm.file = file[0];
            vm.assemblys = [];
            if (type == 'assembly') {
                uploadModelService.getZipFolderContents(vm.file, function (response) {
                    vm.assemblys = response;
                    uploadModelPopup(type, productId);
                });
            } else {
                uploadModelPopup(type, productId);
            }
        }

        function getProductsByManufacturer() {
            vm.loadingInfiniteScrollData = true;
            manufacturerProductService.getProductsByManufacturer()
                .then(getProductsSuccess)
                .catch(getProductsFailed);
        }
        
    function getProductsSuccess(response) {
      // Handle the new response format with products in a nested array
      vm.allProducts = response.data.products || response.data;
      vm.productList = vm.allProducts.slice(0, 100);
      vm.totalItems = vm.allProducts.length;
      vm.loadingInfiniteScrollData = false;
      vm.isProductsLoaded = true;
      handleInfiniteScroll();

      originalProductList = vm.productList;

      for (var n = 0; n < vm.totalItems; n++) {
        // Use range property instead of rangeName if it exists
        var range = vm.allProducts[n].range || vm.allProducts[n].rangeName;
        if (range && !vm.ranges.includes(range)) {
          vm.ranges.push(range);
        }
      }

      // Apply filter after the product list is populated
      if (vm.filter_range) {
        applyFilter();
      }
    }

    function getProductsFailed(error) {
      console.log(error);
      vm.isProductsLoaded = false;
      headerBannerService.setNotification("ERROR", WENT_WRONG, 5000);
    }

    function fileChange(file, type, productId) {
      vm.isFileSelected = true;
      vm.file = file[0];
      vm.assemblys = [];
      if (type == "assembly") {
        uploadModelService.getZipFolderContents(vm.file, function (response) {
          vm.assemblys = response;
          uploadModelPopup(type, productId);
        });
      } else {
        uploadModelPopup(type, productId);
      }
    }

        function toggleAdvancedFilter() {
            vm.displayAdvancedFilter = !vm.displayAdvancedFilter;
            
            if (!vm.displayAdvancedFilter) {
                clearClosedFilter();
            }

            $window.localStorage.setItem(dropdownStateKey, vm.displayAdvancedFilter ? 'open' : 'closed');
        }

        function applyFilter() {
            console.log("Filtering by " + vm.filter_range);
        
            // Start with the original unfiltered list
            vm.productList = originalProductList.slice();
        
            if (vm.filter_range) {
                var filteredProducts = [];
                for (var n = 0; n < originalProductList.length; n++) {
                    var range = originalProductList[n].range || originalProductList[n].rangeName;
                    if (vm.filter_range === range) {
                        filteredProducts.push(originalProductList[n]);
                    }
                }
        
                // Overwrite the product list with the filtered results
                vm.productList = filteredProducts;
                vm.totalItems = filteredProducts.length;
                vm.isFixedHeader = false;
            }
            
            // Save filter to localStorage directly here
            var filters = {
                filter_range: vm.filter_range || ''
            };
            localStorage.setItem('productFilters', JSON.stringify(filters));
        }

        function clearFilter () {
            vm.productList = originalProductList;
            vm.totalItems = vm.productList.length;

            vm.filter_range = null;
            vm.searchValue = "";

            vm.displayAdvancedFilter = false;

            localStorage.removeItem('productFilters');
            $window.localStorage.setItem(dropdownStateKey, 'closed');
            $window.sessionStorage.removeItem("searchValue-'products'");

            console.log("Filter and dropdown state cleared");
        }


        function clearClosedFilter () {
            vm.productList = originalProductList;
            vm.totalItems = vm.productList.length;

            vm.filter_range = null;

            vm.displayAdvancedFilter = false;

            localStorage.removeItem('productFilters');
            $window.localStorage.setItem(dropdownStateKey, 'closed');
            $window.sessionStorage.removeItem("searchValue-'products'");
        }


        function createProductPopUp() {
            headerBannerService.removeNotification();
            $uibModal.open({
                templateUrl: 'features/products/create/editProduct.html',
                controller: 'EditProductController',
                controllerAs: 'editProductCtrl',
                size: 'md',
                resolve: {
                    product: function () {
                        return null;
                    }
                }
            }).result.then(function () {
                headerBannerService.setNotification('SUCCESS', MACHINE_SUCCESS, 10000 );
                getProductsByManufacturer();
            }, function () {
                console.log('Modal Cancelled');
            });
        }

        function handleFilterChange() {
            $window.localStorage.setItem(filterRangeKey, vm.filter_range);
            applyFilter();
        }

        function openViewablePage(product) {
            $state.go("productsModels", {
                productId: product.id,
                machineName: product.name
            });
        }

        function openDeletePopup(product) {
            headerBannerService.removeNotification();
            var deleteObject = {
                name: product.name,
                id: product.id || product.productId,
                url: '/machine/' + (product.id || product.productId)
            };

            $uibModal.open({
                templateUrl: 'features/shared/commonDelete/deleteDialogBox.html',
                controller: 'DeleteController',
                controllerAs: 'deleteCtrl',
                size: 'sm',
                resolve: {
                    deleteObject: function () {
                        return deleteObject;
                    }
                }
            }).result.then(function () {
                getProductsByManufacturer();
            }, function () {
                console.log('Modal Cancelled');
            });
        }

        function editProductPopup(product) {
            headerBannerService.removeNotification();
            $uibModal.open({
                keyboard: false,
                templateUrl: 'features/products/create/editProduct.html',
                controller: 'EditProductController',
                controllerAs: 'editProductCtrl',
                size: 'md',
                resolve: {
                    product: function () {
                        return product;
                    }
                }
            }).result.then(function () {
                headerBannerService.setNotification('SUCCESS', EDIT_SUCCESS, 10000 );
                getProductsByManufacturer();
            }, function () {
                console.log('Modal Cancelled');
            });
        }

        function uploadModal(product) {
                var productId = product.productId;
                $uibModal.open({
                    templateUrl: 'features/viewable/uploadViewable/uploadViewable.html',
                    controller: 'UploadViewableController',
                    controllerAs: 'uploadCtrl',
                    size: 'md',
                    resolve: {
                        productId: function () {
                            return productId;
                        }
                    }
                }).result.then(function () {
                    openViewablePage(product);
                }, function () {
                    console.log('Modal Cancelled');
                });
        }

        var lastScrollTop = 0;
        window.addEventListener('scroll', handleInfiniteScroll);

        function handleInfiniteScroll() {
            var threshold = 250;
            var scrollTop = window.scrollY;

            if (scrollTop > lastScrollTop) {
                vm.isFixedHeader = scrollTop > threshold;
            } else if (scrollTop < threshold) {
                vm.isFixedHeader = false;
            }
            lastScrollTop = scrollTop;


            if (window.innerHeight + scrollTop >= document.body.offsetHeight - 2 && !vm.loadingInfiniteScrollData && !vm.initialLoadDone) {
                loadMoreInfiniteScroll();
            }
        }

        function loadMoreInfiniteScroll() {
            vm.loadingInfiniteScrollData = true;

            var nextItems = vm.allProducts.slice(vm.productList.length, vm.productList.length + 100);
            if (nextItems.length === 0) {
                vm.initialLoadDone = true;
                vm.loadingInfiniteScrollData = false;
                return;
            }

            vm.productList = vm.productList.concat(nextItems);

            vm.loadingInfiniteScrollData = false;
            if (vm.productList.length >= vm.totalItems) {
                vm.initialLoadDone = true;
            }
        }

  function scrollToTop() {
      $window.scrollTo({ top: 0, behavior: "smooth" });
      $("html, body").animate({ scrollTop: 0 }, "slow", function () {
        $("#scrollToTop").removeClass("scrolled-past");
      });
    }

    angular.element($window).on("scroll", function () {
      vm.showBackToTopButton = this.pageYOffset > 100;
      $scope.$apply();
    });

        vm.actions = [
            {
                title: "List Viewables",
                onClick: function (entity) { vm.openViewablePage(entity); },
                icon: "fa-list-ul",
                label: function () { return $translate.instant('PRODUCTS_CATALOG.LIST_VIEWABLES'); }
            },
            {
                title: "Upload",
                onClick: function (entity) { vm.uploadModal(entity); },
                icon: "fa-upload",
                label: function () { return $translate.instant('PRODUCTS_CATALOG.UPLOAD'); }
            },
            {
                title: "Edit",
                onClick: function (entity) { vm.editProductPopup(entity); },
                icon: "fa-pencil",
                label: function () { return $translate.instant('PRODUCTS_CATALOG.EDIT'); }
            },
            {
                title: "Delete",
                onClick: function (entity) { vm.openDeletePopup(entity); },
                icon: "fa-trash",
                label: function () { return $translate.instant('PRODUCTS_CATALOG.DELETE'); }
            }
        ];



    }
})();


