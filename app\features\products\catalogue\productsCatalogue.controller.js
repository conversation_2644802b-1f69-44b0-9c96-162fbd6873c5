(function () {
    'use strict';

    angular
        .module('app.products')
        .controller('ProductsCatalogueController', ProductsCatalogueController);

    ProductsCatalogueController.$inject = ['manufacturerProductService', '$uibModal', '$state', 'uploadModelService', 'headerBannerService', '$translate', '$window', '$transitions', '$scope'];
    function ProductsCatalogueController(manufacturerProductService, $uibModal, $state, uploadModelService, headerBannerService, $translate, $window, $transitions, $scope) {
        var vm = this;
        var filterRangeKey = 'filterRange';

        var dropdownStateKey = 'dropdownState';

        vm.sortReverse = false;
        vm.isMachinesLoaded = false;

        vm.loadingInfiniteScrollData = false;
        vm.showBackToTopButton = false;
        vm.isFixedHeader = false;

        vm.displayAdvancedFilter = false;
        vm.filter_range = $window.localStorage.getItem(filterRangeKey) || '';
        vm.ranges = [];

        vm.sortReverse = true;
        vm.viewable_sort = 'machineId';
        var MACHINE_SUCCESS, EDIT_SUCCESS;
        $translate(['PRODUCTS_CATALOG.MACHINE_SUCCESS', 'PRODUCTS_CATALOG.EDIT_SUCCESS'])
            .then(function (resp) {
                MACHINE_SUCCESS = resp["PRODUCTS_CATALOG.MACHINE_SUCCESS"];
                EDIT_SUCCESS = resp["PRODUCTS_CATALOG.EDIT_SUCCESS"];
            });


        vm.createMachinePopUp = createMachinePopUp;
        vm.openViewablePage = openViewablePage;
        vm.openDeletePopup = openDeletePopup;
        vm.editMachinePopup = editMachinePopup;

        vm.toggleAdvancedFilter = toggleAdvancedFilter;
        vm.applyFilter = applyFilter;
        vm.clearFilter = clearFilter;

        vm.fileChange = fileChange;
        vm.uploadModal = uploadModal;
        vm.handleFilterChange = handleFilterChange;

        vm.scrollToTop = scrollToTop;
        vm.loadMoreInfiniteScroll = loadMoreInfiniteScroll;

        var originalMachineList = [];

        initialize();

        function initialize() {
            var savedFilters = JSON.parse(localStorage.getItem('productFilters') || '{}');
            vm.filter_range = savedFilters.filter_range || '';
            
            var savedDropdownState = localStorage.getItem(dropdownStateKey);
            
            if (savedDropdownState === 'open' || vm.filter_range) {
                vm.displayAdvancedFilter = true;
            } else {
                vm.displayAdvancedFilter = false;
            }
            
            fetchMachines();
        
            $transitions.onStart({}, (transition) => {
                var fromStateUrl = transition.from().url.toString();
                var toStateUrl = transition.to().url.toString();
                var productRoutes = [
                    '/products/',
                    '/productsCatalogue',
                    '/techDocs',
                    '/videos',
                    '/manufacturerViewer/',
                    '/adminViewer/'
                ];
        
                var isFromProduct = productRoutes.some(route => fromStateUrl.includes(route));
                var isToProduct = productRoutes.some(route => toStateUrl.includes(route));
        
                if (isFromProduct && !isToProduct) {
                    vm.searchValue = '';
                }
            });
        }

        function fileChange(file, type, machineId) {
            vm.isFileSelected = true;
            vm.file = file[0];
            vm.assemblys = [];
            if (type == 'assembly') {
                uploadModelService.getZipFolderContents(vm.file, function (response) {
                    vm.assemblys = response;
                    uploadModelPopup(type, machineId);
                });
            } else {
                uploadModelPopup(type, machineId);
            }
        }

        function fetchMachines() {
            vm.loadingInfiniteScrollData = true;
            manufacturerProductService.fetchMachines()
                .then(fetchMachinesSuccess)
                .catch(fetchMachinesFailed);
        }
        
    function fetchMachinesSuccess(response) {
      vm.allProducts = response.data;
      vm.machineList = vm.allProducts.slice(0, 100);
      vm.totalItems = vm.allProducts.length;
      vm.loadingInfiniteScrollData = false;
      vm.isMachinesLoaded = true;
      handleInfiniteScroll();

      originalMachineList = vm.machineList;

      for (var n = 0; n < vm.totalItems; n++) {
        var range = vm.allProducts[n].rangeName;
        if (!vm.ranges.includes(range)) {
          vm.ranges.push(range);
        }
      }

      // Apply filter after the machine list is populated
      if (vm.filter_range) {
        applyFilter();
      }
    }

    function fetchMachinesFailed(error) {
      vm.isMachinesLoaded = false;
      headerBannerService.setNotification("ERROR", error.data.error, 10000);
    }

    function fileChange(file, type, machineId) {
      vm.isFileSelected = true;
      vm.file = file[0];
      vm.assemblys = [];
      if (type == "assembly") {
        uploadModelService.getZipFolderContents(vm.file, function (response) {
          vm.assemblys = response;
          uploadModelPopup(type, machineId);
        });
      } else {
        uploadModelPopup(type, machineId);
      }
    }

        function toggleAdvancedFilter() {
            vm.displayAdvancedFilter = !vm.displayAdvancedFilter;
            
            if (!vm.displayAdvancedFilter) {
                clearClosedFilter();
            }

            $window.localStorage.setItem(dropdownStateKey, vm.displayAdvancedFilter ? 'open' : 'closed');
        }

        function applyFilter() {
            console.log("Filtering by " + vm.filter_range);
        
            // Start with the original unfiltered list
            vm.machineList = originalMachineList.slice();
        
            if (vm.filter_range) {
                var filteredMachines = [];
                for (var n = 0; n < originalMachineList.length; n++) {
                    var range = originalMachineList[n].rangeName;
                    if (vm.filter_range === range) {
                        filteredMachines.push(originalMachineList[n]);
                    }
                }
        
                // Overwrite the machine list with the filtered results
                vm.machineList = filteredMachines;
                vm.totalItems = filteredMachines.length;
                vm.isFixedHeader = false;
            }
            
            // Save filter to localStorage directly here
            var filters = {
                filter_range: vm.filter_range || ''
            };
            localStorage.setItem('productFilters', JSON.stringify(filters));
        }

        function clearFilter () {
            vm.machineList = originalMachineList;
            vm.totalItems = vm.machineList.length;

            vm.filter_range = null;
            vm.searchValue = "";

            vm.displayAdvancedFilter = false;

            localStorage.removeItem('productFilters');
            $window.localStorage.setItem(dropdownStateKey, 'closed');
            $window.sessionStorage.removeItem("searchValue-'products'");

            console.log("Filter and dropdown state cleared");
        }


        function clearClosedFilter () {
            vm.machineList = originalMachineList;
            vm.totalItems = vm.machineList.length;

            vm.filter_range = null;

            vm.displayAdvancedFilter = false;

            localStorage.removeItem('productFilters');
            $window.localStorage.setItem(dropdownStateKey, 'closed');
            $window.sessionStorage.removeItem("searchValue-'products'");
        }


        function createMachinePopUp() {
            headerBannerService.removeNotification();
            $uibModal.open({
                templateUrl: 'features/products/create/editMachine.html',
                controller: 'EditMachineController',
                controllerAs: 'editMachineCtrl',
                size: 'md',
                resolve: {
                    machine: function () {
                        return null;
                    }
                }
            }).result.then(function () {
                headerBannerService.setNotification('SUCCESS', MACHINE_SUCCESS, 10000 );
                fetchMachines();
            }, function () {
                console.log('Modal Cancelled');
            });
        }

        function handleFilterChange() {
            $window.localStorage.setItem(filterRangeKey, vm.filter_range);
            applyFilter();
        }

        function openViewablePage(machine) {
            $state.go("productsModels", {
                productId: machine.machineId,
                machineName: machine.name
            });
        }

        function openDeletePopup(machine) {
            headerBannerService.removeNotification();
            var deleteObject = {
                name: machine.name,
                id: machine.machineId,
                url: '/machine/' + machine.machineId
            };

            $uibModal.open({
                templateUrl: 'features/shared/commonDelete/deleteDialogBox.html',
                controller: 'DeleteController',
                controllerAs: 'deleteCtrl',
                size: 'sm',
                resolve: {
                    deleteObject: function () {
                        return deleteObject;
                    }
                }
            }).result.then(function () {
                fetchMachines();
            }, function () {
                console.log('Modal Cancelled');
            });
        }

        function editMachinePopup(machine) {
            headerBannerService.removeNotification();
            $uibModal.open({
                keyboard: false,
                templateUrl: 'features/products/create/editMachine.html',
                controller: 'EditMachineController',
                controllerAs: 'editMachineCtrl',
                size: 'md',
                resolve: {
                    machine: function () {
                        return machine;
                    }
                }
            }).result.then(function () {
                headerBannerService.setNotification('SUCCESS', EDIT_SUCCESS, 10000 );
                fetchMachines();
            }, function () {
                console.log('Modal Cancelled');
            });
        }

        function uploadModal(machine) {
                var machineId = machine.machineId;
                $uibModal.open({
                    templateUrl: 'features/viewable/uploadViewable/uploadViewable.html',
                    controller: 'UploadViewableController',
                    controllerAs: 'uploadCtrl',
                    size: 'md',
                    resolve: {
                        machineId: function () {
                            return machineId;
                        }
                    }
                }).result.then(function () {
                    openViewablePage(machine);
                }, function () {
                    console.log('Modal Cancelled');
                });
        }

       var lastScrollTop = 0;
window.addEventListener('scroll', handleInfiniteScroll);

function handleInfiniteScroll() {
    var threshold = 250;
    var scrollTop = window.scrollY;

    if (scrollTop > lastScrollTop) {
        vm.isFixedHeader = scrollTop > threshold;
    } else if (scrollTop < threshold){
        vm.isFixedHeader = false;
    }
    lastScrollTop = scrollTop;  

    
    if (window.innerHeight + scrollTop >= document.body.offsetHeight - 2 && !vm.loadingInfiniteScrollData && !vm.initialLoadDone) {
        loadMoreInfiniteScroll();
    }
}

        function loadMoreInfiniteScroll() {
            vm.loadingInfiniteScrollData = true;

            var nextItems = vm.allProducts.slice(vm.machineList.length, vm.machineList.length + 100);
            if (nextItems.length === 0) {
                vm.initialLoadDone = true;
                vm.loadingInfiniteScrollData = false;
                return;
            }

            vm.machineList = vm.machineList.concat(nextItems);

            vm.loadingInfiniteScrollData = false;
            if (vm.machineList.length >= vm.totalItems) {
                vm.initialLoadDone = true;
            }
        }

  function scrollToTop() {
      $window.scrollTo({ top: 0, behavior: "smooth" });
      $("html, body").animate({ scrollTop: 0 }, "slow", function () {
        $("#scrollToTop").removeClass("scrolled-past");
      });
    }

    angular.element($window).on("scroll", function () {
      vm.showBackToTopButton = this.pageYOffset > 100;
      $scope.$apply();
    });

        vm.actions = [
            {
                title: "List Viewables",
                onClick: function (entity) { vm.openViewablePage(entity); },
                icon: "fa-list-ul",
                label: function () { return $translate.instant('PRODUCTS_CATALOG.LIST_VIEWABLES'); }
            },
            {
                title: "Upload",
                onClick: function (entity) { vm.uploadModal(entity); },
                icon: "fa-upload",
                label: function () { return $translate.instant('PRODUCTS_CATALOG.UPLOAD'); }
            },
            {
                title: "Edit",
                onClick: function (entity) { vm.editMachinePopup(entity); },
                icon: "fa-pencil",
                label: function () { return $translate.instant('PRODUCTS_CATALOG.EDIT'); }
            },
            {
                title: "Delete",
                onClick: function (entity) { vm.openDeletePopup(entity); },
                icon: "fa-trash",
                label: function () { return $translate.instant('PRODUCTS_CATALOG.DELETE'); }
            }
        ];



    }
})();
