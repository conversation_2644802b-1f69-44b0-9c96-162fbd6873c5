/* Allows text to wrap */

.cadWrap {
  overflow-wrap: break-word;
}

.dropdown-menu {
  max-height: 200px; /* Adjust the height as needed */
  overflow-y: auto;
}

.disableButton {
  color: #444;
  pointer-events: none;

  .fa-comments:before,
  .fa-file-pdf:before,
  .fa-sticky-note:before {
    color: grey;
  }

  &.warning,
  &.comments,
  &.partnotes {
    border-color: grey !important;
    opacity: 0.2;
  }
}

.disabledElement {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* 2D Viewer Input Width " */

.inputWidth2D {
  min-width: 60px;
}

/* Upload Error Icon Colour */

.upload-box .fa-times {
  color: #dc3545;
}

/* Hide but reserve space */

.hidden-but-space {
  visibility: hidden;
}

/* Flexbox */

.flex-start {
  justify-content: start !important;
}

.cad-dropdown {
  display: flex;
  align-items: center;
  position: relative;
  border-radius: 0.25em;
  margin-top: 0.5em;
  margin-bottom: 0.5em;

  .cad-dropdown-text {
    position: absolute;
    cursor: pointer;
    margin-left: 0.5em;
    margin-bottom: 0 !important;
    pointer-events: none;
  }

  .cad-dropdown-inner {
    margin: 0;
    background-color: white;
    width: 100%;
    border: 1px solid #d2dae5;
    height: 40px;
    padding-left: 8px;
    padding-right: 30px;
    -webkit-border-radius: 2px;
    border-radius: 5px;
    background-clip: padding-box;
    cursor: pointer;
    transition: ease-in-out 0.2s;
    box-shadow: 0 1px 0.5px rgba(0, 0, 0, 0.1);
    appearance: none;
    background-image: url("data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%23131313%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.2-5.5-12.8z%22%2F%3E%3C%2Fsvg%3E");
    background-repeat: no-repeat;
    background-position: right 1rem top 50%;
    background-size: 1rem auto;
  }

  .cad-dropdown-inner:hover {
    background-color: #f1f1f1;
  }

  .cad-dropdown-inner:active {
    border: 1px solid #1a85fc;
  }

  .cad-dropdown-inner:focus {
    outline: none;
  } /* Removes border */
}

/* Global Cursors */

.cursorPointer {
  cursor: pointer;
}

input {
  cursor: text;
}

input[type="checkbox"] {
  cursor: pointer;
}

button:disabled {
  cursor: not-allowed !important;
}

.pointer {
  cursor: pointer;
}

/* Icon Sizing */

.fa-cart-plus,
.fa-check {
  font-size: 1.5em !important;
  padding: 0.2em;
}

/* Disabled Input */

input[type="text"]:disabled {
  background-color: #eee;
  cursor: not-allowed;
}

/* Warehouse Stock Pill Icon */

.pill-badge {
  display: inline-block;
  padding: 0.25em 0.4em;
  font-size: 70%;
  font-weight: 700;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25rem;
  min-width: 3em;
  margin-left: auto;
}

.badge-success {
  color: #fff;
  background-color: #28a745;
}

.badge-warning {
  color: #212529;
  background-color: #ffc107;
}

.badge-danger {
  color: #fff;
  background-color: #dc3545;
}

/* Filter Panel */

.gloBl-filter-panel {
  display: inline-flex;
  width: 100%;
  padding: 0em 1em;
  background: white;

  .filter-option {
    select {
      height: 100%;
    }

    .selectActive {
      color: #1a85fc;
    }
  }
  .filter-buttons {
    margin-top: $spacing * 2;

    .btn:first-child {
      margin-right: $spacing;
    }
  }

  .input-icon-wrap {
    display: flex;
    align-items: center;

    i {
      pointer-events: none;
    }
  }
}

/* Disabled Select Box Styling */

.disabled-select {
  cursor: not-allowed;
  opacity: 0.5;
}

/* Select Box Styling */

.gloBl-filter-panel small,
.select-box small {
  z-index: 1;
  user-select: none;
  position: absolute;
  opacity: 0.75;
  font-size: 1em;
  margin: 0;
  pointer-events: none;
  top: 50%;
  transform: translateY(-50%);
  left: 1rem;
}

/* Select Box Styling */

.gloBl-filter-panel small,
.select-box small {
  z-index: 1;
  user-select: none;
  position: absolute;
  opacity: 0.75;
  font-size: 1em;
  margin: 0;
  pointer-events: none;
  top: 50%;
  transform: translateY(-50%);
  left: 1rem;
}

/* Select Box Styling */

.custom_select {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  outline: 0;
  font: inherit;
  background: url(data:image/svg+xml;base64,PHN2ZyBoZWlnaHQ9JzMwMHB4JyB3aWR0aD0nMzAwcHgnICBmaWxsPSIjMDAwMDAwIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCIgdmVyc2lvbj0iMS4xIiB4PSIwcHgiIHk9IjBweCI+PHRpdGxlPkV4dHJhIEV4dHJhIEJvbGQgQ2hldnJvbiBEb3duPC90aXRsZT48ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz48ZyBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48ZyBmaWxsPSIjMDAwMDAwIj48cGF0aCBkPSJNNjcuNTI1MTI2Myw2Mi40NzQ4NzM3IEM2OC44OTE5NjEzLDYzLjg0MTcwODggNzEuMTA4MDM4Nyw2My44NDE3MDg4IDcyLjQ3NDg3MzcsNjIuNDc0ODczNyBDNzMuODQxNzA4OCw2MS4xMDgwMzg3IDczLjg0MTcwODgsNTguODkxOTYxMyA3Mi40NzQ4NzM3LDU3LjUyNTEyNjMgTDUyLjQ3NDg3MzcsMzcuNTI1MTI2MyBDNTEuMTA4MDM4NywzNi4xNTgyOTEyIDQ4Ljg5MTk2MTMsMzYuMTU4MjkxMiA0Ny41MjUxMjYzLDM3LjUyNTEyNjMgTDI3LjUyNTEyNjMsNTcuNTI1MTI2MyBDMjYuMTU4MjkxMiw1OC44OTE5NjEzIDI2LjE1ODI5MTIsNjEuMTA4MDM4NyAyNy41MjUxMjYzLDYyLjQ3NDg3MzcgQzI4Ljg5MTk2MTMsNjMuODQxNzA4OCAzMS4xMDgwMzg3LDYzLjg0MTcwODggMzIuNDc0ODczNyw2Mi40NzQ4NzM3IEw1MC4wMDk1NzIyLDQ0Ljk0MDE3NTIgTDY3LjUyNTEyNjMsNjIuNDc0ODczNyBaIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSg1MC4wMDAwMDAsIDUwLjAwMDAwMCkgcm90YXRlKDE4MC4wMDAwMDApIHRyYW5zbGF0ZSgtNTAuMDAwMDAwLCAtNTAuMDAwMDAwKSAiPjwvcGF0aD48L2c+PC9nPjwvc3ZnPg==)
      no-repeat right 0.8em center/1.4em,
    linear-gradient(to left, #f2f6f9 3em, #f2f6f9 3em);
  border-radius: 0.25em;
  border: 1px solid #d2dae5;
  cursor: pointer;
  margin: 0 !important;
  padding: 10px 0px 10px 16px !important;
  height: 100%;
}

.custom_select_alt {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  outline: 0;
  font: inherit;
  height: 3em;
  background: url(data:image/svg+xml;base64,PHN2ZyBoZWlnaHQ9JzMwMHB4JyB3aWR0aD0nMzAwcHgnICBmaWxsPSIjMDAwMDAwIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB2ZXJzaW9uPSIxLjEiIHg9IjBweCIgeT0iMHB4IiB2aWV3Qm94PSIwIDAgMTAwIDEwMCIgc3R5bGU9ImVuYWJsZS1iYWNrZ3JvdW5kOm5ldyAwIDAgMTAwIDEwMDsiIHhtbDpzcGFjZT0icHJlc2VydmUiPjxnPjxwb2x5Z29uIHBvaW50cz0iNDkuOSwyMS45IDcyLjUsNDQuNiA3OC45LDM4LjIgNDkuOCw5LjIgMjEuMSwzOC4zIDI3LjUsNDQuNiAgIj48L3BvbHlnb24+PHBvbHlnb24gcG9pbnRzPSI3OC45LDYxLjUgNzIuNSw1NS4yIDUwLjEsNzcuOSAyNy41LDU1LjIgMjEuMSw2MS42IDUwLjIsOTAuNiAgIj48L3BvbHlnb24+PC9nPjwvc3ZnPg==)
      no-repeat right 0.8em center/1.4em,
    linear-gradient(to left, #f2f6f9 3em, #f2f6f9 3em);
  border-radius: 0.25em;
  border: 1px solid #d2dae5;
  cursor: pointer;
  margin: 0 !important;
  padding: 10px 0px 10px 16px !important;
  height: 100%;
}

.custom_select option,
.custom_select_alt option {
  color: inherit;
  background-color: #ffffff;
}
.custom_select:focus,
.custom_select_alt:focus {
  outline: none;
}
.custom_select::-ms-expand,
.custom_select_alt::-ms-expand {
  display: none;
}

.btn.btn-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  padding: 6px 0px;
  text-align: center;
  font-size: inherit;
  line-height: 1.42857;
  justify-content: center;
  align-items: center;
}

/* Responsive Table Styling */

.responsiveContainer {
  border-radius: 5px;
  background: white;
}

.table,
.tableViewer {
  text-align: center;
  margin-bottom: 0;

  td,
  th {
    padding: 0.5rem;
  }
}

.hoverTableBG {
  cursor: pointer;
  transition: 0.2s ease-in-out;
}

.hoverTableBG:hover {
  background-color: #f5f5f5;
}

.borderLeft {
  border-left: 3px solid blue;
}

/* Changes Table Layout to have equal width for each column. */

.tableFixedWidth {
  table-layout: fixed !important;
}

/* Aligns content in first column to the left */

.table td:first-child,
.table th:first-child,
.tableViewer td:first-child,
.tableViewer th:first-child {
  text-align: left;
}

.table td,
.tableViewer td {
  word-break: break-word;
}

.table td button,
.tableViewer td button {
  word-break: keep-all;
}

/* Information Icon */

.fa-info-circle {
  font-size: 1.25em !important;
  cursor: pointer;
}

/* Disables Word Wrapping in Table */

.table,
.tableViewer {
  .disableWordBreak {
    word-break: keep-all;
  }
}

.truncatePartNumber {
  flex-grow: 1;
  margin-left: 5px;
  width: 10vw;
}

.accordion-anim {
  transition: max-height 0.5s ease-in-out, opacity 0.5s ease-in-out;
  overflow: hidden;
  max-height: 0;
  opacity: 0;
}

.accordion-anim.open {
  max-height: 100%;
  opacity: 1;
  width: 100%;
}

.table td input {
  max-width: 15vw;
  width: 15vw;
}

.blueTableBG {
  background-color: #f2f8ff;
  font-size: 15px;
}

.hoverTableBG {
  cursor: pointer;
  transition: 0.2s ease-in-out;
}

.hoverTableBG:hover {
  background-color: #f5f5f5;
}

.borderLeft {
  border-left: 3px solid blue;
}

.table td select,
.tableViewer td select {
  min-width: 7vw;
}

/* Clickable Icon */

.clickable-icon {
  cursor: pointer;
  transition: background-color 0.3s, color 0.3s;
}

.icon-circle {
  background-color: #3392fc;
  color: white;
  border-radius: 50%;
  padding: 5px;
  display: inline-block;
  font-size: 0.9em !important;
}

.clickable-icon:hover {
  background-color: #0056b3;
}

/* Applies background color to every even row. */

.table tr:nth-child(even) {
  background-image: linear-gradient(
    to right,
    rgba(245, 245, 245, 0.6),
    rgba(245, 245, 245, 0.6)
  );
}

/* Updates background colour for table if order is unread */

.table_contents tr.unread {
  font-weight: 700;
  background: #eefbed;
}

tr.unread {
  font-weight: 700;
  background: $lightgreen;
  &:hover {
    background: darken($lightgreen, 5%);
  }
}

.is-invalid {
  border: 1px solid red !important;
}

/* Adds pointer cursor to clickable rows in table */

.clickableRow {
  cursor: pointer;
}

.clickableRow:hover {
  cursor: pointer;
  background: darken($lightback, 5%);
}

// Large devices (desktops, 992px and up)
@media (min-width: 992px) {
  .table td input {
    width: 8vw;
  }

  .table,
  .tableViewer {
    max-width: 100% !important;

    thead {
      background: #f2f2f2;
    }

    th,
    td {
      vertical-align: middle !important;
    }
  }

  .table-smaller-font {
    th {
      font-size: 0.8em;
    }
  }
}

/* Mobile Responsive Table */

@media only screen and (max-width: 992px) {
  .table:not(.table-no-responsive) {
    max-width: 100% !important;
    table-layout: auto;

    table,
    thead,
    tbody,
    th,
    td,
    tr {
      display: block;
    }
    thead tr,
    tfoot tr {
      position: absolute;
      top: -9999px;
      left: -9999px;
    }
    td {
      border: none;
      border-bottom: 1px solid #eee;
      position: relative;
    }

    thead {
      background: #f2f2f2;
    }

    th,
    td {
      vertical-align: middle !important;
    }
  }

  .dataTables_length {
    display: none;
  }

  .table:not(.table-no-responsive) td {
    border-bottom: 1px solid #ddd;
    font-size: 0.8em;
    text-align: left;
    display: flex;
    justify-content: space-between;
    padding: 1rem;
    word-break: break-word;
    flex-wrap: wrap;
  }

  .table:not(.table-no-responsive) td::before {
    content: attr(data-label);
    float: left;
    font-weight: bold;
    text-transform: uppercase;
    padding-right: 0.5em;
  }

  .table:not(.table-no-responsive) td:last-child {
    border-bottom: 0;
  }

  .inputButtonWidth {
    width: 15.25em;
  }

  .input-icon-wrap .input-icon {
    padding: 0.5em 0.5em;
  }
  .table:not(.table-no-responsive) .tableColumn:first-child {
    display: grid;
  }
}

#backToTopBtn {
  position: fixed;
  bottom: 10px;
  right: 10px;
  z-index: 99;
  border: none;
  outline: none;
  cursor: pointer;
  font-size: 2em;
  color: #0075ff;
  transition: all 0.5s ease;
  background: White;
  border-radius: 100%;
}

#infiniteScrollFixedHeader,
#infiniteScrollStaticHeader {
  transition: all 0.3s ease-in-out;
  width: 100%;
  top: 0;
  left: 0;
  right: 0;
}

#infiniteScrollFixedHeader {
  position: sticky;
  z-index: 100;
  transform: scale(1);
  background: white;
  box-shadow: 5px 5px 5px 1px rgba(0, 0, 0, 0.1);
  margin-left: -34px !important;
  width: calc(100% + 64px);
  box-sizing: border-box;
}

#infiniteScrollStaticHeader {
  position: static;
  transform: scale(1);
}

@media screen and (max-width: 768px) {
  #infiniteScrollFixedHeader {
    display: none;
  }
}

#backToTopBtn:hover {
  color: #0048a2;
}

/* Sort Up / Down Icons */

.table thead tr .sortIconDown:after {
  font: normal normal normal 10px/1 FontAwesome;
  content: "\f078";
  cursor: pointer;
  padding-left: 0.5em;
}
.table thead tr .sortIconUp:after {
  font: normal normal normal 10px/1 FontAwesome;
  content: "\f077";
  cursor: pointer;
  padding-left: 0.5em;
}

.sortIconDown,
.sortIconUp {
  cursor: pointer;
}

.search-area {
  width: 100% !important;
}

/* Split Button Right Aligned .btn.btn-circle */

.resp_bomExport:nth-child(odd) {
  border-bottom: 2px solid #000000;
}

.table_resp_bomExport tr:nth-child(3n):not(:nth-child(2n)) {
  background: white;
  border-top: 0.9px solid #dddddd;
  border-bottom: 1px solid #dddddd;
}

/* Split Button Right Aligned Mobile */

@media screen and (max-width: 992px) {
  .mobile-right-aligned-btn {
    display: flex !important;
    justify-content: end !important;
  }
}

/* Equal gap between items */

.cadGap {
  gap: 0.5em;
}

/* Smooth scroll to top */
.scrollBehaviourSmooth {
  scroll-behavior: smooth;
}

/* orders checkbox column width */

.checkbox-column {
  width: 35px;
}

/* Input Box with Icon */

.inputWithIconWrap {
  border: 1px solid #ddd;
  display: inline-flex;
  flex-direction: row;
  word-break: keep-all;
  justify-content: center;
  align-items: center;
  background: #f2f6f9;
}

.input-with-icon {
  border: none;
  flex: 1;
}

.input-icon,
.input-with-icon {
  padding: 0.2em 0.5em;
}

/* Search Bar Padding */

.input-group > .custom-select:not(:last-child),
.input-group > .form-control:not(:last-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  padding: 20px 16px;
  background-color: #f2f6f9;
}

/* Custom Div Styling */

.customDivContainer {
  display: flex;
  flex-wrap: wrap;
}

.customDivStyling {
  background-color: white;
  padding: 1em;
  margin-bottom: 1em;
  border-radius: 0.5em;
  box-shadow: 0 2px 2px rgba(0, 0, 0, 0.2);
  flex-grow: 2;
}

.customDivScrollable {
  max-height: 60vh;
  overflow-y: auto;
}

/* Button Full Width Mobile */

@media (max-width: 768px) {
  .mobileBtnFullWidth {
    gap: 0.3em;
    display: grid;
    width: 100%;

    .btn,
    .btn-group {
      width: 100%;
    }
  }
}

/* Registration Page */

.machine_table {
  max-height: 20vh;
  overflow: auto;
}

/* PDF Customer View Word Wrap */

.wordWrap-partDescription {
  word-wrap: break-word;
  min-width: 115px;
}

/* Global Class Cursor Pointer */

.cursor-pointer {
  cursor: pointer;
}

/* Equal Width for Tables */

@media (min-width: 992px) {
  .equal-width th:not(:first-child):not(:nth-child(2)):not(:last-child),
  .equal-width td:not(:first-child):not(:nth-child(2)):not(:last-child) {
    width: calc(100% / 12);
  }
}

.supersessionPart {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.highlightCurrentPN {
  background: #ecf8ff;
  padding: 5px;
  border-radius: 10px;
  border: solid 1px;
  border-color: #ecf8ff;
}

.hover-row {
  background-color: #f0f0f0;
  cursor: pointer;
}

.selected-row {
  background-color: #e0ecff;
}

.disabled-row {
  opacity: 0.5;
  pointer-events: none;
}


 .user-dropdown-stock {
   display: none;
   position: absolute;
   border-radius: 7px;
   background: #fff;
   box-shadow: 0px 0px 8px rgba(214, 214, 214, 0.78);
   list-style: none;
   padding: 10px 10px;
   width: 225px;
   max-width: 225px;
   margin: 0;
   bottom: 22%;
   right: 0;
   z-index: 1000;
   overflow-wrap: break-word;
 }

 .user-dropdown-stock.u-open {
   display: block;
 }

 .table-no-responsive {
  width: 100%;
    border-collapse: collapse;
    text-align: left;
  
    th,
    td {
      padding: 0.5rem;
      border: 1px solid #ddd;
      text-align: center;
    }

    td:first-child {
      text-align: left;
    }
  
    thead {
      background: #f2f2f2;
    }
  
    tr:nth-child(even) {
      background: #f9f9f9;
    }
  
    tr:hover {
      background: #f1f1f1;
    }
 }