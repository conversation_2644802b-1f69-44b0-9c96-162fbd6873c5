(function () {
    "use strict";

    angular.module("app.publications").controller("CreateManualController", CreateManualController);

    CreateManualController.$inject = [
        "manufactureManualService",
        "$state",
        "$uibModalInstance",
        "modalObject",
        "$uibModal",
        "headerBannerService",
        "manufacturerProductService",
        "$translate",
        "publicationService",
        "$filter"
    ];

    function CreateManualController(
        manufactureManualService,
        $state,
        $uibModalInstance,
        modalObject,
        $uibModal,
        headerBannerService,
        manufacturerProductService,
        $translate,
        publicationService,
        $filter
    ) {
        var vm = this;
        vm.data = { rangeId: "", machineId: "", manualId: "", modelId: "", manufacturerSubEntityId: "", useViewableImage: "" };
        vm.isRangeDropdownDisabled = true;
        vm.isMachineDropdownDisabled = true;
        vm.isManufacturerSubEntityDropdownDisabled = true;
        vm.featuredModelUrl = "images/placeholder.jpg";
        var modelsSetupComplete = false;
        var featuredViewableReturned = false;
        var oldFeaturedViewable;
        vm.modelIds = [];
        vm.isModelsDropdownDisabled = true;
        vm.models = [];
        vm.multiCheckdownSettings = {
            checkBoxes: true,
            showCheckAll: false,
            showUncheckAll: false,
            buttonClasses: buttonStyling(),
            styleActive: true,
            closeOnBlur: true,
        };

        vm.isCustomersDropdownDisabled = true;
        vm.selectedCustomers = [];
        vm.customerSelectSettings = {
            checkBoxes: true,
            showCheckAll: true,
            showUncheckAll: false,
            buttonClasses: buttonStyling(),
            styleActive: true,
            closeOnBlur: true,
        };
        vm.cancel = $uibModalInstance.dismiss;
        vm.isMachineDropdownDisabled = true;
        vm.machineDropdownClass = "disabled-select";
        vm.isFeaturedViewableDropdownDisabled = true;
        vm.featuredViewableDropdownClass = "disabled-select";
        vm.publishUnpublishManual = publicationService.publishUnpublishManual;

        vm.rangeChanged = rangeChanged;
        vm.machineChanged = machineChanged;
        vm.createManual = createManual;
        vm.updateManual = updateManual;
        vm.addFeaturedImage = addFeaturedImage;
        vm.modelsSelected = modelsSelected;
        vm.techDocsSelected = techDocsSelected;
        vm.videosSelected = videosSelected;
        vm.kitsSelected = kitsSelected;
        vm.featuredViewableChanged = featuredViewableChanged;
        vm.handleCustomerClick = handleCustomerClick;
        vm.isSelected = isSelected;
        vm.toggleSelectAll = toggleSelectAll;
        vm.isAllSelected = isAllSelected;
        vm.getSelectedCustomerLabels = getSelectedCustomerLabels;

        initialize();

        function buttonStyling() {
            return "btn btn-default multiCheckbox";
        }

        var WENT_WRONG,
            SELECT_VIEWABLES,
            VIEWABLES_SELECTED,
            SELECT_KIT,
            KITS_SELECTED,
            SELECT_TECH_DOC,
            TECH_DOCS_SELECTED,
            SELECT_VIDEO,
            VIDEOS_SELECTED,
            UNASSIGNED,
            CUSTOMERS;
        $translate([
            "GENERAL.WENT_WRONG",
            "CREATE_MANUAL.SELECT_VIEWABLES",
            "CREATE_MANUAL.VIEWABLES_SELECTED",
            "CREATE_MANUAL.SELECT_KIT",
            "CREATE_MANUAL.KITS_SELECTED",
            "CREATE_MANUAL.SELECT_VIDEO",
            "CREATE_MANUAL.VIDEOS_SELECTED",
            "CREATE_MANUAL.SELECT_TECH_DOC",
            "CREATE_MANUAL.TECH_DOCS_SELECTED",
            "CREATE_MANUAL.UNASSIGNED",
            "CREATE_MANUAL.CUSTOMERS",
        ]).then(function (resp) {
            WENT_WRONG = resp["GENERAL.WENT_WRONG"];
            SELECT_VIEWABLES = resp["CREATE_MANUAL.SELECT_VIEWABLES"];
            VIEWABLES_SELECTED = resp["CREATE_MANUAL.VIEWABLES_SELECTED"];
            SELECT_TECH_DOC = resp["CREATE_MANUAL.SELECT_TECH_DOC"];
            TECH_DOCS_SELECTED = resp["CREATE_MANUAL.TECH_DOCS_SELECTED"];
            SELECT_VIDEO = resp["CREATE_MANUAL.SELECT_VIDEO"];
            VIDEOS_SELECTED = resp["CREATE_MANUAL.VIDEOS_SELECTED"];
            SELECT_KIT = resp["CREATE_MANUAL.SELECT_KIT"];
            KITS_SELECTED = resp["CREATE_MANUAL.KITS_SELECTED"];
            UNASSIGNED = resp["CREATE_MANUAL.UNASSIGNED"];
            CUSTOMERS = resp["CREATE_MANUAL.CUSTOMERS"];

            vm.modelTextSettings = {
                buttonDefaultText: SELECT_VIEWABLES,
                dynamicButtonTextSuffix: VIEWABLES_SELECTED,
            };
            vm.techDocTextSettings = {
                buttonDefaultText: SELECT_TECH_DOC,
                dynamicButtonTextSuffix: TECH_DOCS_SELECTED,
            };
            vm.videoTextSettings = {
                buttonDefaultText: SELECT_VIDEO,
                dynamicButtonTextSuffix: VIDEOS_SELECTED,
            };
            vm.kitTextSettings = {
                buttonDefaultText: SELECT_KIT,
                dynamicButtonTextSuffix: KITS_SELECTED,
            };
            vm.customerTextSettings = {
                buttonDefaultText: UNASSIGNED,
                dynamicButtonTextSuffix: CUSTOMERS,
            };
        });

        function initialize() {
            getRange();
            getTechDocs();
            getVideos();
            getKits();
            externalUpdateToSelectedCustomers();

            if (modalObject) {
                populateData();
            } else {
                getManufacturerSubEntities();
            }
        }

        function populateData() {
            vm.data.rangeId = modalObject.rangeId;
            vm.data.machineId = modalObject.machineId;
            vm.data.modelId = modalObject.modelId;
            vm.data.techDocId = modalObject.techDocId;
            vm.data.videoId = modalObject.videoId;
            vm.data.kitId = modalObject.kitId;
            vm.rangeId = modalObject.rangeId;
            getMachineByRange(vm.rangeId);
            vm.machineId = modalObject.machineId;
            getModelByMachine(vm.machineId);

            if (modalObject.isEdit) {
                vm.modelId = modalObject.modelId;
                vm.techDocId = modalObject.techDocId;
                vm.videoId = modalObject.videoId;
                vm.kitId = modalObject.kitId;

                vm.isEdit = true;
                vm.manufacturerSubEntityIds = modalObject.manufacturerSubEntityIds;
                vm.data.manufacturerSubEntityIds = vm.manufacturerSubEntityIds;
                vm.data.manualId = modalObject.manualId;
                vm.manualName = modalObject.manualName;
                vm.serialNumber = modalObject.serialNumber;
                vm.region = modalObject.region;
                vm.site = modalObject.site;
                vm.featuredModelUrl = modalObject.featuredModelUrl || "images/placeholder.jpg";
                featuredViewableReturned = true;
                vm.isMachineDropdownDisabled = !vm.data.machineId;
                vm.useViewableImage = modalObject.useViewableImage;
                vm.isFeaturedViewableDropdownDisabled = !vm.data.modelId;
                vm.machineDropdownClass = vm.isMachineDropdownDisabled ? "disabled-select" : "";
                vm.featuredViewableDropdownClass = vm.isFeaturedViewableDropdownDisabled ? "disabled-select" : "";
                setUpFeaturedViewable();
            } else {
                vm.isDisabled = true;
            }

            getManufacturerSubEntities();
        }

        function getRange() {
            manufactureManualService.getRangeByManufacturer().then(getRangeSuccess, getRangeFailure);
        }

        function getRangeSuccess(response) {
            vm.rangeValues = response.data;
            vm.isRangeDropdownDisabled = false;
        }

        function getRangeFailure(err) {
            console.log(err);
        }

        function rangeChanged(rangeId) {
            vm.data.featuredModelId = null;
            vm.models = [];
            vm.modelSelect = [];
            vm.viewables = [];
            if (rangeId) {
                vm.isMachineDropdownDisabled = false;
                vm.machineDropdownClass = "";
                vm.rangeId = rangeId;
                getMachineByRange(vm.rangeId);
            }
        }

        function getMachineByRange(rangeId) {
            manufactureManualService.getMachineByRange(rangeId).then(machineRangeSuccess, machineRangeFailure);
        }

        function machineRangeSuccess(response) {
            vm.machines = response.data;
            vm.isMachineDropdownDisabled = false;
        }

        function machineRangeFailure(err) {
            console.log(err);
        }

        function machineChanged(machineId) {
            vm.data.featuredModelId = null;
            vm.models = [];
            vm.modelSelect = [];
            if (machineId) {
                vm.isFeaturedViewableDropdownDisabled = false;
                vm.featuredViewableDropdownClass = "";
                vm.machineId = machineId;
                getModelByMachine(vm.machineId);
            }
        }

        function getModelByMachine(machineId) {
            manufactureManualService.getCompletedModelUploadsByMachine(machineId).then(getMachineModelsSuccess, getMachineModelsFailure);
        }

        function getMachineModelsSuccess(response) {
            vm.models = response.data;
            vm.modelSelect = [];
            vm.viewables = [];
            var existingModels = [];
            for (var i = 0; i < vm.models.length; i++) {
                var modelData = { id: vm.models[i].modelId, label: vm.models[i].modelName };
                vm.modelSelect.push(modelData);
                vm.viewables.push(modelData);
                if (vm.data.modelId != null) {
                    for (var n = 0; n < vm.data.modelId.length; n++) {
                        if (modelData.id === vm.data.modelId[n]) {
                            existingModels.push(modelData);
                        }
                    }
                }
            }
            vm.modelSelect = $filter("orderBy")(vm.modelSelect, "label");
            vm.isModelsDropdownDisabled = false;
            vm.modelIds = [];
            //Only clear if not an edit or else load the existing selected models
            if (vm.data.modelId === null) {
                vm.models = [];
            } else {
                vm.models = existingModels;
                modelsSelected();
                modelsSetupComplete = true;
                setUpFeaturedViewable();
            }
        }

        function getTechDocs() {
            manufacturerProductService.getTechDocs().then(getTechDocsSuccess, serviceFailed);
        }

        function getVideos() {
            manufacturerProductService.getVideos().then(getVideosSuccess, serviceFailed);
        }

        function getKits() {
            manufacturerProductService.getKits().then(getKitsSuccess, serviceFailed);
        }

        function getTechDocsSuccess(response) {
            vm.techDocs = response.data;
            vm.techDocSelect = [];
            var existingTechDocs = [];
            for (var i = 0; i < vm.techDocs.length; i++) {
                var techDocData = { id: vm.techDocs[i].id, label: vm.techDocs[i].name };
                vm.techDocSelect.push(techDocData);

                if (vm.data.techDocId != null) {
                    for (var n = 0; n < vm.data.techDocId.length; n++) {
                        if (techDocData.id === vm.data.techDocId[n]) {
                            existingTechDocs.push(techDocData);
                        }
                    }
                }
            }
            vm.techDocSelect = $filter("orderBy")(vm.techDocSelect, "label");

            vm.istechDocsDropdownDisabled = false;
            vm.techDocIds = [];
            //Only clear if not an edit or else load the existing selected techDocs
            if (vm.data.techDocId === null) {
                vm.techDocs = [];
            } else {
                vm.techDocs = existingTechDocs;
                techDocsSelected();
            }
        }

        function getVideosSuccess(response) {
            vm.videos = response.data;
            vm.videoSelect = [];
            var existingVideos = [];
            for (var i = 0; i < vm.videos.length; i++) {
                var videoData = { id: vm.videos[i].id, label: vm.videos[i].name };
                vm.videoSelect.push(videoData);

                if (vm.data.videoId != null) {
                    for (var n = 0; n < vm.data.videoId.length; n++) {
                        if (videoData.id === vm.data.videoId[n]) {
                            existingVideos.push(videoData);
                        }
                    }
                }
            }

            vm.videoSelect = $filter("orderBy")(vm.videoSelect, "label");

            vm.isvideosDropdownDisabled = false;
            vm.videoIds = [];
            //Only clear if not an edit or else load the existing selected videos
            if (vm.data.videoId === null) {
                vm.videos = [];
            } else {
                vm.videos = existingVideos;
                videosSelected();
            }
        }

        function getKitsSuccess(response) {
            vm.kits = response.data;
            vm.kitSelect = [];
            var existingKits = [];
            for (var i = 0; i < vm.kits.length; i++) {
                var titleDescription = vm.kits[i].title ? vm.kits[i].title + " - " + vm.kits[i].description : vm.kits[i].description;
                var kitData = { id: vm.kits[i].id, label: titleDescription || 'No Title' };
                vm.kitSelect.push(kitData);

                if (vm.data.kitId != null) {
                    for (var n = 0; n < vm.data.kitId.length; n++) {
                        if (kitData.id === vm.data.kitId[n]) {
                            existingKits.push(kitData);
                        }
                    }
                }
            }
            vm.kitSelect = $filter("orderBy")(vm.kitSelect, "label");

            vm.isKitsDropdownDisabled = false;
            vm.kitIds = [];
            //Only clear if not an edit or else load the existing selected techDocs
            if (vm.data.kitId === null) {
                vm.kits = [];
            } else {
                vm.kits = existingKits;
                kitsSelected();
            }
        }

        function getMachineModelsFailure(err) {
            console.log(err);
        }

        function getManufacturerSubEntities() {
            manufactureManualService.getManufacturerSubEntitiesForManufacturer().then(getSubEntitiesSuccess, getSubEntitiesFailure);
        }

        function getSubEntitiesSuccess(response) {
            vm.allCustomers = [];
            vm.selectedCustomers = [];

            for (var i = 0; i < response.data.length; i++) {
                var customerData = { id: response.data[i].manufacturerSubEntityId, label: response.data[i].name };
                vm.allCustomers.push(customerData);

                if (vm.data.manufacturerSubEntityIds != null && vm.data.manufacturerSubEntityIds !== []) {
                    if (vm.data.manufacturerSubEntityIds.indexOf(customerData.id) > -1) {
                        vm.selectedCustomers.push(customerData);
                    }
                }
            }

            vm.allCustomers = $filter("orderBy")(vm.allCustomers, "label");

            vm.isCustomersDropdownDisabled = false;
        }

        function handleCustomerClick(customer) {
            event.stopPropagation();
            var index = vm.selectedCustomers.findIndex(c => c.id === customer.id);
            if (index > -1) {
                // Remove the customer from the selected list if it is already selected
                vm.selectedCustomers.splice(index, 1);
            } else {
                // Add the customer to the selected list if it is not already selected
                vm.selectedCustomers.push(customer);
            }
            console.log('Updated Selected Customers:', vm.selectedCustomers);
        }

        function isSelected(customer) {
            return vm.selectedCustomers.some(c => c.id === customer.id);
        }

        function toggleSelectAll() {
            event.stopPropagation();
            if (isAllSelected()) {
                vm.selectedCustomers = [];
            } else {
                vm.selectedCustomers = angular.copy(vm.allCustomers);
            }
        }

        function isAllSelected() {
            if (!vm.allCustomers || !vm.selectedCustomers) {
                return false;
            }
            return vm.allCustomers.length && vm.selectedCustomers.length === vm.allCustomers.length;
        }
        function getSelectedCustomerLabels() {
            if (vm.selectedCustomers.length > 3) {
                return vm.selectedCustomers.length + ' Selected';
            }
            return vm.selectedCustomers.map(c => c.label).slice(0, 3).join(', ');
        }

        function externalUpdateToSelectedCustomers() {
                    vm.selectedCustomers = angular.copy(vm.allCustomers);
        }
        function getSubEntitiesFailure(err) {
            console.log(err);
        }

        function modelsSelected() {
            vm.modelIds = [];
            for (var i = 0; i < vm.models.length; i++) {
                vm.modelIds.push(vm.models[i].id);
            }
        }

        function techDocsSelected() {
            vm.techDocIds = [];
            for (var i = 0; i < vm.techDocs.length; i++) {
                vm.techDocIds.push(vm.techDocs[i].id);
            }
        }

        function videosSelected() {
            vm.videoIds = [];
            for (var i = 0; i < vm.videos.length; i++) {
                vm.videoIds.push(vm.videos[i].id);
            }
        }

        function kitsSelected() {
            vm.kitIds = [];
            for (var i = 0; i < vm.kits.length; i++) {
                vm.kitIds.push(vm.kits[i].id);
            }
        }

        function createManual() {
            $uibModal
                .open({
                    templateUrl: "features/publications/publicationModal/publishModal.html",
                    controller: "publishModalController",
                    controllerAs: "publishModalCtrl",
                    size: "md",
                })
                .result.then(
                    function (selectedOption) {
                        if (selectedOption === "publishLater") {
                            createAndUpdateManual();
                        } else if (selectedOption === "publishNow") {
                            createAndUpdateManual(function (manualId) {
                                console.log("Calling publishManual from createManual");
                                publicationService
                                    .publishManual(manualId)
                                    .then(publicationService.fetchPublications)
                                    .catch(function (error) {
                                        console.log(error);
                                    });
                            });
                        }
                    },
                    function () {
                        console.log("Modal Cancelled");
                    }
                );
        }

        function createAndUpdateManual(publishNowCallback) {
            manufactureManualService
                .createManual(
                    vm.modelIds,
                    vm.techDocIds,
                    vm.videoIds,
                    vm.kitIds,
                    vm.manualName,
                    vm.serialNumber,
                    vm.data.featuredModelId,
                    vm.featuredModelUrl,
                    vm.useViewableImage
                )
                .then(function (response) {
                    createUpdateManualSuccess(response, publishNowCallback);
                }, serviceFailed);
        }

        function createUpdateManualSuccess(response, publishNowCallback) {
            var manualId = response.data;
            var selectedCustomers = [];
            for (var i = 0; i < vm.selectedCustomers.length; i++) {
                selectedCustomers.push(vm.selectedCustomers[i].id);
            }
            manufactureManualService.assignManualCustomers(manualId, selectedCustomers).then(assignSuccess, serviceFailed);
            if (publishNowCallback) {
                publishNowCallback(manualId);
            }
        }

        function assignSuccess() {
            $uibModalInstance.dismiss();
            $state.reload();
        }

        function serviceFailed(err) {
            console.log(err);
            headerBannerService.setNotification("ERROR", WENT_WRONG, 3000);
        }

        function updateManual() {
            modelsSelected();
            manufactureManualService
                .updateManual(
                    vm.data.manualId,
                    vm.modelIds,
                    vm.techDocIds,
                    vm.videoIds,
                    vm.kitIds,
                    vm.manualName,
                    vm.serialNumber,
                    vm.data.featuredModelId,
                    vm.featuredModelUrl,
                    vm.useViewableImage
                )
                .then(createUpdateManualSuccess, serviceFailed);
        }

        function addFeaturedImage() {
            var cropType = "MACHINE";
            $uibModal
                .open({
                    templateUrl: "features/products/imageCropper/imageCropper.html",
                    controller: "ImageCropperController",
                    controllerAs: "imageCropperCtrl",
                    size: "xl",
                    backdrop: "static",
                    resolve: {
                        cropType: function () {
                            return cropType;
                        },
                    },
                })
                .result.then(function (response) {
                    if (response) {
                        vm.featuredModelUrl = response;
                    }
                });
        }

        function setUpFeaturedViewable() {
            if (featuredViewableReturned && modelsSetupComplete) {
                vm.data.featuredModelId = modalObject.featuredModelId;
                oldFeaturedViewable = modalObject.featuredModelId;
                featuredViewableChanged();
            }
        }

        function featuredViewableChanged() {
            var isNewFeaturedSelected = false;
            var selectedModel;

            for (var i = 0; i < vm.modelSelect.length; i++) {
                vm.modelSelect[i].disabled = vm.modelSelect[i].id === vm.data.featuredModelId;
                if (vm.modelSelect[i].id === vm.data.featuredModelId) {
                    selectedModel = vm.modelSelect[i];
                }
            }
            for (var i = 0; i < vm.models.length; i++) {
                if (vm.models[i] && vm.models[i].id === oldFeaturedViewable) {
                    vm.models.splice(i, 1);
                }
                if (vm.models[i] && vm.models[i].id === vm.data.featuredModelId) {
                    isNewFeaturedSelected = true;
                }
            }
            if (!isNewFeaturedSelected && selectedModel !== undefined) {
                vm.models.push(selectedModel);
            }
            oldFeaturedViewable = vm.data.featuredModelId;
        }
    }
})();
