(function () {
  'use strict';

  angular
    .module('app.services')
    .factory('avaTaxService', avaTaxService);

  avaTaxService.$inject = ['$http', 'apiConstants', 'ordersService', '$location', 'shippingEngineService'];

  function avaTaxService($http, apiConstants, ordersService, $location, shippingEngineService) {
    var taxInfo = null;

    return {
      getTax: getTax,
      getTaxInfo: getTaxInfo,
      setTaxInfo: setTaxInfo
    };

    function getTaxInfo() {
      return taxInfo;
    }

    function setTaxInfo(taxResponse) {
      taxInfo = taxResponse;
    }

    function getTax(order) {
      const orderExtraData = (order && order.orderExtraData) ? order.orderExtraData : null
      const orderMapped = ordersService.mapOrderToPayload(order) || null;
      if (!orderMapped || !orderMapped.orderItems.length) return;

      const wareHouse = shippingEngineService.getCurrentWarehouse();
      if(!wareHouse) {
        console.log("Current warehouse is null")
        return;
      }
      shippingEngineService.splitWarehouseItems(orderMapped);
      const config = { params: { userId: order.userId } };
      if(orderExtraData) {
        orderMapped.orderExtraData = orderExtraData;
      }
      const payload = {
        order: orderMapped,
        wareHouseId: wareHouse.id
      }
      return $http.post(apiConstants.url + '/tax/calculate', payload, config);
    }
  }
})();
