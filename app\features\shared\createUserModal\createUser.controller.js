(function () {
    'use strict';

    angular
        .module('app.shared')
        .controller('CreateUserController', CreateUserController);

    CreateUserController.$inject = ['createUserService', '$uibModalInstance', 'createObject', 'userService', 'dpUserService', '$translate'];

    function CreateUserController(createUserService, $uibModalInstance, createObject, userService, dpUserService, $translate) {
        var vm = this;

        vm.type = createObject.type === "DEALER_PLUS" ? "Dealer Plus" : createObject.type;
        vm.cancel = $uibModalInstance.dismiss;

        vm.invalidEmail = false;
        vm.permissions = [];

        vm.dashboardEnabled = userService.getDashboardEnabled();
        vm.isDealerPlus = userService.isDealerPlusUser();
        vm.isManufacturer = userService.isManufacturer();
        vm.isSupreme = userService.isSupreme();
        vm.isDealer = createObject.type === "DEALER";
        vm.createNewUser = createNewUser;
        var GENERAL_ERROR, EMAIL_IN_USE, SELECT_CHECKBOX;
        $translate(['GENERAL.WENT_WRONG', 'CREATE_USER.EMAIL_IN_USE', 'CREATE_USER.SELECT_CHECKBOX'])
            .then(function (resp) {
                GENERAL_ERROR = resp["GENERAL.WENT_WRONG"];
                EMAIL_IN_USE = resp["CREATE_USER.EMAIL_IN_USE"];
                SELECT_CHECKBOX = resp["CREATE_USER.SELECT_CHECKBOX"];
            });

        initialize();

        function initialize() {
            checkPartSearchEnabled();
        }

        function toggleDiscounts() {
            vm.isDiscountEditable = vm.isDiscountEditable ? true : false;
        }

        function checkPartSearchEnabled() {
            if (createObject.type !== 'Internal') {
                if (vm.isManufacturer) {
                    userService.getManufacturerSubEntitySettings(createObject.manufactuerSubEntityId)
                        .then(getManufacturerSubEntitySettingsSuccess);
                } else if (vm.isDealerPlus) {
                    dpUserService.getManufacturerSubEntitySettings(createObject.manufactuerSubEntityId)
                        .then(getManufacturerSubEntitySettingsSuccess);
                }
            }
        }

        function getManufacturerSubEntitySettingsSuccess(response) {
            vm.hasPartSearchEnabled = response.data.partSearchEnabled;
        }

        function createNewUser() {
            vm.hasErrorMessage = false;
            vm.isDisabled = true;
            var userObject = {
                emailAddress: vm.emailAddress,
                firstName: vm.firstName,
                lastName: vm.lastName,
                permissionsArray: createPermissionArray(),
                userSettings: createUserSettings(),
                visContactId : vm.visContactId
            };
            if (userObject.permissionsArray.length > 0) {
                if (createObject.type === 'Internal') {
                    var manufacturerId = userService.getManufacturerId();
                    createUserService.createManufacturerUser(userObject, manufacturerId)
                        .then(createUserSuccess, createUserFailed);
                } else if (createObject.type === 'DEALER') {
                    createUserService.createDealerUser(userObject, createObject.manufactuerSubEntityId)
                        .then(createUserSuccess, createUserFailed);
                } else if (createObject.type === 'DEALER_PLUS') {
                    createUserService.createDealerPlusUser(userObject, createObject.manufactuerSubEntityId)
                        .then(createUserSuccess, createUserFailed);
                } else {
                    createUserService.createCustomerUser(userObject, createObject.manufactuerSubEntityId)
                        .then(createUserSuccess, createUserFailed);
                }
            } else {
                vm.errorMessage = SELECT_CHECKBOX;
                vm.hasErrorMessage = true;
                vm.isDisabled = false;
            }
        }

        function createUserSuccess() {
            $uibModalInstance.close();
        }

        function createUserFailed(response) {
            vm.isDisabled = false;
            vm.hasErrorMessage = true;
            if (response.status === 409) {
                document.getElementById('scrollToTop').scrollTo(0, 0);
                vm.errorMessage = EMAIL_IN_USE;
            } else {
                document.getElementById('scrollToTop').scrollTo(0, 0);
                vm.errorMessage = GENERAL_ERROR;
            }
        }

        function createPermissionArray() {
            var permissionsArray = [];
            if (vm.permissions.orders) {
                permissionsArray.push('Order');
            }
            if (vm.permissions.products) {
                permissionsArray.push('Products');
            }
            if (vm.permissions.publications) {
                permissionsArray.push('Publication');
            }
            if (vm.permissions.customers) {
                permissionsArray.push('Customer');
            }
            if (vm.permissions.admin) {
                permissionsArray.push('Admin');
            }
            if (vm.permissions.security) {
                permissionsArray.push('Security');
            }
            if (vm.permissions.parts) {
                permissionsArray.push('Parts');
            }
            if (vm.permissions.dashboard) {
                permissionsArray.push('Dashboard');
            }
            if (vm.permissions.publishedProducts) {
                permissionsArray.push('PublishedProducts');
            }
            if (vm.permissions.partSearch) {
                permissionsArray.push('PartSearch');
            }
            return permissionsArray;
        }

        function createUserSettings() {
            var userSettings = {disableDiscountEditing : !vm.isDiscountEditable};
            return userSettings;
        }


    }
})();