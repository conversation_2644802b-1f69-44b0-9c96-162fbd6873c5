<div class="modal-header">
  <button type="button" class="close" data-dismiss="modal" ng-click="shipOpsCtrl.cancel()" aria-label="Close"><i
      class="fa fa-close" aria-hidden="true"></i></button>
  <h2 class="modal-title" translate>SHIPPING_OPTIONS.TITLE</h2>
</div>


<div class="modal-body">

  <div class="modal-head">
    <span ng-show="shipOpsCtrl.isSplitWarehouse" translate>SHIPPING_OPTIONS.NOTIFY_SHIPPING_SPLIT </span>
    <span ng-show="!shipOpsCtrl.isSplitWarehouse" class="modal-head"
      translate>SHIPPING_OPTIONS.SELECT_SHIPPING_OPTIONS</span>
    <strong>{{shipOpsCtrl.currentWarehouse.name}}.</strong>
  </div>

  <table class="table shipping-option-table">
    <thead>
      <tr>
        <th translate>SHIPPING_OPTIONS.NAME</th>
        <th translate>SHIPPING_OPTIONS.PRICE</th>
        <th translate>SHIPPING_OPTIONS.ESTIMATE</th>
        <th></th>
      </tr>
    </thead>

    <tbody>
      <tr ng-repeat="item in shipOpsCtrl.rates | orderBy:'+description':true">
        <td data-label="{{'SHIPPING_OPTIONS.NAME' | translate}}">{{item.description | capitalize}}</td>
        <td data-label="{{'SHIPPING_OPTIONS.PRICE' | translate}}">
          {{ item.shipping_amount.amount  | currency: item.shipping_amount.currency_symbol:2}}
        </td> 
        
        <td data-label="{{'SHIPPING_OPTIONS.ESTIMATE' | translate}}">
          <span>
            {{ item.default_estimated_delivery_days }}
          </span>
        </td>

        <td data-label="{{'GENERAL.ACTIONS'}}">
          <input type="checkbox" ng-model="item.carrierSelected" ng-change="shipOpsCtrl.onCarrierSelected(item)"
            class="mb-0 checkbox">
        </td>
      </tr>

      <tr ng-if="!shipOpsCtrl.rates.length > 0">
        <td class="flex-start noPartsBG" colspan="10" translate>SHIPPING_OPTIONS.NO_CARRIER_FOUND</td>
      </tr>
    </tbody>
  </table>

  <div class="modal-actions">
    <button class="btn secondary" ng-click="shipOpsCtrl.cancel()" translate>GENERAL.CANCEL</button>
    <button class="btn primary" ng-disabled="!shipOpsCtrl.carrier" ng-click="shipOpsCtrl.processToCheckOut()"
      translate>CREATE_ORDER.PROCEED_TO_CHECKOUT</button>
  </div>
</div>