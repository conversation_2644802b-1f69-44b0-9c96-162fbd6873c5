(function () {
    'use strict';

    angular
        .module('app.base')
        .controller('HeaderController', HeaderController);

    HeaderController.$inject = ['$scope', 'userService', 'tokenService', 'basketService', '$state', '$uibModal', '$translate'];

    function HeaderController($scope, userService, tokenService, basketService, $state, $uibModal, $translate) {
        var vm = this;

        vm.showBasket = false;
        vm.isUserLoggedIn = false;
        vm.basketQuatity = 0;
        vm.onBehalfOf = null;
        vm.isLanguageSelectionEnabled = false;

        vm.logOut = logOut;
        vm.goToCreateEnquiry = goToCreateEnquiry;
        vm.exitCustomerOrder = exitCustomerOrder;
        vm.dropdownChangeLanguage = dropdownChangeLanguage;
        vm.openNav = openNav;
        vm.closeNav = closeNav;
        vm.closeProfile = closeProfile;
        vm.goToCustomerAccountSubscriptions = goToCustomerAccountSubscriptions;
        vm.resetPage = resetPage;
        vm.InitializeOffClick = InitializeOffClick;
        vm.clearLocalStorage = clearLocalStorage;

        $scope.$on("User-Info-Updated", updateLoggedInStatus);

        $scope.$on("Page-changed", onPageChange);

        initialize();

        function initialize() {
            updateLoggedInStatus()
                .then(updateBasket()
                    .then(InitializeOffClick));
        }

        function updateLoggedOutStatus() {
            configureHeadersToShow();
            updateLanguages();
            updateAvatarForLogout();
        }

        function updateLoggedInStatus() {
            return new Promise(function(resolve) {
                userService.isUserLoggedIn().then((isUserLoggedIn) => {
                    vm.isUserLoggedIn = isUserLoggedIn;
                    if (isUserLoggedIn) {
                        configureHeadersToShow();
                        updateLanguages();
                        updateInitials()
                        FirstLastName()
                    }
                    resolve();
                });
            });
        }

        function updateAvatarForLogout() {
            vm.firstName = null;
            vm.lastName = null;
            vm.firstNameInitial = null;
            vm.secondNameInitial = null;
        }

        function updateInitials(){
            var firstName = userService.getFirstName();
            var lastName = userService.getLastName();
            vm.firstNameInitial = getInitials(firstName);
            vm.secondNameInitial = getInitials(lastName);
        }

        function getInitials(name){
            if(name.length>0){
                return name.substring(0, 1);
            }
            return "";
        }

        function FirstLastName(){
            vm.firstName = userService.getFirstName();
            vm.lastName = userService.getLastName();
        }

        function configureHeadersToShow() {
            vm.showOrders = userService.hasOrderRole();
            vm.showProducts = userService.hasProductsRole();
            vm.showPublishedProducts = userService.hasPublishedProductsRole();
            vm.showPublications = userService.hasPublicationRole();
            vm.showCustomers = userService.hasCustomerRole();
            vm.showAdmin = userService.hasAdminRole();
            vm.showSecurity = userService.hasSecurityRole();
            vm.showManufacturerInformation = userService.hasManufacturerInformationRole();
            vm.showParts = userService.hasPartsRole();
            vm.showPartSearch = userService.hasPartSearchRole();
            vm.showDashboard = userService.hasDashboardRole();
            vm.showContactUs = userService.isManufacturerSubEntity() && !userService.isDealerPlusCustomer() && userService.getContactUsPageEnabled();
            vm.enquiriesOnly = userService.getEnquiriesOnly();
            vm.showSupport = userService.hasSupportRole();
            vm.isDealerPlusUser = userService.isDealerPlusUser();
            vm.showCustomerSubscriptions = userService.isManufacturer()
        }

        function logOut() {
            tokenService.oauthLogOut();
            updateLoggedOutStatus();
            vm.profileOpen = false;
            $state.go('login');    
        }

        function onPageChange() {
            vm.onBehalfOf = $state.params.onBehalfOf && $state.params.onBehalfOf !== "null" ? JSON.parse(decodeURIComponent(atob($state.params.onBehalfOf))) : undefined;
            vm.hideHeader = ($state.current.name.toUpperCase().includes("VIEWER") && !vm.onBehalfOf) || ($state.current.name.toUpperCase().includes("LOGIN")) || ($state.current.name.toUpperCase().includes("REGISTER")) || ($state.current.name.toUpperCase().includes("RESENDPASSWORD"));
            updateBasket();
        }

        function goToCreateEnquiry() {
            $state.go('create',
                {
                    onBehalfOf: $state.params.onBehalfOf
                });
        }

        $scope.$on("Basket-Updated", function (evt, data) {
            updateBasket();
        });

        function updateBasket() {
            return new Promise(function(resolve) {
                userService.isUserLoggedIn().then((isUserLoggedIn) => {

                    if (isUserLoggedIn) {
                        vm.basket = basketService.getBasket();
                        if (vm.basket) {
                            if (vm.basket.length > 0) {
                                var basketItemCount = 0;
                                for (var i = 0; i < vm.basket.length; i++) {
                                    basketItemCount = basketItemCount + vm.basket[i].quantity;
                                }

                                vm.basketQuatity = basketItemCount;
                                vm.showBasket = true;
                            } else {
                                if (!userService.isManufacturer() || vm.onBehalfOf) {
                                    vm.showBasket = true;
                                } else {
                                    vm.showBasket = false;
                                }
                                vm.basketQuatity = 0;
                            }
                        }

                        vm.basketQuatity = vm.basketQuatity + basketService.getManualPartsCount();

                        if (!$scope.$$phase) {
                            $scope.$apply();
                        }
                    } else {
                        vm.showBasket = false;
                    }
                });
                resolve();
            });
        }

        function exitCustomerOrder() {
            if (vm.showBasket && vm.basketQuatity !== 0) {
                $uibModal.open({
                    templateUrl: 'features/base/header/orderOnBehalfOf/exitOrderOnBehalfOf.html',
                    controller: 'ExitOrderOnBehalfOfController',
                    controllerAs: 'exitOrderOnBehalfOfCtrl',
                    size: 'sm'
                });
            } else if (userService.isDealerPlusUser()) {
                $state.go('dpCustomers');
            } else {
                $state.go('customers');
            }
        }

        function dropdownChangeLanguage(index) {
            var languageCode = vm.availableLanguages[index].languageCode;
            $translate.use(languageCode);
        }

        function updateLanguages() {
            vm.availableLanguages = userService.getUserLanguages();
            //TODO Options we may need to recode into here
            //there is only one language, set it
            //no language set use default

        }

        function closeNav() {
            var close = document.getElementById('mySidenav');
            close.style.width = "0";
        }

        function openNav() {
            var open = document.getElementById('mySidenav');
            open.style.width = "250px";
        }

        function InitializeOffClick() {
            document.addEventListener("click", function(e) {
                if (e.target.id != 'openBtn' && !document.getElementById("mySidenav").contains(e.target)) {
                    closeNav();
                }
            })
        }

        function closeProfile() {

            vm.profileOpen = false;

        }

        function goToCustomerAccountSubscriptions() {
            var userId = userService.getUserId();
            $state.go('userSettings', { userId: userId });
        }

        function resetPage() {
            window.localStorage.clear();
            window.location.reload(true);
        }

        function clearLocalStorage() {
            window.localStorage.removeItem('baskets');
            window.location.reload(true);
        }

    }
})();