<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" ng-click="editCustDetailsCtrl.cancel()" aria-label="Close">
        <i class="fa fa-close" aria-hidden="true"></i>
    </button>

    <h2 class="modal-title">{{"ORDER.ORDER_DETAILS" | translate}}</h2>

</div>


<div class="modal-body d-flex flex-wrap no-gutters">

    <div class="col-12 col-md-4">

        <div class="input-group">
            <label translate>ORDER.DELIVERY_ADDRESS</label>
            <div class="select-box p-0">
                <select class="h-100" ng-model="editCustDetailsCtrl.newDeliveryAddress" name="deliveryAddress">
                    <option ng-hide="true" value='' disabled="" translate>ORDER.SELECT_DELIVERY_ADDRESS</option>
                    <option ng-repeat="address in editCustDetailsCtrl.addresses" value="{{address}}">
                        {{address.addressLine1}}, {{address.city}}, {{address.state}}, {{address.postcode}},
                        {{address.country}}
                    </option>

                </select>

                <div class="select-arrow"></div>
            </div>
        </div>

        <div class="input-group">
            <label translate>ORDER.BILLING_ADDRESS</label>
            <div class="select-box p-0">
                <select class="h-100" ng-model="editCustDetailsCtrl.newBillingAddress" name="billingAddress">
                    <option ng-hide="true" value='' disabled="" translate>ORDER.SELECT_BILLING_ADDRESS</option>
                    <option ng-repeat="address in editCustDetailsCtrl.addresses" value="{{address}}">
                        {{address.addressLine1}}, {{address.city}}, {{address.state}}, {{address.postcode}},
                        {{address.country}}
                    </option>

                </select>

                <div class="select-arrow"></div>
            </div>

            <a ng-hide="editCustDetailsCtrl.hideAddNewAddressButton" class="btn xsmall secondary" href="" style="margin-top:10px;"
               ng-click="editCustDetailsCtrl.addNewAddress()" translate>ORDER.ADD_NEW_ADDRESS</a>

        </div>

    </div>

    <div class="col-12 col-md-4 pl-0 pl-md-4">

        <div class="input-group">
            <label>
                {{"ORDER.DELIVERY_CONTACT_NAME" | translate}}
            </label>
            <div class="select-box">
                <select 
                    ng-model="editCustDetailsCtrl.deliveryName" 
                    name="deliveryName"
                    required
                    ng-class="{'has-error': detailsForm.$submitted && detailsForm.deliveryName.$invalid}"
                    ng-change="editCustDetailsCtrl.updateDeliveryId()"
                >
                    <option value="" disabled translate>ORDER.DELIVERY_CONTACT_NAME</option>
                    <option ng-repeat="contact in editCustDetailsCtrl.contacts" value="{{contact.name}}" data-id="{{contact.id}}">
                        {{contact.name}}
                    </option>
                </select>
                <div class="select-arrow"></div>
            </div>
        </div>

        <div class="input-group mb-0">
            <label>
                {{"ORDER.ORDER_CONTACT_NAME" | translate}}
            </label>
            <div class="select-box">
                <select 
                    ng-model="editCustDetailsCtrl.contactName" 
                    name="contactName"
                    required
                    ng-class="{'has-error': detailsForm.$submitted && detailsForm.contactName.$invalid}"
                    ng-change="editCustDetailsCtrl.updateContactId()"
                >
                    <option value="" disabled translate>ORDER.ORDER_CONTACT_NAME</option>
                    <option ng-repeat="contact in editCustDetailsCtrl.contacts" value="{{contact.name}}" data-id="{{contact.id}}">
                        {{contact.name}}
                    </option>
                </select>
                <div class="select-arrow"></div>
            </div>
        </div>

        <a class="btn xsmall secondary" href="" style="margin-top:10px;"
           ng-click="editCustDetailsCtrl.addNewName()" translate>ORDER.ADD_NEW_NAME</a>

    </div>

    <div class="col-12 col-md-4 pl-0 pl-md-4">

        <div class="input-group">
            <label>
                {{"ORDER.DELIVERY_NUMBER" | translate}}
            </label>
            <div class="select-box">
                <select ng-model="editCustDetailsCtrl.newDeliveryNumber" name="deliveryNumber">
                    <option ng-repeat="number in editCustDetailsCtrl.numbers" value="{{number.contactNumber}}">
                        {{number.contactNumber}}
                    </option>
                </select>
                <div class="select-arrow"></div>
            </div>
        </div>


        <div class="input-group mb-0">
            <label>
                {{"ORDER.CONTACT_NUMBER" | translate}}
            </label>
            <div class="select-box">
                <select ng-model="editCustDetailsCtrl.newContactNumber" name="contactNumber">
                    <option ng-repeat="number in editCustDetailsCtrl.numbers" value="{{number.contactNumber}}">
                        {{number.contactNumber}}
                    </option>
                </select>
                <div class="select-arrow"></div>
            </div>
        </div>

        <a class="btn xsmall secondary" href="" style="margin-top:10px;"
           ng-click="editCustDetailsCtrl.addNewNumber()" translate>ORDER.ADD_NEW_NUMBER</a>

    </div>


    <div class="flex justify-content-end w-100 mt-5">
        <button type="button" class="btn secondary" ng-click="editCustDetailsCtrl.cancel()" translate>GENERAL.CANCEL
        </button>

        <button ng-class="editCustDetailsCtrl.isDealerPlusPage() ? 'dpGreenModal btn primary' : 'btn primary'"
                type="button" class="ml-3 btn primary" ng-click="editCustDetailsCtrl.saveEdits()" translate>
            GENERAL.CONFIRM
        </button>
    </div>

</div>
