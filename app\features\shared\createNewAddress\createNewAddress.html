<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" ng-click="createNewAddressCtrl.cancel()"
            aria-label="Close"><i class="fa fa-close" aria-hidden="true"></i>
    </button>
    <h2 class="modal-title" id="myModalLabel" translate>ADDRESS.ADD_NEW_ADDRESS</h2>
  </div>
<div class="modal-body">
    <form name="editCustomerDetailsForm" class="form">

        <div class="error-alert" ng-if="!editCustomerDetailsForm.$valid && !editCustomerDetailsForm.$pristine">
            <p class="errortext" translate>
                ADDRESS.PLEASE_COMPLETE_ALL_FIELDS
            </p></div>
        <div class="input-group" ng-if="createNewAddressCtrl.showCompanyName">
          <label>{{"ADDRESS.COMPANY_NAME" | translate}}</label>
            <input type="text" ng-model="createNewAddressCtrl.data.companyName" placeholder="{{'ADDRESS.ENTER_COMPANY_NAME' | translate}} 1">
        </div>
        <div class="input-group">
          <label>{{"ADDRESS.ADDRESS_LINE" | translate}} 1 *</label>
            <input type="text" ng-required="true" ng-model="createNewAddressCtrl.data.addressLine1" placeholder="{{'ADDRESS.ENTER_ADD_LINE' | translate}} 1">
        </div>
        <div class="input-group">
          <label>{{"ADDRESS.ADDRESS_LINE" | translate}} 2</label>
            <input type="text" ng-model="createNewAddressCtrl.data.addressLine2" placeholder="{{'ADDRESS.ENTER_ADD_LINE' | translate}} 2">
        </div>
        <div class="form-row">
            <div class="input-group col-md-6">
                <label>{{"ADDRESS.CITY" | translate}} *</label>
                <input type="text" ng-required="true" ng-model="createNewAddressCtrl.data.city" placeholder="{{'ADDRESS.ENTER_CITY' | translate}}">
            </div>
            <div class="input-group col-md-6">
                <label>{{"ADDRESS.STATE" | translate}}
                    <span ng-if="createNewAddressCtrl.data.country === 'United States' || createNewAddressCtrl.data.country === 'Canada'">*</span>
                </label>
                <input type="text" ng-required="createNewAddressCtrl.data.country === 'United States' || createNewAddressCtrl.data.country === 'Canada'" ng-model="createNewAddressCtrl.data.state" placeholder="{{'ADDRESS.ENTER_STATE' | translate}}">
            </div>
        </div>
        <div class="form-row">
            <div class="input-group col-md-6">
                <label>{{"ADDRESS.POSTCODE" | translate}} *</label>
                <input type="text" ng-required="true" ng-model="createNewAddressCtrl.data.postcode" placeholder="{{'ADDRESS.ENTER_POSTCODE' | translate}}">
            </div>
            <div class="input-group col-md-6">
                <label>{{"ADDRESS.COUNTRY" | translate}} *</label>
                <div class="select-box">
                    <select ng-model="createNewAddressCtrl.data.country" ng-required="true"
                            ng-options="country.name as country.name for country in createNewAddressCtrl.countries">
                        <option value="" disabled selected translate>ADDRESS.COUNTRY</option>
                    </select>
                    <div class="select-arrow"></div>
                </div>
            </div>
        </div>


        <div class="modal-actions">
            <button type="button" class="btn secondary" ng-click="createNewAddressCtrl.cancel()" translate>GENERAL.CANCEL</button>
            &nbsp;

            <button type="button" class="btn primary" ng-disabled="!editCustomerDetailsForm.$valid || createNewAddressCtrl.processing" ng-class="createNewAddressCtrl.isDealerPlusPage() ? 'dpGreenModal' : ''"
                    ng-click="createNewAddressCtrl.createAddress()">
                <span ng-hide="createNewAddressCtrl.processing">{{ 'ADDRESS.CREATE_ADDRESS' | translate }}</span>
                <span ng-show="createNewAddressCtrl.processing">
                        <span class="spinner-border text-light" role="status" aria-hidden="true"></span>
                    {{ 'ADDRESS.SAVING' | translate }}
                    </span>
            </button>

        </div>

    </form>
  </div>