// ==========================================================================
// Developers styles go here, they will be moved into their correct stylesheet by design
// ==========================================================================


input[type="radio"] {
height: 16px;
width: auto;
}

.addToBasketBtn {
width: 140px;
margin-right: 16px;
color: white;
}

.parts-accordion .panel-group .panel .panel-heading {
color: #fff;
  background-color: #337ab7;
  }

.parts-accordion .panel-group .panel .panel-heading .span{
color: #fff;
  }

.white {
color: white;
}

.number-cards{
width: calc(50% - 24px);
float: left;
position: relative;
height: auto;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: #fff;
    background-clip: border-box;
    border: 1px solid rgba(0,0,0,.125);
    border-radius: .25rem;
    margin: 8px 0px 0px 8px;

}

.number-cards .card-header{
background-color: #F2F6F9;
    text-transform: uppercase;
    font-weight: 700;
    border-top: 1px solid #D2DAE5;
    border-bottom: 1px solid #D2DAE5;
    font-size: 0.875em;
}

.page-body {
padding: 0px 32px;
}

.inline-box{
	display: inline-block;
}

.disabled-dropdown{
    background-color: $grey;
    border: 1px solid $textdark2;
    border: 1px solid $textdark2;
}

.master-part-text{
display: inline-block;
  width: 75%;
}

.master-part-buttons{
display: inline-block;
  width: 25%;
  float: right;

  & button{
  float: right;
  }
}
.master-part-text-kit{
display: inline-block;
  width: 60%;
}

.master-part-buttons-kit{
display: inline-block;
  width: 40%;
  float: right;

  & button{
  float: right;
  }
}

.recentlyOrderedPanel{
    padding-bottom: 16px;
    padding-right: 16px;
    padding-top: 16px;
}
.frequentlyOrderedPanel{
    padding-bottom: 16px;
    padding-left: 16px;
    padding-top: 16px;
}

.dropdown {
  position: relative;
  display: inline-block;
}

/* Dropdown Content (Hidden by Default) */
.dropdown-content {
  position: absolute;
  background-color: #f9f9f9;
  min-width: 100px;
  box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
  z-index: 1;
  padding: 0;
}

/* Links inside the dropdown */
.dropdown-content a {
  color: black;
  padding: 12px 16px;
  text-decoration: none;
  display: block;
}
.dropdown-content li {
    list-style-type: none;
}
/* Change color of dropdown links on hover */
.dropdown-content li:hover {
background-color: #3392FC;
color: #ffffff;
}


