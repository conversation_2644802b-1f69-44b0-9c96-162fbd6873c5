(function () {
  "use strict";

  angular
    .module("app.viewer")
    .controller("BuyPartController", BuyPartController);

  BuyPartController.$inject = [
    "basketService",
    "viewerHelperService",
    "viewerService",
    "$scope",
    "$rootScope",
    "userService",
    "$stateParams",
    "$translate",
    "$timeout",
    "masterPartService",
  ];

  function BuyPartController(
    basketService,
    viewerHelperService,
    viewerService,
    $scope,
    $rootScope,
    userService,
    $stateParams,
    $translate,
    $timeout,
    masterPartService
  ) {
    var vm = this;

    vm.partsList = [];
    vm.selectedPartNote = "";
    vm.isPreviewMode = userService.isManufacturer() && !$stateParams.onBehalfOf;
    vm.hasOrdersAccess = userService.hasOrderRole();

    vm.addPartsToBasket = addPartsToBasket;
    vm.cancel = cancel;
    vm.isQuantityZero = isQuantityZero;
    vm.addPartToBasket = addPartToBasket;
    vm.isStockWarehousesEnabled = userService.getStockWarehousesEnabled();
    vm.getStockClasses = getStockClasses;
    vm.getPartDescription = getPartDescription;
    vm.shortenNote = shortenNote;

    vm.isNotesExpanded = true;
    vm.isAddBasketButtonClicked = false;

    var PREVIEW_NO_ADD;
    $translate(["BUY_PART.PREVIEW_NO_ADD"]).then(function (resp) {
      PREVIEW_NO_ADD = resp["BUY_PART.PREVIEW_NO_ADD"];
    });

    function cancel() {
      hideBuyParts();
    }

    $scope.$on("Show-Non-Modeled-Parts", setNonModelledParts);
    $scope.$on("Hide-Non-Modeled-Parts", hideBuyParts);

    $rootScope.$on("viewer-part-selected", function (event, partViewerDetail) {
      if (partViewerDetail && partViewerDetail.part && partViewerDetail.part.note) {
        vm.selectedPartNote = partViewerDetail.part.note.split('\n');
      } else {
        vm.selectedPartNote = [];
      }
    });

    function setNonModelledParts(event, nonModelled) {
      vm.isVisible = true;
      vm.partsList = nonModelled;

      for (var i = 0; i < nonModelled.length; i++) {
        vm.partsList[i].quantity = 0;
        vm.partsList[i].clicked = false;
      }
    }

    function hideBuyParts() {
      vm.isVisible = false;
    }

    function shortenNote(note) {
      if (!note) return '';
      return note.length > 50 ? note.substring(0, 50) + '...' : note;
    }
  
    // Stock
    function getStockClasses(stock) {
      return {
        'text-success': stock >= 3,
        'text-warning': stock > 0 && stock < 3,
        'text-danger': !stock || stock < 1
      };
    }
  
    // Part description
    function getPartDescription(part) {
      return part.partDescription || part.description;
    }

    function addPartsToBasket() {
      if (vm.isPreviewMode) {
          alert(PREVIEW_NO_ADD);
      } else {
          var partAdded = false;
          var machineName = viewerHelperService.getMachineName();
          var manufacturerId = userService.getManufacturerId();
          var manufacturerSubEntityId = vm.onBehalfOf ? vm.onBehalfOf.manufacturerSubEntityId : userService.getManufacturerSubEntityId();
          var onBehalfOfUserId = vm.onBehalfOf ? vm.onBehalfOf.userId : null;
          var isExactMatch = true;
          vm.partsList.forEach(function (part) {
              if (part.quantity > 0) {
                  masterPartService.partSearch(
                      manufacturerId,
                      manufacturerSubEntityId,
                      part.partNumber,
                      'partNumber',
                      onBehalfOfUserId,
                      isExactMatch
                  )
                      .then(function (resp) {
                          vm.isVisible = false;
                          part.machineName = machineName;
                          
                          if(resp.data.totalResults > 0)
                              console.log (resp.data)
                               if (resp.data.masterParts && resp.data.masterParts.length > 0) {
                                   part.masterPartId = resp.data.masterParts[0].masterPartId;
                                   part.price = resp.data.masterParts[0].price;
                                   part.stock = resp.data.masterParts[0].stock;
                              }
                          basketService.addPart(part);
                          partAdded = true;
                          part.quantity = 0;
                      })
              }
          });
          $rootScope.$broadcast("Basket-Updated", {action: 'addPartsToBasket'});
          vm.isAddBasketButtonClicked = true;

          $timeout(function () {
            vm.isAddBasketButtonClicked = false;
          }, 500);
        }
      }

  function addPartToBasket(part) {
    if (vm.isPreviewMode) {
        alert(PREVIEW_NO_ADD);
    } else {
        var partCopy = angular.copy(part);

        if (partCopy.quantity === 0) {
            partCopy.quantity = 1;
        }
        var manufacturerId = userService.getManufacturerId();
        var manufacturerSubEntityId = vm.onBehalfOf ? vm.onBehalfOf.manufacturerSubEntityId : userService.getManufacturerSubEntityId();
        var onBehalfOfUserId = vm.onBehalfOf ? vm.onBehalfOf.userId : null;
        var isExactMatch = true;

        masterPartService.partSearch(
            manufacturerId,
            manufacturerSubEntityId,
            partCopy.partNumber,
            'partNumber',
            onBehalfOfUserId,
            isExactMatch
        )
        .then(function (resp) {
            partCopy.machineName = viewerHelperService.getMachineName();
            if(resp.data.totalResults > 0 && resp.data.masterParts && resp.data.masterParts.length > 0) {
                partCopy.masterPartId = resp.data.masterParts[0].masterPartId;
                partCopy.price = resp.data.masterParts[0].price;
                partCopy.stock = resp.data.masterParts[0].stock;
            }
            basketService.addPart(partCopy);
            part.clicked = true; // This operates on the original 'part' to give UI feedback
            $rootScope.$broadcast("Basket-Updated", {action: 'addPartToBasket'});

            $timeout(function() {
                part.clicked = false;
            }, 500);
        });
    }
}

    function isQuantityZero() {
      var isQuantityZero = true;
      for (var i = 0; i < vm.partsList.length; i++) {
        var part = vm.partsList[i];
        if (part.quantity >= 1) {
          isQuantityZero = false;
        }
      }
      return isQuantityZero;
    }
  }
})();
