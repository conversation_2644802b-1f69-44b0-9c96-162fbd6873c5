(function () {
    'use strict';

    angular
        .module('app.parts')
        .controller('CreateMasterPartController', CreateMasterPartController);

    CreateMasterPartController.$inject = ['masterPartService', '$uibModalInstance', 'userService', '$state', '$translate', '$uibModal'];

    function CreateMasterPartController(masterPartService, $uibModalInstance, userService, $state, $translate, $uibModal) {
        var vm = this;

        vm.create = create;
        vm.cancel = $uibModalInstance.dismiss;

        var ALREADY_EXISTS, WENT_WRONG;
        $translate(['CREATE_MP.ALREADY_EXISTS', 'GENERAL.WENT_WRONG'])
            .then(function (resp) {
                ALREADY_EXISTS = resp["CREATE_MP.ALREADY_EXISTS"];
                WENT_WRONG = resp["GENERAL.WENT_WRONG"];
            });

        initialize();

        function initialize() {
            vm.languages = userService.getUserLanguages()
        }

        function serviceFailure(error) {
            if (error.status === 302) {
                vm.error = ALREADY_EXISTS
            } else {
                vm.error = WENT_WRONG;
            }
        }

        function create() {
            vm.error = '';
            masterPartService.createMasterPart(vm.partNumber, vm.description, vm.language.languageId)
                .then(createMasterPartSuccess, serviceFailure);
        }

        function createMasterPartSuccess(response) {
            $uibModalInstance.close();
            $state.go('masterPart', {masterPartId: response.data});
        }
    }
})();
