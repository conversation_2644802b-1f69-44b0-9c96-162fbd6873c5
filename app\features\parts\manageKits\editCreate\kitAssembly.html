<div class="kit-assembly-content ng-scope p-4">
    <!-- Heading -->
    <div>
        <h1 class="mb-2 ng-binding">
            {{ kitAssemblyCtrl.isEditMode ? ('KIT_ASSEMBLY.EDIT_HEADING' | translate) : ('KIT_ASSEMBLY.CREATE_HEADING' | translate) }}
        </h1>
        <p class="mb-2" translate>KIT_ASSEMBLY.DESCRIPTION</p>
    </div>

    <!-- Kit Details, Descriptions, and Price Lists -->
    <div class="row pt-2">
        <div class="col-md-4 customDivContainer">
            <div class="customDivStyling">
                <!-- Kit Details Section -->
                <div class="customCardBody">
                    <h3 translate>KIT_ASSEMBLY.KIT_DETAILS</h3>
                </div>
                <div>
                    <div ng-if="!kitAssemblyCtrl.partNumberExists">
                        <div class="justify-content">
                            <span>
                                <h4 class="mb-2" translate>KIT_ASSEMBLY.PART_NUMBER</h4>
                                <p class="m-0" translate>KIT_ASSEMBLY.ADD_PART_NUM</p>
                            </span>
                            <span>
                                <button class="btn secondary comments float-right" ng-click="kitAssemblyCtrl.assignPartNumber()">
                                    <i class="fa fa-fw fa-pencil fa-lg fa-fw"></i>
                                </button>
                            </span>
                        </div>
                    </div>
                    <div>
                        <div class="justify-content mb-3" ng-if="kitAssemblyCtrl.partNumberExists">
                            <span>
                                <h4 class="mb-2" translate>KIT_ASSEMBLY.PART_NUMBER</h4>
                                <p class="m-0">{{kitAssemblyCtrl.kit.partNumber}}</p>
                            </span>
                            <span>
                                <button class="btn secondary comments float-right" ng-click="kitAssemblyCtrl.assignPartNumber()">
                                    <i class="fa fa-fw fa-pencil fa-lg fa-fw"></i>
                                </button>
                            </span>
                        </div>
                    </div>
                    <div>
                        <div class="justify-content" ng-if="kitAssemblyCtrl.partNumberExists">
                            <span>
                                <div class="d-flex align-items-center cadGap">                              
                                <h4 class="mb-2" translate>KIT_ASSEMBLY.KIT_DESC</h4>
                                <i class="fa fa-info-circle mb-1 cadBlue" aria-hidden="true" data-toggle="tooltip" data-placement="top" 
                                title="{{'KIT_ASSEMBLY.KIT_DESC_TOOLTIP' | translate}}"></i>
                            </div>  
                                <p class="m-0" ng-if="!kitAssemblyCtrl.kit.description">{{'KIT_ASSEMBLY.NO_DESC' | translate}}</p>
                                <p class="m-0" ng-if="kitAssemblyCtrl.kit.description">{{kitAssemblyCtrl.kit.description}}</p>
                            </span>
                            <span>
                                <button
                                    class="btn secondary comments float-right"
                                    ng-click="kitAssemblyCtrl.editDescription(kitAssemblyCtrl.kit.description)"
                                >
                                    <i class="fa fa-fw fa-pencil fa-lg fa-fw"></i>
                                </button>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <div class="col-md-4 customDivContainer pr-1 pl-1">
        <div class="customDivStyling">
            <div class="customCardBody">
                <h3 translate>KIT_ASSEMBLY.KIT_NAME</h3>
            </div>
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th translate>KIT_ASSEMBLY.LANGUAGE</th>
                        <th translate>KIT_ASSEMBLY.TRANSLATION</th>
                        <th class="table-no-th"></th>
                    </tr>
                </thead>
                <tbody>
                    <tr ng-repeat="translation in kitAssemblyCtrl.translations">
                        <td data-label="{{'KIT_ASSEMBLY.LANGUAGE' | translate}}">{{ translation.displayText }}</td>
                        <td data-label="{{'KIT_ASSEMBLY.TRANSLATION' | translate}}">
                            {{ translation.description }}
                        </td>
                        <td data-label="">
                            <button class="btn secondary comments float-right"
                                ng-click="kitAssemblyCtrl.editTranslation(translation, true)"
                                ng-show="translation.description" ng-disabled="!kitAssemblyCtrl.isEditMode">
                                <i class="fa fa-fw fa-pencil fa-lg fa-fw"></i>
                            </button>
                            <button class="btn secondary comments float-right"
                                ng-click="kitAssemblyCtrl.createTranslation(translation)" ng-hide="translation.description"
                                ng-disabled="!kitAssemblyCtrl.isEditMode">
                                <i class="fa fa-fw fa-plus fa-lg fa-fw"></i>
                            </button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
        <div class="col-md-4 customDivContainer" ng-if="kitAssemblyCtrl.kit.prices.length">
            <div class="customDivStyling">
                <!-- Price Lists Section -->
                <div class="customCardBody">
                    <h3 translate>KIT_ASSEMBLY.PRICE_LIST</h3>
                </div>
                <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th translate>KIT_ASSEMBLY.IDENTIFIER</th>
                                <th translate>KIT_ASSEMBLY.PRICE</th>
                                <th class="table-no-th"></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr ng-repeat="priceList in kitAssemblyCtrl.kit.prices">
                                <td data-label="{{'KIT_ASSEMBLY.IDENTIFIER' | translate}}">{{ priceList.currencyIdentifier }}</td>
                                <td data-label="{{'KIT_ASSEMBLY.PRICE' | translate}}">{{ priceList.price }}</td>
                                <td data-label="">
                                    <button
                                        class="btn secondary comments float-right"
                                        ng-click="kitAssemblyCtrl.editPriceList(priceList.currencyIdentifier)"
                                        ng-disabled="!kitAssemblyCtrl.isEditMode"
                                    >
                                        <i class="fa fa-fw fa-pencil fa-lg fa-fw"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr ng-if="!kitAssemblyCtrl.kit.prices.length">
                                <td colspan="3" translate>KIT_ASSEMBLY.NO_PRICE_LISTS</td>
                            </tr>
                        </tbody>                                    
                    </table>
                </div>
            </div>
        </div>
         <div class="col-md-4 customDivContainer" ng-if="!kitAssemblyCtrl.kit.prices.length">
            <div class="customDivStyling">
                <!-- Single Price Section -->
                <div class="customCardBody">
                    <h3 translate>KIT_ASSEMBLY.PRICE</h3>
                </div>
                <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th translate>KIT_ASSEMBLY.PRICE</th>
                                <th class="table-no-th"></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr> 
                                <td data-label="{{'KIT_ASSEMBLY.PRICE' | translate}}">{{kitAssemblyCtrl.kit.price | currency}}<div ng-if="!kitAssemblyCtrl.kit.price">N/A</div></td>
                                <td data-label="">
                                    <button
                                    class="btn secondary comments float-right"
                                    ng-click="kitAssemblyCtrl.editSinglePrice(kitAssemblyCtrl.kit.price)"
                                    ng-disabled="!kitAssemblyCtrl.isEditMode"
                                >
                                    <i class="fa fa-fw fa-pencil fa-lg fa-fw"></i>
                                </button>
                                </td>
                            </tr>
                        </tbody>                                    
                    </table>
                </div>
            </div>
        </div>
        
    </div>

    <!-- Part Search and Kit Contents -->
    <div class="row">
        <!-- Left half (6 columns) -->
        <form  class="col-md-6 pr-3 customized-search" ng-submit="kitAssemblyCtrl.searchParts()">
            <!-- Search input (full width of left half) -->
            <div class="customDivStyling customized-bar">
                <div class="input-group mb-0">
                    <input  class="form-control mr-0" type="search" ng-model="kitAssemblyCtrl.searchQuery" placeholder="{{'PART_SEARCH.SEARCH_BY_PART' | translate}}">
                    <div class="input-group-append">
                        <button type="submit" ng-hide="kitAssemblyCtrl.searching" class="input-group-text input-group-text-btn input-group-text-btn btn-anim"><i class="pr-0 pr-md-3 fa fa-search"></i>
                            <span class="search_mobile_disable">{{'KIT_ASSEMBLY.SEARCH_BUTTON' | translate}}</span>
                            </button>
                        <button type="submit" ng-show="kitAssemblyCtrl.searching" class="input-group-text input-group-text-btn btn-anim"><i class="pr-0 pr-md-3 fa fa-search"></i><span class="search_mobile_disable">{{'PART_SEARCH.SEARCHING' | translate}}</span>
                            <span class="spinner-border text-light" role="status" aria-hidden="true"></span>
                        </button>
                    </div>
                </div>
            </form>
            
            <!-- Search results (full width of left half) -->
            <div class="search-results mt-4">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th translate>KIT_ASSEMBLY.PART_NUMBER</th>
                            <th translate>KIT_ASSEMBLY.PART_DESC</th>
                            <th translate>KIT_ASSEMBLY.ADD</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr ng-repeat="part in kitAssemblyCtrl.searchResults">
                            <td data-label="{{'KIT_ASSEMBLY.PART_NUMBER' | translate}}">{{ part.partNumber }}</td>
                            <td data-label="{{'KIT_ASSEMBLY.KIT_DESC' | translate}}">{{ part.filteredDescription }}</td>
                            <td data-label="{{'KIT_ASSEMBLY.ADD' | translate}}" class="text-center align-middle">
                                <button
                                    class="btn btn-primary"
                                    ng-click="kitAssemblyCtrl.addToKit(part)"
                                    ng-disabled="kitAssemblyCtrl.isPartInKit(part)"
                                >
                                    <i class="fa fa-plus"></i>
                                </button>
                            </td>
                        </tr>
                        <tr ng-if="!kitAssemblyCtrl.searchResults.length">
                            <td colspan="4" translate>KIT_ASSEMBLY.NO_SEARCH_RES</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Right half (6 columns) -->
        <div class="col-md-6 customDivContainer pl-3">
            <div class="customDivStyling parts-in-kit">
                <div class="customCardBody">
                    <h3 translate>KIT_ASSEMBLY.PARTS_IN_KIT</h3>
                </div>
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th translate>KIT_ASSEMBLY.PART_NUMBER</th>
                            <th translate>KIT_ASSEMBLY.DESC</th>
                            <th translate>KIT_ASSEMBLY.QUANTITY</th>
                            <th translate>KIT_ASSEMBLY.REMOVE</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr ng-repeat="part in kitAssemblyCtrl.kit.parts | orderBy:'partNumber'"">
                            <td data-label="{{'KIT_ASSEMBLY.PART_NUMBER' | translate}}">{{ part.partNumber }}</td>
                            <td data-label="{{'KIT_ASSEMBLY.DESC' | translate}}">{{ kitAssemblyCtrl.getDescription(part.descriptions, kitAssemblyCtrl.userLanguage) }}</td>
                            <td data-label="{{'KIT_ASSEMBLY.QUANTITY' | translate}}" class="quantity-input">
                                <input type="number" class="w-100 form-control" ng-model="part.quantity" min="1" ng-change="kitAssemblyCtrl.hasChanges = true" />
                            </td>
                            <td data-label="{{'KIT_ASSEMBLY.REMOVE' | translate}}">
                                <button class="btn secondary danger" ng-click="kitAssemblyCtrl.removeFromKit(part)">
                                    <i class="fa fa-trash fa-lg"></i>
                                </button>
                            </td>
                        </tr>
                        <tr ng-if="!kitAssemblyCtrl.kit.parts.length">
                            <td colspan="4" translate>KIT_ASSEMBLY.NO_PARTS_IN_KIT</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <!-- Actions -->
    <div class="kit-assembly-actions text-right">
        <button type="button" class="btn btn-secondary" ng-click="kitAssemblyCtrl.close()">{{'KIT_ASSEMBLY.CANCEL' | translate}}</button>
    <button type="button" class="btn btn-primary"
        ng-click="kitAssemblyCtrl.isEditMode ? kitAssemblyCtrl.saveKit() : kitAssemblyCtrl.createKit()"
        ng-disabled="kitAssemblyCtrl.isEditMode ? !kitAssemblyCtrl.hasChanges : (!kitAssemblyCtrl.partNumberExists || !kitAssemblyCtrl.partsInKit || !kitAssemblyCtrl.kit.partNumber)">
        {{ kitAssemblyCtrl.isEditMode ? ('KIT_ASSEMBLY.UPDATE' | translate) : ('KIT_ASSEMBLY.SAVE' | translate) }}
    </button>
    </div>
</div>

