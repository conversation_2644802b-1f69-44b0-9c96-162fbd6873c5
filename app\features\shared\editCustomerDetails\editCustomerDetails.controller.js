(function () {
    'use strict';

    angular
        .module('app.shared')
        .controller('EditCustomerDetailsController', EditCustomerDetailsController);

    EditCustomerDetailsController.$inject = ['$uibModalInstance', '$uibModal', '$stateParams', 'ordersService', 'customerDetailsObject', 'userService', '$state'];

    function EditCustomerDetailsController($uibModalInstance, $uibModal, $stateParams, ordersService, customerDetailsObject, userService, $state) {
        var vm = this;
        var isManufacturer = userService.isManufacturer();
        vm.addressCreationDisabled = !userService.getAddressCreationDefault();
        vm.cancel = $uibModalInstance.dismiss;
        vm.onBehalfOf = $stateParams.onBehalfOf && $stateParams.onBehalfOf !== "null" ? JSON.parse(decodeURIComponent(atob($stateParams.onBehalfOf))) : undefined;
        vm.isSupreme = userService.isSupreme();
        vm.userType = userService.getUserType();

        vm.isDealerPlusPage = isDealerPlusPage;
        vm.addNewAddress = addNewAddress;
        vm.addNewName = addNewName;
        vm.addNewNumber = addNewNumber;
        vm.saveEdits = saveEdits;
        vm.hideAddNewAddressButton = false;

        vm.updateContactId = updateContactId;
        vm.updateDeliveryId = updateDeliveryId;

        initialize();

        function initialize() {
            updateAddresses();
            updateNames();
            updateNumbers();
            determineAddNewAddressButtonStatus();

            if (customerDetailsObject.contactId) {
                vm.contactId = customerDetailsObject.contactId;
            }
            if (customerDetailsObject.deliveryId) {
                vm.deliveryId = customerDetailsObject.deliveryId;
            }
        }

        function updateContactId() {
            var selectedOption = document.querySelector('select[name="contactName"] option:checked');
            if (selectedOption) {
                vm.contactId = selectedOption.getAttribute('data-id');
            }
        }

        function updateDeliveryId() {
            var selectedOption = document.querySelector('select[name="deliveryName"] option:checked');
            if (selectedOption) {
                vm.deliveryId = selectedOption.getAttribute('data-id');
            }
        }

        function updateAddresses() {
            var userId = (isManufacturer || customerDetailsObject.isDealerPlus) ? customerDetailsObject.createdByUserId : (vm.onBehalfOf ? vm.onBehalfOf.userId : null);
            ordersService.getAddresses(userId)
                .then(getAddressesSuccess);
        }

        function updateNames() {
            var userId = (isManufacturer || customerDetailsObject.isDealerPlus) ? customerDetailsObject.createdByUserId : (vm.onBehalfOf ? vm.onBehalfOf.userId : null);
            var selectedAddress = vm.deliveryAddress ? JSON.parse(vm.deliveryAddress) : null;
            
            if (customerDetailsObject.contactName) {
                vm.contactName = customerDetailsObject.contactName;
            }
            if (customerDetailsObject.deliveryName) {
                vm.deliveryName = customerDetailsObject.deliveryName;
            }

            if (selectedAddress && selectedAddress.id) {
                ordersService.getAddressContacts(selectedAddress.id, userId)
                    .then(getNamesSuccess);
            } else {
                ordersService.getAddresses(userId)
                    .then(function(response) {
                        if (response.data && response.data.length > 0) {
                            var firstAddressId = response.data[0].id;
                            ordersService.getAddressContacts(firstAddressId, userId)
                                .then(getNamesSuccess);
                        } else {
                            // If no addresses exist, return empty array
                            getNamesSuccess({data: []});
                        }
                    });
            }
        }

        function updateNumbers() {
            var userId = (isManufacturer || customerDetailsObject.isDealerPlus) ? customerDetailsObject.createdByUserId : (vm.onBehalfOf ? vm.onBehalfOf.userId : null);
            ordersService.getNumbers(userId)
                .then(getNumbersSuccess);
        }

        function getAddressesSuccess(resp) {
            vm.addresses = resp.data;
            var index = _.findIndex(vm.addresses, {id: customerDetailsObject.billingAddressId});
            if (index > -1) {
                vm.newBillingAddress = JSON.stringify(vm.addresses[index]);
            }
            var index = _.findIndex(vm.addresses, {id: customerDetailsObject.shippingAddressId});
            if (index > -1) {
                vm.newDeliveryAddress = JSON.stringify(vm.addresses[index]);
            }
        }

        function getNamesSuccess(resp) {
            if (Array.isArray(resp.data)) {
                vm.contacts = resp.data;
            } else if (resp.data && Array.isArray(resp.data.contacts)) {
                vm.contacts = resp.data.contacts;
            } else {
                vm.contacts = [];
            }

            if (customerDetailsObject.contactName) {
                var orderIndex = _.findIndex(vm.contacts, {name: customerDetailsObject.contactName});
                console.log('Setting contactName:', customerDetailsObject.contactName, 'Available:', vm.contacts.map(c => c.name));
                if (orderIndex > -1) {
                    vm.contactName = vm.contacts[orderIndex].name; // ensure exact match
                    vm.contactId = vm.contacts[orderIndex].id;
                } else if (vm.contacts.length > 0) {
                    vm.contactName = vm.contacts[0].name;
                    vm.contactId = vm.contacts[0].id;
                }
            }

            if (customerDetailsObject.deliveryName) {
                var deliveryIndex = _.findIndex(vm.contacts, {name: customerDetailsObject.deliveryName});
                console.log('Setting deliveryName:', customerDetailsObject.deliveryName, 'Available:', vm.contacts.map(c => c.name));
                if (deliveryIndex > -1) {
                    vm.deliveryName = vm.contacts[deliveryIndex].name; // ensure exact match
                    vm.deliveryId = vm.contacts[deliveryIndex].id;
                } else if (vm.contacts.length > 0) {
                    vm.deliveryName = vm.contacts[0].name;
                    vm.deliveryId = vm.contacts[0].id;
                }
            }
        }

        function getNumbersSuccess(resp) {
            vm.numbers = resp.data;
            var index = _.findIndex(vm.numbers, {contactNumber: customerDetailsObject.contactNumber});
            if (index > -1) {
                vm.newContactNumber = vm.numbers[index].contactNumber;
            }
            var index = _.findIndex(vm.numbers, {contactNumber: customerDetailsObject.deliveryNumber});
            if (index > -1) {
                vm.newDeliveryNumber = vm.numbers[index].contactNumber;
            }
        }

        function addNewAddress() {
            var customerUserId = (isManufacturer || customerDetailsObject.isDealerPlus) ? customerDetailsObject.createdByUserId : null;
            $uibModal.open({
                templateUrl: 'features/shared/createNewAddress/createNewAddress.html',
                controller: 'CreateNewAddressController',
                controllerAs: 'createNewAddressCtrl',
                resolve: {
                    customerUserId: function () {
                        return customerUserId;
                    },
                    isRegisterMode: function () {
                        return false;
                    },
                    addressData: function() {
                        return null;
                    },
                    showCompanyName: function() {
                        return true;
                    }
                }
            }).result.then(function (resp) {
                updateAddresses();
            }, function () {
                console.log('Modal Cancelled');
            });
        }

        function addNewName() {
            var customerUserId = (isManufacturer || customerDetailsObject.isDealerPlus) ? customerDetailsObject.createdByUserId : null;
            $uibModal.open({
                templateUrl: 'features/shared/createNewName/createNewName.html',
                controller: 'CreateNewNameController',
                controllerAs: 'createNewNameCtrl',
                resolve: {
                    customerUserId: function () {
                        return customerUserId;
                    }
                }
            }).result.then(function (resp) {
                updateNames();
            }, function () {
                console.log('Modal Cancelled');
            });
        }

        function addNewNumber() {
            var customerUserId = (isManufacturer || customerDetailsObject.isDealerPlus) ? customerDetailsObject.createdByUserId : null;
            $uibModal.open({
                templateUrl: 'features/shared/createNewNumber/createNewNumber.html',
                controller: 'CreateNewNumberController',
                controllerAs: 'createNewNumberCtrl',
                resolve: {
                    customerUserId: function () {
                        return customerUserId;
                    }
                }
            }).result.then(function (resp) {
                updateNumbers();
            }, function () {
                console.log('Modal Cancelled');
            });
        }

        function saveEdits() {
            var orderDetails = {
                newDeliveryAddress: vm.newDeliveryAddress,
                newBillingAddress: vm.newBillingAddress,
                newContactName: vm.contactName,
                newDeliveryName: vm.deliveryName,
                newContactNumber: vm.newContactNumber,
                newDeliveryNumber: vm.newDeliveryNumber
            };

            ordersService.updateOrderDetails(customerDetailsObject.orderId, orderDetails)
                .then(saveEditsSuccess, saveEditFailure);
        }

        function saveEditsSuccess() {
            var editSuccessObject = {
                shippingAddress: vm.newDeliveryAddress,
                billingAddress: vm.newBillingAddress,
                contactName: vm.contactName,
                deliveryName: vm.deliveryName,
                contactNumber: vm.newContactNumber,
                deliveryNumber: vm.newDeliveryNumber,
                requestedDeliveryDate: customerDetailsObject.requestedDeliveryDate
            }
            $uibModalInstance.close(editSuccessObject);
        }

        function saveEditFailure(){
            console.error("save edit details failed");
        }

        function isDealerPlusPage(){
            return userService.isDealerPlusUser() && $state.current.name.includes("customerOrders");
        }

        function determineAddNewAddressButtonStatus() {
           
            var isFarmerUser = false;
            if (userService.isManufacturer() && vm.userType !== "DEALER" && vm.isSupreme) {
                isFarmerUser = true;
            } else if (userService.isFarmer() && vm.isSupreme && vm.userType !== "DEALER") {
                isFarmerUser = true;
            } else {
                isFarmerUser = false;
            }
            vm.hideAddNewAddressButton = (userService.getAddressCreationDefault() == false || isFarmerUser);
        }

    }
})();
