<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" ng-click="createNewNumberCtrl.cancel()"
            aria-label="Close"><i class="fa fa-close" aria-hidden="true"></i>
    </button>
    <h2 class="modal-title" id="myModalLabel" translate>CONTACT_NUMBER.ADD_NEW_NUMBER</h2>
</div>
<div class="modal-body">
    <form name="editCustomerDetailsForm" class="form">

        <div class="error-alert" ng-if="!editCustomerDetailsForm.$valid && !editCustomerDetailsForm.$pristine">
            <p class="errortext" translate>
                CONTACT_NUMBER.PLEASE_COMPLETE_ALL_FIELDS
            </p></div>

        <div class="input-group">
            <label>{{"CONTACT_NUMBER.NUMBER" | translate}} *</label>
            <input type="text" pattern="^\+?\d{8,15}$" ng-required="true" ng-model="createNewNumberCtrl.data.contactNumber" placeholder="{{'CONTACT_NUMBER.ENTER_NUMBER' | translate}}">
        </div>


        <div class="modal-actions">
            <button type="button" class="btn secondary" ng-click="createNewNumberCtrl.cancel()" translate>GENERAL.CANCEL</button> &nbsp;

            <button type="button" class="btn primary" ng-disabled="!editCustomerDetailsForm.$valid" ng-class="createNewNumberCtrl.isDealerPlusPage() ? 'dpGreenModal' : ''"
                    ng-click="createNewNumberCtrl.createNumber()" translate>CONTACT_NUMBER.CREATE_NUMBER
            </button>

        </div>

    </form>
</div>