// =============================================================================
// Viewer

// Prevent zoom/touch gestures on overlays and UI controls on mobile/touch devices
@media (hover: none) and (pointer: coarse) {
  #sidebar,
  #topBar,
  .overlay-box,
  .custViewerVertical_container,
  .cadSnapshotNotes,
  .viewer-notes-opened-vertical,
  .viewer-notes-closed-vertical,
  #SupersessionHistory,
  #part-search-container,
  .product-thumb-step,
  .VerticalThumbStepContainer,
  .custViewerVertical,
  .custViewerHorizontal,
  .loader,
  .partsPalletContainer,
  .pallet-container,
  .basket-container,
  .basket-content{
    touch-action: none;
  }
}


// =============================================================================
.loading-cursor {
    cursor: wait;
}

.loading-icon {
    position: absolute;
    pointer-events: none;
    z-index: 1000;
    font-size: 2em;
}

#SupersessionHistory {
    background-color: white; 
    border: 1px solid #ccc;
    display: flex;
    flex-direction: column;
    max-height: 65vh; 
    width: 300px;
    border-radius: 10px;

    .supersessionHistoryHeader {
        padding: 5px 10px;
        border-radius: 10px 10px 0px 0px;
        background-color: #f8f8f8;
        border-bottom: 1px solid #ccc;
        z-index: 5;
        flex-shrink: 0;
        
        h3 {
            font-size: 0.9em;
            font-weight: bold;
            margin-bottom: 0;
        }
    }

    ul {
        overflow-y: auto;
        max-height: 300px;
        padding: 0.5rem 0;
        list-style: none;
        padding-left: 0;
        margin-bottom: 0;
        flex-grow: 1;

        li {
            padding: 0 10px;
            display:flex;
            flex-direction: column;
            align-items: center;
            margin-bottom:5px;
            .supersessionViewerPart {
                background: #F8FBFD;
                padding: 5px;
                border-radius: 10px;
                border: solid 1px;
                border-color: #F2F6F9;
                display: flex;
                align-items: center;
                width: 100%;
            }
    
            span {
                flex-grow: 1;
            }
    
            i {
                margin-top: 5px;
                margin-bottom: 2px;
            }
        }
    }
}

// =============================================================================
// Side bars
// =============================================================================

/* Top bar styling */
#topBar {
    position: relative;
    top: 0;
    width: 100%;
    height: 5vh;
    background-color: white;
    color: black;
    border-bottom: 1px solid #D2D2D2;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
}

.top-bar-content {
    text-align: center;
}

/* Sidebar styling */
#sidebar {
    position: fixed;
    right: 0;
    min-width: 50px;
    height:100vh;
    background-color: white;
    border-left: 1px solid #D2D2D2;
    color: black;
    display: flex;
    z-index: 10;
    margin-left: auto;

    @media (max-width: 1200px) {
         width: 5vw;
    }
    
    @media (max-width: 768px) {
        width: 7vw;
    }

}

.customerViewer-sidebar-content {
    text-align: center;
}

/* Sidebar icon buttons */
.toggle-icon {
    cursor: pointer;
    color: black;
    font-size: 20px;
    transition: color 0.3s ease;
}

.toggle-icon:hover {
    color: #007bff;
}

.overlay-container {
    display: flex;
    align-items: flex-start;
    position: absolute;
    top: calc(0.2vh + 0.5rem);
    right: 0.2vw;
    z-index: 15;
    padding: 0.5rem;
    padding-top: 0;
    pointer-events: none;
}

.overlay-row {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
}

.overlay-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.overlay-inner-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.overlay-box {
    display: flex;
    align-items: start;
    justify-content: center;
    border-radius: 8px;
    width: 100%;
    gap: 0.5em;
    pointer-events: auto;
}

/* Toggle Sidebar Icon */

  .toggle-sidebar-icon {
    border: 2px solid #3392FC;
    color: black;
    background: white;
    border-radius: 7px;
}

.toggle-sidebar-icon.active {
    background-color: #3392FC;
    color: white;
    border-radius: 7px;
}

.basket-quantity {
   position: absolute;
   top: -8px;
   left: -8px;
   background: #3392FC;
   border-radius: 7px;
   color: white;
   font-size: 0.7em;
   padding: 2px;
   min-width: 15px;
}

#customerViewerId {

    .adsk-viewing-viewer .docking-panel.menu, 
    #guiviewer3d-toolbar, 
    .adsk-viewing-viewer .adsk-button-icon,
    #cadshare-toolbar, 
    .viewcubeWrapper,
    .adsk-viewing-viewer .modelStructurePanel,
    .adsk-viewing-viewer .propertyPanel,
    .adsk-viewing-viewer .toolbar,
    .adsk-viewing-viewer .contextMenu,
    .menu.docking-panel.docking-panel-container-solid-color-a,
    div[style*="position: absolute; z-index: 10;"] {
        display: none !important;
    }
    
    position: relative;
    width: calc(100vw - 50px); // This width includes padding due to box-sizing
    box-sizing: border-box;
    overflow: hidden;
    display: flex;
    font-size: initial;

    .customer_viewer{

        height: calc(100vh - 5vh); 

    }
            
    h1, h2, h3, h4, h5, h6, div {
        @media (max-width: 1500px) {
            font-size: 1.2rem;
        }
    
        @media (max-width: 768px) {
            font-size: 1rem;
        }
    
        @media (max-width: 576px) {
            font-size: 1rem;
        }
    }

    button {
        font-size:revert;
    }

    .minimize-basket-button {
        height: auto;
        position: relative;
        top: auto;
        z-index: 10;
        right: auto;
        width: auto;
    }

    .viewer-basket-plus h2,
    .viewer-basket-only h2 {
        padding: 1rem;
    }

    .product-viewer {

        &.horizontal {
            width: calc(100vw - 50px);
            height: 100%;
            z-index: 0;
        }

        &.vertical {
            
            width: calc(100vw - 50px);
            height:100%;
            right: 0px;
            z-index: 0;
        }

        .viewer {
            width: calc(100vw - 50px);
            height: 100%;
            position: fixed;
            z-index: 0;
        }

        .softCopyViewer {
            width: calc(100vw - 50px);
            height: 100%;
            position: absolute;
            z-index: 0;
        }
    }

    .minimize-basket-button {
        height: auto;
        position: relative;
        top: auto;
        z-index: 1;
        right: auto;
        width: auto;

        button {
            border: 0px;
            height: 100%;
        }
    }

.select-box {
    width: 100%;
    height: 38px;
}

.viewer-header {
    padding: 0;
    overflow: hidden;

    .back-to-products {
        .viewer-back-arrow {
            float: left;
            width: 60px;
            height: 60px;
            padding-top: 19px;
            text-align: center;
            font-size: 18px;
        }

        h3 {
            margin-bottom: 0px;
            padding-top: 9px;
            display: block;
            line-height: 1.2;
            max-width: initial;

            small {
                font-weight: 400;
            }
        }

        &:hover {
            small {
                text-decoration: underline;
                color: darken($blue, 10%);
            }

            .viewer-back-arrow {
                color: $blue;
            }
        }
    }
}

/***************
 * Button Elements *
 ***************/

 .backButton {
     position: absolute;
     left: 20px;
 }

 .emptySection {
     flex: 0 0 auto;
 }

.viewableName {
    flex: 1;
    text-align: center;
}

.placeOrderButton_CustomerViewer {
    display: block;
    text-align: center;
    margin: 0 auto;
    position: relative;
    bottom: 0;
    width: 100%;
    min-height: 75px;
    border-radius: 0;
    margin: 0 !important;
}

.textOverflow {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

.switchSnapshotButton {
    position: absolute;
    right: 1vw;
    bottom: 2.5vh;
    min-width: 60px;
    justify-content: center;
}

.explodetool {
    background-color: #007bff;
    position: absolute;
    right: 1vw;
    bottom: 9vh;
    min-width: 60px;
    min-height: 54px;
    justify-content: center;
    color: white;
    font-size: 25px;
    border-radius: 5px;
    border: 1px solid;
}

/* Pointer Events */

.pointerEventNone {
    pointer-events: none;
}

.pointerEventAuto {
    pointer-events: auto;
}

/* Viewer Banner */

.viewerBanner .header-banner {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    position: fixed;
    width: calc(100vw - 50px);
    z-index: 9;
    padding: 0;
    height: 7vh;
    font-weight: bold;
    left: 0;
    right: auto;
    bottom: 0;
    margin-left: auto;
    margin-right: auto;
    grid-gap: 1em;

    @media (max-width: 992px) {
        font-size: 1.2rem;
        width: calc(100vw - 5vw);
        height: 12vh
    }
}

.INFO {
    background: rgba(26, 133, 252, 0.9);
}

.product-info {
    background: white;
}

.product-thumb-step {
    cursor: pointer;
    background: white;
    width: 180px;
    height: auto;
    font-size: 0.7em;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 3px 3px rgba(0, 0, 0, 0.16), 0 3px 3px rgba(0, 0, 0, 0.23);
    transition: all 0.3s ease 0s;
    -moz-transition: all 0.3s ease 0s;
    -webkit-transition: all 0.3s ease 0s;
    position: relative;
}

.product-thumb-step:after {
    content: "\f107";
    font-family: FontAwesome;
    font-size: 1.1em;
    color: rgba($textdark, 0.5);
    position: relative;
    bottom: initial;
    left: initial;
}

.product-thumb-step-selected,
.product-thumb-step-selected-last {
    border: 3px solid black;
}

.custViewerHorizontal .product-thumb-step:after {
    transform: rotate(-90deg);
}

/* Reverse Angle Button */

.watermark-parent {
    margin-left: initial;
    overflow: hidden;
    z-index: 9;
    position: absolute;
    pointer-events: none;
    max-height: 100vh !important;
}

/* Supersession */

.part-number-container {
    display: flex;
    align-items: center;

    h4 {
        margin-right: 10px;
    }

    .part-link-icon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 25px;
        height: 25px;
        background-color: #007bff;
        border-radius: 50%;
        color: white;
        cursor: pointer;
        font-size: 1.4rem;
    }
}

// =============================================================================

/* Horizontal Carousel Style */

.custViewerHorizontal {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    margin-right: auto;
    margin-left: auto;
    min-height: 240px;
    max-width: calc(100% - 400px);
}

.custViewerHorizontal_header {
    position: relative;
    margin-bottom: 0.5rem;
}

.custViewerHorizontal_title i {
    font-weight: 400;
}

.custViewerHorizontal_btn {
    top: 0;
    background-color: white;
    border: 2px black solid;
    outline: none;
    color: black;
    opacity: 1;
    transition: background-color 350ms ease, color 350ms ease, opacity 350ms ease;

    svg {
        transform: rotate(90deg);
    }
}

.custViewerHorizontal_btn i {
    line-height: 44px;
    font-family: FontAwesome;
}

.custViewerHorizontal_btn:disabled {
    opacity: 0.4;
}

.custViewerHorizontal_btn:enabled:hover {
    background-color: #1da1f2;
    color: black;
}

.custViewerHorizontal_btn--prev {
    left: -40px;
    position: absolute;
    top: 0;
    bottom: 0;
    padding: 0.5em 1em;
    height: fit-content;
    margin-top: auto;
    margin-bottom: auto;
}

.custViewerHorizontal_btn--next {
    right: -40px;
    position: absolute;
    top: 0;
    bottom: 0;
    padding: 0.5em 1em;
    height: fit-content;
    margin-top: auto;
    margin-bottom: auto;
}

.custViewerHorizontal_container::-webkit-scrollbar {
    display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.custViewerHorizontal_container {
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */
    overflow-x: scroll;
    -webkit-overflow-scrolling: touch;
    background: rgba(0, 0, 0, 0.1);
}

.custViewerHorizontal_list {
    list-style: none;
    padding: 0;
    margin: 0;
    transition: transform 1s ease;
    display: flex;
}

.custViewerHorizontal_item {
    background-color: white;
    margin-bottom: 0.5em;
    margin-top: 0.5em;
    margin-left: 0.5em;
    cursor: pointer;
    box-shadow: 0 3px 3px rgba(0, 0, 0, 0.16), 0 3px 3px rgba(0, 0, 0, 0.23);
    padding: 0;
    min-width: 178px;
    max-width: 178px;
    border: solid 1.5px black;
}

.custViewerHorizontal_item:first-child {
    padding-top: 0;
}

.custViewerHorizontal_item_text {
    margin: 0;
    color: black;
    font-weight: 300;
}

.custViewerHorizontal_item_text a {
    color: black;
    text-decoration: none;
    word-break: break-all;
}

.custViewerHorizontal_item_date {
    color: black;
    font-weight: 300;
    font-size: 14px;
}

.custViewerHorizontal_item_img {
    position: relative;
    overflow: hidden;
}

.viewer-notes-opened-horizontal {
    position: absolute;
    top: 85px;
    left: 13px;
    width: 400px;
    background: #3392fc;
    color: #ffffff;
    border-radius: 5px;
    padding: 0.5em;
    z-index: 1;
}

.viewer-notes-closed-horizontal {
    position: absolute;
    top: 85px;
    left: 13px;
    background: $blue;
    width: 400px;
    padding: $spacing/1.5;
    color: $white;
    border-radius: $border-radius;
}

/* Vertical Carousel Style */

.custViewerVertical {
    position: absolute;
    margin: 0.5rem;
    width: 225px;

    .product-thumb-step:hover {
        margin-left: 5px;
    }
}

.custViewerVertical_header {
    position: relative;
    margin-bottom: 0.5rem;
}

.custViewerVertical_title i {
    font-weight: 400;
}

.custViewerVertical_btn {
    top: 0;
    background-color: white;
    border: 2px black solid;
    outline: none;
    color: black;
    opacity: 1;
    transition: background-color 350ms ease, color 350ms ease, opacity 350ms ease;
    width: 100%;

    svg {
        transform: rotate(90deg);
    }
}

.custViewerVertical_btn i {
    line-height: 44px;
    font-family: FontAwesome;
}

.custViewerVertical_btn:disabled {
    opacity: 0.4;
}

.custViewerVertical_btn:enabled:hover {
    background-color: #1da1f2;
    color: black;
}

.custViewerVertical_btn--prev {
    right: 60px;
}

.custViewerVertical_btn--next {
    right: 0;
}

.custViewerVertical_container,
#custViewerVertical_container {
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    background: rgba(0, 0, 0, 0.1);
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */
}

.custViewerVertical_container::-webkit-scrollbar {
    display: none;
}

.custViewerVertical_list,
#custViewerVertical_list {
    list-style: none;
    padding: 0;
    margin: 0;
    transition: transform 1s ease;
    max-height: 85vh;
}

.custViewerVertical_item {
    background-color: white;
    margin: 0.5em;
    cursor: pointer;
    box-shadow: 0 3px 3px rgba(0, 0, 0, 0.16), 0 3px 3px rgba(0, 0, 0, 0.23);
    border: solid 1.5px black;
}

.custViewerVertical_item:first-child {
    padding-top: 0;
}

.custViewerVertical_item_text {
    margin: 0;
    color: black;
    font-weight: 300;
}

.custViewerVertical_item_text a {
    color: black;
    text-decoration: none;
    word-break: break-all;
}

.custViewerVertical_item_date {
    color: black;
    font-weight: 300;
    font-size: 14px;
}

.custViewerVertical_item_img {
    position: relative;
    overflow: hidden;
    height: 140px;
}

/* Mobile responsive styles */
@media (max-width: 915px) {
    .custViewerVertical {
        width: 155px;
    }
    .cadSnapshotNotes {
        left: 160px!important;
    }
}

.viewer-notes-opened-vertical {
    z-index: 10;
    position: absolute;
    top: 0px;
    left: 200px;
    width: 400px;
    background: #3392fc;
    color: #ffffff;
    border-radius: 5px;
    padding: 0.5em;
}

.viewer-notes-closed-vertical {
    z-index: 10;
    position: absolute;
    top: 0px;
    left: 200px;
    background: $blue;
    width: 400px;
    padding: $spacing/1.5;
    color: $white;
    border-radius: $border-radius;
}

/* Snapshot Notes */

.cadSnapshotNotes {
    position: absolute;
    left: 230px;
    top: 0;
    z-index: 1;
    width: 200px;
    box-shadow: 0 4px 4px -2px rgba(0, 0, 0, 0.5);
    background: white;
}

.cadSnapshotNotes .card-header:after {
    font-family: FontAwesome;
    content: "\f068";
    display: flex;
    align-items: center;
}

.cadSnapshotNotes .card-header.collapsed:after {
    font-family: FontAwesome;
    content: "\f067";
    display: flex;
    align-items: center;
}

#cadSnapshotNotesHorizontal.cadSnapshotNotes {
    left: 5px;
    top: 5px;
    width: 250px;
}

/* Part Search*/

#part-search-container {
    display: flex;
    flex-direction: column;
    align-items: stretch;
    z-index: 1;
    font-family: Arial, sans-serif;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    padding: 10px;
    width: 300px;
    pointer-events: auto;
    position: relative;

    input[type="text"] {
        font-size: 16px;
        transform: scale(0.775);
        transform-origin: left;
        -webkit-text-size-adjust: 100%;
    }
    
}

.search-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.search-mode-toggle {
    font-size: 0.8em;
    color: #3392FC;
    text-decoration: none;
    text-align: end;
    
    &:hover {
        text-decoration: underline;
        color: #0056b3;
    }
}

#search-label {
    font-size: 1em;
    color: #333;
    font-weight: bold;
    margin-right: 10px;
    align-self: flex-start;
}

#search-box {
    display: flex;
    flex-grow: 1;
    align-items: center;
    border: 1px solid #cccccc;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
    width: 100%;
}

#partSearch {
    margin: 0;
    flex-grow: 1;
    padding: 5px 10px;
    font-size: 0.9em;
    border: none;
    outline: none;
}

#searchButton {
    background-color: #3392FC;
    color: white;
    border-radius: 8px;
    border: none;
    margin-left: 0.3em;
    padding: 7px 12px 7px 12px;
    transition: ease-in-out 0.2s;

    &:hover {
        background-color: #73b7ff;
    }
}

#searchIcon {

    padding-left: 10px;
    font-size: 1.4em;
    cursor: pointer;
    color:white;

}

   #clear-btn {
       font-size: 1em;
       position: absolute;
       right: 0;
       top: 50%;
       transform: translateY(-50%);
       background: none;
       border: none
   }

   /* Search results container */
   #search-results-container {
    position: relative; 
    background-color: white; 
    border: 1px solid #ccc;
    border-radius: 4px;
    margin-top: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);

    .search-results-header {
        display: flex;
        cursor: pointer;
        justify-content: space-between;
        align-items: center;
        padding: 5px 10px;
        background-color: #f8f8f8;
        border-bottom: 1px solid #ccc;

        .results-title {
            font-weight: bold;
            font-size: 0.9em;
        }

        .minimize-toggle {
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1.1em;
            padding: 0 5px;
        }
    }
}

   .search-results-dropdown {
       background-color: white;
       border: 1px solid #cccccc;
       border-radius: 0 0 10px 10px;
       box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
       max-height: 200px;
       overflow-y: auto;
       width: 100%;

       ul {
           list-style: none;
           padding: 0;
           margin: 0;

           li {
               padding: 8px 12px;
               cursor: pointer;
               border-bottom: 1px solid #f0f0f0;
               transition: background-color 0.2s ease;
               white-space: nowrap;
               overflow: hidden;
               text-overflow: ellipsis;
               display: flex;
               align-items: center;

               &:last-child {
                   border-bottom: none;
               }

               &:hover {
                   background-color: #f5f8ff;
               }
                
               &.selected-part {
                   background-color: #e0ecff;
                   border-left: 3px solid #3392FC;
               }

               .part-number {
                   font-weight: bold;
                   color: #3392FC;
               }

               .part-description {
                    display: inline-block;
                    max-width: 215px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    font-size: 0.9em;
                    color: #444;
                    margin-left: 5px;
                }

               .part-name {
                   font-size: 0.9em;
                   color: #666;
                   margin-left: 5px;
               }
           }
       }
   }

.no-results-message {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 5px;
    padding: 10px;
    margin-top: 10px;
    display: flex;
    align-items: center;
    color: #6c757d;
    font-size: 0.9em;
    
    i {
        margin-right: 8px;
        color: #ffc107;
    }
}

}

/* Rotate Device */

.rotate-device-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 9999;
    display: flex !important;
    justify-content: center;
    align-items: center;
    opacity: 1;
    pointer-events: all;
}

.rotate-device-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10000;
}

.phone-animation {
    width: 150px;
    height: 150px;
    position: relative;
    margin-bottom: 30px;
}

.phone {
    width: 80px;
    height: 130px;
    background: #fff;
    border-radius: 12px;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    animation: rotate 2s ease-in-out infinite;
    border: 3px solid #4a90e2;
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.7);
}

.phone-screen {
    position: absolute;
    top: 6px;
    left: 6px;
    right: 6px;
    bottom: 6px;
    background: #4a90e2;
    border-radius: 8px;
}

.rotate-text {
    font-size: 24px;
    margin: 20px 0 0 0;
    color: #fff;
    text-align: center;
    font-weight: bold;
    text-shadow: 0 0 8px rgba(255, 255, 255, 0.7);
    letter-spacing: 1px;
}

@keyframes rotate {

    0%,
    15% {
        transform: translate(-50%, -50%) rotate(0deg);
    }

    35%,
    65% {
        transform: translate(-50%, -50%) rotate(90deg);
    }

    85%,
    100% {
        transform: translate(-50%, -50%) rotate(0deg);
    }
}

@media (orientation: landscape) and (max-width: 992px) {
    #part-search-container, #SupersessionHistory {
        width:200px!important;
    }
    #SupersessionHistory {
        max-height: 50vh!important; 
    }
}

#searchResultsDropdown {
    overflow-y: auto; 
    max-height: 400px; 
    background-color: white; 
    border: 1px solid #ccc; 
    border-top: none; 
    position: absolute; 
    width: 100%; 
    z-index: 25; 

    ul {
        list-style: none;
        padding: 0;
        margin: 0;

        li {
            padding: 5px 10px;
            cursor: pointer;
            border-bottom: 1px solid #eee;

            &:last-child {
                border-bottom: none;
            }

            &:hover,
            &.selected-part {
                background-color: #f0f0f0;
            }

            .part-number {
                font-weight: bold;
            }

            .part-description {
                font-size: 0.9em;
                color: #555;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                display: inline-block; 
                max-width: 70%; 
            }
        }
    }
}

#part-search-container,
#SupersessionHistory,
.overlay-box { 
    pointer-events: auto;
}

.quantity-box {
    font-size: 16px!important;
}