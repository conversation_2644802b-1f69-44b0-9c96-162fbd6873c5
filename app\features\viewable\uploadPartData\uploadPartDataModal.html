<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" ng-click="uploadPartDataCtrl.cancel()" aria-label="Close">
        <i class="fa fa-close" aria-hidden="true"></i>
    </button>
    <h2 class="modal-title" translate>UPLOAD_PART.TITLE</h2>
</div>

<div class="modal-body">
    <form name="uploadDetailsForm" class="form">
        <p translate>UPLOAD_PART.UPLOAD_CSV</p>

        <div class="upload-box">
            <div>
                <i class="fa fa-upload"></i>
                <h4 class="file-uploader" translate>UPLOAD_PART.CHOOSE_FILE</h4>
                <input
                    type="file"
                    class="fileupload"
                    ng-click="$event = $event"
                    onchange="angular.element(this).scope().uploadPartDataCtrl.fileChanged(event)"
                    accept=".csv"
                />
            </div>
        </div>
        <p><emphasis>{{uploadPartDataCtrl.filename}}</emphasis></p>
        <p class="mb-0"><strong translate>UPLOAD_PART.PART_IDENTIFIER</strong></p>

        <p class="mb-4" translate>UPLOAD_PART.SPECIFY_COLUMN</p>

        <div class="row mb-4">
            <div class="col-6">
                <div class="select-box">
                    <select ng-model="uploadPartDataCtrl.nameColumn" ng-required="true"
                        ng-options="partColumn as partColumn for partColumn in uploadPartDataCtrl.headers">
                        <option value="" disabled selected translate>UPLOAD_PART.SELECT_FILENAME</option>
                    </select>
                    <div class="select-arrow"></div>
                </div>
            </div>
            <div class="col-6">
                <div class="select-box">
                    <select ng-model="uploadPartDataCtrl.objectIdColumn" ng-required="true"
                        ng-options="partColumn as partColumn for partColumn in uploadPartDataCtrl.headers">
                        <option value="" disabled selected translate>UPLOAD_PART.SELECT_OBJECT_ID</option>
                    </select>
                    <div class="select-arrow"></div>
                </div>
            </div>
        </div>

        <div ng-show="uploadPartDataCtrl.validationErrors" translate>UPLOAD_PART.SELECT_FILENAME_COLUMN</div>
        <p translate>UPLOAD_PART.USE_FOLLOWING</p>
        <table class="width-100 table table-bordered table-no-responsive">
            <thead>
                <th translate>UPLOAD_PART.PART_INFO</th>
                <th translate>UPLOAD_PART.FILE_COLUMN</th>
                <th translate>UPLOAD_PART.OVERWRITE</th>
            </thead>
            <tbody>
                <tr title="{{'UPLOAD_PART.PART_INFO' | translate}}">
                    <td translate>UPLOAD_PART.DESCRIPTION</td>
                    <td>
                        <div class="select-box" style="margin-top:0!important;">
                            <select
                                ng-model="uploadPartDataCtrl.descriptionColumn"
                                ng-required="false"
                                ng-options="partColumn as partColumn for partColumn in uploadPartDataCtrl.headers"
                            >
                                <option value="" disabled selected translate>UPLOAD_PART.SELECT_DESC</option>
                            </select>
                            <div class="select-arrow"></div>
                        </div>
                    </td>
                    <td class="checkbox-padding">
                        <input type="checkbox" ng-model="uploadPartDataCtrl.updateDescription" class="checkbox" />
                    </td>
                </tr>
                <tr title="{{'UPLOAD_PART.PART_INFO' | translate}}">
                    <td translate>UPLOAD_PART.PART_NUMBER</td>
                    <td>
                        <div class="select-box" style="margin-top:0!important;">
                            <select
                                ng-model="uploadPartDataCtrl.numberColumn"
                                ng-required="false"
                                ng-options="partColumn as partColumn for partColumn in uploadPartDataCtrl.headers"
                            >
                                <option value="" disabled selected translate>UPLOAD_PART.SELECT_PART_NUM</option>
                            </select>
                            <div class="select-arrow"></div>
                        </div>
                    </td>
                    <td class="checkbox-padding">
                        <input type="checkbox" ng-model="uploadPartDataCtrl.updateNumber" class="checkbox" />
                    </td>
                </tr>
                <!-- <tr>
                    <td>
                        <span translate>UPLOAD_PART.IS_SPARE_PART</span
                        ><sup translate>UPLOAD_PART.OPTIONAL</sup></td>
                    </td>
                    <td>
                        <div class="select-box">
                            <select
                                ng-model="uploadPartDataCtrl.sparePartIdentifier"
                                ng-required="false"
                                ng-options="partColumn as partColumn for partColumn in uploadPartDataCtrl.headers"
                            >
                                <option value="" disabled selected translate>UPLOAD_PART.SELECT_SPARE_IDENTIFIER</option>
                            </select>
                            <div class="select-arrow"></div>
                        </div>
                    </td>
                    <td><input type="checkbox" ng-model="uploadPartDataCtrl.updateSparePart" class="checkbox pull-center" /></td>
                </tr> -->
            </tbody>
        </table>
        <div class="modal-actions">
            <a class="btn small secondary" href="" ng-click="uploadPartDataCtrl.cancel()" translate>GENERAL.CANCEL</a>
            <button type="button" class="btn small primary" ng-click="uploadPartDataCtrl.uploadDetails()" translate>
                <!--<ng-disabled="!uploadPartDataCtrl.isAnyDropdownSelected()"> -->
                UPLOAD_PART.SUBMIT
            </button>
        </div>
    </form>
</div>

<div class="loader modal-loader" ng-show="uploadPartDataCtrl.loading">
    <div class="vertical-align loader modal-loader" id="loader">
        <div id="loader-text">
            <i class="fas fa-sync-alt fa-spin"></i>
            <p>{{"UPLOAD_PART.UPLOADING" | translate}}&hellip;</p>
            <p translate>UPLOAD_PART.FEW_MINS</p>
        </div>
    </div>
</div>
