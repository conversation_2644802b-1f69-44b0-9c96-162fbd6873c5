<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" ng-click="expandSupersessionCtrl.cancel()"
        aria-label="Close">
        <i class="fa fa-close" aria-hidden="true"></i>
    </button>
    <h2 class="modal-title" translate>MASTER_PART.SUPERSESSION_HISTORY</h2>
</div>

<section ng-if="!expandSupersessionCtrl.isLoadingSupersessionHistory && expandSupersessionCtrl.supersessionHistory.length > 0" 
    class="modal-body py-4 p-md-4">
    <div ng-repeat="part in expandSupersessionCtrl.supersessionHistory" class="supersessionPart">
        <div class="d-flex flex-column w-100">
            <p class="mb-0 text-center"
                ng-class="{'highlightCurrentPN': part.partNumber === expandSupersessionCtrl.partNumber, 'bg-white highlightCurrentPN': part.partNumber !== expandSupersessionCtrl.partNumber}">
                <a class="cadBlue" ng-href="#!/masterPart/{{ part.masterPartId }}" ng-click="expandSupersessionCtrl.closeModal()">
                    {{ part.partNumber }}
                </a>
                <span> - {{ part.partDescription }}</span>
            </p>
            <span ng-if="!$last" class="text-center my-3"><i class="fa fa-arrow-down cadBlue"></i></span>
        </div>
    </div>
</section>

<div class="modal-actions border-top p-4">
    <button class="btn small secondary" href="" ng-click="expandSupersessionCtrl.cancel()" translate>GENERAL.CLOSE</button>
    <button ng-click="expandSupersessionCtrl.removePartFromSupersession()"
        class="btn btn-danger" ng-disabled="expandSupersessionCtrl.isLoadingSupersessionHistory" translate>
        GENERAL.REMOVE
      </button>
</div>