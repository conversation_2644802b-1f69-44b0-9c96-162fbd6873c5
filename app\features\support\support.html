<section class="body-content">
    <div class="subtitle-holder">
        <h1 translate>SUPPORT.TITLE</h1>
        <p class="page-desc" translate>SUPPORT.SUBTITLE</p>
    </div>
</section>

<section class="body-content primary-panel">
    <div class="panel manufacturer-details pull-left col-sm-offset-1 col-sm-6">
        <form class="form" name="createMeetingForm" style= "padding: 2rem 1rem 2rem 1rem;">
            <div class="d-flex flex-column input-group">
                <label translate>SUPPORT.SELECT_MODELS</label>
                <div class="select-box" style= "padding: 4px 4px 3px 4px;margin: 2px;">
                    <select ts-select-fix ng-model="supportCtrl.selectedRangeId" ng-change="supportCtrl.rangeChanged(supportCtrl.selectedRangeId)"
                        ng-options="rangeValue.rangeId as rangeValue.name for rangeValue in supportCtrl.rangeValues" placeholder="{{'SUPPORT.SELECT_RANGE'| translate}}">
                        <option value="" disabled selected translate>SUPPORT.SELECT_RANGE</option>
                    </select>
                    <div class="select-arrow"></div>
                </div>
                <div class="select-box" style= "padding: 4px 4px 3px 4px;margin: 2px;">
                    <select ts-select-fix ng-model="supportCtrl.selectedMachineId" ng-change="supportCtrl.machineChanged(supportCtrl.selectedMachineId)"
                        ng-options="machine.machineId as machine.name for machine in supportCtrl.machines" placeholder="{{'SUPPORT.SELECT_MACHINE' | translate}}">
                        <option value="" disabled selected translate>SUPPORT.SELECT_MACHINE</option>
                    </select>
                    <div class="select-arrow"></div>
                </div>
                <div class="select-box" style= "padding: 4px 4px 3px 4px;margin: 2px;">
                    <select ts-select-fix ng-model="supportCtrl.selectedModelId" ng-change="supportCtrl.modelChanged(supportCtrl.selectedModelId)"
                        ng-options="model.modelId as model.modelName for model in supportCtrl.models" placeholder="{{'SUPPORT.SELECT_VIEWABLE' | translate}}">
                        <option value="" disabled selected translate>SUPPORT.SELECT_VIEWABLE</option>
                    </select>
                    <div class="select-arrow"></div>
                </div>
            </div>
        
            <div class="input-group">
                <label translate>SUPPORT.SELECT_USERS</label>
                <div class="select-box" style= "padding: 4px 4px 3px 4px;margin: 2px;">
                    <select ng-model="supportCtrl.selctedCustomerId" ng-change="supportCtrl.fetchUsers(supportCtrl.selctedCustomerId)"
                    ng-options="customer.customer_id as customer.label for customer in supportCtrl.customerList" ng-required="true"
                        placeholder="{{'SUPPORT.SELECT_COMPANY' | translate}}" >
                        <option value="" disabled selected translate>SUPPORT.SELECT_COMPANY</option>
                    </select>
                    <div class="select-arrow"></div>
                </div>
                
            </div>
            <div class="input-group">
                <div class="select-check-box multicheckbox" ng-required="true" ng-dropdown-multiselect options="supportCtrl.userList"
                     selected-model="supportCtrl.selectedUsers" extra-settings="supportCtrl.usersSelectSettings" translation-texts="supportCtrl.userList"
                     disabled="supportCtrl.isUserDropdownDisabled" events="supportCtrl.usersInvitedListEventListeners">
                </div>
            </div>
            <div class="input-group">
                <label translate>SUPPORT.INTERNAL_USERS</label>
                <div class="select-check-box multicheckbox" ng-required="true" ng-dropdown-multiselect options="supportCtrl.internalUserList"
                     selected-model="supportCtrl.selectedInternalUsers" extra-settings="supportCtrl.internalUsersSelectSettings" translation-texts="supportCtrl.internalUserList"
                     disabled="supportCtrl.isInternalUserDropdownDisabled"  events="supportCtrl.internalUsersInvitedListEventListeners">
                </div>
            </div>

            <div class="d-flex justify-content-end">

            <button type="button" class="btn small secondary mr-3" data-dismiss="modal"
                ng-click="supportCtrl.cancel()" translate>
                GENERAL.CANCEL
            </button>

            <button type="button" class="btn small primary"
                    ng-click="supportCtrl.startNewMeeting()" translate  ng-disabled= "supportCtrl.isStartMeetingDisabled">SUPPORT.START_MEETING
            </button>

            </div>

        </form>
    </div>

    <div class="manufacturer-details col-12 col-sm-5 pull-right">
        <h2 class="titletail"><span translate>SUPPORT.INVITE_LIST</span></h2>
        <div class="panel">
            <table>
                <tbody>
                    <tr ng-repeat="invitedUser in supportCtrl.invitedUsers">
                        <td style="padding: 6px 6px 6px 6px;">
                            {{invitedUser.name}}
                        </td>
                        <td style="padding: 6px 6px 6px 6px;">
                            {{invitedUser.companyName}}
                        </td>
                        <td></td>
                        <td></td>
                        <td class="pull-right" style="padding: 6px 6px 6px 6px;">
                            <a href="" class="btn outline" style="color:red"
                                ng-click="supportCtrl.removeUser(invitedUser)">✘</a>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</section>