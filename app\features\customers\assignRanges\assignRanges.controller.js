(function () {
    'use strict';

    angular
        .module('app.customer')
        .controller('AssignRangesController', AssignRangesController);

    AssignRangesController.$inject = ['assignRangeService', 'userService', '$stateParams', 'headerBannerService', '$translate', 'filterFilter'];

    function AssignRangesController(assignRangeService, userService, $stateParams, headerBannerService, $translate, filterFilter) {
        var vm = this;

        var CHANGES_SAVED, CHANGES_CANCELED;
        $translate(['ASSIGN_RANGES.CHANGES_SAVED', 'ASSIGN_RANGES.CHANGES_CANCELED'])
            .then(function (resp) {
                CHANGES_SAVED = resp["ASSIGN_RANGES.CHANGES_SAVED"];
                CHANGES_CANCELED = resp["ASSIGN_RANGES.CHANGES_CANCELED"];
            });

        vm.moveSelectedFromAvailableToAssigned = moveSelectedFromAvailableToAssigned;
        vm.moveSelectedFromAssignedToAvailable = moveSelectedFromAssignedToAvailable;
        vm.toggleSelectAllAvailable = toggleSelectAllAvailable;
        vm.toggleSelectAllAssigned = toggleSelectAllAssigned;
        vm.updateAllAvailableCheckbox = updateAllAvailableCheckbox;
        vm.updateAllAssignedCheckbox = updateAllAssignedCheckbox;
        vm.clearAssignedSearch = clearAssignedSearch;
        vm.clearAvailableSearch = clearAvailableSearch;
        vm.save = save;
        vm.cancel = cancel;

        initialize();

        function initialize() {
            vm.availableRanges = [];
            vm.assignedRanges = [];
            vm.areAllAssignedSelected = false;
            vm.areAllAvailableSelected = false;
            getAllRanges();
            vm.customerName = $stateParams.name;
        }

        function getAllRanges() {
            var manufacturerId = userService.getManufacturerId()
            assignRangeService.getRangesForManufacturer(manufacturerId)
                .then(getRangesForManufacturerSuccess);
        }

        function getRangesForManufacturerSuccess(response) {
            vm.availableRanges = response.data;
            getAssignedRangeIdsForSubEntity();
        }

        function getAssignedRangeIdsForSubEntity() {
            var subEntityId = $stateParams.subEntityId;
            assignRangeService.getAssignedRangeIdsForSubEntity(subEntityId)
                .then(getAssignedRangeIdsForSubEntitySuccess);
        }

        function getAssignedRangeIdsForSubEntitySuccess(response) {
            var rangeIds = response.data;

            for (var i = 0; i < rangeIds.length; i++) {
                var rangeId = rangeIds[i];
                moveBetweenListsById( rangeId, vm.availableRanges, vm.assignedRanges);
            }
        }

        function getSelectedRangeIdsFromList(list, filter) {
            var selectedRangeIds = [];
            var filterList = filterFilter(list, filter);
            for (var i = 0; i < filterList.length; i++) {
                if (filterList[i].selected === true) {
                    selectedRangeIds.push(filterList[i].rangeId);
                }
            }
            return selectedRangeIds;
        }

        function toggleSelectAllAvailable() {
            if(vm.areAllAvailableSelected){
                deselectAll(vm.availableRanges)
            }else{
                selectAll(vm.availableRanges)
            }
        }

        function toggleSelectAllAssigned() {
            if(vm.areAllAssignedSelected){
                deselectAll(vm.assignedRanges)
            }else{
                selectAll(vm.assignedRanges)
            }
        }

        function selectAll(list) {
            for (var i = 0; i < list.length; i++) {
                list[i].selected = true;
            }
        }

        function deselectAll(list) {
            for (var i = 0; i < list.length; i++) {
                list[i].selected = false;
            }
        }

        function updateAllAvailableCheckbox(){
            for (var i = 0; i < vm.availableRanges.length; i++) {
                if(vm.availableRanges[i].selected !== true){
                    vm.areAllAvailableSelected = false;
                    return;
                }
            }
            vm.areAllAvailableSelected = true;
        }

        function updateAllAssignedCheckbox(){
            for (var i = 0; i < vm.assignedRanges.length; i++) {
                if(vm.assignedRanges[i].selected !== true){
                    vm.areAllAssignedSelected = false;
                    return;
                }
            }
            vm.areAllAssignedSelected = true;
        }

        function getIndexOfRangeInList(rangeId, list) {
            var myRange = _.findWhere(list, {rangeId: rangeId});
            return list.indexOf(myRange);
        }

        function moveSelectedFromAvailableToAssigned() {
            var selectedRangeIds = getSelectedRangeIdsFromList(vm.availableRanges, vm.availableSearchValue);
            for (var i = 0; i < selectedRangeIds.length; i++) {
                var rangeId = selectedRangeIds[i];
                moveBetweenListsById(rangeId, vm.availableRanges, vm.assignedRanges);
                updateButtonAndCheckboxes();
            }
        }

        function moveBetweenListsById(rangeId, startList, endList) {
            var index = getIndexOfRangeInList(rangeId, startList);
            var rangeToMove = startList[index];
            startList.splice(index, 1);
            rangeToMove.selected = false;
            endList.push(rangeToMove);
        }

        function moveSelectedFromAssignedToAvailable() {
            var selectedRangeIds = getSelectedRangeIdsFromList(vm.assignedRanges, vm.assignedSearchValue);
            for (var i = 0; i < selectedRangeIds.length; i++) {
                var rangeId = selectedRangeIds[i];
                moveBetweenListsById(rangeId, vm.assignedRanges, vm.availableRanges);
                updateButtonAndCheckboxes();
            }
        }

        function updateButtonAndCheckboxes(){
            vm.isPageEdited = true;
            vm.areAllAssignedSelected = false;
            vm.areAllAvailableSelected = false;
        }

        function save() {
            var assignedRangeIds = [];
            for (var i = 0; i < vm.assignedRanges.length; i++) {
                assignedRangeIds.push(vm.assignedRanges[i].rangeId)
            }

            var subEntityId = $stateParams.subEntityId;
            assignRangeService.assignRangesToCustomer(subEntityId, assignedRangeIds)
                .then(assignSuccess, assignFailure)
        }

        function assignSuccess(){
            vm.isPageEdited = false;
            headerBannerService.setNotification('SUCCESS', CHANGES_SAVED, 5000);
        }

        function assignFailure(){
            headerBannerService.setNotification('ERROR', CHANGES_SAVED_FAILED, 5000);
        }

        function cancel() {
            initialize();
            headerBannerService.setNotification('INFO', CHANGES_CANCELED, 5000);
        }

        function clearAvailableSearch(){
            vm.availableSearchValue = "";
        }

        function clearAssignedSearch(){
            vm.assignedSearchValue = "";
        }

    }

})();
