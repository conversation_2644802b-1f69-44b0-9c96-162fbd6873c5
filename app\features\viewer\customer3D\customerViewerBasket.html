<section class="basket-container" ng-class="{'banner-active': $root.bannerNotificationDisplay}">
  <header class="basket-heading">
    <h3 class="mb-0">
      {{"CUST_VIEWER.BASKET" | translate}} ({{customerViewerCtrl.basketSize}} {{"CUST_VIEWER.ITEMS" | translate}})
    </h3>
  </header>
  <div class="basket-content" ng-if="customerViewerCtrl.hasOrdersAccess">
    <h4 class="pl-3 pt-3" ng-show="customerViewerCtrl.basketSize < 1" class="empty-header" translate>
      CUST_VIEWER.BASKET_IS_EMPTY
    </h4>
    <p class="p-3 mb-0" ng-show="customerViewerCtrl.manualPartsCount > 0" class="ml-8 mr-8">
      {{"CUST_VIEWER.THERE_ARE" | translate}}
      <strong>{{customerViewerCtrl.manualPartsCount}}</strong>
      {{"CUST_VIEWER.MANUALLY_ADDED_PARTS_NOT_DISPLAYED" | translate}}
    </p>
    <table class="table-no-responsive table-bordered" ng-hide="customerViewerCtrl.basket.length < 1">
      <thead>
        <tr>
          <th class="upper col-5 col-md-5 col-lg-4" translate>CUST_VIEWER.PART_NUMBER</th>
          <th class="upper col-4 col-md-4 col-lg-5" translate>CUST_VIEWER.PART_DESC</th>
          <th class="upper col-2" translate>CUST_VIEWER.QTY</th>
          <th class="col-1"></th>
        </tr>
      </thead>
      <tbody>
        <!-- Repeated Part Rows -->
        <tr
          ng-repeat="part in customerViewerCtrl.basket | filter: customerViewerCtrl.filterNoMasterPartKitId"
          class="basket-add-animation"
        >
          <td data-label="{{'CUST_VIEWER.PART_NUMBER' | translate}}">
            {{part.partNumber}}
          </td>
          <td data-label="{{'CUST_VIEWER.PART_DESC' | translate}}">
            {{part.description || part.partDescription}}
          </td>
          <td data-label="{{'CUST_VIEWER.QTY' | translate}}">
            <input
              type="number"
              class="quantity-box m-0"
              min="0"
              ng-model="part.quantity"
              ng-change="customerViewerCtrl.partUpdated(part)"
              ng-model-options="{debounce: 500}"
            />
          </td>
          <td data-label="{{'GENERAL.DELETE' | translate}}">
            <a href="" ng-click="customerViewerCtrl.removePart(part)" class="delete fa fa-trash"></a>
          </td>
        </tr>

        <tr
          ng-repeat-start="kit in customerViewerCtrl.basket | filter: customerViewerCtrl.filterMasterPartKitId"
          class="basket-add-animation hoverTableBG"
          ng-class="{'borderLeft': customerViewerCtrl.accordionStates[kit.kitId]}">
          <td ng-click="customerViewerCtrl.toggleKitsAccordion(kit.kitId)" title="{{kit.masterPartNumber}}" data-label="{{'CUST_VIEWER.PART_NUMBER' | translate}}"
            class="text-nowrap">
            <div class="d-inline-flex align-items-center w-100">
              <i class="fa"
                ng-class="customerViewerCtrl.accordionStates[kit.kitId] ? 'fa-chevron-up' : 'fa-chevron-down'"></i>
              <span class="text-truncate truncatePartNumber">{{kit.masterPartNumber}}</span>
            </div>
          </td>
          <td ng-click="customerViewerCtrl.toggleKitsAccordion(kit.kitId)" data-label="{{'CUST_VIEWER.PART_DESC' | translate}}">
            {{kit.description || kit.partDescription}}
          </td>
          <td data-label="{{'CUST_VIEWER.QTY' | translate}}">
            <input ng-model="kit.quantity" ng-change="customerViewerCtrl.kitUpdated(kit)" type="number" min="0"
              ng-model-options="{debounce: 500}" class="quantity-box m-0" style="min-width: 50px;" />
          </td>
          <td data-label="{{'GENERAL.DELETE' | translate}}">
            <a href="" ng-click="customerViewerCtrl.removeKitItem(kit)" class="delete fa fa-trash"></a>
          </td>
        </tr>
        <tr ng-repeat-end>
          <td class="p-0" colspan="100%">
            <div class="accordion-anim" ng-class="{'open': customerViewerCtrl.accordionStates[kit.kitId]}">
              <table class="table-no-responsive table-bordered">
                <tbody>
                  <tr ng-repeat="part in kit.parts" class="basket-add-animation blueTableBG borderLeft">
                    <td class="col-4" data-label="{{'CUST_VIEWER.PART_NUMBER' | translate}}">{{part.partNumber}}</td>
                    <td class="col-5" data-label="{{'CUST_VIEWER.PART_DESC' | translate}}">{{ customerViewerCtrl.getDescription(part) }}</td>
                    <td class="col-2" data-label="{{'CUST_VIEWER.QTY' | translate}}">{{part.quantity}}</td>
                    <td class="col-1"></td>
                  </tr>
                </tbody>
              </table>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <button type="button" class="btn primary m-3" ng-click="customerViewerCtrl.goToCreateEnquiry()"
    ng-hide="customerViewerCtrl.basketSize < 1" translate>
    CUST_VIEWER.PLACE_ORDER
  </button>
</section>