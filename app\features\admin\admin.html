<section class="m-5 px-4">
    <h1 translate>ADMIN.INTERNAL_USERS_HEADER</h1>
    <div translate>ADMIN.INTERNAL_USERS_SUBHEADER</div>
</section>

        <section class="responsiveContainer m-5">

            <div>

                <div class="success-alert" ng-if="adminCtrl.successMessage != ''">
                    {{adminCtrl.successMessage}}
                </div>

<div id="{{adminCtrl.isFixedHeader ? 'infiniteScrollFixedHeader' : 'infiniteScrollStaticHeader'}}"
    class="flex p-4 p-md-0">
<search-filter class="col-12 col-md-3" state-name="'admin'" on-search-change="adminCtrl.searchFilterChange()"
    value="adminCtrl.searchValue" placeholder-key="ADMIN.SEARCH_BY_NAME"></search-filter>

<button class="btn primary ml-auto mt-3 mt-md-0 mr-4 col-12 col-md-auto" href="" ng-click="adminCtrl.createUser()"
    translate>ADMIN.CREATE_BUTTON
</button>
</div>

<table class="table table-bordered">

    <thead>
    <tr>
        <th ng-class="adminCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'" ng-click="adminCtrl.admin_sort='firstName'; adminCtrl.sortReverse = !adminCtrl.sortReverse" translate>ADMIN.NAME</th>
        <th ng-class="adminCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'"
            ng-click="adminCtrl.admin_sort='emailAddress'; adminCtrl.sortReverse = !adminCtrl.sortReverse" translate>ADMIN.EMAIL</th>
        <th ng-class="adminCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'"
            ng-click="adminCtrl.admin_sort='userPermissions'; adminCtrl.sortReverse = !adminCtrl.sortReverse" translate>
            ADMIN.PERMISSIONS</th>
        <th ng-class="adminCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'"
            ng-click="adminCtrl.passwordSet='createdDate'; adminCtrl.sortReverse = !adminCtrl.sortReverse" translate>
            ADMIN.USER_STATUS</th>
        <th translate>GENERAL.ACTIONS</th>
    </tr>
    </thead>

    <tbody infinite-scroll="adminCtrl.loadMoreInfiniteScroll()" infinite-scroll-distance="3" infinite-scroll-disabled="adminCtrl.loadingInfiniteScrollData">
    <tr ng-repeat="user in adminCtrl.userList | orderBy:adminCtrl.admin_sort:adminCtrl.sortReverse | filter : adminCtrl.searchValue"
        ng-show="adminCtrl.userList.length > 0">
        <td data-label="{{'ADMIN.NAME' | translate}}">{{user.name}}</td>
        <td class="text-nowrap" data-label="{{'ADMIN.EMAIL' | translate}}">{{user.emailAddress}}</td>
        <td data-label="{{'ADMIN.PERMISSIONS' | translate}}"><span ng-repeat="permission in user.userPermissions">{{permission}}<span ng-if="!$last">, </span></span></td>
        <td data-label="{{'ADMIN.USER_STATUS' | translate}}">{{user.userStatus}}</td>

        <td>
            <split-button-dropdown main-action="adminCtrl.editUser(user)" main-action-label="{{'ADMIN.EDIT_USER' | translate}}"
                actions="adminCtrl.actions" entity="user">
            </split-button-dropdown>
        </td>
    </tr>

    <tr ng-show="!adminCtrl.userList.length > 0 && adminCtrl.isMachinesLoaded">
        <td colspan="5" translate>ADMIN.NO_USERS</td>
    </tr>

    <tr ng-hide="adminCtrl.areUsersLoaded" align="center">
        <td class="preloader" colspan="5"><img ng-src="images/cadpreloader.gif" class="ajax-loader" height="60"
                                               width="60"/></td>
    </tr>
    </tbody>
</table>

<span ng-click="adminCtrl.scrollToTop()" id="backToTopBtn" title="Go to top" class="fas fa-arrow-alt-circle-up"
    ng-show="adminCtrl.showBackToTopButton"></span>

    </tbody>
</table>

    </div>

</section>