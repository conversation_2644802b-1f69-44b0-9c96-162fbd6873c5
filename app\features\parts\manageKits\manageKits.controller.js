(function () {
    "use strict";

    angular.module("app.parts").controller("ManageKitsController", ManageKitsController);

    ManageKitsController.$inject = [
        "$scope",
        "$state",
        "kitService",
        "viewerBannerService",
        "userService",
        "$uibModal",
        "$translate",
        "manageKitService",
        "headerBannerService",
        "$window"
    ];

    function ManageKitsController(
        $scope,
        $state,
        kitService,
        viewerBannerService,
        userService,
        $uibModal,
        $translate,
        manageKitService,
        headerBannerService,
        $window
    ) {
        var vm = this;

        vm.endRecord = vm.itemPerPage;
        vm.loadingInfiniteScrollData = false;
        vm.isOrdersLoaded = false;
        vm.showBackToTopButton = false;

        vm.kits = [];
        vm.searchValue = "";
        vm.sortType = "partNumber";
        vm.sortReverse = false;
        vm.isKitsLoaded = false;

        vm.createKit = createKit;
        vm.editKit = editKit;
        vm.deleteKit = deleteKit;
        vm.sort = sort;
        vm.getSortClass = getSortClass;
        vm.scrollToTop = scrollToTop;
        vm.loadMoreInfiniteScroll = loadMoreInfiniteScroll;
        var manufacturerId;

        init();

        function init() {
            manufacturerId = userService.getManufacturerId();
        }

        vm.kitActions = [
            {
                title: $translate.instant("MANAGE_KITS.EDIT_KIT"),
                label: function () {
                    return $translate.instant("MANAGE_KITS.EDIT_KIT");
                },
                icon: "fa fa-pencil",
                onClick: function (kit) {
                    vm.editKit(kit.id);
                },
            },
            {
                title: $translate.instant("MANAGE_KITS.DELETE_KIT"),
                label: function () {
                    return $translate.instant("MANAGE_KITS.DELETE_KIT");
                },
                icon: "fa fa-trash",
                onClick: function (kit) {
                    vm.deleteKit(kit.id);
                },
            },
        ];

        function loadKits(manufacturerId) {
            manageKitService.fetchKitsToManage(manufacturerId).then(fetchKitsToManageSuccess, fetchKitsToManageFailed);
        }

        function fetchKitsToManageSuccess(response) {
            vm.userLang = localStorage.getItem("NG_TRANSLATE_LANG_KEY") || "EN";
            vm.kits = response.data.masterKits.map(function (kit) {
                if (kit.titles) {
                    var titleObj = kit.titles.find(function (title) {
                        return title.languageCode === vm.userLang;
                    });
                    kit.displayName = titleObj ? titleObj.translation : kit.description;
                } else {
                    kit.displayName = kit.description;
                }
                return kit;
            });
            vm.totalItems = vm.kits.length;
            vm.isKitsLoaded = true;
        }

        function fetchKitsToManageFailed(error) {
            console.log("Error while fetching kits: ", error);
            headerBannerService.setNotification("ERROR", "Error while fetching kits", 10000);
        }

        function createKit() {
            $state.go("kitassembly", { kitId: null });
        }

        function editKit(id) {
            $state.go("kitassembly", { kitId: id });
        }

        function deleteKit(id) {
            manageKitService
                .deleteKit(id)
                .then((response) => {
                    console.log(response.data);
                    loadKits(manufacturerId);
                })
                .catch((error) => {
                    console.error("Error deleting kit:", error);
                });
        }

        function sort(sortKey) {
            vm.sortType = sortKey;
            vm.sortReverse = !vm.sortReverse;
        }

        function getSortClass(sortKey) {
            if (vm.sortType === sortKey) {
                return vm.sortReverse ? "sortIconDown" : "sortIconUp";
            } else {
                return "sortIconDown";
            }
        }

        var lastScrollTop = 0;
        window.addEventListener('scroll', handleInfiniteScroll);

        function handleInfiniteScroll() {
            var threshold = 250;
            var scrollTop = window.scrollY;

            if (scrollTop > lastScrollTop) {
                vm.isFixedHeader = scrollTop > threshold;
            } else if (scrollTop < threshold) {
                vm.isFixedHeader = false;
            }
            lastScrollTop = scrollTop;


            if (window.innerHeight + scrollTop >= document.body.offsetHeight - 2 && !vm.loadingInfiniteScrollData && !vm.initialLoadDone) {
                loadMoreInfiniteScroll();
            }
        }

        function loadMoreInfiniteScroll() {
            vm.loadingInfiniteScrollData = true;

            var nextItems = vm.kits.slice(vm.kits.length, vm.kits.length + 100);
            if (nextItems.length === 0) {
                vm.initialLoadDone = true;
                vm.loadingInfiniteScrollData = false;
                return;
            }

            vm.orders = vm.orders.concat(nextItems);

            vm.loadingInfiniteScrollData = false;
            if (vm.kits.length >= vm.totalItems) {
                vm.initialLoadDone = true;
            }
        }

        function scrollToTop() {
            $window.scrollTo({ top: 0, behavior: "smooth" });
            $("html, body").animate({ scrollTop: 0 }, "slow", function () {
                $("#scrollToTop").removeClass("scrolled-past");
            });
        }

        angular.element($window).on("scroll", function () {
            vm.showBackToTopButton = this.pageYOffset > 100;
            $scope.$apply();
        });


        loadKits(manufacturerId);
    }
})();
