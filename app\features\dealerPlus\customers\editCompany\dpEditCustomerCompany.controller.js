(function () {
    'use strict';

    angular
        .module('app.customer')
        .controller('DPEditCustomerCompanyController', DPEditCustomerCompanyController);

    DPEditCustomerCompanyController.$inject = ['dpCreateUserService', '$uibModalInstance', 'companyDetails'];

    function DPEditCustomerCompanyController(dpCreateUserService, $uibModalInstance, companyDetails) {
        var vm = this;

        vm.cancel = $uibModalInstance.dismiss;
        vm.newCompanyName = companyDetails.name;
        vm.newDefaultDiscount = companyDetails.defaultDiscount;

        vm.editCompany = editCompany;

        function editCompany() {
            vm.isDisabled = true;

            dpCreateUserService.editManufacturerSubEntityCustomer(vm.newCompanyName, companyDetails.subEntityId, vm.newDefaultDiscount)
                .then(createCustomerSuccess, createCustomerFailed);
        }

        function createCustomerSuccess() {
            $uibModalInstance.close();
        }

        function createCustomerFailed(response) {
            vm.companyFailure = true;
            vm.isDisabled = false;
            vm.internalFailureMessage = response.data.message;
        }

    }
})();