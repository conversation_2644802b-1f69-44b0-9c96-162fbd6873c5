.messageBanner{
  width: 100%;
  left: 0px;

  .header-banner {
    padding: $spacing*2 $spacing*3;
    width: 100%;
    z-index: 14;
    font-weight: bold;

    button{
      margin-right:16px;
    }

    i{
      color:#ffffff;
      font-size:18px;
      margin-top:2px;
    }

    &.ERROR {
      background: lighten($red,35%);
      color: darken($red,10%);

      i{
        color: darken($red,10%);
      }
    }
    &.WARN {
      background: #fce8b5;
      color: #8a6d3b;

      i{
        color: #8a6d3b;
      }
    }
    &.SUCCESS {
      background: lighten($green,50%);
      color: darken($green,15%);

      i{
        color: darken($green,15%);
      }
    }
    &.INFO {
      background: lighten($blue,30%);
      color: darken($blue,15%);
    }

    i{
      //color: #ffffff!important;
    }
  }

}

.header-banner {
  padding: $spacing*2 $spacing*3;
  width: 100%;
  z-index: 14;
  font-weight: bold;

  button{
    margin-left:16px;
  }

  i{
    font-size:18px;
    margin-top:2px;
  }

  &.ERROR {
    background: lighten($red,35%);
    color: darken($red,10%);
  }
  &.WARN {
    background: #fce8b5;
    color: #8a6d3b;
  }
  &.SUCCESS {
    background: lighten($green,50%);
    color: darken($green,15%);
  }
  &.INFO {
    background: lighten($blue,30%);
    color:#ffffff!important;
  }
}

.viewerBanner{
  width: 100%;


  .header-banner {
    position: absolute;
    right: 0px;
    width: 100%;
    z-index: 9;
    padding: $spacing*2 $spacing*3;
    font-weight: bold;
    word-wrap: break-word;

    &.ERROR {
      background: lighten($red, 80%);
      color: $red;
    }
    &.WARN {
      background: lighten($orange, 80%);
      color: $orange;
    }
    &.SUCCESS {
      background: #cbf0bb;
      color: #3c763d;
    }
    &.INFO {
      background: #1a85fc;
      color: #FFFFFF;
    }
  }
  .meeting-banner{
    position: absolute;
    z-index: 9;
    width: auto;
    padding: $spacing $spacing;
    margin: $spacing*2 $spacing*3;
    background-color: white;
    font-weight: bold;
  }

}

