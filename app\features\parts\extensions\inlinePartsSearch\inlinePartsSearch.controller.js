(function () {
    'use strict';

    angular
        .module('app.parts')
        .controller('InlinePartsSearchController', InlinePartsSearchController);

    InlinePartsSearchController.$inject = ['masterPartService', 'userService', '$timeout', '$state', "$stateParams", 'basketService', '$translate', '$location', 'headerBannerService', '$scope'];

    function InlinePartsSearchController(masterPartService, userService, $timeout, $state, $stateParams, basketService, $translate, $location, headerBannerService, $scope) {
        var vm = this;
        vm.$onInit = onInit;
        vm.isCustomerOrder = isCustomerOrder;

        function onInit(){
            setCurrency();
        }

        vm.sortReverse = false;
        vm.searchBy = 'partNumber';
        vm.searching = false;
        vm.isLoading = false;
        vm.showSupersession = false;
        vm.masterParts = [];
        vm.kits = [];
        vm.accordionStates = [];
        vm.accordionSupersession = [];
        vm.isOnOptionSetPage = $location.path().includes('parts/optionSet');
        vm.isOnAdditionalPartPage = $location.path().includes('parts/additionalPart');
        vm.isPreviewStockLevelEnabled = userService.getPreviewStockLevelEnabled();
        vm.previewPricingEnabled = userService.getPreviewPricingEnabled();
        vm.hidePrice = false;
        vm.isSearchOnlyPartNumbers = userService.getSearchOnlyPartNumbers();
        vm.isDealerPlusCustomer = userService.isManufacturerSubEntity() && userService.isDealerPlusCustomer();
        vm.isManufacturerSubEntity = userService.isManufacturerSubEntity();
        vm.isStockWarehousesEnabled = userService.getStockWarehousesEnabled();
        vm.hasOrdersAccess = userService.hasOrderRole();
        vm.isManufacturer = userService.isManufacturer();

        vm.search = search;
        vm.addPart = addPart;
        vm.addKit = addKit;
        vm.isDealerPlusPage = isDealerPlusOrderPage;
        vm.toggleKitsAccordion = toggleKitsAccordion;
        vm.fetchAndDisplayPurchaserKitDetails = fetchAndDisplayPurchaserKitDetails;
        vm.fetchAndDisplayManufacturerKitDetails = fetchAndDisplayManufacturerKitDetails;
        vm.addKitToBasket = addKitToBasket;
        vm.noPartsFound = noPartsFound;
        vm.noKitsFound = noKitsFound;
        vm.partsReturned = partsReturned;
        vm.kitsReturned = kitsReturned;
        vm.toggleSupersessionHistory = toggleSupersessionHistory;
        vm.fetchSupersessionHistory = fetchSupersessionHistory;
        vm.toggleSupersessionAccordion = toggleSupersessionAccordion;
        vm.hidePrice = hidePrice;

        initialize();

        function initialize() {
            hidePrice();
        }

        function addPart(part) {
            var partToAdd = angular.copy(part); // Create a copy to avoid affecting the UI

            if (partToAdd.quantity > 0) {
                // Check if the part is in supersession
                if (partToAdd.inSupersession) {
                    partToAdd.partNumber = partToAdd.maxSupersessionPartNumber;
                    partToAdd.masterPartId = partToAdd.maxSupersessionPartId;
          
                    if (partToAdd.supersessionDetails && partToAdd.supersessionDetails.length > 0) {
          
                      var currentSupersessionPart = partToAdd.supersessionDetails[0];
          
                      if (currentSupersessionPart.description && currentSupersessionPart.description.trim() !== "") {
                        partToAdd.description = currentSupersessionPart.description;
                      } else {
                        partToAdd.description = "";
                      }
          
                      if (currentSupersessionPart.price) {
                        partToAdd.price = currentSupersessionPart.price;
                      } else {
                        partToAdd.price = null;
                      }
                    }
                  }

                // Set machine name if not already set
                if (partToAdd.machineName === null || partToAdd.machineName === undefined) {
                    partToAdd.machineName = "Part Search";
                }

                part.clicked = true;
                $timeout(function () {
                    part.clicked = false;
                }, 500);

                // Add to basket
                vm.onAddClicked({ masterPart: partToAdd, masterKit: null });
            } else {
                headerBannerService.setNotification("WARN", ENTER_QUANTITY, 3000);
            }
        }

        function addKit(part) {
            console.log('Adding part: ', part);
            part.clicked = true;
            $timeout(function () {
                part.clicked = false;
            }, 500);
            vm.onAddClicked({ masterPart: null, masterKit: part})
        }

        function search() {
            vm.isLoading = true;
            vm.hasSearchError = false;
            sessionStorage.setItem('inlinePartSearchValue', vm.searchValue);
            var onBehalfOf =
            $stateParams.onBehalfOf && $stateParams.onBehalfOf !== "null"
                ? JSON.parse(decodeURIComponent(atob($stateParams.onBehalfOf)))
                : undefined;

            var onBehalfOfUserId = userService.getOnBehalfOfUserId();

            vm.masterParts = [];
            vm.accordionSupersession = [];
            vm.resultsReturned = false;
            vm.searching = true;

            var partNumber = vm.searchBy === 'partNumber' ? vm.searchValue : null;
            var partDescription = vm.searchBy === 'partDescription' ? vm.searchValue : null;

            var onBehalfOf =
            $stateParams.onBehalfOf && $stateParams.onBehalfOf !== "null"
                ? JSON.parse(decodeURIComponent(atob($stateParams.onBehalfOf)))
                : undefined;

            var manufacturerSubEntityId = onBehalfOf ? onBehalfOf.manufacturerSubEntityId : userService.getManufacturerSubEntityId();

            if (manufacturerSubEntityId !== null && manufacturerSubEntityId !== undefined) {
                var kitSearchPromise = masterPartService.purchaserKitSearch(manufacturerSubEntityId, partNumber, partDescription)
                    .then(searchKitSuccess);

                var partSearchPromise = masterPartService.subEntityPartSearch(manufacturerSubEntityId, partNumber, partDescription, onBehalfOfUserId)
                    .then(searchSuccess, searchFailed);

                Promise.all([kitSearchPromise, partSearchPromise]).then(function () {
                    $timeout(function () {
                        vm.searching = false;
                        vm.resultsReturned = true;
                        vm.isLoading = false;
                    });
                });
            } else {
                var manufacturerId = userService.getManufacturerId();
                
                var partSearchPromise = masterPartService.manufacturerPartSearch(manufacturerId, partDescription, partNumber)
                    .then(searchSuccess, searchFailed);

                var kitSearchPromise = masterPartService.manufacturerKitSearch(manufacturerId, partNumber, partDescription)
                    .then(searchKitSuccess);

                Promise.all([partSearchPromise, kitSearchPromise]).finally(function () {
                    $timeout(function () {
                        vm.searching = false;
                        vm.resultsReturned = true;
                        vm.isLoading = false;
                    });
                });
            }
        }

        function searchKitSuccess(resp) {
            vm.kits = resp.data.kits !== null ? resp.data.kits : [];
            console.log("Kits search result:", vm.kits);
        }

        function searchSuccess(resp) {
            vm.masterParts = resp.data.masterParts !== null ? resp.data.masterParts : [];
            vm.searching = false;
            vm.resultsReturned = true;
            setDefaultMasterPartsQuantity();
            quantityUpdated();
        }

        function setDefaultMasterPartsQuantity() {
            for (var i = 0; i < vm.masterParts.length; i++) {
                vm.masterParts[i].quantity = 1;
            }
        }

        function searchFailed() {
            vm.hasSearchError = true;
            vm.searching = false;
        }

        function isDealerPlusOrderPage() {
            return userService.isDealerPlusUser() && $state.current.name.includes("customerOrders");
        }

        function isDealerPlusCreateOrderPage() {
            return userService.isDealerPlusUser() && $state.current.name.includes("create");
        }

        function quantityUpdated() {
            for (var i = 0; i < vm.masterParts.length; i++) {
                vm.masterParts[i].totalPrice = vm.masterParts[i].price ?
                    vm.masterParts[i].price * vm.masterParts[i].quantity : "TBC";
            }

            for (var j = 0; j < vm.kits.length; j++) {
                if (vm.kits[j].quantity === undefined) {
                    vm.kits[j].quantity = 1;
                }
                vm.kits[j].totalPrice = vm.kits[j].price && vm.kits[j].price.value ?
                    vm.kits[j].price.value * vm.kits[j].quantity : "TBC";
            }
        }

        function setCurrency() {
            vm.defaultCurrency = userService.getDefaultCurrency();
            var isDealerPlus = isDealerPlusOrderPage() || isDealerPlusCreateOrderPage();
            if (userService.isOnBehalfOf()) {
                userService.getCurrencyForUser(userService.getOnBehalfOfUserId(), isDealerPlus)
                    .then(function (response) {
                        vm.defaultCurrency = response.data;
                    })
            }
            if (vm.subEntityId) {
                userService.getCurrencyForSubEntity(vm.subEntityId, isDealerPlusOrderPage())
                    .then(function (response) {
                        vm.defaultCurrency = response.data;
                    })

            }
        }

        function isCustomerOrder() {
            return $state.current.name.includes("customerOrders");
        }

        function toggleKitsAccordion(kitId, index, $event) {
            if ($event.target.tagName !== 'INPUT' && $event.target.tagName !== 'BUTTON' && $event.target.tagName !== 'I') {
                if (vm.accordionStates[index] === undefined) {
                    vm.accordionStates[index] = false;
                }
                vm.accordionStates[index] = !vm.accordionStates[index];
                if (vm.isManufacturerSubEntity) {
                    fetchAndDisplayPurchaserKitDetails(kitId, index);
                } else {
                    fetchAndDisplayManufacturerKitDetails(kitId, index);
                }
            }
        }

        function fetchAndDisplayPurchaserKitDetails(kitId, index) {
            var purchaserId = userService.getManufacturerSubEntityId();
            masterPartService.getPurchaserKit(purchaserId, kitId)
                .then(function (response) {
                    if (response.data && response.data.parts) {
                        var kit = vm.kits[index];
                        if (kit) {
                            kit.kitDetails = response.data.parts;
                        }
                    }
                })
                .catch(function (error) {
                    console.log(error);
                });
        }
      
          function toggleSupersessionHistory(selectedPart) {
            var isAlreadyOpen = selectedPart.showSupersessionHistory;
            vm.masterParts.forEach(function (part) {
              part.showSupersessionHistory = false;
            });
            if (!isAlreadyOpen) {
              selectedPart.showSupersessionHistory = true;
              fetchSupersessionHistory(selectedPart.maxSupersessionPartNumber, selectedPart.masterPartId);
            }
          }
      
          vm.masterParts.forEach(function () {
            vm.showSupersessionHistory = false;
            fetchSupersessionHistory
          });
      
          function fetchSupersessionHistory(masterPartNumber, masterPartId) {
            console.log("Fetching supersession history for part:", masterPartNumber);
        
            if (!masterPartNumber) {
                console.error("No masterPartNumber provided for fetching supersession history.");
                return;
            }
        
            vm.isSupersessionLoading = true;
            var currentLanguage = $translate.use();
            
            var manufacturerId = userService.getManufacturerId();
            var purchaserId = userService.getManufacturerSubEntityId();
            var promise;
            
            
            if (manufacturerId && !purchaserId) {
                promise = masterPartService.getSupersessionHistory(manufacturerId, masterPartId);
            } else {
                promise = masterPartService.getSupersessionHistoryForPurchaser(purchaserId, masterPartNumber);
            }
        
            promise
                .then(response => {
                    var history = response.data.supersessionHistory.sort((a, b) => b.supersessionIndex - a.supersessionIndex);
                    
                    if (!history || history.length === 0) {
                        console.error("No supersession history data found in the response.");
                        vm.hasError = true;
                    } else {
                        vm.supersessionHistory = history.map(item => {
                            let matchingDescription = "No description available";
                            if (item.languageDescriptions && item.languageDescriptions.length > 0) {
                                for (let desc of item.languageDescriptions) {
                                    if (desc.code === currentLanguage) {
                                        matchingDescription = desc.description;
                                        break;
                                    }
                                }
                            }
                            return {
                                masterPartId: item.masterPartId,
                                partNumber: item.partNumber,
                                partDescription: matchingDescription,
                                supersedingPartNumber: item.supersedingPartNumber
                            };
                        });
        
                        vm.hasError = false;
                        console.log("Supersession history fetched successfully:", vm.supersessionHistory);
                    }
                })
                .catch(error => {
                    console.error("Error fetching supersession history:", error);
                    vm.hasError = true;
                })
                .finally(() => {
                    vm.isSupersessionLoading = false;
                    $timeout(function() {});
                });
        }
      
          angular.element(document).on('click', function (event) {
            $scope.$apply(function () {
              var tooltipContainers = document.querySelectorAll('.tooltip-container');
              var clickedInsideTooltip = false;
      
              tooltipContainers.forEach(function (container) {
                if (container.contains(event.target)) {
                  clickedInsideTooltip = true;
                }
              });
      
              if (!clickedInsideTooltip) {
                vm.masterParts.forEach(function (part) {
                  if (part.showSupersessionHistory) {
                    part.showSupersessionHistory = false;
                  }
                });
              }
            });
          });
          function toggleSupersessionAccordion(maxSupersessionPartNumber, index) {
            toggleAccordionState(index);
            fetchAndUpdateMasterPartDetails(maxSupersessionPartNumber, index);
          }
      
          function toggleAccordionState(index) {
            vm.accordionSupersession[index] = !vm.accordionSupersession[index];
            $timeout(function () { });
          }
      
          function fetchAndUpdateMasterPartDetails(maxSupersessionPartNumber, index) {
            var purchaserId = userService.getManufacturerSubEntityId();
            var onBehalfOfUserId = vm.onBehalfOfUserId;
            var isExactMatch = true;
      
            var searchPromise;
            if (purchaserId !== null && purchaserId !== undefined) {
                searchPromise = masterPartService.subEntityPartSearch(purchaserId, maxSupersessionPartNumber, null, onBehalfOfUserId, isExactMatch);
            } else {
                searchPromise = masterPartService.manufacturerPartSearch(userService.getManufacturerId(), null, maxSupersessionPartNumber, isExactMatch);
            }

            searchPromise
                .then((searchResponse) => {
                    console.log('Received search response for supersession:', searchResponse);
                    var supersessionParts = searchResponse.data.masterParts || [];
                    console.log('Supersession parts:', supersessionParts);

                    if (supersessionParts.length > 0) {
                        vm.masterParts[index].supersessionDetails = supersessionParts;
                    }
                })
                .catch((error) => {
                    console.error('Error fetching master part details:', error);
                });
          }

        function fetchAndDisplayManufacturerKitDetails(kitId, index) {
            var manufacturerId = userService.getManufacturerId();
            masterPartService.getManufacturerKit(manufacturerId, kitId)
                .then(function (response) {
                    if (response.data && response.data.parts) {
                        var kit = vm.kits[index];
                        if (kit) {
                            kit.kitDetails = response.data.parts;
                        }
                    }
                })
                .catch(function (error) {
                    console.log(error);
                });
        }

        function addKitToBasket(index) {
            var kitToAdd = vm.kits[index];
            var purchaserId = userService.getManufacturerSubEntityId();
            masterPartService.getPurchaserKit(purchaserId, kitToAdd.id)
                .then(function (response) {
                    if (response.data && response.data.parts) {
                        kitToAdd.kitDetails = response.data.parts;

                        if (kitToAdd.quantity > 0) {
                            var kitItem = {
                                masterPartNumber: kitToAdd.partNumber,
                                kitPrice: (kitToAdd.price && typeof kitToAdd.price === 'object') ? kitToAdd.price.value : null,
                                kitId: kitToAdd.id,
                                description: kitToAdd.description,
                                modelId: kitToAdd.modelId ? kitToAdd.modelId.toString() : null,
                                quantity: kitToAdd.quantity,
                                parts: kitToAdd.kitDetails,
                                masterPartKitId: kitToAdd.masterPartKitId,
                                stock: kitToAdd.stock,
                                machineName: kitToAdd.machineName || "Kit Search",
                                modelName: kitToAdd.modelName
                            };

                            basketService.addKit(kitItem);
                            vm.kits[index].clicked = true;

                            $timeout(function () {
                                vm.kits[index].clicked = false;
                            }, 500);
                        } else {
                            headerBannerService.setNotification("WARN", ENTER_QUANTITY, 3000);
                        }
                    }
                })
                .catch(function (error) {
                    console.log("Error fetching kit details:", error);
                });
        }
        function noKitsFound() {
            return vm.resultsReturned && !vm.hasSearchError && !vm.kits.length;
        }
        
        function noPartsFound() {
            return vm.resultsReturned && !vm.hasSearchError && !vm.masterParts.length;
        }
        
        function kitsReturned() {
            return vm.resultsReturned && !vm.hasSearchError;
        }
        
        function partsReturned() {
            return vm.resultsReturned && !vm.hasSearchError;
        }
        
        function hidePrice() {
            if (vm.isManufacturer) {
                vm.hidePrice = false;
            } else {
                vm.hidePrice = !userService.getPreviewPricingEnabled();
            }
            return vm.hidePrice;
        }

    }
})();