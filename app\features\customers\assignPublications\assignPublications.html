<section class="m-5">

<div class="mb-5">

    <div>

        <h1 class="">{{'ASSIGN_PUBLICATIONS.ASSIGNED_FOR' | translate}} {{assignPublicationsCtrl.customerName}}</h1>
        <p class="mb-0 " translate>ASSIGN_PUBLICATIONS.DESCRIPTION</p>

    </div>

</div>

<div class="d-flex justify-content-between align-items-center flex-wrap listBoxContainer">

    <div class="col-12 col-md-5 p-5 listBoxLeft d-flex flex-column">

        <h3 class="" translate>ASSIGN_PUBLICATIONS.AVAILABLE</h3>

        <ul class="list-unstyled">

            <div ng-click="assignPublicationsCtrl.areAllAvailableSelected" ng-class="assignPublicationsCtrl.areAllAvailableSelected ? 'assignlistboxHoverCustom' : 'assignlistboxStyleCustom'" class="px-3 px-lg-5">

                <div class="col-12">

                    <div class="d-flex justify-content-between align-items-center text-container">

                        <input title="Select All" type="checkbox" ng-model="assignPublicationsCtrl.areAllAvailableSelected" ng-click="assignPublicationsCtrl.toggleSelectAllAvailable()" class="mb-0 mr-5 checkbox">

                        <div class="input-group col mb-0">
                            <input ng-model="assignPublicationsCtrl.availableSearchValue" type="search"
                                   class="form-control h-100 p-3 mr-0 ng-pristine ng-valid ng-empty ng-touched" name="SearchDualList"
                                   placeholder="{{'ASSIGN_PUBLICATIONS.SEARCH' | translate}}">
                            <span ng-if="assignPublicationsCtrl.availableSearchValue != null" ng-click="assignPublicationsCtrl.clearAvailableSearch()" class="search-clear"><i class="fas fa-times"></i></span>
                            <div class="input-group-append">
                                <span class="input-group-text"><i class="fa fa-search"></i></span>
                            </div>
                        </div>

                    </div>

                </div>

            </div>

            </label>

        </ul>

        <ul class="list-group list-unstyled">

            <div href="" ng-class="publication.selected ? 'assignlistboxHover' : 'assignlistboxStyle'" ng-click="publication.selected = !publication.selected; assignPublicationsCtrl.updateAllAvailableCheckbox()" class="assignlistboxStyle py-3 px-3 py-lg-4 px-lg-5 mb-3" ng-repeat="publication in assignPublicationsCtrl.availablePublications | filter : assignPublicationsCtrl.availableSearchValue">

                <div class="d-flex col-12 justify-content-between align-items-center">

                    <div class="d-flex justify-content-between align-items-center text-container">

                        <input type="checkbox" ng-model="publication.selected" class="mb-0 mr-5 checkbox">

                        <p class="mb-0"><strong> {{publication.name}} </strong><br>
                            <span class="badge-pill"
                                  ng-class="publication.published ? 'primary' : 'secondary'">
                                {{publication.published ? ('PUBLICATIONS.PUBLISHED' | translate) : ('PUBLICATIONS.UNPUBLISHED' | translate)}}
                            </span>
                        </p>

                    </div>

                    <i ng-click="availablePublicationsCtrl.profileOpen = !availablePublicationsCtrl.profileOpen; $event.stopPropagation()" class="info_listbox fas fa-info"></i>

                </div>

                <div ng-show="availablePublicationsCtrl.profileOpen"
                     class="listbox_accordion d-flex col-12 justify-content-between align-items-center">
                    <ul class="mb-0 list-unstyled">
                        <li ng-if="publication.viewables && publication.viewables.length > 0"><strong>Viewables</strong></li>
                        <li ng-if="publication.viewables && publication.viewables.length > 0"><span
                                ng-repeat="viewable in publication.viewables">{{viewable.name}}<span
                                ng-if="!$last">, </span></span></li>
                        <li ng-if="publication.kits && publication.kits.length > 0"><strong>Kits</strong></li>
                        <li ng-if="publication.kits && publication.kits.length > 0"><span ng-repeat="kit in publication.kits">{{kit.name}}<span
                                ng-if="!$last">, </span></span></li>
                        <li ng-if="publication.techDocs && publication.techDocs.length > 0"><strong>Tech Docs</strong></li>
                        <li ng-if="publication.techDocs && publication.techDocs.length > 0"><span
                                ng-repeat="techDoc in publication.techDocs">{{techDoc.name}}<span
                                ng-if="!$last">, </span></span></li>
                        <li ng-if="publication.videos && publication.videos.length > 0"><strong>Videos</strong></li>
                        <li ng-if="publication.videos && publication.videos.length > 0"><span ng-repeat="video in publication.videos">{{video.name}}<span
                                ng-if="!$last">, </span></span></li>

                    </ul>
                </div>

            </div>

                </label>

        </ul>

    </div>

    <div class="my-4 my-md-0 col-md-1 col-12 listbox_items d-flex justify-content-center text-center flex-md-column flex-row align-items-center">

        <button title="Move selected item from available to assigned." class="m-2 item btn assignlistboxLeft"
                ng-click="assignPublicationsCtrl.moveSelectedFromAvailableToAssigned()">
            <i class="fas fa-angle-right"></i>
        </button>

        <button title="Move selected item from assigned to available." class="m-2 item btn assignlistboxRight"
                ng-click="assignPublicationsCtrl.moveSelectedFromAssignedToAvailable()">
            <i class="fas fa-angle-left"></i>
        </button>

    </div>

    <div class="col-12 col-md-5 listBoxRight p-5 ">

        <h3 class="" translate>ASSIGN_PUBLICATIONS.ASSIGNED</h3>

        <ul class="list-unstyled">

            <div ng-click="assignPublicationsCtrl.areAllAssignedSelected" ng-class="assignPublicationsCtrl.areAllAssignedSelected ? 'assignlistboxHoverCustom' : 'assignlistboxStyleCustom'" class="px-3 px-lg-5">

                <div class="col-12">

                    <div class="d-flex justify-content-between align-items-center text-container">

                        <input title="Select All" type="checkbox" ng-model="assignPublicationsCtrl.areAllAssignedSelected" ng-click="assignPublicationsCtrl.toggleSelectAllAssigned()" class="mb-0 mr-5 checkbox">

                        <div class="input-group col mb-0">
                            <input ng-model="assignPublicationsCtrl.assignedSearchValue" type="search"
                                   class="form-control p-3 h-100 mr-0 ng-pristine ng-valid ng-empty ng-touched" name="SearchDualList"
                                   placeholder="{{'ASSIGN_PUBLICATIONS.SEARCH' | translate}}">
                            <span ng-if="assignPublicationsCtrl.assignedSearchValue != null" ng-click="assignPublicationsCtrl.clearAssignedSearch()" class="search-clear"><i class="fas fa-times"></i></span>
                            <div class="input-group-append">
                                <span class="input-group-text"><i class="fa fa-search"></i></span>
                            </div>
                        </div>

                    </div>

                </div>

            </div>

            </label>

        </ul>

        <ul class="list-group list-unstyled">

            <li ng-class="publication.selected ? 'assignlistboxHover' : 'assignlistboxStyle'" ng-click="publication.selected = !publication.selected; assignPublicationsCtrl.updateAllAssignedCheckbox()" class="assignlistboxStyle py-3 px-3 px-lg-5 mb-3"
                ng-repeat="publication in assignPublicationsCtrl.assignedPublications | filter : assignPublicationsCtrl.assignedSearchValue">

                <div class="d-flex col-12 justify-content-between align-items-center">

                    <div class="d-flex justify-content-between align-items-center text-container">

                        <input type="checkbox" ng-model="publication.selected" class="mb-0 mr-5 checkbox">
                        <p class="mb-0"><strong> {{publication.name}} </strong><br>
                            <span class="badge-pill"
                                  ng-class="publication.published ? 'primary' : 'secondary'">
                                {{publication.published ? ('PUBLICATIONS.PUBLISHED' | translate) : ('PUBLICATIONS.UNPUBLISHED' | translate)}}
                            </span>
                        </p>

                    </div>

                    <i ng-click="assignedPublicationsCtrl.profileOpen = !assignedPublicationsCtrl.profileOpen; $event.stopPropagation()" class="info_listbox fas fa-info"></i>

                </div>

                <div ng-show="assignedPublicationsCtrl.profileOpen"
                     class="listbox_accordion d-flex col-12 justify-content-between align-items-center">
                    <ul class="mb-0 list-unstyled">
                        <li ng-if="publication.viewables && publication.viewables.length > 0"><strong>Viewables</strong></li>
                        <li ng-if="publication.viewables && publication.viewables.length > 0"><span
                                ng-repeat="viewable in publication.viewables">{{viewable.name}}<span
                                ng-if="!$last">, </span></span></li>
                        <li ng-if="publication.kits && publication.kits.length > 0"><strong>Kits</strong></li>
                        <li ng-if="publication.kits && publication.kits.length > 0"><span ng-repeat="kit in publication.kits">{{kit.name}}<span
                                ng-if="!$last">, </span></span></li>
                        <li ng-if="publication.techDocs && publication.techDocs.length > 0"><strong>Tech Docs</strong></li>
                        <li ng-if="publication.techDocs && publication.techDocs.length > 0"><span
                                ng-repeat="techDoc in publication.techDocs">{{techDoc.name}}<span
                                ng-if="!$last">, </span></span></li>
                        <li ng-if="publication.videos && publication.videos.length > 0"><strong>Videos</strong></li>
                        <li ng-if="publication.videos && publication.videos.length > 0"><span ng-repeat="video in publication.videos">{{video.name}}<span
                                ng-if="!$last">, </span></span></li>

                    </ul>
                </div>
            </li>

        </ul>

    </div>

</div>

<div class="flex justify-content-end listboxButtons my-5">

    <button ng-click="assignPublicationsCtrl.cancel()" class="btn primary px-5" translate>
        ASSIGN_PUBLICATIONS.RESET
    </button>

    <button ng-class="assignPublicationsCtrl.isPageEdited ? 'btn primary' : 'btn-cancel'" ng-click="assignPublicationsCtrl.save()" class="btn primary ml-3" translate>
        ASSIGN_PUBLICATIONS.SAVE_CHANGES
    </button>

</div>

</section>