{"name": "multi-room-chat-app", "version": "1.0.0", "description": "Multi-room Chat Web Application built with NodeJS and socket.io", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js"}, "keywords": ["chat-app", "multi-room-chat-app", "multi-user-chat-app", "nodejs", "websockets", "socket.io"], "author": "<PERSON>", "license": "MIT", "dependencies": {"express": "^4.17.1", "socket.io": "^2.3.0"}, "devDependencies": {}}