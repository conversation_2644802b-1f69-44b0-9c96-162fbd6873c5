(function () {
    'use strict';

    angular
        .module('app.orders')
        .controller('LiveOrderController', LiveOrderController);

    LiveOrderController.$inject = ['ordersService', '$rootScope', '$scope', '$controller', 'userService', '$uibModal', '$filter', '$state', 'headerBannerService', '$window', '$translate', '$timeout'];

    function LiveOrderController(ordersService, $rootScope, $scope, $controller, userService, $uibModal, $filter, $state, headerBannerService, $window, $translate, $timeout) {
        var vm = this;

        angular.extend(vm, $controller('OrderController', {$scope: $scope}));

        var oldDate;
        vm.isManufacturer = userService.isManufacturer();
        vm.closeOrder = false;

        vm.nextStep = nextStep;
        vm.editEstimatedDeliveryDate = editEstimatedDeliveryDate;
        vm.saveEstimatedDeliveryDate = saveEstimatedDeliveryDate;
        vm.viewInvoice = viewInvoice;
        vm.openComment = openComment;
        vm.altStep = altStep;
        vm.goToSplitOrder = goToSplitOrder;
        vm.formatShipEngineServiceCode = formatShipEngineServiceCode;
        vm.archiveOrder = archiveOrder;
        vm.onlyKitsPresent = onlyKitsPresent;
        vm.hasVisibleDiscountedPrices = hasVisibleDiscountedPrices;
        vm.isFarmer = userService.isFarmer();
        vm.previewPricingEnabled = userService.getPreviewPricingEnabled();
        
        var originalEstimatedDeliveryDate;
        var dateReadable = false;
        var ARCHIVE_ORDER;
        var LIVE_SHIPPED;
        var UPDATE_SUCCESS;
        getTranslations()
        initialize();

        function initialize() {
            vm.hidePartSearch = true;
            vm.showPurchaseOrder = true;
            vm.isDetailsEditable = false;
            vm.isPriceEditable = false;
            vm.isNotesEditable = false;
            vm.isPurchaseOrderEditable = false;
            vm.showEstimatedDelivery = true;
            vm.isActionActive = false;
            vm.isEstimatedDeliveryDateEditable = false;
            vm.hidePrices = false;
            vm.displayNotes = false;
            vm.isDiscountEditable = false;
            vm.isDiscountVisible = false;
            vm.isDiscountEnabled = false;
            vm.exportPartDataCsvVisible = true;
            vm.showSplitOrderBtn = false;
            vm.showCancelOrderButton = true;
            vm.isPreviewStockLevelEnabled = false;
            vm.showPartDescription = false;
            vm.showModelName = false;
            vm.showProductName = false;
            vm.isStockWarehousesEnabled = userService.getStockWarehousesEnabled();
            vm.showPartialShipped = userService.getPartialShippingEnabled();
            vm.switchTrackingUrl = switchTrackingUrl;

            if (vm.isManufacturer && ('SHIPPED' || 'PARTIALLY_SHIPPED' || 'PROCESSED')) {
                vm.isArchiveActive = true;
            } else {
                vm.isArchiveActive = false;
            }
            vm.duplicateOrder = duplicateOrder;

            if (vm.isManufacturer) {
                vm.showAltStepBtn = true;
                vm.isQuantityEditable = true;
                vm.isShippingEditable = true;
                vm.showNextStep = true;
                vm.partialShippingEnabled = userService.getPartialShippingEnabled();
                $translate(['ORDER.ORDER_SHIPPED'])
                    .then(function (resp) {
                        vm.nextStepText = resp['ORDER.ORDER_SHIPPED'];
                    });
                configureDate();
            } else {
                vm.isShippingEditable = false;
                vm.deleteOrderItem = false;
                vm.showNextStep = false;
                vm.partialShippingEnabled = false;
                vm.showShippingPriceDisclaimer = true;
            }

            $translate(['QUOTATION.LIVE_PROCESSED', 'ORDER.ORDER_SHIPPED', 'ORDERS.ORDER', 'ORDER.UPDATE_ORDER', 'ORDER.UPDATE_SUCCESS'])
                .then(function (resp) {
                    CONFIRM_ORDER_BANNER
                    vm.orderStatusPill = resp['QUOTATION.LIVE_PROCESSED'];
                    vm.nextStepText = resp['ORDER.ORDER_SHIPPED'];
                    vm.STAGE = resp['ORDERS.ORDER'];
                    vm.altStepText = resp["ORDER.UPDATE_ORDER"];
                    UPDATE_SUCCESS = resp["ORDER.UPDATE_SUCCESS"];
                });
            getOrder();
        }

        function getOrder() {
            ordersService.getOrder(vm.orderId)
                .then(getOrdersSuccess, serviceCallFailed);
        }

        var WENT_WRONG, EST_DEL_UPDATED, MARKED_AS_SHIPPED, CONFIRM_ORDER_CLOSED, ORDER_CLOSED, ENTER_EST_DATE,
            IF_YOU_CONFIRM, ESTIMATED_DEL_DATE, CONFIRM_ORDER_ARCHIVED, ARCHIVE_MODAL, CONFIRM_ORDER_BANNER;

        function getTranslations() {
            $translate(['LIVE_ORDER.ARCHIVE_ORDER', 'LIVE_ORDER.LIVE_SHIPPED', 'LIVE_ORDER.EST_DEL_UPDATED', 'LIVE_ORDER.MARKED_AS_SHIPPED', 'LIVE_ORDER.CONFIRM_ORDER_CLOSED',
                'LIVE_ORDER.ORDER_CLOSED', 'LIVE_ORDER.ENTER_EST_DATE', 'LIVE_ORDER.IF_YOU_CONFIRM', 'LIVE_ORDER.ESTIMATED_DEL_DATE', 'GENERAL.WENT_WRONG', 'CONFIRM_ORDER_ARCHIVED', 'LIVE_ORDER.CONFIRM_ORDER_ARCHIVED', 'ORDERS.CONFIRM_ORDER_ARCHIVED', 'ORDERS.ARCHIVE_MODAL', 'ORDERS.CONFIRM_ORDER_BANNER'])
                .then(function (resp) {
                    ARCHIVE_ORDER = resp["LIVE_ORDER.ARCHIVE_ORDER"];
                    LIVE_SHIPPED = resp["LIVE_ORDER.LIVE_SHIPPED"];
                    EST_DEL_UPDATED = resp["LIVE_ORDER.EST_DEL_UPDATED"];
                    MARKED_AS_SHIPPED = resp["LIVE_ORDER.MARKED_AS_SHIPPED"];
                    CONFIRM_ORDER_CLOSED = resp["LIVE_ORDER.CONFIRM_ORDER_CLOSED"];
                    ORDER_CLOSED = resp["LIVE_ORDER.ORDER_CLOSED"];
                    ENTER_EST_DATE = resp["LIVE_ORDER.ENTER_EST_DATE"];
                    IF_YOU_CONFIRM = resp["LIVE_ORDER.IF_YOU_CONFIRM"];
                    ESTIMATED_DEL_DATE = resp["LIVE_ORDER.ESTIMATED_DEL_DATE"];
                    WENT_WRONG = resp["GENERAL.WENT_WRONG"];
                    CONFIRM_ORDER_ARCHIVED = resp["ORDERS.CONFIRM_ORDER_ARCHIVED"];
                    ARCHIVE_MODAL = resp["ORDERS.ARCHIVE_MODAL"];
                    CONFIRM_ORDER_BANNER = resp["ORDERS.CONFIRM_ORDER_BANNER"];

                });
        }

        function getOrdersSuccess(response) {

            $rootScope.$broadcast("Update-Unread-Order-Tabs");

            if (response.data.orderStatus === 'RECEIVED' || response.data.orderStatus === 'PROCESSED' || response.data.orderStatus === 'PARTIALLY_SHIPPED' || response.data.orderStatus === 'SHIPPED') {
                vm.data = response.data;
                vm.orderLists = response.data.orderItems;
                vm.totalItems = vm.orderLists.length;
                vm.billingAddress = vm.createReadableAddress(response.data.billingAddress);
                vm.shippingAddress = vm.createReadableAddress(response.data.shippingAddress);
                vm.data.originalShippingPrice = (vm.data.orderExtraData && vm.data.orderExtraData.shippingCost) ? vm.data.orderExtraData.shippingCost : vm.data.shippingPrice;
                vm.data.shippingPrice = vm.data.originalShippingPrice ? vm.data.originalShippingPrice : 0;
                vm.data.taxPrice = (vm.data.orderExtraData && vm.data.orderExtraData.taxAmount) || 0;
                originalEstimatedDeliveryDate = vm.data.estimatedDeliveryDate;
                vm.isOrderItemsLoaded = true;
                vm.displayNotes = vm.data.notes !== null && vm.data.notes !== undefined;
                vm.parentOrderId = vm.data.parentOrderId;
                vm.parentCustomOrderDisplay = vm.data.parentCustomOrderDisplay;
                vm.associatedCustomOrderDisplay = vm.data.associatedCustomOrderDisplay;
                vm.associatedOrderId = vm.data.associatedOrderId;
                vm.showOrderReference = vm.data.orderExtraData && vm.data.orderExtraData.globalPaymentTransactionId;
                vm.showERPRefVisible = userService.isManufacturer() && vm.data.visSalesOrderNoX != null;
                vm.showShippingRefVisible = vm.data.orderExtraData && vm.data.orderExtraData.shipEngineTrackingNumber != null;
                vm.showTax = (vm.data.orderExtraData && vm.data.orderExtraData.taxAmount != null)
                vm.isShippingEditable = vm.isManufacturer && !vm.data.orderExtraData;
                vm.displayWarehouseName = vm.data.warehouseName !== null && vm.data.warehouseName !== undefined;
                vm.isPartInformationAvailable = isPartInformationAvailable;
                delete vm.data.associatedCustomOrderDisplay;
                vm.updateItemTotals(true, this, true);
                calculateSplitOrders();
                fetchShippingRequirements();
                vm.selectedCurrency = userService.getCurrencyData(vm.data.currency);
                if (vm.isManufacturer) {
                    vm.deleteOrderItem = true;
                }

                var manufacturerSubEntityId = vm.data.manufacturerSubEntityId;
                vm.orderLists.forEach(function (item) {
                    if (item.kitId) {
                        var id = vm.isManufacturer ? userService.getManufacturerId() : manufacturerSubEntityId;
                        ordersService.getKit(id, item.kitId, vm.isManufacturer)
                            .then(kitResponse => {
                                item.kitDetails = kitResponse;
                            });
                    }
                });

                if (response.data.orderStatus === 'PARTIALLY_SHIPPED') {
                    $translate(['LIVE_ORDER.LIVE_PARTIALLY_SHIPPED', 'ORDER.ORDER_SHIPPED_COMPLETE'])
                        .then(function (resp) {
                            vm.orderStatusPill = resp['LIVE_ORDER.LIVE_PARTIALLY_SHIPPED'];
                            vm.nextStepText = resp['ORDER.ORDER_SHIPPED_COMPLETE'];
                        });

                }
                if (response.data.orderStatus === 'SHIPPED') {
                    vm.nextStepText = ARCHIVE_ORDER;
                    vm.orderStatusPill = LIVE_SHIPPED;
                    vm.closeOrder = true;
                    vm.deleteOrderItem = false;
                    if (!vm.isManufacturer) {
                        vm.showCancelOrderButton = false;
                    }
                    vm.partialShippingEnabled = false;
                    vm.showShippingPriceDisclaimer = false;
                }
                if (vm.data.percentageDiscount > 0) {
                    vm.isDiscountVisible = true;
                }
                vm.isPartiallyShipped = hasBeenPartiallyShipped();
            } else {
                vm.redirectToOrder(response.data.orderId, response.data.orderStatus);
            }
        }

        function serviceCallFailed(error) {
            vm.isOrderItemsLoaded = true;
            headerBannerService.setNotification('ERROR', error.data.error, 10000);
        }

        function configureDate() {
            var d = new Date();
            setTimeout(function () {
                $('#datepicker').datepicker({
                    dateFormat: 'dd/mm/yy',
                    onSelect: function (dateText) {
                        vm.data.estimatedDeliveryDate = dateText;
                    }
                });
            }, 200);
        }

        function editEstimatedDeliveryDate() {
            processDeliveryDate();
            oldDate = vm.data.estimatedDeliveryDate;
            vm.isEstimatedDeliveryDateEditable = true;
        }

        function switchTrackingUrl() {
            if (vm.data && vm.data.orderExtraData) {
                if (vm.data.orderExtraData.shipEngineCarrierCode === 'ups') {
                    vm.trackingUrl = `https://wwwapps.ups.com/tracking/tracking.cgi?tracknum=${vm.data.orderExtraData.shipEngineTrackingNumber}`;
                } else {
                    vm.trackingUrl = `https://www.purolator.com/en/shipping/tracker?pins=${vm.data.orderExtraData.shipEngineTrackingNumber}`;
                }
            }
        }

        function processDeliveryDate() {
            if (vm.data.estimatedDeliveryDate) {
                var d = new Date(originalEstimatedDeliveryDate);
                var dateString = ("0" + d.getDate()).slice(-2) + "/" + ("0" + (d.getMonth() + 1)).slice(-2) + "/" +
                    d.getFullYear();

                dateReadable = true;
                vm.data.estimatedDeliveryDate = dateString;
            }
        }

        function saveEstimatedDeliveryDate() {
            if (!(vm.data.estimatedDeliveryDate === null || vm.data.estimatedDeliveryDate === '' || vm.data.estimatedDeliveryDate === undefined)) {
                var dateObject = formatEstimatedDeliveryDate();
                vm.estDate = $filter('date')(dateObject);
                var confirmObject = {
                    titleText: ESTIMATED_DEL_DATE + ": " + vm.estDate,
                    bodyText: IF_YOU_CONFIRM,
                    notifyDeliveryDateUpdated: true
                };
                $uibModal.open({
                    templateUrl: 'features/orders/live/saveDeliveryDateModal/saveDeliveryDateModal.html',
                    controller: 'SaveDeliveryDateModalController',
                    controllerAs: 'saveDeliveryDateModalCtrl',
                    size: 'sm',
                    resolve: {
                        confirmObject: function () {
                            return confirmObject;
                        }
                    }
                }).result.then(function () {
                    saveDateConfirmed(dateObject, confirmObject.notifyDeliveryDateUpdated);
                }, function () {
                    vm.data.estimatedDeliveryDate = oldDate;
                    vm.isEstimatedDeliveryDateEditable = false;
                    console.log('Modal Cancelled');
                });
            }
        }

        function formatEstimatedDeliveryDate() {
            var dateString = vm.data.estimatedDeliveryDate;
            var dateParts = dateString.split("/");
            var dateObject = new Date(dateParts[2], dateParts[1] - 1, dateParts[0]); // month is 0-based
            return dateObject;
        }

        function saveDateConfirmed(dateObject, notifyDeliveryDateUpdated) {
            vm.data.estimatedDeliveryDate = dateObject;
            vm.data.price = vm.total;
            vm.data.notifyDeliveryDateUpdated = notifyDeliveryDateUpdated;
            ordersService.updateOrder(vm.data)
                .then(updateEstimatedDeliveryDateSuccess, updateOrderFailed);
        }

        function nextStep() {
            if (vm.closeOrder === true) {
                var confirmObject = {
                    titleText: ORDER_CLOSED,
                    bodyText: CONFIRM_ORDER_CLOSED
                };
                $uibModal.open({
                    templateUrl: 'features/shared/confirmationModal/confirmModal.html',
                    controller: 'ConfirmModalController',
                    controllerAs: 'confirmModalCtrl',
                    size: 'sm',
                    resolve: {
                        confirmObject: function () {
                            return confirmObject;
                        }
                    }
                }).result.then(function () {
                    vm.data.orderStatus = "CLOSED";

                    ordersService.updateOrder(vm.data)
                        .then(archiveOrderSuccess, updateOrderFailed);
                }, function () {
                    console.log('Modal Cancelled');
                });
            } else {
                if (!dateReadable) {
                    processDeliveryDate();
                    if (vm.data.estimatedDeliveryDate) {
                        var dateObject = formatEstimatedDeliveryDate();
                        vm.estDate = $filter('date')(dateObject);
                        vm.data.estimatedDeliveryDate = originalEstimatedDeliveryDate;
                    }
                }

                var dataObject;
                if (vm.data.estimatedDeliveryDate) {
                    dataObject = {
                        estDate: vm.estDate.toString(),
                        estimatedDeliveryDate: vm.data.estimatedDeliveryDate,
                        order: vm.data
                    };
                } else {
                    dataObject = {order: vm.data};
                }
                var partsRemainingToShip = false;
                //If order partially shipped, check there are parts remaining to partially ship before giving modal as option
                if (vm.partialShippingEnabled && vm.data.orderStatus === 'PARTIALLY_SHIPPED') {
                    var orderItems = vm.data.orderItems;
                    for (var i = 0; i < orderItems.length; i++) {
                        var shippedPartsCount = 0;
                        for (var n = 0; n < orderItems[i].shippedOrderItems.length; n++) {
                            shippedPartsCount = shippedPartsCount + orderItems[i].shippedOrderItems[n].quantity;
                        }
                        if (orderItems[i].quantity > shippedPartsCount) {
                            partsRemainingToShip = true;
                            break;
                        }

                    }
                } else {
                    partsRemainingToShip = true;
                }
                if (vm.partialShippingEnabled && partsRemainingToShip) {
                    $uibModal.open({
                        templateUrl: 'features/orders/live/orderConfirmationModal/orderConfirmModal.html',
                        controller: 'OrderConfirmModalController',
                        controllerAs: 'orderConfirmModalCtrl',
                        size: 'sm',
                        resolve: {
                            dataObject: function () {
                                return dataObject;
                            }
                        }
                    }).result.then(function () {
                        vm.isEstimatedDeliveryDateEditable = false;
                        $timeout(function () {
                            $state.reload();
                        }, 500);
                    }, function () {
                        console.log('Modal Cancelled');
                    });
                } else {
                    $uibModal.open({
                        templateUrl: 'features/orders/live/fullShipOrder/fullShipOrderModal.html',
                        controller: 'FullShipOrderModalController',
                        controllerAs: 'fullShipOrderModalCtrl',
                        size: 'sm',
                        resolve: {
                            dataObject: function () {
                                return dataObject;
                            }
                        }
                    }).result.then(function (response) {
                        if (response.invoiceURL && response.invoiceURL.length > 0) {
                            vm.data.invoiceUrl = response.invoiceURL;
                        }
                        vm.data.estimatedDeliveryDate = ordersService.createDateObject(response.shippingDate);
                        vm.isEstimatedDeliveryDateEditable = false;
                        vm.data.orderStatus = "SHIPPED";
                        vm.data.price = vm.total;
                        ordersService.updateOrder(vm.data)
                            .then(updateOrderSuccess, updateOrderFailed);
                    }, function () {
                        vm.isEstimatedDeliveryDateEditable = false;
                        console.log('Modal Cancelled');
                    });
                }
            }
        }

        function updateOrderSuccess() {
            headerBannerService.setNotification('SUCCESS', MARKED_AS_SHIPPED, 10000);
            $state.reload();
        }

        function archiveOrderSuccess() {
            headerBannerService.setNotification('SUCCESS', CONFIRM_ORDER_ARCHIVED, 2000);
            $state.reload();
        }

        function updateEstimatedDeliveryDateSuccess() {
            headerBannerService.setNotification('SUCCESS', EST_DEL_UPDATED, 10000);
            originalEstimatedDeliveryDate = vm.data.estimatedDeliveryDate;
            vm.isEstimatedDeliveryDateEditable = false;
        }

        function updateOrderFailed() {
            headerBannerService.setNotification('ERROR', WENT_WRONG, 10000);
        }

        function viewInvoice(id) {
            if (id) {
                $window.open(vm.data.invoiceUrl[id], '_blank');
            } else {
                $window.open(vm.data.invoiceUrl[0], '_blank');
            }
        }

        function openComment(commentThread, orderItemId) {
            var commentObject = {
                threadId: commentThread,
                orderId: vm.orderId,
                orderItemId: orderItemId,
                blockAddingComment: (vm.data.orderStatus === 'SHIPPED')
            };

            $uibModal.open({
                templateUrl: 'features/shared/comments/comments.html',
                controller: 'CommentController',
                controllerAs: 'commentsCtrl',
                resolve: {
                    commentObject: function () {
                        return commentObject;
                    }
                }
            }).result.then(function (commentObject) {

                if (commentObject.orderItemId) {
                    var index = _.findIndex(vm.data.orderItems, {orderItemId: commentObject.orderItemId});
                    vm.data.orderItems[index].commentThread = {};
                    vm.data.orderItems[index].commentThread.orderItemId = commentObject.orderItemId;
                    vm.data.orderItems[index].commentThread.id = commentObject.threadId;
                    vm.data.orderItems[index].commentThread.orderId = commentObject.orderId;
                } else {
                    vm.data.commentThread = {};
                    vm.data.commentThread.id = commentObject.threadId;
                    vm.data.commentThread.orderId = commentObject.orderId;
                }
            }, function () {
                console.log('Modal Cancelled');
            });

        }

        function altStep() {
            updateData();
            updateOrder();
        }

        function updateData() {
            vm.data.price = vm.total;
            vm.data.additionalParts = vm.additionalParts;
            vm.data.currency = vm.selectedCurrency;
            if (vm.isCustDetailEdit) {
                vm.data.contactName = vm.newContactName;
                vm.data.deliveryName = vm.deliveryName;
                vm.data.contactNumber = vm.newContactNumber;
                vm.data.deliveryNumber = vm.newDeliveryNumber;
                vm.data.shippingAddress = JSON.parse(vm.newDeliveryAddress);
                vm.data.billingAddress = JSON.parse(vm.newBillingAddress);
            }
        }

        function updateOrder() {
            ordersService.updateOrder(vm.data)
                .then(updateOrderSuccessful, updateOrderFailed);
        }

        function updateOrderSuccessful() {
            headerBannerService.setNotification('SUCCESS', UPDATE_SUCCESS, 10000);
            vm.isOrderEdited = false;
            getOrder();
        }

        $scope.$on("Order-Edited", orderEdited);

        function orderEdited() {
            vm.isOrderEdited = true;
        }

        function hasBeenPartiallyShipped() {
            for (var i = 0; i < vm.data.orderHistory.length; i++) {
                if (vm.data.orderHistory[i].orderStatus === "Order partially shipped") {
                    return true;
                }
            }
            return false;
        }

        function goToSplitOrder(splitOrderId) {
            $state.go('orders.enquiry', {orderId: splitOrderId});
        }

        function calculateSplitOrders() {
            vm.splitOrders = [];
            if (vm.orderLists) {
                for (var i = 0; i < vm.orderLists.length; i++) {
                    if (vm.orderLists[i].splitOrderIds && vm.orderLists[i].splitOrderIds.length > 0) {
                        for (var j = 0; j < vm.orderLists[i].splitOrderIds.length; j++) {
                            if (!_.findWhere(vm.splitOrders, {orderId: vm.orderLists[i].splitOrderIds[j]})) {
                                var splitOrder = {
                                    orderId: vm.orderLists[i].splitOrderIds[j],
                                    displayId: vm.orderLists[i].splitOrderCustomDisplayNumbers[j] ? vm.orderLists[i].splitOrderCustomDisplayNumbers[j] : vm.orderLists[i].splitOrderIds[j]
                                }
                                vm.splitOrders.push(splitOrder);
                            }
                        }
                    }
                }
            }
            if (vm.additionalParts) {
                for (var k = 0; k < vm.additionalParts.length; k++) {
                    if (vm.additionalParts[k].splitOrderIds && vm.additionalParts[k].splitOrderIds.length > 0) {
                        for (var l = 0; l < vm.additionalParts[k].splitOrderIds.length; l++) {
                            if (!_.findWhere(vm.splitOrders, {orderId: vm.additionalParts[k].splitOrderIds[l]})) {
                                var splitOrder = {
                                    orderId: vm.additionalParts[k].splitOrderIds[l],
                                    displayId: vm.additionalParts[k].splitOrderCustomDisplayNumbers[l] ? vm.additionalParts[k].splitOrderCustomDisplayNumbers[l] : vm.additionalParts[k].splitOrderIds[l]
                                }
                                vm.splitOrders.push(splitOrder);
                            }
                        }
                    }
                }
            }

        }

        function formatShipEngineServiceCode(shipEngineServiceCode) {
            if (shipEngineServiceCode) {
                return shipEngineServiceCode.replace(/_/g, ' ');
            } else {
                return '';
            }
        }

        function archiveOrder() {
            var confirmObject = {
                titleText: CONFIRM_ORDER_ARCHIVED,
                bodyText: ARCHIVE_MODAL
            };

            $uibModal.open({
                templateUrl: 'features/shared/confirmationModal/confirmModal.html',
                controller: 'ConfirmModalController',
                controllerAs: 'confirmModalCtrl',
                size: 'sm',
                resolve: {
                    confirmObject: function () {
                        return confirmObject;
                    }
                }
            }).result.then(function () {
                vm.data.orderStatus = "CLOSED";
                ordersService.archiveOrder(vm.orderId)
                    .then(function () {
                        headerBannerService.setNotification('SUCCESS', CONFIRM_ORDER_BANNER, 2000);
                        $state.go('orders.historicalorders');
                    });
            }, function () {
                console.log('Modal Cancelled');
            });
        }

        function isPartInformationAvailable(item) {
            var partInformation = {
                showPartDescription: item.partDescription,
                showModelName: item.modelName,
                showMachineName: item.machineName
            };
            vm.showPartDescription = partInformation.showPartDescription;
            vm.showModelName = partInformation.showModelName;
            vm.showMachineName = partInformation.showMachineName;
            return partInformation;
        }

        function fetchShippingRequirements() {
            if (!vm.isFarmer) {
                ordersService.getShippingRequirements(vm.data.manufacturerSubEntityId).then(function(response) {
                    var shippingRequirements = response.data.shippingRequirements;
                    vm.orderShippingRequirement = shippingRequirements.find(req => req.shippingRequirementId === vm.data.shippingRequirementId);
                    vm.isShippingRequirementsValid = !!vm.orderShippingRequirement && !!vm.orderShippingRequirement.preferredCourier && !!vm.orderShippingRequirement.courierNumber;
                }, function(error) {
                    console.error("Error fetching shipping requirements:", error);
                    vm.isShippingRequirementsValid = false;
                });
              }
            }

          function duplicateOrder() {
            ordersService.duplicateOrder(vm.orderId)
                .then(function (response) {
                    var duplicatedOrder = response.data;
                    $state.go('create', {
                        orderDetails: duplicatedOrder
                    });
                }, function (error) {
                    console.error('Failed to duplicate order:', error);
                });
        }

        function onlyKitsPresent() {
            if (!vm.data || !vm.data.orderItems) {
                return false;
            }
            return vm.data.orderItems.every(item => item.kitId && !item.partId);
        }

        function hasVisibleDiscountedPrices() {

            if (!vm.data || !vm.data.orderItems) {
                return false;
            }

            var orderItems = vm.data.orderItems;

            var hasDiscountedPrices = orderItems.some(item => item.discountedPrice && item.discountedPrice !== 0);

            return hasDiscountedPrices;
        }

    }
})();
