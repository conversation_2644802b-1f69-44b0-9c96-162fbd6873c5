.publications_container{
  border-radius: 10px;
}

.publications {
  border: 1px solid #ccc;
  border-collapse: collapse;
  margin: 0;
  padding: 0;
  width: 100%;
  table-layout: fixed;
}

.publications caption {
  font-size: 1.5em;
  margin: 0.5em 0 0.75em;
}

.publications_heading{

  background: #F2F6F9;

}

.publications_buttons{

  background: white;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px

}

.publications_body tr {
  border: 1px solid #ddd;
  padding: 0.35em;
  background: white;
}

.publications th,
.publications td {
  padding: 1em;
  word-break: break-word;
}

.publications .dropdown-toggle{

  cursor: pointer;

}

.publications th {
  font-size: 0.85em;
  letter-spacing: 0.1em;
  text-transform: uppercase;
}

.search-panel {
  display: inline-block;
  width: auto;
}

.searchgroup {
  width: 340px;
  display: inline-block;
}

.publications_pageNumber{

  background: white;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px

}

/* SCSS for Publications Viewable Text Filter Dropdown */

#textFilterDropdown {
  padding: 20px;
  margin-top: -6px;
  border: 0;
  border-radius: 0;
  background: #f1f1f1;
}

.dropdownTextSearchBtnInnerTxt{

  opacity: 0.75;

}

.dropdownTextSearchBtn{
  width: 180px;
  padding: 0;
  position: relative;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: left;
  height: 38px;
}

.dropdownTextViewable{

  .dropdown-menu {
    position: relative;
  }

  .search-container {
    background-color: #f1f1f1;
  }

  .items-container {
    max-height: 200px;
    overflow-y: auto;
    overflow-x: hidden;
  }

  .dropdown-menu::after {
    display: none;
  }

  .dropdown-menu {
    max-width: 100%;
    width: auto;
  }

  .input-icon-wrap::after {
    display: none;
  }

}

/* END */

/* Animation for newly added categories */
@keyframes blueFlash {
  0% { background-color: #007bff; }
  100% { background-color: transparent; }
}

.category-item-new {
  animation: blueFlash 1s ease-out;
}

.category-item {
  transition: background-color 0.3s ease;
}

@media screen and (max-width: 800px) {
  .publications {
    border: 0;
  }

  .publications caption {
    font-size: 1.3em;
  }

  .publications thead {
    border: none;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
  }

  .publications tr {
    border-bottom: 3px solid #ddd;
    display: block;
    margin-bottom: 0.625em;
  }

  .publications td {
    border-bottom: 1px solid #ddd;
    display: block;
    font-size: 1em;
  }

  .publications td::before {
    /*
    * aria-label has no advantage, it won't be read inside a table
    content: attr(aria-label);
    */
    content: attr(data-label);
    font-weight: bold;
    text-transform: uppercase;
    padding-right:10px;

  }

  .publications td:last-child {
    border-bottom: 0;
  }

  .products-filter {

    margin-left: auto;

  }

  .order-details-holder {
    width: 100%;
    float: left;

  }

}
