<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" ng-click="uploadCtrl.cancel()" aria-label="Close"><i
            class="fa fa-close" aria-hidden="true"></i></button>

    <h2 class="modal-title" translate>VIEWABLE.UPLOAD_VIEWABLE</h2>
</div>

<div class="modal-body">
	  <form class="form" name="uploadViewableForm" ng-submit="uploadCtrl.addToProduct()">
    <p translate>UPLOAD_VIEWABLE.FILE_TYPES</p>
    <p translate>UPLOAD_VIEWABLE.ASSEMBLY_FILES</p>

          <div class="upload-box">
              <div>
                  <i class="fa" ng-class="{'fa-upload': uploadCtrl.isValid, 'fa-times': !uploadCtrl.isValid}"></i>
                  <h4 ng-if="uploadCtrl.isValid" class="file-uploader" translate>UPLOAD_VIEWABLE.CHOOSE_FILE</h4>
                  <h4 ng-if="!uploadCtrl.isValid" class="file-uploader" translate>UPLOAD_VIEWABLE.ERROR_FILENAME</h4>
                  <input type="file" class="fileupload" ng-click="$event = $event"
                         onchange="angular.element(this).scope().uploadCtrl.fileChanged(event)"
                         ng-required="true"
                         accept=".pdf, text/pdf, .zip, .f3d, .jt, .skp, .dwf, .stp, .ipt, .nwd" required/>
              </div>
          </div>

        <div class="alert success-alert" role="alert" ng-show="uploadCtrl.fileUploadedSuccess" translate>
            UPLOAD_VIEWABLE.SUCCESS
        </div>

        <div class="error-message alert alert-danger" role="alert" ng-show="uploadCtrl.fileUploadedFailure" translate>
            UPLOAD_VIEWABLE.ERROR
        </div>


        <div ng-if="uploadCtrl.file.name">
            <p>{{"UPLOAD_VIEWABLE.YOU_HAVE" | translate}} <strong class="uploaded-file cadWrap">File {{uploadCtrl.file.name}}</strong></p>
        </div>

        <div id="assembly">
            <div ng-show="uploadCtrl.isFileSelected" ng-if="uploadCtrl.isAssembly">
                <div class="mb-3 font-weight-bold" translate>UPLOAD_VIEWABLE.TOP_LEVEL</div>

                <div class="mb-3 select-box pl-3 w-100">
                    <select ng-model="uploadCtrl.topLevelAssembly" placeholder="{{'UPLOAD_VIEWABLE.PLEASE_SPECIFY' | translate}}" required>
				    <option value="" translate>UPLOAD_VIEWABLE.PLEASE_SELECT_TOP</option>
                        	<option ng-repeat="assembly in uploadCtrl.assemblys" value="{{assembly.name}}">{{assembly.name}}
                        </option>
                    </select>
                    <div class="select-arrow"></div>
                </div>
                <div class="d-flex">
                    <input type="checkbox" id="showPRTFiles" ng-model="uploadCtrl.showPRTFiles" ng-change="uploadCtrl.showPRTFilesChanged()"d>
                    <label for="showPRTFiles">Show .PRT files</label>
                </div>


            </div>

            <div class="input-group">
                <label translate>UPLOAD_VIEWABLE.VIEWABLE_NAME</label>
                <input class="w-100" type="text" placeholder="{{'UPLOAD_VIEWABLE.PLEASE_ENTER_NAME' | translate}}"
                       ng-required="true" ng-model="uploadCtrl.modelName" required>

            </div>
        </div>
          <div class="modal-actions">
              <button class="btn small secondary" href="" ng-click="uploadCtrl.cancel()" translate>GENERAL.CANCEL</button>
              <button class="btn small primary" type="submit" ng-disabled="!uploadCtrl.isValid" translate>UPLOAD_VIEWABLE.CONFIRM</button>
          </div>
	  </form>
</div>

<div class="loader modal-loader" ng-show="uploadCtrl.loading">
    <div class="vertical-align loader modal-loader" id="loader">
        <div id="loader-text">
            <i class="fas fa-sync-alt fa-spin"></i>
            <p>{{'UPLOAD_VIEWABLE.UPLOADING' | translate}}&hellip;</p>
            <p translate> UPLOAD_VIEWABLE.FEW_MINS</p>
            <div id="myProgress" ng-show="uploadCtrl.progress">
                <div id="myBar" style="width: {{uploadCtrl.progressPercentage}}%;"></div>
            </div>
            <div ng-if="uploadCtrl.file.name">
                <p>{{"UPLOAD_VIEWABLE.YOU_HAVE" | translate}} <strong class="uploaded-file">File {{uploadCtrl.file.name}}</strong></p>
            </div>
        </div>
    </div>
</div>

