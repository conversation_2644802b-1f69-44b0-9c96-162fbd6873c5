(function () {
    'use strict';

    angular
        .module('app.shared')
        .controller('CreateNewAddressController', CreateNewAddressController);

    CreateNewAddressController.$inject = ['$uibModalInstance', 'ordersService', '$state', 'customerUserId', 'userService', 'addressData', 'isRegisterMode', 'showCompanyName'];

    function CreateNewAddressController($uibModalInstance, ordersService, $state, customerUserId, userService, addressData, isRegisterMode, showCompanyName) {

        var vm = this;
        vm.processing = false;

        vm.cancel = $uibModalInstance.dismiss;
        vm.createAddress = createAddress;
        vm.isDealerPlusPage = isDealerPlusPage;
        vm.data = null;
        vm.showCompanyName = (typeof showCompanyName != 'undefined') ? showCompanyName: true;
        initialize();

        function initialize() {
            vm.countries = ordersService.getCountryList();
            vm.data = addressData || null;
        }

        function createAddress() {
            var userId = null;
            vm.processing = true;
            if(customerUserId !== null){
                userId = customerUserId;
            }else{
                if($state.params.onBehalfOf && $state.params.onBehalfOf !== "null"){
                    var decrypted = atob($state.params.onBehalfOf);
                    var parsed = JSON.parse(decodeURIComponent(decrypted));
                        userId = parsed.userId
                }
            }

            if(isRegisterMode) {
                $uibModalInstance.close(vm.data);
                return;
            }
            ordersService.createAddress(vm.data, userId)
                .then(createAddressSuccess, createAddressFailure);
        }

        function createAddressSuccess(resp) {
            vm.processing = true;
            $uibModalInstance.close(resp.data);
        }

        function createAddressFailure(error) {
            vm.processing = false;
            headerBannerService.setNotification('ERROR', error.data.error, 10000);
        }

        function isDealerPlusPage(){
            return userService.isDealerPlusUser() && $state.current.name.includes("customerOrders");
        }

    }
})();
