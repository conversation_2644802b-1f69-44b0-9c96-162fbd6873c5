(function () {
    'use strict';

    angular
        .module('app.orders')
        .controller('DPOrdersTopLevelController', DPOrdersTopLevelController);

    DPOrdersTopLevelController.$inject = ['$state', '$location', '$rootScope', 'dpOrdersService', 'ordersService', 'headerBannerService', 'userService'];

    function DPOrdersTopLevelController($state, $location, $rootScope, dpOrdersService, ordersService, headerBannerService, userService) {
        var vm = this;
        vm.isActive = isActive;
        vm.myOrdersUnreadCount = 0;
        vm.customerOrdersUnreadCount = 0;

        initialize();

        function initialize() {
            refreshCounts();
        }

        function getMyOrdersUnreadCountSuccess(response) {
            var data = response.data;
            vm.myOrdersUnreadCount = data.enquiryCount + data.quoteCount + data.liveCount;
        }

        function getCustomerOrdersUnreadCountSuccess(response) {
            var data = response.data;
            vm.customerOrdersUnreadCount = data.enquiryCount + data.quoteCount + data.liveCount;
        }

        function serviceCallFailed(error) {
            headerBannerService.setNotification('ERROR', error.data.error, 5000);
        }

        vm.isCustOrder = function () {
            return $state.current.name.includes("customerOrders");
        }

        function isActive(route) {
            return $location.path().includes(route);
        }

        $rootScope.$on("Update-Unread-DP-Order-Tabs", refreshCounts);
        $rootScope.$on("Update-Unread-Order-Tabs", refreshCounts);

        function refreshCounts() {
            ordersService.getUnreadCounts()
                .then(getMyOrdersUnreadCountSuccess, serviceCallFailed);
            if(userService.isDealerPlusUser()){
                dpOrdersService.getUnreadCounts()
                    .then(getCustomerOrdersUnreadCountSuccess, serviceCallFailed);
            }
        }
    }
})();
