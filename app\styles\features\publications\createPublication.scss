.viewable-item{

    background: #F8F9FA;
    padding: 0.5em;
    border: solid 2px #DEE2E7;
    border-radius: 5px;

}

.viewable-item-assigned{

    background: #F0F7FF;
    padding: 0.5em;
    border: solid 2px #3395FF;
    border-radius: 5px;

}

.viewable-item-disabled {
    background: #F8F9FA;
    padding: 0.5em;
    border: solid 2px #DEE2E7;
    border-radius: 5px;
    opacity: 0.5;
    
    .font-weight-bold {
        color: #6c757d !important;
    }
    
    input[type="checkbox"] {
        cursor: not-allowed;
        opacity: 0.6;
    }
    
    // Grey out all text content
    * {
        color: #6c757d !important;
    }
    
    // Override the background colors for range/product tags
    span[style*="background-color"] {
        background-color: #e9ecef !important;
        opacity: 0.7;
    }
}

.publication-details-section,
.viewables-section,
.featured-image-section,
.additional-image-section {
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
}

// Styles for Manage Categories Modal
.manage-categories-modal-body {
    .scrollable-list {
        max-height: 300px;
        overflow-y: auto;
        margin-bottom: 1rem;
        border: 1px solid #dee2e6;
        border-radius: .25rem;
    }

    .category-item {
        cursor: pointer;
        padding-top: 0.5rem;
        padding-bottom: 0.5rem;

        &:focus-within {
            box-shadow: none;
        }
    }

    .list-group-item input[type="checkbox"] {
        width: auto;
    }
}

.search-input-wrapper {
    position: relative;
    display: flex;
    flex-grow: 1;
    align-self: stretch;

    .form-control {
        padding-right: 2.5rem;
        height: 100%;
    }

    .clear-search-icon {
        position: absolute;
        right: 20px;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
        z-index: 100;
        line-height: 1;
        font-size: 2.5rem;
        color: #6c757d;

        &:hover {
            color: #343a40;
        }
    }
}

// Create Publication Styles

.border.rounded {
    height: 600px;
    display: flex;
    flex-direction: column;
    overflow: hidden; // Prevent any overflow from the main container
}

.col-md.py-3.border.rounded:not(.selected-viewables-container) {
    .viewable-list-container {
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;
        padding: 0 8px 8px 0; // Add padding to prevent content from touching edges
        
        // Custom scrollbar styling
        &::-webkit-scrollbar {
            width: 6px;
        }
        
        &::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }
        
        &::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
            
            &:hover {
                background: #a8a8a8;
            }
        }
    }
    
    // Ensure viewable items don't overflow
    .viewable-item,
    .viewable-item-assigned,
    .viewable-item-disabled {
        margin-right: 8px;
        word-wrap: break-word;
        overflow-wrap: break-word;
    }
}

// Right container (Selected Viewables) - overflow on content area excluding title
.selected-viewables-container {
    .selected-viewables-scroll-container {
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;
        padding: 0 8px 8px 0; // Add padding to prevent content from touching edges
        
        // Custom scrollbar styling
        &::-webkit-scrollbar {
            width: 6px;
        }
        
        &::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }
        
        &::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
            
            &:hover {
                background: #a8a8a8;
            }
        }
    }
    
    // Ensure viewable items don't overflow
    .viewable-item,
    .viewable-item-assigned,
    .viewable-item-disabled {
        margin-right: 8px;
        word-wrap: break-word;
        overflow-wrap: break-word;
    }
}