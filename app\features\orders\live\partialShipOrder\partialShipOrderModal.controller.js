(function () {
    'use strict';

    angular
        .module('app.orders')
        .controller('PartialShipOrderModalController', PartialShipOrderModalController);

    PartialShipOrderModalController.$inject = ['$uibModalInstance', 'orderId', 'ordersService', 'userService', '$state'];

    function PartialShipOrderModalController($uibModalInstance, orderId, orderService, userService, $state) {
        var vm = this;

        vm.cancel = cancel;

        initialize();

        function initialize() {
            var isDealerPlusPage = userService.isDealerPlusUser() && $state.current.name.includes("customerOrders");
                orderService.getPartialShipDetails(orderId, isDealerPlusPage)
                    .then(getPartialShipDetailsSuccess, getPartialShipDetailsFailed)
        }

        function getPartialShipDetailsSuccess(response) {
            vm.shipments = response.data;
        }

        function getPartialShipDetailsFailed() {

        }

        function cancel() {
            $uibModalInstance.dismiss();
        }
    }
})();