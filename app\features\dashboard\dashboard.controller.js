(function () {
    "use strict";

    angular.module("app.dashboard").controller("DashboardController", DashboardController);

    DashboardController.$inject = ["customerService", "headerBannerService", "dashboardService", "userService", "$translate", "$scope"];

    function DashboardController(customerService, headerBannerService, dashboardService, userService, $translate, $scope) {
        var vm = this;
        var orderValueChartCTX;
        var mostOrderedPartsChartCTX;
        var orderValueChart;
        var mostOrderedPartsChart;
        var manufacturerId = userService.getManufacturerId();

        vm.populateOrderValues = populateOrderValues;
        vm.mostActiveSubEntityClicked = mostActiveSubEntityClicked;
        vm.highestOrderSubEntityClicked = highestOrderSubEntityClicked;
        vm.orderValueChartUpdate = orderValueChartUpdate;
        vm.populateUserTables = populateUserTables;
        vm.sortAndPin = sortAndPin;

        vm.selectedSubEntity = {};
        vm.chartSelectedSubEntity = {};
        vm.manufacturerSubEntities = [];
        vm.mostActiveCustomers = [];
        vm.totalEnquiries = 0;
        vm.totalEnquiriesDifference = 0;
        vm.totalQuotes = 0;
        vm.totalQuotesDifference = 0;
        vm.totalOrders = 0;
        vm.totalOrdersDifference = 0;
        vm.conversionRate = 0;
        vm.conversionRateDifference = 0;
        vm.valueOfQuotes = 0;
        vm.valueOfQuotesDifference = 0;
        vm.valueOfOrders = 0;
        vm.valueOfOrdersDifference = 0;
        vm.isMostOrderedLoading = true;

        vm.defaultCurrency = userService.getDefaultCurrency();

        var WENT_WRONG,
            ALL_TIME,
            CUSTOM,
            LAST_YEAR,
            LAST_SIX_MONTHS,
            LAST_THREE_MONTHS,
            LAST_MONTH,
            THIS_MONTH,
            MOST_ORDERED_PART,
            ORDER_VALUE,
            ALL_USERS;

        $translate([
            "GENERAL.WENT_WRONG",
            "DASHBOARD.ALL_TIME",
            "DASHBOARD.CUSTOM",
            "DASHBOARD.LAST_YEAR",
            "DASHBOARD.LAST_SIX_MONTHS",
            "DASHBOARD.LAST_THREE_MONTHS",
            "DASHBOARD.LAST_MONTH",
            "DASHBOARD.THIS_MONTH",
            "DASHBOARD.MOST_ORDERED_PART",
            "DASHBOARD.ORDER_VALUE_POUND",
            "DASHBOARD.ALL_USERS",
        ]).then(function (resp) {
            WENT_WRONG = resp["GENERAL.WENT_WRONG"];
            ALL_TIME = resp["DASHBOARD.ALL_TIME"];
            CUSTOM = resp["DASHBOARD.CUSTOM"];
            LAST_YEAR = resp["DASHBOARD.LAST_YEAR"];
            LAST_SIX_MONTHS = resp["DASHBOARD.LAST_SIX_MONTHS"];
            LAST_THREE_MONTHS = resp["DASHBOARD.LAST_THREE_MONTHS"];
            LAST_MONTH = resp["DASHBOARD.LAST_MONTH"];
            THIS_MONTH = resp["DASHBOARD.THIS_MONTH"];
            MOST_ORDERED_PART = resp["DASHBOARD.MOST_ORDERED_PART"];
            ORDER_VALUE = resp["DASHBOARD.ORDER_VALUE_POUND"];
            ALL_USERS = resp["DASHBOARD.ALL_USERS"];

            vm.timePeriods = getTimePeriods();
            vm.chartTimePeriods = getChartTimePeriods();
            vm.showCustomDatePickers = false;

            initialize();
        });

        function sortAndPin(manufacturerSubEntity) {
            if (manufacturerSubEntity.name === "All Users") {
                return -1; //Ensures "All Users" is sorted to the top
            }
            return manufacturerSubEntity.name;
        }

        function initialize() {
            vm.selectedTimePeriod = vm.timePeriods[0];
            vm.chartSelectedTimePeriod = vm.chartTimePeriods[0];
            orderValueChartCTX = document.getElementById("orderValueChart").getContext("2d");
            mostOrderedPartsChartCTX = document.getElementById("mostOrderedPartsChart").getContext("2d");
            configureDatePickers();

            customerService.getCustomers().then(getCustomersSuccess, webserviceFailed);

            populateUserTables();
        }

        function configureDatePickers() {
            var d = new Date();
            setTimeout(function () {
                $("#datepickerStartDate").datepicker({
                    dateFormat: "dd/mm/yy",
                    maxDate: new Date(d.setDate(d.getDate()) - 1),
                    onSelect: function (dateText) {
                        vm.customStartDate = dateText;
                        $scope.$apply();
                    },
                });
            }, 200);

            var e = new Date();
            setTimeout(function () {
                $("#datepickerEndDate").datepicker({
                    dateFormat: "dd/mm/yy",
                    maxDate: new Date(e.setDate(e.getDate())),
                    onSelect: function (dateText) {
                        vm.customEndDate = dateText;
                        /*$('#datepickerStartDate').datepicker({
                            maxDate: new Date(d.setDate(e.getDate())),
                        });*/
                        $scope.$apply();
                    },
                });
            }, 200);
        }

        function populateUserTables() {}

        function getMostActiveCustomersSuccess(response) {
            vm.mostActiveCustomers = response.data;
        }

        function getHighestOrderValueCustomersSuccess(response) {
            vm.highestOrderValueCustomers = response.data;
        }

        function getCustomersSuccess(response) {
            vm.manufacturerSubEntities = response.data;
            vm.manufacturerSubEntities.unshift({ manufacturerSubEntityId: -1, name: ALL_USERS });
            vm.selectedSubEntity = vm.manufacturerSubEntities[0];
            vm.chartSelectedSubEntity = vm.manufacturerSubEntities[0];
            populateOrderValues();
            orderValueChartUpdate();
        }

        function populateOrderValues() {
            if (vm.selectedTimePeriod.enum === "CUSTOM") {
                vm.showCustomDatePickers = true;
                if (vm.customStartDate != null || vm.customEndDate != null) {
                    dashboardService
                        .getTotalEnquiries(
                            manufacturerId,
                            vm.selectedTimePeriod.enum,
                            vm.selectedSubEntity.manufacturerSubEntityId,
                            vm.customStartDate,
                            vm.customEndDate
                        )
                        .then(getTotalEnquiriesSuccess, webserviceFailed);
                    dashboardService
                        .getTotalQuotes(
                            manufacturerId,
                            vm.selectedTimePeriod.enum,
                            vm.selectedSubEntity.manufacturerSubEntityId,
                            vm.customStartDate,
                            vm.customEndDate
                        )
                        .then(getTotalQuotesSuccess, webserviceFailed);
                    dashboardService
                        .getTotalOrders(
                            manufacturerId,
                            vm.selectedTimePeriod.enum,
                            vm.selectedSubEntity.manufacturerSubEntityId,
                            vm.customStartDate,
                            vm.customEndDate
                        )
                        .then(getTotalOrdersSuccess, webserviceFailed);
                    dashboardService
                        .getConversionRate(
                            manufacturerId,
                            vm.selectedTimePeriod.enum,
                            vm.selectedSubEntity.manufacturerSubEntityId,
                            vm.customStartDate,
                            vm.customEndDate
                        )
                        .then(getConversionRateSuccess, webserviceFailed);
                    dashboardService
                        .getValueOfQuotes(
                            manufacturerId,
                            vm.selectedTimePeriod.enum,
                            vm.selectedSubEntity.manufacturerSubEntityId,
                            vm.customStartDate,
                            vm.customEndDate
                        )
                        .then(getValueOfQuotesSuccess, webserviceFailed);
                    dashboardService
                        .getValueOfOrders(
                            manufacturerId,
                            vm.selectedTimePeriod.enum,
                            vm.selectedSubEntity.manufacturerSubEntityId,
                            vm.customStartDate,
                            vm.customEndDate
                        )
                        .then(getValueOfOrdersSuccess, webserviceFailed);
                    dashboardService
                        .getMostOrderedParts(
                            manufacturerId,
                            vm.selectedTimePeriod.enum,
                            vm.selectedSubEntity.manufacturerSubEntityId,
                            vm.customStartDate,
                            vm.customEndDate
                        )
                        .then(getMostOrderedPartsSuccess, webserviceFailed);
                    dashboardService
                        .getMostActiveCustomers(manufacturerId, vm.selectedTimePeriod.enum, vm.customStartDate, vm.customEndDate)
                        .then(getMostActiveCustomersSuccess, webserviceFailed);
                    dashboardService
                        .getHighestOrderValueCustomers(manufacturerId, vm.selectedTimePeriod.enum, vm.customStartDate, vm.customEndDate)
                        .then(getHighestOrderValueCustomersSuccess, webserviceFailed);
                }
            } else {
                vm.showCustomDatePickers = false;
                vm.isMostOrderedLoading = true;
                vm.mostOrderedPartsHasData = false;
                vm.customStartDate = null;
                vm.customEndDate = null;

                dashboardService
                    .getTotalEnquiries(manufacturerId, vm.selectedTimePeriod.enum, vm.selectedSubEntity.manufacturerSubEntityId)
                    .then(getTotalEnquiriesSuccess, webserviceFailed);
                dashboardService
                    .getTotalQuotes(
                        manufacturerId,
                        vm.selectedTimePeriod.enum,
                        vm.selectedSubEntity.manufacturerSubEntityId,
                        vm.customStartDate,
                        vm.customEndDate
                    )
                    .then(getTotalQuotesSuccess, webserviceFailed);
                dashboardService
                    .getTotalOrders(manufacturerId, vm.selectedTimePeriod.enum, vm.selectedSubEntity.manufacturerSubEntityId)
                    .then(getTotalOrdersSuccess, webserviceFailed);
                dashboardService
                    .getConversionRate(manufacturerId, vm.selectedTimePeriod.enum, vm.selectedSubEntity.manufacturerSubEntityId)
                    .then(getConversionRateSuccess, webserviceFailed);
                dashboardService
                    .getValueOfQuotes(manufacturerId, vm.selectedTimePeriod.enum, vm.selectedSubEntity.manufacturerSubEntityId)
                    .then(getValueOfQuotesSuccess, webserviceFailed);
                dashboardService
                    .getValueOfOrders(manufacturerId, vm.selectedTimePeriod.enum, vm.selectedSubEntity.manufacturerSubEntityId)
                    .then(getValueOfOrdersSuccess, webserviceFailed);
                dashboardService
                    .getMostOrderedParts(manufacturerId, vm.selectedTimePeriod.enum, vm.selectedSubEntity.manufacturerSubEntityId)
                    .then(getMostOrderedPartsSuccess, webserviceFailed);
                dashboardService
                    .getMostActiveCustomers(manufacturerId, vm.selectedTimePeriod.enum)
                    .then(getMostActiveCustomersSuccess, webserviceFailed);
                dashboardService
                    .getHighestOrderValueCustomers(manufacturerId, vm.selectedTimePeriod.enum)
                    .then(getHighestOrderValueCustomersSuccess, webserviceFailed);
            }
        }

        function getTotalEnquiriesSuccess(response) {
            vm.totalEnquiries = response.data.current;
            vm.totalEnquiriesDifference = response.data.current - response.data.previous;
        }

        function getTotalQuotesSuccess(response) {
            vm.totalQuotes = response.data.current;
            vm.totalQuotesDifference = response.data.current - response.data.previous;
        }

        function getTotalOrdersSuccess(response) {
            vm.totalOrders = response.data.current;
            vm.totalOrdersDifference = response.data.current - response.data.previous;
        }

        function getConversionRateSuccess(response) {
            vm.conversionRate = response.data.current === "NaN" ? 0 : response.data.current;
            vm.conversionRateDifference = response.data.current === "NaN" ? 0 : response.data.current - response.data.previous;
        }

        function getValueOfQuotesSuccess(response) {
            vm.valueOfQuotes = response.data.current;
            vm.valueOfQuotesDifference = response.data.current - response.data.previous;
        }

        function getValueOfOrdersSuccess(response) {
            vm.valueOfOrders = response.data.current;
            vm.valueOfOrdersDifference = response.data.current - response.data.previous;
        }

        function getMostOrderedPartsSuccess(response) {
            var partNames = [];
            var numberOfPartsOrdered = [];
            for (var i = 0; i < response.data.length; i++) {
                var partName = response.data[i].partDescription
                    ? response.data[i].partNumber + " - " + response.data[i].partDescription
                    : response.data[i].partNumber;
                var trimmedPartName = partName.length > 40 ? partName.substring(0, 37) + "..." : partName;
                partNames.push(trimmedPartName);
                numberOfPartsOrdered.push(response.data[i].quantityOrdered);
            }
            updateMostOrderPartsChart(partNames, numberOfPartsOrdered);
        }

        function mostActiveSubEntityClicked(index) {
            for (var i = 0; i < vm.manufacturerSubEntities.length; i++) {
                if (vm.manufacturerSubEntities[i].manufacturerSubEntityId === vm.mostActiveCustomers[index].manufacturerSubEntityId) {
                    vm.selectedSubEntity = vm.manufacturerSubEntities[i];
                    populateOrderValues();
                    vm.chartSelectedSubEntity = vm.manufacturerSubEntities[i];
                    orderValueChartUpdate();
                    break;
                }
            }
        }

        function highestOrderSubEntityClicked(index) {
            for (var i = 0; i < vm.manufacturerSubEntities.length; i++) {
                if (
                    vm.manufacturerSubEntities[i].manufacturerSubEntityId === vm.highestOrderValueCustomers[index].manufacturerSubEntityId
                ) {
                    vm.selectedSubEntity = vm.manufacturerSubEntities[i];
                    populateOrderValues();
                    vm.chartSelectedSubEntity = vm.manufacturerSubEntities[i];
                    orderValueChartUpdate();
                    break;
                }
            }
        }

        function orderValueChartUpdate() {
            dashboardService
                .getOrderValues(manufacturerId, vm.chartSelectedTimePeriod.enum, vm.chartSelectedSubEntity.manufacturerSubEntityId)
                .then(getOrderValuesSuccess, webserviceFailed);
        }

        function getOrderValuesSuccess(response) {
            var months = response.data.x;
            var orderValues = response.data.y;
            updateOrderValueChart(months, orderValues);
        }

        function updateOrderValueChart(months, orderValues) {
            if (!orderValueChart) {
                orderValueChart = new Chart(orderValueChartCTX, createOrderValueChartData(months, orderValues));
            } else {
                orderValueChart.data.datasets[0].data = orderValues;
                orderValueChart.data.labels = months;
                orderValueChart.update();
            }
        }

        function updateMostOrderPartsChart(partNames, numberOfPartsOrdered) {
            vm.isMostOrderedLoading = false;
            vm.mostOrderedPartsHasData = numberOfPartsOrdered.length > 0;
            if (!mostOrderedPartsChart) {
                mostOrderedPartsChart = new Chart(mostOrderedPartsChartCTX, createMostOrderedPartsChart(partNames, numberOfPartsOrdered));
            } else {
                mostOrderedPartsChart.data.datasets[0].data = numberOfPartsOrdered;
                mostOrderedPartsChart.data.labels = partNames;
                mostOrderedPartsChart.update();
            }
        }

        function createOrderValueChartData(months, orderValues) {
            return {
                type: "bar",
                data: {
                    labels: months,
                    datasets: [
                        {
                            label: ORDER_VALUE,
                            data: orderValues,
                            backgroundColor: "rgba(51,146,252,0.8)",
                            borderColor: "rgba(51,146,252,1)",
                        },
                    ],
                },
                options: {
                    legend: { display: false },
                    scales: {
                        yAxes: [{ ticks: { beginAtZero: true } }],
                        xAxes: [{ gridLines: { display: false } }],
                    },
                },
            };
        }

        function createMostOrderedPartsChart(partNames, numberOfPartsOrdered) {
            return {
                type: "doughnut",
                data: {
                    datasets: [
                        {
                            data: numberOfPartsOrdered,
                            backgroundColor: getColoursArray(),
                            label: MOST_ORDERED_PART,
                        },
                    ],
                    labels: partNames,
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    legend: {
                        display: true,
                        onHover: function (e) {
                            e.target.style.cursor = "pointer";
                        },
                        onLeave: function (e) {
                            e.target.style.cursor = "default";
                        },
                    },
                },
            };
        }

        function webserviceFailed(error) {
            console.log(error);
            headerBannerService.setNotification("ERROR", WENT_WRONG, 5000);
        }

        function getTimePeriods() {
            return [
                { label: ALL_TIME, enum: "ALL_TIME" },
                { label: THIS_MONTH, enum: "CURRENT_MONTH" },
                { label: LAST_MONTH, enum: "LAST_MONTH" },
                { label: LAST_THREE_MONTHS, enum: "LAST_3_MONTHS" },
                { label: LAST_SIX_MONTHS, enum: "LAST_6_MONTHS" },
                { label: LAST_YEAR, enum: "LAST_YEAR" },
                { label: CUSTOM, enum: "CUSTOM" },
            ];
        }

        function getChartTimePeriods() {
            return [
                { label: LAST_YEAR, enum: "LAST_YEAR" },
                { label: LAST_SIX_MONTHS, enum: "LAST_6_MONTHS" },
            ];
        }

        function getColoursArray() {
            return [
                "rgba(52, 156, 228, 0.8)",
                "rgba(28, 76, 116, 0.8)",
                "rgb(53,69,81, 0.8)",
                "rgba(34,97,126, 0.8)",
                "rgba(58,118,144, 0.8)",
                "rgba(82,140,161, 0.8)",
                "rgb(106,162,179, 0.8)",
                "rgba(131,185,197, 0.8)",
                "rgb(157,208,216, 0.8)",
                "rgba(178, 182, 176, 0.8)",
            ];
        }
    }
})();
