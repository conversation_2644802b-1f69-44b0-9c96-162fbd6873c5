(function () {
    'use strict';

    angular
        .module('app.services')
        .factory('createUserService', createUserService);

    createUserService.$inject = ['$http', 'apiConstants', 'userService', '$location'];

    function createUserService($http, apiConstants, userService, $location) {
        return {
            createManufacturerSubEntityCustomer: createManufacturerSubEntityCustomer,
            editManufacturerSubEntityCustomer: editManufacturerSubEntityCustomer,
            deleteManufacturerSubEntity: deleteManufacturerSubEntity,
            createManufacturerSubEntityDealer: createManufacturerSubEntityDealer,
            createCustomerUser: createCustomerUser,
            createDealerUser: createDealerUser,
            createDealerPlusUser: createDealerPlusUser,
            createManufacturerUser: createManufacturerUser,
            editCustomerUser: editCustomerUser,
            editDealerUser: editDealerUser,
            editManufacturerUser: editManufacturerUser,
            editSingleManufacturerUser: editSingleManufacturerUser,
            createManufacturer: createManufacturer,
            deleteManufacturerUser: deleteManufacturerUser,
            createSingleManufacturerUser: createSingleManufacturerUser
        };

        function createManufacturerSubEntityCustomer(customerName, defaultDiscount, priceListId) {
            var manufacturerId = userService.getManufacturerId();
            var customerData = {
                "name": customerName,
                "description": "",
                "defaultDiscount": defaultDiscount,
                "manufacturerId": manufacturerId,
                "manufacturerSubEntityType": "CUSTOMER"
            };
            if(priceListId){
                customerData.priceListIdentifierId = priceListId;
            }
            return $http.post(apiConstants.url + '/manufacturersubentity', customerData);
        }

        function editManufacturerSubEntityCustomer(customerName, subEntityId, defaultDiscount, priceListId, visCustomerCode, warehouseId) {
            var customerData = {
                "name": customerName,
                "defaultDiscount": defaultDiscount,
                "manufacturerSubEntityId": subEntityId,
            };
          
            visCustomerCode && (customerData.visCustomerCode = visCustomerCode);
            warehouseId && (customerData.warehouseId = warehouseId);
            
            if(priceListId){
                customerData.priceListIdentifierId = priceListId;
            }
            return $http.put(apiConstants.url + '/manufacturersubentity', customerData);
        }

        function deleteManufacturerSubEntity(manufacturerSubEntityId) {
            return $http.delete(apiConstants.url + '/manufacturersubentity/' + manufacturerSubEntityId);
        }

        function createManufacturerSubEntityDealer(dealerName, defaultDiscount, priceListId, visCustomerCode, warehouseId) {
            var manufacturerId = userService.getManufacturerId();
            var customerData = {
                "name": dealerName,
                "description": "",
                "defaultDiscount": defaultDiscount,
                "manufacturerId": manufacturerId,
                "manufacturerSubEntityType": "DEALER",
                "visCustomerCode": visCustomerCode,
            };

            warehouseId && (customerData.warehouseId = warehouseId);
            if(priceListId){
                customerData.priceListIdentifierId = priceListId;
            }
            return $http.post(apiConstants.url + '/manufacturersubentity', customerData);
        }

        function createCustomerUser(userObject, manufacturerSubEntityId) {
            var userData = {
                "emailAddress": userObject.emailAddress,
                "firstName": userObject.firstName,
                "lastName": userObject.lastName,
                "userType": "MANUFACTURER_SUB_ENTITY_CUSTOMER",
                "manufacturerSubEntityId": manufacturerSubEntityId,
                "userPermissions": userObject.permissionsArray
            };
            var siteUrl = $location.protocol() + '://' + $location.host();
            var config = {headers: {'Site-Url': siteUrl}};

            var theme = localStorage.getItem("theme");
            if (theme && theme != null) {
                return $http.post(apiConstants.url + '/user?theme=' + theme.toLowerCase(), userData, config);
            } else {
                return $http.post(apiConstants.url + '/user', userData, config);
            }
        }

        function createDealerUser(userObject, manufacturerSubEntityId) {
            var userData = {
                "emailAddress": userObject.emailAddress,
                "firstName": userObject.firstName,
                "lastName": userObject.lastName,
                "userType": "MANUFACTURER_SUB_ENTITY_DEALER",
                "manufacturerSubEntityId": manufacturerSubEntityId,
                "userPermissions": userObject.permissionsArray,
                "visContactId": userObject.visContactId,
            };
            var siteUrl = $location.protocol() + '://' + $location.host();
            var config = {headers: {'Site-Url': siteUrl}};

            var theme = localStorage.getItem("theme");
            if (theme && theme != null) {
                return $http.post(apiConstants.url + '/user?theme=' + theme.toLowerCase(), userData, config);
            } else {
                return $http.post(apiConstants.url + '/user', userData, config);
            }
        }

        function createDealerPlusUser(userObject, manufacturerSubEntityId) {
            var userData = {
                "emailAddress": userObject.emailAddress,
                "firstName": userObject.firstName,
                "lastName": userObject.lastName,
                "userType": "MANUFACTURER_SUB_ENTITY_DEALER_PLUS",
                "manufacturerSubEntityId": manufacturerSubEntityId,
                "userPermissions": userObject.permissionsArray
            };
            var siteUrl = $location.protocol() + '://' + $location.host();
            var config = {headers: {'Site-Url': siteUrl}};

            return $http.post(apiConstants.url + '/user', userData, config);

        }

       function createManufacturerUser(userObject, manufacturerId) {
            var userData = {
                "emailAddress": userObject.emailAddress,
                "firstName": userObject.firstName,
                "lastName": userObject.lastName,
                "userType": "MANUFACTURER",
                "manufacturerId": manufacturerId,
                "userPermissions": userObject.permissionsArray,
                "userSettings": userObject.userSettings
            };
            var siteUrl = $location.protocol() + '://' + $location.host();
            var config = {headers: {'Site-Url': siteUrl}};

            var theme = localStorage.getItem("theme");
            if (theme && theme != null) {
                return $http.post(apiConstants.url + '/user?theme=' + theme.toLowerCase(), userData, config);
            } else {
                return $http.post(apiConstants.url + '/user', userData, config);
            }
        }

         function createSingleManufacturerUser(userObject, manufacturerId) {

          var userData = {
              "emailAddress": userObject.emailAddress,
              "firstName": userObject.firstName,
              "lastName": userObject.lastName,
              "userType": "MANUFACTURER",
              "manufacturerId": manufacturerId,
              "userPermissions": userObject.permissionsArray,
              "userSettings": userObject.userSettings,
              "notificationSubscriptions": userObject.notificationSubscriptions.map(sub => sub.purchaserId)
          };

          var siteUrl = $location.protocol() + '://' + $location.host();
          var config = { headers: { 'Site-Url': siteUrl } };

             return $http.post(apiConstants.url + '/manufacturers/' + manufacturerId + '/users', userData, config)
              .then(function(response) {
                  console.log("Successfully created manufacturer user:", response.data);
                  return response;
              })
              .catch(function(error) {
                  console.error("Error creating manufacturer user:", error);
                  throw error;
              });
      }

        function deleteManufacturerUser(manufacturerId, userId) {
            return $http.delete(apiConstants.url + '/manufacturers/' + manufacturerId + '/users/' + userId);
        }

        function editCustomerUser(userObject, manufacturerSubEntityId, userId) {
            var userData = {
                "emailAddress": userObject.emailAddress,
                "firstName": userObject.firstName,
                "lastName": userObject.lastName,
                "userType": "MANUFACTURER_SUB_ENTITY_CUSTOMER",
                "manufacturerSubEntityId": manufacturerSubEntityId,
                "active": userObject.active,
                "userPermissions": userObject.permissionsArray
            };
            var siteUrl = $location.protocol() + '://' + $location.host();
            var config = {headers: {'Site-Url': siteUrl}};

            var theme = localStorage.getItem("theme");
            if (theme && theme != null) {
                return $http.put(apiConstants.url + '/user/' + userId +'?theme=' + theme.toLowerCase(), userData, config);
            } else {
                return $http.put(apiConstants.url + '/user/' + userId, userData, config);
            }
        }

        function editDealerUser(userObject, manufacturerSubEntityId, userId) {
            var userData = {
                "emailAddress": userObject.emailAddress,
                "firstName": userObject.firstName,
                "lastName": userObject.lastName,
                "userType": "MANUFACTURER_SUB_ENTITY_DEALER",
                "manufacturerSubEntityId": manufacturerSubEntityId,
                "active": userObject.active,
                "userPermissions": userObject.permissionsArray,
                "visContactId": userObject.visContactId
            };
            var siteUrl = $location.protocol() + '://' + $location.host();
            var config = {headers: {'Site-Url': siteUrl}};
            return $http.put(apiConstants.url + '/user/' + userId, userData, config);
        }

        function editManufacturerUser(userObject, manufacturerId, userId) {
            var userData = {
                "emailAddress": userObject.emailAddress,
                "firstName": userObject.firstName,
                "lastName": userObject.lastName,
                "userType": "MANUFACTURER",
                "manufacturerId": manufacturerId,
                "userPermissions": userObject.permissionsArray,
                "userSettings": userObject.userSettings,
                "active": userObject.active
            };
            var siteUrl = $location.protocol() + '://' + $location.host();
            var config = { headers: { 'Site-Url': siteUrl } };
            return $http.put(apiConstants.url + '/user/' + userId, userData, config);
        }

        function editSingleManufacturerUser(userObject, manufacturerId, userId) {
            console.log("editManufacturerUser called with arguments:", userObject, manufacturerId, userId);

            var userData = {
                emailAddress: userObject.emailAddress,
                firstName: userObject.firstName,
                lastName: userObject.lastName,
                userType: "MANUFACTURER",
                manufacturerId: manufacturerId,
                userPermissions: userObject.permissionsArray,
                userSettings: userObject.userSettings,
                notificationSubscriptions: userObject.notificationSubscriptions.map(sub => sub.purchaserId),
                userStatus: userObject.userStatus,
            };

            console.log("userData constructed:", userData);

            var url = apiConstants.url + '/manufacturers/' + manufacturerId + '/users/' + userId;
            console.log("HTTP PUT request URL:", url);

            return $http.put(url, userData)
                .then(function(response) {
                    console.log("Successfully edited manufacturer user:", response.data);
                    return response;
                })
                .catch(function(error) {
                    console.error("Error editing manufacturer user:", error);
                    throw error;
                });
        }
        
        function createManufacturer(manufacturerName) {
            var data = {
                "name": manufacturerName,
                "description": ""
            };
            return $http.post(apiConstants.url + '/manufacturer', data);
        }

    }
})();
