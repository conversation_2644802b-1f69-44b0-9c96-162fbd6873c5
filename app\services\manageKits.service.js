(function () {
    "use strict";
    angular.module("app.services").factory("manageKitService", manageKitService);

    manageKitService.$inject = ["$http", "apiConstants", "userService"];

    function manageKitService($http, apiConstants, userService) {
        return {
            fetchKitsToManage: fetchKitsToManage,
            editKit: editKit,
            createKit: createKit,
            updateKit: updateKit,
            deleteKit: deleteKit,
        };
        function fetchKitsToManage(manufacturerId) {
            return $http.get(apiConstants.url + "/manufacturers/" + manufacturerId + "/master-part-kits");
        }

        function editKit(KitId) {
            var manufacturerId = userService.getManufacturerId();
            return $http.get(apiConstants.url + "/manufacturers/" + manufacturerId + "/master-part-kits/" + KitId);
        }

        function createKit(kitData) {
            var manufacturerId = userService.getManufacturerId();
            console.log("Create kit request: ", kitData);
            return $http.post(apiConstants.url + "/manufacturers/" + manufacturerId + "/master-part-kits", kitData);
        }

        function updateKit(kitData, kitID) {
            var manufacturerId = userService.getManufacturerId();
            console.log("Update kit request: ", kitData);
            return $http.put(apiConstants.url + "/manufacturers/" + manufacturerId + "/master-part-kits/" + kitID, kitData);
        }

        function deleteKit(kitID) {
            var manufacturerId = userService.getManufacturerId();
            return $http.delete(apiConstants.url + "/manufacturers/" + manufacturerId + "/master-part-kits/" + kitID);
        }
    }
})();
