(function () {
    'use strict';

    angular
        .module('app.viewer')
        .controller('CreateLinkedPartController', CreateLinkedPartController);

    CreateLinkedPartController.$inject = ['$stateParams', '$scope', 'viewerService', 'viewerBannerService', '$rootScope', 'viewerHelperService', 'manufacturerPublicationService', '$translate'];

    function CreateLinkedPartController($stateParams, $scope, viewerService, viewerBannerService, $rootScope, viewerHelperService, manufacturerPublicationService, $translate) {
        var vm = this;
        var linkedPartId;
        var editLinkedPartResponse;
        var isEdit = false;
        var isEditSetup = false;

        vm.errors = {};
        vm.isOpen = false;
        vm.selectedPart = [];
        vm.modelId = $stateParams.modelId;
        vm.isMachineDropdownDisabled = true;
        vm.isViewableDropdownDisabled = true;

        vm.rangeChanged = rangeChanged;
        vm.machineChanged = machineChanged;
        vm.saveLinkedPart = saveLinkedPart;
        vm.cancel = cancel;

        var WENT_WRONG, LINK_SUCCESS;
        $translate(['GENERAL.WENT_WRONG', 'CREATE_LINKED.LINK_SUCCESS'])
            .then(function (resp) {
                WENT_WRONG = resp["GENERAL.WENT_WRONG"];
                LINK_SUCCESS = resp["CREATE_LINKED.LINK_SUCCESS"];
            });


        function initialize() {
            vm.rangeValues = null;
            vm.rangeId = null;
            vm.machineId = null;
            vm.machines = null;
            vm.selectedModel = null;
            vm.viewables = null;

            isEdit = !!linkedPartId;
            isEditSetup = isEdit;

            if (isEdit) {
                getParkLink();
            } else {
                getRange();
            }

        }

        function getRange() {
            manufacturerPublicationService.getRangeByManufacturer()
                .then(getRangeSuccess, getRangeFailure);
        }

        function getRangeSuccess(response) {
            vm.rangeValues = response.data.productRanges;
            vm.isRangeDropdownDisabled = false;

            if (isEditSetup) {
                rangeChanged(editLinkedPartResponse.rangeId);
            }
        }

        function getRangeFailure() {
            console.log(err);
            viewerBannerService.setNotification('WARN', WENT_WRONG, 5000);
        }

        function rangeChanged(rangeId) {
            if (rangeId) {
                vm.isViewableDropdownDisabled = true;
                vm.isMachineDropdownDisabled = true;
                vm.rangeId = rangeId;
                vm.machineId = null;
                vm.machines = null;
                vm.selectedModel = null;
                vm.viewables = null;
                getMachineByRange(vm.rangeId);
            }
        }

        function getMachineByRange(rangeId) {
            manufacturerPublicationService.getMachineByRange(rangeId)
                .then(machineRangeSuccess, machineRangeFailure);
        }

        function machineRangeSuccess(response) {
            vm.machines = response.data;
            vm.isMachineDropdownDisabled = false;

            if (isEditSetup) {
                machineChanged(editLinkedPartResponse.machineId);
            }
        }

        function machineRangeFailure(err) {
            console.log(err);
            viewerBannerService.setNotification('WARN', WENT_WRONG, 5000);
        }

        function machineChanged(machineId) {
            if (machineId) {
                vm.isViewableDropdownDisabled = true;
                vm.machineId = machineId;
                vm.viewables = null;
                vm.selectedModel = null;
                getModelByMachine(vm.machineId);
            }
        }

        function getModelByMachine(machineId) {
            manufacturerPublicationService.getCompletedModelUploadsByMachine(machineId)
                .then(getLinkableViewablesSuccess, getLinkableViewablesFailed);
        }

        function getLinkableViewablesSuccess(response) {
            if (response && response.data.length > 0) {
                vm.isViewableDropdownDisabled = false;
                vm.viewables = [];
                for (var i = 0; i < response.data.length; i++) {
                    if (response.data[i].modelId !== vm.modelId)
                        vm.viewables.push(response.data[i]);
                }
                if (isEditSetup) {
                    for (var i = 0; i < vm.viewables.length; i++) {
                        if (vm.viewables[i].modelId === editLinkedPartResponse.modelId) {
                            vm.selectedModel = vm.viewables[i];
                        }
                    }
                    isEditSetup = false;
                    viewerHelperService.selectParts([editLinkedPartResponse.objectId]);
                }
            }
        }

        function getLinkableViewablesFailed(error) {
            viewerBannerService.setNotification('ERROR', error.data.error, 3000);
        }

        function getParkLink() {
            viewerService.getPartLink(linkedPartId)
                .then(getPartLinkSuccess)
                .catch(getPartLinkFailed);
        }

        function getPartLinkSuccess(response) {
            if (response && response.data.modelId) {
                editLinkedPartResponse = response.data;

                getRange();
            }
        }

        function getPartLinkFailed(err) {
            console.log(err);
            viewerBannerService.setNotification('WARN', WENT_WRONG, 5000);
        }

        function cancel() {
            vm.viewables = {};
            vm.selectedModel = '';
            vm.isOpen = false;
            vm.selectedViewableId = "";
            isEdit = false;
            vm.viewables = [];
            $rootScope.$broadcast("create-linked-part-closed");
        }

        function saveLinkedPart() {
            vm.errors.noPartSelected = vm.selectedPart.partId === undefined;
            vm.errors.noViewableSelected = vm.selectedModel === null;

            if (vm.errors.noPartSelected || vm.errors.noViewableSelected || vm.errors.alreadyHasLinkedPart) {
                return;
            }

            vm.selectedViewableId = vm.selectedModel.modelId;

            if (isEdit) {
                viewerService.editPartModelLink(vm.selectedPart.objectId, vm.selectedViewableId, linkedPartId)
                    .then(saveEditPartModelLinkSuccess)
                    .catch(saveEditPartModelLinkFailed);
            } else {
                viewerService.savePartModelLink(vm.modelId, vm.selectedPart.objectId, vm.selectedViewableId)
                    .then(saveEditPartModelLinkSuccess)
                    .catch(saveEditPartModelLinkFailed);
            }
        }

        function saveEditPartModelLinkSuccess(response) {
            viewerBannerService.setNotification('SUCCESS', LINK_SUCCESS, 2000);
            vm.isOpen = false;
            $rootScope.$broadcast("create-linked-part-closed");
            $rootScope.$broadcast('node-link-part-added', vm.selectedPart.objectId);
        }

        function saveEditPartModelLinkFailed(error) {
            viewerBannerService.setNotification('ERROR', error.data.error, 3000);
        }

        $scope.$on("viewer-part-selected", function (event, partViewerDetails) {

            if (partViewerDetails.length === 1) {
                vm.selectedPart = partViewerDetails[0].part;
                vm.errors.alreadyHasLinkedPart = !isPartValidForLinking(partViewerDetails);
            } else if (partViewerDetails.length > 1) {
                vm.selectedPart = partViewerDetails
            } else {
                vm.selectedPart = [];
            }
        });

        function isPartValidForLinking(viewerPartDetails) {
            if (viewerPartDetails.linked) {
                if (isEdit) {
                    return linkedPartId === viewerPartDetails.linkedModel.id
                }
                return false;
            }
            return true;
        }

        $scope.$on("create-linked-part-opened", function (event, aLinkedPartId) {
            vm.isOpen = true;
            linkedPartId = aLinkedPartId;
            initialize();
        });
    }

})();
