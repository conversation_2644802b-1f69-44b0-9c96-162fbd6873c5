(function () {
  'use strict';

  angular
  .module('app.services')
  .factory('modelService', modelService);

  modelService.$inject = ['$http', 'apiConstants', '$q'];

  function modelService($http, apiConstants, $q) {
    return {
      fetchModels: fetchModels,
      fetchModel: fetchModel,
      fetchModelTranslationWarnings: fetchModelTranslationWarnings,
      editModel: editModel,
      cloneModel: cloneModel,
      exportPartDetailsToCSV : exportPartDetailsToCSV
    };

    function fetchModels(machineId) {
      return $http.get(apiConstants.url + '/machine/' + machineId + '/models', null);
    }

    function fetchModel(modelId) {
      return $http.get(apiConstants.url + '/model/' + modelId);
    }

    function fetchModelTranslationWarnings(modelId) {
      return $http.get(apiConstants.url + '/model/' + modelId + '/translationErrors', null);
    }

    function editModel(modelName, modelId) {
      var updateData = {
        "modelName": modelName,
        "modelDescription": "New Description"
      };
      return $http.put(apiConstants.url + '/model/' + modelId, updateData);
    }

    function cloneModel(modelId, cloneSettings) {
      return $http.post(apiConstants.url + '/model/' + modelId + '/clone', cloneSettings);
    }

      function exportPartDetailsToCSV(modelId) {
          return $http.get(apiConstants.url + '/model/' + modelId + '/partDetailDownload');
      }

  }
})();
