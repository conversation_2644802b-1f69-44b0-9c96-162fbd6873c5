// =============================================================================
// Mixins
// ============================================================================

$small:  767px;
$medium: 992px;
$large:  1200px;

@mixin breakpoint($media) {
     @if $media == small{
        @media only screen and (max-width: $small) {
             @content;
        }
    }
    @else if $media == medium {
         @media only screen and (min-width: $medium){
            @content;
        }
    }
    @else if $media == large {
         @media only screen and (min-width: $large){
            @content;
        }
    }
    @else if $media == xlarge {
         @media only screen and (min-width: $xlarge){
            @content;
        }
    }
    @else if $media == xxlarge {
         @media only screen and (min-width: $xxlarge){
            @content;
        }
    }
}


// Box Shadows

@mixin box-shadow($top, $left, $blur, $color, $inset: false) {
  @if $inset {
    -webkit-box-shadow:inset $left $top $blur $color;
    -moz-box-shadow:inset $left $top $blur $color;
    box-shadow:inset $left $top $blur $color;
  } @else {
    -webkit-box-shadow: $left $top $blur $color;
    -moz-box-shadow: $left $top $blur $color;
    box-shadow: $left $top $blur $color;
  }
}

@mixin box-shadow-spread($top, $left, $blur, $spread, $color, $inset: false) {
  @if $inset {
    -webkit-box-shadow:inset $left $top $blur $spread $color;
    -moz-box-shadow:inset $left $top $blur $spread $color;
    box-shadow:inset $left $top $blur $spread $color;
  } @else {
    -webkit-box-shadow: $left $top $blur $spread $color;
    -moz-box-shadow: $left $top $blur $spread $color;
    box-shadow: $left $top $blur $spread $color;
  }
}

@mixin transition($args...) {
  -webkit-transition: $args;
  -moz-transition: $args;
  -ms-transition: $args;
  -o-transition: $args;
  transition: $args;
}


@mixin border-radius($radius) {
  -webkit-border-radius: $radius;
  border-radius: $radius;
  background-clip: padding-box;  /* stops bg color from leaking outside the border: */
}