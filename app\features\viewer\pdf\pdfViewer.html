<div id="manufacturerViewerId">
    <div class=""
         style="width:100% !important;height:100% !important;"
         ng-show="pdfViewerCtrl.showCreateNewPart"></div>

    <div class="vertical-align loader"id="loader">
        <div id="loader-text">
            <i class="fas fa-sync-alt fa-spin"></i>
            <p translate>PDF_VIEWER.LOADING_ASSETS</p>
            <p translate>PDF_VIEWER.FEW_MINS</p>
        </div>
    </div>

    <header class="site-header viewer-header">
        <a ng-if="!pdfViewerCtrl.fromWhereUsedModal" href="" class="back-to-products" ng-click="pdfViewerCtrl.backToViewables()">
            <div class="viewer-back-arrow">
                <i class="fa fa-chevron-left"></i>
            </div>
            <h3>
                {{pdfViewerCtrl.machineName}} - {{pdfViewerCtrl.viewableName}}<br />
                <small translate>PDF_VIEWER.BACK_TO</small>
            </h3>
        </a>

        <a ng-if="pdfViewerCtrl.fromWhereUsedModal" href="" class="back-to-products" ng-click="pdfViewerCtrl.goBackToPartSearch()">
            <div class="viewer-back-arrow">
                <i class="fa fa-chevron-left"></i>
            </div>
            <h3>
                {{pdfViewerCtrl.machineName}} - {{pdfViewerCtrl.viewableName}}<br />
                <small translate>PDF_VIEWER.BACK_TO_PART_SEARCH</small>
            </h3>
        </a>

        <button id="preview_button" class="btn xsmall primary-outline previewButton"
                ng-click="pdfViewerCtrl.openCustomerViewerPreviewPage(model)" translate>
            PDF_VIEWER.PREVIEW_AS
        </button>

        <div class="expand-model-browser-button">
            <button class="btn-model" ng-click="pdfViewerCtrl.openAddParts()">
               <i class="fa fa-plus-circle"></i>&nbsp;
                {{"PDF_VIEWER.ADD_PARTS" | translate}}
            </button>
        </div>


    </header>

    <div class="product-viewer vertical">
        <viewer-banner></viewer-banner>


        <add-part confirm-button-text="{{'PDF_VIEWER.SAVE_PARTS' | translate}}" title="{{'PDF_VIEWER.ADD_NEW_PART' | translate}}" decline-button-text="{{'GENERAL.CANCEL' | translate}}"
                  is-visible="pdfViewerCtrl.isCreateNewPartVisible()"
                  load-parts="pdfViewerCtrl.getNonModeledParts()"
                  on-confirm="pdfViewerCtrl.updateNonModeledParts(partList)"
                  on-decline="pdfViewerCtrl.hideCreateNewPart()"
                  model-id="{{pdfViewerCtrl.modelId}}" add-part-list="pdfViewerCtrl.createdParts">
        </add-part>

        <div class="viewer" id="MyViewerDiv"></div>

    </div>

    <div class="product-thumbnails-carousel-wrap vertical customer pdf">
        <div class="product-thumbnails-carousel ">

            <div class="product-thumb-cell" ng-repeat="page in pdfViewerCtrl.pdfPages track by $index">

                <div ng-class="page.selected === true ? 'selected-pdf' : 'unselected-pdf'"
                     ng-click="pdfViewerCtrl.goToPage(page.stateId)" class="pdf-title d-flex justify-content-between">
                        	<span>{{page.stateId -0 +1 }} - {{page.stateName}}</span>
				    <i class="fa fa-pencil" ng-click="pdfViewerCtrl.editPDFName(page.stateId)"></i>
                </div>

            </div>

        </div>


    </div>

</div>
