(function () {
    'use strict';

    angular
        .module('app.services')
        .factory('persistentSearch', persistentSearch);

    persistentSearch.$inject = [];

    function persistentSearch() {
        return {
            setPartSearchValue: setPartSearchValue,
            getPartSearchValue: getPartSearchValue,
            getPartSearchType: getPartSearchType,
            setPartSearchType: setPartSearchType

        };

        function setPartSearchType(value) {
            // Assuming value is 'partNumber' or 'partDescription'
            localStorage.setItem('searchType', value);
        }

        function getPartSearchType() {
            return localStorage.getItem('searchType') || 'partNumber'; // Default to 'partNumber'
        }


        function setPartSearchValue(value) {
            localStorage.setItem('searchValue', value);
        }

        function getPartSearchValue() {
            return localStorage.getItem('searchValue') || '';
        }

    }
})();
