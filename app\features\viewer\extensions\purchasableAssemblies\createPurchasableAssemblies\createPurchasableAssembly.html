<div class="sidebar-content" ng-show="createPurchasableAssemblyCtrl.isOpen">
    <form class="form">

        <div>

            <div class="well clearfix">
                <div class="selected-part-text" ng-hide="createPurchasableAssemblyCtrl.selectedPart.length > 1">
                    <h4 translate>CREATE_ASSEMBLY.SELECTED_PART</h4>
                    <span ng-hide="createPurchasableAssemblyCtrl.selectedPart.length === 0">
                    {{createPurchasableAssemblyCtrl.selectedPart.partNumber}} &nbsp;<small><strong>{{createPurchasableAssemblyCtrl.selectedPart.partDescription}}</strong></small>
                </span>
                    <span ng-show="createPurchasableAssemblyCtrl.selectedPart.length === 0" translate>CREATE_ASSEMBLY.NONE</span>
                </div>

                <div class="selected-part-error" ng-show="createPurchasableAssemblyCtrl.selectedPart.length > 1">
                    <h4 translate>CREATE_ASSEMBLY.MULTI_PARTS</h4>
                    <p translate>CREATE_ASSEMBLY.INDIVIDUALS_ONLY</p>
                </div>
            </div>

            <p class="mt-16" translate>CREATE_ASSEMBLY.PLEASE_SELECT</p>

            <button ng-click="createPurchasableAssemblyCtrl.selectParentPart()" class="btn small primary-outline" translate>
                CREATE_ASSEMBLY.SELECT_ASSEMBLY
            </button>


        </div><!-- /side-menu-content -->

        <div class="side-menu-content">

            <div class="error-well mt-16" ng-show="createPurchasableAssemblyCtrl.errors.noPartSelected || createPurchasableAssemblyCtrl.errors.isPartAlreadyAnAssembly">

                <p ng-show="createPurchasableAssemblyCtrl.errors.noPartSelected" translate>
                    CREATE_ASSEMBLY.SELECT_ERROR
                </p>
                <p ng-show="createPurchasableAssemblyCtrl.errors.isPartAlreadyAnAssembly" translate>
                    CREATE_ASSEMBLY.ALREADY_ERROR
                </p>
            </div>

            <div class="purchasable-assembly-actions">
                <button class="btn small secondary" type="button" ng-click="createPurchasableAssemblyCtrl.cancel()" translate>GENERAL.CANCEL
                </button>
                <button class="btn small primary purchasable-assembly-button" ng-click="createPurchasableAssemblyCtrl.savePurchasableAssembly()" translate>
                    CREATE_ASSEMBLY.SAVE
                </button>
            </div>

        </div><!-- /side-menu-content -->

    </form>


</div>