(function () {
    'use strict';

    angular
        .module('app.orders')
        .controller('DPQuotationController', DPQuotationController);

    DPQuotationController.$inject = ['dpOrdersService', '$rootScope', '$scope', '$controller', 'userService', '$state', 'headerBannerService', '$uibModal', '$translate'];

    function DPQuotationController(dpOrdersService, $rootScope, $scope, $controller, userService, $state, headerBannerService, $uibModal, $translate) {
        var vm = this;

        angular.extend(vm, $controller('DPOrderController', {$scope: $scope}));

        vm.nextStep = nextStep;
        vm.goToSplitOrder = goToSplitOrder;
        vm.openComment = openComment;
        vm.reloadOrder = reloadOrder;
        vm.altStep = altStep;
        vm.isPartInformationAvailable = isPartInformationAvailable;
        vm.hasVisibleDiscountedPrices = hasVisibleDiscountedPrices

        var WENT_WRONG, PLACE_ORDER, QUOTE_SENT, ENTER_PO, QUOTATION_SUCCESS;
        $translate(['GENERAL.WENT_WRONG', 'QUOTATION.PLACE_ORDER', 'QUOTATION.QUOTE_SENT', 'QUOTATION.ENTER_PO', 'QUOTATION.QUOTATION_SUCCESS', 'QUOTATION.UPDATE_QUOTE', 'ORDERS.QUOTE'])
            .then(function (resp) {
                WENT_WRONG = resp["GENERAL.WENT_WRONG"];
                PLACE_ORDER = resp["QUOTATION.PLACE_ORDER"];
                QUOTE_SENT = resp["QUOTATION.QUOTE_SENT"];
                ENTER_PO = resp["QUOTATION.ENTER_PO"];
                QUOTATION_SUCCESS = resp["QUOTATION.QUOTATION_SUCCESS"];
                vm.altStepText = resp["QUOTATION.UPDATE_QUOTE"];
                vm.STAGE = resp["ORDERS.QUOTE"];
                vm.orderStatusPill = QUOTE_SENT;
            });


        initialize();

        function initialize() {
            vm.showPurchaseOrder = true;
            vm.isDetailsEditable = false;
            vm.isPriceEditable = false;
            vm.showAltStepBtn = false;
            vm.showEstimatedDelivery = false;
            vm.isNotesEditable = false;
            vm.displayNotes = false;
            vm.isDiscountEditable = false;
            vm.isDiscountVisible = false;
            vm.exportPartDataCsvVisible = true;
            vm.showSplitOrderBtn = false;
            vm.splitOrders = [];
            vm.showCancelOrderButton = true;
            vm.showPartDescription = false;
            vm.showModelName = false;
            vm.showProductName = false;

            vm.showAltStepBtn = true;
            vm.showNextStep = false;
            vm.isActionActive = false;
            vm.isPurchaseOrderEditable = false;
            vm.deleteOrderItem = true;
            vm.isShippingEditable = true;
            vm.isQuantityEditable = true;


            getOrder()
        }

        function getOrder() {
            dpOrdersService.getOrder(vm.orderId)
                .then(getOrdersSuccess, serviceCallFailed);
        }

        function getOrdersSuccess(response) {

            $rootScope.$broadcast("Update-Unread-DP-Order-Tabs");

            if (response.data.orderStatus === 'QUOTE') {
                vm.data = response.data;
                vm.orderLists = response.data.orderItems;
                vm.totalItems = vm.orderLists.length;
                vm.billingAddress = vm.createReadableAddress(response.data.billingAddress);
                vm.shippingAddress = vm.createReadableAddress(response.data.shippingAddress);
                vm.data.originalShippingPrice = vm.data.shippingPrice;
                vm.data.shippingPrice = vm.data.shippingPrice ? vm.data.shippingPrice : 0;
                vm.isOrderItemsLoaded = true;
                vm.displayNotes = vm.data.notes !== null && vm.data.notes !== undefined;
                vm.parentOrderId = vm.data.parentOrderId;
                vm.parentCustomOrderDisplay = vm.data.parentCustomOrderDisplay;
                vm.selectedCurrency = userService.getCurrencyData(vm.data.currency);
                if (vm.data.percentageDiscount > 0) {
                    vm.isDiscountVisible = true;
                }

                vm.updateItemTotals(true, this, true);
                calculateSplitOrders();
                vm.isPreviewStockLevelEnabled = userService.getPreviewStockLevelEnabled();
                if (vm.data.associatedDPOrderIds) {
                    for (var i = 0; i < vm.data.associatedDPOrderIds.length; i++) {
                        vm.associatedDPOrderIds.push({
                            associatedDPOrderIds: vm.data.associatedDPOrderIds[i],
                            associatedDPCustomDisplayNumbers: vm.data.associatedDPCustomDisplayNumbers[i]
                        })
                    }
                }
            } else {
                vm.redirectToOrder(response.data.orderId, response.data.orderStatus);
            }
        }

        function serviceCallFailed(error) {
            vm.isOrderItemsLoaded = true;
            headerBannerService.setNotification('ERROR', error.data.error, 10000);
        }

        function nextStep() {
            if (vm.data.purchaseOrder === null || vm.data.purchaseOrder === '' || vm.data.purchaseOrder === undefined) {
                headerBannerService.setNotification('WARN', ENTER_PO, 10000);
            } else {
                vm.data.orderStatus = "PROCESSED";
                vm.data.price = vm.total;
                dpOrdersService.updateOrder(vm.data)
                    .then(updateOrderSuccess, updateOrderFailed);
            }
        }

        function updateOrderSuccess() {
            headerBannerService.setNotification('SUCCESS', QUOTATION_SUCCESS, 10000);
            var viewState = 'orders.liveorder';

            $state.go(viewState, {
                orderId: vm.data.orderId
            });
        }

        function updateOrderFailed() {
            headerBannerService.setNotification('ERROR', WENT_WRONG, 10000);
        }

        function goToSplitOrder(splitOrderId) {
            $state.go('dpOrders.customerOrders.orders.enquiry', {orderId: splitOrderId});
        }

        function calculateSplitOrders() {
            vm.splitOrders = [];
            if (vm.orderLists) {
                for (var i = 0; i < vm.orderLists.length; i++) {
                    if (vm.orderLists[i].splitOrderIds && vm.orderLists[i].splitOrderIds.length > 0) {
                        for (var j = 0; j < vm.orderLists[i].splitOrderIds.length; j++) {
                            if (!_.findWhere(vm.splitOrders, {orderId: vm.orderLists[i].splitOrderIds[j]})) {
                                var splitOrder = {
                                    orderId: vm.orderLists[i].splitOrderIds[j],
                                    displayId: vm.orderLists[i].splitOrderCustomDisplayNumbers[j] ? vm.orderLists[i].splitOrderCustomDisplayNumbers[j] : vm.orderLists[i].splitOrderIds[j]
                                }
                                vm.splitOrders.push(splitOrder);
                            }
                        }
                    }
                }
            }
            if (vm.additionalParts) {
                for (var k = 0; k < vm.additionalParts.length; k++) {
                    if (vm.additionalParts[k].splitOrderIds && vm.additionalParts[k].splitOrderIds.length > 0) {
                        for (var l = 0; l < vm.additionalParts[k].splitOrderIds.length; l++) {
                            if (!_.findWhere(vm.splitOrders, {orderId: vm.additionalParts[k].splitOrderIds[l]})) {
                                var splitOrder = {
                                    orderId: vm.additionalParts[k].splitOrderIds[l],
                                    displayId: vm.additionalParts[k].splitOrderCustomDisplayNumbers[l] ? vm.additionalParts[k].splitOrderCustomDisplayNumbers[l] : vm.additionalParts[k].splitOrderIds[l]
                                }
                                vm.splitOrders.push(splitOrder);
                            }
                        }
                    }
                }
            }

        }

        function openComment(commentThread, orderItemId) {
            var commentObject = {
                threadId: commentThread,
                orderId: vm.orderId,
                orderItemId: orderItemId
            };

            $uibModal.open({
                templateUrl: 'features/shared/comments/comments.html',
                controller: 'CommentController',
                controllerAs: 'commentsCtrl',
                resolve: {
                    commentObject: function () {
                        return commentObject;
                    }
                }
            }).result.then(function (commentObject) {

                if (commentObject.orderItemId) {
                    var index = _.findIndex(vm.data.orderItems, {orderItemId: commentObject.orderItemId});
                    vm.data.orderItems[index].commentThread = {};
                    vm.data.orderItems[index].commentThread.orderItemId = commentObject.orderItemId;
                    vm.data.orderItems[index].commentThread.id = commentObject.threadId;
                    vm.data.orderItems[index].commentThread.orderId = commentObject.orderId;
                } else {
                    vm.data.commentThread = {};
                    vm.data.commentThread.id = commentObject.threadId;
                    vm.data.commentThread.orderId = commentObject.orderId;
                }
            }, function () {
                console.log('Modal Cancelled');
            });

        }

        function reloadOrder() {
            getOrder();
        }

        function altStep() {
            updateData();
            updateOrder();
        }

        function updateData() {
            vm.data.price = vm.total;
            vm.data.additionalParts = vm.additionalParts;
            vm.data.currency = vm.selectedCurrency;
            if (vm.isCustDetailEdit) {
                vm.data.contactName = vm.newContactName;
                vm.data.deliveryName = vm.deliveryName;
                vm.data.contactNumber = vm.newContactNumber;
                vm.data.deliveryNumber = vm.newDeliveryNumber;
                vm.data.shippingAddress = JSON.parse(vm.newDeliveryAddress);
                vm.data.billingAddress = JSON.parse(vm.newBillingAddress);
            }
        }

        function updateOrder() {
            dpOrdersService.updateOrder(vm.data)
                .then(updateOrderSuccessful, updateOrderFailed);
        }

        function updateOrderSuccessful() {
            headerBannerService.setNotification('SUCCESS', QUOTATION_SUCCESS, 10000);
            vm.isOrderEdited = false;
            getOrder();
        }

        $scope.$on("Order-Edited", orderEdited);

        function orderEdited() {
            vm.isOrderEdited = true;
        }

        function isPartInformationAvailable(item) {
            var partInformation = {
                showPartDescription: item.partDescription,
                showModelName: item.modelName,
                showMachineName: item.machineName
            };
            vm.showPartDescription = partInformation.showPartDescription;
            vm.showModelName = partInformation.showModelName;
            vm.showMachineName = partInformation.showMachineName;
            return partInformation;
        }

        function hasVisibleDiscountedPrices() {

            if (!vm.data || !vm.data.orderItems) {
                return false;
            }

            var orderItems = vm.data.orderItems;

            var hasDiscountedPrices = orderItems.some(item => item.discountedPrice && item.discountedPrice !== 0);

            return hasDiscountedPrices;
        }

    }
})();
