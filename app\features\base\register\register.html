<div class="d-flex justify-content-center align-items-md-center vh-100 pt-4 pt-md-0" >

	  <div class="col-12 col-lg-10">

	  <div class="d-flex justify-content-md-between cadGap justify-content-center flex-wrap align-items-center w-100">
		    <img alt="CadShareLogo" class="col-8 col-md-4 col-lg-2" ng-src="{{appCtrl.logoUrl}}">
		    <h1 class="text-center mb-0">{{'REGISTER.TITLE' | translate}} - {{'REGISTER.SIGN_UP' | translate}}</h1>
	  </div>

	  <hr class="underline_order border-bottom-0">

  <form name="registerForm" ng-submit="registerCtrl.signUp()">
    <div class="row">
      <div class="col-12">
        <div class="error-alert" ng-if="registerCtrl.error && !registerCtrl.deliveryAddressStr">
          <p class="errortext" translate>REGISTER.NO_DELIVERY_ADDRESS</p>
        </div>

        <div class="error-alert"
          ng-if="registerCtrl.error && !registerCtrl.machines.length && registerCtrl.deliveryAddressStr">
          <p class="errortext" translate>REGISTER.NO_MACHINE</p>
        </div>

        <div class="error-alert" ng-if="registerCtrl.isSignUpError && !registerCtrl.messageErrorLoginFail">
          <p class="errortext" translate>REGISTER.SIGN_UP_FAILED</p>
        </div>

        <div class="error-alert" ng-if="registerCtrl.messageErrorLoginFail">
          <p class="errortext"> {{registerCtrl.messageErrorLoginFail }} <span>{{'REGISTER.PLEASE_CONTACT_SUPREME' | translate}}</span>
            <a href="mailto:{{registerCtrl.manufacturerSupportEmail}}">{{registerCtrl.manufacturerSupportEmail}}</a>
          </p>
        </div>
      </div>

		<div class="col-12 col-md customDivContainer">
			  <div class="customDivStyling">
        <div class="input-group mb-1">
          <label>{{'CREATE_USER.EMAIL_ADDRESS' | translate}}
            <span class="text-danger">*</span>
          </label>
          <input type="email" name="emailAddress" ng-model="registerCtrl.user.emailAddress" required
            ng-class="{'has-error': registerCtrl.submitting && registerForm.emailAddress.$invalid}"
            placeholder="{{'CREATE_USER.EMAIL_ADDRESS' | translate}}">
        </div>

        <div class="input-group mb-1">
          <label>{{'CREATE_USER.FIRST_NAME' | translate}}
            <span class="text-danger">*</span>
          </label>
          <input type="text" ng-model="registerCtrl.user.firstName" name="firstName" required
            ng-class="{'has-error': registerCtrl.submitting && registerForm.firstName.$invalid}"
            placeholder="{{'CREATE_USER.FIRST_NAME' | translate}}">
        </div>

        <div class="input-group mb-1">
          <label>{{'CREATE_USER.LAST_NAME' | translate}}
            <span class="text-danger">*</span>
          </label>
          <input type="text" ng-model="registerCtrl.user.lastName" name="lastName" required
            ng-class="{'has-error': registerCtrl.submitting && registerForm.lastName.$invalid}"
            placeholder="{{'CREATE_USER.LAST_NAME' | translate}}">
        </div>

				    <div class="input-group mb-1">
						<label>{{'REGISTER.CONTACT_NUMBER' | translate}}
							  <span class="text-danger">*</span>
						</label>

						<input type="text" ng-model="registerCtrl.user.contactNumber" name="contactNumber" required
							 pattern="^\+?\d{8,15}$"
							 ng-class="{'has-error': registerCtrl.submitting && registerForm.contactNumber.$invalid}"
							 placeholder="{{'REGISTER.CONTACT_NUMBER' | translate}}">
				    </div>

      </div>
		</div>

		<div class="col-12 col-md customDivContainer">
			  <div class="customDivStyling">

			  <div class="input-group mb-1">
				    <label>{{'REGISTER.COMPANY_NAME' | translate}}
						<span class="text-danger">*</span>
				    </label>
				    <input class="pb-0" type="text" ng-model="registerCtrl.user.companyName" name="companyName" required
					     ng-class="{'has-error': registerCtrl.submitting && registerForm.companyName.$invalid}"
					     placeholder="{{'REGISTER.COMPANY_NAME' | translate}}">
			  </div>

			  <div class="input-group">
				    <label>{{'REGISTER.DELIVERY_ADDRESS' | translate}}
						<span class="text-danger">*</span>
				    </label>
				    <div class="full-width-comment-registration px-2" rows="2" required>
						{{registerCtrl.deliveryAddressStr}}
				    </div>

				    <a class="btn secondary mt-3" ng-click="registerCtrl.updateAddress()"
						ng-class="{'has-error': registerCtrl.error && !registerCtrl.deliveryAddressStr}"
						translate>REGISTER.CREATE_EDIT_ADDRESS</a>
			  </div>
		</div>
			  </div>

		<div class="col-12 col-md-12 col-lg customDivContainer">
			  <div class="customDivStyling">

        <div class="input-group">
          <label>{{'REGISTER.YOUR_MACHINES' | translate}}
            <span class="text-danger">*</span>
          </label>
          <span>{{'REGISTER.MACHINE_YOU_WANT' | translate}}</span>
          <label class="mt-2">
            <span>{{'REGISTER.NUMBER_CURRENT_MACHINE_FIRST' | translate}}</span>
            <strong style="color:cadetblue">{{ registerCtrl.machines.length }}</strong>
            <span>{{'REGISTER.NUMBER_CURRENT_MACHINE_SECOND' | translate}}</span>
          </label>

		    <div class="w-100 machine_table mb-4 border-bottom">

		    <table class="table table-bordered mb-3">
				<thead>
				<tr>
					  <th translate>REGISTER.MACHINE.MACHINE_NAME</th>
					  <th translate>REGISTER.MACHINE.RANGE_NAME</th>
				</tr>
				</thead>
				<tbody>
				<tr ng-repeat="item in registerCtrl.machines">
					  <td data-label="{{ 'REGISTER.MACHINE.MACHINE_NAME' | translate}}">{{ item.manualName }}</td>
					  <td data-label="{{ 'REGISTER.MACHINE.RANGE_NAME' | translate}}">{{ item.rangeName }}</td>
				</tr>

				<tr ng-if="!registerCtrl.machines.length > 0">
					  <td class="flex-start noPartsBG" colspan="10" translate>REGISTER.MACHINE.NO_MACHINE</td>
				</tr>

				</tbody>
		    </table>

		    </div>

          <button class="btn secondary" ng-click="registerCtrl.viewUpdateMachine()"
            ng-class="{'has-error': registerCtrl.error && !registerCtrl.machines.length}"
            translate>REGISTER.VIEW_ADD_MACHINE</button>
        </div>

      </div>
    </div>

		<div class="col-12 col-md-12 customDivContainer" ng-hide="registerCtrl.hasTaxExemptionCert === false">
			  <div class="customDivStyling">

				    <div class="input-group mb-0">
						<div>
							  <label class="d-inline">{{'REGISTER.TAX_EXEMPTION_REQUIRED' | translate}} <span class="text-danger font-bold">*</span></label>
							  <span>{{'REGISTER.TAX_EXEMPTION' | translate}}</span>
						</div>

						<div class="d-inline mt-3 p-0 col-12">
							  <div class="d-inline-flex">
								    <label class="mr-2">{{'REGISTER.TAX_EXEMPTION_YES' | translate}}</label>
								    <input type="checkbox" class="checkbox mb-0" ng-true-value="true" ng-false-value="false" ng-model="registerCtrl.hasTaxExemptionCert"
									     ng-click="registerCtrl.onTaxExemptChanged(true)" class="mb-0 mr-5 checkbox">
							  </div>

							  <div class="d-inline-flex">
								    <label class="mr-2">{{'REGISTER.TAX_EXEMPTION_NO' | translate}}</label>
								    <input type="checkbox" class="checkbox mb-0" ng-true-value="false" ng-false-value="true" ng-model="registerCtrl.hasTaxExemptionCert"
									     ng-click="registerCtrl.onTaxExemptChanged(false)" class="mb-0 mr-5 checkbox">
							  </div>

						</div>
				    </div>

						</div>
				    </div>

			  </div>

	    <div class="d-flex justify-content-center flex-wrap">

			<button class="btn primary col-12 col-md-3 mb-3" ng-hide="registerCtrl.submitting">
				  {{"REGISTER.SIGN_UP" | translate}}</button>
			<button class="btn primary col-12 col-md-3 mb-3" ng-show="registerCtrl.submitting" type="button">
				  {{"REGISTER.SUBMITTING" | translate}}
				  <span class="spinner-border text-light" role="status" aria-hidden="true"></span>
			</button>
			<div class="col-12 text-center">
				  {{'REGISTER.ALREADY_HAVE_ACCOUNT' | translate}}
				  <a href="" ng-click="registerCtrl.goToLogin()">{{'LOGIN.SIGN_IN' | translate}}</a>
			</div>

	    </div>

  </form>

	  <hr class="underline_order border-bottom-0">

	  <div class="d-flex justify-content-center">

		    <h5 class="text-center mr-3 font-weight-normal grey-text" translate>LOGIN.TROUBLE</h5>
		    <h5 class="text-center grey-text"><a href="mailto:{{appCtrl.supportEmail}}">{{'LOGIN.CONTACT' | translate}} {{appCtrl.supportEmail}}</a></h5>

	  </div>

</div>

</div>