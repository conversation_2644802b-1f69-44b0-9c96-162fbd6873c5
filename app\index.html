<!DOCTYPE html>

<html lang="en" ng-app="cadshareApp" ng-controller="AppController as appCtrl" ng-strict-di>
<head>
    <script src="https://cdn.trackjs.com/agent/v3/latest/t.js" crossorigin></script>
    <script>
        if (location.host.indexOf("localhost") !== 0) {
            window.TrackJS && TrackJS.install({
                token: "19dce81fd1b64e2d822f54b0091abe5a",
                application: window.location.host
            });
        }
    </script>

    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="data:,">
    <!--bootstrap-->
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css"
          integrity="sha384-ggOyR0iXCbMQv3Xipma34MD+dH/1fQ784/j6cY/iJTQUOhcWr7x9JvoRxT2MZw1T" crossorigin="anonymous">
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:400,600,700" rel="stylesheet">
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.3.1/css/all.css"
          integrity="sha384-mzrmE5qonljUremFsqc01SB46JvROS7bZs3IO2EmfFsd15uHvIt+Y8vEf7N7fWAU" crossorigin="anonymous">


    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.3.1/css/all.css"
          integrity="sha384-mzrmE5qonljUremFsqc01SB46JvROS7bZs3IO2EmfFsd15uHvIt+Y8vEf7N7fWAU" crossorigin="anonymous">
    <title>{{appCtrl.title}}</title>
    <!-- inject:css -->
    <!-- endinject -->

    <script src="https://developer.api.autodesk.com/modelderivative/v2/viewers/viewer3D.min.js?v=v7.91"></script>
<!--    TODO look into why this is added-->
    <script src="https://developer.api.autodesk.com/modelderivative/v2/viewers/7.91/legacy/ViewingApplication.js"></script>
    <link rel="stylesheet" href="https://developer.api.autodesk.com/modelderivative/v2/viewers/style.min.css?v=v7.91"
          type="text/css">
    <link href="https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">

    <!-- Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.6.0.slim.min.js"
            integrity="sha384-Qg00WFl9r0Xr6rUqNLv1ffTSSKEFFCDCKVyHZ+sVt8KuvG99nWw5RNvbhuKgif9z"
            crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.7/umd/popper.min.js"
            integrity="sha384-UO2eT0CpHqdSJQ6hJty5KVphtPhzWj9WO1clHTMGa3JDZwrnQq4sF86dIHNDz0W1"
            crossorigin="anonymous"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js"
            integrity="sha384-JjSmVgyd0p3pXB1rRibZUAYoIIy6OrQ6VrjIEaFf/nJGzIxFDsf4x0xIM+B07jRM"
            crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.6.347/pdf.min.js"></script>
    <!-- Chart.JS-->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@2.8.0/dist/Chart.min.js"></script>

    <script src="//cdnjs.cloudflare.com/ajax/libs/d3/3.5.3/d3.min.js"></script>
    <script src="//cdnjs.cloudflare.com/ajax/libs/topojson/1.6.9/topojson.min.js"></script>
    <script src="//cdnjs.cloudflare.com/ajax/libs/datamaps/0.5.8/datamaps.all.js"></script>
    <!-- Websockets-->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/2.3.0/socket.io.js"></script>
    <link rel="shortcut icon" ng-href={{appCtrl.favicon}}>
    <!-- inject:js -->
    <!-- endinject -->
</head>
<body>

<div>
    <hpp:body>
</div>



<cadshare-header></cadshare-header>
<header-banner></header-banner>
<ui-view></ui-view>

<!--<link rel="stylesheet" ng-href="app/css/{{appCtrl.subdomain}}.css"/>-->

<!--<script src="https://developer.api.autodesk.com/modelderivative/v2/viewers/three.min.js?v=v6.1.0"></script>-->


<!--<div class="powered-label"><a href="https://www.cadshare.com/" onClick="return confirm('Do you want to leave')"
                              target="_blank">

    <p>Powered by</p><img alt="CadShareLogo" ng-src="./images/CADshare-logo-dark.svg">
</a>
</div>-->

</body>
</html>