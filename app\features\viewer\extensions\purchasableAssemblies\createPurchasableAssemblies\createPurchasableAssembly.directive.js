(function () {
    'use strict';

    angular
        .module('app.viewer')
        .directive('createPurchasableAssembly', createPurchasableAssembly);

    function createPurchasableAssembly() {
        var directive = {
            restrict: 'E',
            templateUrl: 'features/viewer/extensions/purchasableAssemblies/createPurchasableAssemblies/createPurchasableAssembly.html',
            controller: 'CreatePurchasableAssemblyController',
            controllerAs: 'createPurchasableAssemblyCtrl',
            bindToController: true
        };
        return directive;
    }

})();