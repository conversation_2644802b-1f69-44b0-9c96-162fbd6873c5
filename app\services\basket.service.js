(function () {
  "use strict";

  angular.module("app.services").factory("basketService", basketService);

  basketService.$inject = ["userService", "$rootScope", "$stateParams"];

  function basketService(userService, $rootScope, $stateParams) {
    return {
      addPart: addPart,
      addKit: addKit,
      updatePart: updatePart,
      removePart: removePart,
      updateKit: updateKit,
      removeKit: removeKit,
      getBasket: getBasket,
      emptyBasket: emptyBasket,
      getManualPartsBasket: getManualPartsBasket,
      updateManualPartsBasketStorage: updateManualPartsBasketStorage,
      getManualPartsCount: getManualPartsCount,
      addManualPartToBasket: addManualPartToBasket,
      updateBasketComment: updateBasketComment,
      getBasketComment: getBasketComment,
    };

    function addKit(kit) {
      if (isPreviewMode()) {
        alert("Cannot add kits to basket in Preview Mode.");
      } else {
        var basket = getBasketFromStorage();
        var existingKitIndex = _.findIndex(basket, { kitId: kit.kitId });
    
        if (existingKitIndex > -1) {
          var updatedKitArray = basket.splice(existingKitIndex, 1);
          updateBasketStorage(basket);
          var updatedKit = updatedKitArray[0];
          updatedKit.quantity += kit.quantity;
          basket.unshift(updatedKit);
        } else {
          basket.unshift(kit);
        }
    
        console.log("updated basket: ", basket);
        updateBasketStorage(basket);
      }
    }

    function addPart(item) {
    if (isPreviewMode()) {
        alert("Can not add parts to basket in Preview Mode.");
    } else {
        var basket = getBasketFromStorage($stateParams);

        // Determine which part number and master part ID to use based on supersession
        var partNumberToUse = item.inSupersession ? item.maxSupersessionPartNumber : item.partNumber;
        var masterPartIdToUse = item.inSupersession ? item.maxSupersessionPartId : item.masterPartId;

        var alreadyInBasketIndexPartNumber = partNumberToUse === null ? -1 : _.findIndex(basket, { partNumber: partNumberToUse });
        var alreadyInBasketIndexMasterPart = masterPartIdToUse === null ? -1 : _.findIndex(basket, { masterPartId: masterPartIdToUse });

        if (alreadyInBasketIndexMasterPart > -1) {
            var updatedItemArray = basket.splice(alreadyInBasketIndexMasterPart, 1);
            updateBasketStorage(basket);
            var updatedItem = updatedItemArray[0];
            updatedItem.quantity = updatedItem.quantity + item.quantity;
            basket.unshift(updatedItem);
        } else if (alreadyInBasketIndexPartNumber > -1) {
            var updatedItemArray = basket.splice(alreadyInBasketIndexPartNumber, 1);
            updateBasketStorage(basket);
            var updatedItem = updatedItemArray[0];
            updatedItem.quantity = updatedItem.quantity + item.quantity;
            basket.unshift(updatedItem);
        } else {
            basket.unshift(item);
        }
        console.log("updated basket: ", basket);
        updateBasketStorage(basket);
    }
}

    function isPreviewMode() {
      var manufacturerSubEntityId = userService.getManufacturerSubEntityId();
      return !manufacturerSubEntityId && !$stateParams.onBehalfOf;
    }

    function updatePart(item) {
      item.quantity = isNaN(item.quantity) ? 1 : item.quantity;
      var basket = getBasketFromStorage($stateParams);
      var alreadyInBasketIndexPartNumber =
        item.partNumber === null
          ? -1
          : _.findIndex(basket, { partNumber: item.partNumber });
      var alreadyInBasketIndexMasterPart =
        item.masterPartId === null
          ? -1
          : _.findIndex(basket, { masterPartId: item.masterPartId });
      if (alreadyInBasketIndexMasterPart > -1) {
        basket[alreadyInBasketIndexMasterPart] = item;
      } else if (alreadyInBasketIndexPartNumber > -1) {
        basket[alreadyInBasketIndexPartNumber] = item;
      }

      updateBasketStorage(basket);
    }

    function updateKit(kit) {
      kit.quantity = isNaN(kit.quantity) ? 1 : kit.quantity;
      var basket = getBasketFromStorage($stateParams);
      var alreadyInBasketIndexKitId =
        kit.kitId === null ? -1 : _.findIndex(basket, { kitId: kit.kitId });
      if (alreadyInBasketIndexKitId > -1) {
        basket[alreadyInBasketIndexKitId] = kit;
      }
      updateBasketStorage(basket);
    }

    function getBasketFromStorage($stateParams) {
      var basket = [];

            if ($stateParams && $stateParams.orderDetails && $stateParams.orderDetails.orderItems) {
                var basket = $stateParams.orderDetails.orderItems;
                updateBasketStorage(basket);
                return basket;
            }

      var storageBaskets = JSON.parse(localStorage.getItem("baskets"));
      var usersBasketIndex = _.findIndex(storageBaskets, {
        userId: getBasketUserId(),
      });
      if (usersBasketIndex > -1) {
        basket = storageBaskets[usersBasketIndex].basket;
      }

      return basket;
    }

    function updateBasketStorage(currentBasket) {
      var storageBaskets = JSON.parse(localStorage.getItem("baskets")) || [];
      var usersBasketIndex = _.findIndex(storageBaskets, {
        userId: getBasketUserId(),
      });

      if (usersBasketIndex > -1) {
        storageBaskets[usersBasketIndex].basket = currentBasket;
      } else {
        storageBaskets.push({
          userId: getBasketUserId(),
          basket: currentBasket,
        });
      }

      localStorage.setItem("baskets", JSON.stringify(storageBaskets));

      $rootScope.$broadcast("Basket-Updated");
    }

    function removePart(item) {
      var basket = getBasketFromStorage($stateParams);
      var alreadyInBasketIndexPartNumber =
        item.partNumber === null
          ? -1
          : _.findIndex(basket, { partNumber: item.partNumber });
      var alreadyInBasketIndexMasterPart =
        item.masterPartId === null
          ? -1
          : _.findIndex(basket, { masterPartId: item.masterPartId });
      if (alreadyInBasketIndexMasterPart > -1) {
        basket.splice(alreadyInBasketIndexMasterPart, 1);
      } else if (alreadyInBasketIndexPartNumber > -1) {
        basket.splice(alreadyInBasketIndexPartNumber, 1);
      }
      updateBasketStorage(basket);
    }

    function removeKit(kit) {
      var basket = getBasketFromStorage($stateParams);
      var kitIndex = _.findIndex(basket, { kitId: kit.kitId});
      if (kitIndex > -1) {
        basket.splice(kitIndex, 1); 
        updateBasketStorage(basket);
      }
    }

    function getBasket($stateParams) {
      return getBasketFromStorage($stateParams);
    }

    function emptyBasket() {
      var storageBaskets = JSON.parse(localStorage.getItem("baskets"));
      var usersBasketIndex = _.findIndex(storageBaskets, {
        userId: getBasketUserId(),
      });

      if (usersBasketIndex > -1) {
        storageBaskets.splice(usersBasketIndex, 1);
      }

      localStorage.setItem("baskets", JSON.stringify(storageBaskets));
      clearManualPartsBasket();
      clearBasketComment();
    }

    function getBasketUserId() {
      if ($stateParams.onBehalfOf && $stateParams.onBehalfOf !== "null") {
        return JSON.parse(decodeURIComponent(atob($stateParams.onBehalfOf)))
          .userId;
      } else {
        return userService.getUserId();
      }
    }

        function getManualPartsBasket($stateParams) {
            var additionalPartsBasket = [];
                var storageBaskets = JSON.parse(localStorage.getItem("manualPartsBasket"));
                var usersBasketIndex = _.findIndex(storageBaskets, {userId: getBasketUserId()});

            if ($stateParams && $stateParams.orderDetails && $stateParams.orderDetails.additionalParts) {
                var additionalPartsBasket = $stateParams.orderDetails.additionalParts;
                updateManualPartsBasketStorage(additionalPartsBasket);
                return additionalPartsBasket;
            } else if (usersBasketIndex > -1) {
                additionalPartsBasket = storageBaskets[usersBasketIndex].manualPartsBasket;
            }

            return additionalPartsBasket;
        }

    function updateManualPartsBasketStorage(manualParts) {
      var storageBaskets =
        JSON.parse(localStorage.getItem("manualPartsBasket")) || [];
      var usersBasketIndex = _.findIndex(storageBaskets, {
        userId: getBasketUserId(),
      });

      if (usersBasketIndex > -1) {
        storageBaskets[usersBasketIndex].manualPartsBasket = manualParts;
      } else {
        storageBaskets.push({
          userId: getBasketUserId(),
          manualPartsBasket: manualParts,
        });
      }

      localStorage.setItem("manualPartsBasket", JSON.stringify(storageBaskets));
      $rootScope.$broadcast("Basket-Updated");
    }

    function addManualPartToBasket(manualPart) {
      var manualPartsBasket = getManualPartsBasket();

      if (
        _.findWhere(manualPartsBasket, {
          partNumber: manualPart.partNumber,
        }) !== undefined
      ) {
        var oldPart = _.findWhere(manualPartsBasket, {
          partNumber: manualPart.partNumber,
        });
        var oldPartIndex = manualPartsBasket.indexOf(oldPart);
        manualPartsBasket[oldPartIndex].quantity =
          manualPartsBasket[oldPartIndex].quantity + manualPart.quantity;
      } else {
        manualPartsBasket.push(manualPart);
      }
      updateManualPartsBasketStorage(manualPartsBasket);
    }

    function getManualPartsCount() {
      var manualParts = getManualPartsBasket();
      var manualPartsCount = 0;

      if (manualParts) {
        for (var i = 0; i < manualParts.length; i++) {
          manualPartsCount = manualPartsCount + manualParts[i].quantity;
        }
      }
      return manualPartsCount;
    }

    function clearManualPartsBasket() {
      var storageBaskets = JSON.parse(
        localStorage.getItem("manualPartsBasket")
      );
      var usersBasketIndex = _.findIndex(storageBaskets, {
        userId: getBasketUserId(),
      });

      if (usersBasketIndex > -1) {
        storageBaskets.splice(usersBasketIndex, 1);
      }
      localStorage.setItem("manualPartsBasket", JSON.stringify(storageBaskets));
    }

    function getBasketComment() {
      var comment = "";
      var storageBaskets = JSON.parse(localStorage.getItem("basketComment"));
      var usersBasketIndex = _.findIndex(storageBaskets, {
        userId: getBasketUserId(),
      });
      if (usersBasketIndex > -1) {
        comment = storageBaskets[usersBasketIndex].comment;
      }

      return comment;
    }

    function updateBasketComment(comment) {
      var storageBaskets =
        JSON.parse(localStorage.getItem("basketComment")) || [];
      var usersBasketIndex = _.findIndex(storageBaskets, {
        userId: getBasketUserId(),
      });

      if (usersBasketIndex > -1) {
        storageBaskets[usersBasketIndex].comment = comment;
      } else {
        storageBaskets.push({ userId: getBasketUserId(), comment: comment });
      }

      localStorage.setItem("basketComment", JSON.stringify(storageBaskets));
      //$rootScope.$broadcast("Basket-Updated");
    }

    function clearBasketComment() {
      var storageBaskets = JSON.parse(localStorage.getItem("basketComment"));
      var usersBasketIndex = _.findIndex(storageBaskets, {
        userId: getBasketUserId(),
      });

      if (usersBasketIndex > -1) {
        storageBaskets.splice(usersBasketIndex, 1);
      }
      localStorage.setItem("basketComment", JSON.stringify(storageBaskets));
    }
  }
})();
