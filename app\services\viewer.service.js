(function () {
    "use strict";

    angular.module("app.services").factory("viewerService", viewerService);

    viewerService.$inject = ["$http", "apiConstants", "$q", "$filter", "tokenService", "userService"];

    function viewerService($http, apiConstants, $q, $filter, tokenService, userService) {
        return {
            getAutodeskToken: getAutodeskToken,
            uploadThumbnailToAWS: uploadThumbnailToAWS,
            save2dState: save2dState,
            getStates: getStates,
            getViewableByModelId: getViewableByModelId,
            createViewable: createViewable,
            modelSetupComplete: modelSetupComplete,
            createStateDetail: createStateDetail,
            updateStateDetail: updateStateDetail,
            getStateDetails: getStateDetails,
            deleteStateDetail: deleteStateDetailByStateId,
            getPDFStates: getPDFStates,
            guid: guid,
            createPart: createPart,
            getPart: getPart,
            getPartViewerDetails: getPartViewerDetails,
            getNonModeledParts: getNonModeledParts,
            getLinkableViewables: getLinkableViewables,
            getLinkedViewable: getLinkedViewable,
            getPartLink: getPartLink,
            deletePartModelLink: deletePartModelLink,
            savePartModelLink: savePartModelLink,
            editPartModelLink: editPartModelLink,
            addPartToNonModeledParts: addPartToNonModeledParts,
            removeNonModeledPart: removeNonModeledPart,
            updateParts: updateParts,
            getModelTree: getModelTree,

            getSoftCopyViewableBySoftCopyId: getSoftCopyViewableBySoftCopyId,
            createSoftCopyStateDetail: createSoftCopyStateDetail,
            updateSoftCopyStateDetail: updateSoftCopyStateDetail,
            getViewableStateDetails: getViewableStateDetails,
            deleteSoftCopyStateDetail: deleteSoftCopyStateDetailByStateId,

            getSupersedeParts: getSupersedeParts,
            getSupersessionHistory : getSupersessionHistory,

            getWorkInstructionsViewableByViewableId: getWorkInstructionsViewableByViewableId,

            createAndPublishPublication: createAndPublishPublication,
            getSupersessionHistoryForManufacturer: getSupersessionHistoryForManufacturer
        };

        function getAutodeskToken() {
            return $http.get(apiConstants.url + "/token/autodesk").then(getAutodeskTokenSuccess, getAutodeskTokenFailed);
        }

        function save2dState(pdfPages, modelId, pagesFromDb) {
            if (pagesFromDb) {
                return $http.put(apiConstants.url + "/pdf/" + modelId, { pdfPages: pdfPages });
            } else {
                return $http.post(apiConstants.url + "/pdf/" + modelId, { pdfPages: pdfPages });
            }
        }

        function getModelTree(modelId) {
            return $http.get(apiConstants.url + "/model/" + modelId + "/partTree");
        }

        function getAutodeskTokenSuccess(response) {
            tokenService.setAutodeskToken(response.data);
            return {
                env: "AutodeskProduction",
                accessToken: response.data.accessToken,
            };
        }

        function getAutodeskTokenFailed(error) {
            console.log(error);
        }

        function uploadThumbnailToAWS(imgURL) {
            var promise = $q.defer();
            var xhr = new XMLHttpRequest();
            xhr.open("GET", imgURL, true);
            xhr.responseType = "blob";
            xhr.onload = function (e) {
                if (this.status === 200) {
                    var myBlob = this.response;
                    var fileName = guid() + ".png";
                    myBlob.lastModifiedDate = new Date();
                    myBlob.name = fileName;

                    return $http
                        .post(apiConstants.url + "/aws/s3/getpresignedurl", {
                            objectKey: myBlob.name,
                            fileSizeInBytes: myBlob.size,
                        })
                        .then(function getPresignedUrlSuccess(resp) {
                            //console.log("File is: " + myBlob);
                            let url = new URL(resp.data.URL);
                            url.searchParams.delete("Signature");
                            return $http.put(url.toString(), myBlob, { headers: { "Content-Type": myBlob.type } }).then(
                                function () {
                                    var imageURL = resp.data.URL.split("?");
                                    return promise.resolve(imageURL[0]);
                                },
                                function (error) {
                                    uploadFail(error);
                                }
                            );
                        }, uploadFail);
                }
            };
            xhr.send();
            return promise.promise;
        }

        function guid() {
            var d = new Date().getTime();
            return "xxxx-xxxx-xxxx-xxxx-xxxx-xxxx-xxxx-xxxx".replace(/[xy]/g, function (c) {
                var r = (d + Math.random() * 16) % 16 | 0;
                d = Math.floor(d / 16);
                return (c === "x" ? r : (r & 0x7) | 0x8).toString(16);
            });
        }

        function uploadFail(error) {
            alert("An Error Occurred Uploading Your Thumbnail to AWS");
            return $q.reject(error);
        }

        function getStates(modelId) {
            return $http
                .get(apiConstants.url + "/model/" + modelId + "/snapshotStates") //states
                .then(
                    function (resp) {
                        return resp.data;
                    },
                    function (error) {
                        console.error(error);
                    }
                );
        }

        function createViewable(modelState) {
            return $http.post(apiConstants.url + "/viewable", modelState);
        }

        function modelSetupComplete(setupComplete) {
            return $http.put(apiConstants.url + "/model/setupComplete", setupComplete);
        }

        function createStateDetail(stateDetail, modelId) {
            return $http.post(apiConstants.url + "/model/" + modelId + "/statedetails", stateDetail);
        }

        function updateStateDetail(stateDetail, modelId) {
            return $http.put(apiConstants.url + "/model/" + modelId + "/statedetails/" + stateDetail.id, stateDetail);
        }

        function getStateDetails(modelId, topLevelStateDetailId, levels) {
            return $http
                .get(apiConstants.url + "/model/" + modelId + "/statedetails", {
                    params: {
                        topLevelStateDetailId: topLevelStateDetailId,
                        levels: levels,
                    },
                })
                .then(
                    function (resp) {
                        return resp.data;
                    },
                    function (error) {
                        console.error(error);
                    }
                );
        }

        function deleteStateDetailByStateId(stateId, modelId) {
            return $http.delete(apiConstants.url + "/model/" + modelId + "/statedetails/", {
                params: {
                    stateId: stateId,
                    deleteChildren: true,
                },
            });
        }

        function getViewableByModelId(modelId) {
            return $http.get(apiConstants.url + "/viewable/model/" + modelId).then(
                function (resp) {
                    return resp.data;
                },
                function (error) {
                    console.error(error);
                }
            );
        }

        function getPDFStates(modelId) {
            return $http
                .get(apiConstants.url + "/pdf/" + modelId) //states
                .then(
                    function (resp) {
                        return resp.data;
                    },
                    function (error) {
                        console.error(error);
                    }
                );
        }

        function createPart(newPart) {
            var createPartData = {
                partNumber: newPart.name,
                fileName: newPart.name,
                partDescription: newPart.description,
                modelId: parseInt(newPart.modelId),
                partWeight: newPart.partWeight ? $filter("number")(newPart.partWeight, 2) : "N/A",
                partMassUnit: newPart.massUnit,
            };
            return $http.post(apiConstants.url + "/part", createPartData);
        }

        function getPart(modelId, objectId) {
            return $http.get(apiConstants.url + "/model/" + modelId + "/part/" + objectId);
        }

        function getPartViewerDetails(modelId, id, manualId, onBehalfOfId) {
            var objectId = id[0];
            var filterKits =
                userService.getCustomerSearchManualsOnly() && userService.isManufacturerSubEntity()
                    ? "&filterKitsManualId=" + manualId
                    : "";
            var queryParams = onBehalfOfId ? "&userId=" + onBehalfOfId : "";

            var userType = window.location.href.includes("customerViewer") || !userService.isManufacturer()
                ? 'purchaser'
                : 'manufacturer';

            var baseUrl = apiConstants.url + "/model/" + modelId + "/part/" + objectId + "/viewerDetails?userType=" + userType;

            return $http.get(baseUrl + filterKits + queryParams);
        }

        function updateParts(partsList) {
            return $http.put(apiConstants.url + "/part", partsList);
        }

        function addPartToNonModeledParts(partList, associatedDbId, modelId) {
            var nonModelPartData = {
                associatedDBId: associatedDbId,
                modelId: modelId,
                partIds: partList,
            };
            return $http.put(apiConstants.url + "/nonmodeledpart", nonModelPartData);
        }

        function getNonModeledParts(associatedDbId, modelId) {
            return $http.get(apiConstants.url + "/nonmodeledpart/parts/" + associatedDbId + "/" + modelId);
        }

        function getLinkableViewables(modelId) {
            return $http.get(apiConstants.url + "/model/" + modelId + "/linkableViewables");
        }

        function getLinkedViewable(modelId, objectId) {
            return $http.get(apiConstants.url + "/model/" + modelId + "/part/" + objectId + "/linkedViewable");
        }

        function getPartLink(partLinkId) {
            return $http.get(apiConstants.url + "/model/partLink/" + partLinkId);
        }

        function deletePartModelLink(modelId, objectId) {
            return $http.delete(apiConstants.url + "/model/" + modelId + "/part/" + objectId + "/link", null);
        }

        function savePartModelLink(modelId, objectId, viewableId) {
            return $http.put(apiConstants.url + "/model/" + modelId + "/part/" + objectId + "/link", viewableId);
        }

        function editPartModelLink(objectId, viewableId, linkedPartId) {
            var data = { modelId: viewableId, objectId: objectId };
            return $http.put(apiConstants.url + "/model/partLink/" + linkedPartId, data);
        }

        function removeNonModeledPart(partId, associatedDbId, modelId) {
            var nonModelPartData = {
                associatedDbId: associatedDbId,
                modelId: modelId,
                partId: partId,
            };
            return $http.post(apiConstants.url + "/nonmodeledpart/remove", nonModelPartData);
        }

        function getSoftCopyViewableBySoftCopyId(viewableId) {
            return $http
                .get(apiConstants.url + "/softcopy/viewer/viewable/" + viewableId) //states
                .then(
                    function (resp) {
                        return resp.data;
                    },
                    function (error) {
                        console.error(error);
                    }
                );
        }

        function createSoftCopyStateDetail(stateDetail, viewableId) {
            return $http.post(apiConstants.url + "/statedetails/viewable/" + viewableId, stateDetail);
        }

        function updateSoftCopyStateDetail(stateDetail, viewableId) {
            return $http.put(apiConstants.url + "/statedetails/viewable/" + viewableId + "/statedetails/" + stateDetail.id, stateDetail);
        }

        function getViewableStateDetails(viewableId, topLevelStateDetailId, levels) {
            return $http
                .get(apiConstants.url + "/statedetails/viewable/" + viewableId, {
                    params: {
                        topLevelStateDetailId: topLevelStateDetailId,
                        levels: levels,
                    },
                })
                .then(
                    function (resp) {
                        return resp.data;
                    },
                    function (error) {
                        console.error(error);
                    }
                );
        }

        function deleteSoftCopyStateDetailByStateId(stateId, viewableId) {
            return $http.delete(apiConstants.url + "/statedetails/viewable/" + viewableId, {
                params: {
                    stateId: stateId,
                    deleteChildren: true,
                },
            });
        }

        function getSupersedeParts(viewableId) {
            return $http.get(apiConstants.url + "/model/" + viewableId + "/masterPart/superseded");
        }

        function getSupersessionHistory(purchaserId, masterPartNumber) {
            return $http.get(apiConstants.url + "/purchasers/" + purchaserId + "/master-parts/" + masterPartNumber + "/supersession-history");
        }

        function getSupersessionHistoryForManufacturer(manufacturerId, masterPartNumber) {
            return $http.get(apiConstants.url + "/manufacturers/" + manufacturerId + "/master-parts/" + masterPartNumber + "/supersession-history");
        }

        function getWorkInstructionsViewableByViewableId(viewableId) {
            return $http.get(apiConstants.url + "/instruction/viewable/" + viewableId).then(
                function (resp) {
                    return resp.data;
                },
                function (error) {
                    console.error(error);
                }
            );
        }

        function createAndPublishPublication(manufacturerId, viewableId) {
            var payload = { viewableId: viewableId };
            return $http.post(apiConstants.url + '/manufacturers/' + manufacturerId + '/publications/create-and-publish/', payload);
        }
    }
})();
