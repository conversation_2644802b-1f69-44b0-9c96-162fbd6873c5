<div class="" id="order-page-identifier">
    <section class="flex justify-content-between">
        <div class="">
            <h1 class="mb-0">
                {{viewOrderCtrl.STAGE}} {{"ORDER.DETAILS" | translate}}: #{{viewOrderCtrl.data.customOrderDisplay}}
                <a
                    href=""
                    ng-show="viewOrderCtrl.isManufacturer && viewOrderCtrl.showSplitOrderBtn"
                    ng-click="viewOrderCtrl.editCustomOrderNumber()"
                    ><i class="fa fa-fw fa-pencil"></i
                ></a>
                <span class="badge-pill badge-lg primary">{{viewOrderCtrl.orderStatusPill}}</span>
            </h1>
        </div>

        <div class="d-flex flex-wrap mt-4 cadGap" ng-hide="viewOrderCtrl.enquiriesOnly">

            <button ng-if="viewOrderCtrl.isCustomer" class="btn primary-outline" ng-click="viewOrderCtrl.duplicateOrder()" translate>
                ORDER.DUPLICATE_ORDER
            </button>

            <button
                ng-if="viewOrderCtrl.data.commentThread === undefined"
                ng-hide="viewOrderCtrl.data.orderStatus == 'CLOSED' || viewOrderCtrl.data.orderStatus == 'CANCELLED'
                        || viewOrderCtrl.data.orderStatus == 'SHIPPED' || viewOrderCtrl.data.orderStatus == 'EXTERNAL' || viewOrderCtrl.enquiriesOnly"
                class="btn primary-outline"
                ng-click="viewOrderCtrl.openComment(viewOrderCtrl.data.commentThread.id, null)"
                translate
            >
                ORDER.ADD_COMMENT
                <i class="fa fa-comments ng-scope" ng-if="viewOrderCtrl.data.unreadComment"></i>
            </button>

            <button
                ng-if="viewOrderCtrl.data.commentThread !== undefined"
                ng-hide="viewOrderCtrl.data.unreadComment"
                class="btn primary-outline"
                ng-click="viewOrderCtrl.openComment(viewOrderCtrl.data.commentThread.id, null)"
                translate
            >
                ORDER.VIEW_COMMENTS
                <i class="fa fa-comments ng-scope" ng-if="viewOrderCtrl.data.unreadComment"></i>
            </button>

            <button
                ng-class="viewOrderCtrl.data.unreadComment ? 'unreadcomment' : '' "
                ng-if="viewOrderCtrl.data.commentThread !== undefined"
                ng-hide="!viewOrderCtrl.data.unreadComment"
                class="btn primary-outline"
                ng-click="viewOrderCtrl.openComment(viewOrderCtrl.data.commentThread.id, null)"
            >
                <span>
                    {{"ORDERS.NEW_COMMENTS" | translate}}
                    <i class="ng-scope blob_order fas fa-bell" ng-if="viewOrderCtrl.data.unreadComment"></i
                ></span>
            </button>

            <button
                class="btn primary-outline"
                ng-hide="!viewOrderCtrl.showPartSearch || viewOrderCtrl.hidePartSearch"
                ng-click="viewOrderCtrl.openPartSearchModal()"
            >
                <i class="fa fa-search"></i>
                {{"ORDER.PART_SEARCH" | translate}}
            </button>

            <button class="btn primary-outline" ng-click="viewOrderCtrl.archiveOrder()" ng-if="viewOrderCtrl.isArchiveActive">
                <i class="fa fa-archive"></i>
                {{"ORDER.ARCHIVE_ORDER" | translate}}
            </button>

            <button
                class="btn btn-outline-danger"
                ng-if="viewOrderCtrl.isManufacturer && viewOrderCtrl.archived != true"
                ng-click="viewOrderCtrl.cancelOrder(viewOrderCtrl.data.orderId, viewOrderCtrl.data.customOrderDisplay)"
            >
                {{"GENERAL.CANCEL" | translate}} {{viewOrderCtrl.STAGE}}
            </button>

            <button
                class="btn btn-primary"
                ng-show="viewOrderCtrl.showNextStep && viewOrderCtrl.isNextStepDisabled"
                ng-disabled="viewOrderCtrl.isNextStepDisabled"
            >
                <span class="spinner-border text-light" role="status" aria-hidden="true"></span>
                {{"ORDER.GENERATING" | translate}}
            </button>

            <button
                class="btn btn-outline-dark"
                ng-click="viewOrderCtrl.splitOrder(viewOrderCtrl.data.orderItems, viewOrderCtrl.data.additionalParts, viewOrderCtrl.data.orderId, viewOrderCtrl.data.customOrderDisplay)"
                ng-show="viewOrderCtrl.showSplitOrderBtn"
                ng-disabled="(viewOrderCtrl.data.orderItems.length === 1 && viewOrderCtrl.hasQuantityMissing) && (viewOrderCtrl.data.additionalParts.length !== 1 && !viewOrderCtrl.hasManuallyAddedQuantityMissing) || (viewOrderCtrl.data.orderItems.length !== 1 && !viewOrderCtrl.hasQuantityMissing) && (viewOrderCtrl.data.additionalParts.length === 1 && viewOrderCtrl.hasManuallyAddedQuantityMissing)"
                translate
            >
                ORDER.SPLIT_ENQUIRY
            </button>

            <button
                class="btn secondary"
                ng-click="viewOrderCtrl.altStep()"
                ng-show="viewOrderCtrl.showAltStepBtn && viewOrderCtrl.isOrderEdited"
            >
                {{viewOrderCtrl.altStepText}}
            </button>

            <button
                class="btn btn-outline-dark"
                ng-show="viewOrderCtrl.data.invoiceUrl.length === 1"
                ng-click="viewOrderCtrl.viewInvoice()"
                translate
            >
                ORDER.VIEW_INVOICE
            </button>

            <div class="customDropdown" ng-show="viewOrderCtrl.data.invoiceUrl.length > 1">
                <button class="btn btn-outline-dark dropdown-toggle" data-toggle="dropdown" data-hover="dropdown">
                    {{'ORDER.VIEW_INVOICES' | translate}} <span class="caret"></span>
                </button>
                <ul class="dropdown-menu">
                    <li title="{{'ORDER.DOWNLOAD' | translate}}" ng-repeat="invoice in viewOrderCtrl.data.invoiceUrl">
                        <a href="" class="dark-secondary" ng-click="viewOrderCtrl.viewInvoice($index)">
                            {{'ORDER.DOWNLOAD' | translate}} {{'ORDER.INVOICE' | translate}} {{$index + 1 }}
                        </a>
                    </li>
                </ul>
            </div>

            <button
                class="btn primary"
                ng-click="viewOrderCtrl.nextStep()"
                ng-show="viewOrderCtrl.showNextStep && !viewOrderCtrl.isNextStepDisabled"
                ng-disabled="viewOrderCtrl.isNextStepDisabled"
            >
                {{viewOrderCtrl.nextStepText}}
            </button>

            <span ng-show="!viewOrderCtrl.showNextStep && !viewOrderCtrl.showAltStepBtn" style="line-height: 40px">&nbsp;</span>
        </div>

        <div class="" ng-show="viewOrderCtrl.showExternallyProcessButton">
            <button class="btn primary" ng-click="viewOrderCtrl.externallyProcessOrder(viewOrderCtrl.data)">
                {{"ORDER.EXTERNAL_ACCEPT" | translate}} {{viewOrderCtrl.STAGE}}
            </button>
        </div>
    </section>

    <section>
        <p ng-if="viewOrderCtrl.showDisclaimer" class="mb-0 mt-4">
            <span class="font-weight-bold">{{"ORDER.PLEASE_NOTE" | translate}}</span> {{"ORDER.DISCLAIMER" | translate}}
        </p>
    </section>

    <hr class="underline_order" />

    <section>
        <div class="">
            <p class="mb-0" ng-if="viewOrderCtrl.parentOrderId">
                <strong
                    >{{"ORDER.ORDER_BEEN_CREATED" | translate}}
                    <a href="" ng-click="viewOrderCtrl.goToSplitOrder(viewOrderCtrl.parentOrderId)"
                        >#{{viewOrderCtrl.parentCustomOrderDisplay}}</a
                    >
                </strong>
            </p>
            <p class="mb-0" ng-if="viewOrderCtrl.splitOrders.length > 0">
                <strong
                    >{{"ORDER.ORDER_BEEN_SPLIT" | translate}}:
                    <a
                        ng-repeat="splitOrder in viewOrderCtrl.splitOrders"
                        href=""
                        ng-click="viewOrderCtrl.goToSplitOrder(splitOrder.orderId)"
                        >#{{splitOrder.displayId}}<span ng-if="!$last">, </span></a
                    >
                </strong>
            </p>
            <p class="mb-0" ng-if="viewOrderCtrl.associatedOrderId">
                <strong
                    >{{"ORDER.NOTIFY_ASSOCIATED_ORDER_1" | translate}}
                    <a href="" ng-click="viewOrderCtrl.goToSplitOrder(viewOrderCtrl.associatedOrderId)"
                        ><strong>#{{viewOrderCtrl.associatedCustomOrderDisplay}}</strong></a
                    >&period; {{"ORDER.NOTIFY_ASSOCIATED_ORDER_2" | translate}}
                </strong>
            </p>

            <p class="mb-0" ng-if="viewOrderCtrl.data.associatedCustomerOrder">
                <strong
                    >{{"ORDER.ORDER_CREATED_FROM_CUST" | translate}}
                    <a
                        href=""
                        ng-click="viewOrderCtrl.goToCustomerOrder(viewOrderCtrl.data.associatedCustomerOrder.associatedDPCustomerOrderId)"
                    >
                        #{{viewOrderCtrl.data.associatedCustomerOrder.associatedDPCustomerCustomDisplayNumber}}
                    </a>
                    {{"ORDER.FOR" | translate}} {{viewOrderCtrl.data.associatedCustomerOrder.customerName}}{{"ORDER.ON" | translate}}
                    {{viewOrderCtrl.data.associatedCustomerOrder.createdDate | date: 'dd MMM yyyy'}}
                </strong>
            </p>
        </div>
    </section>

    <!---->

    <section class="my-3">
        <div class="row">
            <div class="col-12 col-md-12 col-lg-6 customDivContainer">
                <div class="customDivStyling">
                    <h2 class="">
                        <span translate>ORDER.ORDER_DETAILS</span> - <strong>{{viewOrderCtrl.data.manufacturerSubEntityName}}</strong>
                    </h2>

                    <div class="row" ng-hide="viewOrderCtrl.isCustDetailEdit">
                        <div class="col-md-6">
                            <label><strong translate>ORDER.DELIVERY_ADDRESS</strong></label>
                            <p>{{viewOrderCtrl.shippingAddress}}</p>
                        </div>

                        <div class="col-md-6">
                            <label><strong translate>ORDER.DELIVERY_DETAILS</strong></label>
                            <p class="mb-2">
                                <strong><i class="fa fa-truck"></i></strong>&nbsp; {{viewOrderCtrl.data.deliveryName}}
                            </p>
                            <p class="mb-2">
                                <strong><i class="fa fa-phone"></i></strong>&nbsp;&nbsp; {{viewOrderCtrl.data.deliveryNumber}}
                            </p>
                            <div ng-hide="viewOrderCtrl.isManufacturer">
                                <p class="word-break">
                                    <strong><i class="fa fa-envelope"></i></strong>&nbsp;&nbsp; {{viewOrderCtrl.data.emailAddress}}
                                </p>
                            </div>
                        </div>

                        <hr class="col-11 hr-text" />

                        <div class="col-md-6">
                            <label><strong translate>ORDER.BILLING_ADDRESS</strong></label>
                            <p>{{viewOrderCtrl.billingAddress}}</p>
                        </div>

                        <div class="col-md-6">
                            <label><strong translate>ORDER.CONTACT_DETAILS</strong></label>
                            <p class="mb-2">
                                <strong><i class="fa fa-user"></i></strong>&nbsp;&nbsp; {{viewOrderCtrl.data.contactName}}
                            </p>
                            <p>
                                <strong><i class="fa fa-phone"></i></strong>&nbsp;&nbsp; {{viewOrderCtrl.data.contactNumber}}
                            </p>
                        </div>

                        <hr class="col-11 hr-text" />

                        <div class="col-md-6" ng-if="viewOrderCtrl.data.requestedDeliveryDate !== undefined">
                            <label><strong translate>ORDER.REQUESTED_DELIVERY_DATE</strong></label>
                            <p>{{viewOrderCtrl.data.requestedDeliveryDate | date: 'dd MMM yyyy'}}</p>
                        </div>

                        <div
                            class="col-md-6"
                            ng-if="viewOrderCtrl.showEstimatedDelivery && viewOrderCtrl.data.estimatedDeliveryDate !== null"
                        >
                            <label><strong translate>ORDER.ESTIMATED_DELIVERY_DATE</strong></label>

                            <div ng-show="viewOrderCtrl.isEstimatedDeliveryDateEditable">
                                <input class="dark-text" type="text" id="datepicker" ng-model="viewOrderCtrl.data.estimatedDeliveryDate" />
                                <button class="btn success" ng-click="viewOrderCtrl.saveEstimatedDeliveryDate()">
                                    <i class="fa fa-floppy-o"></i> {{"ORDER.SAVE_DELIVERY_DATE" | translate}}
                                </button>
                            </div>

                            <div ng-hide="viewOrderCtrl.isEstimatedDeliveryDateEditable">
                                <div ng-show="viewOrderCtrl.data.estimatedDeliveryDate">
                                    <p>{{viewOrderCtrl.data.estimatedDeliveryDate | date: 'dd MMM yyyy'}}</p>
                                </div>
                                <div ng-hide="viewOrderCtrl.data.estimatedDeliveryDate"><p translate>ORDER.TO_BE_CONFIRMED</p></div>
                                <button
                                    ng-show="viewOrderCtrl.isManufacturer && viewOrderCtrl.archived != true"
                                    class="btn primary"
                                    ng-click="viewOrderCtrl.editEstimatedDeliveryDate()"
                                >
                                    <i class="fa fa-pencil"></i> {{"ORDER.EDIT_DELIVERY_DATE" | translate}}
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="button_row flex justify-content-between">
                        <button
                            ng-if="viewOrderCtrl.showTsAndCs"
                            class="btn primary-outline my-2"
                            ng-click="viewOrderCtrl.viewTermsAndConditions()"
                        >
                            <i class="fa fa-download pr-3"></i>{{'ORDER.TERMS_CONDITIONS' | translate}}
                        </button>

                        <button
                            class="btn primary alignEditEnd"
                            ng-click="viewOrderCtrl.editCustomerDetails()"
                            ng-show="viewOrderCtrl.showEditDetailsButton"
                            ng-hide="viewOrderCtrl.enquiriesOnly"
                            translate
                        >
                            ORDER.EDIT_DETAILS
                        </button>
                    </div>
                </div>
            </div>

            <div class="col-12 col-md-6 col-lg-3 customDivContainer">
                <div class="customDivStyling">
                    <h2 class=""><span translate>ORDER.ORDER_REFERENCES</span></h2>

                    <div ng-if="viewOrderCtrl.showPurchaseOrder && !viewOrderCtrl.showOrderReference">
                        <label>
                            <strong translate>ORDER.PURCHASE_ORDER_NUMBER</strong>
                        </label>
                        <input
                            class="dark-text"
                            ng-model="viewOrderCtrl.data.purchaseOrder"
                            ng-show="viewOrderCtrl.isPurchaseOrderEditable"
                            placeholder="{{'ORDER.ENTER_PO_NUM' | translate}}"
                        />
                        <p class="order-field" ng-hide="viewOrderCtrl.isPurchaseOrderEditable">
                            {{viewOrderCtrl.data.purchaseOrder || "N/A"}}
                        </p>
                    </div>
                    <div ng-if="viewOrderCtrl.showERPRefVisible">
                        <label>
                            <strong translate>ORDER.ERP_REFERENCE</strong>
                        </label>
                        <p class="order-field">{{viewOrderCtrl.data.visSalesOrderNoX || "N/A"}}</p>
                    </div>
                    <div ng-if="viewOrderCtrl.isSupreme">
                        <label>
                            <strong translate>ORDER.COURIER_DETAILS</strong>
                        </label>
                        <p class="order-field">
                            {{ viewOrderCtrl.data.shippingRequirementAsInstruction || ('ORDER.NONE_SUBMITTED' | translate) }}
                        </p>
                    </div>
                    <div ng-if="viewOrderCtrl.showOrderReference">
                        <label>
                            <strong translate>ORDER.PAYMENT_REFERENCE</strong>
                        </label>
                        <p class="order-field">{{viewOrderCtrl.data.orderExtraData.globalPaymentTransactionId || "N/A"}}</p>
                    </div>
                    <div ng-if="viewOrderCtrl.showShippingRefVisible" ng-init="viewOrderCtrl.switchTrackingUrl()">
                        <label>
                            <strong translate>ORDER.SHIPPING_REFERENCE</strong>
                        </label>
                        <p class="order-field">
                            <a target="_blank" href="{{viewOrderCtrl.trackingUrl}}">
                                {{viewOrderCtrl.data.orderExtraData.shipEngineTrackingNumber || "N/A"}}
                            </a>
                            -
                            <span class="text-capitalize"
                                >{{ viewOrderCtrl.formatShipEngineServiceCode(viewOrderCtrl.data.orderExtraData.shipEngineServiceCode) }}
                            </span>
                        </p>
                    </div>
                    <div ng-if="viewOrderCtrl.showRequiredSerialNumber">
                        <div ng-hide="viewOrderCtrl.data.stockOrder">
                            <label><strong translate>ORDER.SERIAL_NUMBER</strong></label>
                            <p class="order-field">{{viewOrderCtrl.data.serialNumber || "N/A"}}</p>
                        </div>
                        <div class="d-flex" ng-show="viewOrderCtrl.data.stockOrder">
                            <label><strong class="pr-2" translate>ORDER.STOCK_ORDER</strong></label>
                            <input
                                type="checkbox"
                                id="stockOrder"
                                class="checkbox"
                                ng-disabled="true"
                                ng-model="viewOrderCtrl.data.stockOrder"
                            />
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-12 col-md-6 col-lg-3 customDivContainer">
                <div class="customDivStyling">
                    <h2 class=""><span>{{viewOrderCtrl.STAGE}} {{'ORDER.TIMELINE' | translate}}</span></h2>
                    <p class="order-field" ng-repeat="step in viewOrderCtrl.data.orderHistory">
                        {{step.date | date : 'dd MMM yyyy HH:mm'}} ({{step.orderStatus }}
                        <span ng-show="step.firstName.length > 0"> {{"ORDER.BY" | translate}} {{step.firstName}} {{step.lastName}})</span>
                    </p>

                    <button
                        class="btn primary-outline"
                        ng-click="viewOrderCtrl.viewPartiallyShipped()"
                        ng-show="viewOrderCtrl.isPartiallyShipped"
                        translate
                    >
                        ORDER.VIEW_PARTIALLY_SHIPPED_INFO
                    </button>
                </div>
            </div>

            <div class="col-12 col-md-6 col-lg-12 customDivContainer" ng-show="viewOrderCtrl.displayNotes">
                <div class="customDivStyling">
                    <h2 class=""><span translate>ORDER.ENQUIRY_NOTES</span></h2>
                    <pre ng-hide="viewOrderCtrl.isNotesEditable">{{viewOrderCtrl.data.notes}}</pre>

                    <div ng-show="viewOrderCtrl.isNotesEditable" class="customDivContainer flex-column">
                        <div class="form-group flex-grow-1 customDivContainer flex-column full-width-comment">
                            <textarea
                                class="form-control flex-grow-1"
                                ng-model="viewOrderCtrl.data.notes"
                                ng-change="viewOrderCtrl.orderEdited()"
                                rows="8"
                                placeholder="{{'ORDER.PLEASE_ENTER_NOTES_HERE' | translate}}"
                            ></textarea>
                        </div>
                    </div>
                </div>
            </div>

                <div class="col-12 col-md-6 col-lg-9">
                    <div class="customDivStyling">
                        <div ng-include="'features/orders/shared/order/kitsTable.html'"></div>
                    </div>
                    <div class="customDivStyling" ng-class="!viewOrderCtrl.displayNotes ? 'identifiedParts' : ''">
                        <h2 class="">
                            <span translate>ORDER.PARTS</span>
                        </h2>
                        <p translate>ORDER.EX_WORKS_DISCLAIMER</p>
                        
                        <table class="table table-bordered equal-width">
                            <thead>
                                <tr>
                                    <th class="col-md-3 col-12" translate>ORDER.PARTS</th>
                                    <th class="col-md-2 col-12" translate>ORDER.QTY</th>
                                    <th ng-if="!((viewOrderCtrl.hidePrice || viewOrderCtrl.hasNoPrices) && !viewOrderCtrl.isPriceEditable)"
                                        translate>
                                        ORDER.ITEM_PRICE
                                    </th>
                                    <th ng-if="!viewOrderCtrl.hidePrice && viewOrderCtrl.hasVisibleDiscountedPrices()"
                                        translate>ORDER.DISCOUNTED_PRICE</th>
                                    <th ng-if="!(viewOrderCtrl.hidePrice || (viewOrderCtrl.hasNoPrices && !viewOrderCtrl.isPriceEditable))"
                                        translate>
                                        ORDER.TOTAL_PRICE
                                    </th>
                                    <th class="width-5" ng-show="viewOrderCtrl.showPartialShipped" translate>ORDER.QTY_SHIPPED</th>
                                    <th ng-if="viewOrderCtrl.isPreviewStockLevelEnabled && !viewOrderCtrl.isDealerPlusCustomer && !viewOrderCtrl.isStockWarehousesEnabled || viewOrderCtrl.isStockWarehousesEnabled && !viewOrderCtrl.isDealerPlusCustomer && viewOrderCtrl.displayWarehouseName"
                                        translate>
                                        ORDER.STOCK
                                    </th>
                                    <th translate>GENERAL.ACTIONS</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr ng-repeat="item in viewOrderCtrl.data.orderItems | filter: {kitId: '!'}"
                                    ng-class="item.quantity>0 ? '' : 'strike-through'">
                                    <td data-label="{{'ORDER.PART_DETAILS' | translate}}">
                                        <p>
                                                <span class="font-weight-bold" ng-if="viewOrderCtrl.isCustomer || !item.masterPartId">{{item.partNumber}}</span>
                        
                                                <a ng-if="!viewOrderCtrl.isCustomer && item.masterPartId"
                                                    ng-click="viewOrderCtrl.goToMasterPart(item.masterPartId)" href="">{{item.partNumber}}</a>
                                            </strong>
                                            <span ng-if="item.alternatePartNumber"> - ({{item.alternatePartNumber}})</span>
                                            </p>
                                            <span ng-if="viewOrderCtrl.isPartInformationAvailable(item).showPartDescription">-</span>
                                            {{item.partDescription}}
                                        </p>
                                        <p
                                            ng-if="viewOrderCtrl.isPartInformationAvailable(item).showMachineName || viewOrderCtrl.isPartInformationAvailable(item).showModelName">
                                            {{'CREATE_ORDER.IDENTIFIED_FROM' | translate}} {{item.machineName}}
                                            <span ng-if="viewOrderCtrl.isPartInformationAvailable(item).showModelName">-</span>
                                            {{item.modelName}}
                                        </p>
                                    </td>
                        
                                    <td data-label="{{'ORDER.QTY' | translate}}" ng-show="viewOrderCtrl.isQuantityEditable">
                                        <span ng-show="item.quantity > 0">
                                            <input onclick="this.select()" class="priceInput inputButtonWidth" type="number"
                                                ng-model="item.quantity" ng-change="viewOrderCtrl.updateItemTotals(true)" />
                                        </span>
                                        <span ng-hide="item.quantity > 0">{{item.quantity}}</span>
                                    </td>
                        
                                    <td data-label="{{'ORDER.QTY' | translate}}"
                                        ng-hide="viewOrderCtrl.isQuantityEditable && !viewOrderCtrl.hidePrice">
                                        {{item.quantity}}
                                    </td>
                        
                                    <td class="disableWordBreak" data-label="{{'ORDER.ITEM_PRICE' | translate}}"
                                        ng-hide="viewOrderCtrl.hidePrice || viewOrderCtrl.isPriceEditable || viewOrderCtrl.hasNoPrices || (item.price === 0 && viewOrderCtrl.showTBC)">
                                        {{item.price | currency: viewOrderCtrl.selectedCurrency.symbol :2}}
                                    </td>
                                    <td class="disableWordBreak" data-label="{{'ORDER.ITEM_PRICE' | translate}}"
                                        ng-hide="viewOrderCtrl.hidePrice || viewOrderCtrl.isPriceEditable || item.price > 0 || !viewOrderCtrl.showTBC || viewOrderCtrl.hasNoPrices">
                                        -
                                    </td>
                        
                                    <td class="disableWordBreak" ng-show="viewOrderCtrl.isPriceEditable && !viewOrderCtrl.hidePrice"
                                        data-label="{{'ORDER.ITEM_PRICE' | translate}}">
                                        <div class="inputWithIconWrap" ng-show="item.quantity > 0">
                                            <span ng-show="item.quantity > 0"
                                                class="input-icon">{{viewOrderCtrl.selectedCurrency.symbol}}&nbsp;</span>
                                            <input
                                                onclick="this.select()"
                                                ng-init="item.price = item.price || 0" ng-model="item.price"
                                                ng-change="viewOrderCtrl.updateItemTotals(true)"
                                                type="number"
                                                class="input-with-icon"
                                            />
                                            <span ng-hide="item.quantity > 0" class="input-icon">{{item.price |
                                                currency:viewOrderCtrl.selectedCurrency.symbol:2}}</span>
                                        </div>
                                        <span ng-hide="item.quantity > 0">-</span>
                                    </td>
                        
                                    <td ng-show="viewOrderCtrl.hasVisibleDiscountedPrices() && !viewOrderCtrl.hidePrice"
                                        class="disableWordBreak" data-label="{{'ORDER.DISCOUNTED_PRICE' | translate}}">
                                        {{ item.discountedPrice ? (item.discountedPrice | currency:viewOrderCtrl.selectedCurrency.symbol:2) : '-' }}
                                    </td>
                        
                                    <td class="disableWordBreak" data-label="{{'ORDER.TOTAL_PRICE' | translate}}"
                                        ng-hide="viewOrderCtrl.hidePrice || (viewOrderCtrl.hasNoPrices && !viewOrderCtrl.isPriceEditable)">
                                        {{ item.totalPrice ? (item.totalPrice | currency:viewOrderCtrl.selectedCurrency.symbol:2) : '-' }}
                                    </td>
                        
                                    <td data-label="{{'ORDER.QTY_SHIPPED' | translate}}" ng-show="viewOrderCtrl.showPartialShipped">
                                        {{item.shippedQuantity}}
                                    </td>
                                    <td data-label="{{'ORDER.STOCK' | translate}}"
                                        ng-if="viewOrderCtrl.isPreviewStockLevelEnabled && !viewOrderCtrl.isStockWarehousesEnabled && !viewOrderCtrl.isDealerPlusCustomer">
                                        <span title="{{'ORDER.IN_STOCK' | translate}}" tooltip-trigger="outsideClick"
                                            uib-tooltip="{{'ORDER.IN_STOCK' | translate}}" class="success-alert" ng-if="item.stock >= 3"><i
                                                class="fas fa-layer-group text-success pointer"></i></span>
                                        <span title="{{'ORDER.LOW_STOCK' | translate}}" tooltip-trigger="outsideClick"
                                            uib-tooltip="{{'ORDER.LOW_STOCK' | translate}}" class="warning-alert"
                                            ng-if="item.stock < 3 && item.stock > 0 "><i
                                                class="fas fa-layer-group text-warning pointer"></i></span>
                                        <span title="{{'ORDER.STOCK_SUBJECT' | translate}}" tooltip-trigger="outsideClick"
                                            uib-tooltip="{{'ORDER.STOCK_SUBJECT' | translate}}" class="warning-alert"
                                            ng-if="item.stock === null || item.stock < 1"><i
                                                class="fas fa-layer-group text-danger pointer"></i></span>
                                    </td>
                        
                                    <td class="disableWordBreak" data-label="{{'ORDER.STOCK' | translate}}"
                                        ng-if="viewOrderCtrl.isStockWarehousesEnabled && viewOrderCtrl.displayWarehouseName">
                                        <div>
                                            <p>
                                                <span title="{{'ORDER.IN_STOCK' | translate}}" tooltip-trigger="outsideClick"
                                                    uib-tooltip="{{'ORDER.IN_STOCK' | translate}}" class="success-alert mx-2"
                                                    ng-if="item.stock >= 3"><i class="fas fa-circle text-success pointer"></i></span>
                                                <span title="{{'ORDER.LOW_STOCK' | translate}}" tooltip-trigger="outsideClick"
                                                    uib-tooltip="{{'ORDER.LOW_STOCK' | translate}}" class="warning-alert mx-2"
                                                    ng-if="item.stock < 3 && item.stock > 0 "><i
                                                        class="fas fa-circle text-warning pointer"></i></span>
                                                <span title="{{'ORDER.STOCK_SUBJECT' | translate}}" tooltip-trigger="outsideClick"
                                                    uib-tooltip="{{'ORDER.STOCK_SUBJECT' | translate}}" class="warning-alert mx-2"
                                                    ng-if="item.stock === null || item.stock < 1"><i
                                                        class="fas fa-circle text-danger pointer"></i></span>
                                                {{ viewOrderCtrl.data.warehouseName }}
                                            </p>
                                        </div>
                                    </td>
                        
                                    <td data-label="{{'GENERAL.ACTIONS' | translate}}">
                                        <div class="d-flex justify-content-center align-items-center cadGap">
                                            <button type="button" uib-tooltip="{{'ORDER.VIEW_PART_NOTE' | translate}}"
                                                ng-click="viewOrderCtrl.viewPartNote(item.masterPartId)" class="btn secondary partnotes"
                                                ng-class="{'disableButton': !item.note.length > 0}">
                                                <i class="fas fa-sticky-note fa-lg fa-fw"></i>
                                            </button>
                        
                                            <button type="button" ng-class="{'disableButton': !item.techDocs.length > 0}"
                                                ng-disabled="!item.techDocs.length > 0" uib-tooltip="{{'ORDER.VIEW_TECH_INFO' | translate}}"
                                                ng-click="viewOrderCtrl.viewLinkedTechDocs(item)" class="btn secondary warning">
                                                <i class="fa fa-file-pdf fa-lg fa-fw"></i>
                                            </button>
                        
                                            <button type="button" ng-class="item.commentThread && item.unreadComment ? 'newCommentAnim_order' : '' || item.unreadComment ? 'unreadcomment' : '' || {'disableButton': (viewOrderCtrl.data.orderStatus == 'CLOSED' && item.commentThread === null) ||
                                                    (viewOrderCtrl.data.orderStatus == 'CANCELLED' && item.commentThread === null) ||
                                                    (viewOrderCtrl.data.orderStatus == 'SHIPPED' && item.commentThread === null) ||
                                                    (viewOrderCtrl.data.orderStatus == 'EXTERNAL' && item.commentThread === null) ||
                                                (viewOrderCtrl.enquiriesOnly && item.commentThread === null)}" ng-disabled="(viewOrderCtrl.data.orderStatus == 'CLOSED' && item.commentThread === null) ||
                        								(viewOrderCtrl.data.orderStatus == 'CANCELLED' && item.commentThread === null) ||
                        								(viewOrderCtrl.data.orderStatus == 'SHIPPED' && item.commentThread === null) ||
                        								(viewOrderCtrl.data.orderStatus == 'EXTERNAL' && item.commentThread === null) ||
                        								(viewOrderCtrl.enquiriesOnly && item.commentThread === null)"
                                                uib-tooltip="{{'ORDER.COMMENTS_TOOLTIP' | translate}}"
                                                ng-click="viewOrderCtrl.openComment(item.commentThread.id, item.orderItemId)"
                                                class="btn secondary comments">
                                                <i ng-show="item.commentThread === null" class="far fa-comments fa-lg fa-fw"></i>
                        
                                                <i ng-show="!item.unreadComment && item.commentThread !== null"
                                                    class="fa fa-comments fa-lg fa-fw"></i>
                                                <i ng-if="item.commentThread && item.unreadComment" class="fa fa-comments fa-lg fa-fw"></i>
                                            </button>
                        
                                            <span ng-show="viewOrderCtrl.isActionActive">
                                                <button ng-show="!item.archived" uib-tooltip="{{'ORDER.REMOVE' | translate}}"
                                                    ng-click="viewOrderCtrl.removeItem($index)" class="btn secondary danger">
                                                    <i class="fa fa-trash-o fa-lg fa-fw"></i>
                                                </button>
                        
                                                <button ng-show="item.archived" uib-tooltip="{{'ORDER.UNDO' | translate}}"
                                                    ng-click="viewOrderCtrl.removeItem($index)" class="btn secondary danger">
                                                    <i class="fa fa-undo fa-lg fa-fw"></i>
                                                </button>
                                            </span>
                        
                                            <span ng-show="viewOrderCtrl.deleteOrderItem" uib-tooltip="{{'ORDER.REMOVE' | translate}}">
                                                <button ng-show="item.quantity > 0" ng-click="viewOrderCtrl.deleteItem($index)"
                                                    class="btn secondary danger">
                                                    <i class="fa fa-trash-o fa-lg fa-fw"></i>
                                                </button>
                                            </span>
                                        </div>
                                    </td>
                                </tr>
                        
                                <tr ng-hide="viewOrderCtrl.isOrderItemsLoaded" align="center">
                                    <td class="preloader" colspan="6">
                                        <img ng-src="images/cadpreloader.gif" class="ajax-loader" height="60" width="60" />
                                    </td>
                                </tr>
                                <tr ng-show="viewOrderCtrl.onlyKitsPresent()">
                                    <td class="flex-start noPartsBG" colspan="6" translate>ORDER.NO_IDENTIFIED_PARTS</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

            <div class="col-12 col-md-6 col-lg-3 customDivContainer pricingSummary" ng-class="!viewOrderCtrl.displayNotes ? 'priceSummary' : ''" style="display: initial;" ng-if="!viewOrderCtrl.hidePrice">
                <div class="customDivStyling">
                    <div class="card-body p-1">
                        <h2 class="card-title pl-2 pb-2" translate>ORDER.PRICING_SUMMARY</h2>
                        <table class="table">
                            <tbody>
                                <tr>
                                    <th scope="row" data-label="{{'ORDER.ITEMS_TOTAL' | translate}}"><b translate>ORDER.ITEMS_TOTAL</b></th>
                                    <td ng-hide="viewOrderCtrl.hasPricesMissing && viewOrderCtrl.showTBC">
                                        {{viewOrderCtrl.combinedTotalPrice | currency:viewOrderCtrl.selectedCurrency.symbol:2}}
                                    </td>
                                    <td ng-show="viewOrderCtrl.hasPricesMissing && viewOrderCtrl.showTBC">TBC</td>
                                </tr>
                                <tr ng-show="viewOrderCtrl.isDiscountVisible && viewOrderCtrl.data.percentageDiscount > 0">
                                    <th scope="row" data-label="{{'ORDER.DISCOUNT_PERCENTAGE' | translate}}">
                                        <b>
                                            {{'ORDER.DISCOUNT_PERCENTAGE' | translate}}
                                            <span ng-hide="viewOrderCtrl.isDiscountEditable">%</span></b
                                        >
                                    </th>
                                    <td class="" ng-hide="viewOrderCtrl.isDiscountEditable">{{viewOrderCtrl.data.percentageDiscount}}</td>

                                    <td ng-show="viewOrderCtrl.isDiscountEditable">
                                        <div class="inputWithIconWrap">
                                            <span class="input-icon"><span>%&nbsp;</span></span>
                                            <input
                                                onclick="this.select()"
                                                ng-disabled="!viewOrderCtrl.isDiscountEnabled"
                                                ng-model="viewOrderCtrl.data.percentageDiscount"
                                                ng-change="viewOrderCtrl.updateItemTotals(true)"
                                                type="number"
                                                class="input-with-icon"
                                            />
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th scope="row" data-label="{{'ORDER.SHIPPING' | translate}}">
                                        <b translate>ORDER.SHIPPING</b>

                                        <span
                                            ng-show="viewOrderCtrl.isCustomer && viewOrderCtrl.showShippingPriceDisclaimer && !viewOrderCtrl.isSupreme"
                                            class="d-inline-flex align-items-center fa-stack fa-lg fa-customStack"
                                            uib-tooltip="{{'ORDER.SHIPPING_COST_SUBJECT' | translate}}"
                                            tooltip-trigger="outsideClick"
                                        >
                                            <i class="fa fa-circle fa-stack-2x"></i
                                            ><!--Make background icon large-->
                                            <i class="fa fa-info fa-stack-1x text-white"></i
                                            ><!--make foreground icon smaller-->
                                        </span>
                                    </th>
                                    <td
                                        ng-show="!viewOrderCtrl.isShippingEditable && viewOrderCtrl.data.originalShippingPrice !== undefined"
                                    >
                                        {{viewOrderCtrl.data.shippingPrice | currency:viewOrderCtrl.selectedCurrency.symbol:2}}
                                    </td>
                                    <td
                                        ng-show="!viewOrderCtrl.isShippingEditable && viewOrderCtrl.data.originalShippingPrice === undefined"
                                    >
                                        TBC
                                    </td>
                                    <td ng-show="viewOrderCtrl.isShippingEditable">
                                        <div class="inputWithIconWrap">
                                            <span class="input-icon">{{viewOrderCtrl.selectedCurrency.symbol}}&nbsp;</span>
                                            <input
                                                onclick="this.select()"
                                                ng-model="viewOrderCtrl.data.shippingPrice"
                                                ng-change="viewOrderCtrl.updateItemTotals(true)"
                                                type="number"
                                                class="input-with-icon"
                                            />
                                        </div>
                                    </td>
                                </tr>
                                <tr ng-show="viewOrderCtrl.showTax">
                                    <th scope="row" data-label="{{'ORDER.TAX' | translate}}"><b translate>ORDER.TAX</b></th>

                                    <td>{{viewOrderCtrl.data.taxPrice | currency:viewOrderCtrl.selectedCurrency.symbol:2}}</td>
                                </tr>
                                <tr>
                                    <th data-label="{{'ORDER.TOTAL' | translate}}"><b translate>ORDER.TOTAL</b></th>
                                    <td ng-hide="viewOrderCtrl.hasPricesMissing && viewOrderCtrl.showTBC">
                                        {{viewOrderCtrl.total | currency:viewOrderCtrl.selectedCurrency.symbol:2}}
                                    </td>
                                    <td ng-show="viewOrderCtrl.hasPricesMissing && viewOrderCtrl.showTBC">TBC</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="card-footer text-center">
                        <div ng-hide="viewOrderCtrl.hidePrice">
                            <span ng-hide="viewOrderCtrl.showCurrencySelection"
                                >{{"ORDER.PRICES_SHOWN_IN" | translate}} <strong>{{viewOrderCtrl.selectedCurrency.display}}</strong></span
                            >
                            <span ng-show="viewOrderCtrl.showCurrencySelection"
                                >{{"ORDER.PRICES_SHOWN_IN" | translate}}
                                <select
                                    class="currency-selector"
                                    ng-options="currency as currency.display for currency in viewOrderCtrl.currencies track by currency.code"
                                    ng-model="viewOrderCtrl.selectedCurrency"
                                    ng-change="viewOrderCtrl.orderEdited()"
                                ></select>
                            </span>
                        </div>
                    </div>
                </div>
            </div>


            <div class="col-md-12 customDivContainer order_partsList manuallyAddedOrder" ng-show="viewOrderCtrl.displayAdditionalParts">
                <div class="customDivStyling">
                    <h2 class=""><span translate>ORDER.MANUALLY_ADDED_PARTS</span></h2>
                    <div class="">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th translate>ORDER.PART_DETAILS</th>
                                    <th translate>ORDER.QTY</th>
                                    <th ng-hide="viewOrderCtrl.hidePrice" translate>ORDER.ITEM_PRICE</th>
                                    <th ng-hide="viewOrderCtrl.hidePrice" translate>ORDER.PRICE</th>
                                    <th ng-show="viewOrderCtrl.isActionActive" translate>ORDER.REMOVE</th>
                                </tr>
                            </thead>

                            <tbody>
                                <tr
                                    ng-repeat="manualPart in viewOrderCtrl.additionalParts"
                                    ng-class="manualPart.quantity>0 ? '' : 'strike-through'"
                                >
                                    <td data-label="{{'ORDER.PART_DETAILS' | translate}}">
                                        <strong>{{"ORDER.PART" | translate}}# {{manualPart.partNumber}}</strong> -
                                        {{manualPart.partDescription}} {{"ORDER.BASED_ON_PRODUCT" | translate}} -
                                        <strong>{{manualPart.machineName}}</strong>
                                    </td>

                                    <td data-label="{{'ORDER.QTY' | translate}}" ng-show="viewOrderCtrl.isQuantityEditable">
                                        <span ng-show="manualPart.quantity > 0">
                                            <input
                                                onclick="this.select()"
                                                class="priceInput inputButtonWidth"
                                                type="number"
                                                min="0"
                                                ng-model="manualPart.quantity"
                                                ng-change="viewOrderCtrl.updateManualPartTotals(true)"
                                            />
                                        </span>
                                        <span ng-hide="manualPart.quantity > 0">{{manualPart.quantity}}</span>
                                    </td>

                                    <td data-label="{{'ORDER.QTY' | translate}}" ng-hide="viewOrderCtrl.isQuantityEditable">
                                        {{manualPart.quantity}}
                                    </td>
                                    <td
                                        class="disableWordBreak"
                                        data-label="{{'ORDER.ITEM_PRICE' | translate}}"
                                        ng-show="!viewOrderCtrl.isPriceEditable && !viewOrderCtrl.hidePrice">
                                        {{manualPart.price | currency:viewOrderCtrl.selectedCurrency.symbol:2}}
                                    </td>

                                    <td
                                        class="disableWordBreak"
                                        data-label="{{'ORDER.ITEM_PRICE' | translate}}"
                                        ng-show="viewOrderCtrl.isPriceEditable && !viewOrderCtrl.hidePrice">
                                        <div ng-show="manualPart.quantity > 0" class="inputWithIconWrap">
                                            <span class="input-icon">{{viewOrderCtrl.selectedCurrency.symbol}}&nbsp;</span>
                                            <input
                                                class="input-with-icon"
                                                type="number"
                                                ng-model="manualPart.price"
                                                ng-change="viewOrderCtrl.updateManualPartTotals(true)"
                                            />
                                            <span ng-hide="manualPart.quantity > 0" class="input-icon"
                                                >{{item.price | currency:viewOrderCtrl.selectedCurrency.symbol:2}}</span
                                            >
                                        </div>
                                    </td>

                                    <td
                                        class="disableWordBreak"
                                        data-label="{{'ORDER.PRICE' | translate}}"
                                        ng-hide="viewOrderCtrl.hidePrice">
                                        {{manualPart.totalPrice | currency:viewOrderCtrl.selectedCurrency.symbol:2}}
                                    </td>

                                    <td data-label="{{'ORDER.REMOVE' | translate}}" ng-show="viewOrderCtrl.isActionActive">
                                        <button
                                            ng-show="!manualPart.archived"
                                            uib-tooltip="{{'ORDER.REMOVE' | translate}}"
                                            ng-click="viewOrderCtrl.removeManualPart($index)"
                                            class="btn secondary danger"
                                        >
                                            <i class="fa fa-trash-o fa-lg fa-fw"></i>
                                        </button>

                                        <button
                                            ng-show="manualPart.archived"
                                            uib-tooltip="{{'ORDER.UNDO' | translate}}"
                                            ng-click="viewOrderCtrl.removeManualPart($index)"
                                            class="btn secondary danger"
                                        >
                                            <i class="fa fa-undo fa-lg fa-fw"></i>
                                        </button>
                                    </td>
                                </tr>

                                <tr data-label="{{'ORDER.REMOVE' | translate}}" ng-hide="viewOrderCtrl.isOrderItemsLoaded" align="center">
                                    <td class="preloader" colspan="6">
                                        <img ng-src="images/cadpreloader.gif" class="ajax-loader" height="60" width="60" />
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="col-md-12 customDivContainer addPartsOrder" ng-show="viewOrderCtrl.isAddPartsVisible && !viewOrderCtrl.isSupreme">
                <div class="customDivStyling">
                    <h2 class=""><span translate>ORDER.ADD_PARTS_TO_ORDER</span></h2>
                    <form class="form" id="addManualPartsForm" name="viewOrderCtrl.addManualPartsForm">
                        <div class="">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>
                                            {{"ORDER.PART_NO" | translate}}
                                            <span class="required-field">&ast;{{"ORDER.REQUIRED" | translate}}</span>
                                        </th>
                                        <th>
                                            {{"ORDER.PART_DESC" | translate}}
                                            <span class="required-field">&ast;{{"ORDER.REQUIRED" | translate}}</span>
                                        </th>
                                        <th>{{"ORDER.PRODUCT" | translate}}</th>
                                        <th>
                                            {{"ORDER.QTY" | translate}}
                                            <span class="required-field">&ast;{{"ORDER.REQUIRED" | translate}}</span>
                                        </th>
                                        <th><span>{{"ORDER.REMOVE" | translate}}</span></th>
                                    </tr>
                                </thead>

                                <tbody>
                                    <tr ng-repeat="manualPart in viewOrderCtrl.addManualParts">
                                        <td>
                                            <input
                                                maxlength="200"
                                                data-label="{{'ORDER.PART_NUMBER' | translate}}"
                                                class="w-100 mw-100"
                                                placeholder="{{'ORDER.PART_NUMBER' | translate}}"
                                                type="text"
                                                ng-model="manualPart.partNumber"
                                                required
                                                ng-class=""
                                                name="partNumber"
                                            />
                                        </td>
                                        <td>
                                            <input
                                                maxlength="200"
                                                data-label="{{'ORDER.PART_DESCRIPTION_EXAMPLE' | translate}}"
                                                class="w-100 mw-100"
                                                placeholder="{{'ORDER.PART_DESCRIPTION_EXAMPLE' | translate}}"
                                                type="text"
                                                ng-model="manualPart.partDescription"
                                                required
                                                ng-class=""
                                                name="partDescription"
                                            />
                                        </td>
                                        <td>
                                            <input
                                                maxlength="80"
                                                data-label="{{'ORDER.PRODUCT_NAME' | translate}}"
                                                class="w-100 mw-100"
                                                placeholder="{{'ORDER.PRODUCT_NAME' | translate}}"
                                                type="text"
                                                ng-model="manualPart.machineName"
                                                name="machineName"
                                            />
                                        </td>
                                        <td>
                                            <input
                                                data-label="{{'ORDER.QUANTITY' | translate}}"
                                                class="w-100 mw-100"
                                                placeholder="{{'ORDER.QUANTITY' | translate}}"
                                                ng-model="manualPart.quantity"
                                                type="number"
                                                ng-model-options=""
                                            />
                                        </td>
                                        <td>
                                            <button
                                                class="btn secondary small danger"
                                                uib-tooltip="{{'ORDER.REMOVE' | translate}}"
                                                ng-click="viewOrderCtrl.removeAddManualPart(manualPart)"
                                            >
                                                <i class="fa fa-trash-o"></i>
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <div class="table-footer">
                                <div class="left">
                                    <a href="" class="light" ng-click="viewOrderCtrl.createAddManualPart()"
                                        ><i class="fa fa-plus"></i> {{"ORDER.ADD_NEW_ITEM" | translate}}</a
                                    >
                                </div>
                                <div ng-show="viewOrderCtrl.addManualPartsActive" class="d-flex justify-content-end">
                                    <button class="btn secondary" ng-click="viewOrderCtrl.cancelCreateAddManualParts()" translate>
                                        GENERAL.CANCEL
                                    </button>

                                    <button class="btn primary ml-2" type="submit" ng-click="viewOrderCtrl.saveAddManualParts()" translate>
                                        ORDER.ADD_TO_ORDER
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <div
                class="col-md-12 csvOrder"
                ng-class="!viewOrderCtrl.displayNotes ? 'csvDownload' : ''"
                ng-show="viewOrderCtrl.exportPartDataCsvVisible"
            >
                <div class="flex customDivStyling">
                    <div class="col-12 col-md-auto">
                        <h2 class=""><span translate>ORDER.DOWNLOAD_CSV</span></h2>
                        <p class="mb-0" translate>ORDER.CSV_DESCRIPTION</p>
                    </div>

                    <div class="col-12 col-md-auto ml-auto">
                        <button class="btn btn-primary" ng-click="viewOrderCtrl.exportOrderPartsToCSV()" translate>
                            ORDER.DOWNLOAD_CSV
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
