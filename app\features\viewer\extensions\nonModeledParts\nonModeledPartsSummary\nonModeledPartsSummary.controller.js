(function () {
    'use strict';

    angular
        .module('app.viewer')
        .controller('NonModeledPartSummaryController', NonModeledPartSummaryController);

    NonModeledPartSummaryController.$inject = ['nonModeledPartService', 'viewerBannerService', '$stateParams', '$uibModal', '$rootScope', '$scope'];

    function NonModeledPartSummaryController(nonModeledPartService, viewerBannerService, $stateParams, $uibModal, $rootScope, $scope) {
        var vm = this;

        vm.isOpen = true;
        vm.existingNonModeledParts = {};
        vm.createNonModeledPart = createNonModeledPart;
        vm.editNonModeledPart = editNonModeledPart;
        vm.deleteNonModeledPart = deleteNonModeledPart;

        initialize();

        function initialize() {
            vm.modelId = $stateParams.modelId;
            fetchNonModeledParts();
        }

        function fetchNonModeledParts(){
            nonModeledPartService.fetchNonModeledPartsForModel(vm.modelId)
                 .then(fetchNonModeledPartsForModelSuccess, fetchNonModeledPartsForModelFailed);
        }

        function fetchNonModeledPartsForModelSuccess(response) {
            vm.existingNonModeledParts = response.data;
        }

        function fetchNonModeledPartsForModelFailed(error) {
            viewerBannerService.setNotification("ERROR", error);
        }

        function createNonModeledPart() {
            vm.isOpen = false;
            $rootScope.$broadcast("create-non-modeled-parts-opened");
        }

        function editNonModeledPart(nonModeledPart) {
            vm.isOpen = false;
            $rootScope.$broadcast("create-non-modeled-parts-opened", nonModeledPart.id);
        }

        function deleteNonModeledPart(nonModeledPart) {
            vm.successMessage = "";
            var deleteObject = {
                name: "Non modelled parts for part " + nonModeledPart.partNumber,
                id: nonModeledPart.id,
                url: '/nonmodelled/' + nonModeledPart.id
            };

            $uibModal.open({
                templateUrl: 'features/shared/commonDelete/deleteDialogBox.html',
                controller: 'DeleteController',
                controllerAs: 'deleteCtrl',
                size: 'sm',
                resolve: {
                    deleteObject: function () {
                        return deleteObject;
                    }
                }
            }).result.then(function () {
                fetchNonModeledParts();
            });
        }

        $scope.$on("create-non-modeled-parts-closed", function () {
            vm.isOpen = true;
            initialize();
        });

        $scope.$on("nonModeled-updated", function () {
            initialize();
        });
    }

})();
