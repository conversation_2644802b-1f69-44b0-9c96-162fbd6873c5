<section class="pallet-container">

    <div class="pallet-heading p-3">
        <h3 class="mb-0">{{'CUST_VIEWER.PARTS_DATA' | translate}} <span ng-if="customerViewerCtrl.nonModelledParts">({{ "BUY_PART.TITLE" | translate }})</span></h3>
    </div>

    <div class="options-container p-4" ng-if="customerViewerCtrl.containsOptionsSet">
        <div class="options-header d-flex align-items-center cadGap">
            <div class="optionSetIcon d-flex justify-content-center align-items-center me-3">
                <i class="fas fa-object-ungroup"></i>
            </div>
            <div>
                <div class="options-title" translate>CUST_VIEWER.OPTIONS</div>
                <div class="options-description">{{ customerViewerCtrl.optionsSets.description }}</div>
            </div>
        </div>
        <div class="option-set-wrapper">
            <div class="option-set-container"
                ng-repeat="option in customerViewerCtrl.optionsSets.optionsSet track by (option.id + '_' + option.partNumber)"
                ng-class="{'selected': customerViewerCtrl.selectedOption === option}">
                <input type="radio" id="option-{{option.partNumber}}" name="optionSet"
                    ng-model="customerViewerCtrl.selectedOption" ng-value="option"
                    ng-change="customerViewerCtrl.updateOptionSelected()">
                <label for="option-{{option.partNumber}}" class="option-content">
                    <div class="option-details">
                        <div class="top-row">
                            <div class="part-number">
                                <strong translate>CUST_VIEWER.PART_NUMBER</strong>
                                <div>{{ option.partNumber }}</div>
                            </div>
                            <div class="description">
                                <strong translate>CUST_VIEWER.PART_DESC</strong>
                                <div title="{{ option.partDescription }}" class="truncate-description">{{
                                    option.partDescription }}</div>
                            </div>
                        </div>
                        <div class="bottom-row">
                            <div class="price d-flex align-items-center cadGap" ng-if="!customerViewerCtrl.hidePrice">
                                <strong class="mb-0" translate>CUST_VIEWER.PRICE</strong>
                                <div>{{ option.price | currency }}</div>
                            </div>
                            <div class="stock d-flex align-items-center cadGap">
                                <strong class="mb-0" translate>CUST_VIEWER.STOCK</strong>
                                <div ng-if="!customerViewerCtrl.isStockWarehousesEnabled">
                                    <span title="{{'CUST_VIEWER.IN_STOCK' | translate}}" tooltip-trigger="outsideClick"
                                        uib-tooltip="{{'CUST_VIEWER.IN_STOCK' | translate}}" class="success-alert"
                                        ng-if="option.stock >= 3"><i
                                            class="fas fa-layer-group text-success pointer"></i></span>
                                    <span title="{{'CUST_VIEWER.LOW_STOCK' | translate}}" tooltip-trigger="outsideClick"
                                        uib-tooltip="{{'CUST_VIEWER.LOW_STOCK' | translate}}" class="warning-alert"
                                        ng-if="option.stock < 3 && option.stock > 0"><i
                                            class="fas fa-layer-group text-warning pointer"></i></span>
                                    <span title="{{'CUST_VIEWER.STOCK_SUBJECT' | translate}}" tooltip-trigger="outsideClick"
                                        uib-tooltip="{{'CUST_VIEWER.STOCK_SUBJECT' | translate}}" class="danger-alert"
                                        ng-if="option.stock === null || option.stock < 1"><i
                                            class="fas fa-layer-group text-danger pointer"></i></span>
                                </div>
                                <div ng-if="customerViewerCtrl.isStockWarehousesEnabled">
                                    <span
                                        ng-class="{'text-success': option.stock >= 3, 'text-warning': option.stock < 3 && option.stock > 0, 'text-danger': option.stock === null || option.stock < 1}">
                                        {{ option.stock || 0 }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </label>
            </div>
    
    
    </div>
    <div class="d-flex align-items-center mb-2 cadGap">
        <input type="number" class="m-0 quantity-box" min="1" ng-model="customerViewerCtrl.optionsSets.quantity"
            ng-blur="customerViewerCtrl.optionQuantityChanged()">
        <button class="btn primary fixed-width-btn" ng-click="customerViewerCtrl.addOptionToBasket()"
            ng-disabled="!customerViewerCtrl.selectedOption.partNumber">
            <div class="d-flex justify-content-center align-items-center w-100"
                ng-hide="customerViewerCtrl.isAddBasketButtonClicked">
                <span>{{"CUST_VIEWER.ADD_TO_BASKET" | translate}}</span>
            </div>
            <div class="d-flex justify-content-center align-items-center w-100 check-icon-wrapper"
                ng-show="customerViewerCtrl.isAddBasketButtonClicked">
                <i class="fa fa-check" style="font-size: 1.4em!important;"></i>
            </div>
        </button>
    </div>
    </div>

    <buy-part ng-show="customerViewerCtrl.nonModelledParts" model-id="{{customerViewerCtrl.myModelId}}"></buy-part>

    <div class="add-item" ng-if="!customerViewerCtrl.containsOptionsSet && !customerViewerCtrl.nonModelledParts">
        <div class="partsPalletContainer position-relative">
            <div ng-show="customerViewerCtrl.isLoading"
                 style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; display: flex; justify-content: center; align-items: center;">
                <span class="spinner-border text-primary" role="status" aria-hidden="true"
                      style="width: 3rem; height: 3rem"></span>
            </div>
            <div class="parts-pallet-info border-bottom border-secondary"
                 ng-style="{'opacity': customerViewerCtrl.isLoading ? 0.25 : 1}">
                <div class="part-number">
                    <small class="text-muted" translate>CUST_VIEWER.PART_NUMBER</small>
                    <h4 class="mb-0">{{ customerViewerCtrl.part.partNumber || ('CUST_VIEWER.NO_PART_SELECTED' | translate) }}</h4>
                    <small class="cadBlue hover" ng-if="customerViewerCtrl.showSupersessionHistoryIcon" ng-click="customerViewerCtrl.toggleSupersessionHistory()"
                    style="cursor: pointer; text-decoration: underline;">
                    {{ customerViewerCtrl.showSupersessionHistory ? 'CUST_VIEWER.HIDE_SUPERSESSION_HISTORY' :
                    'CUST_VIEWER.VIEW_SUPERSESSION_HISTORY' | translate }}
                  </small>
                </div>
                <div class="description">
                    <small class="text-muted" translate>CUST_VIEWER.PART_DESC</small>
                    <h4 class="mb-2">{{customerViewerCtrl.part.description || ('' | translate)}}</h4>
                </div>

                <div class="alt-part-number">
                    <small ng-if="customerViewerCtrl.part.alternatePartNumber" class="text-muted"
                           translate>CUST_VIEWER.ALT_PART_NUMBER</small>
                    <h4 class="font-weight-light pt-1">{{customerViewerCtrl.part.alternatePartNumber}}</h4>
                </div>
                <div class="price">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex cadGap align-items-center">
                            <small ng-if="customerViewerCtrl.previewPricingEnabled && !customerViewerCtrl.hidePrice" class="text-muted mb-0 me-2"
                                translate>CUST_VIEWER.PRICE</small>
                            <button title="{{'CUST_VIEWER.HIDE_SHOW_PRICE' | translate}}" ng-if="customerViewerCtrl.previewPricingEnabled" ng-click="customerViewerCtrl.togglePriceVisibility()"
                            class="eye-button ml-auto" ng-class="{'mb-3': customerViewerCtrl.hidePrice}">
                                <i
                                ng-class="{'fa fa-eye-slash': !customerViewerCtrl.hidePrice, 'fa fa-eye': customerViewerCtrl.hidePrice}"></i>
                            </button>
                        </div>
                    </div>
                    <div class="flex justify-content-between" ng-if="!customerViewerCtrl.hidePrice">
                        <div ng-class="{'hidden-but-space': !customerViewerCtrl.part.partNumber}"
                             class="d-flex align-items-center">
                            <h4>{{customerViewerCtrl.previewPricingEnabled && customerViewerCtrl.part.partNumber ? 
                                (customerViewerCtrl.part.price | currency:customerViewerCtrl.defaultCurrency.symbol:2) : ''}}</h4>
                            <h4 ng-if="customerViewerCtrl.previewPricingEnabled && customerViewerCtrl.part.partNumber && (customerViewerCtrl.part.price === null || customerViewerCtrl.part.price === undefined || !customerViewerCtrl.part.price)"
                                translate>CUST_VIEWER.TBC</h4>
                        </div>
                        <div ng-if="!customerViewerCtrl.hidePrice && customerViewerCtrl.calculateDiscountedPrice() !== null && customerViewerCtrl.calculateDiscountedPrice() !== undefined" class="d-flex cadGap align-items-center">
                            <h4 class="text-muted">
                                {{customerViewerCtrl.calculateDiscountedPrice() |
                                currency:customerViewerCtrl.defaultCurrency.symbol:2}}</h4>
                            <small class="text-muted mt-1"
                                translate>CUST_VIEWER.PRICE_DISCOUNT</small>
                        </div>
                    </div>
                </div>
            </div>

            <div ng-if="customerViewerCtrl.hasOrderRole" class="d-flex align-items-center mt-3 ng-scope"
                 ng-class="{'justify-content-between': customerViewerCtrl.isStockWarehousesEnabled}">
                <div class="d-flex cadGap align-items-center side-forms">
                    <input
                        ng-disabled="!customerViewerCtrl.part.partNumber || customerViewerCtrl.part.partNumber === ''"
                        class="quantity-box m-0"
                        type="number"
                        min="1"
                        placeholder="1"
                        pattern="[0-9.]+"
                        ng-model="customerViewerCtrl.part.quantity"
                        ng-blur="customerViewerCtrl.quantityChanged()"
                    />

                    <button class="btn primary fixed-width-btn" ng-click="customerViewerCtrl.addToBasket()"
                        ng-disabled="!customerViewerCtrl.part.partNumber || customerViewerCtrl.part.partNumber === ''">
                        <div class="d-flex justify-content-center align-items-center w-100"
                            ng-hide="customerViewerCtrl.isAddBasketButtonClicked">
                            <span>{{"CUST_VIEWER.ADD_TO_BASKET" | translate}}</span>
                        </div>
                        <div class="d-flex justify-content-center align-items-center check-icon-wrapper w-100"
                            ng-show="customerViewerCtrl.isAddBasketButtonClicked">
                            <i class="fa fa-check" style="font-size: 1.4em!important;"></i>
                        </div>
                    </button>
            
                    <a
                        ng-show="customerViewerCtrl.hasLinkedTechDocs"
                        href=""
                        style="font-size: x-large"
                        ng-click="customerViewerCtrl.viewLinkedTechDocs()"
                        ><i
                          class="fa fa-fw fa-file-pdf"
                          uib-tooltip="{{'CUST_VIEWER.VIEW_TECH_INFO' | translate}}"
                        ></i
                    ></a>
                </div>
                <div class="d-flex cadGap align-items-center stock-level"
                     ng-if="customerViewerCtrl.isPreviewStockLevelEnabled && !customerViewerCtrl.isDealerPlusCustomer">
                    <div ng-if="!customerViewerCtrl.isStockWarehousesEnabled">
                        <span title="{{'CUST_VIEWER.IN_STOCK' | translate}}" tooltip-trigger="outsideClick"
                              uib-tooltip="{{'CUST_VIEWER.IN_STOCK' | translate}}" class="success-alert ml-3"
                              ng-if="customerViewerCtrl.part.stock >= 3">
                              <i class="fas fa-layer-group text-success pointer"></i>
                        </span>
                        <span title="{{'CUST_VIEWER.LOW_STOCK' | translate}}" tooltip-trigger="outsideClick"
                              uib-tooltip="{{'CUST_VIEWER.LOW_STOCK' | translate}}" class="warning-alert ml-3"
                              ng-if="customerViewerCtrl.part.stock < 3 && customerViewerCtrl.part.stock > 0 ">
                              <i class="fas fa-layer-group text-warning pointer"></i>
                        </span>
                        <span title="{{'CUST_VIEWER.STOCK_SUBJECT' | translate}}" tooltip-trigger="outsideClick"
                              uib-tooltip="{{'CUST_VIEWER.STOCK_SUBJECT' | translate}}" class="warning-alert ml-3"
                              ng-if="customerViewerCtrl.part.stock === null || customerViewerCtrl.part.stock < 1">
                              <i class="fas fa-layer-group text-danger pointer"></i>
                        </span>
                    </div>

                    <div ng-if="customerViewerCtrl.isStockWarehousesEnabled && customerViewerCtrl.part.partNumber && !customerViewerCtrl.part.stock"
                         ng-class="{'text-success': customerViewerCtrl.part.stock > 0,'text-danger': customerViewerCtrl.part.stock === 0 || customerViewerCtrl.part.stock === null || customerViewerCtrl.part.stock === undefined}">
                        <p class="mb-0 font-weight-bold">{{'CUST_VIEWER.STOCK' | translate}}:
                            {{customerViewerCtrl.part.stock || 'CUST_VIEWER.TBC' | translate }}
                        </p>
                    </div>

                    <div class="nav-right navigation_custom" role="navigation_custom"
                         ng-if="customerViewerCtrl.isStockWarehousesEnabled && customerViewerCtrl.part.partNumber && customerViewerCtrl.part.stock">
                        <ul class="list-unstyled mb-0">
                            <li>
                                <a class="font-weight-bold dark-secondary profile" href="" ng-class="{
                                     'text-success': customerViewerCtrl.part.stock > 0,
                                     'text-warning': customerViewerCtrl.part.stock < 3 && customerViewerCtrl.part.stock > 0,
                                     'text-danger': customerViewerCtrl.part.stock === 0 || customerViewerCtrl.part.stock === null || customerViewerCtrl.part.stock === undefined
                                   }">
                                    <i class="fa fa-info-circle cadBlue"></i>
                                    {{ customerViewerCtrl.warehouseName || 'CUST_VIEWER.STOCK' | translate }}: {{ customerViewerCtrl.part.stock || 0 }}
                                </a>
                                <ul class="user-dropdown-stock user-dropdown-stock-position U-open">
                                    <p class="font-weight-bold text-dark">{{ 'CUST_VIEWER.WAREHOUSE_STOCK' | translate }}</p>
                                    <li ng-repeat="stock in customerViewerCtrl.warehouseStock track by $index"
                                        ng-class="{'text-success': stock.stock >= 3, 'text-warning': stock.stock > 0 && stock.stock < 3, 'text-danger': stock.stock === 0}">
                                        {{ stock.name }}: {{ stock.stock }}
                                    </li>
                                </ul>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <div ng-if="customerViewerCtrl.showPartNotes()" class="part-note mt-3 py-2 px-3 cursor-pointer">
                <div class="d-flex justify-content-between" ng-click="customerViewerCtrl.isNotesExpanded = !customerViewerCtrl.isNotesExpanded">
                  <div>
                    <strong>{{ 'CUST_VIEWER.NOTES' | translate }}</strong>
                    <span ng-if="!customerViewerCtrl.isNotesExpanded">{{ customerViewerCtrl.shortenNote(customerViewerCtrl.selectedPartNote[0]) }}</span>
                  </div>
                  <i class="fa"
                    ng-class="{'fa-chevron-down': !customerViewerCtrl.isNotesExpanded, 'fa-chevron-up': customerViewerCtrl.isNotesExpanded}"></i>
                </div>
                <div ng-click="customerViewerCtrl.isNotesExpanded = !customerViewerCtrl.isNotesExpanded" ng-if="customerViewerCtrl.isNotesExpanded">
                  <div ng-repeat="note in customerViewerCtrl.part.note track by $index">
                    <span ng-if="note.includes('*')">
                      {{ note.replace('*', '') }}
                    </span>
                    <span ng-if="!note.includes('*')">
                      {{ note }}
                    </span>
                  </div>
                </div>
              </div>
        </div>
        </div>
    </div>
</section>
