<div id="workInstructionsManufacturerViewerId">

    <div class="vertical-align loader" id="loader">
        <div id="loader-text">
            <i class="fas fa-sync-alt fa-spin"></i>
            <p translate>MANUFACTURER_VIEWER.LOADING</p>
            <p translate>MANUFACTURER_VIEWER.FEW_MINS</p>
        </div>
    </div>
    <header class="site-header viewer-header">
        <a href="" class="back-to-products" ng-click="manufacturerWorkInstructionsCtrl.backToViewables()">
            <div class="viewer-back-arrow">
                <i class="fa fa-chevron-left"></i>
            </div>
            <h3 class="text-overflow">
                {{manufacturerWorkInstructionsCtrl.machineName}}- {{manufacturerWorkInstructionsCtrl.viewableName}}</h3>
            <p><small translate>MANUFACTURER_VIEWER.BACK_TO</small></p>
        </a>

    </header>

    <div class="product-viewer vertical">
        <viewer-banner></viewer-banner>

        <div class="explode-tool-container">

            <div class="newExplodeRangeInput" ng-show="manufacturerWorkInstructionsCtrl.isExplodeSliderVisible">
                <input type="range" id="explode_range" value="0" min="0" max="100" step="10"
                       data-show-value="true">
            </div>
        </div>

        <div class="viewerMessage" ng-show="manufacturerWorkInstructionsCtrl.viewerMessage !== ''"><h2>
            {{manufacturerWorkInstructionsCtrl.viewerMessage}}</h2>
        </div>

        <div ng-class="{'viewerSideMenuOpen': manufacturerWorkInstructionsCtrl.isSideMenuOpen,  'viewer':!manufacturerWorkInstructionsCtrl.isSideMenuOpen}"
             id="MyViewerDiv"></div>

    </div>

    <work-side-menu></work-side-menu>

    <div class="product-thumbnails-carousel-wrap vertical manufacturer">
        <div class="product-thumbnails-carousel">
            <!--The take-snapshot-container was updated in manufacturer and softcopy viewer, may need changed here to fit this pages styling-->
            <div class="take-snapshot-container">
                <div class="snapshot-container">
                    <button class="btn btn-block primary" style="padding: 7px; border-radius: 0px;"
                            ng-click="manufacturerWorkInstructionsCtrl.saveSnapshot()">
                        <i class="fa fa-camera" aria-hidden="true"></i> &nbsp;
                        {{"MANUFACTURER_VIEWER.ADD_NEW_SNAP" | translate}}
                    </button>

                    <!-- Buttons to animate snapshots -->
                    <div class="btn btn-block" style="padding: 7px; border: none; border-radius: 0px; background-color: #F2F6F9; margin-top: 0;">
                        <button ng-class="'btn-play'" ng-click="manufacturerWorkInstructionsCtrl.moveToStart()"> <i class="fas fa-step-backward"></i> </button>
                        <button ng-class="'btn-play'" ng-click="manufacturerWorkInstructionsCtrl.moveBackward()"> <i class="fas fa-backward"></i> </button>
                        <button ng-class="'btn-play'" ng-click="manufacturerWorkInstructionsCtrl.moveForward()"> <i class="fas fa-forward"></i> </button>
                        <button ng-class="'btn-play'" ng-click="manufacturerWorkInstructionsCtrl.moveToEnd()"> <i class="fas fa-step-forward"></i> </button>
                    </div>
                </div>
            </div>



            <div ng-class="manufacturerWorkInstructionsCtrl.selectedIndex === $index ? 'snapshot-active' : '' " ng-repeat="snapshot in manufacturerWorkInstructionsCtrl.workInstructionsDetails track by snapshot.stateName" class="panel work-instruction-snapshot">

                <div ng-click="manufacturerWorkInstructionsCtrl.loadSnapshot($index)">
                    {{snapshot.stateName}}
                </div>
                <div class="d-flex pull-right">
                    <div ng-click="manufacturerWorkInstructionsCtrl.editSnapshotName($index)">
                        <i class="fa fa-pencil"></i>
                    </div>
                    <div ng-click="manufacturerWorkInstructionsCtrl.deleteSnapshot($index)" ng-if="!$first">
                        <i class="fa fa-trash px-2"></i>
                    </div>
                    <div ng-click="manufacturerWorkInstructionsCtrl.overwriteSnapshot($index)" ng-if="manufacturerWorkInstructionsCtrl.selectedIndex === $index">
                        <i class="fa fa-refresh"></i>
                    </div>
                </div>

            </div>


        </div>


    </div>

    <div>

    </div>

</div>
