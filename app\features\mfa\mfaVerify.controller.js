(function () {
    'use strict';

    angular
        .module('app.mfa')
        .controller('MfaVerifyController', MfaVerifyController);

    MfaVerifyController.$inject = ['mfaService'];

    function MfaVerifyController(mfaService) {
        var vm = this;

        vm.verifyUserCode = verifyUserCode;

        vm.verifyCode = "";

        function verifyUserCode() {
            mfaService.verifyMfa(vm.verifyCode);
        }

    }
})();
