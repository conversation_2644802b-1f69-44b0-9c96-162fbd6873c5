(function () {
    "use strict";

    angular.module("app.viewable").controller("UploadPartDataController", UploadPartDataController);

    UploadPartDataController.$inject = ["$uibModalInstance", "model", "uploadPartService", "headerBannerService", "$scope", "$translate"];

    function UploadPartDataController($uibModalInstance, model, uploadPartService, headerBannerService, $scope, $translate) {
        var vm = this;
        vm.uploadDetails = uploadDetails;
        vm.fileChanged = fileChanged;

        vm.cancel = $uibModalInstance.dismiss;
        vm.loading = false;
        vm.isHeaderListLoaded = false;
        vm.validationErrors = false;
        //vm.partVariables = ["Name", "Description", "Part Number"];
        vm.filename = "";
        vm.model = model;

        var SELECT_CSV, ADD_PART_DATA, FAILED_UPLOAD;
        $translate(["UPLOAD_PART.SELECT_CSV", "UPLOAD_PART.ADD_PART_DATA", "UPLOAD_PART.FAILED_UPLOAD"]).then(function (resp) {
            SELECT_CSV = resp["UPLOAD_PART.SELECT_CSV"];
            ADD_PART_DATA = resp["UPLOAD_PART.ADD_PART_DATA"];
            FAILED_UPLOAD = resp["UPLOAD_PART.FAILED_UPLOAD"];
        });

        function fileChanged(obj) {
            vm.loading = true;

            var elem = obj.target || obj.srcElement;
            if (elem.files.length > 0) {
                vm.file = elem.files[0];

                vm.isFileSelected = true;
                $scope.$digest();

                var ext = vm.file.name.substring(vm.file.name.lastIndexOf(".") + 1);
                vm.filename = vm.file.name;
                if (ext.toUpperCase() === "CSV") {
                    uploadPartService.getHeaderDetails(vm.file).then(csvHeaderSuccess, csvHeaderFailed);
                } else {
                    //Not a CSV file.
                    headerBannerService.setNotification("ERROR", SELECT_CSV, 5000);
                }
                vm.loading = false;
            }
        }

        function csvHeaderSuccess(response) {
            vm.headers = response.data;
            vm.isHeaderListLoaded = true;
        }

        function csvHeaderFailed(error) {
            console.log(error);
        }

        function uploadDetails() {
            vm.loading = true;

            if (!vm.nameColumn || !vm.objectIdColumn) {
                vm.validationErrors = true;
                vm.loading = false;
            } else {
                vm.validationErrors = false;
            }

            if (!vm.validationErrors) {
                var data = createRequestData();
                uploadPartService.uploadPartDetailsFile(vm.model.modelId, vm.file, data).then(detailsUploadSuccess, detailsUploadFailed);
            }
        }

        function detailsUploadSuccess(response) {
            vm.loading = false;
            $uibModalInstance.close();
            headerBannerService.setNotification("SUCCESS", ADD_PART_DATA, 5000);
        }

        function detailsUploadFailed(error) {
            headerBannerService.setNotification("ERROR", FAILED_UPLOAD, 5000);
        }

        function createRequestData() {
            var partData = {
                nameColumn: vm.headers.indexOf(vm.nameColumn),
                objectIdColumn: vm.headers.indexOf(vm.objectIdColumn),
                descriptionColumn: vm.headers.indexOf(vm.descriptionColumn),
                numberColumn: vm.headers.indexOf(vm.numberColumn),
                updateDescription: !!vm.updateDescription,
                updateNumber: !!vm.updateNumber
            };
            return partData;
        }

        // vm.isAnyDropdownSelected = function () {
        //     if (vm.nameColumn) {
        //         return vm.descriptionColumn || vm.numberColumn || vm.sparePartIdentifier;
        //     }
        //     return false;
        // };
    }
})();
