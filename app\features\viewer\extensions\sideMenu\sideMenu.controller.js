(function () {
    "use strict";

    angular.module("app.viewer").controller("SideMenuController", SideMenuController);

    SideMenuController.$inject = ["$rootScope", "$scope"];

    function SideMenuController($rootScope, $scope) {
        var vm = this;

        vm.isMinimized = window.window.innerWidth <= 680;
        vm.status = {
            isModelBrowserOpen: true,
            isKitBuilderOpen: false,
            isOptionsSetOpen: false,
            isLinkedPartOpen: false,
            isPurchasableAssembliesOpen: false,
            isNonModeledPartsOpen: false,
            isViewerSettingsOpen: false,
        };

        vm.minimizeSideMenu = minimizeSideMenu;
        vm.maximizeSideMenu = maximizeSideMenu;
        vm.handleToggleLockingClick = handleToggleLockingClick;

        initialize();

        function initialize() {}

        function minimizeSideMenu() {
            vm.isMinimized = true;
            $rootScope.$broadcast("side-menu-minimized");
        }

        function maximizeSideMenu() {
            vm.isMinimized = false;
            $rootScope.$broadcast("side-menu-maximized");
        }

        function handleToggleLockingClick($event) {
            $event.stopPropagation();
            $event.preventDefault();
            $scope.lockState = !$scope.lockState; // Toggle the lock state
            $scope.$broadcast("toggle-all-locked-nodes");
        }

        $scope.$on("bulk-lock-state-updated", function (event, allLocked) {
            $scope.lockState = allLocked;
        });
    }
})();
