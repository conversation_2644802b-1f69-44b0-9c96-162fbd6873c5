<section class="body-content">
  <section class="d-flex justify-content-between flex-wrap">
    <div>
      <h1 translate>CREATE_ORDER.CREATE_ENQUIRY</h1>
    </div>

    <div class="mobileBtnFullWidth cadGap">
      <button
        type="button"
        class="btn primary-outline"
        ng-click="createCtrl.addComment(createCtrl.comment)"
      >
        <span ng-hide="createCtrl.comment" translate>ORDER.ADD_COMMENT</span>
        <span ng-show="createCtrl.comment" translate>ORDER.EDIT_COMMENT</span>
      </button>
      <button
        class="btn primary-outline"
        ng-show="createCtrl.showPartSearch || createCtrl.onBehalfOf"
        ng-click="createCtrl.openPartSearchModal()"
      >
        <i class="fa fa-search"></i>
        {{"ORDER.PART_SEARCH" | translate}}
      </button>
      <button
        class="btn primary"
        ng-show="createCtrl.submitting && !createCtrl.ifPlaceOrderIsClicked"
      >
        <span
          class="spinner-border text-light"
          role="status"
          aria-hidden="true"
        ></span>
        {{'CREATE_ORDER.SUBMITTING' | translate}}
      </button>
      <button
        class="btn primary-outline"
        ng-hide="createCtrl.submitting && !createCtrl.ifPlaceOrderIsClicked || !createCtrl.ifBasketContainsItems"
        ng-disabled="createCtrl.submitDisabled"
        ng-click="createCtrl.submitEnquiry()"
      >
        <span
          ng-show="!createCtrl.enquiryPOToLiveEnabled || !createCtrl.areAllPricesReturned()"
          translate
          >CREATE_ORDER.SUBMIT_ENQUIRY</span
        >
        <span
          ng-show="createCtrl.enquiryPOToLiveEnabled && createCtrl.areAllPricesReturned()"
          translate
          >CREATE_ORDER.REQUEST_SHIPPING</span
        >
      </button>
      <button
        class="btn primary"
        ng-hide="createCtrl.submitting || !createCtrl.showCheckoutBtn || !createCtrl.isFarmerPaymentsEnabled || !createCtrl.ifBasketContainsItems"
        ng-click="createCtrl.proceedToCheckout()"
      >
        <span translate>CREATE_ORDER.PROCEED_TO_CHECKOUT</span>
      </button>
      <button
        class="btn primary-outline"
        ng-show="(createCtrl.enquiryPOToLiveEnabled && createCtrl.areAllPricesReturned() && createCtrl.isStockAvailable() && !createCtrl.submitting && !createCtrl.isFarmerPaymentsEnabled && createCtrl.isSupreme || createCtrl.overrideForDirectOrders) && createCtrl.ifBasketContainsItems && !(createCtrl.submitting && createCtrl.ifPlaceOrderIsClicked)"
        ng-disabled="createCtrl.submitDisabled"
        ng-click="createCtrl.placeOrder()"
        translate
      >
        CREATE_ORDER.PLACE_ORDER
      </button>
      <button
        class="btn primary-outline"
        ng-show="createCtrl.enquiryPOToLiveEnabled && createCtrl.areAllPricesReturned() && !createCtrl.submitting && !createCtrl.isFarmerPaymentsEnabled && !createCtrl.isSupreme && createCtrl.ifBasketContainsItems && !(createCtrl.submitting && createCtrl.ifPlaceOrderIsClicked)"
        ng-disabled="createCtrl.submitDisabled"
        ng-click="createCtrl.placeOrder()"
        translate
      >
        CREATE_ORDER.PLACE_ORDER
      </button>
      <button
        class="btn primary"
        ng-show="createCtrl.submitting && createCtrl.ifPlaceOrderIsClicked"
      >
        <span
          class="spinner-border text-light"
          role="status"
          aria-hidden="true"
        ></span>
        {{'CREATE_ORDER.SUBMITTING' | translate}}
      </button>
    </div>
  </section>

  <section>
    <p ng-if="createCtrl.showDisclaimer" class="my-4">
      <span class="font-weight-bold">{{"ORDER.PLEASE_NOTE" | translate}}</span>
      {{"ORDER.DISCLAIMER" | translate}}
    </p>
  </section>

  <section class="mt-4 mt-md-0">
    <form name="detailsForm" novalidate>
      <h2><span translate>CREATE_ORDER.ENQUIRY_DETAILS</span></h2>
      <div class="row">
        <div class="col-12 col-md-6 col-lg customDivContainer">
          <div
            class="customDivStyling d-flex flex-column justify-content-between"
          >
            <div class="addressContainer">
              <h3 translate>CREATE_ORDER.DELIVERY_ADDRESS</h3>
              <select
                ng-model="createCtrl.deliveryAddress"
                required
                ng-class="{'has-error': detailsForm.$submitted && detailsForm.deliveryAddress.$invalid}"
                name="deliveryAddress"
              >
                <option value="" disabled="" translate>
                  CREATE_ORDER.SELECT_DELIVERY_ADDRESS
                </option>
                <option
                  ng-repeat="address in createCtrl.addresses"
                  value="{{address}}"
                >
                  {{address.companyName}}{{address.companyName ? ", " :
                  ""}}{{address.addressLine1}}, {{address.city}},
                  {{address.state}}, {{address.postcode}}, {{address.country}}
                </option>
              </select>
              <h3 translate>CREATE_ORDER.BILLING_ADDRESS</h3>
              <select
                ng-model="createCtrl.billingAddress"
                required
                ng-class="{'has-error': detailsForm.$submitted && detailsForm.billingAddress.$invalid }"
                name="billingAddress"
              >
                <option value="" disabled="" translate>
                  CREATE_ORDER.SELECT_BILLING_ADDRESS
                </option>
                <option
                  ng-repeat="address in createCtrl.addresses"
                  value="{{address}}"
                >
                  {{address.companyName}}{{address.companyName ? ", " :
                  ""}}{{address.addressLine1}}, {{address.city}},
                  {{address.state}}, {{address.postcode}}, {{address.country}}
                </option>
              </select>
            </div>
            <div class="d-flex">
              <button
                type="button"
                class="btn secondary"
                ng-if="!createCtrl.isFarmerPaymentsEnabled && createCtrl.isAddressCreationEnabled"
                ng-click="createCtrl.addNewAddress()"
                translate
              >
                CREATE_ORDER.ADD_NEW_ADDRESS
              </button>
            </div>
          </div>
        </div>
        <div class="col-12 col-md-6 col-lg customDivContainer">
          <div
            class="customDivStyling d-flex flex-column justify-content-between"
          >
            <div class="nameContainer">
              <h3>
                <i class="fa fa-truck"></i>
                {{'CREATE_ORDER.DELIVERY_CONTACT_NAME' | translate}}
              </h3>
              <select
                ng-model="createCtrl.deliveryName"
                required
                ng-class="{'has-error': detailsForm.$submitted && detailsForm.deliveryName.$invalid}"
                name="deliveryName"
                ng-change="createCtrl.updateDeliveryId()"
              >
                <option value="" disabled="" translate>
                  CREATE_ORDER.DELIVERY_CONTACT_NAME
                </option>
                <option
                  ng-repeat="contact in createCtrl.deliveryContacts"
                  value="{{contact.name}}"
                  data-id="{{contact.id}}"
                >
                  {{contact.name}}
                </option>
              </select>
              <h3>
                <i class="fa fa-user"></i> {{'CREATE_ORDER.ORDER_CONTACT_NAME' |
                translate}}
              </h3>
              <select
                ng-model="createCtrl.contactName"
                required
                ng-class="{'has-error': detailsForm.$submitted && detailsForm.contactName.$invalid}"
                name="contactName"
                ng-change="createCtrl.updateContactId()"
              >
                <option value="" disabled="" translate>
                  CREATE_ORDER.ORDER_CONTACT_NAME
                </option>
                <option
                  ng-repeat="contact in createCtrl.orderContacts"
                  value="{{contact.name}}"
                  data-id="{{contact.id}}"
                >
                  {{contact.name}}
                </option>
              </select>
            </div>
            <div class="d-flex">
              <button
                type="button"
                class="btn secondary"
                ng-click="createCtrl.addNewName()" ng-if="createCtrl.isAddressCreationEnabled"
                translate
              >
                CREATE_ORDER.ADD_NEW_NAME
              </button>
            </div>
          </div>
        </div>
        <div class="col-12 col-md-6 col-lg customDivContainer">
          <div
            class="customDivStyling d-flex flex-column justify-content-between"
          >
            <div class="numberContainer">
              <h3>
                <i class="fa fa-truck"></i> {{'CREATE_ORDER.DELIVERY_NUMBER' |
                translate}}
              </h3>
              <select
                ng-model="createCtrl.deliveryNumber"
                required
                ng-class="{'has-error': detailsForm.$submitted && detailsForm.deliveryNumber.$invalid}"
                name="deliveryNumber"
              >
                <option value="" disabled="" translate>
                  CREATE_ORDER.DELIVERY_NUMBER
                </option>
                <option
                  ng-repeat="number in createCtrl.numbers"
                  value="{{number.contactNumber}}"
                >
                  {{number.contactNumber}}
                </option>
              </select>
              <h3>
                <i class="fa fa-phone"></i> {{'CREATE_ORDER.CONTACT_NUMBER' |
                translate}}
              </h3>
              <select
                ng-model="createCtrl.contactNumber"
                required
                ng-class="{'has-error': detailsForm.$submitted && detailsForm.contactNumber.$invalid}"
                name="contactNumber"
              >
                <option value="" disabled="" translate>
                  CREATE_ORDER.CONTACT_NUMBER
                </option>
                <option
                  ng-repeat="number in createCtrl.numbers"
                  value="{{number.contactNumber}}"
                >
                  {{number.contactNumber}}
                </option>
              </select>
            </div>
            <div class="d-flex">
              <button
                type="button"
                class="btn secondary"
                ng-click="createCtrl.addNewNumber()"
                translate
              >
                CREATE_ORDER.ADD_NEW_NUMBER
              </button>
            </div>
          </div>
        </div>
        <div
          class="col-12 col-md-6 col-lg customDivContainer"
          ng-if="createCtrl.isSupreme"
        >
          <div
            class="customDivStyling d-flex flex-column justify-content-between"
          >
            <div>
              <h3>
                {{'CREATE_ORDER.COURIER_DETAILS' | translate}}
                <span
                  type="button"
                  data-toggle="tooltip"
                  data-placement="top"
                  title="{{'CREATE_ORDER.PCD_TOOL_TIP' | translate}}"
                >
                  <i class="fa fa-info-circle cadBlue"></i>
                </span>
              </h3>

              <div
                class="border-top border-bottom d-flex my-2 py-3 flex-column cadGap overflow-auto"
                style="height: 150px"
              >
                <!-- Loop through shippingRequirements -->
                <div
                  class="form-check"
                  ng-repeat="requirement in createCtrl.shippingRequirements"
                >
                  <label class="flex flex-nowrap orm-check-label cursorPointer">
                    <input
                      type="radio"
                      class="position-relative cursorPointer mb-0 mt-0 form-check-input"
                      name="optradio"
                      ng-value="requirement"
                      ng-model="createCtrl.selectedShippingRequirementId"
                      ng-change="createCtrl.setSelectedShippingRequirementId(requirement.shippingRequirementId)"
                    />
                    <span class="cadGap">
                      <span translate> CREATE_ORDER.SHIP_VIA </span>
                      <span class="font-weight-bold text-uppercase"
                        >{{requirement.preferredCourier}}</span
                      >
                      <span translate>CREATE_ORDER.AC_NUMBER</span>
                      <span>{{requirement.courierNumber}}</span>
                    </span>
                    <button
                      class="btn secondary small danger ml-auto mr-2"
                      ng-click="createCtrl.deleteShippingRequirements(requirement)"
                    >
                      <i class="fa fa-trash-o"></i>
                    </button>
                  </label>
                </div>
              </div>

              <div class="input-group text-right">
                <input
                  class="inp-chBox"
                  id="chBox"
                  type="checkbox"
                  ng-model="createCtrl.shippingRequirementExpressDelivery"
                  style="display: none"
                />
                <label class="chBox" for="chBox"
                  ><span>
                    <svg width="12px" height="10px" viewbox="0 0 12 10">
                      <polyline
                        points="1.5 6 4.5 9 10.5 1"
                      ></polyline></svg></span
                  ><span translate>CREATE_ORDER.EXPRESS_DELIVERY</span></label
                >
              </div>
            </div>

            <div class="d-flex">
              <button
                type="button"
                class="btn secondary"
                ng-click="createCtrl.addShippingRequirements()"
                translate
              >
                CREATE_ORDER.ADD_NEW_DETAILS
              </button>
            </div>
          </div>
        </div>
        <div
          class="col-12 col-md-6 col-lg-3 customDivContainer"
          ng-if="!createCtrl.isSubEntityPaymentsEnabled"
        >
          <div class="customDivStyling">
            <h3 translate>CREATE_ORDER.REQUESTED_DELIVERY_DATE</h3>
            <input
              type="text"
              id="datepicker"
              autocomplete="off"
              ng-model="createCtrl.requestedDeliveryDate"
              placeholder="dd/mm/yyyy"
              required
              ng-class="{'has-error': detailsForm.$submitted && detailsForm.requestedDeliveryDate.$invalid }"
              name="requestedDeliveryDate"
            />
            <div
              ng-if="createCtrl.enquiryPurchaseOrder && createCtrl.showPurchaseOrderInput()"
            >
              <h3 translate>CREATE_ORDER.PURCHASE_ORDER_NUMBER</h3>
              <input
                type="text"
                ng-model="createCtrl.purchaseOrder"
                placeholder="{{'CREATE_ORDER.PURCHASE_ORDER_NUMBER' | translate}}"
                ng-class="{'has-error': detailsForm.$submitted && detailsForm.purchaseOrder.$invalid && !detailsForm.purchaseOrder.$pristine }"
                name="purchaseOrder"
              />
            </div>
            <div ng-if="createCtrl.isRequiredSerialNumber">
              <p class="mb-2" translate>
                CREATE_ORDER.SERIAL_STOCK_DESCRIPTION
              </p>
              <h3 translate>CREATE_ORDER.SERIAL_NUMBER</h3>
              <input
                type="text"
                ng-disabled="createCtrl.stockOrderSelected"
                placeholder="{{'CREATE_ORDER.SERIAL_NUMBER' | translate}}"
                ng-model="createCtrl.serialNumber"
                name="serialNumber"
                ng-required="!createCtrl.stockOrderSelected"
                ng-class="{'has-error': detailsForm.$submitted && !createCtrl.stockOrderSelected && !detailsForm.serialNumber.$valid}"
              />
              <div class="d-flex stockorderContainer">
                <h3 class="pr-4" translate>CREATE_ORDER.STOCK_ORDER</h3>
                <input
                  type="checkbox"
                  ng-model="createCtrl.stockOrder"
                  ng-change="createCtrl.toggleStockOrder()"
                  class="checkbox"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </section>

  <div ng-include="'features/orders/create/customerKitsTable.html'"></div>

  <section>
    <div>
      <div class="customDivStyling">
        <h2><span translate>ORDER.PARTS</span></h2>
        <div class="d-flex cadGap flex-wrap">
          <p translate>CREATE_ORDER.SELECTED_FROM_INTERACTIVE</p>
          <p translate>CREATE_ORDER.EX_WORKS_DISCLAIMER</p>
        </div>

        <table class="table table-bordered equal-width">
          <thead>
            <tr>
              <th class="col-md-3 col-12" translate>ORDER.PARTS</th>
              <th class="col-md-1 col-12" translate>ORDER.QUANTITY</th>
              <th
                ng-if="!createCtrl.hidePrice && createCtrl.areAnyPricesReturned()"
                class="width-10"
                translate
              >
                ORDER.ITEM_PRICE
              </th>
              <th ng-if="!createCtrl.hidePrice && createCtrl.defaultDiscount && createCtrl.areAnyPricesReturned()" translate>
                ORDER.DISCOUNTED_PRICE
              </th>
              <th
                ng-if="!createCtrl.hidePrice && createCtrl.areAnyPricesReturned()"
                class="width-10"
                translate
              >
                ORDER.PRICE
              </th>
              <th
                ng-if="createCtrl.isPreviewStockLevelEnabled && !createCtrl.isDealerPlusCustomer || createCtrl.isStockWarehousesEnabled && !createCtrl.isDealerPlusCustomer"
                class="width-10"
                translate
              >
                ORDER.STOCK
              </th>
              <th translate>GENERAL.ACTIONS</th>
              <!-- linked tech doc-->
            </tr>
          </thead>
          <tbody>
            <tr
              ng-repeat="item in createCtrl.basket | filter: {kitId: '!'}"
              ng-class="item.quantity > 0 ? '' : 'strike-through'"
            >
              <td data-label="{{'ORDER.PART_DETAILS' | translate}}">
                <p>
                  <strong><span>{{item.partNumber}}</span> </strong>
                  <span ng-if="item.alternatePartNumber"> - ({{item.alternatePartNumber}})</span>
                  <span ng-if="item.description || item.partDescription"> - {{item.description ? item.description : item.partDescription}}</span>
                </p>
                <p
                  ng-if="createCtrl.isPartInformationAvailable(item).showMachineName || createCtrl.isPartInformationAvailable(item).showModelName"
                >
                  {{'CREATE_ORDER.IDENTIFIED_FROM' | translate}} {{item.machineName}}
                  <span
                    ng-if="createCtrl.isPartInformationAvailable(item).showModelName"
                    >-</span
                  >
                  {{item.modelName}}
                </p>
              </td>

              <td data-label="{{'ORDER.QUANTITY' | translate}}">
                <input
                  ng-model="item.quantity"
                  ng-change="createCtrl.partUpdated(item)"
                  type="number"
                  min="0"
                  ng-model-options="{debounce: 500}"
                />
              </td>
              <td
                class="disableWordBreak"
                data-label="{{'ORDER.ITEM_PRICE' | translate}}"
                ng-if="!createCtrl.hidePrice && createCtrl.areAnyPricesReturned() && item.price"
              >
                {{item.price | currency:createCtrl.defaultCurrency.symbol:2}}
              </td>
              <td
                class="disableWordBreak"
                data-label="{{'ORDER.ITEM_PRICE' | translate}}"
                ng-if="!createCtrl.hidePrice && createCtrl.areAnyPricesReturned() && !item.price"
              >
                -
              </td>

              <td ng-if="!createCtrl.hidePrice && createCtrl.defaultDiscount && createCtrl.areAnyPricesReturned()" class="disableWordBreak"
                data-label="{{'ORDER.DISCOUNTED_PRICE' | translate}}">
                {{ createCtrl.calculateDiscountedPrice(item) ? (createCtrl.calculateDiscountedPrice(item) |
                currency:createCtrl.defaultCurrency.symbol:2) : '-' }}
              </td>

              <td
                data-label="{{'ORDER.PRICE' | translate}}"
                ng-if="!createCtrl.hidePrice && createCtrl.areAnyPricesReturned() && item.price"
              >
                {{item.totalPrice |
                currency:createCtrl.defaultCurrency.symbol:2}}
              </td>
              <td
                data-label="{{'ORDER.PRICE' | translate}}"
                ng-if="!createCtrl.hidePrice && createCtrl.areAnyPricesReturned() && !item.price"
              >
                -
              </td>

              <td
                class="disableWordBreak"
                data-label="{{'ORDER.STOCK' | translate}}"
                ng-if="createCtrl.isPreviewStockLevelEnabled && !createCtrl.isDealerPlusCustomer"
              >
                <span
                  title="{{'ORDER.IN_STOCK' | translate}}"
                  tooltip-trigger="outsideClick"
                  uib-tooltip="{{'ORDER.IN_STOCK' | translate}}"
                  class="success-alert"
                  ng-if="item.stock >= 3"
                  ><i class="fas fa-layer-group text-success pointer"></i
                ></span>
                <span
                  title="{{'ORDER.LOW_STOCK' | translate}}"
                  tooltip-trigger="outsideClick"
                  uib-tooltip="{{'ORDER.LOW_STOCK' | translate}}"
                  class="warning-alert"
                  ng-if="item.stock < 3 && item.stock > 0 "
                  ><i class="fas fa-layer-group text-warning pointer"></i
                ></span>
                <span
                  title="{{'ORDER.STOCK_SUBJECT' | translate}}"
                  tooltip-trigger="outsideClick"
                  uib-tooltip="{{'ORDER.STOCK_SUBJECT' | translate}}"
                  class="warning-alert"
                  ng-if="item.stock === null || item.stock < 1"
                  ><i class="fas fa-layer-group text-danger pointer"></i
                ></span>
              </td>

              <td
                class="disableWordBreak"
                data-label="{{'ORDER.STOCK' | translate}}"
                ng-if="createCtrl.isStockWarehousesEnabled && !createCtrl.isDealerPlusCustomer"
              >
                <div class="dropdownWarehouse">
                  <button
                    class="dropdown-toggle d-flex align-items-center"
                    type="button"
                    id="dropdownMenuButton"
                    data-toggle="dropdown"
                    aria-haspopup="true"
                    aria-expanded="false"
                  >
                    <span class="mr-2"
                      >{{ item.selectedWarehouse || item.warehouseStock[1].name
                      }}</span
                    >
                    <span
                      class="pill-badge badge-success mr-2"
                      ng-if="createCtrl.isStockHigh(item.stock)"
                      >{{item.stock > 1000 ? '>999' : item.stock}}</span
                    >
                    <span
                      class="pill-badge badge-warning mr-2"
                      ng-if="createCtrl.isStockLow(item.stock)"
                      >{{item.stock}}</span
                    >
                    <span
                      class="pill-badge badge-danger mr-2"
                      ng-if="createCtrl.isStockEmptyOrNull(item.stock)"
                      >{{item.stock || item.stock === 0 ? item.stock : 0}}</span
                    >
                  </button>
                  <div
                    class="dropdown-menu"
                    aria-labelledby="dropdownMenuButton"
                  >
                    <a
                      class="dropdown-item d-flex align-items-center"
                      value="{{warehouse.warehouseId}}"
                      ng-repeat="warehouse in createCtrl.getUniqueWarehouses(item)"
                      ng-click="createCtrl.updateBasketWarehouse(item, warehouse)"
                    >
                      <span class="mr-2">{{warehouse.name}}</span>
                      <span
                        class="pill-badge badge-success mr-2"
                        ng-if="createCtrl.isStockHigh(warehouse.stock)"
                        >{{warehouse.stock > 1000 ? '>999' :
                        warehouse.stock}}</span
                      >
                      <span
                        class="pill-badge badge-warning mr-2"
                        ng-if="createCtrl.isStockLow(warehouse.stock)"
                        >{{warehouse.stock}}</span
                      >
                      <span
                        class="pill-badge badge-danger mr-2"
                        ng-if="createCtrl.isStockEmptyOrNull(warehouse.stock)"
                        >{{warehouse.stock || warehouse.stock === 0 ?
                        warehouse.stock : 0}}</span
                      >
                    </a>
                  </div>
                </div>
              </td>

              <td data-label="{{'GENERAL.ACTIONS' | translate}}">

                <span class="d-flex justify-content-center align-items-center cadGap">

                  <button
                    type="button" 
                    uib-tooltip="{{'ORDER.VIEW_PART_NOTE' | translate}}"
                    ng-click="createCtrl.viewPartNote(item.masterPartId)"
                    class="btn secondary partnotes"
                    ng-class="{'disableButton': !item.note.length > 0}"
                  >
                    <i class="fas fa-sticky-note fa-lg fa-fw"></i>
                  </button>

                  <button type="button" ng-class="{'disableButton': !item.hasLinkedTechDocs}" ng-disabled="!item.hasLinkedTechDocs"
                    uib-tooltip="{{'ORDER.VIEW_TECH_INFO' | translate}}" ng-click="createCtrl.viewLinkedTechDocs(item)"
                    class="btn secondary warning">
                    <i class="fa fa-file-pdf fa-lg fa-fw"></i>
                  </button>

                  <button
                    type="button"
                    uib-tooltip="{{'ORDER.COMMENTS_TOOLTIP' | translate}}"
                    ng-click="createCtrl.addPartComment(item.comment, item)"
                    class="btn secondary comments"
                  >
                    <i
                      ng-hide="item.comment"
                      class="far fa-comments fa-lg fa-fw"
                    ></i>
                    <i
                      ng-show="item.comment"
                      class="fa fa-comments fa-lg fa-fw"
                    ></i>
                  </button>

                  <button
                    type="button"
                    uib-tooltip="{{'ORDER.REMOVE' | translate}}"
                    ng-show="!item.archived"
                    class="btn secondary danger"
                    ng-click="createCtrl.removeItem(item)"
                  >
                    <i class="fa fa-trash-o fa-lg fa-fw"></i>
                  </button>
                </span>
              </td>
            </tr>

            <tr ng-if="!createCtrl.basket.length > 0">
              <td class="flex-start noPartsBG" colspan="10" translate>
                CREATE_ORDER.NO_IDENTIFIED_PARTS
              </td>
            </tr>
            <tr
              ng-if="!createCtrl.hidePrice && createCtrl.areAnyPricesReturned()"
            >
              <td
                colspan="10"
                style="text-align: right"
                ng-show="createCtrl.areAllPricesReturned()"
              >
                <span>
                  <strong class="pr-2" translate>CREATE_ORDER.EST_TOTAL</strong
                  >{{createCtrl.estimatedTotal |
                  currency:createCtrl.defaultCurrency.symbol:2}}
                </span>
              </td>
              <td
                colspan="10"
                style="text-align: right"
                class="flex-start"
                ng-hide="createCtrl.areAllPricesReturned()"
              >
                <strong class="pr-2" translate>CREATE_ORDER.EST_TOTAL</strong>
                TBC
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </section>
  <section ng-if="!createCtrl.isSubEntityPaymentsEnabled">
    <div>
      <form class="form" id="manualPartsForm" name="createCtrl.manualPartsForm">
        <div class="customDivStyling">
          <h2><span translate>CREATE_ORDER.MANUALLY_ADDED_PARTS</span></h2>
          <p translate>CREATE_ORDER.MANUAL_PARTS_DESC</p>
          <table class="table table-bordered">
            <thead>
              <tr>
                <th>
                  {{'ORDER.PART_NO' | translate}}
                  <span class="required-field"
                    >&ast;{{"CREATE_ORDER.REQUIRED" | translate}}</span
                  >
                </th>
                <th>
                  {{"ORDER.PART_DESC" | translate}}
                  <span class="required-field"
                    >&ast;{{"CREATE_ORDER.REQUIRED" | translate}}</span
                  >
                </th>
                <th>{{"ORDER.PRODUCT" | translate}}</th>
                <th>
                  {{"ORDER.QUANTITY" | translate}}
                  <span class="required-field"
                    >&ast;{{"CREATE_ORDER.REQUIRED" | translate}}</span
                  >
                </th>
                <th>
                  <span ng-show="createCtrl.manualPartsEditable"
                    >{{"ORDER.REMOVE" | translate}}</span
                  >
                </th>
              </tr>
            </thead>
            <tbody>
              <tr
                ng-hide="createCtrl.manualPartsEditable"
                ng-repeat="manualPart in createCtrl.savedAdditionalParts"
              >
                <td data-label="{{'ORDER.PART_NO' | translate}}">
                  {{manualPart.partNumber}}
                </td>
                <td data-label="{{'ORDER.PART_DESC' | translate}}">
                  {{manualPart.partDescription}}
                </td>
                <td data-label="{{'ORDER.PRODUCT' | translate}}">
                  {{manualPart.machineName}}
                </td>
                <td data-label="{{'ORDER.QUANTITY' | translate}}">
                  {{manualPart.quantity}}
                </td>
                <td class="">
                  <!--<button class="btn secondary small" ng-click="createCtrl.removeManualPart(manualPart)">
                                   <i class="fa fa-trash-o"></i>
                                   </button>-->
                </td>
              </tr>
              <tr
                ng-show="createCtrl.manualPartsEditable"
                ng-repeat="manualPart in createCtrl.savedAdditionalParts"
              >
                <td class="">
                  <input
                    maxlength="200"
                    placeholder="{{'GENERAL.PART_NUMBER' | translate}}"
                    class="w-100 mw-100"
                    type="text"
                    ng-model="manualPart.partNumber"
                    required
                    ng-class=""
                    name="partNumber"
                  />
                </td>
                <td class="">
                  <input
                    maxlength="200"
                    placeholder="{{'CREATE_ORDER.PART_DESCRIPTION_EXAMPLE' | translate}}"
                    class="w-100 mw-100"
                    type="text"
                    ng-model="manualPart.partDescription"
                    required
                    ng-class=""
                    name="partDescription"
                  />
                </td>
                <td class="">
                  <input
                    maxlength="80"
                    placeholder="{{'CREATE_ORDER.PRODUCT_NAME' | translate}}"
                    class="w-100 mw-100"
                    type="text"
                    ng-model="manualPart.machineName"
                    name="machineName"
                  />
                </td>
                <td class="">
                  <input
                    placeholder="{{'ORDER.QUANTITY' | translate}}"
                    class="w-100 mw-100"
                    ng-model="manualPart.quantity"
                    type="number"
                    min="0"
                    max="1000000"
                    ng-model-options=""
                  />
                </td>
                <td class="">
                  <button
                    class="btn secondary small danger"
                    uib-tooltip="{{'ORDER.REMOVE' | translate}}"
                    ng-click="createCtrl.removeManualPart(manualPart)"
                  >
                    <i class="fa fa-trash-o"></i>
                  </button>
                </td>
              </tr>
              <tr
                ng-hide="createCtrl.manualPartsEditable || createCtrl.savedAdditionalParts.length > 0"
              >
                <td class="flex-start noPartsBG" colspan="5" translate>
                  CREATE_ORDER.NO_MANUAL_PARTS
                </td>
              </tr>
            </tbody>
          </table>
          <div class="table-footer d-flex justify-content-between pt-4 px-4">
            <div>
              <a href="" class="light" ng-click="createCtrl.createManualPart()"
                ><i class="fa fa-plus"></i> {{"ORDER.ADD_NEW_ITEM" |
                translate}}</a
              >
            </div>
            <div ng-hide="createCtrl.manualPartsEditable">
              <button
                class="btn secondary"
                ng-click="createCtrl.editManualPart()"
                ng-disabled="!createCtrl.savedAdditionalParts.length > 0"
                translate
              >
                GENERAL.EDIT
              </button>
            </div>
            <div ng-show="createCtrl.manualPartsEditable">
              <button
                class="btn secondary"
                ng-click="createCtrl.cancelCreateManualParts()"
                translate
              >
                GENERAL.CANCEL
              </button>
              <button
                class="btn primary"
                type="submit"
                ng-click="createCtrl.saveManualParts()"
                translate
              >
                ORDER.ADD_TO_ORDER
              </button>
            </div>
          </div>
        </div>
      </form>
    </div>
  </section>
</section>
