<div class="non-modeled-parts">
  <div class="non-modeled-contents">

    <!-- Parts Table -->
    <div class="basket-box">
      <div class="table-responsive">
        <table class="table-no-responsive table-bordered bg-white mb-0">
          <thead>
            <tr>
              <th translate>BUY_PART.PART_NUMBER</th>
              <th translate>BUY_PART.PART_DESCRIPTION</th>
              <th translate>BUY_PART.STOCK</th>
              <th translate>BUY_PART.QTY</th>
            </tr>
          </thead>
          <tbody>

            <!-- Part Notes -->

        <div ng-if="buyPartCtrl.selectedPartNote.length" class="part-note m-3 py-2 px-3 cursor-pointer">
          <div class="d-flex justify-content-between" ng-click="buyPartCtrl.isNotesExpanded = !buyPartCtrl.isNotesExpanded">
            <div>
              <strong>{{ 'CUST_VIEWER.NOTES' | translate }}</strong>
              <span ng-if="!buyPartCtrl.isNotesExpanded">{{ buyPartCtrl.shortenNote(buyPartCtrl.selectedPartNote[0]) }}</span>
            </div>
            <i class="fa"
              ng-class="{'fa-chevron-down': !buyPartCtrl.isNotesExpanded, 'fa-chevron-up': buyPartCtrl.isNotesExpanded}"></i>
          </div>
          <div ng-click="buyPartCtrl.isNotesExpanded = !buyPartCtrl.isNotesExpanded" ng-if="buyPartCtrl.isNotesExpanded">
            <div ng-repeat="note in buyPartCtrl.selectedPartNote track by $index">
              <span ng-if="note.includes('*')">
                {{ note.replace('*', '') }}
              </span>
              <span ng-if="!note.includes('*')">
                {{ note }}
              </span>
            </div>
          </div>
        </div>

            <tr ng-repeat="part in buyPartCtrl.partsList track by $index">
              <td>{{part.partNumber}}</td>
              <td>{{buyPartCtrl.getPartDescription(part)}}</td>
              <td>
                <span ng-if="!buyPartCtrl.isStockWarehousesEnabled" 
                      class="stock-indicator"
                      ng-class="buyPartCtrl.getStockClasses(part.stock)"
                      uib-tooltip="{{ part.stockTooltip | translate }}"
                      tooltip-trigger="'outsideClick'">
                  <i class="fas fa-layer-group"></i>
                </span>
                <span ng-if="buyPartCtrl.isStockWarehousesEnabled"
                      class="stock-value bg-transparent"
                      ng-class="buyPartCtrl.getStockClasses(part.stock)"
                      uib-tooltip="{{ 'CUST_VIEWER.IN_STOCK' | translate }}"
                      tooltip-trigger="'outsideClick'">
                  {{ part.stock || 0 }}
                </span>
              </td>
              <td>
                <input ng-model="part.quantity" 
                       type="number" 
                       min="0"
                       ng-model-options="{debounce: 500}" 
                       class="quantity-box m-0">
              </td>
            </tr>

           
  
          </tbody>
        </table>

  </div>

</div>

 <!-- Buttons -->
 <div class="d-flex cadGap p-4">

  <button ng-if="buyPartCtrl.hasOrdersAccess" class="btn primary fixed-width-btn"
    ng-click="buyPartCtrl.addPartsToBasket()" ng-disabled="buyPartCtrl.isQuantityZero()">
    <div class="d-flex justify-content-center align-items-center w-100" ng-hide="buyPartCtrl.isAddBasketButtonClicked">
      <span>{{"BUY_PART.ADD_BASKET" | translate}}</span>
    </div>
    <div class="d-flex justify-content-center align-items-center check-icon-wrapper w-100"
      ng-show="buyPartCtrl.isAddBasketButtonClicked">
      <i class="fa fa-check" style="font-size: 1.4em!important;"></i>
    </div>
  </button>

</div>
