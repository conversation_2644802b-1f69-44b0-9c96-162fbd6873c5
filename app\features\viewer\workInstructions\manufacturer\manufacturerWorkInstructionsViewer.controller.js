(function () {
        'use strict';

        angular
            .module('app.viewer')
            .controller('ManufacturerWorkInstructionsController', ManufacturerWorkInstructionsController);

        ManufacturerWorkInstructionsController.$inject = ['viewerSettingsService', 'viewerService', '$q', '$uibModal',
            '$stateParams', '$state', '$scope', '$window', 'viewerHelperService', '$timeout', 'viewerVariablesService',
            '$rootScope', 'viewerBannerService', 'tokenService', 'userService', 'apiConstants', 'modelService', '$translate', 'workInstructionsService'];

        function ManufacturerWorkInstructionsController(viewerSettingsService, viewerService, $q, $uibModal, $stateParams, $state,
                                                        $scope, $window, viewerHelperService, $timeout, viewerVariablesService, $rootScope, viewerBannerService,
                                                        tokenService, userService, apiConstants, modelService, $translate, workInstructionsService) {

            var vm = this;
            var viewerApp;

            var options = viewerService.getAutodeskToken()
                .then(function (resp) {
                    options = resp;
                    options.useADP = false;
                    if ($stateParams.translateType === "SVF2") {
                        options.env = 'AutodeskProduction2';
                        options.api = 'streamingV2';
                    }
                    initialize();
                });

            var leafNodes = [];
            var priorSelection = [];
            var statesReturned = false;
            var viewerLoaded = false;
            var translateToolBtn;
            var rotateToolBtn;
            var isGhosted = false;
            var viewer = {};
            var isCtrlPressed = false;
            var selectionWindowActive = false;
            var customsSettingsAppliedFlag = false;
            var removedSnapshotIndex;

            vm.modelId = $stateParams.modelId;
            vm.model = {};
            vm.workInstructionsDetails = [];
            vm.selectedIndex = 0;
            vm.machineName = $stateParams.machineName;
            vm.viewableName = $stateParams.viewableName;
            vm.isExplodeSliderVisible = false;
            vm.viewerSettings = {};
            vm.selectedParts = [];
            vm.currentList = [];
            vm.partDetailsOpen = false;
            vm.isSideMenuOpen = window.window.innerWidth > 680;

            vm.moveToEnd = moveToEnd;
            vm.moveForward = moveForward;
            vm.moveBackward = moveBackward;
            vm.moveToStart = moveToStart;
            vm.saveSnapshot = saveSnapshot;
            vm.loadSnapshot = loadSnapshot;
            vm.editSnapshotName = editSnapshotName;
            vm.deleteSnapshot = deleteSnapshot;
            vm.overwriteSnapshot = overwriteSnapshot;

            vm.weldmentsUpdated = weldmentsUpdated;
            vm.backToViewables = backToViewables;
            vm.showSpinner = showSpinner;
            vm.hideSpinner = hideSpinner;
            vm.disableTranslate = disableTranslate;
            vm.deactivateExplodeTool = deactivateExplodeTool;
            vm.toggleSelectionWindow = toggleSelectionWindow;
            vm.deactivateWindowSelection = deactivateWindowSelection;


            var WENT_WRONG, RESET_TO_TOP, DELETE_SNAPSHOT, IF_YOU_CONFIRM, CHILDREN_DELETED, SELECT_PART_ASSOCIATED,
                NON_MODEL_SUCCESS, SELECT_LINK, OVERWRITE, IF_CONFIRM, WILL_BE_OVERWRITTEN, PART_EDIT_SUCCESS,
                PART_EDIT_FAIL;
            $translate(['GENERAL.WENT_WRONG', 'MANUFACTURER_VIEWER.RESET_TO_TOP', 'MANUFACTURER_VIEWER.DELETE_SNAPSHOT', 'MANUFACTURER_VIEWER.IF_YOU_CONFIRM', 'MANUFACTURER_VIEWER.CHILDREN_DELETED',
                'MANUFACTURER_VIEWER.SELECT_PART_ASSOCIATED', 'MANUFACTURER_VIEWER.NON_MODEL_SUCCESS', 'MANUFACTURER_VIEWER.SELECT_LINK', 'MANUFACTURER_VIEWER.OVERWRITE', 'MANUFACTURER_VIEWER.IF_CONFIRM',
                'MANUFACTURER_VIEWER.WILL_BE_OVERWRITTEN', 'MANUFACTURER_VIEWER.PART_EDIT_SUCCESS', 'MANUFACTURER_VIEWER.PART_EDIT_FAIL'])
                .then(function (resp) {
                    WENT_WRONG = resp["GENERAL.WENT_WRONG"];
                    RESET_TO_TOP = resp["MANUFACTURER_VIEWER.RESET_TO_TOP"];
                    DELETE_SNAPSHOT = resp["MANUFACTURER_VIEWER.DELETE_SNAPSHOT"];
                    IF_YOU_CONFIRM = resp["MANUFACTURER_VIEWER.IF_YOU_CONFIRM"];
                    CHILDREN_DELETED = resp["MANUFACTURER_VIEWER.CHILDREN_DELETED"];
                    SELECT_PART_ASSOCIATED = resp["MANUFACTURER_VIEWER.SELECT_PART_ASSOCIATED"];
                    NON_MODEL_SUCCESS = resp["MANUFACTURER_VIEWER.NON_MODEL_SUCCESS"];
                    SELECT_LINK = resp["MANUFACTURER_VIEWER.SELECT_LINK"];
                    OVERWRITE = resp["MANUFACTURER_VIEWER.OVERWRITE"];
                    IF_CONFIRM = resp["MANUFACTURER_VIEWER.IF_CONFIRM"];
                    WILL_BE_OVERWRITTEN = resp["MANUFACTURER_VIEWER.WILL_BE_OVERWRITTEN"];
                    PART_EDIT_SUCCESS = resp["MANUFACTURER_VIEWER.PART_EDIT_SUCCESS"];
                    PART_EDIT_FAIL = resp["MANUFACTURER_VIEWER.PART_EDIT_FAIL"];
                });

            viewerVariablesService.setModelId($stateParams.modelId);

            function getModel() {
                modelService.fetchModel(vm.modelId)
                    .then(getModelSuccess);
            }

            function getModelSuccess(response) {
                vm.model = response.data;
                leafNodes = JSON.parse(response.data.leafNodes) || [];
            }

            function getViewable() {
                //needs updated to point to correct viewable
                vm.viewableId = $stateParams.viewableId;
                viewerService.getWorkInstructionsViewableByViewableId(vm.viewableId)
                    .then(geViewableSuccess);
            }

            function initialize() {
                getModel();
                viewerHelperService.showSpinner();
                autodeskViewerInitialize();
                getViewable();
            }

            function autodeskViewerInitialize() {

                $scope.$on('$locationChangeStart', function (event) {
                    if (!viewerLoaded) {
                        event.preventDefault();
                        console.log("Viewable loading back navigation disabled");
                    } else {
                        console.log("Viewer Shutdown On Navigation");
                    }
                });

                Autodesk.Viewing.Initializer(options, function onInitialized() {
                    var config3d = {
                        extensions: ['CADShareExtension', 'Autodesk.NPR']
                    };
                    viewerApp = new Autodesk.Viewing.ViewingApplication('MyViewerDiv');
                    viewerApp.registerViewer(viewerApp.k3D, Autodesk.Viewing.GuiViewer3D, config3d);

                    var documentId = "urn:" + $stateParams.autodeskURN;
                    viewerApp.loadDocument(documentId, onDocumentLoadSuccess, loadFailure);
                });
            }

            function setLocalViewerSettings(data) {
                if (data) {
                    vm.viewerSettings.lineDrawingEnabled = data.lineDrawingEnabled;
                    vm.viewerSettings.edgingEnabled = data.edgingEnabled;
                    vm.viewerSettings.viewLocked = data.viewLocked;
                    $rootScope.$broadcast("viewer-settings-loaded", vm.viewerSettings);
                }
            }

            function geViewableSuccess(data) {
                setLocalViewerSettings(data);
                getWorkInstructionsDetails();
            }

            function onDocumentLoadSuccess() {
                var viewables = viewerApp.bubble.search({'type': 'geometry'});

                if (viewables.length === 0) {
                    console.error('Document contains no viewables.');
                    return;
                }

                viewerApp.selectItem(viewables[0].data);
                viewerApp.getCurrentViewer().addEventListener(Autodesk.Viewing.SELECTION_CHANGED_EVENT,
                    onSelectionChanged);
                viewerApp.getCurrentViewer().addEventListener(Autodesk.Viewing.GEOMETRY_LOADED_EVENT,
                    onViewerLoadedSuccess);
                viewerApp.getCurrentViewer().addEventListener(Autodesk.Viewing.OBJECT_TREE_CREATED_EVENT,
                    geometobjectTreeCreated);

                viewerHelperService.setViewerApp(viewerApp.getCurrentViewer());
                viewerApp.getCurrentViewer()._hotkeyManager.popHotkeys('Autodesk.Escape');
            }

            $scope.$on('model-tree-initialized', function (event, modelTree) {
                vm.modelTree = modelTree;
                viewerHelperService.setPartTreeTopId(vm.modelTree[0].objectId);
            });

            function geometobjectTreeCreated(evt) {
                //load the extension MySelectionWindow and call initialization
                viewerApp.getCurrentViewer().loadExtension('MySelectionWindow');
            }

            function loadFailure(viewerErrorCode) {
                console.error('onDocumentLoadFailure() - errorCode:' + viewerErrorCode);
                viewerHelperService.hideSpinner();
            }

            function applyDefaultSettings(viewer) {
                var bgColour = viewerHelperService.getBackgroundColour();
                viewer.setBackgroundColor(bgColour.r, bgColour.g, bgColour.b, bgColour.r, bgColour.g, bgColour.b);
                viewer.setTheme("light-theme");
                viewer.setEnvMapBackground(false);
                viewer.setGroundReflection(false);
                viewer.setGroundShadow(false);
                viewer.setOptimizeNavigation(false);
                viewer.hidePoints(true);
                viewer.hideLines(true);
                viewer.setProgressiveRendering(true);
                viewer.setQualityLevel(false, false);
                viewer.setGhosting(isGhosted);
                viewer.impl.selectionMaterialTop.opacity = 0;
            }

            function configureViewerAndToolbar(viewer) {
                applyDefaultSettings(viewer);

                var toolbar = viewer.toolbar;

                var navTools = toolbar.getControl('navTools');
                navTools.removeControl('toolbar-bimWalkTool');

                var modelTools = toolbar.getControl('modelTools');
                modelTools.removeControl('toolbar-measurementSubmenuTool');
                modelTools.removeControl('toolbar-explodeTool');

                toolbar.removeControl("settingsTools");

                translateToolBtn = new Autodesk.Viewing.UI.Button('translate-tool');
                translateToolBtn.onClick = function (e) {
                    txTool = _translateTool.getName();

                    if (viewer.toolController.getActiveTool().activeName === txTool) {
                        viewer.toolController.deactivateTool(txTool);
                        translateToolBtn.removeClass('active');
                        translateToolBtn.addClass('inactive');
                    } else {
                        deactivateWindowSelection();
                        deactivateExplodeTool();
                        deactivateRotateTool();
                        viewer.toolController.activateTool(txTool);
                        translateToolBtn.removeClass('inactive');
                        translateToolBtn.addClass('active');
                    }
                };
                translateToolBtn.addClass('translate-tool');
                translateToolBtn.setToolTip('Translate');

                // Add rotate tool button on viewer toolbar
                rotateToolBtn = new Autodesk.Viewing.UI.Button('rotate-tool');
                rotateToolBtn.onClick = function (e) {
                    rotateTool = _rotateTool.getName();
                    if (viewer.toolController.getActiveTool().activeName === rotateTool) {
                        viewer.toolController.deactivateTool(rotateTool);
                        rotateToolBtn.removeClass('active');
                        rotateToolBtn.addClass('inactive');
                    } else {
                        deactivateWindowSelection();
                        deactivateExplodeTool();
                        deactivateTranslateTool()
                        viewer.toolController.activateTool(rotateTool);
                        rotateToolBtn.removeClass('inactive');
                        rotateToolBtn.addClass('active');
                    }
                }
                rotateToolBtn.addClass('rotate-tool');
                rotateToolBtn.setToolTip('Rotate');

                var fragToolBtn = new Autodesk.Viewing.UI.Button('selection-window');
                fragToolBtn.onClick = selectionWindowClicked;
                fragToolBtn.addClass('selection-window');
                fragToolBtn.setToolTip('Selection Window');

                var explodeToolBtn = new Autodesk.Viewing.UI.Button('explode-tool');
                explodeToolBtn.onClick = function (e) {
                    toggleExplodePartVisibility();
                };
                explodeToolBtn.addClass('explode-tool');
                explodeToolBtn.setToolTip('Explode parts');

                var ghostToolBtn = new Autodesk.Viewing.UI.Button('ghosting-tool');
                ghostToolBtn.onClick = function (e) {
                    toggleGhosting();

                    if (isGhosted) {
                        ghostToolBtn.removeClass('inactive');
                        ghostToolBtn.addClass('active');
                    } else {
                        ghostToolBtn.removeClass('active');
                        ghostToolBtn.addClass('inactive');
                    }
                };
                ghostToolBtn.addClass('ghosting-tool');
                ghostToolBtn.setToolTip('Toggle ghosting on or off');

                var reverseViewBtn = new Autodesk.Viewing.UI.Button('reverse-view');
                reverseViewBtn.onClick = function () {
                    flip180()
                };
                reverseViewBtn.addClass('reverse-view');
                reverseViewBtn.setToolTip('Reverse angle');

                var subToolbar = new Autodesk.Viewing.UI.ControlGroup('cadshare-toolbar');
                subToolbar.addControl(explodeToolBtn);
                subToolbar.addControl(translateToolBtn);
                subToolbar.addControl(rotateToolBtn);
                subToolbar.addControl(fragToolBtn);
                /* subToolbar.addControl(nonModeledPartToolBtn);
                 subToolbar.addControl(partLinkToolBtn);*/
                subToolbar.addControl(ghostToolBtn);
                subToolbar.addControl(reverseViewBtn);
                toolbar.addControl(subToolbar, {index: 0});
            }

            function deactivateExplodeTool() {
                $timeout(function () {
                    vm.isExplodeSliderVisible = false;
                })
            }

            function selectionWindowClicked(e) {
                deactivateExplodeTool();
                deactivateTranslateTool();
                deactivateRotateTool();
                var fragToolBtn = viewer.toolbar.getControl('cadshare-toolbar').getControl('selection-window');
                if (selectionWindowActive) {
                    fragToolBtn.removeClass('active');
                    fragToolBtn.addClass('inactive');
                } else {
                    fragToolBtn.removeClass('inactive');
                    fragToolBtn.addClass('active');
                }
                toggleSelectionWindow();
            }

            function deactivateWindowSelection() {
                var fragToolBtn = viewer.toolbar.getControl('cadshare-toolbar').getControl('selection-window');
                selectionWindowActive = false;
                disableSelectionWindow();
                fragToolBtn.removeClass('active');
                fragToolBtn.addClass('inactive');
            }

            function deactivateExplodeTool() {
                vm.isExplodeSliderVisible = false;
                $scope.$apply();
            }

            function deactivateTranslateTool() {
                var translateToolBtn = viewer.toolbar.getControl('cadshare-toolbar').getControl('translate-tool');
                txTool = _translateTool.getName();
                if (viewer.toolController.getActiveTool().activeName === txTool) {
                    viewer.toolController.deactivateTool(txTool);
                }
                translateToolBtn.removeClass('active');
                translateToolBtn.addClass('inactive');
            }

            function deactivateRotateTool() {
                var rotateToolBtn = viewer.toolbar.getControl('cadshare-toolbar').getControl('rotate-tool');
                rotateTool = _rotateTool.getName();
                if (viewer.toolController.getActiveTool().activeName === rotateTool) {
                    viewer.toolController.deactivateTool(rotateTool);
                }
                rotateToolBtn.removeClass('active');
                rotateToolBtn.addClass('inactive');
            }

            function flip180() {
                var bbox = viewer.impl.getVisibleBounds(false, false);
                var pivot = bbox.center();
                var oldTarget = viewer.navigation.getTarget();
                var oldPosition = viewer.navigation.getPosition();

                var newPosition = {
                    x: oldPosition.x + 2.0 * (pivot.x - oldPosition.x),
                    y: oldPosition.y + 2.0 * (pivot.y - oldPosition.y),
                    z: oldPosition.z + 2.0 * (pivot.z - oldPosition.z)
                };

                var newTarget = {
                    x: oldTarget.x + 2.0 * (pivot.x - oldTarget.x),
                    y: oldTarget.y + 2.0 * (pivot.y - oldTarget.y),
                    z: oldTarget.z + 2.0 * (pivot.z - oldTarget.z)
                };

                viewer.navigation.setView(newPosition, newTarget);
            }

            function toggleExplodePartVisibility() {
                deactivateWindowSelection();
                deactivateTranslateTool();
                deactivateRotateTool();
                vm.isExplodeSliderVisible = !vm.isExplodeSliderVisible;
                $scope.$apply();
            }

            function toggleGhosting() {
                isGhosted = !isGhosted;
                viewer.setGhosting(isGhosted);
            }

            function backToViewables() {
                $state.go("productsModels", {
                    productId: $stateParams.productId,
                    machineName: $stateParams.machineName
                });
            }

            function onViewerLoadedSuccess(response) {
                viewer = response.target;
                viewerHelperService.buildCustomContextMenu(viewer);
                configureViewerAndToolbar(viewer);
                viewerHelperService.setIsTwoD(false);

                $(document).bind('keyup', onKeyUp);
                $(document).bind('keydown', onKeyDown);

                viewerLoaded = true;
                
                var cdeId = apiConstants.cdeId ? apiConstants.cdeId[0] : 0;
                console.log(apiConstants.cdeId[0]);
                if (userService.getManufacturerId() === cdeId) { 
                    var interval = setInterval(() => {
                        var matman = NOP_VIEWER.impl.matman();
                        if(matman !== undefined) {
                            clearInterval(interval);
                            // matman._materials["model:1|mat:5"].opacity = 1;
                            Object.keys(matman._materials).forEach(key => {
                                matman._materials[key].transparent = false;
                            });
                            NOP_VIEWER.impl.invalidate(true, false, true);
                        }
                    }, 500);
                }
                //End of CDE trsnparent part hotfix

                $rootScope.$broadcast('loading-complete');
            }

            $scope.$on('loading-complete', function () {
                if (viewerLoaded && statesReturned) {
                    if (viewerApp.getCurrentViewer().model.getData().instanceTree) {
                        if (vm.workInstructionsDetails.length > 0) {
                            loadSnapshot(0);
                        }
                        viewerHelperService.hideSpinner();
                    } else {
                        $timeout(function () {
                            console.log("looping");
                            $rootScope.$broadcast('loading-complete');
                        }, 1000);
                    }
                }
            });

            function getLockedParts() {
                var lockedArray = [];
                for (var i = 0; i < nodeIdsSetsExplodeAsSingleEntity.length; i++) {
                    lockedArray.push(nodeIdsSetsExplodeAsSingleEntity[i][0]);
                }
                return lockedArray;
            }

            function calculateSelectionIds(idArray, lockedArray) {
                var idsToSelect = [];
                for (var k = 0; k < idArray.length; k++) {
                    var dbId = idArray[k];

                    var parentId = dbId;
                    while (parentId !== vm.modelTree[0].objectId) {
                        parentId = viewerHelperService.getParentId(parentId);
                        if (lockedArray.indexOf(parentId) > -1) {
                            dbId = parentId;
                        }
                    }
                    if (idsToSelect.indexOf(dbId) === -1) {
                        idsToSelect.push(dbId);
                    }
                }
                return idsToSelect;
            }

            function getWeldmentId(dbId) {
                var lockedArray = getLockedParts();
                var parentId = dbId;
                while (parentId !== vm.modelTree[0].objectId) {
                    parentId = viewerHelperService.getParentId(parentId);
                    if (lockedArray.indexOf(parentId) > -1) {
                        dbId = parentId;
                    }
                }
                return dbId;
            }

            var DOING_AGG_EVENT = false;
            function onSelectionChanged(data) {
                if(DOING_AGG_EVENT){return true;}
                if (data.dbIdArray.length > 0) {

                    if (isCtrlPressed && _.difference(data.dbIdArray, priorSelection).length === 1) {
                        var newObjectId = _.difference(data.dbIdArray, priorSelection);
                        var weldmentId = getWeldmentId(newObjectId);
                        if (priorSelection.indexOf(weldmentId) >= 0) {
                            priorSelection.splice(priorSelection.indexOf(weldmentId), 1);
                            viewerHelperService.selectParts(priorSelection);
                            return true;
                        }
                    }

                    priorSelection = viewerHelperService.getSelectedParts();
                    var lockedArray = getLockedParts();
                    var idsToSelect = calculateSelectionIds(data.dbIdArray, lockedArray);
                    var visibleNodes = viewerVariablesService.getVisibleNodes();
                    var visibleIdsToSelect = _.intersection(visibleNodes, idsToSelect);


                    if (_.difference(priorSelection, idsToSelect).length > 0) {
                        viewerHelperService.selectParts(visibleIdsToSelect);
                    } else {
                        DOING_AGG_EVENT = true;
                        viewerApp.getCurrentViewer().clearSelection();
                        var selections = [];
                        var selection = {};
                        selection.model = data.model;
                        selection.dbIdArray = Array.isArray(visibleIdsToSelect) ? visibleIdsToSelect : [visibleIdsToSelect];
                        selections.push(selection);

                        viewerApp.getCurrentViewer().setAggregateSelection(selections.map(selection => ({
                            model: selection.model,
                            ids: selection.dbIdArray,
                            selectionType: Autodesk.Viewing.SelectionType.OVERLAYED})));

                        DOING_AGG_EVENT = false;
                        onValidatedSelectionChange(visibleIdsToSelect);
                    }

                } else {
                    if (viewerApp.getCurrentViewer().getSelection().length === 0) {
                        $rootScope.$broadcast("viewer-part-selected", []);
                    }
                }
                return true;
            }

            $scope.$on("viewer-part-selected", function (event, partViewerDetails) {
                if (partViewerDetails.length === 1) {
                    vm.selectedParts = [partViewerDetails[0].part];
                } else if (partViewerDetails.length === 0) {
                    vm.selectedParts = [];
                    vm.partDetailsOpen = false;
                } else if (partViewerDetails.length > 0) {
                    vm.selectedParts = partViewerDetails;
                    vm.partDetailsOpen = false;
                }
            });

            function shutDownViewer() {
                if (viewerApp && viewerApp.getCurrentViewer() != null) {
                    viewerApp.getCurrentViewer().removeEventListener(Autodesk.Viewing.SELECTION_CHANGED_EVENT,
                        onSelectionChanged);
                    viewerApp.getCurrentViewer().finish();
                }

                $(document).unbind('keyup');
                $(document).unbind('keydown');
            }

            $scope.$on('$destroy', function () {
                shutDownViewer();
            });

            function toggleSelectionWindow() {
                selectionWindowActive = !selectionWindowActive;
                refreshFragProxyLocation();

                //If active turn on the selection extension without S key press
                if (selectionWindowActive) {
                    activateSelectionWindow();
                } else {
                    //If inactive implement same functionality as pressing Q to prevent selection
                    disableSelectionWindow();
                }
            }

            function refreshFragProxyLocation() {
                calculateFragBounding(viewerApp.getCurrentViewer());
            }

            //called from cadshare_extension
            function weldmentsUpdated(weldmentArray) {
                var lockedItems = [];
                for (var x = 0; x < weldmentArray.length; x++) {
                    var nonArrayId = weldmentArray[x][0];
                    lockedItems.push(nonArrayId);

                }
                $rootScope.$broadcast('locked-model-tree-updated', lockedItems);
            }

            function disableTranslate() {
                var txTool = _translateTool.getName();
                viewer.toolController.deactivateTool(txTool);
                translateToolBtn.removeClass('translate-tool-active');
                translateToolBtn.removeClass('active');
                translateToolBtn.addClass('translate-tool');
            }

            function showSpinner() {
                viewerHelperService.showSpinner();
            }

            function hideSpinner() {
                viewerHelperService.hideSpinner();
            }

            $scope.$on("viewer-settings-changed", function (event, viewerSettings) {
                vm.viewerSettings = viewerSettings;
                viewerHelperService.applyCustomViewerSettings(viewer, vm.viewerSettings);
            });

            function onKeyUp(evt) {
                isCtrlPressed = evt.keyCode === 17 ? false : isCtrlPressed;
            }

            function onKeyDown(evt) {
                isCtrlPressed = (evt.keyCode === 17) ? true : isCtrlPressed;
            }

            $scope.$on('side-menu-minimized', function () {
                vm.isSideMenuOpen = false;
                $timeout(function () {
                    viewer.resize()
                });

            });

            $scope.$on('side-menu-maximized', function () {
                vm.isSideMenuOpen = true;
                $timeout(function () {
                    viewer.resize()
                });
            });


            function getWorkInstructionsDetails() {
                workInstructionsService.getWorkInstructionsDetails(vm.viewableId)
                    .then(getWorkInstructionsDetailsSuccess);
            }

            function getWorkInstructionsDetailsSuccess(resp) {
                vm.workInstructionsDetails = resp.data;
                vm.snapshots = resp.data;
                statesReturned = true;
                $rootScope.$broadcast('loading-complete');
            }

            function moveToStart() {
                vm.selectedIndex = 0;
                loadSnapshot(vm.selectedIndex);
            }

            function moveToEnd() {
                vm.selectedIndex = vm.workInstructionsDetails.length - 1;
                loadSnapshot(vm.selectedIndex);
            }

            function moveForward() {
                if (vm.selectedIndex < vm.workInstructionsDetails.length - 1) {
                    vm.selectedIndex++;
                }
                playSnapshots();
            }

            function moveBackward() {
                if (vm.selectedIndex >= 1) {
                    vm.selectedIndex--;
                }
                playSnapshots();
            }

            //function to animate snapshots
            function playSnapshots(isJump, jumpIndex, isInc) {
                var cadShareExt = viewer.getExtension("CADShareExtension");
                cadShareExt.setPlay(true);
                loadSnapshot(vm.selectedIndex);
            }

            function loadSnapshot(index) {
                vm.selectedIndex = index;
                var snapshot = vm.workInstructionsDetails[index];
                var jsonState = JSON.parse(snapshot.state)
                viewerApp.getCurrentViewer().restoreState(jsonState, true);
                viewerHelperService.isolateParts(snapshot.visibleDbIds);
            }

            function saveSnapshot() {
                var currentState = {};

                currentState.state = JSON.stringify(viewerApp.getCurrentViewer().getState());
                currentState.viewableId = vm.viewableId;

                currentState.stateId = viewerService.guid();
                currentState.visibleDbIds = JSON.stringify(viewerVariablesService.getVisibleNodes());
                currentState.stateName = currentState.stateId;
                currentState.notes = '';
                vm.workInstructionsDetails.push(currentState);

                createWorkInstructionsDetail(currentState)
                    .then(function (resp) {
                            currentState.id = resp.data;
                            hideSpinner();
                        }
                    );
            }

            function createWorkInstructionsDetail(currentState) {
                return workInstructionsService.createWorkInstructionsDetail(currentState.state, currentState.viewableId,
                    currentState.stateId, currentState.visibleDbIds, currentState.stateName, currentState.notes)
            }

            function editSnapshotName(index) {
                var name = vm.workInstructionsDetails[index].stateName ? vm.workInstructionsDetails[index].stateName : "";
                var notes = vm.workInstructionsDetails[index].notes ? vm.workInstructionsDetails[index].notes : "";
                var snapshotDetails = {name: name, notes: notes};
                var modalInstance = $uibModal.open({
                    templateUrl: 'features/viewer/extensions/editNameAndNotes/editNameAndNotesModal.html',
                    controller: 'EditNameAndNotesModalController as editNameAndNotesCtrl',
                    resolve: {
                        snapshotDetails: function () {
                            return snapshotDetails;
                        }
                    }
                });

                modalInstance.result.then(function (newSnapshotDetails) {
                    if (newSnapshotDetails.name !== '' && newSnapshotDetails.name !== undefined) {
                        vm.workInstructionsDetails[index].stateName = newSnapshotDetails.name;
                        vm.workInstructionsDetails[index].notes = newSnapshotDetails.notes ? newSnapshotDetails.notes : "";
                        updateWorkInstructionsDetail(vm.workInstructionsDetails[index]);
                    }
                });
            }

            function updateWorkInstructionsDetail(workInstructionsDetail) {
                return workInstructionsService.updateWorkInstructionsDetail(workInstructionsDetail.id, workInstructionsDetail);
            }

            function deleteSnapshot(index) {
                removedSnapshotIndex = index;

                var confirmObject = {
                    titleText: "Delete Work Instruction Step",
                    bodyText: "If you confirm then the work instruction step " + " \"" + vm.workInstructionsDetails[index].stateName
                        + "\" " + " will be deleted."
                };
                $uibModal.open({
                    templateUrl: 'features/shared/confirmationModal/confirmModal.html',
                    controller: 'ConfirmModalController',
                    controllerAs: 'confirmModalCtrl',
                    size: 'sm',
                    resolve: {
                        confirmObject: function () {
                            return confirmObject;
                        }
                    }
                }).result
                    .then(deleteConfirmed, doNothing);
            }

            function deleteConfirmed() {
                workInstructionsService.deleteWorkInstructionsDetail(vm.workInstructionsDetails[removedSnapshotIndex].id)
                vm.workInstructionsDetails.splice(removedSnapshotIndex, 1);
            }

            function doNothing() {
            }

            function overwriteSnapshot(index) {
                var snapshot = vm.workInstructionsDetails[index];
                vm.overwriteSnapshotIndex = index;
                var confirmObject = {
                    titleText: OVERWRITE,
                    bodyText: IF_CONFIRM + " \"" + snapshot.stateName + "\" " + WILL_BE_OVERWRITTEN
                };

                $uibModal.open({
                    templateUrl: 'features/shared/confirmationModal/confirmModal.html',
                    controller: 'ConfirmModalController',
                    controllerAs: 'confirmModalCtrl',
                    size: 'sm',
                    resolve: {
                        confirmObject: function () {
                            return confirmObject;
                        }
                    }
                }).result
                    .then(overwriteConfirmed, doNothing);
            }

            function overwriteConfirmed() {
                var snapshot = vm.workInstructionsDetails[index];
                var currentState = {
                    state: JSON.stringify(viewerApp.getCurrentViewer().getState()),
                    viewableId: vm.viewableId,
                    visibleDbIds: JSON.stringify(viewerVariablesService.getVisibleNodes()),
                    stateId: snapshot.stateId,
                    stateName: snapshot.stateName ? snapshot.stateName : "",
                    notes: snapshot.notes ? snapshot.notes : "",
                }

                snapshot.state = currentState.state;
                snapshot.viewableId = currentState.viewableId;
                snapshot.visibleDbIds = currentState.visibleDbIds;

                workInstructionsService.updateWorkInstructionsDetail(snapshot.id, currentState);
            }

        }
    }

)
();
