"use strict";

function _toConsumableArray(arr) {
    if (Array.isArray(arr)) {
        for (var i = 0, arr2 = Array(arr.length); i < arr.length; i++) {
            arr2[i] = arr[i];
        }
        return arr2;
    } else {
        return Array.from(arr);
    }
}

/////////////////////////////////////////////////////////////////
// SelectionWindow Viewer Extension
// By <PERSON><PERSON> Liang, Autodesk Inc, August 2018
//
/////////////////////////////////////////////////////////////////

//referece:
// https://forge.autodesk.com/blog/custom-window-selection-forge-viewer-part-iii


//bounding sphere of this model
var _boundingSphere = null;
var _boundingBoxInfo = [];

var _viewer = null;
//container DIV of the viewer
var _container = null;
//start point of select window
var _mouseStart = new THREE.Vector3(0, 0, -10);
var _startPoint = new THREE.Vector3(0, 0, -10);
//end point of select window
var _mouseEnd = new THREE.Vector3(0, 0, -10);
//is selecting window running
var _running = false;
//rectangle lines of select window
var _lineGeom = null;
var _rectGroup = null;
//material for rectangle lines of select window
var _materialLine = null;


//build boundingbox info of each fragments
function calculateFragBounding(_viewer) {
    _boundingSphere = null;
    _boundingBoxInfo = [];

    var model = _viewer.model;

    //get bounding sphere of  whole model
    _boundingSphere = model.getBoundingBox().getBoundingSphere();

    //fragments list array
    var fragList = model.getFragmentList();
    //boxes array
    var boxes = fragList.fragments.boxes;
    //map from frag to dbid
    var fragid2dbid = fragList.fragments.fragId2dbId;

    //build _boundingBoxInfo by the data of Viewer directly
    //might probably be a bit slow with large model..
    _boundingBoxInfo = [];
    var index = 0;

    for (var step = 0; step < fragid2dbid.length; step++) {

        var fragProxy = _viewer.impl.getFragmentProxy(model, step);

        fragProxy.getAnimTransform();
        ///---------------------------------------------------------
        //for (var index in fragPositionArray) {


        var offsetX = fragProxy.position.x;
        var offsetY = fragProxy.position.y;
        var offsetZ = fragProxy.position.z;

        ///---------------------------------------------------------

        var proxyBoxes = fragProxy.frags.fragments.boxes;

        index = step * 6;
        var thisBox = new THREE.Box3(
            new THREE.Vector3(proxyBoxes[index] + offsetX, proxyBoxes[index + 1] + offsetY, proxyBoxes[index + 2] + offsetZ),
            new THREE.Vector3(proxyBoxes[index + 3] + offsetX, proxyBoxes[index + 4] + offsetY, proxyBoxes[index + 5] + offsetZ)
        );

        _boundingBoxInfo.push({bbox: thisBox, dbId: fragid2dbid[step]});


    }
}


function activateSelectionWindow() {
    _container = _viewer.canvas.parentElement;

    //lock navigation to fix the camera
    _viewer.navigation.setIsLocked(true);
    _viewer.navigation.setLockSettings({
        "pan": false,
        "orbit": false,
        "roll": false,
        "fov": false,
        "gotoview": false
    });

    //start to monitor mouse down
    _container.addEventListener("mousedown", onMouseDown);

    //get current camera
    var canvas = _viewer.canvas;
    var canvasWidth = canvas.clientWidth;
    var canvasHeight = canvas.clientHeight;

    var camera = new THREE.OrthographicCamera(
        300,
        canvasWidth,
        60,
        canvasHeight,
        1,
        1000
    );


    //create overlay scene for selection window
    _viewer.impl.createOverlayScene(
        "selectionWindowOverlay",
        _materialLine,
        _materialLine,
        camera
    );


    var canvasBounds = viewer.canvas.getBoundingClientRect();
    var width = canvasBounds.width;
    var height = canvasBounds.height;
    var orthoCamera = createOrthoCamera(width, height);
    viewer.impl.createOverlayScene('selectionWindowOverlay', null, null, orthoCamera);
    //END OF OPY OF CREATING OVERLAY FROM SOFTCOPY
}

function createOrthoCamera(width, height) {
    return new THREE.OrthographicCamera(-width / 2, width / 2, height / 2, -height / 2, 0, 30);
}

function disableSelectionWindow() {

    //container DIV of the viewer
    var _container = _viewer.canvas.parentElement;

    //unlock current navigation
    _viewer.navigation.setIsLocked(false);
    _viewer.navigation.setLockSettings({
        "pan": true,
        "orbit": true,
        "roll": true,
        "fov": true,
        "gotoview": true
    });

    //remove mouse events
    _container.removeEventListener("mouseup", onMouseUp);
    _container.removeEventListener("mousemove", onMouseMove);

    _running = false;

    _container.removeEventListener("mousedown", onMouseDown);

    //remove the Overlay Scene
    _viewer.impl.removeOverlayScene("selectionWindowOverlay");
}

function onMouseDown(evt) {
    _viewer.clearSelection();

    var bounds = _viewer.canvas.getBoundingClientRect();
    screenX = evt.clientX - bounds.left;
    screenY = evt.clientY - bounds.top;
    _mouseStart.x = screenX;
    _mouseStart.y = screenY;
    var screenPoint = new THREE.Vector3(screenX, screenY, -10);
    var width = bounds.width;
    var height = bounds.height;
    _startPoint.x = screenPoint.x - (width / 2);
    _startPoint.y = -(screenPoint.y - (height / 2));

    _running = true;

    //build the rectangle lines of select window
    if (_rectGroup === null) {
        _lineGeom = new THREE.Geometry();

        _lineGeom.vertices.push(
            _startPoint.clone(),
            _startPoint.clone(),
            _startPoint.clone(),
            _startPoint.clone(),
            _startPoint.clone()
        );

        // add geom to group
        var line_mesh = new THREE.Line(_lineGeom, _materialLine, THREE.LineStrip);

        _rectGroup = new THREE.Group();
        _rectGroup.add(line_mesh);
    } else {
        _lineGeom.vertices[0] = _startPoint.clone();
        _lineGeom.vertices[1] = _startPoint.clone();
        _lineGeom.vertices[2] = _startPoint.clone();
        _lineGeom.vertices[3] = _startPoint.clone();
        _lineGeom.vertices[4] = _startPoint.clone();

        _lineGeom.verticesNeedUpdate = true;
    }

    _viewer.impl.addOverlay("selectionWindowOverlay", _rectGroup);
    _viewer.impl.invalidate(false, false, true);

    //start to monitor the mouse events
    _container.addEventListener("mouseup", onMouseUp);
    _container.addEventListener("mousemove", onMouseMove);
}

function onMouseMove(evt) {

    if (_running) {
        var bounds = _viewer.canvas.getBoundingClientRect();
        screenX = evt.clientX - bounds.left;
        screenY = evt.clientY - bounds.top;
        var screenPoint = new THREE.Vector3(screenX, screenY, -10);
        var width = bounds.width;
        var height = bounds.height;
        _mouseEnd.x = screenPoint.x - (width / 2);
        _mouseEnd.y = -(screenPoint.y - (height / 2));

        //update rectangle lines
        _lineGeom.vertices[1].x = _startPoint.x;
        _lineGeom.vertices[1].y = _mouseEnd.y;
        _lineGeom.vertices[2] = _mouseEnd.clone();
        _lineGeom.vertices[3].x = _mouseEnd.x;
        _lineGeom.vertices[3].y = _startPoint.y;
        _lineGeom.vertices[4] = _lineGeom.vertices[0];

        _lineGeom.verticesNeedUpdate = true;
        _viewer.impl.invalidate(false, false, true);
    }
}

function onMouseUp(evt) {
    //var viewport = _viewer.impl.clientToViewport(evt.clientX, evt.clientY);

    if (_running) {
        //get mouse points
        _mouseEnd.x = evt.clientX;
        _mouseEnd.y = evt.clientY;

        //remove the overlay of one time rectangle
        _viewer.impl.removeOverlay("selectionWindowOverlay", _rectGroup);
        _running = false;

        //remove mouse event
        _container.removeEventListener("mouseup", onMouseUp);
        _container.removeEventListener("mousemove", onMouseMove);

        var viewer_pos = _viewer.container.getBoundingClientRect();
        var isPartialIntersection = _mouseEnd.x > _mouseStart.x + viewer_pos.x;
        //get box within the area of select window, or partially intersected.
        var ids = compute({
                clientX: _mouseStart.x + viewer_pos.x,
                clientY: _mouseStart.y + viewer_pos.y
            },
            {
                clientX: _mouseEnd.x ,
                clientY: _mouseEnd.y
            },
            isPartialIntersection);
        // true:  partially intersected.  false: inside the area only

        //highlight the selected objects
        _viewer.select(ids);
        if(document.getElementById("workInstructionsManufacturerViewerId")){
            angular
                .element(document.getElementById("workInstructionsManufacturerViewerId"))
                .scope()
                .manufacturerWorkInstructionsCtrl.deactivateWindowSelection();
            _viewer.impl.invalidate(true, true, true);
        } else if(document.getElementById("manufacturerViewerId")){
            angular
                .element(document.getElementById("manufacturerViewerId"))
                .scope()
                .manufacturerViewerCtrl.deactivateWindowSelection();
            _viewer.impl.invalidate(true, true, true);
        }
    }
}

//prepare the range of select window and filter out those objects
function compute(pointer1, pointer2, partialSelect) {
    // build 4 rays to project the 4 corners
    // of the selection window

    var xMin = Math.min(pointer1.clientX, pointer2.clientX);
    var xMax = Math.max(pointer1.clientX, pointer2.clientX);

    var yMin = Math.min(pointer1.clientY, pointer2.clientY);
    var yMax = Math.max(pointer1.clientY, pointer2.clientY);

    var ray1 = pointerToRay({
        clientX: xMin,
        clientY: yMin
    });

    var ray2 = pointerToRay({
        clientX: xMax,
        clientY: yMin
    });

    var ray3 = pointerToRay({
        clientX: xMax,
        clientY: yMax
    });

    var ray4 = pointerToRay({
        clientX: xMin,
        clientY: yMax
    });

    // first we compute the top of the pyramid
    var top = new THREE.Vector3(0, 0, 0);

    top.add(ray1.origin);
    top.add(ray2.origin);
    top.add(ray3.origin);
    top.add(ray4.origin);

    top.multiplyScalar(0.25);

    // we use the bounding sphere to determine
    // the height of the pyramid
    var _boundingSphere2 = _boundingSphere,
        center = _boundingSphere2.center,
        radius = _boundingSphere2.radius;

    // compute distance from pyramid top to center
    // of bounding sphere

    var dist = new THREE.Vector3(
        top.x - center.x,
        top.y - center.y,
        top.z - center.z
    );

    // compute height of the pyramid:
    // to make sure we go far enough,
    // we add the radius of the bounding sphere

    var height = radius + dist.length();

    // compute the length of the side edges

    var angle = ray1.direction.angleTo(ray2.direction);

    var length = height / Math.cos(angle * 0.5);

    // compute bottom vertices

    var v1 = new THREE.Vector3(
        ray1.origin.x + ray1.direction.x * length,
        ray1.origin.y + ray1.direction.y * length,
        ray1.origin.z + ray1.direction.z * length
    );

    var v2 = new THREE.Vector3(
        ray2.origin.x + ray2.direction.x * length,
        ray2.origin.y + ray2.direction.y * length,
        ray2.origin.z + ray2.direction.z * length
    );

    var v3 = new THREE.Vector3(
        ray3.origin.x + ray3.direction.x * length,
        ray3.origin.y + ray3.direction.y * length,
        ray3.origin.z + ray3.direction.z * length
    );

    var v4 = new THREE.Vector3(
        ray4.origin.x + ray4.direction.x * length,
        ray4.origin.y + ray4.direction.y * length,
        ray4.origin.z + ray4.direction.z * length
    );

    // create planes

    var plane1 = new THREE.Plane();
    var plane2 = new THREE.Plane();
    var plane3 = new THREE.Plane();
    var plane4 = new THREE.Plane();
    var plane5 = new THREE.Plane();

    plane1.setFromCoplanarPoints(top, v1, v2);
    plane2.setFromCoplanarPoints(top, v2, v3);
    plane3.setFromCoplanarPoints(top, v3, v4);
    plane4.setFromCoplanarPoints(top, v4, v1);
    plane5.setFromCoplanarPoints(v3, v2, v1);

    var planes = [plane1, plane2, plane3, plane4, plane5];

    var vertices = [v1, v2, v3, v4, top];

    // filter all bounding boxes to determine
    // if inside, outside or intersect

    var result = filterBoundingBoxes(planes, vertices, partialSelect);

    // all inside bboxes need to be part of the selection

    var dbIdsInside = result.inside.map(function (bboxInfo) {
        return bboxInfo.dbId;
    });

    // if partialSelect = true
    // we need to return the intersect bboxes

    if (partialSelect) {
        var dbIdsIntersect = result.intersect.map(function (bboxInfo) {
            return bboxInfo.dbId;
        });

        return [].concat(
            _toConsumableArray(dbIdsInside),
            _toConsumableArray(dbIdsIntersect)
        );
    }

    return dbIdsInside;
}

//rays of the corners of select window
function pointerToRay(pointer) {
    var camera = _viewer.navigation.getCamera();
    var pointerVector = new THREE.Vector3();
    var rayCaster = new THREE.Raycaster();
    var pointerDir = new THREE.Vector3();
    var domElement = _viewer.canvas;

    var rect = domElement.getBoundingClientRect();

    var x = ((pointer.clientX - rect.left) / rect.width) * 2 - 1;
    var y = -((pointer.clientY - rect.top) / rect.height) * 2 + 1;

    if (camera.isPerspective) {
        pointerVector.set(x, y, 0.5);

        pointerVector.unproject(camera);

        rayCaster.set(
            camera.position,
            pointerVector.sub(camera.position).normalize()
        );
    } else {
        pointerVector.set(x, y, -15);

        pointerVector.unproject(camera);

        pointerDir.set(0, 0, -1);

        rayCaster.set(
            pointerVector,
            pointerDir.transformDirection(camera.matrixWorld)
        );
    }

    return rayCaster.ray;
}

//filter out those objects in the range of select window
function filterBoundingBoxes(planes, vertices, partialSelect) {
    var intersect = [];
    var outside = [];
    var inside = [];

    var triangles = [
        {a: vertices[0], b: vertices[1], c: vertices[2]},
        {a: vertices[0], b: vertices[2], c: vertices[3]},
        {a: vertices[1], b: vertices[0], c: vertices[4]},
        {a: vertices[2], b: vertices[1], c: vertices[4]},
        {a: vertices[3], b: vertices[2], c: vertices[4]},
        {a: vertices[0], b: vertices[3], c: vertices[4]}
    ];

    var _iteratorNormalCompletion = true;
    var _didIteratorError = false;
    var _iteratorError = undefined;

    try {
        for (
            var _iterator = _boundingBoxInfo[Symbol.iterator](), _step;
            !(_iteratorNormalCompletion = (_step = _iterator.next()).done);
            _iteratorNormalCompletion = true
        ) {
            var bboxInfo = _step.value;

            // if bounding box inside, then we can be sure
            // the mesh is inside too

            if (containsBox(planes, bboxInfo.bbox)) {
                inside.push(bboxInfo);
            } else if (partialSelect) {
                //reconstructed by using AABBCollision lib.
                if (boxIntersectVertex(bboxInfo.bbox, triangles))
                    intersect.push(bboxInfo);
                else outside.push(bboxInfo);
            } else {
                outside.push(bboxInfo);
            }
        }
    } catch (err) {
        _didIteratorError = true;
        _iteratorError = err;
    } finally {
        try {
            if (!_iteratorNormalCompletion && _iterator.return) {
                _iterator.return();
            }
        } finally {
            if (_didIteratorError) {
                throw _iteratorError;
            }
        }
    }

    return {
        intersect: intersect,
        outside: outside,
        inside: inside
    };
}

//get those boxes which are included in the
//range of select window
function containsBox(planes, box) {
    var min = box.min,
        max = box.max;

    var vertices = [
        new THREE.Vector3(min.x, min.y, min.z),
        new THREE.Vector3(min.x, min.y, max.z),
        new THREE.Vector3(min.x, max.y, max.z),
        new THREE.Vector3(max.x, max.y, max.z),
        new THREE.Vector3(max.x, max.y, min.z),
        new THREE.Vector3(max.x, min.y, min.z),
        new THREE.Vector3(min.x, max.y, min.z),
        new THREE.Vector3(max.x, min.y, max.z)
    ];

    var _iteratorNormalCompletion2 = true;
    var _didIteratorError2 = false;
    var _iteratorError2 = undefined;

    try {
        for (
            var _iterator2 = vertices[Symbol.iterator](), _step2;
            !(_iteratorNormalCompletion2 = (_step2 = _iterator2.next()).done);
            _iteratorNormalCompletion2 = true
        ) {
            var vertex = _step2.value;
            var _iteratorNormalCompletion3 = true;
            var _didIteratorError3 = false;
            var _iteratorError3 = undefined;

            try {
                for (
                    var _iterator3 = planes[Symbol.iterator](), _step3;
                    !(_iteratorNormalCompletion3 = (_step3 = _iterator3.next()).done);
                    _iteratorNormalCompletion3 = true
                ) {
                    var plane = _step3.value;

                    if (plane.distanceToPoint(vertex) < 0) {
                        return false;
                    }
                }
            } catch (err) {
                _didIteratorError3 = true;
                _iteratorError3 = err;
            } finally {
                try {
                    if (!_iteratorNormalCompletion3 && _iterator3.return) {
                        _iterator3.return();
                    }
                } finally {
                    if (_didIteratorError3) {
                        throw _iteratorError3;
                    }
                }
            }
        }
    } catch (err) {
        _didIteratorError2 = true;
        _iteratorError2 = err;
    } finally {
        try {
            if (!_iteratorNormalCompletion2 && _iterator2.return) {
                _iterator2.return();
            }
        } finally {
            if (_didIteratorError2) {
                throw _iteratorError2;
            }
        }
    }

    return true;
}

//get those boxes which are initersected with the
//range of select window (triangles)
function boxIntersectVertex(box, triangles) {
    for (index in triangles) {
        var t = triangles[index];
        if (collision.isIntersectionTriangleAABB(t.a, t.b, t.c, box)) return true;
    }
    return false;
}

function MySelectionWindow(viewer, options) {
    Autodesk.Viewing.Extension.call(this, viewer, options);

    _viewer = this.viewer;


    //when extension is loaded
    this.load = function () {
        _viewer.impl.invalidate(true, true, true);
        return true;
    };

    calculateFragBounding(_viewer);

    //create a material for the selection rectangle
    _materialLine = new THREE.LineBasicMaterial({
        color: new THREE.Color(0xff00ff),
        linewidth: 1,
        opacity: 0.6
    });

    //when extension is unloaded
    this.unload = function () {
        return true;
    };

}

MySelectionWindow.prototype = Object.create(
    Autodesk.Viewing.Extension.prototype
);
MySelectionWindow.prototype.varructor = MySelectionWindow;

Autodesk.Viewing.theExtensionManager.registerExtension(
    "MySelectionWindow",
    MySelectionWindow
);

var collision = {};


// aabb: <THREE.Box3>
// Plane: <THREE.Plane>
collision.isIntersectionAABBPlane = function (aabb, Plane) {

    var center = new THREE.Vector3().addVectors(aabb.max, aabb.min).multiplyScalar(0.5),
        extents = new THREE.Vector3().subVectors(aabb.max, center);

    var r = extents.x * Math.abs(Plane.normal.x) + extents.y * Math.abs(Plane.normal.y) + extents.z * Math.abs(Plane.normal.z);
    var s = Plane.normal.dot(center) - Plane.constant;

    return Math.abs(s) <= r;

}

// based on http://www.gamedev.net/topic/534655-aabb-triangleplane-intersection--distance-to-plane-is-incorrect-i-have-solved-it/
//
// a: <THREE.Vector3>, // vertex of a triangle
// b: <THREE.Vector3>, // vertex of a triangle
// c: <THREE.Vector3>, // vertex of a triangle
// aabb: <THREE.Box3>
collision.isIntersectionTriangleAABB = function (a, b, c, aabb) {

    var p0, p1, p2, r;

    // Compute box center and extents of AABoundingBox (if not already given in that format)
    var center = new THREE.Vector3().addVectors(aabb.max, aabb.min).multiplyScalar(0.5),
        extents = new THREE.Vector3().subVectors(aabb.max, center);

    // Translate triangle as conceptually moving AABB to origin
    var v0 = new THREE.Vector3().subVectors(a, center),
        v1 = new THREE.Vector3().subVectors(b, center),
        v2 = new THREE.Vector3().subVectors(c, center);

    // Compute edge vectors for triangle
    var f0 = new THREE.Vector3().subVectors(v1, v0),
        f1 = new THREE.Vector3().subVectors(v2, v1),
        f2 = new THREE.Vector3().subVectors(v0, v2);

    // Test axes a00..a22 (category 3)
    var a00 = new THREE.Vector3(0, -f0.z, f0.y),
        a01 = new THREE.Vector3(0, -f1.z, f1.y),
        a02 = new THREE.Vector3(0, -f2.z, f2.y),
        a10 = new THREE.Vector3(f0.z, 0, -f0.x),
        a11 = new THREE.Vector3(f1.z, 0, -f1.x),
        a12 = new THREE.Vector3(f2.z, 0, -f2.x),
        a20 = new THREE.Vector3(-f0.y, f0.x, 0),
        a21 = new THREE.Vector3(-f1.y, f1.x, 0),
        a22 = new THREE.Vector3(-f2.y, f2.x, 0);

    // Test axis a00
    p0 = v0.dot(a00);
    p1 = v1.dot(a00);
    p2 = v2.dot(a00);
    r = extents.y * Math.abs(f0.z) + extents.z * Math.abs(f0.y);

    if (Math.max(-Math.max(p0, p1, p2), Math.min(p0, p1, p2)) > r) {

        return false; // Axis is a separating axis

    }

    // Test axis a01
    p0 = v0.dot(a01);
    p1 = v1.dot(a01);
    p2 = v2.dot(a01);
    r = extents.y * Math.abs(f1.z) + extents.z * Math.abs(f1.y);

    if (Math.max(-Math.max(p0, p1, p2), Math.min(p0, p1, p2)) > r) {

        return false; // Axis is a separating axis

    }

    // Test axis a02
    p0 = v0.dot(a02);
    p1 = v1.dot(a02);
    p2 = v2.dot(a02);
    r = extents.y * Math.abs(f2.z) + extents.z * Math.abs(f2.y);

    if (Math.max(-Math.max(p0, p1, p2), Math.min(p0, p1, p2)) > r) {

        return false; // Axis is a separating axis

    }

    // Test axis a10
    p0 = v0.dot(a10);
    p1 = v1.dot(a10);
    p2 = v2.dot(a10);
    r = extents.x * Math.abs(f0.z) + extents.z * Math.abs(f0.x);
    if (Math.max(-Math.max(p0, p1, p2), Math.min(p0, p1, p2)) > r) {

        return false; // Axis is a separating axis

    }

    // Test axis a11
    p0 = v0.dot(a11);
    p1 = v1.dot(a11);
    p2 = v2.dot(a11);
    r = extents.x * Math.abs(f1.z) + extents.z * Math.abs(f1.x);

    if (Math.max(-Math.max(p0, p1, p2), Math.min(p0, p1, p2)) > r) {

        return false; // Axis is a separating axis

    }

    // Test axis a12
    p0 = v0.dot(a12);
    p1 = v1.dot(a12);
    p2 = v2.dot(a12);
    r = extents.x * Math.abs(f2.z) + extents.z * Math.abs(f2.x);

    if (Math.max(-Math.max(p0, p1, p2), Math.min(p0, p1, p2)) > r) {

        return false; // Axis is a separating axis

    }

    // Test axis a20
    p0 = v0.dot(a20);
    p1 = v1.dot(a20);
    p2 = v2.dot(a20);
    r = extents.x * Math.abs(f0.y) + extents.y * Math.abs(f0.x);

    if (Math.max(-Math.max(p0, p1, p2), Math.min(p0, p1, p2)) > r) {

        return false; // Axis is a separating axis

    }

    // Test axis a21
    p0 = v0.dot(a21);
    p1 = v1.dot(a21);
    p2 = v2.dot(a21);
    r = extents.x * Math.abs(f1.y) + extents.y * Math.abs(f1.x);

    if (Math.max(-Math.max(p0, p1, p2), Math.min(p0, p1, p2)) > r) {

        return false; // Axis is a separating axis

    }

    // Test axis a22
    p0 = v0.dot(a22);
    p1 = v1.dot(a22);
    p2 = v2.dot(a22);
    r = extents.x * Math.abs(f2.y) + extents.y * Math.abs(f2.x);

    if (Math.max(-Math.max(p0, p1, p2), Math.min(p0, p1, p2)) > r) {

        return false; // Axis is a separating axis

    }

    // Test the three axes corresponding to the face normals of AABB b (category 1).
    // Exit if...
    // ... [-extents.x, extents.x] and [min(v0.x,v1.x,v2.x), max(v0.x,v1.x,v2.x)] do not overlap
    if (Math.max(v0.x, v1.x, v2.x) < -extents.x || Math.min(v0.x, v1.x, v2.x) > extents.x) {

        return false;

    }
    // ... [-extents.y, extents.y] and [min(v0.y,v1.y,v2.y), max(v0.y,v1.y,v2.y)] do not overlap
    if (Math.max(v0.y, v1.y, v2.y) < -extents.y || Math.min(v0.y, v1.y, v2.y) > extents.y) {

        return false;

    }
    // ... [-extents.z, extents.z] and [min(v0.z,v1.z,v2.z), max(v0.z,v1.z,v2.z)] do not overlap
    if (Math.max(v0.z, v1.z, v2.z) < -extents.z || Math.min(v0.z, v1.z, v2.z) > extents.z) {

        return false;

    }

    // Test separating axis corresponding to triangle face normal (category 2)
    // Face Normal is -ve as Triangle is clockwise winding (and XNA uses -z for into screen)
    var plane = new THREE.Plane();
    plane.normal = new THREE.Vector3().copy(f1).cross(f0).normalize();
    plane.constant = plane.normal.dot(a);

    return collision.isIntersectionAABBPlane(aabb, plane);

}


// sphere1: <THREE.Sphere>
// sphere2: <THREE.Sphere>
collision.isIntersectionSphereSphere = function (sphere1, sphere2) {

    var radiusSum = sphere1.radius + sphere2.radius;

    return sphere1.center.distanceToSquared(sphere2.center) <= (radiusSum * radiusSum);

};

// sphere: <THREE.Sphere>
// aabb: <THREE.Box3>
collision.isIntersectionSphereAABB = function (sphere, aabb) {

    var i,
        rr = sphere.radius * sphere.radius,
        dmin = 0;

    if (sphere.center.x < aabb.min.x) {
        dmin += Math.sqrt(sphere.center.x - aabb.min.x)
    } else if (sphere.center.x > aabb.max.x) {
        dmin += Math.sqrt(sphere.center.x - aabb.max.x)
    }

    if (sphere.center.y < aabb.min.y) {
        dmin += Math.sqrt(sphere.center.y - aabb.min.y)
    } else if (sphere.center.y > aabb.max.y) {
        dmin += Math.sqrt(sphere.center.y - aabb.max.y)
    }

    if (sphere.center.z < aabb.min.z) {
        dmin += Math.sqrt(sphere.center.z - aabb.min.z)
    } else if (sphere.center.z > aabb.max.z) {
        dmin += Math.sqrt(sphere.center.z - aabb.max.z)
    }

    return dmin <= rr;

};

// based on http://realtimecollisiondetection.net/blog/?p=103
// sphere: <THREE.Sphere>
// a: <THREE.Vector3>, // vertex of a triangle
// b: <THREE.Vector3>, // vertex of a triangle
// c: <THREE.Vector3>, // vertex of a triangle
// normal: <THREE.Vector3>, // normal of a triangle
collision.isIntersectionSphereTriangle = function (sphere, a, b, c, normal) {

    // vs plane of traiangle face
    var A = new THREE.Vector3(),
        B = new THREE.Vector3(),
        C = new THREE.Vector3(),
        rr,
        V = new THREE.Vector3(),
        d,
        e;

    A.subVectors(a, sphere.center);
    B.subVectors(b, sphere.center);
    C.subVectors(c, sphere.center);
    rr = sphere.radius * sphere.radius;
    V.crossVectors(B.clone().sub(A), C.clone().sub(A));
    d = A.dot(V);
    e = V.dot(V);

    if (d * d > rr * e) {

        return false;

    }

    // vs triangle vertex
    var aa,
        ab,
        ac,
        bb,
        bc,
        cc;

    aa = A.dot(A);
    ab = A.dot(B);
    ac = A.dot(C);
    bb = B.dot(B);
    bc = B.dot(C);
    cc = C.dot(C);

    if (
        (aa > rr) & (ab > aa) & (ac > aa) ||
        (bb > rr) & (ab > bb) & (bc > bb) ||
        (cc > rr) & (ac > cc) & (bc > cc)
    ) {

        return false;

    }

    // vs edge
    var AB = new THREE.Vector3(),
        BC = new THREE.Vector3(),
        CA = new THREE.Vector3(),
        d1,
        d2,
        d3,
        e1,
        e2,
        e3,
        Q1 = new THREE.Vector3(),
        Q2 = new THREE.Vector3(),
        Q3 = new THREE.Vector3(),
        QC = new THREE.Vector3(),
        QA = new THREE.Vector3(),
        QB = new THREE.Vector3();

    AB.subVectors(B, A);
    BC.subVectors(C, B);
    CA.subVectors(A, C);
    d1 = ab - aa;
    d2 = bc - bb;
    d3 = ac - cc;
    e1 = AB.dot(AB);
    e2 = BC.dot(BC);
    e3 = CA.dot(CA);
    Q1.subVectors(A.multiplyScalar(e1), AB.multiplyScalar(d1));
    Q2.subVectors(B.multiplyScalar(e2), BC.multiplyScalar(d2));
    Q3.subVectors(C.multiplyScalar(e3), CA.multiplyScalar(d3));
    QC.subVectors(C.multiplyScalar(e1), Q1);
    QA.subVectors(A.multiplyScalar(e2), Q2);
    QB.subVectors(B.multiplyScalar(e3), Q3);

    if (
        (Q1.dot(Q1) > rr * e1 * e1) && (Q1.dot(QC) >= 0) ||
        (Q2.dot(Q2) > rr * e2 * e2) && (Q2.dot(QA) >= 0) ||
        (Q3.dot(Q3) > rr * e3 * e3) && (Q3.dot(QB) >= 0)
    ) {

        return false;

    }

    var distance = Math.sqrt(d * d / e) - sphere.radius,
        contactPoint = new THREE.Vector3(),
        negatedNormal = new THREE.Vector3(-normal.x, -normal.y, -normal.z);

    contactPoint.copy(sphere.center).add(negatedNormal.multiplyScalar(distance));

    return {
        distance: distance,
        contactPoint: contactPoint
    };

};

// based on Real-Time Collision Detection Section 5.3.6
// p: <THREE.Vector3>, // line3.start
// q: <THREE.Vector3>, // line3.end
// a: <THREE.Vector3>, // triangle.a
// b: <THREE.Vector3>, // triangle.b
// c: <THREE.Vector3>, // triangle.c
// normal: <THREE.Vector3>, // triangle.normal, optional

collision.isIntersectionSegmentTriangle = function (p, q, a, b, c, normal) {

    var ab = new THREE.Vector3().subVectors(b, a),
        ac = new THREE.Vector3().subVectors(c, a),
        qp = new THREE.Vector3().subVectors(p, q);

    if (!normal) {

        normal = new THREE.Vector3().copy(ab).cross(ac);

    }

    var d = qp.dot(normal);

    if (d <= 0.0) {
        return false;
    }

    var ap = new THREE.Vector3().subVectors(p, a),
        t = ap.dot(normal);

    if (t < 0) {
        return false;
    }
    if (t > d) {
        return false;
    }

    var e = new THREE.Vector3().copy(qp).cross(ap),
        v = ac.dot(e);

    if (v < 0 || v > d) {
        return false;
    }

    var w = -ab.dot(e);

    if (w < 0 || v + w > d) {
        return false;
    }

    var ood = 1 / d,
        u;

    t *= ood;
    v *= ood;
    w *= ood;
    u = 1 - v - w;

    return new THREE.Vector3(u, v, w);

}

// based on Real-Time Collision Detection Section 5.4.2
// p: <THREE.Vector3>, // point
// a: <THREE.Vector3>, // triangle.a
// b: <THREE.Vector3>, // triangle.c
// c: <THREE.Vector3>, // triangle.c
collision.isPointInTriangle = function (p, a, b, c) {

    var v0 = new THREE.Vector3(),
        v1 = new THREE.Vector3(),
        v2 = new THREE.Vector3(),
        u = new THREE.Vector3(),
        v = new THREE.Vector3(),
        w = new THREE.Vector3();

    v0.subVectors(a, p);
    v1.subVectors(b, p);
    v2.subVectors(c, p);

    u.copy(v1).cross(v2);
    v.copy(v2).cross(v0);

    if (u.dot(v) < 0) {

        return false;

    }

    w.copy(v0).cross(v1);

    if (u.dot(w) < 0) {

        return false;

    }

    return true;

}
