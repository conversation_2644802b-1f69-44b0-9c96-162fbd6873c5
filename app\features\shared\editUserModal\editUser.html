<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" ng-click="editUserCtrl.cancel()" aria-label="Close">
        <i class="fa fa-close" aria-hidden="true"></i>
    </button>

    <h2 class="modal-title" translate>CREATE_USER.EDIT_USER</h2>

    <h3 ng-if="editUserCtrl.hasErrorMessage" class="error-alert">
        {{editUserCtrl.errorMessage}}
    </h3>
</div>


<div class="modal-body">
    <form class="form" name="createCompanyForm">

        <div class="input-group">
            <label translate>CREATE_USER.FIRST_NAME</label>
            <input type="text" placeholder="{{'CREATE_USER.FIRST_NAME' | translate}}" ng-model="editUserCtrl.firstName" ng-required="true">
        </div>

        <div class="input-group">
            <label translate>CREATE_USER.LAST_NAME</label>
            <input type="text" placeholder="{{'CREATE_USER.LAST_NAME' | translate}}" ng-model="editUserCtrl.lastName" ng-required="true">
        </div>

        <div class="input-group">
            <label translate>CREATE_USER.EMAIL_ADDRESS</label>
            <input type="email" placeholder="{{'CREATE_USER.EMAIL_ADDRESS' | translate}}" ng-model="editUserCtrl.emailAddress"
                   ng-required="true">
        </div>
        <div class="input-group" ng-if="editUserCtrl.isSupreme && editUserCtrl.isDealer">
            <label>Visibility Contact ID</label>
            <input type="number" placeholder="Visibility Contact ID" ng-model="editUserCtrl.visContactId" ng-required="true">
        </div>


        <div ng-if="editUserCtrl.type === 'Internal'">
            <h3 class="group-title" translate>CREATE_USER.USER_PERMISSIONS</h3>

            <table class="simple-table">
                <thead>
                <tr>
                    <th translate>CREATE_USER.PERMISSION</th>
                    <th translate>CREATE_USER.DESCRIPTION</th>
                    <th></th>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <td><strong translate>CREATE_USER.ORDERS</strong></td>
                    <td translate>CREATE_USER.ORDERS_SUBTEXT</td>
                    <td><input type="checkbox" ng-model="editUserCtrl.permissions.orders" class="checkbox"></td>

                </tr>
                <tr>
                    <td><strong translate>CREATE_USER.PRODUCTS</strong></td>
                    <td translate>CREATE_USER.PRODUCTS_SUBTEXT</td>
                    <td><input type="checkbox" ng-model="editUserCtrl.permissions.products" class="checkbox"></td>

                </tr>
                <tr>
                    <td><strong translate>CREATE_USER.PUBLICATIONS</strong></td>
                    <td translate>CREATE_USER.PUBLICATIONS_SUBTEXT</td>
                    <td><input type="checkbox" ng-model="editUserCtrl.permissions.publications" class="checkbox"></td>

                </tr>
                <tr>
                    <td><strong translate>CREATE_USER.CUSTOMERS</strong></td>
                    <td translate>CREATE_USER.CUSTOMERS_SUBTEXT</td>
                    <td><input type="checkbox" ng-model="editUserCtrl.permissions.customers" class="checkbox"></td>
                </tr>
                <tr>
                    <td><strong translate>CREATE_USER.ADMIN</strong></td>
                    <td translate>CREATE_USER.ADMIN_SUBTEXT</td>
                    <td><input type="checkbox" ng-model="editUserCtrl.permissions.admin" class="checkbox"></td>
                </tr>
                <tr>
                    <td><strong translate>CREATE_USER.SECURITY</strong></td>
                    <td translate>CREATE_USER.SECURITY_SUBTEXT</td>
                    <td><input type="checkbox" ng-model="editUserCtrl.permissions.security" class="checkbox"></td>
                </tr>
                <tr>
                    <td><strong translate>CREATE_USER.PARTS</strong></td>
                    <td translate>CREATE_USER.PARTS_SUBTEXT</td>
                    <td><input type="checkbox" ng-model="editUserCtrl.permissions.parts" class="checkbox"></td>
                </tr>
                <tr ng-show="editUserCtrl.dashboardEnabled">
                    <td><strong translate>CREATE_USER.DASHBOARD</strong></td>
                    <td translate>CREATE_USER.DASHBOARD_SUBTEXT</td>
                    <td><input type="checkbox" ng-model="editUserCtrl.permissions.dashboard" class="checkbox"></td>
                </tr>

                </tbody>
            </table>
        </div>

		<div class="mt-2" ng-if="editUserCtrl.type === 'Internal'" ng-show="editUserCtrl.isManufacturer">
			  <h3 class="group-title" translate>CREATE_USER.USER_SETTINGS</h3>

			  <table class="simple-table">
				    <thead>
				    <tr>
						<th translate>CREATE_USER.PERMISSION</th>
						<th translate>CREATE_USER.DESCRIPTION</th>
						<th></th>
				    </tr>
				    </thead>
				    <tbody>

				    <tr>
						<td><strong translate>CREATE_USER.DISCOUNT</strong></td>
						<td translate>CREATE_USER.DISCOUNT_SUBTEXT</td>
						<td><input type="checkbox" ng-model="editUserCtrl.isDiscountEditable" class="checkbox"></td>
				    </tr>

				    </tbody>
			  </table>
		</div>

        <div ng-if="editUserCtrl.type !== 'Internal'">
            <h3 class="group-title" translate>CREATE_USER.USER_PERMISSIONS</h3>

            <table class="simple-table">
                <thead>
                <tr>
                    <th translate>CREATE_USER.PERMISSION</th>
                    <th translate>CREATE_USER.DESCRIPTION</th>
                    <th></th>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <td><strong translate>CREATE_USER.CUST_ORDERS</strong></td>
                    <td translate>CREATE_USER.CUST_ORDERS_SUBTEXT</td>
                    <td><input type="checkbox" ng-model="editUserCtrl.permissions.orders" class="checkbox"></td>

                </tr>
                <tr>
                    <td><strong translate>CREATE_USER.PUBLISHED_PRODUCTS</strong></td>
                    <td translate>CREATE_USER.PUBLISHED_PRODUCTS_SUBTEXT</td>
                    <td><input type="checkbox" ng-model="editUserCtrl.permissions.publishedProducts" class="checkbox"></td>

                </tr>
                <tr ng-if="editUserCtrl.hasPartSearchEnabled">
                    <td><strong translate>CREATE_USER.PART_SEARCH</strong></td>
                    <td translate>CREATE_USER.PART_SEARCH_SUBTEXT</td>
                    <td><input type="checkbox" ng-model="editUserCtrl.permissions.partSearch" class="checkbox"></td>

                </tr>

                </tbody>
            </table>
        </div>

        <div class="modal-actions my-4 mr-4">
            <button type="button" class="btn secondary ml-0 ml-lg-2" data-dismiss="modal"
                    ng-click="editUserCtrl.cancel()" translate>
                GENERAL.CANCEL
            </button>
            <button type="button" class="btn primary ml-0 mt-lg-0"
                    ng-disabled="!createCompanyForm.$valid || editUserCtrl.isDisabled"
                    ng-click="editUserCtrl.editUser()" translate>
                CREATE_USER.SAVE_CHANGES
            </button>
        </div>

    </form>
</div>


