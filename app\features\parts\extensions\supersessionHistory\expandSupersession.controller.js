(function () {
    'use strict';

    angular
        .module('app.parts')
        .controller('ExpandSupersessionController', ExpandSupersessionController);

    ExpandSupersessionController.$inject = ['$uibModalInstance', 'supersessionHistory', 'masterPartId', 'partNumber', 'isLoadingSupersessionHistory', 'removePartFromSupersession'];

    function ExpandSupersessionController($uibModalInstance, supersessionHistory, masterPartId, partNumber, isLoadingSupersessionHistory, removePartFromSupersession) {
        var vm = this;
        vm.supersessionHistory = supersessionHistory;
        vm.masterPartId = masterPartId;
        vm.partNumber = partNumber;
        vm.isLoadingSupersessionHistory = isLoadingSupersessionHistory;
        vm.cancel = $uibModalInstance.dismiss;
        vm.removePartFromSupersession = removeAndClose;

        vm.closeModal = closeModal;

        initialize();

        function initialize() {
        }

      function removeAndClose() {
        removePartFromSupersession()
          .then(function () {
            closeModal();
          })
          .catch(function (error) {
            console.error("Error removing supersession:", error);
          });
      }

        function closeModal() {
            $uibModalInstance.close();
        }
    }
})();
