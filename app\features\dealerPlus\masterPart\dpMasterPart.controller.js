(function () {
    'use strict';

    angular
        .module('app.parts')
        .controller('DPMasterPartController', DPMasterPartController);

    DPMasterPartController.$inject = ['$stateParams', 'DPMasterPartService', '$state', 'userService', '$uibModal', '$translate'];

    function DPMasterPartController($stateParams, DPMasterPartService, $state, userService, $uibModal, $translate) {
        var vm = this;

        vm.masterPartId = $stateParams.masterPartId;
        vm.currency = userService.getDPCustomerDefaultCurrency();
        vm.price = null;
        vm.stock = null;

        vm.translations = [];
        vm.kits = [];
        vm.optionSet = [];
        vm.additionalParts = [];
        vm.models = [];

        vm.goToModel = goToModel;
        vm.editPart = editPart;
        vm.cancelEdit = cancelEdit;
        vm.saveEdit = saveEdit;

        initialize();

        function initialize() {
            DPMasterPartService.getMasterPartDetails(vm.masterPartId)
                .then(getMasterPartDetailsSuccess, serviceFailed);
            DPMasterPartService.getTranslations(vm.masterPartId)
                .then(getTranslationsSuccess, serviceFailed);
        }

        function getMasterPartDetailsSuccess(response) {
            vm.partNumber = response.data.partNumber;
            vm.defaultDescription = response.data.description;

            vm.previewPricingEnabled = response.data.previewPricingEnabled;
            vm.previewStockLevelEnabled = response.data.previewStockLevelEnabled;
            vm.hasLinkedPart = response.data.linked;
            vm.hasLinkedTechDocs = response.data.hasLinkedTechDoc;
            vm.hasOptionSet = response.data.containsOptionsSet;
            vm.hasAdditionalPart = response.data.hasNonModelledPart;
            vm.isSuperseded = response.data.superseded;

            DPMasterPartService.getPrice(vm.masterPartId)
                .then(getPriceSuccess, serviceFailed);

            if (vm.hasLinkedPart) {
                DPMasterPartService.getLink(vm.masterPartId)
                    .then(getLinkSuccess, serviceFailed);
            }
            if (vm.isSuperseded) {
                getSuperseded();
            }
            if (response.data.inKit) {
                getKits();
            }
            if (vm.hasOptionSet) {
                getOptionSet();
            }
            if (vm.hasAdditionalPart) {
                getAdditionalParts();
            }

            DPMasterPartService.getModelsForParts(vm.masterPartId)
                .then(getModelsForPartsSuccess, serviceFailed);
        }

        function getPriceSuccess(response) {
            vm.price = response.data;
        }

        function getOptionSetForMasterPartSuccess(response) {
            vm.optionSetId = response.data[0].id;
            vm.optionSetDescription = response.data[0].description;
            vm.optionSetParts = response.data[0].optionsSet;
        }

        function getLinkSuccess(response) {
            vm.linkedPart = response.data;
        }

        function getNonModeledPartsSuccess(response) {
            vm.additionalParts = response.data.nonModelledPart;
        }

        function getModelsForPartsSuccess(response) {
            vm.models = response.data;
        }

        function serviceFailed() {

        }

        function getKits() {
            DPMasterPartService.getKitsForMasterPart(vm.masterPartId)
                .then(getKitsSuccess, serviceFailed)
        }

        function getOptionSet() {
            DPMasterPartService.getOptionSetForMasterPart(vm.masterPartId)
                .then(getOptionSetForMasterPartSuccess, serviceFailed);
        }

        function getAdditionalParts() {
            DPMasterPartService.getAdditionalPart(vm.masterPartId)
                .then(getNonModeledPartsSuccess, serviceFailed);
        }

        function getKitsSuccess(response) {
            vm.kits = response.data;
        }

        function goToModel(modelIndex) {
            var model = vm.models[modelIndex];
            $state.go("manufacturerViewer", {
                productId: model.machineId,
                autodeskURN: model.autodeskUrn,
                modelId: model.modelId,
                machineName: model.machineName,
                viewableName: model.modelName
            });
        }

        function getSupersedeSuccess(response) {
            vm.supersededPart = response.data;
        }

        function getSuperseded() {
            DPMasterPartService.getSupersede(vm.masterPartId)
                .then(getSupersedeSuccess, serviceFailed);
        }


        function getTranslationsSuccess(response) {
            vm.translations = response.data;
            var allLanguages = userService.getUserLanguages();
            for (var i = 0; i < allLanguages.length; i++) {
                if (_.findIndex(vm.translations, {languageId: allLanguages[i].languageId}) < 0) {
                    vm.translations.push({
                        languageId: allLanguages[i].languageId,
                        displayText: allLanguages[i].displayText,
                        description: null
                    });
                }
            }
        }

        function editPart(partId) {
            vm.oldValue = vm.price;
            vm.isBeingEdited = true;
            vm.isActiveEdit = true;
        }

        function cancelEdit() {
            vm.price = vm.oldValue;
            vm.isBeingEdited = false;
            vm.isActiveEdit = false;
        }

        function saveEdit() {
            DPMasterPartService.updatePrice(vm.masterPartId, vm.price)
                .then(saveEditSuccess, saveEditFailed);
        }

        function saveEditSuccess() {
            vm.isBeingEdited = false;
            vm.isActiveEdit = false;
        }

        function saveEditFailed() {
            headerBannerService.setNotification('ERROR', WENT_WRONG, 10000);
        }

    }
})();