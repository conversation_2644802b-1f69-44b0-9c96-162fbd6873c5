<!-- Heading Section -->
<section class="body-content">
    <div class="col-12 col-md-8" >
        <h1 translate>PRICE_MANAGEMENT.TITLE</h1>
        <p translate>PRICE_MANAGEMENT.SUBTITLE</p>
    </div>

    <!-- Search Section -->
    <div class="row">
        <div class="col-12 col-md-8">
            <div class="pb-5">

                <form class="d-flex justify-content-center align-items-center flex-column search_contents p-5"
                      ng-submit="priceMgmtCtrl.search()">

                    <div class="d-flex w-75 search-area">

                        <div class="input-group mb-0 pr-3">
                            <input class="form-control mr-0" type="search" ng-model="priceMgmtCtrl.searchValue"
                                   placeholder="{{priceMgmtCtrl.isSearchOnlyPartNumbers ? 'PRICE_MANAGEMENT.SEARCH_BY_PART' : 'PRICE_MANAGEMENT.SEARCH_BY_PART_AND_DESC' | translate}}">
                            <div class="input-group-append">
                                <button type="button" ng-click="priceMgmtCtrl.search()"
                                        ng-hide="priceMgmtCtrl.searching"
                                        class="input-group-text input-group-text-btn input-group-text-btn btn-anim"><i
                                        class="pr-0 pr-md-3 fa fa-search"></i>
                                    <span class="search_mobile_disable">{{'PRICE_MANAGEMENT.SEARCH' | translate}}</span>
                                </button>
                                <button type="button" ng-show="priceMgmtCtrl.searching"
                                        class="input-group-text input-group-text-btn btn-anim"><i
                                        class="pr-0 pr-md-3 fa fa-search"></i><span
                                        class="search_mobile_disable">{{'PRICE_MANAGEMENT.SEARCHING' | translate}}</span>
                                    <span class="spinner-border text-light" role="status" aria-hidden="true"></span>
                                </button>
                            </div>
                        </div>

                    </div>

                    <div class="d-flex justify-content-center align-content-center flex-wrap mt-4" ng-hide="priceMgmtCtrl.isSearchOnlyPartNumbers">
                        <span class="pr-3" translate>PRICE_MANAGEMENT.SEARCH_BY</span>
                        <label class="pr-3 radio-inline">
                            <input type="radio" ng-model="priceMgmtCtrl.searchBy" value="partNumber"
                                   style="white-space:nowrap"><span style="white-space:nowrap" translate>PRICE_MANAGEMENT.PART_NUM</span>
                        </label>
                        <label class="radio-inline">
                            <input type="radio" ng-model="priceMgmtCtrl.searchBy" value="partDescription"
                                   style="white-space:nowrap"><span style="white-space:nowrap" translate>PRICE_MANAGEMENT.PART_DESC</span>
                        </label>
                    </div>

                    <div ng-show="!priceMgmtCtrl.searchResults.length > 0 && priceMgmtCtrl.resultsReturned">
                        <h2 class="center-align" translate>PRICE_MANAGEMENT.NO_PART</h2>
                    </div>
                    <div ng-show="priceMgmtCtrl.searchError">
                        <h3 class="error-alert center-align" translate>GENERAL.WENT_WRONG</h3>
                    </div>

                </form>
            </div>

            <div class="customDivStyling p-0">

                <table class="table table-bordered" ng-show="priceMgmtCtrl.searchResults.length > 0">
                    <thead>
                    <tr>
                        <th ng-class="priceMgmtCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'" ng-click="priceMgmtCtrl.part_sort='partNumber'; priceMgmtCtrl.sortReverse = !priceMgmtCtrl.sortReverse"
                            class="col-3" translate>PRICE_MANAGEMENT.PART_NUM
                        </th>
                        <th ng-class="priceMgmtCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'" class="col-3"
                            ng-click="priceMgmtCtrl.part_sort='description'; priceMgmtCtrl.sortReverse = !priceMgmtCtrl.sortReverse"
                             translate>PRICE_MANAGEMENT.DESCRIPTION
                        </th>
                        <th ng-class="priceMgmtCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'" class="col-3"
                            ng-click="priceMgmtCtrl.part_sort='price'; priceMgmtCtrl.sortReverse = !priceMgmtCtrl.sortReverse"
                            translate>PRICE_MANAGEMENT.PRICE
                        </th>
                        <th class="col-3" translate>GENERAL.ACTIONS</th>
                    </tr>
                    </thead>

                    <tbody>
                    <tr ng-repeat="part in priceMgmtCtrl.searchResults | orderBy:priceMgmtCtrl.part_sort:priceMgmtCtrl.sortReverse"
                        ng-show="priceMgmtCtrl.searchResults.length > 0">
                        <td data-label="{{'PRICE_MANAGEMENT.PART_NUM' | translate}}">{{part.partNumber}}</td>

                        <td data-label="{{'PRICE_MANAGEMENT.DESCRIPTION' | translate}}">
                            {{part.description ? part.description : part.partDescription}}
                        </td>

                        <td data-label="{{'PRICE_MANAGEMENT.PRICE' | translate}}" ng-hide="part.isBeingEdited">
					  <span class="d-flex justify-content-center mr-0 mr-lg-5">
                            <span class="d-flex align-items-center justify-content-end cadIconContainerWidth">

                            {{part.price | currency:priceMgmtCtrl.defaultCurrency.symbol:2}}
                            <button class="btn primary cadIconContainer ml-4"
                                 ng-click="priceMgmtCtrl.editPart(part.masterPartId)" ng-hide="part.isBeingEdited"
                                 ng-disabled="priceMgmtCtrl.isActiveEdit">
                                <i class="fa fa-pencil"></i>
                            </button>
                            </span>
				    </span>
                        </td>

                        <td data-label="{{'PRICE_MANAGEMENT.PRICE' | translate}}" ng-show="part.isBeingEdited" >
<span class="d-flex align-items-center justify-content-center tableWidth100" >
                            <input type="number" ng-model="part.price" />

                            <button class="btn success cadIconContainer mx-2"
                                 ng-click="priceMgmtCtrl.saveEdit(part.masterPartId)" ng-show="part.isBeingEdited">
                                <i class="fa fa-save"></i>
                            </button>
                            <button class="btn danger cadIconContainer"
                                 ng-click="priceMgmtCtrl.cancelEdit(part.masterPartId)" ng-show="part.isBeingEdited">
                                <i class="fa fa-times"></i>
                            </button>
</span>
                        </td>

                        <td class="mobile-right-aligned-btn">
                            <button class="btn primary" ng-click="priceMgmtCtrl.viewPart(part.masterPartId)">
                                <div translate="" class="ng-scope" translate>PRICE_MANAGEMENT.VIEW_PART</div>
                            </button>
                        </td>

                    </tr>

                    <tr ng-show="priceMgmtCtrl.searching" align="center">
                        <td class="preloader" colspan="3">
                            <img ng-src="images/cadpreloader.gif" class="ajax-loader" height="60" width="60"/>
                        </td>
                    </tr>

                    </tbody>

                </table>

            </div>

        </div>
        <div class="col-12 col-md-4 mt-4 mt-md-0">
            <div class="py-5 white_div">
                <h1 translate class="text-center">PRICE_MANAGEMENT.UPLOAD_TITLE</h1>
                <div class="upload-box">
                    <div>
                        <i class="fa fa-upload"></i>
                        <h4 class="file-uploader" translate>PRICE_MANAGEMENT.CHOOSE_FILE</h4>
                        <input type="file" class="fileupload" ng-click="$event = $event"
                               onchange="angular.element(this).scope().priceMgmtCtrl.fileChanged(event)"
                               accept=".csv"/>
                    </div>
                </div>
                <p>
                    <emphasis>{{priceMgmtCtrl.filename}}</emphasis>
                </p>


                <p class="error-alert" ng-show="priceMgmtCtrl.errorMessage !== ''">
                    {{priceMgmtCtrl.errorMessage}}</p>
                <p class="success-alert" ng-show="priceMgmtCtrl.successMessage !== ''">
                    {{priceMgmtCtrl.successMessage}}</p>

                <button class="btn primary col-12" ng-click="priceMgmtCtrl.upload()"
                        ng-disabled="priceMgmtCtrl.uploading">
                    <span ng-hide="priceMgmtCtrl.uploading">{{"PRICE_MANAGEMENT.UPLOAD" | translate}}</span>
                    <span ng-show="priceMgmtCtrl.uploading">
                        <span class="spinner-border text-light" role="status" aria-hidden="true"></span>
                    {{"PRICE_MANAGEMENT.UPLOADING" | translate}}
                    </span>
                </button>

                <button class="btn primary-outline col-12 mt-3" ng-click="priceMgmtCtrl.exportPrices()"
                        ng-disabled="priceMgmtCtrl.exporting">
                    <span ng-hide="priceMgmtCtrl.exporting">{{"PRICE_MANAGEMENT.EXPORT_PRICE_DATA" | translate}}</span>
                    <span ng-show="priceMgmtCtrl.exporting">
                        <span class="spinner-border text-light" role="status" aria-hidden="true"></span>
                    {{"PRICE_MANAGEMENT.EXPORTING_PRICE_DATA" | translate}}
                    </span>
                </button>

                <button class="btn secondary col-12 mt-3" ng-click="priceMgmtCtrl.downloadTemplate()"
                        ng-disabled="priceMgmtCtrl.downloading">
                    <span ng-hide="priceMgmtCtrl.downloading">{{"PRICE_MANAGEMENT.DOWNLOAD_TEMPLATE" | translate}}</span>
                    <span ng-show="priceMgmtCtrl.downloading">
                        <span class="spinner-border text-light" role="status" aria-hidden="true"></span>
                    {{"PRICE_MANAGEMENT.DOWNLOADING_TEMPLATE" | translate}}
                    </span>
                </button>

            </div>
            <div class="customDivStyling p-0" ng-show="priceMgmtCtrl.displayProgress">
                <table class="table table-bordered">
                    <thead>
                    <tr>
                        <th>{{"PARTS_UPLOAD.PROCESS" | translate}}</th>
                        <th>{{"PARTS_UPLOAD.STATUS" | translate}}</th>
                        <th>{{"PARTS_UPLOAD.FILE" | translate}}</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr ng-repeat="process in priceMgmtCtrl.processes">
                        <td data-label="{{'PARTS_UPLOAD.PROCESS' | translate}}">{{process.processDisplayName}}</td>
                        <td data-label="{{'PARTS_UPLOAD.STATUS' | translate}}">{{process.status}}</td>
                        <td>
                            <button class="btn primary" ng-show="process.status === 'COMPLETE' && process.process === 'MASTERPART_INVENTORY_EXPORT'"
                                    ng-click="priceMgmtCtrl.downloadCSV(process, 'parts')">
                                <i class="fa fa-download fa-lg"></i>
                            </button>
                        </td>
                    </tr>
                </table>
            </div>

        </div>

    </div>


</section>
