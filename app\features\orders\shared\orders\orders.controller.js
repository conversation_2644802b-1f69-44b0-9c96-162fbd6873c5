(function () {
    'use strict';

    angular
        .module('app.orders')
        .controller('OrdersController', OrdersController);

    OrdersController.$inject = ['$state', 'ordersService', 'headerBannerService', '$uibModal', '$window', '$translate', '$filter'];

    function OrdersController($state, ordersService, headerBannerService, $uibModal, $window, $translate, $filter) {
        var vm = this;

        vm.orders = [];
        vm.searchValue = '';
        vm.sortValue = null;
        vm.isOrdersLoaded = false;
        vm.sortReverse = false;
        vm.order_item = 'modifieddate';
        vm.filterValue = {};

        vm.preventClickPropagation = preventClickPropagation;
        vm.viewOrder = viewOrder;
        vm.cancelOrder = cancelOrder;
        vm.searchFilterChange = searchFilterChange;

        var CANCEL_ORDER, DELETE_SUCCESS, NOT_DELETED, ARE_YOU_SURE;
        $translate(['ORDERS.CANCEL_ORDER', 'ORDERS.DELETE_SUCCESS', 'ORDERS.NOT_DELETED', 'ORDERS.ARE_YOU_SURE'])
            .then(function (resp) {
                CANCEL_ORDER = resp["ORDERS.CANCEL_ORDER"];
                DELETE_SUCCESS = resp["ORDERS.DELETE_SUCCESS"];
                NOT_DELETED = resp["ORDERS.NOT_DELETED"];
                ARE_YOU_SURE = resp["ORDERS.ARE_YOU_SURE"];
            });

        function viewOrder(orderId) {
            $state.go(this.viewState, {
                orderId: orderId
            });
        }

        function preventClickPropagation($event) {
            $event.stopPropagation();
        }

        var cancelOrderId;
        function cancelOrder(orderId) {
            cancelOrderId = orderId;
            var confirmObject = {
                titleText: CANCEL_ORDER + " #" + orderId,
                bodyText: ARE_YOU_SURE + " #" + orderId + "?"
            };
            $uibModal.open({
                templateUrl: 'features/shared/confirmationModal/confirmModal.html',
                controller: 'ConfirmModalController',
                controllerAs: 'confirmModalCtrl',
                size: 'sm',
                resolve: {
                    confirmObject: function () {
                        return confirmObject;
                    }
                }
            }).result
                .then(cancelOrderConfirmed, doNothing);
        }

        function cancelOrderConfirmed() {
            ordersService.cancelOrder(cancelOrderId)
                .then(cancelOrderSuccess, cancelOrderFailed)
        }

        function doNothing() {
        }

        function cancelOrderSuccess() {
            headerBannerService.setNotification('SUCCESS', DELETE_SUCCESS, 2000);
            $window.location.reload();
        }

        function cancelOrderFailed() {
            headerBannerService.setNotification('ERROR', NOT_DELETED, 2000);
        }

        function searchFilterChange() {
            updateTotalItemCount();
        }

        function updateTotalItemCount() {
            vm.totalItems = $filter('filter')(vm.orders, vm.searchValue);
        }

    }
})();
