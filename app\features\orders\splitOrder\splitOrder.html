<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" ng-click="splitOrderCtrl.cancel()"
            aria-label="Close"><i class="fa fa-close" aria-hidden="true"></i>
    </button>
    <h2 class="modal-title">{{"SPLIT_ORDER.TITLE" | translate}} #{{splitOrderCtrl.displayId}}</h2>
</div>

<div class="modal-body">
    <div class="error-well" ng-if="splitOrderCtrl.errorSplittingOrder">
        <p translate>SPLIT_ORDER.ERROR</p>
    </div>
    <div class="error-well" ng-if="splitOrderCtrl.quantityMismatchError">
        <p translate>SPLIT_ORDER.ENTER_VALID_QUANTITY</p>
    </div>
    <div class="error-well" ng-if="splitOrderCtrl.nothingSelectedError">
        <p translate>SPLIT_ORDER.AT_LEAST_ONE</p>
    </div>


    <p translate>SPLIT_ORDER.CHOOSE_TO_SPLIT</p>

    <form class="form">

        <h3 translate>SPLIT_ORDER.IDENTIFIED_PARTS</h3>
        <table class="table table-bordered">
            <thead>
            <tr>
                <th translate>SPLIT_ORDER.PART_NO</th>
                <th translate>SPLIT_ORDER.PART_DESC</th>
                <th translate>SPLIT_ORDER.QTY</th>
                <th translate>SPLIT_ORDER.QTY_TO_SPLIT</th>
                <th translate>SPLIT_ORDER.SPLIT</th>
            </tr>
            </thead>

            <tbody>
            <tr ng-repeat="item in splitOrderCtrl.partsList">
                <td data-label="{{'SPLIT_ORDER.PART_NO' | translate}}">{{item.partNumber}}</td>
                <td data-label="{{'SPLIT_ORDER.PART_DESC' | translate}}">{{item.partDescription}}</td>
                <td data-label="{{'SPLIT_ORDER.QTY' | translate}}">{{item.oldQuantity}}</td>
                <td data-label="{{'SPLIT_ORDER.QTY_TO_SPLIT' | translate}}"><input ng-model="item.quantity" type="number" min="0" max="{{splitOrderCtrl.getSplitOrderMaxQuantity(item)}}"></td>
                <td data-label="{{'SPLIT_ORDER.SPLIT' | translate}}"><input ng-model="item.isSelected" type="checkbox"></td>
            </tr>

            </tbody>
        </table>
        <br>

        <h3 ng-show="splitOrderCtrl.manualPartsList.length>0" translate>SPLIT_ORDER.MANUALLY_ADDED_PARTS</h3>
        <table class="table table-bordered" ng-show="splitOrderCtrl.manualPartsList.length>0">
            <thead>
            <tr>
                <th translate>SPLIT_ORDER.PART_NO</th>
                <th translate>SPLIT_ORDER.PART_DESC</th>
                <th translate>SPLIT_ORDER.QTY</th>
                <th translate>SPLIT_ORDER.QTY_TO_SPLIT</th>
                <th translate>SPLIT_ORDER.SPLIT</th>
            </tr>
            </thead>

            <tbody>
            <tr ng-repeat="manualItem in splitOrderCtrl.manualPartsList">
                <td data-label="{{'SPLIT_ORDER.PART_NO' | translate}}">{{manualItem.partNumber}}</td>
                <td data-label="{{'SPLIT_ORDER.PART_DESC' | translate}}">{{manualItem.partDescription}}</td>
                <td data-label="{{'SPLIT_ORDER.QTY' | translate}}">{{manualItem.oldQuantity}}</td>
                <td data-label="{{'SPLIT_ORDER.QTY_TO_SPLIT' | translate}}"><input ng-model="manualItem.quantity" type="number" min="0" max="{{manualItem.oldQuantity}}"></td>
                <td data-label="{{'SPLIT_ORDER.SPLIT' | translate}}"><input ng-model="manualItem.isSelected" type="checkbox"></td>
            </tr>

            </tbody>
        </table>

        <div class="modal-actions">
            <button type="button" class="btn secondary" ng-click="splitOrderCtrl.cancel()" translate>GENERAL.CANCEL</button>

            <button type="button" class="btn primary"
                    ng-click="splitOrderCtrl.splitOrder()" ng-class="splitOrderCtrl.isDealerPlusPage() ? 'dpGreenModal btn primary' : 'btn primary'" translate>SPLIT_ORDER.SPLIT_ORDER
            </button>

        </div>

    </form>
</div>