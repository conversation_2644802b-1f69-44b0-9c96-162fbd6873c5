// .kit-modal-content {
//     width: calc(100vw - 600px);
//     min-width: 800px;
// }

.search-results {
    overflow-y: auto;
    max-height: 570px;
}

.parts-in-kit {
    overflow-y: auto;
    max-height: 660px;
}

.search-input-flex {
    flex-grow: 0 !important;
    flex-shrink: 0 !important ;
    width: 100% !important;
}

.text-truncate-kits {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    max-width: 60vh;
}

.filter-icon-right .input-group.has-search > .form-control {
    padding-left: 1rem !important;
}

.filter-icon-right .fa-search {
    display: none !important;
}

/* filter icon on the right side */
.filter-icon-right .input-group-append:before {
    content: "\f0b0"; /* Unicode for 'fa-filter' icon */
    font-family: "FontAwesome";
    display: inline-block;
    position: absolute;
    right: 1.2rem;
    top: 50%;
    transform: translateY(-50%);
    z-index: 3;
    cursor: pointer;
    font-size: 18px;
}

.filter-icon-right .input-group .form-control {
    padding-right: 3rem;
}

/* Ensure clear icon is hidden */
.filter-icon-right .form-control-clear {
    display: none;
}

.desc-text-area {
    width: 100%;
    min-height: 13.5rem;
}

.table-no-th {
    width: 0px;
}

.customized-search {
    display: flex;
    flex-direction: column;
}

.customized-bar {
    flex-grow: unset;
}

.quantity-input {
    text-align: -webkit-center;
    max-width: 80px;
}
