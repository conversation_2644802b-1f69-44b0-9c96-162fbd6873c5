<h2 ng-if="customerPartSearchCtrl.masterParts.length > 0" translate>ORDER.PARTS</h2>

<div class="responsiveContainer py-0">    

    <table class="table table-bordered equal-width" ng-show="customerPartSearchCtrl.masterParts.length > 0">
        <thead>
            <tr>
                <th class="col-md-2 col-12" translate>GENERAL.PART_NUMBER</th>
                <th class="col-md-4 col-12" translate>ORDER.DESCRIPTION</th>
                <th ng-if="customerPartSearchCtrl.isCustomerSearchManualsOnly" translate>
                    ORDER.PUBLICATION
                </th>
                <th ng-if="customerPartSearchCtrl.isPreviewStockLevelEnabled && !customerPartSearchCtrl.isDealerPlusCustomer"
                    translate>
                    ORDER.STOCK
                </th>
                <th translate>ORDER.QUANTITY</th>
                <th class="disableWordBreak" ng-if="!customerPartSearchCtrl.hidePrice" translate>
                    ORDER.ITEM_PRICE
                </th>
                <th class="disableWordBreak" ng-if="!customerPartSearchCtrl.hidePrice" translate>
                    ORDER.TOTAL_PRICE
                </th>
                <th></th>
            </tr>
        </thead>
    
        <tbody>
            <tr ng-class="{'borderTopSupersession': customerPartSearchCtrl.accordionSupersession[$index]}"
                ng-repeat-start="part in customerPartSearchCtrl.masterParts" ng-show="customerPartSearchCtrl.masterParts.length > 0"
                ng-click="($event.target.tagName !== 'INPUT' && $event.target.tagName !== 'BUTTON' && $event.target.tagName !== 'I') && (part.isOpen = !part.isOpen)">
                <td data-label="{{'PART_SEARCH.PART_NUM' | translate}}">
                        <div ng-class="{'text-muted': customerPartSearchCtrl.accordionSupersession[$index]}">
                            {{ part.partNumber }} <span class="font-weight-bold" ng-if="part.superseded" translate>PART_SEARCH.SUPERSEDED</span>
                        </div>
    
                        <div class="tooltip-container" ng-if="part.inSupersession && !part.superseded">
                            <button class="tooltip-trigger" ng-click="customerPartSearchCtrl.toggleSupersessionHistory(part)">
                              <small class="underline">
                                {{ part.showSupersessionHistory ? 'PART_SEARCH.HIDE_SUPERSESSION_HISTORY' : 'PART_SEARCH.VIEW_SUPERSESSION_HISTORY' | translate }}
                              </small>
                            </button>
                            <div class="custom-tooltip" ng-class="{'show': part.showSupersessionHistory, 'last-item': $last}">
                            <div class="tooltip-arrow"></div>
                            <div ng-if="!customerPartSearchCtrl.isSupersessionLoading"class="flex supersessionHistoryHeader mb-3">
                                <h3 class="mb-0" translate>PART_SEARCH.SUPERSESSION_HISTORY</h3>
                            </div>
                            <div ng-if="customerPartSearchCtrl.isSupersessionLoading" class="p-4 text-center">
                                <span class="spinner-border text-primary" role="status" aria-hidden="true"></span>
                                <p translate>GENERAL.LOADING</p>
                            </div>
                            <div class="d-flex flex-column align-items-center" ng-if="!customerPartSearchCtrl.isSupersessionLoading" ng-repeat="part in customerPartSearchCtrl.supersessionHistory track by $index">
                                <div class="supersessionViewerPart">
                                    <small class="mb-0 font-weight-bold tooltip-title">{{ part.partNumber }}</small>
                                    <small class="mb-0">-</small>
                                    <small class="mb-0" ng-if="part.partDescription">
                                        {{ part.partDescription }}
                                      </small>
                                </div>
                                <i class="fa fa-long-arrow-up text-primary py-2" ng-class="{'hide': $last}"></i>
                            </div>
                        </div>
                    </div>
                </td>
                <td data-label="{{'ORDER.DESCRIPTION' | translate}}">
                    <span ng-class="{'text-muted': customerPartSearchCtrl.accordionSupersession[$index]}">{{ part.description }}</span>
                    <div class="cadTooltip" ng-if="part.partNote && part.partNote.length > 0">
                        <i class="fas fa-sticky-note cadBlue cursor-pointer"></i>
                        <ul class="list-unstyled cadTooltiptext mb-0 p-2">
                            <li ng-repeat="note in part.partNote track by $index">
                                <p>{{ note }}</p></li>
                        </ul>
                    </div>
                </td>
                <td ng-if="customerPartSearchCtrl.isCustomerSearchManualsOnly">
                    {{part.manualName}}
                </td>
    
                <td class="disableWordBreak" data-label="{{'ORDER.STOCK' | translate}}"
                ng-if="customerPartSearchCtrl.isPreviewStockLevelEnabled && !customerPartSearchCtrl.isDealerPlusCustomer">
                <!-- Show stock number if isStockWarehousesEnabled is true -->
                <span ng-if="customerPartSearchCtrl.isStockWarehousesEnabled">
                    <span ng-if="part.superseded">-</span>
                    <span class="text-success" ng-if="!part.superseded && part.stock >= 3">{{ part.stock }}</span>
                    <span class="text-warning" ng-if="!part.superseded && part.stock < 3 && part.stock > 0">{{ part.stock }}</span>
                    <span class="text-danger" ng-if="!part.superseded && (part.stock === null || part.stock < 1)">{{ part.stock || '0' }}</span>
                </span>
                <!-- Show icons if isStockWarehousesEnabled is false -->
                <span ng-if="!customerPartSearchCtrl.isStockWarehousesEnabled">
                    <span ng-if="part.superseded">-</span>
                    <span title="{{'ORDER.IN_STOCK' | translate}}" tooltip-trigger="outsideClick"
                        uib-tooltip="{{'ORDER.IN_STOCK' | translate}}" class="success-alert"
                        ng-if="!part.superseded && part.stock >= 3"><i class="fas fa-layer-group text-success pointer"></i></span>
                    <span title="{{'ORDER.LOW_STOCK' | translate}}" tooltip-trigger="outsideClick"
                        uib-tooltip="{{'ORDER.LOW_STOCK' | translate}}" class="warning-alert"
                        ng-if="!part.superseded && part.stock < 3 && part.stock > 0"><i
                            class="fas fa-layer-group text-warning pointer"></i></span>
                    <span title="{{'ORDER.STOCK_SUBJECT' | translate}}" tooltip-trigger="outsideClick"
                        uib-tooltip="{{'ORDER.STOCK_SUBJECT' | translate}}" class="warning-alert"
                        ng-if="!part.superseded && (part.stock === null || part.stock < 1)"><i
                            class="fas fa-layer-group text-danger pointer"></i></span>
                </span>
            </td>            
    
            <td data-label="{{'ORDER.QUANTITY' | translate}}">
                <span ng-if="part.superseded">-</span>
                <input class="priceInput" type="number" min="0" ng-model="part.quantity"
                    ng-change="customerPartSearchCtrl.quantityUpdated()" ng-if="!part.superseded" />
            </td>            
            
            <td class="disableWordBreak" data-label="{{'ORDER.ITEM_PRICE' | translate}}"
                ng-if="!customerPartSearchCtrl.hidePrice && part.price !== null">
                <span ng-if="!part.superseded">{{ part.price | currency:customerPartSearchCtrl.defaultCurrency.symbol:2 }}</span>
                <span ng-if="part.superseded">-</span>
            </td>
            
            <td data-label="{{'ORDER.ITEM_PRICE' | translate}}"
                ng-if="!customerPartSearchCtrl.hidePrice && part.price === null">
                <span ng-if="!part.superseded">-</span>
                <span ng-if="part.superseded">-</span>
            </td>
            
            <td class="disableWordBreak" data-label="{{'ORDER.TOTAL_PRICE' | translate}}"
                ng-if="!customerPartSearchCtrl.hidePrice && part.totalPrice !== 'TBC'">
                <span ng-if="!part.superseded">{{ part.totalPrice | currency:customerPartSearchCtrl.defaultCurrency.symbol:2 }}</span>
                <span ng-if="part.superseded">-</span>
            </td>
            
            <td class="disableWordBreak" data-label="{{'ORDER.TOTAL_PRICE' | translate}}"
                ng-if="!customerPartSearchCtrl.hidePrice && part.totalPrice === 'TBC'">
                <span ng-if="!part.superseded">-</span>
                <span ng-if="part.superseded">-</span>
            </td>
            
                <td ng-if="customerPartSearchCtrl.hasOrdersAccess">
                    <div class="d-flex justify-content-center cadSplitDropdown">
                        <div class="btn-group btn-hover">
                            <a ng-if="!part.superseded" class="btn primary text-white fixed-width-btn d-flex align-items-center justify-content-center"
                                ng-click="customerPartSearchCtrl.addToBasket($index)">
                                <div ng-hide="part.clicked" translate>GENERAL.ADD_TO_BASKET</div>
                                <div class="check-icon-wrapper" ng-show="part.clicked">
                                    <i class="fa fa-check"></i>
                                </div>
                            </a>
    
                            <button ng-if="part.superseded" class="btn primary-outline" ng-click="customerPartSearchCtrl.toggleSupersessionAccordion(part.maxSupersessionPartNumber, $index)"
                            translate>{{ part.superseded && customerPartSearchCtrl.accordionSupersession[$index] ? 'PART_SEARCH.HIDE_SUPERSESSION' : 'PART_SEARCH.VIEW_SUPERSESSION' }}</button>
                            
                            <div class="dropdown-split">
                                <button type="button" ng-class="{'primary-outline': part.superseded, 'primary': !part.superseded}" class="btn dropdown-toggle dropdown-toggle-split" data-toggle="dropdown"
                                    aria-haspopup="true" aria-expanded="false">
                                    <span class="sr-only">Toggle Dropdown</span>
                                </button>
                                <div class="dropdown-menu">
                                    <ul class="list-unstyled m-0 p-0">
                                        <li>
                                            <a href="javascript:void(0)" class="px-3 py-2 dark-secondary ng-binding"
                                                ng-click="part.superseded ? customerPartSearchCtrl.toggleSupersessionAccordion(part.maxSupersessionPartNumber, $index, $event) : customerPartSearchCtrl.addToBasket($index)">
                                                <i class="fa fa-fw fa-shopping-basket"></i>
                                                <span translate>{{ part.superseded && customerPartSearchCtrl.accordionSupersession[$index] ? 'PART_SEARCH.HIDE_SUPERSESSION' : (part.superseded ? 'PART_SEARCH.VIEW_SUPERSESSION' : 'GENERAL.ADD_TO_BASKET') }}</span>
                                            </a>
                                        </li>
                                        <li>
                                            <a href="javascript:void(0)" class="px-3 py-2 dark-secondary ng-binding"
                                                ng-click="customerPartSearchCtrl.whereUsedModal(part)">
                                                <i class="fa fa-fw fa-cubes"></i>
                                                {{'PART_SEARCH.WHERE_USED' | translate}}
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </td>  
            </tr>
    
            <!-- New row with the same number of columns -->
            <tr  ng-if="part.superseded && customerPartSearchCtrl.accordionSupersession[$index]" ng-class="{'borderBottomSupersession': customerPartSearchCtrl.accordionSupersession[$index]}" ng-repeat-end>
                <td data-label="{{'ORDER.PART_DETAILS' | translate}}">

                <div ng-if="part.supersessionDetails && part.supersessionDetails.length > 0">
                    {{ part.supersessionDetails[0].partNumber }}
                </div>

                    <div class="tooltip-container" ng-if="part.superseded && customerPartSearchCtrl.accordionSupersession[$index]">
                        <button class="tooltip-trigger" ng-click="customerPartSearchCtrl.toggleSupersessionHistory(part)">
                          <small class="underline">
                            {{ part.showSupersessionHistory ? 'PART_SEARCH.HIDE_SUPERSESSION_HISTORY' : 'PART_SEARCH.VIEW_SUPERSESSION_HISTORY' | translate }}
                          </small>
                        </button>
                        <div class="custom-tooltip" ng-class="{'show': part.showSupersessionHistory, 'last-item': $last}">
                        <div class="tooltip-arrow"></div>
                        <div ng-if="!customerPartSearchCtrl.isSupersessionLoading"class="flex supersessionHistoryHeader mb-3">
                            <h3 class="mb-0" translate>PART_SEARCH.SUPERSESSION_HISTORY</h3>
                        </div>
                        <div ng-if="customerPartSearchCtrl.isSupersessionLoading" class="p-4 text-center">
                            <span class="spinner-border text-primary" role="status" aria-hidden="true"></span>
                            <p translate>GENERAL.LOADING</p>
                        </div>
                        <div class="d-flex flex-column align-items-center" ng-if="!customerPartSearchCtrl.isSupersessionLoading" ng-repeat="part in customerPartSearchCtrl.supersessionHistory track by $index">
                            <div class="supersessionViewerPart">
                                <small class="mb-0 font-weight-bold tooltip-title">{{ part.partNumber }}</small>
                                <small class="mb-0">-</small>
                                <small class="mb-0" ng-if="part.partDescription">
                                    {{ part.partDescription }}
                                  </small>
                            </div>
                            <i class="fa fa-long-arrow-up text-primary py-2" ng-class="{'hide': $last}"></i>
                        </div>
                    </div>
                </div>
                </td>
                <td data-label="{{'ORDER.DESCRIPTION' | translate}}">
                    <span
                        ng-if="part.supersessionDetails[0].description && part.supersessionDetails[0].description.trim() !== ''">
                        {{ part.supersessionDetails[0].description }}
                    </span>
                    <span
                        ng-if="!part.supersessionDetails[0].description || part.supersessionDetails[0].description.trim() === ''">
                        -
                    </span>
                    <div class="cadTooltip"
                        ng-if="part.supersessionDetails[0].note && part.supersessionDetails[0].note.length > 0">
                        <i class="fas fa-sticky-note cadBlue cursor-pointer"></i>
                        <ul class="list-unstyled cadTooltiptext mb-0 p-2">
                            <li>
                                <p>{{ part.supersessionDetails[0].note }}</p>
                            </li>
                        </ul>
                    </div>
                </td>
                
                <td ng-if="customerPartSearchCtrl.isCustomerSearchManualsOnly">
                    {{ part.supersessionDetails[0].manualName }}
                </td>
                
                <td class="disableWordBreak" data-label="{{'ORDER.STOCK' | translate}}"
                    ng-if="customerPartSearchCtrl.isPreviewStockLevelEnabled && !customerPartSearchCtrl.isDealerPlusCustomer">
                    <span ng-if="customerPartSearchCtrl.isStockWarehousesEnabled">
                        <span class="text-success" ng-if="part.supersessionDetails[0].stock >= 3">{{
                            part.supersessionDetails[0].stock }}</span>
                        <span class="text-warning"
                            ng-if="part.supersessionDetails[0].stock < 3 && part.supersessionDetails[0].stock > 0">{{
                            part.supersessionDetails[0].stock }}</span>
                        <span class="text-danger"
                            ng-if="!part.supersessionDetails[0].stock || part.supersessionDetails[0].stock < 1">{{
                            part.supersessionDetails[0].stock || '0' }}</span>
                    </span>
                    <span ng-if="!customerPartSearchCtrl.isStockWarehousesEnabled">
                        <span title="{{'ORDER.IN_STOCK' | translate}}" tooltip-trigger="outsideClick"
                            uib-tooltip="{{'ORDER.IN_STOCK' | translate}}" class="success-alert"
                            ng-if="part.supersessionDetails[0].stock >= 3">
                            <i class="fas fa-layer-group text-success pointer"></i>
                        </span>
                        <span title="{{'ORDER.LOW_STOCK' | translate}}" tooltip-trigger="outsideClick"
                            uib-tooltip="{{'ORDER.LOW_STOCK' | translate}}" class="warning-alert"
                            ng-if="part.supersessionDetails[0].stock < 3 && part.supersessionDetails[0].stock > 0">
                            <i class="fas fa-layer-group text-warning pointer"></i>
                        </span>
                        <span title="{{'ORDER.STOCK_SUBJECT' | translate}}" tooltip-trigger="outsideClick"
                            uib-tooltip="{{'ORDER.STOCK_SUBJECT' | translate}}" class="warning-alert"
                            ng-if="!part.supersessionDetails[0].stock || part.supersessionDetails[0].stock < 1">
                            <i class="fas fa-layer-group text-danger pointer"></i>
                        </span>
                    </span>
                </td>
                
                <td data-label="{{'ORDER.QUANTITY' | translate}}">
                        <input class="priceInput" type="number" min="0" ng-model="part.quantity"
                            ng-change="customerPartSearchCtrl.quantityUpdated()" />
                </td>
                
                <td class="disableWordBreak" data-label="{{'ORDER.ITEM_PRICE' | translate}}"
                    ng-if="!customerPartSearchCtrl.hidePrice">
                    <span ng-if="part.supersessionDetails[0].price !== null">
                        {{ part.supersessionDetails[0].price | currency:customerPartSearchCtrl.defaultCurrency.symbol:2
                        }}
                    </span>
                    <span ng-if="part.supersessionDetails[0].price === null">-</span>
                </td>
                
                <td class="disableWordBreak" data-label="{{'ORDER.TOTAL_PRICE' | translate}}"
                    ng-if="!customerPartSearchCtrl.hidePrice">
                    <span ng-if="part.supersessionDetails[0].price !== null">
                        {{ part.supersessionDetails[0].price * part.quantity |
                        currency:customerPartSearchCtrl.defaultCurrency.symbol:2 }}
                    </span>
                    <span ng-if="part.supersessionDetails[0].price === null">-</span>
                </td>


                <td ng-if="customerPartSearchCtrl.hasOrdersAccess">
                    <div class="d-flex justify-content-center cadSplitDropdown">
                        <div class="btn-group btn-hover">
                            <a class="btn primary text-white fixed-width-btn d-flex align-items-center justify-content-center"
                            ng-click="customerPartSearchCtrl.addToBasket($index)">
                                <div ng-hide="part.clicked" translate>
                                    GENERAL.ADD_TO_BASKET
                                </div>
                                <div class="check-icon-wrapper" ng-show="part.clicked">
                                    <i class="fa fa-check"></i>
                                </div>
                            </a>
                            
                            <div class="dropdown-split">
                                <button type="button" class="btn primary dropdown-toggle dropdown-toggle-split" data-toggle="dropdown"
                                    aria-haspopup="true" aria-expanded="false">
                                    <span class="sr-only">Toggle Dropdown</span>
                                </button>
                                <div class="dropdown-menu">
                                    <ul class="list-unstyled m-0 p-0">
                                        <li>
                                            <a href="javascript:void(0)" class="px-3 py-2 dark-secondary ng-binding"
                                                ng-click="customerPartSearchCtrl.addToBasket($index)">
                                                <i class="fa fa-fw fa-shopping-basket"></i>
                                                <span translate>GENERAL.ADD_TO_BASKET</span>
                                            </a>
                                        </li>
                                        <li>
                                            <a href="javascript:void(0)" class="px-3 py-2 dark-secondary ng-binding"
                                                ng-click="customerPartSearchCtrl.whereUsedModal(part)">
                                                <i class="fa fa-fw fa-cubes"></i>
                                                {{'PART_SEARCH.WHERE_USED' | translate}}
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
            </tr>
        </tbody>
    </table>

</div>