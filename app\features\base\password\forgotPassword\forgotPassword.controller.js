(function () {
    'use strict';

    angular
        .module('app.base')
        .controller('ForgotPasswordController', ForgotPasswordController);

    ForgotPasswordController.$inject = ['userService', '$stateParams'];

    function ForgotPasswordController(userService, $stateParams) {
        var vm = this;

        vm.emailAddress = $stateParams.emailAddress;
        vm.manufacturerId = $stateParams.manufacturerId;
        vm.showSuccessMessage = false;
        vm.showErrorMessage = false;

        vm.forgotPasswordClicked = forgotPasswordClicked;

        function forgotPasswordClicked() {
            userService.forgotPassword(vm.emailAddress, vm.manufacturerId)
                .then(forgotPasswordSuccess, forgotPasswordFailed);
        }

        function forgotPasswordSuccess(response) {
            vm.showSuccessMessage = response;
            vm.showErrorMessage = !response;
        }

        function forgotPasswordFailed() {
            vm.showSuccessMessage = false;
            vm.showErrorMessage = true;
        }
    }
})();