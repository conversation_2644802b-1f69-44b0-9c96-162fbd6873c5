var express = require("express");
var socket = require("socket.io");

var app = express();

app.use(express.static("public"));

var server = app.listen(5001, function() {
  console.log("Listening to port 5001.");
});

var io = socket(server);

// Global variables to hold all usernames and rooms created
var usernames = {};
var rooms = ["global"];
var activityTrackForRoom = {};

io.on("connection", function(socket) {

  console.log("User connected to server.");

  socket.on("createUser", function(username) {
    socket.username = username;
    usernames[username] = username;
    socket.currentRoom = "global";
    socket.join("global");
    socket.emit("updateChat", "INFO", "You have joined global room");
    socket.broadcast
      .to("global")
      .emit("updateChat", "INFO", username + " has joined global room");
    io.sockets.emit("updateUsers", usernames);
    socket.emit("updateRooms", rooms, "global");
  });

  function setIntervalToTrackRoom(roomId){
    activityTrackForRoom[roomId] = {};
    activityTrackForRoom[roomId].lastSend = (new Date()).getTime();
    activityTrackForRoom[roomId].interval = setInterval(function () {
      if (activityTrackForRoom[roomId] && activityTrackForRoom[roomId].lastSend + 900000 < (new Date()).getTime()) {
        clearInterval(activityTrackForRoom[roomId].interval);
        delete activityTrackForRoom[roomId];
        var roomIndex = rooms.indexOf(roomId);
        rooms.splice(roomIndex, 1);
        for(key in  io.sockets.connected){
          if(io.sockets.connected[key].currentRoom === roomId){ 
              delete usernames[io.sockets.connected[key].username]
          }
        }
        io.sockets.emit("updateRooms", rooms, null);
      }
    }, 300000);
  }

  socket.on("sendMessage", function(data) {
    io.sockets
      .to(socket.currentRoom)
      .emit("updateChat", socket.username, data);
  });

  socket.on("stateChanged", function(data) {
    if(rooms.indexOf(socket.currentRoom) !== -1 && activityTrackForRoom[socket.currentRoom])
      activityTrackForRoom[socket.currentRoom].lastSend = (new Date()).getTime();
    if(usernames[socket.username])
    io.sockets
      .to(socket.currentRoom)
      .emit("updateState", socket.username, data);
  });

  socket.on("snapshotChanged", function(data) {
    io.sockets
      .to(socket.currentRoom)
      .emit("updateSnapshot", socket.username, data);
  });
  
  socket.on("startSharing", function() {
    io.sockets
      .to(socket.currentRoom)
      .emit("joinSharing", socket.username);
  });

  socket.on("createRoom", function(room, callback) {
    if (room != null) {
      if(rooms.indexOf(room) === -1){
        rooms.push(room);
      }
      io.sockets.emit("updateRooms", rooms, null);
      setIntervalToTrackRoom(room);
      callback(true);
    }
  });


  socket.on("updateRooms", function(room, callback) {
    socket.broadcast
      .to(socket.currentRoom)
      .emit("updateUsersRoom", socket.username, socket.currentRoom, "left");
    socket.leave(socket.currentRoom);
    var checkIfUserInvited = false;
    if(activityTrackForRoom[room] && activityTrackForRoom[room].meetingData){
        if(activityTrackForRoom[room].meetingData.host === socket.username)
            checkIfUserInvited = true;
        else activityTrackForRoom[room].meetingData.invitedUsers.forEach(user => {
          if(user.name === socket.username){
            checkIfUserInvited = true;
            return;
          }
        });
    }

    if(rooms.indexOf(room) === -1 || !checkIfUserInvited){
      callback(false);
    } else{
      socket.currentRoom = room;
      socket.join(room);
      socket.emit("updateUsersRoom", socket.username, room, "join");
      socket.broadcast
        .to(room)
        .emit("updateUsersRoom", socket.username, room, "join");
        callback(true);
    }
  });

  socket.on("setInvitedUsers", function(meetingData) {
    if(activityTrackForRoom[meetingData.roomId] === undefined){
      activityTrackForRoom[meetingData.roomId] = {};
    }
    activityTrackForRoom[meetingData.roomId].meetingData = meetingData;
  });
  
  socket.on("getInvitedUsers", function(roomId, callback) {
    if(activityTrackForRoom[roomId] !== undefined){
      callback(activityTrackForRoom[roomId].meetingData);
    }
  });

  socket.on("deleteRoom", function(isHost, callback) {
    socket.broadcast
    .to(socket.currentRoom)
    .emit("updateUsersRoom", socket.username, socket.currentRoom, "left");
      if(isHost){
          if(activityTrackForRoom[socket.currentRoom]){
            clearInterval(activityTrackForRoom[socket.currentRoom].interval);
            delete activityTrackForRoom[socket.currentRoom];
          }
          for(key in  io.sockets.connected){
            if(io.sockets.connected[key].currentRoom === socket.currentRoom){ 
                delete usernames[io.sockets.connected[key].username]
            io.sockets.emit("updateUsers", usernames);
            socket.broadcast.emit("updateChat", socket.username + " has disconnected");
            }
          }
          io.sockets.emit("leaveMeeting");
          var roomIndex = rooms.indexOf(socket.currentRoom);
          rooms.splice(roomIndex, 1);
      } else {
        delete usernames[socket.username]
      }
      socket.leave(socket.currentRoom);
      io.sockets.emit("updateRooms", rooms, null);
      callback(true);
  });

  socket.on("disconnect", function() {
    delete usernames[socket.username];
    io.sockets.emit("updateUsers", usernames);
    io.sockets.emit("updateUsersRoom", socket.username, socket.currentRoom, "left");
    socket.broadcast.emit("updateChat", socket.username + " has disconnected");
  });
});
