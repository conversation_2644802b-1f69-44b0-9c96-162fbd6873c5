(function () {
    'use strict';

    angular
        .module('app.parts')
        .controller('PartsUploadController', PartsUploadController);

    PartsUploadController.$inject = ['$scope', 'masterPartService', 'manufacturerProgressService', '$translate', 'userService', '$timeout', '$uibModal'];

    function PartsUploadController($scope, masterPartService, manufacturerProgressService, $translate, userService, $timeout, $uibModal) {
        var vm = this;

        var isLanguageCSVSelected = false;
        var isInventoryCSVSelected = false;
        var isPriceListCSVSelected = false;
        var languageTimeout;
        var inventoryTimeout;
        var priceListTimeout;

        vm.languageFileChanged = languageFileChanged;
        vm.uploadLanguages = uploadLanguages;
        vm.downloadLanguageTemplate = downloadLanguageTemplate;
        vm.exportLanguages = exportLanguages;
        vm.inventoryFileChanged = inventoryFileChanged;
        vm.uploadInventory = uploadInventory;
        vm.downloadInventoryTemplate = downloadInventoryTemplate;
        vm.exportInventory = exportInventory;
        vm.downloadCSV = downloadCSV;
        vm.priceListFileChanged = priceListFileChanged;
        vm.uploadPriceList = uploadPriceList;
        vm.showLanguagesTab = showLanguagesTab;
        vm.showInventoryTab = showInventoryTab;
        vm.showPriceTab = showPriceTab;
        vm.showConfigurationsTab = showConfigurationsTab;

        var languageProcess = ['MASTERPART_LANGUAGE_EXPORT', 'MASTERPART_LANGUAGE_UPLOAD'];
        var inventoryProcess = ['MASTERPART_INVENTORY_EXPORT', 'MASTERPART_INVENTORY_UPLOAD'];
        var priceListProcess = ['MASTERPART_PRICE_LIST_UPLOAD'];

        var SELECT_CSV, PLEASE_SELECT_CSV, UPLOAD_SUCCESS, ERROR, EXPORT, UPLOAD;
        $translate(['PARTS_UPLOAD.SELECT_CSV', 'PARTS_UPLOAD.PLEASE_SELECT_CSV', 'PARTS_UPLOAD.UPLOAD_SUCCESS', 'PARTS_UPLOAD.ERROR', 'PARTS_UPLOAD.EXPORT', 'PARTS_UPLOAD.UPLOAD'])
            .then(function (resp) {
                SELECT_CSV = resp["PARTS_UPLOAD.SELECT_CSV"];
                PLEASE_SELECT_CSV = resp["PARTS_UPLOAD.PLEASE_SELECT_CSV"];
                UPLOAD_SUCCESS = resp["PARTS_UPLOAD.UPLOAD_SUCCESS"];
                ERROR = resp["PARTS_UPLOAD.ERROR"];
                EXPORT = resp["PARTS_UPLOAD.EXPORT"];
                UPLOAD = resp["PARTS_UPLOAD.UPLOAD"];
            });

        vm.displayLanguageProgress = false;
        vm.displayInventoryProgress = false;
        vm.displayPriceListProgress = false;
        vm.displayPriceListHistory = true;
        vm.isPriceListEnabled = userService.getPriceListsEnabled();
        vm.activeTab = 'languages';

        initialize();

        function initialize() {
            checkForInProgressExports();
            fetchPriceList();
        }

        function fetchPriceList() {
            masterPartService.getPriceList(vm.priceListHistory)
                .then(fetchPriceListSuccess)
        }

        function showLanguagesTab() {
            vm.activeTab = 'languages';
        }

        function showInventoryTab() {
            vm.activeTab = 'inventory';
        }

        function showPriceTab() {
            vm.activeTab = 'price';
        }

        function showConfigurationsTab() {
            vm.activeTab = 'configurations';
        }

        function fetchPriceListSuccess(response) {
            vm.priceListUploadTime = response.data.updatedDate;
            vm.priceListHistory = response.data;
            if (vm.priceListHistory.updatedDate == null) {
                vm.displayPriceListHistory = false;
            }
            if (!$scope.$$phase) {
                $scope.$apply();
            }
        }

        function languageFileChanged(obj) {
            clearLanguageMessages();
            isLanguageCSVSelected = false;

            var elem = obj.target || obj.srcElement;
            if (elem.files.length > 0) {
                vm.languageFile = elem.files[0];

                var ext = vm.languageFile.name.substring(vm.languageFile.name.lastIndexOf('.') + 1);
                vm.languageFilename = vm.languageFile.name;
                $scope.$digest();
                if (ext.toUpperCase() !== "CSV") {
                    isLanguageCSVSelected = false;
                    vm.languageErrorMessage = SELECT_CSV;
                } else {
                    isLanguageCSVSelected = true;
                }
            }
        }

        function uploadLanguages() {
            clearLanguageMessages();

            if (isLanguageCSVSelected) {
                vm.uploading = true;
                masterPartService.uploadLanguages(vm.languageFile)
                    .then(uploadLanguagesSuccess, uploadLanguagesFailed);
            } else {
                vm.languageErrorMessage = PLEASE_SELECT_CSV
            }
        }

        function uploadLanguagesSuccess(resp) {
            vm.languageSuccessMessage = UPLOAD_SUCCESS;
            vm.languageUploading = false;
            manufacturerProgressService.fetchProgress(languageProcess)
                .then(languageProgressSuccess, languageFailed);
        }

        function uploadLanguagesFailed(error) {
            vm.languageErrorMessage = error.data.message;
            vm.languageUploading = false;
        }

        function clearLanguageMessages() {
            vm.languageSuccessMessage = "";
            vm.languageErrorMessage = "";
        }

        function exportLanguages() {
            clearLanguageMessages();
            vm.languageExporting = true;
            masterPartService.getMasterPartLanguages()
                .then(getLanguagesSuccess, languageFailed);
        }

        function getLanguagesSuccess() {
            vm.languageExporting = false;
            manufacturerProgressService.fetchProgress(languageProcess)
                .then(languageProgressSuccess, languageFailed);
        }

        function checkForInProgressExports() {
            manufacturerProgressService.fetchProgress(languageProcess)
                .then(languageProgressSuccess, languageFailed);
            manufacturerProgressService.fetchProgress(inventoryProcess)
                .then(inventoryProgressSuccess, languageFailed);
            manufacturerProgressService.fetchProgress(priceListProcess)
                .then(priceListProgressSuccess, languageFailed);
        }

        function languageProgressSuccess(response) {
            if (response.data && response.data.length > 0) {
                vm.displayLanguageProgress = true;
                vm.languageProcesses = response.data;

                //Check all found records are complete
                var processComplete = true;
                for (var i = 0; i < vm.languageProcesses.length; i++) {
                    if (vm.languageProcesses[i].status != 'COMPLETE') {
                        processComplete = false;
                        break;
                    } else {
                        processComplete = true;
                    }
                }
                populateLanguageProgressDisplayNames(vm.languageProcesses);

                if (processComplete) {
                    //IF all completed
                    //Stop polling to prevent spamming services
                    stopPollingLanguageUpdates()
                } else {
                    //IF NOT all completed
                    //Start timer to poll for updates every 10 seconds
                    beginPollingLanguageUpdates();
                }
            }
        }

        function populateLanguageProgressDisplayNames(processes) {
            for (var i = 0; i < processes.length; i++) {
                if (processes[i].process === 'MASTERPART_LANGUAGE_EXPORT') {
                    processes[i].processDisplayName = EXPORT;
                } else if (processes[i].process === 'MASTERPART_LANGUAGE_UPLOAD') {
                    processes[i].processDisplayName = UPLOAD;
                }
            }
        }

        function inventoryProgressSuccess(response) {
            if (response.data && response.data.length > 0) {
                vm.displayInventoryProgress = true;
                vm.inventoryProcesses = response.data;

                //Check all found records are complete
                var processComplete = true;
                for (var i = 0; i < vm.inventoryProcesses.length; i++) {
                    if (vm.inventoryProcesses[i].status != 'COMPLETE') {
                        processComplete = false;
                        break;
                    } else {
                        processComplete = true;
                    }
                }
                populateInventoryProgressDisplayNames(vm.inventoryProcesses);

                if (processComplete) {
                    //IF all completed
                    //Stop polling to prevent spamming services
                    stopPollingInventoryUpdates()
                } else {
                    //IF NOT all completed
                    //Start timer to poll for updates every 10 seconds
                    beginPollingInventoryUpdates();
                }
            }
        }

        function populateInventoryProgressDisplayNames(processes) {
            for (var i = 0; i < processes.length; i++) {
                if (processes[i].process === 'MASTERPART_INVENTORY_EXPORT') {
                    processes[i].processDisplayName = EXPORT;
                } else if (processes[i].process === 'MASTERPART_INVENTORY_UPLOAD') {
                    processes[i].processDisplayName = UPLOAD;
                }
            }
        }

        function beginPollingLanguageUpdates() {
            languageTimeout = $timeout(getLanguagesSuccess,15000);
        }

        function stopPollingLanguageUpdates() {
            clearTimeout(languageTimeout);
        }


        function beginPollingInventoryUpdates() {
            inventoryTimeout = $timeout(getInventorySuccess,15000);
        }

        function stopPollingInventoryUpdates() {
            clearTimeout(inventoryTimeout);
        }

        function downloadLanguageTemplate() {
            clearLanguageMessages();
            vm.languageDownloading = true;
            masterPartService.getMasterPartLanguagesHeaders()
                .then(getLanguageHeadersSuccess, languageFailed);
        }

        function getLanguageHeadersSuccess(response) {
            var content = response.data.toString();
            var filename = "Master Parts Template.csv";
            exportToCSV(content, filename);
            vm.languageDownloading = false;
        }

        function languageFailed(){
            vm.languageErrorMessage = ERROR;
            vm.languageExporting = false;
            vm.languageDownloading = false;
        }

        function inventoryFileChanged(obj) {
            clearInventoryMessages();
            isInventoryCSVSelected = false;

            var elem = obj.target || obj.srcElement;
            if (elem.files.length > 0) {
                vm.inventoryFile = elem.files[0];

                var ext = vm.inventoryFile.name.substring(vm.inventoryFile.name.lastIndexOf('.') + 1);
                vm.inventoryFilename = vm.inventoryFile.name;
                $scope.$digest();
                if (ext.toUpperCase() !== "CSV") {
                    isInventoryCSVSelected = false;
                    vm.inventoryErrorMessage = 'Please select a .CSV file ';
                } else {
                    isInventoryCSVSelected = true;
                }
            }
        }

        function uploadInventory() {
            clearInventoryMessages();

            if (isInventoryCSVSelected) {
                vm.uploading = true;
                masterPartService.uploadInventory(vm.inventoryFile)
                    .then(uploadInventorySuccess, uploadInventoryFailed);
            } else {
                vm.inventoryErrorMessage = "Please select a csv file to upload."
            }
        }

        function uploadInventorySuccess(resp) {
            vm.inventorySuccessMessage = "File has been uploaded successfully";
            vm.inventoryUploading = false;
            manufacturerProgressService.fetchProgress(inventoryProcess)
                .then(inventoryProgressSuccess, languageFailed);
        }

        function uploadInventoryFailed(error) {
            vm.inventoryErrorMessage = error.data.message;
            vm.inventoryUploading = false;
        }

        function clearInventoryMessages() {
            vm.inventorySuccessMessage = "";
            vm.inventoryErrorMessage = "";
        }

        function exportInventory() {
            clearInventoryMessages();
            vm.inventoryExporting = true;
            masterPartService.getInventory()
                .then(getInventorySuccess, inventoryFailed);
        }

        function downloadCSV(process, processName) {
            var filename = processName+'.csv';
            downloadCSVFileContents(process.s3Url, filename)
        }

        function downloadCSVFileContents(s3Url, filename) {
            var csvResponse = "";
            var xhr = new XMLHttpRequest();
            xhr.open("GET", s3Url);
            xhr.onload = function() {
                if (xhr.status == 200) {
                    csvResponse = xhr.responseText;
                    exportToCSV(csvResponse, filename);
                }
            }
            xhr.send();
        }

        function getInventorySuccess() {
            vm.inventoryExporting = false;
            manufacturerProgressService.fetchProgress(inventoryProcess)
                .then(inventoryProgressSuccess, languageFailed);
        }

        function downloadInventoryTemplate() {
            clearInventoryMessages();
            vm.inventoryDownloading = true;
            masterPartService.getInventoryHeaders()
                .then(getInventoryHeadersSuccess, inventoryFailed);
        }

        function getInventoryHeadersSuccess(response) {
            var content = response.data.toString();
            var filename = "Inventory Template.csv";
            exportToCSV(content, filename);
            vm.inventoryDownloading = false;
        }

        function inventoryFailed(){
            vm.inventoryErrorMessage = 'Service Error. Please try again.';
            vm.inventoryExporting = false;
            vm.inventoryDownloading = false;
        }

        function exportToCSV(content, filename) {
            var blob = new Blob([content], {type: 'text/csv;charset=utf-8;'});
            if (navigator.msSaveBlob) { // IE 10+
                navigator.msSaveBlob(blob, filename);
            } else {
                var link = document.createElement("a");
                if (link.download !== undefined) { // feature detection
                    // Browsers that support HTML5 download attribute
                    var url = URL.createObjectURL(blob);
                    link.setAttribute("href", url);
                    link.setAttribute("download", filename);
                    link.style.visibility = 'hidden';
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                }
            }
        }

        function priceListFileChanged(obj) {
            clearPriceListMessages();
            isPriceListCSVSelected = false;

            var elem = obj.target || obj.srcElement;
            if (elem.files.length > 0) {
                vm.priceListFile = elem.files[0];

                var ext = vm.priceListFile.name.substring(vm.priceListFile.name.lastIndexOf('.') + 1);
                vm.priceListFilename = vm.priceListFile.name;
                $scope.$digest();
                if (ext.toUpperCase() !== "CSV") {
                    isPriceListCSVSelected = false;
                    vm.priceListErrorMessage = 'Please select a .CSV file ';
                } else {
                    isPriceListCSVSelected = true;
                }
            }
        }
        
        function uploadPriceList() {
            vm.priceListExporting = true;
            clearPriceListMessages();

            if (isPriceListCSVSelected) {
                vm.priceListUploadTime = new Date();
                vm.uploading = true;
                masterPartService.uploadPriceList(vm.priceListFile)
                    .then(uploadPriceListSuccess, uploadPriceListFailed);
            } else {
                vm.priceListErrorMessage = "Please select a csv file to upload."
            }
        }

        function uploadPriceListSuccess(resp) {
            vm.priceListSuccessMessage = "File has been uploaded successfully";
            vm.priceListUploading = false;

            $timeout(getPriceListSuccess,1000);
        }

        function uploadPriceListFailed(error) {
            vm.priceListErrorMessage = error.data.message;
            vm.priceListUploading = false;
            vm.priceListExporting = false;
        }

        function clearPriceListMessages() {
            vm.priceListSuccessMessage = "";
            vm.priceListErrorMessage = "";
        }

        function priceListProgressSuccess(response) {
            if (response.data && response.data.length > 0) {
                vm.displayPriceListProgress = true;
                vm.priceListProcesses = response.data;

                var areAllProcessComplete = true;
                for (var i = 0; i < vm.priceListProcesses.length; i++) {
                    if (vm.priceListProcesses[i].status != 'COMPLETE') {
                        areAllProcessComplete = false;
                        break;
                    }
                }
                populatePriceListProgressDisplayNames(vm.priceListProcesses);

                if (areAllProcessComplete) {
                    stopPollingPriceListUpdates()
                } else {
                    beginPollingPriceListUpdates();
                }
            }
        }

        function beginPollingPriceListUpdates() {
            $timeout(getPriceListSuccess, 15000);
            vm.priceListExporting = true;
        }

        function getPriceListSuccess(response) {
            manufacturerProgressService.fetchProgress(priceListProcess)
                .then(priceListProgressSuccess, languageFailed);
        }
        
        function stopPollingPriceListUpdates () {
            vm.priceListExporting = false;
        }
        
        function populatePriceListProgressDisplayNames(processes) {
            for (var i = 0; i < processes.length; i++) {
                if (processes[i].process === 'MASTERPART_PRICE_LIST_UPLOAD') {
                    processes[i].processDisplayName = UPLOAD;
                }
            }
        }
    }
})();
