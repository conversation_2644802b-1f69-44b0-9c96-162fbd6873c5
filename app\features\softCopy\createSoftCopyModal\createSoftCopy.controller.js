(function () {
    'use strict';

    angular
        .module('app.viewable')
        .controller('CreateSoftCopyController', CreateSoftCopyController);

    CreateSoftCopyController.$inject = ['softCopyService',  '$uibModalInstance','model'];
    function CreateSoftCopyController(softCopyService, $uibModalInstance, model) {

        var vm = this;
        vm.createSoftCopy = createSoftCopy;
        vm.cancel = $uibModalInstance.dismiss;
        vm.model = model;
        vm.settings = {
            copySnapshots: false,
            softCopyName: null
        };
        vm.submitDisabled = false;

        initialize();
        function initialize() {
        }
        
        function createSoftCopy() {
            vm.submitDisabled = true;
            softCopyService.createSoftCopy(vm.model.modelId, vm.settings)
                .then(createSoftCopySuccess, createSoftCopyFailure);
        }

        function createSoftCopySuccess() {
            $uibModalInstance.close();
        }

        function createSoftCopyFailure(error) {
            console.log(error);
        }


       }
})();
