(function () {
    'use strict';

    angular
        .module('app.viewer')
        .factory('viewerVariablesService', viewerVariablesService);

    viewerVariablesService.$inject = [];

    function viewerVariablesService() {

        var modelId;
        var visibleNodes;

        return {

            getModelId: getModelId,
            setModelId: setModelId,
            getVisibleNodes: getVisibleNodes,
            setVisibleNodes: setVisibleNodes
        };

        function getModelId() {
            return modelId;
        }

        function setModelId(value) {
            modelId = value;
        }

        function getVisibleNodes() {
            return visibleNodes;
        }

        function setVisibleNodes(value) {
            visibleNodes = value;
        }
    }
})();
