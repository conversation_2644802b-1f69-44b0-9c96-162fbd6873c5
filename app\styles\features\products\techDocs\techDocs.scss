.techDocs_container{
  border-radius: 10px;
}

.techDocs {
  border: 1px solid #ccc;
  border-collapse: collapse;
  margin: 0;
  padding: 0;
  width: 100%;
  table-layout: fixed;
}

.techDocs caption {
  font-size: 1.5em;
  margin: 0.5em 0 0.75em;
}

.techDocs_heading{

  background: #F2F6F9;

}

.techDocs_buttons{

  background: white;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px

}

.techDocs_body tr {
  border: 1px solid #ddd;
  padding: 0.35em;
  background: white;
}

.techDocs th,
.techDocs td {
  padding: 1em;
  word-break: break-word;
}

.techDocs th {
  font-size: 0.85em;
  letter-spacing: 0.1em;
  text-transform: uppercase;
}

.search-panel {
  display: inline-block;
  width: auto;
}

.searchgroup {
  width: 340px;
  display: inline-block;
}

.techDocs_pageNumber{

  background: white;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px

}

.techDocs-filter-panel{

  width: 100%;
  padding: 1.5em;
  background: white;

  filter-header {
    font-size: 0.8em;
    text-transform: uppercase;
    font-weight: 700;
  }
  .first-filter {
  }

  .filter-option {
    display: inline-block;
    margin-right: $spacing*2;
    margin-top: $spacing;
  }
  .filter-buttons {
    margin-top: $spacing*2;

    .btn:first-child{
      margin-right: $spacing;
    }
  }

}

@media screen and (max-width: 800px) {
  .techDocs {
    border: 0;
  }

  .techDocs caption {
    font-size: 1.3em;
  }

  .techDocs thead {
    border: none;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
  }

  .techDocs tr {
    border-bottom: 3px solid #ddd;
    display: block;
    margin-bottom: 0.625em;
  }

  .techDocs td {
    border-bottom: 1px solid #ddd;
    display: block;
    font-size: 1em;
  }

  .techDocs td::before {
    /*
    * aria-label has no advantage, it won't be read inside a table
    content: attr(aria-label);
    */
    content: attr(data-label);
    font-weight: bold;
    text-transform: uppercase;
    padding-right:10px;

  }

  .techDocs td:last-child {
    border-bottom: 0;
  }

  .products-filter {

    margin-left: auto;

  }

  .order-details-holder {
    width: 100%;
    float: left;

  }

}
