class CreateCourierDetailsController {
    static $inject = [
        'userService',
        'ordersService',
        '$translate',
        '$uibModalInstance',
        'headerBannerService',
        'shippingRequirements',
        'manufacturerSubEntityId'
    ];

    constructor(
        private userService: any,
        private ordersService: any,
        private $translate: any,
        private $uibModalInstance: any,
        private headerBannerService: any,
        private shippingRequirements: any[],
        private manufacturerSubEntityId: number
    ) {
        this.shippingRequirements = this.shippingRequirements || [];
        this.manufacturerSubEntityId = manufacturerSubEntityId;
    }

    preferredCourier: string = '';
    courierNumber: string = '';

    fetchShippingRequirements(manufacturerSubEntityId: number): void { // This is the function that fetches the shipping requirements
        this.ordersService.getShippingRequirements(manufacturerSubEntityId).then((response: any) => {
            this.shippingRequirements = response.data.shippingRequirements;
        }, (error: any) => {
            console.error("Error fetching shipping requirements:", error);
        });
    }

    saveCourierDetails(): void {
        const manufacturerSubEntityId = this.userService.getManufacturerSubEntityId();

        const newShippingRequirement = {
            preferredCourier: this.preferredCourier,
            courierNumber: this.courierNumber
        }

        this.ordersService.postShippingRequirements(this.manufacturerSubEntityId, newShippingRequirement)
            .then(this.postShippingRequirementsSuccess.bind(this), this.postShippingRequirementsFailure.bind(this));
    }

    private postShippingRequirementsSuccess(response: any): void {
        const shippingRequirement = {
            preferredCourier: this.preferredCourier,
            courierNumber: this.courierNumber,
            shippingRequirementId: response.data.shippingRequirementId
        };

        this.$uibModalInstance.close(shippingRequirement);
    }

    private postShippingRequirementsFailure(error: any): void {
        this.headerBannerService.setNotification('ERROR', error.data.error, 10000);
    }

    cancel(): void {
        this.$uibModalInstance.dismiss();
    }
}

angular.module('app.shared').controller('CreateCourierDetailsController', CreateCourierDetailsController);
