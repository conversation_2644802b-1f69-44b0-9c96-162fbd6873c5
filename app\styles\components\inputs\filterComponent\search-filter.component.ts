class SearchFilterController {
    stateName: string = '';
    value: string = '';
    placeholderKey: string = '';

    onSearchChange!: () => void;

    static $inject = ['$window', '$transitions'];
    constructor(private $window: angular.IWindowService, private $transitions: any) {
        // Listen to the onStart hook of the $transitions service
        this.$transitions.onStart({}, (transition: any) => {
            const fromStateUrl = transition.$from().url.toString();
            const toStateUrl = transition.$to().url.toString();

            // Define the route groups
            const productRoutes = [
                '/products/',
                '/productsCatalogue',
                '/techDocs',
                '/videos',
                '/manufacturerViewer/',
                '/adminViewer/'
            ];
            const customersRoutes = [
                'customers',
            ];
            const partSearchRoutes = [
                'partsSearch',
                'masterPart'
            ];
            const customerProductsRoutes = [
                'p-products',
                'customerViewables',
                'customerKits',
                'customerTechDocs',
                'customerVideos',
                'customerViewer',
                'customerPdfViewer'
            ];
            const orderRoutes = [
                '/enquiries',
                '/quotations',
                '/liveorders',
                '/historicalorders',
                '/enquiry',
                '/quotation',
                '/liveorder',
                '/historicalorder'
            ];
            // Check which group the from and to states belong to
            const isFromProduct = productRoutes.some(route => fromStateUrl.includes(route));
            const isToProduct = productRoutes.some(route => toStateUrl.includes(route));

            const isFromOrder = orderRoutes.some(route => fromStateUrl.includes(route));
            const isToOrder = orderRoutes.some(route => toStateUrl.includes(route));

            const isFromCustomers = customersRoutes.some(route => fromStateUrl.includes(route));
            const isToCustomers = customersRoutes.some(route => toStateUrl.includes(route));

            const isFromPartSearch = partSearchRoutes.some(route => fromStateUrl.includes(route));
            const isToPartSearch = partSearchRoutes.some(route => toStateUrl.includes(route));

            const isFromCustomerProductsRoutes = customerProductsRoutes.some(route => fromStateUrl.includes(route));
            const isToCustomerProductsRoutes = customerProductsRoutes.some(route => toStateUrl.includes(route));

            if ((isFromProduct && isToProduct) || (isFromOrder && isToOrder) || (isFromCustomers && isToCustomers) || (isFromPartSearch && isToPartSearch) || (isFromCustomerProductsRoutes && isToCustomerProductsRoutes)) {
                // If moving within the same group, retain the filter value
                return;
            } else {
                // If transitioning between different groups, clear session storage and UI text
                this.clearPersistentSearch();
                this.value = '';
            }
        });
    }

    $onInit() {
        const searchValueKey = `searchValue-${this.stateName}`;
        this.value = this.$window.sessionStorage.getItem(searchValueKey) || '';
    }

    searchFilterChange() {
        const searchValueKey = `searchValue-${this.stateName}`;
        this.$window.sessionStorage.setItem(searchValueKey, this.value);

        // Call the parent's callback, if provided
        if (this.onSearchChange) {
            this.onSearchChange();
        }
    }

    clearSearch() {
        this.value = '';
        this.searchFilterChange(); // Update the persistent value
    }

    clearPersistentSearch() {
        const searchValueKey = `searchValue-${this.stateName}`;
        this.$window.sessionStorage.removeItem(searchValueKey);
    }
}


angular
    .module('app.customer')
    .component('searchFilter', {
        bindings: {
            stateName: '@',
            value: '=',
            onSearchChange: '&',
            placeholderKey: '@'
        },
        controller: SearchFilterController,
        template: `
<div class="search-filter py-4 filter-icon-right">
  <div class="input-group mb-0 px-0 has-search" >
      <span class="fa fa-search h-100 px-3 d-flex align-items-center form-control-feedback"></span>
    <input type="text" class="form-control mr-0" ng-model="$ctrl.value" ng-change="$ctrl.searchFilterChange()" placeholder="{{ $ctrl.placeholderKey | translate }}" aria-label="Username" aria-describedby="basic-addon1">
    <div class="input-group-append">
      <span class="form-control-clear input-group-text border-0" ng-click="$ctrl.clearSearch()"><i class="fa fa-times" ng-if="$ctrl.value"></i></span>
    </div>
  </div>
</div>
  `,
    });