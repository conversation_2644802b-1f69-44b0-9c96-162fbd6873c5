(function () {
    'use strict';

    angular
        .module('app.viewable')
        .controller('WorkInstructionsController', WorkInstructionsController);

    WorkInstructionsController.$inject = ['viewableService', '$stateParams', '$uibModal', '$interval', '$scope', '$state', '$filter', 'modelService', 'workInstructionsService'];

    function WorkInstructionsController(viewableService, $stateParams, $uibModal, $interval, $scope, $state, $filter, modelService, workInstructionsService) {

        var vm = this;

        vm.modelName = $stateParams.modelName;
        vm.modelId = $stateParams.modelId;
        vm.model = {};
        vm.machineName = $stateParams.machineName;
        vm.workInstructionsList = [];
        vm.isWorkInstructionsLoaded = false;
        vm.count = 8;
        vm.currentPage = 1;
        vm.itemPerPage = 8;
        vm.start = 0;
        vm.endRecord = vm.itemPerPage;
        vm.sortReverse = false;
        vm.isHeaderListLoaded = false;
        vm.missingTranslationWarnings = [];
        vm.isOpened = false;
        vm.showLoader = false;

        vm.pageChanged = pageChanged;
        vm.backToModels = backToModels;
        vm.openWorkInstructionsViewerPage = openWorkInstructionsViewerPage;
        vm.createWorkInstructions = createWorkInstructions;
        vm.editWorkInstructions = editWorkInstructions;
        vm.deleteWorkInstructions = deleteWorkInstructions;

        vm.displayAdvancedFilter = false;
        vm.filterValue = {};

        vm.createdByUsers = [''];
        vm.filter_createdBy = "";
        vm.statuses = [''];
        vm.filter_status = "";

        vm.toggleAdvancedFilter = toggleAdvancedFilter;
        vm.searchFilterChange = searchFilterChange;
        vm.applyFilter = applyFilter;
        vm.clearFilter = clearFilter;

        vm.sortReverse = true;
        vm.viewable_sort = 'modelId';

        initialize();

        function initialize() {
            fetchModel();
            fetchWorkInstructions();
        }

        function fetchModel() {
            modelService.fetchModel(vm.modelId)
                .then(fetchModelSuccess)
                .catch(fetchModelFailed);
        }

        function fetchModelSuccess(response) {
            vm.model = response.data;
        }


        function fetchModelFailed(error) {
            console.log(error);
            //TODO add proper error handling
        }

        function fetchWorkInstructions() {
            workInstructionsService.fetchWorkInstructions(vm.modelId)
                .then(fetchWorkInstructionsSuccess)
                .catch(fetchWorkInstructionsFailed);
        }

        function fetchWorkInstructionsSuccess(response) {
            vm.workInstructionsList = response.data;
            vm.isWorkInstructionsLoaded = true;
        }

        function fetchWorkInstructionsFailed(error) {
            console.log(error);
            vm.isWorkInstructionsLoaded = true;
            //TODO add proper error handling
        }

        function backToModels() {
            $state.go('productsModels', {
                productId: $stateParams.productId,
                machineName: vm.machineName
            });
        }

        vm.sortBy = [
            {name: 'modelName', value: 'Model Name'},
            {name: 'filename', value: 'File Name'},
            {name: 'autodeskStatus', value: 'Model Status'},
            {name: 'createdDate', value: 'Create Date'},
            {name: 'createdByUserFirstName', value: 'Created By'}
        ];

        function pageChanged() {
            vm.start = ((vm.currentPage - 1) * vm.itemPerPage);
        }

        function openWorkInstructionsViewerPage(workInstructions) {
            $state.go('manufacturerWorkInstructionsViewer', {
                productId: $stateParams.productId,
                machineName: vm.machineName,
                modelId: vm.modelId,
                modelName: vm.modelName,
                viewableId: workInstructions.viewableId,
                viewableName: workInstructions.name,
                autodeskURN: vm.model.autodeskUrn,
                translateType: vm.model.translateType,
                workInstructionsId: workInstructions.id
            })
        }

        function createWorkInstructions() {
            $uibModal.open({
                templateUrl: 'features/workInstructions/createWorkInstructionsModal/createWorkInstructions.html',
                controller: 'CreateWorkInstructionsController',
                controllerAs: 'createWorkInstructionsCtrl',
                size: 'md',
                resolve: {
                    model: function () {
                        return vm.model;
                    }
                }
            }).result.then(function () {
                fetchWorkInstructions();
            });
        }

        function editWorkInstructions(workInstructions) {
            $uibModal.open({
                templateUrl: 'features/workInstructions/editWorkInstructionsModal/editWorkInstructions.html',
                controller: 'EditWorkInstructionsController',
                controllerAs: 'editWorkInstructionsCtrl',
                size: 'md',
                resolve: {
                    workInstructions: function () {
                        return workInstructions;
                    }
                }
            }).result.then(function () {
                fetchWorkInstructions();
            });
        }

        function deleteWorkInstructions(workInstructions) {
            $uibModal.open({
                templateUrl: 'features/workInstructions/deleteWorkInstructionsModal/deleteWorkInstructions.html',
                controller: 'DeleteWorkInstructionsController',
                controllerAs: 'deleteWorkInstructionsCtrl',
                size: 'md',
                resolve: {
                    workInstructions: function () {
                        return workInstructions;
                    }
                }
            }).result.then(function () {
                fetchWorkInstructions();
            });
        }

        function toggleAdvancedFilter() {
            vm.displayAdvancedFilter = !vm.displayAdvancedFilter;
        }

        function searchFilterChange() {
            updateTotalItemCount();
        }

        function updateTotalItemCount() {
            var textFilter = $filter('filter')(vm.workInstructionsList, vm.searchValue);
            vm.totalItems = $filter('filter')(textFilter, vm.filterValue, true).length;
        }

        function applyFilter() {
            vm.filterValue = {};

            if (vm.filter_createdBy) {
                vm.filterValue.createdByUserFullName = vm.filter_createdBy;

            }
            if (vm.filter_status) {
                vm.filterValue.autodeskStatusDisplay = vm.filter_status;
            }

            updateTotalItemCount();
        }

        function clearFilter() {

            vm.filterValue = {};
            vm.totalItems = vm.modelList.length;

            vm.filter_createdBy = vm.createdByUsers[0];
            vm.filter_status = vm.statuses[0];

            var searchBox = document.getElementById("searchInput");
            searchBox.value = "";
            vm.searchValue = "";

            console.log("Filter cleared");
        }

    }
})();
