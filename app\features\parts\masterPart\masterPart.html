<section class="p-5 inventory">
  <section
    class="inventory_header d-flex flex-wrap justify-content-between mx-md-4"
  >
    <div class="d-flex justify-content-center align-items-center">
      <h1 class="mb-0 mr-2">{{masterPartCtrl.partNumber}}</h1>
    </div>
    <div class="d-flex justify-content-center cadGap align-items-center">
      <div>
        <div ng-attr-data-toggle="{{ masterPartCtrl.inSupersession ? 'tooltip' : undefined }}"
          ng-attr-title="{{ masterPartCtrl.inSupersession ? ('MASTER_PART.DISABLED_SUPERSESSION_CHAIN' | translate) : undefined }}"
          data-placement="top">
          <button class="btn danger d-flex justify-content-between" ng-click="masterPartCtrl.deleteMasterPart()"
            ng-disabled="masterPartCtrl.inSupersession" translate>
            MASTER_PART.DELETE_PART
          </button>
        </div>
        </div>
        </div>
  </section>

  <!-- ----------------------------------------------- INVENTORY  -------------------------------------------------------------->
  <section
    class="d-flex flex-wrap no-gutters mt-4 mx-md-4"
    style="grid-gap: 2em"
  >
    <div class="customDivStyling py-0 text-center col">
      <div
        class="card-header font-weight-bold d-flex justify-content-center align-items-center position-relative py-4"
      >
        <span class="text-center">{{"MASTER_PART.STOCK" | translate}}</span>
        <button
          type="button"
          aria-label="Edit Stock"
          uib-tooltip="{{'MASTER_PART.STOCK' | translate}}"
          ng-click="masterPartCtrl.editStock()"
          class="btn secondary p-2 px-3 mr-3 comments iconRightMp"
        >
          <i class="fa fa-fw fa-pencil fa-lg fa-fw"></i>
        </button>
      </div>

      <div class="card-body" ng-if="masterPartCtrl.stock">
        {{masterPartCtrl.stock}}
      </div>
      <div class="card-body" ng-if="!masterPartCtrl.stock">N/A</div>
    </div>

    <div
      class="customDivStyling py-0 text-center col"
      ng-hide="masterPartCtrl.isPriceListEnabled"
    >
      <div
        class="card-header font-weight-bold d-flex justify-content-center align-items-center position-relative py-4"
      >
        <span class="text-center">{{"MASTER_PART.PRICE" | translate}}</span>
        <button
          type="button"
          aria-label="Edit Price"
          uib-tooltip="{{'MASTER_PART.PRICE' | translate}}"
          ng-click="masterPartCtrl.editPrice()"
          class="btn secondary p-2 px-3 mr-3 comments iconRightMp"
        >
          <i class="fa fa-fw fa-pencil fa-lg fa-fw"></i>
        </button>
      </div>

      <div class="card-body" ng-if="masterPartCtrl.price">
        {{masterPartCtrl.price | currency:masterPartCtrl.currency.symbol:2}}
      </div>
      <div class="card-body" ng-if="!masterPartCtrl.price">N/A</div>
    </div>
    <div class="customDivStyling py-0 text-center col-md col-12">
      <div
        class="card-header font-weight-bold d-flex justify-content-center align-items-center position-relative py-4"
      >
        <span class="text-center">{{"MASTER_PART.TECH_INFO" | translate}}</span>
        <button
          type="button"
          aria-label="Edit Tech Doc"
          ng-disabled="masterPartCtrl.isSuperseded"
          uib-tooltip="{{'MASTER_PART.TECH_INFO' | translate}}"
          ng-click="masterPartCtrl.editLinkedTechDocs()"
          class="btn secondary p-2 px-3 mr-3 comments iconRightMp"
        >
          <i class="fa fa-fw fa-pencil fa-lg fa-fw"></i>
        </button>
      </div>

      <div class="card-body" ng-if="masterPartCtrl.hasLinkedTechDocs" translate>
        MASTER_PART.LINKED
      </div>
      <div class="card-body" ng-if="!masterPartCtrl.hasLinkedTechDocs">N/A</div>
    </div>
  </section>

  <section class="d-flex no-gutters flex-wrap">
    <!------------------------------------------------- PRICE LISTS -------------------------------------------------------------->

    <div
      class="col-12 col-md-6 col-xl-4 py-4 p-md-4 customDivContainer"
      ng-show="masterPartCtrl.isPriceListEnabled"
    >
      <div class="customDivStyling customDivScrollable">
        <div class="customCardBody">
          <h2 class="mb-2">
            <span translate>MASTER_PART.PRICE_LIST</span>
          </h2>
        </div>
        <table class="table table-bordered">
          <thead>
            <tr>
              <th translate>MASTER_PART.PRICE_LIST_IDENTIFIER</th>
              <th translate>MASTER_PART.PRICE</th>
              <th></th>
            </tr>
          </thead>
          <tbody>
            <tr ng-repeat="priceList in masterPartCtrl.priceLists">
              <td
                data-label="{{'MASTER_PART.PRICE_LIST_IDENTIFIER' | translate}}"
              >
                {{priceList.identifier}}
              </td>
              <td data-label="{{'MASTER_PART.PRICE' | translate}}">
                {{priceList.price | currency:priceList.currencySymbol:2}}
              </td>
              <td>
                <button
                  class="btn btn-primary btn-small pull-right mr-8"
                  ng-click="masterPartCtrl.editPriceList($index)"
                  translate
                >
                  GENERAL.EDIT
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- ----------------------------------------------- TRANSLATIONS -------------------------------------------------------------->
    <div class="col-12 col-md-6 col-xl-4 py-4 p-md-4 customDivContainer">
      <div class="customDivStyling">
        <div class="customCardBody">
          <h2 class="mb-2">
            <span translate>MASTER_PART.DESCS</span>
          </h2>
        </div>

        <table class="table table-bordered">
          <thead>
            <tr>
              <th translate>MASTER_PART.LANGUAGE</th>
              <th translate>MASTER_PART.TRANSLATION</th>
              <th></th>
            </tr>
          </thead>
          <tbody>
            <tr ng-repeat="translation in masterPartCtrl.translations">
              <td data-label="{{'MASTER_PART.LANGUAGE' | translate}}">
                {{translation.displayText}}
              </td>
              <td
                class="word-break"
                data-label="{{'MASTER_PART.TRANSLATION' | translate}}"
              >
                {{translation.description}}
              </td>
              <td class="pr-4">
                <div class="btn-group">
                  <a
                    href=""
                    class="btn xsmall secondary main-action"
                    ng-if="translation.description"
                    ng-click="masterPartCtrl.editTranslation(translation)"
                    translate
                    >MASTER_PART.EDIT</a
                  >
                  <a
                    href=""
                    class="btn xsmall secondary main-action"
                    ng-if="!translation.description"
                    ng-click="masterPartCtrl.createNewTranslation(translation)"
                    translate
                    >MASTER_PART.CREATE</a
                  >
                  <div
                    href=""
                    class="btn xsmall secondary dropdown-toggle"
                    data-toggle="dropdown"
                    aria-haspopup="true"
                    aria-expanded="false"
                  >
                    <div class="sub-popup">
                      <ul class="more-options">
                        <li
                          title="Create"
                          ng-if="translation.description === null"
                        >
                          <a
                            href=""
                            class="dark-secondary"
                            ng-click="masterPartCtrl.createNewTranslation(translation)"
                            ><i class="fa fa-fw fa-plus"></i
                            >{{"MASTER_PART.CREATE_TRANS" | translate}}</a
                          >
                        </li>
                        <li
                          title="Edit"
                          ng-if="translation.description !== null"
                        >
                          <a
                            href=""
                            class="dark-secondary"
                            ng-click="masterPartCtrl.editTranslation(translation)"
                            ><i class="fa fa-fw fa-pencil"></i
                            >{{"MASTER_PART.EDIT_TRANS" | translate}}</a
                          >
                        </li>
                        <li title="Delete">
                          <a
                            href=""
                            class="dark-secondary delete"
                            ng-click="masterPartCtrl.deleteTranslation(translation.languageId)"
                            ><i class="fa fa-fw fa-window-close"></i>
                            {{"MASTER_PART.DELETE_TRANS" | translate}}</a
                          >
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- ----------------------------------------------- KITS -------------------------------------------------------------->
    <div
      class="col-12 col-md-6 col-xl-4 py-4 p-md-4 breakpointMasterPart customDivContainer"
    >
      <div class="customDivStyling flexible-container customDivScrollable">
        <div class="customCardBody ">
          <h2 class="mb-2">
            <span translate>MASTER_PART.KITS</span>
          </h2>
          <p translate>MASTER_PART.KIT_DESCRIPTION</p>
        </div>

        <div ng-if="masterPartCtrl.kits.length > 0">
          <table class="table table-bordered mt-3">
            <thead>
              <tr>
                <th translate>MASTER_PART.KIT_NUMBER</th>
                <th translate>MASTER_PART.DESCRIPTION</th>
                <th></th>
              </tr>
            </thead>
            <tbody>
              <tr ng-repeat="kit in masterPartCtrl.kits">
                <td data-label="{{'MASTER_PART.KIT_NUMBER' | translate}}">
                  <p>{{kit.masterPartNumber}}</p>
                </td>
                <td data-label="{{'MASTER_PART.DESCRIPTION' | translate}}">
                  <p>{{kit.description}}</p>
                </td>
                <td class="pr-4">
                  <!-- Commented out dropdown button group -->
                  <!-- <div class="btn-group">
                    <a
                      href=""
                      class="btn xsmall secondary main-action"
                      ng-click="masterPartCtrl.editKit(kit.id)"
                      translate
                      >MASTER_PART.EDIT</a
                    >
                    <div
                      href=""
                      class="btn xsmall secondary dropdown-toggle"
                      data-toggle="dropdown"
                      aria-haspopup="true"
                      aria-expanded="false"
                    >
                      <div class="sub-popup">
                        <ul class="more-options">
                          <li title="Edit">
                            <a
                              href=""
                              class="dark-secondary"
                              ng-click="masterPartCtrl.editKit(kit.id)"
                              ><i class="fa fa-fw fa-pencil"></i
                              >{{"MASTER_PART.EDIT_KIT" | translate}}</a
                            >
                          </li>
                          <li title="Delete">
                            <a
                              href=""
                              class="dark-secondary delete"
                              ng-click="masterPartCtrl.deleteKit(kit.id)"
                              ><i class="fa fa-fw fa-window-close"></i>
                              {{"MASTER_PART.DELETE_KIT" | translate}}</a
                            >
                          </li>
                          <li title="Remove">
                            <a
                              href=""
                              class="dark-secondary delete"
                              ng-click="masterPartCtrl.removePartFromKit(kit.id)"
                              ><i class="fa fa-fw fa-minus-square"></i>
                              {{"MASTER_PART.REMOVE_KIT" | translate}}</a
                            >
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div> -->
                  <button
                    class="btn btn-primary"
                    ng-click="masterPartCtrl.editKit(kit.id)"
                    translate
                  >
                    MASTER_PART.EDIT
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

          <div ng-if="masterPartCtrl.isSuperseded" class="mb-4">
            <span class="small-text">{{"MASTER_PART.DISABLED_SUPERSEDED" | translate}}</span>
          </div>
          <div class="button-container-bottom-right">
            <button
              class="btn btn-primary"
              ng-click="masterPartCtrl.createKit()"
              ng-disabled="masterPartCtrl.isSuperseded"
              translate
            >
              MASTER_PART.CREATE_KIT
            </button>
            <!-- Temporarily Hidden Until Endpoint for Kits is updated -->
            <!-- <button
              class="btn btn-secondary"
              ng-click="masterPartCtrl.addToKit()"
              translate
            >
              MASTER_PART.ADD_TO_KIT
            </button> -->
          </div>
      </div>
    </div>

    <!-- ----------------------------------------------- Supersession History  -------------------------------------------------------------->
    <div class="col-12 col-md-6 col-xl-4 py-4 p-md-4 customDivContainer">
      <div class="customDivStyling flexible-container">
        <div class="customCardBody ">
          <h2 class="mb-2">
            <span translate>MASTER_PART.SUPERSESSION_HISTORY</span>
          </h2>
          <p translate>MASTER_PART.SUPERSESSION_HISTORY_DESC</p>
        </div>

                <span
                class="d-flex justify-content-center w-100 mb-3 cadGap" 
                ng-show="masterPartCtrl.isLoadingSupersessionHistory">
                  <span class="spinner-border cadBlue" role="status" aria-hidden="true"></span>
                  {{"GENERAL.LOADING" | translate}}
                </span>
    
                <div ng-if="!masterPartCtrl.isLoadingSupersessionHistory && masterPartCtrl.supersessionHistoryResponse.length > 0"
                  ng-repeat="part in masterPartCtrl.supersessionHistoryResponse | limitTo: 3" class="supersessionPart">
                  <div class="d-flex flex-column w-100 text-center">
                    <p class="mb-0"
                      ng-class="{'highlightCurrentPN': part.partNumber === masterPartCtrl.partNumber, 'bg-white highlightCurrentPN': part.partNumber !== masterPartCtrl.partNumber}">
                      <a class="cadBlue" ng-href="#!/masterPart/{{ part.masterPartId }}">
                        {{ part.partNumber }}
                      </a>
                      <span> - {{ part.partDescription }}</span>
                    </p>
                    <span class="text-center my-3">
                      <i class="fa fa-arrow-down cadBlue" ng-if="!$last || masterPartCtrl.supersessionHistoryResponse.length > 3"></i>
                    </span>
                    <p ng-if="$last && masterPartCtrl.supersessionHistoryResponse.length > 3">{{"MASTER_PART.EXPAND_SUPERSESSIONS_PART1"
                      | translate}}
                      {{masterPartCtrl.supersessionHistoryResponse.length}}.
                      {{"MASTER_PART.EXPAND_SUPERSESSIONS_PART2" | translate}}
                      <span class="font-weight-bold">{{"MASTER_PART.EXPAND_SUPERSESSIONS_PART3" | translate}}</span>
                      {{"MASTER_PART.EXPAND_SUPERSESSIONS_PART4" | translate}}
                    </p>
                  </div>
                </div>

      <div class="button-container-bottom-right">
        <button ng-if="masterPartCtrl.supersessionHistoryResponse.length > 3" class="btn btn-secondary"
          ng-click="masterPartCtrl.expandSupersede()"
          ng-disabled="masterPartCtrl.isLoadingSupersessionHistory">
          {{"MASTER_PART.EXPAND" | translate}}
        </button>
        <button ng-if="masterPartCtrl.supersessionHistoryResponse.length > 0" ng-click="masterPartCtrl.removePartFromSupersession()"
          class="btn btn-danger" ng-disabled="masterPartCtrl.isLoadingSupersessionHistory" translate>
          GENERAL.REMOVE
        </button>
        <button ng-if="!masterPartCtrl.isSuperseded" ng-click="masterPartCtrl.supersessionHistory()" class="btn btn-primary"
          ng-disabled="masterPartCtrl.isLoadingSupersessionHistory"
          translate>
          MASTER_PART.SUPERSEDE
        </button>
      </div>
        </div>
      </div>
    </div>

    <!-- ----------------------------------------------- Part Notes -------------------------------------------------------------->
    <div class="col-12 col-md-6 col-xl-4 py-4 p-md-4 customDivContainer">
      <div class="customDivStyling flexible-container">
        <div class="customCardBody ">
          <h2 class="mb-2">
            <span translate>MASTER_PART.PART_NOTES</span>
          </h2>
          <p translate>MASTER_PART.PART_NOTES_DESC</p>
        </div>
        <div class="button-container-bottom-right" ng-if="!masterPartCtrl.partNote && !masterPartCtrl.isPartNoteEdit">
          <div>
            <button
              class="btn btn-primary"
              ng-click="masterPartCtrl.createPartNote()"
              translate
            >
              MASTER_PART.CREATE
            </button>
          </div>
        </div>

        <div ng-if="masterPartCtrl.isPartNoteEdit">
          <textarea
            placeholder="{{'MASTER_PART.ENTER_NOTE_DESC' | translate}}" ng-model="masterPartCtrl.partNote"
            class="form-control mb-2"
          ></textarea>
        </div>
        <div class="button-container-bottom-right" ng-if="masterPartCtrl.isPartNoteEdit">
          <button
            class="btn btn-secondary"
            ng-click="masterPartCtrl.cancelEdit()"
            translate
          >
            MASTER_PART.CANCEL
          </button>
          <button
            class="btn btn-primary"
            ng-click="masterPartCtrl.savePartNote()"
            ng-disabled="!masterPartCtrl.partNote"
            translate
          >
            MASTER_PART.SAVE
          </button>
        </div>

        <div ng-if="masterPartCtrl.partNote && !masterPartCtrl.isPartNoteEdit">
          <ul class="list-unstyled" ng-if="masterPartCtrl.partNoteContent.isBulletList">
            <li ng-repeat="line in masterPartCtrl.partNoteContent.content track by $index">{{line}}</li>
          </ul>
          <div class="list-unstyled" ng-if="!masterPartCtrl.partNoteContent.isBulletList">
            <p ng-repeat="line in masterPartCtrl.partNoteContent.content track by $index">{{line}}</p>
          </div>
      </div>

          <div class="button-container-bottom-right" ng-if="masterPartCtrl.partNote && !masterPartCtrl.isPartNoteEdit">
            <button
              class="btn danger"
              ng-click="masterPartCtrl.deletePartNote()"
              translate
            >
              MASTER_PART.DELETE
            </button>
            <button
              class="btn btn-primary"
              ng-click="masterPartCtrl.editPartNote()"
              ng-show="!masterPartCtrl.isPartNoteEdit"
              translate
            >
              MASTER_PART.EDIT
            </button>
            <button
              class="btn btn-primary"
              ng-click="masterPartCtrl.savePartNote()"
              ng-show="masterPartCtrl.isPartNoteEdit"
              translate
            >
              MASTER_PART.SAVE
            </button>
          </div>
        </div>

      </div>
    </div>

    <!-- ----------------------------------------------- MODELS PART IS IN -------------------------------------------------------------->

    <div class="col-12 col-md-6 col-xl-4 py-4 p-md-4 customDivContainer">
      <div class="customDivStyling flexible-container customDivScrollable">
        <div class="customCardBody">
          <h2 class="mb-2">
            <span translate>MASTER_PART.WHERE_USED</span>
          </h2>
          <p ng-if="masterPartCtrl.models.length" translate>MASTER_PART.PART_CONTAINED</p>
          <p ng-if="!masterPartCtrl.models.length" translate>MASTER_PART.NO_WHERE_USED</p>
        </div>
        <table ng-if="masterPartCtrl.models.length" class="table table-bordered">
          <thead>
            <tr>
              <th translate>MASTER_PART.PRODUCT</th>
              <th translate>MASTER_PART.MODEL</th>
              <th></th>
            </tr>
          </thead>
          <tbody>
            <tr ng-repeat="model in masterPartCtrl.models">
              <td data-label="{{'MASTER_PART.PRODUCT' | translate}}">
                {{model.machineName}}
              </td>
              <td data-label="{{'MASTER_PART.MODEL' | translate}}">
                {{model.modelName}}
              </td>
              <td>
                <button
                  class="btn btn-primary"
                  ng-click="masterPartCtrl.goToModel($index)"
                  translate
                >
                  MASTER_PART.VIEW
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- ----------------------------------------------- OPTIONS -------------------------------------------------------------->
    <div class="col-12 col-md-6 col-xl-4 py-4 p-md-4 customDivContainer">
      <div class="customDivStyling flexible-container">
        <div class="customCardBody ">
          <h2 class="mb-2">
            <span translate>MASTER_PART.OPTION_SET</span>
          </h2>
          <p translate>MASTER_PART.CREATE_OPTION_SETS</p>
        </div>
        <div ng-if="masterPartCtrl.isSuperseded" class="mb-4">
          <span class="small-text">{{"MASTER_PART.DISABLED_SUPERSEDED" | translate}}</span>
        </div>
        <div class="button-container-bottom-right" ng-if="!masterPartCtrl.hasOptionSet">
          <button class="btn btn-primary" ng-click="masterPartCtrl.createOptionSet()"
            ng-disabled="masterPartCtrl.hasExtensionAlreadyActive()" translate>
            MASTER_PART.ADD_OPTIONS
          </button>
        </div>
    
        <div ng-if="masterPartCtrl.hasOptionSet">
          <p class="mb-8 font-weight-bold">
            {{masterPartCtrl.optionSetDescription}}
          </p>
          <div class="customDivScrollable">
          <table class="table table-bordered">
            <thead>
              <tr>
                <th translate>MASTER_PART.PART_NUM</th>
                <th translate>MASTER_PART.DESCRIPTION</th>
              </tr>
            </thead>
            <tbody>
              <tr ng-repeat="part in masterPartCtrl.optionSetParts">
                <td data-label="{{'MASTER_PART.PART_NUM' | translate}}">
                  {{part.partNumber}}
                </td>
                <td data-label="{{'MASTER_PART.DESCRIPTION' | translate}}">
                  {{part.description ? part.description : part.partDescription}}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        </div>
    
        <div class="button-container-bottom-right mt-3" ng-if="masterPartCtrl.hasOptionSet">
          <button class="btn btn-primary" ng-click="masterPartCtrl.editOptionSet()" ng-disabled="masterPartCtrl.isSuperseded" translate>
            MASTER_PART.EDIT
          </button>
          <button class="btn btn-danger" ng-disabled="masterPartCtrl.isSuperseded" ng-click="masterPartCtrl.deleteOptionSet()" translate>
            MASTER_PART.DELETE
          </button>
        </div>
      </div>
    </div>
    
    <!-- ----------------------------------------------- LINKED -------------------------------------------------------------->
    <div class="col-12 col-md-6 col-xl-4 py-4 p-md-4 customDivContainer">
      <div class="customDivStyling flexible-container">
        <div class="customCardBody ">
          <h2 class="mb-2">
            <span translate>MASTER_PART.LINKED_MODEL</span>
          </h2>
          <p translate>MASTER_PART.LINK_THIS</p>
        </div>
        <div ng-if="masterPartCtrl.isSuperseded" class="mb-4">
          <span class="small-text">{{"MASTER_PART.DISABLED_SUPERSEDED" | translate}}</span>
        </div>
        <div ng-if="!masterPartCtrl.hasLinkedPart" class="button-container-bottom-right">
          <button class="btn btn-primary" ng-click="masterPartCtrl.createLink()"
            ng-disabled="masterPartCtrl.hasExtensionAlreadyActive()" translate>
            MASTER_PART.LINK
          </button>
        </div>
        <div ng-if="masterPartCtrl.hasLinkedPart">
          <p>
            {{"MASTER_PART.LINKED_TO" | translate}}:
            <strong>{{masterPartCtrl.linkedPart.machineName}} -
              {{masterPartCtrl.linkedPart.modelName}}</strong>
          </p>
        </div>
        <div ng-if="masterPartCtrl.hasLinkedPart" class="button-container-bottom-right">
          <button class="btn btn-primary" ng-click="masterPartCtrl.editLink()" ng-disabled="masterPartCtrl.isSuperseded" translate>
            MASTER_PART.EDIT
          </button>
          <button class="btn btn-danger" ng-click="masterPartCtrl.deleteLink()" ng-disabled="masterPartCtrl.isSuperseded" translate>
            MASTER_PART.DELETE
          </button>
        </div>
      </div>
    </div>

    <!-- ----------------------------------------------- ADDITIONAL PARTS -------------------------------------------------------------->
    <div class="col-12 col-md-6 col-xl-4 py-4 p-md-4 customDivContainer">
      <div class="customDivStyling flexible-container">
        <div class="customCardBody ">
          <h2 class="mb-2">
            <span translate>MASTER_PART.BOM</span>
          </h2>
          <p translate>MASTER_PART.CREATE_LIST</p>
        </div>
        <div ng-if="masterPartCtrl.isSuperseded" class="mb-4">
          <span class="small-text">{{"MASTER_PART.DISABLED_SUPERSEDED" | translate}}</span>
        </div>
          <div class="button-container-bottom-right"
             ng-if="!masterPartCtrl.hasAdditionalPart"
            ng-if="!masterPartCtrl.hasAdditionalPart"
          >
            <button
              class="btn btn-primary"
              ng-click="masterPartCtrl.createAdditionalPart()"
              ng-disabled="masterPartCtrl.hasExtensionAlreadyActive()"
              translate
            >
              MASTER_PART.CREATE_BOM
            </button>
        </div>
        <div ng-if="masterPartCtrl.hasAdditionalPart">
          <table class="table table-bordered mt-4">
            <thead>
              <tr>
                <th translate>MASTER_PART.PART_NUM</th>
                <th translate>MASTER_PART.DESCRIPTION</th>
              </tr>
            </thead>
            <tbody>
              <tr ng-repeat="part in masterPartCtrl.additionalParts">
                <td data-label="{{'MASTER_PART.PART_NUM' | translate}}">
                  {{part.partNumber}}
                </td>
                <td data-label="{{'MASTER_PART.DESCRIPTION' | translate}}">
                  {{part.description ? part.description : part.partDescription}}
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="button-container-bottom-right mt-3" ng-if="masterPartCtrl.hasAdditionalPart">
          <button
            class="btn btn-primary"
            ng-click="masterPartCtrl.editAdditionalPart()"
            ng-disabled="masterPartCtrl.isSuperseded"
            translate
          >
            MASTER_PART.EDIT
          </button>
          <button
            class="btn btn-danger"
            ng-click="masterPartCtrl.deleteAdditionalPart()"
            ng-disabled="masterPartCtrl.isSuperseded"
            translate
          >
            MASTER_PART.DELETE
          </button>
        </div>
      </div>
    </div>
  </section>
</section>
