<div class="no-access-error-message" ng-show="manufacturerViewerCtrl.errorMessage">
    <div class="error-message-text m-4">
        {{ manufacturerViewerCtrl.errorMessage | translate}}

        <button class="btn primary m-2 col-12 col-md-auto" ng-click="manufacturerViewerCtrl.backToViewables()" translate>
            <i class="fa fa-chevron-left"></i>
            {{'MANUFACTURER_VIEWER.BACK_TO' | translate}}
        </button>
    </div>
</div>

<div id="manufacturerViewerId">
    <div class="" ng-show="manufacturerViewerCtrl.showCreateNewPart"></div>

    <div class="vertical-align loader" id="loader">
        <div id="loader-text">
            <i class="fas fa-sync-alt fa-spin"></i>
            <p translate>MANUFACTURER_VIEWER.LOADING</p>
            <p translate>MANUFACTURER_VIEWER.FEW_MINS</p>
        </div>
    </div>
    <header class="site-header viewer-header">
        <a
            ng-if="!manufacturerViewerCtrl.fromWhereUsedModal"
            href=""
            class="back-to-products"
            ng-click="manufacturerViewerCtrl.backToViewables()"
        >
            <div class="viewer-back-arrow">
                <i class="fa fa-chevron-left"></i>
            </div>
            <div class="d-inline-flex flex-column">
                <h3 class="text-overflow">{{manufacturerViewerCtrl.machineName}}- {{manufacturerViewerCtrl.viewableName}}</h3>
                <p class="mb-0"><small translate>MANUFACTURER_VIEWER.BACK_TO</small></p>
            </div>
        </a>
        <a
            ng-if="manufacturerViewerCtrl.fromWhereUsedModal"
            href=""
            class="back-to-products"
            ng-click="manufacturerViewerCtrl.goBackToPartSearch()"
        >
            <div class="viewer-back-arrow">
                <i class="fa fa-chevron-left"></i>
            </div>
            <div class="d-inline-flex flex-column">
                <h3 class="text-overflow">{{manufacturerViewerCtrl.machineName}}- {{manufacturerViewerCtrl.viewableName}}</h3>
                <p class="mb-0"><small translate>MANUFACTURER_VIEWER.BACK_TO_PART_SEARCH</small></p>
            </div>
        </a>

        <button
            id="preview_button"
            class="btn xsmall primary-outline"
            ng-class="{'previewButtonSideMenuOpen': manufacturerViewerCtrl.isSideMenuOpen,  'previewButton':!manufacturerViewerCtrl.isSideMenuOpen}"
            ng-click="manufacturerViewerCtrl.openCustomerPreview(model)"
            translate
        >
            MANUFACTURER_VIEWER.PREVIEW
        </button>
    </header>

    <div class="product-viewer vertical">
        <viewer-banner></viewer-banner>
        <live-meeting-banner></live-meeting-banner>

        <add-part
            confirm-button-text="{{'MANUFACTURER_VIEWER.SAVE_PARTS' | translate}}"
            title="{{'MANUFACTURER_VIEWER.ADD_NEW' | translate}}"
            decline-button-text="{{'GENERAL.CANCEL' | translate}}"
            is-visible="manufacturerViewerCtrl.isCreateNewPartVisible()"
            load-parts="manufacturerViewerCtrl.getNonModeledParts()"
            on-confirm="manufacturerViewerCtrl.updateNonModeledParts(partList)"
            on-decline="manufacturerViewerCtrl.hideCreateNewPart()"
            model-id="{{manufacturerViewerCtrl.modelId}}"
            add-part-list="manufacturerViewerCtrl.createdParts"
        >
        </add-part>

        <div class="viewerMessage" ng-show="manufacturerViewerCtrl.viewerMessage !== ''">
            <h2>{{manufacturerViewerCtrl.viewerMessage}}</h2>
        </div>

        <!--<div class="viewerMessage" ng-show="manufacturerViewerCtrl.showSaveSpinner"><h2>Saving! Please-->
        <!--do not close the-->
        <!--browser</h2>-->
        <!--</div>-->

        <div
            ng-class="{'viewerSideMenuOpen': manufacturerViewerCtrl.isSideMenuOpen,  'viewer':!manufacturerViewerCtrl.isSideMenuOpen}"
            id="MyViewerDiv"
        ></div>

        <div class="manufacturer-explode-tool-container">
            <div class="newExplodeRangeInput" ng-show="manufacturerViewerCtrl.isExplodeSliderVisible">
                <input type="range" id="explode_range" value="0" min="0" max="100" step="10" data-show-value="true" />
                <select id="explode_axis">
                    <option>Radial</option>
                    <option>X</option>
                    <option>Y</option>
                    <option>Z</option>
                </select>
            </div>
        </div>

        <form class="form">
            <div class="viewerFooterContainer" id="viewerFooterContainer">
                <div
                    ng-if="manufacturerViewerCtrl.selectedParts.length > 1"
                    class="PartDetailsButton partDetailsContent multiple-parts-selected"
                >
                    <h4 translate>MANUFACTURER_VIEWER.MULTI_PARTS</h4>
                    <p translate>MANUFACTURER_VIEWER.SINGLE_ONLY</p>
                </div>

                <div
                    ng-if="manufacturerViewerCtrl.selectedParts.length >= 0 && !manufacturerViewerCtrl.partDetailsOpen"
                    ng-click="manufacturerViewerCtrl.setPartDetailsOpen(true)"
                    class="PartDetailsButton"
                 >
                    <button class="part-details">
                        {{"MANUFACTURER_VIEWER.PART_DETAILS" | translate}} <i class="fa fa-chevron-right pull-right"></i>
                    </button>
                 </div>

                 <div
                 ng-if="manufacturerViewerCtrl.partDetailsOpen"
                 ng-class="{'partDetailsContentMin': manufacturerViewerCtrl.isSideMenuOpen,  'partDetailsContentMax':!manufacturerViewerCtrl.isSideMenuOpen}"
                 ng-keyup="$event.keyCode == 13 && manufacturerViewerCtrl.savePartEdits()"
              >

              <div class="container-fluid py-3 mt-0">

                    <div class="partDetailsContent cadGap spinner-container cadBlue" ng-show="manufacturerViewerCtrl.isPartsListLoading">
                        <span class="spinner-border cadBlue" role="status" aria-hidden="true"></span>
                        <span style="font-size:1.3em" class="pr-3 font-weight-bold">{{'GENERAL.LOADING' | translate}}</span>
                    </div>
                
                <div ng-hide="manufacturerViewerCtrl.isPartsListLoading" class="d-flex align-items-center w-100 partDetailsContent">

                  <!-- Part Number Section -->
                  <div class="flex-shrink-0" style="flex: 1 0;">
                    <label class="font-weight-bold" translate>MANUFACTURER_VIEWER.PART_NUM</label>
                    <div class="d-flex cadGap partDetailsPartNumber align-items-center">
                      <input
                        type="text"
                        class="m-0 form-control" style="min-width: 150px;"
                        ng-model="manufacturerViewerCtrl.partEdits.partNumber" ng-disabled="!manufacturerViewerCtrl.selectedParts.length"
                      >
                        <p class="mb-0 text-nowrap partDetailsSupersessionNumber" ng-if="manufacturerViewerCtrl.partEdits.supersessionPartNumber">
                          {{"MANUFACTURER_VIEWER.SUPERSEDED_BY" | translate}} <strong>{{manufacturerViewerCtrl.partEdits.supersessionPartNumber}}</strong>
                        </p>
                    </div>
                  </div>

                <div ng-if="manufacturerViewerCtrl.partEdits.alternatePartNumber" class="vertical-divider align-self-stretch"></div>

                <!-- Part Description Section -->
                <div class="flex-grow-1" ng-if="manufacturerViewerCtrl.partEdits.alternatePartNumber">
                    <label class="font-weight-bold" translate>MANUFACTURER_VIEWER.ALTERNATE_NUM</label>
                    <p class="mb-0">{{manufacturerViewerCtrl.partEdits.alternatePartNumber}}</p>
                </div>
              
                  <!-- Divider -->
                  <div class="vertical-divider align-self-stretch"></div>
              
                  <!-- Part Description Section -->
                  <div class="flex-grow-1" ng-hide="manufacturerViewerCtrl.partEdits.masterPartFound">
                    <label class="font-weight-bold" translate>MANUFACTURER_VIEWER.PART_DESC</label>
                    <input
                      type="text"
                      class="m-0 form-control"
                      ng-model="manufacturerViewerCtrl.partEdits.partDescription"
                      ng-disabled="!manufacturerViewerCtrl.selectedParts.length"
                    >
                  </div>

                  <!-- Part Description (Read Only) Section -->
                  <div class="flex-grow-1" ng-if="manufacturerViewerCtrl.partEdits.masterPartFound">
                    <label class="font-weight-bold" translate>MANUFACTURER_VIEWER.PART_DESC_TAB</label>
                    <p class="mb-0">
                      {{manufacturerViewerCtrl.partEdits.partDescription}}
                    </p>
                    <p class="mb-0 font-italic" ng-if="!manufacturerViewerCtrl.partEdits.partDescription">
                        {{"MANUFACTURER_VIEWER.NO_PART_DESC" | translate}}
                    </p>
                  </div>
              
                  <!-- Divider -->
                  <div class="vertical-divider align-self-stretch"></div>
              
                  <!-- Action Buttons -->
                  <div class="flex-shrink-0 align-self-end partDetailsActionBtns">
                    <div class="d-flex align-items-center cadGap">
                      <button
                        class="btn xsmall primary w-100"
                        ng-click="manufacturerViewerCtrl.savePartEdits()"
                        ng-class="{disabled: !manufacturerViewerCtrl.isPartChanges()}"
                        ng-disabled="!manufacturerViewerCtrl.isPartChanges()"
                        translate
                      >
                        MANUFACTURER_VIEWER.SAVE
                      </button>
                      <i
                        class="fa fa-undo viewerFooterIcon"
                        ng-class="{disabled: !manufacturerViewerCtrl.isPartChanges()}"
                        ng-click="manufacturerViewerCtrl.resetPartEdits()"
                        style="cursor: pointer;"
                      ></i>
                      <i
                        class="fa fa-times viewerFooterIcon"
                        ng-click="manufacturerViewerCtrl.setPartDetailsOpen(false)"
                        style="cursor: pointer;"
                      ></i>
                    </div>
                 
                </div>
                </div>
              </div>
              
              
                </div>
            </div>
        </form>
    </div>

    <side-menu></side-menu>

    <div class="product-thumbnails-carousel-wrap vertical manufacturer">
        <div class="product-thumbnails-carousel">
            <div class="take-snapshot-container">
                <button class="btn btn-block primary snapshot" ng-click="manufacturerViewerCtrl.saveSnapshot()">
                    <i class="fa fa-camera" aria-hidden="true"></i> &nbsp; {{"MANUFACTURER_VIEWER.ADD_NEW_SNAP" | translate}}
                </button>
            </div>

            <div
                class="vertical-align"
                ng-repeat="step in manufacturerViewerCtrl.steps track by step.stateId"
                ng-class="($last) ? (manufacturerViewerCtrl.currentList < 1  ? 'product-thumb-step-selected-last' : 'product-thumb-step-selected') : 'product-thumb-step'"
                ng-click="manufacturerViewerCtrl.loadSnapshot(step.stateId)"
            >
                <img ng-src="{{step.imgUrl}}" width="36" height="36" />
                <span class="text-area">{{step.stateName}}</span>
                <span ng-show="$last" class="overwrite-button" ng-click="manufacturerViewerCtrl.overwriteSnapshot($event)">
                    <i class="fa fa-refresh"></i>
                </span>
            </div>

            <div class="product-thumb-cell" ng-repeat="snapshot in manufacturerViewerCtrl.currentList track by snapshot.stateId">
                <div
                    class="img-wrap"
                    style="background-image:url({{snapshot.imgUrl}});"
                    ng-click="manufacturerViewerCtrl.loadSnapshot(snapshot.stateId)"
                ></div>

                <div class="product-info">
                    <h4 class="mouse-pointer full-width">{{snapshot.stateName}}</h4>

                    <div class="full-width pt-1">
                        <i class="fa fa-pencil pull-left" ng-click="manufacturerViewerCtrl.editSnapshotName(snapshot.stateId)"></i>
                        <i class="fa fa-trash pull-right" ng-click="manufacturerViewerCtrl.deleteSnapshot(snapshot.stateId)"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
