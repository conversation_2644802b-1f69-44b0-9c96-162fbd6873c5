(function () {
    'use strict';

    angular
        .module('app.viewer')
        .controller('SoftCopySideMenuController', SoftCopySideMenuController);

    SoftCopySideMenuController.$inject = [];

    function SoftCopySideMenuController() {
        var vm = this;

        vm.isMinimized = window.window.innerWidth <= 680;
        vm.status = {
            isModelBrowserOpen : true,
            isKitBuilderOpen: false,
            isOptionsSetOpen: false,
            isLinkedPartOpen: false,
            isPurchasableAssembliesOpen: false,
            isNonModeledPartsOpen: false,
            isViewerSettingsOpen: false
        };

        vm.minimizeSideMenu = minimizeSideMenu;
        vm.maximizeSideMenu = maximizeSideMenu;

        initialize();

        function initialize() {

        }

        function minimizeSideMenu() {
            vm.isMinimized = true;
        }

        function maximizeSideMenu() {
            vm.isMinimized = false;
        }

    }


})();