(function () {
    "use strict";

    angular.module("app.parts").controller("editPriceListController", editPriceListController);

    editPriceListController.$inject = ["$uibModalInstance", "data"];

    function editPriceListController($uibModalInstance, data) {
        var vm = this;

        vm.id = data.id;
        vm.currencyIdentifier = data.currencyIdentifier;
        var match = data.price.match(/^([^\d]+)?(\d+(\.\d+)?)/);
        vm.currencySymbol = match ? match[1] : '';
        vm.price = match ? parseFloat(match[2]) : 0;
        vm.update = update;
        vm.cancel = function () {
            $uibModalInstance.dismiss("cancel");
        };

        function update() {
            $uibModalInstance.close({ id: vm.id, currencyIdentifier: vm.currencyIdentifier, price: vm.currencySymbol + vm.price.toFixed(2) });
        }
    }
})();
