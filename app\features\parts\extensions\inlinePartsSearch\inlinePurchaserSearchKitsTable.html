<h2 ng-show="inlinePartsSearchCtrl.kitsReturned()" translate>ORDER.KITS</h2>

<div ng-show="inlinePartsSearchCtrl.noKitsFound()">
    <p class="font-weight-light" translate>INLINE_SEARCH.NO_KITS</p>
</div>

<div class="responsiveContainer py-0 mb-4">

    <table class="table table-bordered equal-width table-smaller-font" ng-show="inlinePartsSearchCtrl.kits.length > 0">
        <thead>
            <tr>
                <th class="col-md-2 col-12" translate>GENERAL.KIT_NUMBER</th>
                <th class="col-md-4 col-12" translate>ORDER.DESCRIPTION</th>
                <th ng-if="inlinePartsSearchCtrl.isCustomerSearchManualsOnly" translate>
                    ORDER.PUBLICATION
                </th>
                <th ng-if="inlinePartsSearchCtrl.isPreviewStockLevelEnabled && !inlinePartsSearchCtrl.isDealerPlusCustomer"
                    translate>
                    ORDER.STOCK
                </th>
                <th translate>ORDER.QUANTITY</th>
                <th class="disableWordBreak" ng-if="!inlinePartsSearchCtrl.hidePrice && !inlinePartsSearchCtrl.isPriceTBC()" translate>
                    ORDER.ITEM_PRICE
                </th>
                <th class="disableWordBreak" ng-if="!inlinePartsSearchCtrl.hidePrice && !inlinePartsSearchCtrl.isPriceTBC()" translate>
                    ORDER.TOTAL_PRICE
                </th>
                <th></th>
            </tr>
        </thead>

        <tbody>
            <tr class="hoverTableBG" ng-if="inlinePartsSearchCtrl.kits.length > 0"
                ng-class="{'strike-through': kit.stock <= 0, 'borderLeft': inlinePartsSearchCtrl.accordionStates[$index]}"
                ng-repeat-start="part in inlinePartsSearchCtrl.kits"
                ng-click="inlinePartsSearchCtrl.toggleKitsAccordion(part.kitId, $index, $event)">
                <td data-label="{{'GENERAL.KIT_NUMBER' | translate}}">
                    <i class="pr-2 fa" ng-class="inlinePartsSearchCtrl.accordionStates[$index] ? 'fa-chevron-up' : 'fa-chevron-down'"
                        style="pointer-events: none;"></i>
                    {{ part.partNumber }}
                    <span ng-if="(kit.kitCount || kit.partCountInKits) >= 0" class="badge badge-pill badge-primary">{{ kit.kitCount || kit.partCountInKits }} Parts</span>
                </td>
                <td data-label="{{'ORDER.DESCRIPTION' | translate}}">
                    {{ part.partDescription }}
                </td>
                <td ng-if="inlinePartsSearchCtrl.isCustomerSearchManualsOnly">
                    {{part.manualName}}
                </td>

                <td class="disableWordBreak" data-label="{{'ORDER.STOCK' | translate}}"
                    ng-if="inlinePartsSearchCtrl.isPreviewStockLevelEnabled && !inlinePartsSearchCtrl.isDealerPlusCustomer">
                    <!-- Show stock number isStockWarehousesEnabled is true -->
                    <span ng-if="inlinePartsSearchCtrl.isStockWarehousesEnabled">
                        <span class="text-success" ng-if="part.stock >= 3">{{ part.stock }}</span>
                        <span class="text-warning" ng-if="part.stock < 3 && part.stock > 0">{{ part.stock }}</span>
                        <span class="text-danger" ng-if="part.stock === null || part.stock < 1">{{ part.stock || '0'
                            }}</span>
                    </span>
                    <!-- Show icons if isStockWarehousesEnabled is false -->
                    <span ng-if="!inlinePartsSearchCtrl.isStockWarehousesEnabled">
                        <span title="{{'ORDER.IN_STOCK' | translate}}" tooltip-trigger="outsideClick"
                            uib-tooltip="{{'ORDER.IN_STOCK' | translate}}" class="success-alert"
                            ng-if="part.stock >= 3"><i class="fas fa-layer-group text-success pointer"></i></span>
                        <span title="{{'ORDER.LOW_STOCK' | translate}}" tooltip-trigger="outsideClick"
                            uib-tooltip="{{'ORDER.LOW_STOCK' | translate}}" class="warning-alert"
                            ng-if="part.stock < 3 && part.stock > 0"><i
                                class="fas fa-layer-group text-warning pointer"></i></span>
                        <span title="{{'ORDER.STOCK_SUBJECT' | translate}}" tooltip-trigger="outsideClick"
                            uib-tooltip="{{'ORDER.STOCK_SUBJECT' | translate}}" class="warning-alert"
                            ng-if="part.stock === null || part.stock < 1"><i
                                class="fas fa-layer-group text-danger pointer"></i></span>
                    </span>
                </td>
                <td data-label="{{'ORDER.QUANTITY' | translate}}">
                    <input class="inline-quantity-input" type="number" min="0" ng-model="part.quantity"
                    ng-init="part.quantity=part.quantity||1" ng-change="inlinePartsSearchCtrl.quantityUpdated()" />
                </td>
                <td class="disableWordBreak" data-label="{{'ORDER.ITEM_PRICE' | translate}}" ng-if="!inlinePartsSearchCtrl.hidePrice && !inlinePartsSearchCtrl.isPriceTBC()">
                    {{ part.price ? (part.price.value | currency:part.price.currencyIdentifier:2) : '-' }}
                </td>
                <td class="disableWordBreak" data-label="{{'ORDER.TOTAL_PRICE' | translate}}" ng-if="!inlinePartsSearchCtrl.hidePrice && !inlinePartsSearchCtrl.isPriceTBC()">
                    {{ part.totalPrice === "TBC" ? '-' : (part.totalPrice | currency:part.price.currencyIdentifier:2) }}
                </td>
                <td ng-if="inlinePartsSearchCtrl.hasOrdersAccess">
                    <button ng-if="!part.superseded"
                        class="btn primary fixed-width-btn"
                        ng-click="inlinePartsSearchCtrl.addKit(part); $event.stopPropagation()">
                        <div ng-hide="part.clicked" translate>GENERAL.ADD_TO_BASKET</div>
                        <div class="check-icon-wrapper" ng-show="part.clicked">
                            <i class="fa fa-check"></i>
                        </div>
                    </button>
                </td>
             </tr>

             <tr ng-repeat-end>
                <td class="p-0" colspan="100%">
                    <div class="accordion-anim" ng-class="{'open': inlinePartsSearchCtrl.accordionStates[$index]}">
                        <table class="table table-bordered w-100">
                            <thead class="borderLeft">
                                <tr>
                                    <th class="col-12 col-md-2" translate>ORDER.PART_DETAILS</th>
                                    <th class="col-12 col-md-3" translate>ORDER.TITLE</th>
                                    <th class="col-lg-1 col-12" translate>ORDER.QTY</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="blueTableBG borderLeft" ng-repeat="partDetail in inlinePartsSearchCtrl.kits[$index].kitDetails"
                                    ng-class="partDetail.quantity > 0 ? '' : 'strike-through'">
                                    <td data-label="{{'ORDER.PART_DETAILS' | translate}}">
                                        <span>{{partDetail.partNumber}}</span>
                                    </td>
                                    <td data-label="{{'ORDER.TITLE' | translate}}">
                                       {{ (partDetail.descriptions | filter: {languageCode: inlinePartsSearchCtrl.currentLanguage})[0].translation
                                        }} 
                                    </td>
                                    <td data-label="{{'ORDER.QTY' | translate}}">
                                        <span>{{partDetail.quantity}}</span>
                                    </td>
                                    <td class="bg-white"></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </td>
             </tr>

            </tr>
        </tbody>
    </table>

</div>