(function () {
    'use strict';

    angular
        .module('app.viewer')
        .controller('CreateOptionsSetController', CreateOptionsSetController);

    CreateOptionsSetController.$inject = ['optionsSetService', '$stateParams', '$scope', 'viewerService', 'viewerBannerService', '$rootScope', 'viewerHelperService', 'nonModeledPartService', '$translate'];

    function CreateOptionsSetController(optionsSetService, $stateParams, $scope, viewerService, viewerBannerService, $rootScope, viewerHelperService, nonModeledPartService, $translate) {
        var vm = this;

        vm.isOpen = false;
        vm.description = "";
        vm.optionsSet = [];
        vm.selectedPart = [];
        vm.isEdit = false;
        vm.nonModeledId = "";
        vm.alreadyHasOptions = false;
        vm.modelId = $stateParams.modelId;

        vm.saveOptionsSet = saveOptionsSet;
        vm.cancel = cancel;
        vm.removeOption = removeOption;
        vm.addAnotherOption = addAnotherOption;

        var OPTION_SUCCESS;
        $translate(['CREATE_OPTION_SET.OPTION_SUCCESS'])
            .then(function (resp) {
                OPTION_SUCCESS = resp["CREATE_OPTION_SET.OPTION_SUCCESS"];
            });

        function initialize() {
            if (vm.optionsSetId) {
                vm.isEdit = true;
                fetchOptionsSet(vm.optionsSetId);
            } else {
                var selectedParts = viewerHelperService.getSelectedParts();
                viewerHelperService.selectParts([]);
                viewerHelperService.selectParts(selectedParts);
                vm.isEdit = false;
                vm.description = "";
                vm.optionsSet = [{partDescription: "", partNumber: ""}, {partDescription: "", partNumber: ""}];
            }
        }

        function fetchOptionsSet(optionsSetId) {
            optionsSetService.fetchOptionsSet(optionsSetId)
                .then(fetchOptionsSetSuccess, serviceCallFailed);
        }

        function fetchOptionsSetSuccess(response) {
            vm.description = response.data.description;
            vm.optionsSet = response.data.optionsSet;
            vm.originalEditObjectId = response.data.objectId;
            viewerHelperService.selectParts([response.data.objectId]);
        }

        function addAnotherOption() {
            vm.optionsSet.push({partDescription: "", partNumber: ""});
        }

        function removeOption(index) {
            vm.optionsSet.splice(index, 1);
            if (vm.optionsSet.length < 2) {
                addAnotherOption();
            }
        }

        function cancel() {
            vm.description = "";
            vm.optionsSet = [];
            vm.isOpen = false;
            $rootScope.$broadcast("create-options-set-closed");
        }

        function validateOptionParts() {
            if (vm.optionsSet.length >= 2) {
                for (var i = 0; i < vm.optionsSet.length; i++) {
                    if (vm.optionsSet[i].partDescription === "" || vm.optionsSet[i].partNumber === "") {
                        return false;
                    }
                }
            } else {
                return false;
            }
            return true;
        }

        function saveOptionsSet() {
            vm.errors = {};
            vm.errors.noPartSelected = vm.selectedPart.partId === undefined;
            vm.errors.noDescription = vm.description === "";
            vm.errors.notEnoughParts = !validateOptionParts();

            if (vm.errors.noPartSelected || vm.errors.noDescription || vm.errors.notEnoughParts || vm.alreadyHasOptions) {
                return;
            }

            if (vm.isEdit) {
                optionsSetService.editOptionsSet(vm.optionsSetId, vm.selectedPart.partId, vm.description, vm.optionsSet)
                    .then(saveOptionsSetSuccess, serviceCallFailed);
            } else {
                optionsSetService.createOptionsSet(vm.modelId, vm.selectedPart.partId, vm.description, vm.optionsSet)
                    .then(saveOptionsSetSuccess, serviceCallFailed);
            }
            if (vm.hasNonModeled) {

                nonModeledPartService.deleteNonModeledPart(vm.nonModeledId).then(function () {
                    $rootScope.$broadcast("nonModeled-updated");
                });

            }
        }

        function saveOptionsSetSuccess() {
            viewerBannerService.setNotification("SUCCESS", OPTION_SUCCESS);
            vm.isOpen = false;
            $rootScope.$broadcast("create-options-set-closed");
        }

        function serviceCallFailed(error) {
            viewerBannerService.setNotification("ERROR", error.error);
        }

        $scope.$on("viewer-part-selected", function (event, partViewerDetails) {

            if (partViewerDetails.length === 1) {
                vm.hasOptionsSet = partViewerDetails.containsOptionsSet;
                if (vm.hasOptionsSet) {
                    vm.optionsSetDescription = vm.optionsSetDescription + partViewerDetails.optionsSet[0].description;
                    vm.optionsSetIds.push(partViewerDetails.optionsSet[0].id);
                } else {

                    vm.optionsSetDescription = "";
                    vm.optionsSetIds = [];
                    vm.hasOptionsSet = false;
                }
                vm.selectedPart = partViewerDetails[0].part;
                vm.hasNonModeled = !!partViewerDetails.nonModelled;
                vm.nonModeledId = vm.hasNonModeled ? partViewerDetails.nonModelledId : '';

                if (!!partViewerDetails.containsOptionsSet) {
                    if (vm.isEdit) {
                        vm.alreadyHasOptions = vm.originalEditObjectId !== partViewerDetails.part.objectId;
                    } else {
                        vm.alreadyHasOptions = true
                    }
                } else {
                    vm.alreadyHasOptions = false;
                }
            } else if (partViewerDetails.length > 1) {
                vm.selectedPart = partViewerDetails
            } else {
                vm.selectedPart = [];
                vm.alreadyHasOptions = false;

            }
        });

        $scope.$on("create-options-set-opened", function (event, optionsSetId) {
            vm.isOpen = true;
            vm.optionsSetId = optionsSetId;
            initialize();
        });
    }

})();
