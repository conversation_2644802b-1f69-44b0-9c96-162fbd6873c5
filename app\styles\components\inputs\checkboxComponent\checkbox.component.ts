class CheckboxController {

  label: string;
  isChecked: boolean;
  isCheckedChange: Function;
  uniqueId: string;

  static $inject = ['$scope'];

  constructor(private $scope: angular.IScope) {
    this.label = '';
    this.isChecked = false;
    this.isCheckedChange = () => {};
    this.uniqueId = 'checkbox_' + Math.random().toString(36).substr(2, 9); // Generate a unique ID
  }

  onClick() {
    this.isChecked = !this.isChecked;
    this.isCheckedChange({ isChecked: this.isChecked });
    this.$scope.$emit('isCheckedChange', this.isChecked);
  }
}

angular
  .module('app.customer')
  .component('appCheckboxComponent', {
    bindings: {
        label: '<',
        isChecked: '=',
        isCheckedChange: '&'
    },
    controller: CheckboxController,
    template: `
<div class="input-group mb-0 px-4">
  <input class="inp-chBox" id="{{$ctrl.uniqueId}}" type="checkbox" ng-model="$ctrl.isChecked" ng-click="$ctrl.onClick()" style="display: none" />
  <label class="chBox mb-0" for="{{$ctrl.uniqueId}}">
    <span>
      <svg width="12px" height="10px" viewbox="0 0 12 10">
        <polyline points="1.5 6 4.5 9 10.5 1"></polyline>
      </svg>
    </span>
    <span class="pl-2">{{$ctrl.label | translate}}</span>
  </label>
</div>
    `,
  });