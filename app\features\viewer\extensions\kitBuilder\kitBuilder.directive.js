(function () {
    'use strict';

    angular
        .module('app.viewer')
        .directive('kitBuilder', kitBuilder);

    function kitBuilder() {
        var directive = {
            restrict: 'E',
            templateUrl: 'features/viewer/extensions/kitBuilder/kitBuilder.html',
            controller: 'KitBuilderController',
            controllerAs: 'kitBuilderCtrl',
            bindToController: true
        };
        return directive;
    }

})();