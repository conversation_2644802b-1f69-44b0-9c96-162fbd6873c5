<section class="p-5 inventory">

    <section class="inventory_header d-flex justify-content-between">

        <div class="pl-4">
            <h1>{{DPMasterPartCtrl.partNumber}}</h1>
        </div>

    </section>

    <section class="d-flex flex-wrap">

        <!-- ----------------------------------------------- PRICE  -------------------------------------------------------------->
        <div class="col-lg-4 col-md-6 col-sm-12 customDivContainer" ng-hide="DPMasterPartCtrl.hasOptionSet">
            <div class="customDivStyling">
                <h2 class="mb-2">{{"MASTER_PART.PRICE" | translate}}</h2>
                <div class="customCardBody pb-3" translate>MASTER_PART.PRICING_DESCRIPTION</div>

                <div class="d-flex justify-content-between align-items-center">
                    <div class="font-weight-bold">Price</div>
                    <div class="" ng-if="DPMasterPartCtrl.price && !DPMasterPartCtrl.isBeingEdited">
                        {{DPMasterPartCtrl.price | currency:DPMasterPartCtrl.currency.symbol:2}}
                    </div>
                    <div class="" ng-if="!DPMasterPartCtrl.price && !DPMasterPartCtrl.isBeingEdited">N/A</div>
                    <div ng-hide="DPMasterPartCtrl.isBeingEdited">
                        <button class="btn primary cadIconContainer d-flex justify-content-center align-items-center ml-2"
                             ng-click="DPMasterPartCtrl.editPart()"
                             ng-disabled="DPMasterPartCtrl.isActiveEdit">
                            <i class="fa fa-pencil"></i>
                        </button>
                    </div>
                    <div class="mx-2" ng-show="DPMasterPartCtrl.isBeingEdited">
                        <input type="number" ng-model="DPMasterPartCtrl.price" class="mb-0"/>
                    </div>
                    <div class="d-flex" ng-show="DPMasterPartCtrl.isBeingEdited">
                        <button class="btn success cadIconContainer d-flex justify-content-center align-items-center mx-2"
                             ng-click="DPMasterPartCtrl.saveEdit()">
                            <i class="fa fa-save"></i>
                        </button>
                        <button class="btn danger cadIconContainer d-flex justify-content-center align-items-center"
                             ng-click="DPMasterPartCtrl.cancelEdit(part.masterPartId)">
                            <i class="fa fa-times"></i>
                        </button>
                    </div>
                </div>

            </div>
        </div>
        <!-- ----------------------------------------------- TECH DOCS  -------------------------------------------------------------->
        <div class="text-center col-lg-4 col-md-6 col-sm-12 customDivContainer" ng-if="DPMasterPartCtrl.hasLinkedTechDocs">
            <div class="customDivStyling">
                <h2 class="mb-2">{{"MASTER_PART.TECH_INFO" | translate}}</h2>
                <div class="customCardBody pb-3" translate>MASTER_PART.TECH_DESCRIPTION</div>
                <div translate>MASTER_PART.LINKED
                </div>
            </div>
        </div>
        <!-- ----------------------------------------------- TRANSLATIONS -------------------------------------------------------------->
        <div class="col-lg-4 col-md-6 col-12 customDivContainer">
            <div class="customDivStyling">

                <h2 class="mb-2">{{"MASTER_PART.DESCS" | translate}}</h2>
                <div class="customCardBody pb-3" translate>MASTER_PART.DESCRIPTIONS_DESCRIPTION</div>

                <table class="table table-bordered">
                    <thead>
                    <tr>
                        <th translate>MASTER_PART.LANGUAGE</th>
                        <th translate>MASTER_PART.TRANSLATION</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr ng-repeat="translation in DPMasterPartCtrl.translations">
                        <td data-label="{{'MASTER_PART.LANGUAGE' | translate}}">{{translation.displayText}}</td>
                        <td class="word-break" data-label="{{'MASTER_PART.TRANSLATION' | translate}}">
                            {{translation.description}}
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <!-- ----------------------------------------------- KITS -------------------------------------------------------------->
        <div class="col-lg-4 col-md-6 col-sm-12 customDivContainer" ng-if="DPMasterPartCtrl.kits.length > 0">

            <div class="customDivStyling">

                <h2 class="mb-2">{{"MASTER_PART.KITS" | translate}}</h2>
                <div class="customCardBody pb-3" translate>MASTER_PART.KITS_DESCRIPTION</div>


                <table class="table table-bordered mt-3">
                    <thead>
                    <tr>
                        <th translate>MASTER_PART.KIT_INFO</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr ng-repeat="kit in DPMasterPartCtrl.kits">
                        <td data-label="{{'MASTER_PART.KIT_INFO' | translate}}"><p class="font-weight-bold">
                            {{kit.title}}</p>
                            <p>{{kit.description}}</p></td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- ----------------------------------------------- OPTIONS -------------------------------------------------------------->
        <div class="col-lg-4 col-md-6 col-sm-12 customDivContainer" ng-if="DPMasterPartCtrl.hasOptionSet">

            <div class="customDivStyling">

                <h2 class="mb-2">{{"MASTER_PART.OPTION_SET" | translate}}</h2>
                <div class="customCardBody pb-3" translate>MASTER_PART.OPTIONS_DESCRIPTION</div>

                <p class="mb-2 font-weight-bold">{{DPMasterPartCtrl.optionSetDescription}}</p>

                <div>
                    <table class="table table-bordered">
                        <thead>
                        <tr>
                            <th translate>MASTER_PART.PART_NUM</th>
                            <th translate>MASTER_PART.DESCRIPTION</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr ng-repeat="part in DPMasterPartCtrl.optionSetParts">
                            <td data-label="{{'MASTER_PART.PART_NUM' | translate}}">{{part.partNumber}}</td>
                            <td data-label="{{'MASTER_PART.DESCRIPTION' | translate}}">
                                {{part.description ? part.description : part.partDescription}}
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>

            </div>
        </div>

        <!-- ----------------------------------------------- SUPERSEDE -------------------------------------------------------------->
        <div class="col-lg-4 col-md-6 col-sm-12 customDivContainer" ng-if="DPMasterPartCtrl.isSuperseded">
            <div class="customDivStyling">

                <h2 class="mb-2">{{"MASTER_PART.SUPERSEDE_THIS" | translate}}</h2>
                <div class="customCardBody pb-3" translate>MASTER_PART.SUPERSEDE_DESCRIPTION</div>


                <div class="mb-2">
                    <div>
                        <p class="mb-0">{{"MASTER_PART.BEEN_REPLACED" | translate}}:
                            <strong>{{DPMasterPartCtrl.supersededPart.machineName}}
                                - {{DPMasterPartCtrl.supersededPart.modelName}}</strong></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- ----------------------------------------------- LINKED -------------------------------------------------------------->
        <div class="col-lg-4 col-md-6 col-sm-12 customDivContainer" ng-if="DPMasterPartCtrl.hasLinkedPart">

            <div class="customDivStyling">

                <h2 class="mb-2">{{"MASTER_PART.LINKED_MODEL" | translate}}</h2>
                <div class="customCardBody pb-3" translate>MASTER_PART.LINKED_DESCRIPTION</div>


                <div class="mb-2">
                    <p>{{"MASTER_PART.LINKED_TO" | translate}}: <strong>{{DPMasterPartCtrl.linkedPart.machineName}} -
                        {{DPMasterPartCtrl.linkedPart.modelName}}</strong></p>
                </div>


            </div>
        </div>
        <!-- ----------------------------------------------- ADDITIONAL PARTS -------------------------------------------------------------->
        <div class="col-lg-4 col-md-6 col-sm-12 customDivContainer" ng-if="DPMasterPartCtrl.hasAdditionalPart">

            <div class="customDivStyling">

                <h2 class="mb-2">{{"MASTER_PART.BOM" | translate}}</h2>
                <div class="customCardBody pb-3" translate>MASTER_PART.BOM_DESCRIPTION</div>

                <div>
                    <table class="table table-bordered mt-4">
                        <thead>
                        <tr>
                            <th translate>MASTER_PART.PART_NUM</th>
                            <th translate>MASTER_PART.DESCRIPTION</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr ng-repeat="part in DPMasterPartCtrl.additionalParts">
                            <td data-label="{{'MASTER_PART.PART_NUM' | translate}}">{{part.partNumber}}</td>
                            <td data-label="{{'MASTER_PART.DESCRIPTION' | translate}}">
                                {{part.description ? part.description : part.partDescription}}
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>

            </div>

        </div>

        <!-- ----------------------------------------------- MODELS PART IS IN -------------------------------------------------------------->
        <div class="col-lg-4 col-md-6 col-sm-12 customDivContainer">

            <div class="customDivStyling">

                <div>

                    <h2 class="mb-2">{{"MASTER_PART.WHERE_USED" | translate}}</h2>
                    <div class="customCardBody pb-3" translate>MASTER_PART.WHERE_USED_DESCRIPTION</div>

                    <table class="table table-bordered">
                        <thead>
                        <tr>
                            <th translate>MASTER_PART.PRODUCT</th>
                            <th translate>MASTER_PART.MODEL</th>
                            <th></th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr ng-repeat="model in DPMasterPartCtrl.models">
                            <td data-label="{{'MASTER_PART.PRODUCT' | translate}}">{{model.machineName}}</td>
                            <td data-label="{{'MASTER_PART.MODEL' | translate}}">{{model.modelName}}</td>
                            <td>
                                <button class="btn btn-primary btn-small pull-right mr-8"
                                        ng-click="DPMasterPartCtrl.goToModel($index)" translate>MASTER_PART.VIEW
                                </button>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>

            </div>

        </div>


    </section>

</section>