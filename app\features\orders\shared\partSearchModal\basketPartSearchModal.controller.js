(function () {
    'use strict';

    angular
        .module('app.viewer')
        .controller('BasketPartSearchModalController', BasketPartSearchModalController);

    BasketPartSearchModalController.$inject = ['$uibModalInstance',  'orderDetails', 'basketService'];

    function BasketPartSearchModalController($uibModalInstance,  orderDetails, basketService) {
        var vm = this;

        vm.onAddClicked = onAddClicked;
        vm.cancel = cancel;

        vm.userId = orderDetails.userId;

        function onAddClicked(part, kit) {
        
            if (kit && kit.kitId) {
                var kit = kitDataMap(kit);
                kit.machineName = "Kit Search";
                basketService.addKit(kit);
            } else {
                part.machineName = "Part Search";
                basketService.addPart(part);
            }
        }
        
        function kitDataMap(kit) {
            return {
                masterPartNumber: kit.partNumber,
                kitPrice: (kit.price && typeof kit.price === 'object') ? kit.price.value : null,
                kitId: kit.kitId,
                description: kit.partDescription,
                modelId: kit.modelId ? kit.modelId.toString() : null,
                quantity: kit.quantity,
                parts: kit.kitDetails,
                masterPartId: kit.masterPartId,
                masterPartKitId: kit.masterPartKitId,
                stock: kit.stock,
                machineName: kit.machineName,
                modelName: kit.modelName
            };
        }        

        function cancel() {
            $uibModalInstance.close();
        }


    }

})();

