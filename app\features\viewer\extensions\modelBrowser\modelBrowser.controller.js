(function () {
    "use strict";

    angular.module("app.viewer").controller("ModelBrowserController", ModelBrowserController);

    ModelBrowserController.$inject = [
        "ivhTreeviewInterpolateStartSymbol",
        "ivhTreeviewInterpolateEndSymbol",
        "$scope",
        "$rootScope",
        "viewerHelperService",
        "ivhTreeviewBfs",
        "viewerService",
        "viewerVariablesService",
        "ivhTreeviewMgr",
        "$timeout",
        "$stateParams",
    ];

    function ModelBrowserController(
        ivhTreeviewInterpolateStartSymbol,
        ivhTreeviewInterpolateEndSymbol,
        $scope,
        $rootScope,
        viewerHelperService,
        ivhTreeviewBfs,
        viewerService,
        viewerVariablesService,
        ivhTreeviewMgr,
        $timeout,
        $stateParams
    ) {
        var vm = this;
        var isAllUnlocked = false;
        var initiallyLockedNodes = [];

        vm.showTree = false;
        vm.isMinimized = false;
        vm.modelTree = [];

        vm.onSelectionChange = onSelectionChange;
        vm.expandNodes = expandNodes;
        vm.expand = expand;
        vm.collapse = collapse;
        vm.clearSearch = clearSearch;
        vm.toggleChange = toggleChange;
        vm.calculateExplodeNodes = calculateExplodeNodes;

        vm.customOpts = {
            defaultSelectedState: true,
            labelAttribute: "partNumber",
            expandToDepth: 1,
            childrenAttribute: "childParts",
            twistieCollapsedTpl:
                "<span ng-class=\"node.hasChildSelected === true ? (node.viewerSelected === true ? 'fa fa-chevron-right white' : 'fa fa-fw fa-chevron-right blue') : 'fa fa-fw fa-chevron-right'\" ></span>",
            twistieExpandedTpl:
                "<span ng-class=\"node.hasChildSelected === true ? (node.viewerSelected === true ? 'fa fa-chevron-down white' : 'fa fa-fw fa-chevron-down blue') : 'fa fa-fw fa-chevron-down'\" ></span>",
            twistieLeafTpl: '<span style="cursor: default;"></span>',
            nodeTpl: [
                '<div class="ivh-treeview-node-content" ng-class="node.viewerSelected === true ? \'selected-browser-part\' : \'\'"  style="text-overflow: ellipsis; overflow: hidden;white-space: nowrap;" title="{{trvw.label(node)}}">',
                '<span class="ivh-treeview-checkbox-wrapper" ng-if="trvw.useCheckboxes()" ivh-treeview-checkbox-cadshare="" style="float:left">',
                "</span>",
                '<span class="labelrow">',
                "<span>{{getSpaces(depth)}}</span>",
                "<span ivh-treeview-toggle>",
                '<span class="ivh-treeview-twistie-wrapper" ivh-treeview-twistie></span>',
                "</span>",

                /* '<span class="ivh-treeview-node-label" name-node-functions>{{trvw.label(node)}}</span>',*/
                '<span class="ivh-treeview-node-label" name-node-functions ng-class="{\'critical-spare-part\': node.criticalSparePart}">{{trvw.label(node)}}</span>',
                "</span>",
                '<span class="ivh-other-items">',
                '<a href=""  ng-click="openViewEditPart(node)">&nbsp;<span class="fa fa-info-circle"></span></a>',
                "</span>",
                '<span class="ivh-other-items">',
                '<a href="" ng-click="togglePartLock(node)"  ng-show="node.childParts && node.childParts.length > 0">&nbsp;<span ng-class="node.isLocked === true ? \'fa fa-lock\' : \'fa fa-lock faded\'"></span></a>',
                /*'<a ng-hide="node.childParts && node.childParts.length > 0"></a>',*/
                "</span>",
                '<span  class="ivh-other-items">',
                '<a href=""  ng-show="node.linkedPart"><span class="fa fa-paperclip"></span></a>',
                '<a href="" ng-show="node.nonModeledPart && !node.linkedPart" ng-click="nonModeledPartClicked(node)"><span class="fa fa-list"></span></a>',
                "</span>",
                '<div  ng-if="trvw.isExpanded(node)" ivh-treeview-children></div>',
                "</div>",
            ]
                .join("\n")
                .replace(new RegExp("{{", "g"), ivhTreeviewInterpolateStartSymbol)
                .replace(new RegExp("}}", "g"), ivhTreeviewInterpolateEndSymbol),
        };

        initialize();

        $rootScope.$on("model-tree-initialized", function (event, modelTree) {
            vm.modelTree = modelTree;
            vm.showTree = true;
            viewerHelperService.setPartTreeTopId(vm.modelTree[0].objectId);
            resetViewerSelections();
        });

        function initialize() {
            var modelId = $stateParams.modelId;
            viewerService.getModelTree(modelId).then(getModelTreeSuccess, failureMsg);
        }

        function failureMsg() {}

        function getModelTreeSuccess(response) {
            vm.modelTree = [response.data];
            $rootScope.$broadcast("model-tree-initialized", vm.modelTree);
            vm.showTree = true;

            viewerHelperService.setPartTreeTopId(vm.modelTree[0].objectId);

            resetViewerSelections();
        }

        $scope.$on("toggle-all-locked-nodes", function () {
            var updatedLockedNodes = [];
            if (!isAllUnlocked) {
                $rootScope.$emit(
                    "unlockAllNodesEvent",
                    ivhTreeviewBfs,
                    vm.modelTree,
                    vm.customOpts,
                    initiallyLockedNodes,
                    updatedLockedNodes
                );
                isAllUnlocked = true;
            } else {
                var lockedBulkNodes = [];
                initiallyLockedNodes.forEach(function (node) {
                    node.isLocked = true;
                    lockedBulkNodes.push([node.objectId]);
                });
                isAllUnlocked = false;
                initiallyLockedNodes = [];
            }
            $rootScope.$broadcast("unlock-children-of-locked-nodes");
            $timeout(function () {
                doExplosion();
            });
        });

        function resetViewerSelections() {
            ivhTreeviewBfs(vm.modelTree, vm.customOpts, function (node) {
                node.viewerSelected = false;
                node.hasChildSelected = false;
                return true;
            });
        }

        function calculateExplodeNodes() {
            var lockedForExplodeNodes = [];
            ivhTreeviewBfs(vm.modelTree, vm.customOpts, function (node) {
                if (node.isLocked) {
                    lockedForExplodeNodes.push([node.objectId]);
                    return false;
                }
            });
            return lockedForExplodeNodes;
        }

        function onSelectionChange(partNode) {
            if (partNode.selected) {
                viewerHelperService.showParts(partNode.objectId);
            } else {
                viewerHelperService.hideParts(partNode.objectId);
            }
            updateShowHideNodes();
        }

        function toggleChange(partNode) {
            if (partNode.isLocked) {
                ivhTreeviewMgr.collapse(vm.modelTree, partNode, vm.customOpts);
            }
        }

        function updateShowHideNodes() {
            resetViewerSelections();
            var hiddenNodes = [];
            var visibleNodes = [];
            ivhTreeviewBfs(vm.modelTree, vm.customOpts, function (node) {
                if (node.selected) {
                    visibleNodes.push(node.objectId);
                } else {
                    hiddenNodes.push(node.objectId);
                }
                return true;
            });
            //viewerVariablesService.setHiddenNodes(hiddenNodes);
            viewerVariablesService.setVisibleNodes(visibleNodes);
        }

        function minimizeModelBrowser() {
            vm.isMinimized = true;
        }

        function maximizeModelBrowser() {
            vm.isMinimized = false;
        }

        function expandNodes() {
            ivhTreeviewMgr.expandRecursive(vm.modelTree, vm.modelTree, vm.customOpts, true);
        }

        function expand() {
            ivhTreeviewMgr.expandRecursive(vm.modelTree, vm.modelTree, vm.customOpts, true);
        }

        function collapse() {
            ivhTreeviewMgr.expandRecursive(vm.modelTree, vm.modelTree, vm.customOpts, false);
        }

        function clearSearch() {
            vm.browserFilter = "";
        }

        function hideChildNodes(childPart) {
            angular.forEach(childPart.childParts, function (gc) {
                gc.selected = false;
                hideChildNodes(gc);
            });
        }
        $scope.$on("reset-viewer-clicked", function () {
            ivhTreeviewMgr.selectAll(vm.modelTree, vm.customOpts, true);
        });

        $scope.$on("hide-parts", function (event, hiddenParts) {
            ivhTreeviewBfs(vm.modelTree, vm.customOpts, function (node, parents) {
                if (hiddenParts.indexOf(node.objectId) > -1) {
                    node.selected = false;
                    angular.forEach(parents, function (p) {
                        p.selected = false;
                    });
                    angular.forEach(node.childParts, function (c) {
                        c.selected = false;
                        hideChildNodes(c);
                    });
                }
                return true;
            });
            updateShowHideNodes();
        });

        $scope.$on("show-parts", function (event, shownParts) {
            ivhTreeviewBfs(vm.modelTree, vm.customOpts, function (node) {
                if (shownParts.indexOf(node.objectId) > -1) {
                    node.selected = true;
                }
                return true;
            });
            ivhTreeviewMgr.validate(vm.modelTree, vm.customOpts, false);
            updateShowHideNodes();
        });

        $scope.$on("locked-model-tree-updated", function (event, args) {
            ivhTreeviewBfs(vm.modelTree, vm.customOpts, function (node) {
                if (args.indexOf(node.objectId) > -1) {
                    node.isLocked = true;
                    ivhTreeviewMgr.collapse(vm.modelTree, node, vm.customOpts);
                } else {
                    node.isLocked = false;
                }
            });
        });

        $scope.$on("viewer-part-selected", function (event) {
            $timeout(function () {
                var args = viewerHelperService.getSelectedParts();
                ivhTreeviewBfs(vm.modelTree, vm.customOpts, function (node, parents) {
                    var isSelectedInViewer = args.indexOf(node.objectId) > -1;
                    node.viewerSelected = isSelectedInViewer;
                    node.hasChildSelected = isSelectedInViewer;
                    if (isSelectedInViewer) {
                        angular.forEach(parents, function (p) {
                            p.hasChildSelected = true;
                        });
                    }
                    return true;
                });
            }, 10);
        });

        $scope.$on("unlock-children-of-locked-nodes", function () {
            var lockedNodes = [];
            ivhTreeviewBfs(vm.modelTree, vm.customOpts, function (node, parents) {
                if (node.isLocked) {
                    lockedNodes.push([node.objectId]);
                    ivhTreeviewMgr.collapse(vm.modelTree, node, vm.customOpts);
                }
            });

            bulkAddIdToExplodeAsSingleEntity(lockedNodes);
        });

        $scope.$on("collapse-nodes-children", function (event, partNode) {
            ivhTreeviewMgr.collapse(vm.modelTree, partNode, vm.customOpts);
        });

        $scope.$on("node-link-part-added", function (event, nodeToUpdate) {
            ivhTreeviewBfs(vm.modelTree, vm.customOpts, function (node) {
                if (node.objectId === nodeToUpdate) {
                    node.linkedPart = true;
                    return false;
                }
            });
        });

        $scope.$on("node-link-part-deleted", function (event, nodeToUpdate) {
            ivhTreeviewBfs(vm.modelTree, vm.customOpts, function (node) {
                if (node.objectId === nodeToUpdate) {
                    node.linkedPart = false;
                    return false;
                }
            });
        });

        $scope.$on("non-modeled-part-added", function (event, nodeToUpdate) {
            ivhTreeviewBfs(vm.modelTree, vm.customOpts, function (node) {
                if (node.objectId === nodeToUpdate) {
                    node.nonModeledPart = true;
                    return false;
                }
            });
        });

        $scope.$on("non-modeled-part-deleted", function (event, nodeToUpdate) {
            ivhTreeviewBfs(vm.modelTree, vm.customOpts, function (node) {
                if (node.objectId === nodeToUpdate) {
                    node.nonModeledPart = false;
                    return false;
                }
            });
        });
    }
})();
