(function () {
    "use strict";

    angular.module("app.parts").controller("editKitDetailController", editKitDetailController);

    editKitDetailController.$inject = ["$uibModalInstance", "data"];

    function editKitDetailController($uibModalInstance, data) {
        var vm = this;

        vm.editMasterPartDesc = data.masterPartDesc;
        vm.update = update;
        vm.cancel = $uibModalInstance.dismiss;

        function update() {
            console.log("Updated Desc:", vm.editMasterPartDesc);
            $uibModalInstance.close(vm.editMasterPartDesc);
        }
    }
})();
