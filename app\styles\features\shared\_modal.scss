// =============================================================================
// Modal
// =============================================================================

%absolute-center {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

// Modal Styles
.modal {
    @extend %absolute-center;
    z-index: 12;
    box-sizing: border-box; // Critical fix
    
    // Box shadow
    -webkit-box-shadow: 0 0 8px 0 rgba($midnight, 0.45);
    -moz-box-shadow: 0 0 8px 0 rgba($midnight, 0.45);
    box-shadow: 0 0 8px 0 rgba($midnight, 0.45);
    
    // Core sizing
    width: auto;
    min-width: 450px;
    max-width: calc(100vw - 40px);
    
    // Content constraints
    max-height: 90vh;
    height: auto;
    overflow-y: auto;
    display: inline-block;
    
    // Modern browsers
    @supports (width: max-content) {
        width: max-content;
        max-width: calc(100vw - 40px);
    }

    // Desktop very large
    @media (min-width: 1000px) {
        width: calc(100vw - 20px);
        max-width: 1200px;
    }
    
    // Desktop large
    @media (min-width: 800px) and (max-width: 999px) {
        width: calc(100vw - 20px);
        max-width: 800px;
    }
    
    // Tablet range
    @media (max-width: 799px) and (min-width: 601px) {
        width: calc(100vw - 60px);
        max-width: 600px;
    }
    
    // Mobile
    @media (max-width: 600px) {
        min-width: unset !important;
        width: calc(100vw - 20px);
        max-width: calc(100vw - 20px);
    }

    .error-alert {
        color: $red;
        margin: 0;
        background: lighten($red, 45%);
        padding: $spacing;
        border: 1px solid $red;
        @include border-radius(4px);
        margin-bottom: $spacing * 2;
    }

    .input-group > .custom-select:not(:first-child),
    .input-group > .form-control:not(:first-child) {
        border-top-left-radius: 3px;
        border-bottom-left-radius: 3px;
    }

    .select-box {
        background: $lightback;
        line-height: normal;
        display: block;
        padding: 4px 8px;

        select {
            height: 30px;
        }
    }

    .modal-header {
        background: $lightback;
        padding: $spacing * 2 $spacing * 3;
        border-radius: $border-radius * 2 $border-radius * 2 0 0;
    }

    .modal-body {
        padding: $spacing * 2 $spacing * 3;
        max-height: 60vh;
        overflow-x: hidden;

        .modal-title,
        h2,
        h3 {
            margin-bottom: 8px;
            font-size: 18px;
        }
    }

    .modal-content {
        //max-height: calc(90vh - 60px);
        overflow-y: auto;
        border: none;
    }

    .modal-actions {
        padding: $spacing 0 0 0;
        text-align: right;

        .btn {
            margin-left: 5px;
        }
    }

    .width-100 {
        margin-bottom: $spacing !important;
    }

    .modal-title,
    h2,
    h3 {
        margin-bottom: 0;
        font-size: 18px;
    }

    input[type="text"],
    input[type="email"],
    .select-box {
        width: 100%;
        margin: 0;
    }

    .close {
        position: absolute;
        top: 20px;
        right: 24px;
        background: transparent;
        border: 0;
        color: lighten($textdark, 10%);
        font-size: 1.2em;
        @include transition(color 0.3s ease);

        &:hover {
            color: $textdark;
        }

        .btn {
        }
    }

    .errortext {
        color: $red;
        margin: 0;
    }
}

body.modal-open {
    overflow: hidden;
}

.overlay-cover {
    width: 100%;
    height: 100%;
    background: rgba($midnight, 0.8);
    z-index: 11;
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
}

.stackModal-overlay-cover {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: rgba(39, 41, 46, 0.8);
    z-index: 999;
}

.modal-backdrop {
    background: rgba(39, 41, 46, 0.8);
}

.stackModal {
    z-index: 1000 !important;
}

.comment {
    display: block;
    width: 100%;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
    transition: all 0.15s ease;
    background: $white;
    border-radius: 4px;
    padding: 10px;
    margin: 0 0 20px 0;

    .comment-message {
        text-align: left;
        margin-bottom: 5px;
        font-family: $body-font;
        word-break: break-word;
    }

    .comment-name {
        text-align: left;
        margin-bottom: 5px;

        small {
            font-weight: 600;
        }
    }
}

.upload-box {
    position: relative;
    width: 100%;
    height: 140px;
    border-radius: 4px;
    border: 2px dashed $divider-color;
    margin-bottom: $spacing;
    align-items: center;
    display: flex;
    color: darken($divider-color, 10%) !important;

    &:hover {
        border: 2px dashed darken($divider-color, 30%);
        color: darken($divider-color, 40%) !important;
    }

    h4 {
        margin: 0 !important;
    }

    div {
        text-align: center;
        margin: 0 auto;
        display: block;

        i {
            font-size: 40px;
            margin-bottom: $spacing * 2;
        }
    }

    input.fileupload {
        position: absolute;
        top: 0;
        left: 0;
        margin: 0;
        padding: 0;
        width: 100%;
        height: 100%;
        outline: none;
        opacity: 0;
        cursor: pointer;
    }
}

.uploaded-file {
    margin: $spacing 0 $spacing * 2 0;
}

.file-uploader-selected {
    color: #28a745; // Green color for text
}
.file-text-success {
    color: #28a745 !important;
}

.error-message {
    color: red;
}

.modal-dialog {
    margin: 0;
    max-width: 100% !important;
    transform: none !important;
}

.overlay-modal {
    z-index: 12;
}
