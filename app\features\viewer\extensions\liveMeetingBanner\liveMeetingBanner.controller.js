(function () {
    'use strict';

    angular
        .module('app.viewer')
        .controller('LiveMeetingBannerController', LiveMeetingBannerController);

    LiveMeetingBannerController.$inject = ['$scope', 'liveMeetingBannerService', 'createMeetingService', '$location', 'userService'];

    function LiveMeetingBannerController($scope, liveMeetingBannerService, createMeetingService, $location, userService) {
        var vm = this;

        vm.notificationDisplay = false;
        vm.timeout = 0;
        vm.presenter = null;
        vm.users = [];
        vm.endMeeting = endMeeting;

        $scope.$on("meetingNotification", createBanner);

        vm.closeNotification = closeNotification;

        function createBanner() {
            vm.notificationDisplay = false;
            var notification = liveMeetingBannerService.getNotification();
            
            if (notification && !notification.removeBanner) {
                vm.notificationDisplay = true;
                vm.presenter = notification.presenter;
                vm.activeUsers = notification.users;
                vm.isHost = false;
                var meetingGuid =  $location.search().guid;
                createMeetingService.getInvitedUsers()
                .then( users => {
                if(users && (users.host === userService.getFullName()) && (meetingGuid === users.roomId)){
                    vm.isHost = true;
                    vm.users = [];
                    users.invitedUsers.forEach(element => {
                        let flag = false;
                        vm.activeUsers.forEach( userName => {
                            if(element.name === userName) {
                                flag = true;
                                return;
                            }
                        });
                        vm.users.push({name:element.name, isActive: flag})
                    });
                    vm.hostName = users.host;
                }
                });
            } else {
                vm.notificationDisplay = false;
            }
        }

        function closeNotification() {
            liveMeetingBannerService.removeNotification();
            vm.notificationDisplay = false;
        }

        function endMeeting(){
            closeNotification();
            var removeEvents = liveMeetingBannerService.getMethodToRemoveEvents();
            removeEvents();  
            vm.socket = createMeetingService.getSocket();
            vm.socket.emit("deleteRoom", vm.isHost, function(isRoomDeleted){
                if(isRoomDeleted) {
                    localStorage.removeItem("liveMeetingInfo");
                    vm.socket.emit("disconnect");
                    console.log("Meeting is stopped");
                }
                else {
                    console.log("Error to delet the room");
                }
            });
        }
    }

})();
