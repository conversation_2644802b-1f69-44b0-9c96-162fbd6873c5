<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" ng-click="editNameAndNotesCtrl.cancel()"
            aria-label="Close"><i class="fa fa-close" aria-hidden="true"></i>
    </button>
    <h2 class="modal-title" translate>NAMES_NOTES.PLEASE_ENTER_NAME</h2>
</div>
<div class="modal-body">
    <form ng-submit="editNameAndNotesCtrl.ok()" class="form">
        <h4 translate>NAMES_NOTES.SNAP_TITLE</h4>
        <input type="text" ng-model="editNameAndNotesCtrl.name" placeholder="{{'NAMES_NOTES.AXLE_CHAIN' | translate}}" autofocus>

        <h4>{{'NAMES_NOTES.SNAP_NOTES' | translate}} <span class="not-bold">({{'NAMES_NOTES.OPTIONAL' | translate}})</span></h4>
        <textarea ng-model="editNameAndNotesCtrl.notes" class="notes"></textarea>

    </form>

    <div class="modal-actions">
        <button class="btn small secondary" type="button" ng-click="editNameAndNotesCtrl.cancel()" translate>GENERAL.CANCEL</button> &nbsp;
        <button class="btn small primary" type="button" ng-click="editNameAndNotesCtrl.ok()" translate>NAMES_NOTES.SAVE</button>
    </div>
</div>
