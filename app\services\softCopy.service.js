(function () {
    'use strict';

    angular
        .module('app.services')
        .factory('softCopyService', softCopyService);

    softCopyService.$inject = ['$http', 'apiConstants', '$q'];

    function softCopyService($http, apiConstants, $q) {
        return {
            fetchSoftCopies: fetchSoftCopies,
            createSoftCopy: createSoftCopy,
            updateSoftCopy: updateSoftCopy,
            deleteSoftCopy: deleteSoftCopy,
            getSoftCopyDetailsForViewable: getSoftCopyDetailsForViewable
        };

        function fetchSoftCopies(modelId) {
            return $http.get(apiConstants.url + '/softCopy/model/' + modelId);
        }

        function createSoftCopy(modelId, settings) {
            return $http.post(apiConstants.url + '/softCopy/model/' + modelId, {name: settings.softCopyName},
                {params: {copySnapshots: settings.copySnapshots}});
        }

        function updateSoftCopy(softcopyId, softCopy) {
            return $http.put(apiConstants.url + '/softCopy/' + softcopyId, softCopy);
        }

        function deleteSoftCopy(softcopyId) {
            return $http.delete(apiConstants.url + '/softCopy/' + softcopyId);
        }

        function getSoftCopyDetailsForViewable(viewableId) {
            return $http.get(apiConstants.url + '/softCopy/viewable/' + viewableId);
        }
    }
})();
