(function () {
    "use strict";

    angular
        .module("cadshareApp")

        .config([
            "$stateProvider",
            "$urlRouterProvider",
            function ($stateProvider, $urlRouterProvider) {
                $urlRouterProvider.otherwise("/notFound");

                $stateProvider
                    .state("notFound", {
                        url: "/notFound",
                        templateUrl: "features/base/notFound.html",
                    })
                    .state("dashboard", {
                        url: "/dashboard",
                        templateUrl: "features/dashboard/dashboard.html",
                        controller: "DashboardController as dashboardCtrl",
                    })
                    .state("orders", {
                        url: "/orders",
                        templateUrl: "features/orders/ordersTabs.html",
                        controller: "OrdersTabsController as ordersTabsCtrl",
                    })
                    .state("products", {
                        url: "/productsTab",
                        templateUrl: "features/products/productsTab.html",
                        controller: "ProductsTabsController as productsTabsCtrl",
                    })
                    .state("products.catalogue", {
                        url: "/productsCatalogue",
                        templateUrl: "features/products/catalogue/productsCatalogue.html",
                        controller: "ProductsCatalogueController as productsCatalogueCtrl",
                    })
                    .state("products.techDocs", {
                        url: "/techDocs",
                        templateUrl: "features/products/techDocs/techDocs.html",
                        controller: "TechDocsController as techDocsCtrl",
                    })
                    .state("products.videos", {
                        url: "/videos",
                        templateUrl: "features/products/videos/videos.html",
                        controller: "VideosController as videosCtrl",
                    })
                    .state("parts", {
                        url: "/parts",
                        templateUrl: "features/parts/partsTabs.html",
                        controller: "PartsTabsController as partsTabsCtrl",
                    })
                    .state("parts.partsUpload", {
                        url: "/partsUpload",
                        templateUrl: "features/parts/partsUpload/partsUpload.html",
                        controller: "PartsUploadController as partsUploadCtrl",
                    })
                    .state("parts.partsSearch", {
                        url: "/partsSearch",
                        templateUrl: "features/parts/partsSearch/partsSearch.html",
                        controller: "PartsSearchController as partsSearchCtrl",
                    })
                    .state("parts.managekits", {
                        url: "/managekits",
                        templateUrl: "features/parts/manageKits/manageKits.html",
                        controller: "ManageKitsController as manageKitsCtrl",
                    })
                    .state("kitassembly", {
                        url: "/managekits/kitsassembly/:kitId",
                        templateUrl: "features/parts/manageKits/editCreate/kitAssembly.html",
                        controller: "KitCreateEditController as kitAssemblyCtrl",
                        params: {
                            kitId: null,
                            origin: { dynamic: true }
                        }
                    })
                    .state("masterPart", {
                        url: "/masterPart/:masterPartId",
                        templateUrl: "features/parts/masterPart/masterPart.html",
                        controller: "MasterPartController as masterPartCtrl",
                    })
                    .state("customerPartSearch", {
                        url: "/customerPartSearch",
                        templateUrl: "features/customerPartSearch/customerPartSearchTabs.html",
                        controller: "CustomerPartSearchTabsController as customerPartSearchTabsCtrl",
                    })
                    .state('customerPartSearch.search', {
                        url: '/customerSearch',
                        templateUrl: 'features/customerPartSearch/customerPartSearch.html',
                        controller: 'CustomerPartSearchController as customerPartSearchCtrl'
                    })
                    .state('customerPartSearch.recent', {
                        url: '/customerRecent',
                        templateUrl: 'features/customerPartSearch/customerPartSearchRecent.html',
                        controller: 'CustomerPartSearchController as customerPartSearchCtrl'
                    })
                    .state('customerPartSearch.frequent', {
                        url: '/customerFrequent',
                        templateUrl: 'features/customerPartSearch/customerPartSearchFrequent.html',
                        controller: 'CustomerPartSearchController as customerPartSearchCtrl'
                    })
                    .state("publishedProducts", {
                        url: "/p-products",
                        templateUrl: "features/customerProducts/customerProducts.html",
                        controller: "CustomerProductsController",
                        controllerAs: "customerProductsCtrl",
                    })
                    .state("publishedProductsOnBehalfOf", {
                        url: "/p-products/:onBehalfOf",
                        templateUrl: "features/customerProducts/customerProducts.html",
                        controller: "CustomerProductsController",
                        controllerAs: "customerProductsCtrl",
                    })
                    .state("customerManual", {
                        url: "/customerManual",
                        templateUrl: "features/customerProducts/customerManuals/customerManual.html",
                        controller: "CustomerManualController as customerManualCtrl",
                    })
                    .state("customerManual.viewables", {
                        url: "/customerViewables/:manualId/:onBehalfOf",
                        templateUrl: "features/customerProducts/customerManuals/viewables/customerViewables.html",
                        controller: "CustomerViewablesController as customerViewablesCtrl",
                        params: {
                            onBehalfOf: null,
                        },
                    })
                    .state("customerManual.techDocs", {
                        url: "/customerTechDocs/:manualId/:onBehalfOf",
                        templateUrl: "features/customerProducts/customerManuals/techDocs/customerTechDocs.html",
                        controller: "CustomerTechDocsController as customerTechDocsCtrl",
                        params: {
                            onBehalfOf: null,
                        },
                    })
                    .state("customerManual.videos", {
                        url: "/customerVideos/:manualId/:onBehalfOf",
                        templateUrl: "features/customerProducts/customerManuals/videos/customerVideos.html",
                        controller: "CustomerVideosController as customerVideosCtrl",
                        params: {
                            onBehalfOf: null,
                        },
                    })
                    .state("customerManual.kits", {
                        url: "/customerKits/:manualId/:onBehalfOf",
                        templateUrl: "features/customerProducts/customerManuals/kits/customerKits.html",
                        controller: "CustomerKitController as customerKitsCtrl",
                        params: {
                            onBehalfOf: null,
                        },
                    })
                    .state("publications", {
                        url: "/publications",
                        templateUrl: "features/publications/publications.html",
                        controller: "PublicationsController as publicationsCtrl",
                    })
                    .state("createPublication", {
                        url: "/create-publication",
                        templateUrl: "features/publications/createPublication.html",
                        controller: "CreatePublicationController as createPublicationCtrl",
                        resolve: {
                            modalObject: function () {
                                return null;
                            }
                        }
                    })
                    .state("editPublication", {
                        url: "/edit-publication/:id",
                        templateUrl: "features/publications/createPublication.html",
                        controller: "CreatePublicationController as createPublicationCtrl",
                        resolve: {
                            modalObject: ['$stateParams', 'manufacturerPublicationService',
                                function ($stateParams, manufacturerPublicationService) {
                                    return manufacturerPublicationService.getPublication($stateParams.id).then(function (publicationResponse) {
                                        var publication = publicationResponse.data;
                                        if (!publication) {
                                            console.error(`Publication with the id ${$stateParams.id} not found`);
                                            return null;
                                        }

                                        // Set the isEdit flag
                                        publication.isEdit = true;
                                        return publication;
                                    }).catch(function (error) {
                                        console.error(`An error occurred while fetching the publication: ${error}`);
                                        return null;
                                    });
                                }
                            ]
                        }
                    })
                    .state("customers", {
                        url: "/customers",
                        templateUrl: "features/customers/customers.html",
                        controller: "CustomersController as customerCtrl",
                    })
                    .state("customerUsers", {
                        url: "/customers/users/:subEntityId/:type/:name",
                        templateUrl: "features/customers/customerUsers.html",
                        controller: "CustomerUserController as customerUserCtrl",
                    })
                    .state("assignPublications", {
                        url: "/customers/:subEntityId/:name/assignPublications",
                        templateUrl: "features/customers/assignPublications/assignPublications.html",
                        controller: "AssignPublicationsController as assignPublicationsCtrl",
                    })
                    .state("assignCategories", {
                        url: "/customers/:subEntityId/:name/assignCategories",
                        templateUrl: "features/customers/assignCategories/assignCategories.html",
                        controller: "AssignCategoriesController as assignCategoriesCtrl",
                    })
                    .state("admin", {
                        url: "/admin",
                        templateUrl: "features/admin/admin.html",
                        controller: "AdminController as adminCtrl",
                    })
                    .state('userSettings', {
                        url: '/userSettings/:userId?mode',
                        templateUrl: 'features/admin/userSettings/userSettings.html',
                        controller: "userSettingsController as userSettingsCtrl",
                        params: {
                            userId: null,
                            createObject: null
                        }
                    })
                    .state("security", {
                        url: "/security",
                        templateUrl: "features/security/security.html",
                        controller: "SecurityController as securityCtrl",
                    })
                    .state("manufacturerInformation", {
                        url: "/manufacturerInformation",
                        templateUrl: "features/manufacturerInformation/manufacturerInformation.html",
                        controller: "ManufacturerInformationController as manufacturerInformationCtrl",
                    })
                    .state("support", {
                        url: "/support",
                        templateUrl: "features/support/support.html",
                        controller: "SupportController as supportCtrl",
                    })
                    .state("login", {
                        url: "/login",
                        templateUrl: "features/base/login/login.html",
                        controller: "LoginController as loginCtrl",
                    })
                    .state("register", {
                        url: "/register",
                        templateUrl: "features/base/register/register.html",
                        controller: "RegisterController as registerCtrl",
                    })
                    .state("forgotPassword", {
                        url: "/forgotpassword",
                        templateUrl: "features/base/password/forgotPassword/forgotPassword.html",
                        controller: "ForgotPasswordController as forgotCtrl",
                        params: { manufacturerId: "", emailAddress: "" },
                    })
                    .state("resetPassword", {
                        url: "/password?code=",
                        templateUrl: "features/base/password/resetPassword/resetPassword.html",
                        controller: "ResetPasswordController as resetCtrl",
                    })
                    .state("resendPassword", {
                        url: "/password/resend?code=",
                        templateUrl: "features/base/password/resendPassword/resendPassword.html",
                        controller: "ResendPasswordController as resendCtrl",
                    })
                    .state("lockConfirmation", {
                        url: "/lockConfirmation/user/:userId",
                        templateUrl: "features/base/lockConfirmation/lockConfirmation.html",
                        controller: "LockAccountController as lockAccountCtrl",
                    })
                    .state("splitButtonDropdown", {
                        url: "/splitButtonDropdown",
                        templateUrl: "styles/components/buttons/splitButtonDropdown/splitButtonDropdown.html",
                        controller: "splitButtonDropdownController as splitButtonDropdownCtrl",
                    })
                    .state("customerPdfViewer", {
                        url: "/customerPdfViewer/:machineName/:modelId/:viewableName/:autodeskURN/:manualId/:productId/:onBehalfOf",
                        templateUrl: "features/viewer/customerPDF/customerPdfViewer.html",
                        controller: "CustomerPdfViewerController as customerPdfViewerCtrl",
                        params: {
                            manualId: null,
                            productId: null,
                            onBehalfOf: null,
                        },
                    })
                    .state("pdfViewer", {
                        url: "/adminViewer/:productId/:machineName/:modelId/:viewableName/:autodeskURN",
                        templateUrl: "features/viewer/pdf/pdfViewer.html",
                        controller: "PDFViewerController as pdfViewerCtrl",
                    })
                    .state("productsModels", {
                        url: "/products/:productId/:machineName",
                        templateUrl: "features/viewable/viewable.html",
                        controller: "ManufacturerViewableController as manufacturerViewableCtrl",
                    })
                    .state("manufacturerViewer", {
                        url: "/manufacturerViewer/:productId/:machineName/:modelId/:viewableName/:autodeskURN/:translateType?guid={{meetingGuid}}",
                        templateUrl: "features/viewer/manufacturerViewer/manufacturerViewer.html",
                        controller: "ManufacturerViewerController as manufacturerViewerCtrl",
                    })
                    .state("customerViewer", {
                        url: "/customerViewer/:machineName/:modelId/:viewableName/:autodeskURN/:manualId/:productId/:translateType/:onBehalfOf/?guid={{ roomGuid }}&partNumber",
                        templateUrl: "features/viewer/customer3D/customerViewer.html",
                        controller: "CustomerViewerController as customerViewerCtrl",
                        params: {
                            manualId: null,
                            productId: null,
                            onBehalfOf: null,
                        },
                    })

                    .state("softCopy", {
                        url: "/products/:productId/:machineName/:modelId/:modelName/softCopy/",
                        templateUrl: "features/softCopy/softCopy.html",
                        controller: "SoftCopyController as softCopyCtrl",
                    })

                    .state("softCopyViewer", {
                        url: "/softCopyViewer/:productId/:machineName/:modelId/:modelName/:viewableName/:autodeskURN/:softCopyId/:viewableId/:translateType",
                        templateUrl: "features/viewer/softCopy/softCopyViewer.html",
                        controller: "SoftCopyViewerController as softCopyViewerCtrl",
                    })

                    .state("workInstructions", {
                        url: "/products/:productId/:machineName/:modelId/:modelName/workInstructions/",
                        templateUrl: "features/workInstructions/workInstructions.html",
                        controller: "WorkInstructionsController as workInstructionsCtrl",
                    })
                    .state("manufacturerWorkInstructionsViewer", {
                        url: "/manufacturerWorkInstructionsViewer/:productId/:machineName/:modelId/:viewableName/:autodeskURN/:workInstructionsId/:viewableId/:translateType",
                        templateUrl: "features/viewer/workInstructions/manufacturer/manufacturerWorkInstructionsViewer.html",
                        controller: "ManufacturerWorkInstructionsController as manufacturerWorkInstructionsCtrl",
                    })
                    .state("preAuth", {
                        url: "/preAuth?client&token",
                        templateUrl: "features/base/login/preAuth.html",
                        controller: "PreAuthController as preAuthCtrl",
                    })
                    .state("orders.enquiries", {
                        url: "/enquiries",
                        templateUrl: "features/orders/shared/orders/orders.html",
                        controller: "EnquiriesController as ordersListCtrl",
                    })
                    .state("orders.enquiry", {
                        url: "/enquiry/:orderId",
                        templateUrl: "features/orders/shared/order/order.html",
                        controller: "EnquiryController as viewOrderCtrl",
                    })
                    .state("orders.quotations", {
                        url: "/quotations",
                        templateUrl: "features/orders/shared/orders/orders.html",
                        controller: "QuotationsController as ordersListCtrl",
                    })
                    .state("orders.quotation", {
                        url: "/quotation/:orderId",
                        templateUrl: "features/orders/shared/order/order.html",
                        controller: "QuotationController as viewOrderCtrl",
                    })
                    .state("orders.liveorders", {
                        url: "/liveorders",
                        templateUrl: "features/orders/shared/orders/orders.html",
                        controller: "LiveOrdersController as ordersListCtrl",
                    })
                    .state("orders.liveorder", {
                        url: "/liveorder/:orderId",
                        templateUrl: "features/orders/shared/order/order.html",
                        controller: "LiveOrderController as viewOrderCtrl",
                    })
                    .state("orders.historicalorders", {
                        url: "/historicalorders",
                        templateUrl: "features/orders/shared/orders/orders.html",
                        controller: "HistoricalOrdersController as ordersListCtrl",
                    })
                    .state("orders.historicalorder", {
                        url: "/historicalorder/:orderId",
                        templateUrl: "features/orders/shared/order/order.html",
                        controller: "HistoricalOrderController as viewOrderCtrl",
                    })
                    .state("create", {
                        url: "/create/:onBehalfOf",
                        templateUrl: "features/orders/create/create.html",
                        controller: "CreateController as createCtrl",
                        params: {
                            onBehalfOf: null,
                            orderDetails: null
                        }
                    })
                    .state("checkout", {
                        url: "/create/checkout",
                        templateUrl: "features/orders/checkout/checkout.html",
                        controller: "CheckoutController as checkoutCtrl",
                    })
                    .state("kit", {
                        url: "/parts/kit/:id/:masterPartId",
                        templateUrl: "features/parts/extensions/kits/kit.html",
                        controller: "KitController as kitCtrl",
                    })
                    .state("optionSet", {
                        url: "/parts/optionSet/:id/:masterPartId",
                        templateUrl: "features/parts/extensions/optionSet/optionSet.html",
                        controller: "OptionSetController as optionSetCtrl",
                    })
                    .state("AdditionalPart", {
                        url: "/parts/additionalPart/:isEdit/:masterPartId",
                        templateUrl: "features/parts/extensions/additionalPart/additionalPart.html",
                        controller: "AdditionalPartController as additionalPartCtrl",
                    })
                    .state("contactUs", {
                        url: "/contactUs",
                        templateUrl: "features/contactUs/contactUs.html",
                        controller: "ContactUsController as contactUsCtrl",
                    })
                    .state("priceManagement", {
                        url: "/priceManagement",
                        templateUrl: "features/dealerPlus/priceManagement/priceManagement.html",
                        controller: "PriceManagementController as priceMgmtCtrl",
                    })
                    .state("dpMasterPart", {
                        url: "/dpMasterPart/:masterPartId",
                        templateUrl: "features/dealerPlus/masterPart/dpMasterPart.html",
                        controller: "DPMasterPartController as DPMasterPartCtrl",
                    })
                    .state("dpCustomers", {
                        url: "/dpCustomers",
                        templateUrl: "features/dealerPlus/customers/dpCustomers.html",
                        controller: "DPCustomersController as dpCustomerCtrl",
                    })
                    .state("dpCustomerUsers", {
                        url: "/dpCustomers/users/:subEntityId/:type/:name",
                        templateUrl: "features/dealerPlus/customers/customerUsers/dpCustomerUsers.html",
                        controller: "DPCustomerUserController as dpCustomerUserCtrl",
                    })
                    .state("dpAssignPublications", {
                        url: "/dpCustomers/:subEntityId/:name/assignPublications",
                        templateUrl: "features/dealerPlus/customers/assignPublications/dpAssignPublications.html",
                        controller: "DPAssignPublicationsController as dpAssignPublicationsCtrl",
                    })
                    .state("dpOrders", {
                        url: "/dpOrders",
                        templateUrl: "features/dealerPlus/orders/dpOrders.html",
                        controller: "DPOrdersTopLevelController as DPOrdersTopTabCtrl",
                    })
                    .state("dpOrders.myOrders", {
                        url: "/myOrders",
                        templateUrl: "features/dealerPlus/orders/myOrders/myOrders.html",
                    })
                    .state("dpOrders.customerOrders", {
                        url: "/customerOrders",
                        templateUrl: "features/dealerPlus/orders/customerOrders/customerOrders.html",
                    })

                    .state("dpOrders.myOrders.orders", {
                        url: "/orders",
                        templateUrl: "features/orders/ordersTabs.html",
                        controller: "OrdersTabsController as ordersTabsCtrl",
                    })
                    .state("dpOrders.customerOrders.orders", {
                        url: "/orders",
                        templateUrl: "features/dealerPlus/orders/customerOrders/dpOrdersTabs.html",
                        controller: "DPOrdersTabsController as dpOrdersTabCtrl",
                    })
                    .state("dpOrders.myOrders.orders.enquiries", {
                        url: "/enquiries",
                        templateUrl: "features/orders/shared/orders/orders.html",
                        controller: "EnquiriesController as ordersListCtrl",
                    })
                    .state("dpOrders.myOrders.orders.enquiry", {
                        url: "/enquiry/:orderId",
                        templateUrl: "features/orders/shared/order/order.html",
                        controller: "EnquiryController as viewOrderCtrl",
                    })
                    .state("dpOrders.myOrders.orders.quotations", {
                        url: "/quotations",
                        templateUrl: "features/orders/shared/orders/orders.html",
                        controller: "QuotationsController as ordersListCtrl",
                    })
                    .state("dpOrders.myOrders.orders.quotation", {
                        url: "/quotation/:orderId",
                        templateUrl: "features/orders/shared/order/order.html",
                        controller: "QuotationController as viewOrderCtrl",
                    })
                    .state("dpOrders.myOrders.orders.liveorders", {
                        url: "/liveorders",
                        templateUrl: "features/orders/shared/orders/orders.html",
                        controller: "LiveOrdersController as ordersListCtrl",
                    })
                    .state("dpOrders.myOrders.orders.liveorder", {
                        url: "/liveorder/:orderId",
                        templateUrl: "features/orders/shared/order/order.html",
                        controller: "LiveOrderController as viewOrderCtrl",
                    })
                    .state("dpOrders.myOrders.orders.historicalorders", {
                        url: "/historicalorders",
                        templateUrl: "features/orders/shared/orders/orders.html",
                        controller: "HistoricalOrdersController as ordersListCtrl",
                    })
                    .state("dpOrders.myOrders.orders.historicalorder", {
                        url: "/historicalorder/:orderId",
                        templateUrl: "features/orders/shared/order/order.html",
                        controller: "HistoricalOrderController as viewOrderCtrl",
                    })

                    .state("dpOrders.customerOrders.orders.enquiries", {
                        url: "/enquiries",
                        templateUrl: "features/dealerPlus/orders/customerOrders/shared/orders/dpOrders.html",
                        controller: "DPEnquiriesController as dpOrdersListCtrl",
                    })
                    .state("dpOrders.customerOrders.orders.enquiry", {
                        url: "/enquiry/:orderId",
                        templateUrl: "features/dealerPlus/orders/customerOrders/shared/order/dpOrder.html",
                        controller: "DPEnquiryController as dpViewOrderCtrl",
                    })
                    .state("dpOrders.customerOrders.orders.quotations", {
                        url: "/quotations",
                        templateUrl: "features/dealerPlus/orders/customerOrders/shared/orders/dpOrders.html",
                        controller: "DPQuotationsController as dpOrdersListCtrl",
                    })
                    .state("dpOrders.customerOrders.orders.quotation", {
                        url: "/quotation/:orderId",
                        templateUrl: "features/dealerPlus/orders/customerOrders/shared/order/dpOrder.html",
                        controller: "DPQuotationController as dpViewOrderCtrl",
                    })
                    .state("dpOrders.customerOrders.orders.liveorders", {
                        url: "/liveorders",
                        templateUrl: "features/dealerPlus/orders/customerOrders/shared/orders/dpOrders.html",
                        controller: "DPLiveOrdersController as dpOrdersListCtrl",
                    })
                    .state("dpOrders.customerOrders.orders.liveorder", {
                        url: "/liveorder/:orderId",
                        templateUrl: "features/dealerPlus/orders/customerOrders/shared/order/dpOrder.html",
                        controller: "DPLiveOrderController as dpViewOrderCtrl",
                    })
                    .state("dpOrders.customerOrders.orders.historicalorders", {
                        url: "/historicalorders",
                        templateUrl: "features/dealerPlus/orders/customerOrders/shared/orders/dpOrders.html",
                        controller: "DPHistoricalOrdersController as dpOrdersListCtrl",
                    })
                    .state("dpOrders.customerOrders.orders.historicalorder", {
                        url: "/historicalorder/:orderId",
                        templateUrl: "features/dealerPlus/orders/customerOrders/shared/order/dpOrder.html",
                        controller: "DPHistoricalOrderController as dpViewOrderCtrl",
                    });
            },
        ])

        .run([
            "$transitions",
            "userService",
            "$state",
            "$rootScope",
            "$window",
            function ($transitions, userService, $state, $rootScope, $window) {
                $transitions.onError({}, function (transition) {
                    console.log(transition);
                });

                $transitions.onSuccess({}, function (transition) {
                    $rootScope.$broadcast("Page-changed");
                    if (transition.to().name.toUpperCase() === "ORDERS" && transition.from().name.toUpperCase().includes("ORDERS.")) {
                        $rootScope.$broadcast("Order-page-refresh");
                    }
                });

                $transitions.onStart({}, function (transition) {
                    if (transition.from().name === "orders.enquiry") {
                        var hasBeenEdited = angular.element(document.getElementById("order-page-identifier")).scope()
                            .viewOrderCtrl.isOrderEdited;
                        if (hasBeenEdited) {
                            if (!confirm("You may have unsaved edits on this page. Are you sure you want to leave it without saving?")) {
                                return false;
                            }
                        }
                    }

                    if (transition.from().name.includes("dpOrders.myOrders.orders")) {
                        if (
                            transition.to().name.includes("orders") &&
                            !transition.to().name.includes("customerOrders") &&
                            !transition.to().name.includes("dpOrders.myOrders.orders")
                        ) {
                            var to = transition.to().name;
                            var newTo = to.replace("orders", "dpOrders.myOrders.orders");
                            var target = $state.target(newTo, transition.params());
                            return target;
                        }
                    }

                    accessAllowed(transition.to().name).then((isAllowed) => {
                        if (!isAllowed) {
                            $state.go("login");
                            return false;
                        }
                    });

                    if (transition.to().name === "notFound") {
                        if (userService.hasPublishedProductsRole()) {
                            $state.go("publishedProducts");
                            return false;
                        } else if (userService.hasDashboardRole()) {
                            $state.go("dashboard");
                            return false;
                        } else if (userService.hasOrderRole()) {
                            $state.go("orders");
                            return false;
                        } else if (userService.hasProductsRole()) {
                            $state.go("products.catalogue");
                            return false;
                        } else if (userService.hasPublicationRole()) {
                            $state.go("publications");
                            return false;
                        } else if (userService.hasPartsRole()) {
                            $state.go("parts");
                            return false;
                        } else if (userService.hasCustomerRole()) {
                            $state.go("customers");
                            return false;
                        } else if (userService.hasAdminRole()) {
                            $state.go("admin");
                            return false;
                        } else if (userService.hasSecurityRole()) {
                            $state.go("security");
                            return false;
                        }
                    }
                    return checkUserHasRoleForPage(transition);
                });

                function accessAllowed(toName) {
                    return new Promise(function (resolve) {
                        var isAccessAllowed =
                            toName === "login" ||
                            toName === "forgotPassword" ||
                            toName === "resetPassword" ||
                            toName === "resendPassword" ||
                            toName === "lockConfirmation" ||
                            toName === "preAuth" ||
                            toName === "register";
                        if (isAccessAllowed) resolve(isAccessAllowed);
                        else {
                            userService.isUserLoggedIn().then((isUserLoggedIn) => resolve(isUserLoggedIn));
                        }
                    });
                }

                function checkUserHasRoleForPage(transition) {
                    var topLevelSection = transition.to().name.split(".");
                    var hasPermissions = true;
                    switch (topLevelSection[0]) {
                        case "dashboard":
                            hasPermissions = userService.hasDashboardRole();
                            break;
                        case "orders":
                            hasPermissions = userService.hasOrderRole();
                            break;
                        case "publications":
                            hasPermissions = userService.hasPublicationRole();
                            break;
                        case "products":
                            hasPermissions = userService.hasProductsRole();
                            break;
                        case "publishedProducts":
                            hasPermissions = userService.hasPublishedProductsRole();
                            break;
                        case "customers":
                            hasPermissions = userService.hasCustomerRole();
                            break;
                        case "admin":
                            hasPermissions = userService.hasAdminRole();
                            break;
                        case "security":
                            hasPermissions = userService.hasSecurityRole();
                            break;
                        case "parts":
                            hasPermissions = userService.hasPartsRole();
                            break;
                        case "partSearch":
                            hasPermissions = userService.hasPartSearchRole();
                            break;
                        case "support":
                            hasPermissions = userService.hasSupportRole();
                            break;
                        case "dpOrders":
                            hasPermissions = userService.isDealerPlusUser();
                            break;
                        case "priceManagement":
                            hasPermissions = userService.isDealerPlusUser();
                            break;
                        case "dpMasterPart":
                            hasPermissions = userService.isDealerPlusUser();
                            break;
                        case "dpCustomers":
                            hasPermissions = userService.isDealerPlusUser();
                            break;
                        case "dpCustomerUsers":
                            hasPermissions = userService.isDealerPlusUser();
                            break;
                    }
                    return hasPermissions;
                }
            },
        ]);
})();
