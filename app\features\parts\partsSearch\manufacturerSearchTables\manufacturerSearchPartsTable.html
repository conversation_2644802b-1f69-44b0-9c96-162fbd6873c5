<h2 ng-if="partsSearchCtrl.masterParts.length > 0" translate>ORDER.PARTS</h2>

<div class="responsiveContainer py-0">

    <table class="table table-bordered tableFixedWidth" ng-show="partsSearchCtrl.masterParts.length > 0">
        <thead>
            <tr>
                <th ng-class="partsSearchCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'"
                    ng-click="partsSearchCtrl.part_sort='partNumber'; partsSearchCtrl.sortReverse = !partsSearchCtrl.sortReverse"
                    translate>PART_SEARCH.PART_NUM</th>
                <th ng-class="partsSearchCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'"
                    ng-class="partsSearchCtrl.isPreviewStockLevelEnabled ? 'width-40' : 'width-60'"
                    ng-click="partsSearchCtrl.part_sort='description'; partsSearchCtrl.sortReverse = !partsSearchCtrl.sortReverse"
                    translate>PART_SEARCH.DESC</th>
                <th ng-class="partsSearchCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'"
                    ng-if="partsSearchCtrl.isPreviewStockLevelEnabled && !partsSearchCtrl.isStockWarehousesEnabled"
                    ng-click="partsSearchCtrl.part_sort='stock'; partsSearchCtrl.sortReverse = !partsSearchCtrl.sortReverse"
                    translate>PART_SEARCH.STOCK</th>
                <th translate>GENERAL.ACTIONS</th>
            </tr>
        </thead>

        <tbody>
            <tr ng-repeat="part in partsSearchCtrl.masterParts | orderBy:partsSearchCtrl.part_sort:partsSearchCtrl.sortReverse">
                <td data-label="{{'PART_SEARCH.PART_NUM' | translate}}">
                    {{part.partNumber}}
                    <p ng-if="" class="supersedePartNumber"> 
                    <small>
                        {{'PART_SEARCH.SUPERSEDES' | translate}} <span class="font-weight-bold">{{part.supersessionPartNumber}}</span>
                    </small>
                    </p>
                </td>
                <td data-label="{{'PART_SEARCH.DESC' | translate}}">{{part.description ? part.description : part.partDescription}}</td>

                <td class="disableWordBreak" data-label="{{'PART_SEARCH.STOCK' | translate}}"
                    ng-if="partsSearchCtrl.isPreviewStockLevelEnabled && !partsSearchCtrl.isStockWarehousesEnabled">
                    <span title="{{'ORDER.IN_STOCK' | translate}}" tooltip-trigger="outsideClick"
                        uib-tooltip="{{'ORDER.IN_STOCK' | translate}}" class="success-alert" ng-if="part.stock >= 3"><i
                            class="fas fa-layer-group text-success pointer"></i></span>
                    <span title="{{'ORDER.LOW_STOCK' | translate}}" tooltip-trigger="outsideClick"
                        uib-tooltip="{{'ORDER.LOW_STOCK' | translate}}" class="warning-alert"
                        ng-if="part.stock < 3 && part.stock > 0 "><i
                            class="fas fa-layer-group text-warning pointer"></i></span>
                    <span title="{{'ORDER.STOCK_SUBJECT' | translate}}" tooltip-trigger="outsideClick"
                        uib-tooltip="{{'ORDER.STOCK_SUBJECT' | translate}}" class="warning-alert "
                        ng-if="part.stock === null || part.stock < 1"><i
                            class="fas fa-layer-group text-danger pointer"></i></span>
                </td>

                <td>
                    <div class="d-flex justify-content-center cadSplitDropdown">
                        <div class="btn-group btn-hover">
                            <button type="button" class="btn secondary main-action ng-binding"
                                ng-click="partsSearchCtrl.goToMasterPart(part.masterPartId)" translate>
                                PART_SEARCH.VIEW_PART
                            </button>
                            <div class="dropdown-split">
                                <button type="button" class="btn secondary dropdown-toggle dropdown-toggle-split"
                                    data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <span class="sr-only">Toggle Dropdown</span>
                                </button>
                                <div class="dropdown-menu">
                                    <ul class="list-unstyled m-0 p-0">
                                        <li>
                                            <a href="javascript:void(0)" class="px-3 py-2 dark-secondary ng-binding"
                                                ng-click="partsSearchCtrl.goToMasterPart(part.masterPartId)">
                                                <i class="fa fa-fw fa-cog"></i>
                                                {{'PART_SEARCH.VIEW_PART' | translate}}
                                            </a>
                                        </li>
                                        <li>
                                            <a href="javascript:void(0)" class="px-3 py-2 dark-secondary ng-binding"
                                                ng-click="partsSearchCtrl.whereUsedModal(part)">
                                                <i class="fa fa-fw fa-cubes"></i>
                                                {{'PART_SEARCH.WHERE_USED' | translate}}
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </td>

            </tr>

        </tbody>

    </table>

    </div>