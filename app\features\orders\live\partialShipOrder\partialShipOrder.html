<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" ng-click="partialShipOrderCtrl.cancel()"
            aria-label="Close"><i class="fa fa-close" aria-hidden="true"></i>
    </button>
    <h2 class="modal-title">{{"SHIP_ITEMS.TITLE" | translate}} #{{partialShipOrderCtrl.displayId}}</h2>
</div>

<div class="modal-body">

    <p translate>SHIP_ITEMS.CHOOSE_TO_SHIP</p>

    <form class="form">
        <h3 translate>SHIP_ITEMS.DELIVERY_DATE</h3>
        <input type="text" id="datepicker" autocomplete="off" ng-model="partialShipOrderCtrl.shippingDate"
               placeholder="dd/mm/yyyy"/>
        <p>

        <h3 translate>SHIP_ITEMS.IDENTIFIED_PARTS</h3>
        <div class="responsiveContainer py-0">
        <table class="table table-bordered">
            <thead>
            <tr>
                <th translate>SHIP_ITEMS.PART_NO</th>
                <th translate>SHIP_ITEMS.PART_DESC</th>
                <th translate>SHIP_ITEMS.QTY</th>
                <th translate>SHIP_ITEMS.QTY_TO_SHIP</th>
                <th translate>SHIP_ITEMS.SHIP</th>
            </tr>
            </thead>

            <tbody>
            <tr ng-repeat="item in partialShipOrderCtrl.partsList">
                <td data-label="{{'SHIP_ITEMS.PART_NO' | translate}}">{{item.partNumber}}</td>
                <td data-label="{{'SHIP_ITEMS.PART_DESC' | translate}}">{{item.partDescription}}</td>
                <td data-label="{{'SHIP_ITEMS.QTY' | translate}}">{{item.oldQuantity}}</td>
                <td data-label="{{'SHIP_ITEMS.QTY_TO_SHIP' | translate}}"><input ng-model="item.quantity" type="number" min="0" max="{{item.oldQuantity}}"></td>
                <td data-label="{{'SHIP_ITEMS.SHIP' | translate}}"><input ng-disabled="item.oldQuantity == 0" ng-model="item.isSelected" type="checkbox"></td>
            </tr>

            </tbody>
        </table>
        </div>
        <br>
        <h4 translate>ORDER_CONFIRM.ATTACH_INVOICE</h4>
        <div class="upload-box">
            <div>
                <i class="fa fa-upload"></i>
                <h4 class="file-uploader" translate>ORDER_CONFIRM.CHOOSE_FILE</h4>
                <input type="file" class="fileupload" ng-click="$event = $event" multiple="multiple"
                       onchange="angular.element(this).scope().partialShipOrderCtrl.invoiceAttached(event)"
                       accept=".pdf"/>
            </div>
        </div>
        <p>
            <emphasis ng-repeat="invoice in partialShipOrderCtrl.invoiceFileNames"><span ng-if="!$first">, </span>{{invoice}}</emphasis>
        </p>

        <div class="error-well" ng-if="partialShipOrderCtrl.errorShippingItems">
            <p translate>SHIP_ITEMS.ERROR</p>
        </div>
        <div class="error-well" ng-if="partialShipOrderCtrl.quantityMismatchError">
            <p translate>SHIP_ITEMS.ENTER_VALID_QUANTITY</p>
        </div>
        <div class="error-well" ng-if="partialShipOrderCtrl.nothingSelectedError">
            <p translate>SHIP_ITEMS.AT_LEAST_ONE</p>
        </div>
        <div class="error-well" ng-if="partialShipOrderCtrl.noDateSelectedError">
            <p translate>SHIP_ITEMS.PICK_DATE</p>
        </div>

        <div class="modal-actions">
            <button type="button" class="btn small secondary" ng-click="partialShipOrderCtrl.cancel()" translate>GENERAL.CANCEL</button>

            <button type="button" class="btn small primary"
                    ng-click="partialShipOrderCtrl.shipOrderItems()" ng-hide="partialShipOrderCtrl.isConfirming" translate>SHIP_ITEMS.SHIP_PARTIAL
            </button>
            <button class="btn small primary" href="" ng-show="partialShipOrderCtrl.isConfirming">
                <span class="spinner-border text-light" role="status" aria-hidden="true"></span>
                {{'ORDER_CONFIRM.CONFIRMING' | translate}}
            </button>


        </div>

    </form>
</div>