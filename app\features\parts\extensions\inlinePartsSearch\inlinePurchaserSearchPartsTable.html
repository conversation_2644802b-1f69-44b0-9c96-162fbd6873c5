<h2 ng-show="inlinePartsSearchCtrl.partsReturned()" translate>ORDER.PARTS</h2>

<div ng-show="inlinePartsSearchCtrl.noPartsFound()">
    <p class="font-weight-light" translate>INLINE_SEARCH.NO_PARTS</p>
</div>

<table class="table bg-white table-bordered equal-width table-smaller-font"
    ng-show="inlinePartsSearchCtrl.masterParts.length > 0">
    <thead>
        <tr>
            <th class="col-md-2 col-12"
                translate>
                INLINE_SEARCH.PART_NUM
            </th>
            <th class="col-md-4 col-12"
                translate>
                INLINE_SEARCH.DESCRIPTION
            </th>
            <th ng-if="inlinePartsSearchCtrl.isPreviewStockLevelEnabled && !inlinePartsSearchCtrl.isDealerPlusCustomer && !inlinePartsSearchCtrl.isCustomerOrder()"
                translate>
                INLINE_SEARCH.STOCK
            </th>
            <th translate>ORDER.QUANTITY</th>
            <th class="disableWordBreak" ng-if="!inlinePartsSearchCtrl.hidePrice" translate>ORDER.ITEM_PRICE
            </th>
            <th class="disableWordBreak" ng-if="!inlinePartsSearchCtrl.hidePrice" translate>ORDER.TOTAL_PRICE
            </th>
            <th></th>
        </tr>
    </thead>
    <tbody>
        <tr ng-init="inlinePartsSearchCtrl.accordionSupersession = []" ng-class="{'borderTopSupersession': inlinePartsSearchCtrl.accordionSupersession[$index]}"
            ng-repeat-start="part in inlinePartsSearchCtrl.masterParts | orderBy:inlinePartsSearchCtrl.part_sort:inlinePartsSearchCtrl.sortReverse"
            ng-show="inlinePartsSearchCtrl.masterParts.length > 0"
            ng-click="($event.target.tagName !== 'INPUT' && $event.target.tagName !== 'BUTTON' && $event.target.tagName !== 'I') && (part.isOpen = !part.isOpen)">
            <td data-label="{{'INLINE_SEARCH.PART_NUM' | translate}}">
                <div class="" ng-class="{'text-muted': inlinePartsSearchCtrl.accordionSupersession[$index]}">
                    {{ part.partNumber }} <span class="small-superseded font-weight-bold" ng-if="part.superseded" translate>PART_SEARCH.SUPERSEDED</span>
                </div>

                <div class="tooltip-container" ng-if="part.inSupersession && !part.superseded">
                    <button class="inline-tooltip-trigger" ng-click="inlinePartsSearchCtrl.toggleSupersessionHistory(part)">
                        <small>
                            {{ part.showSupersessionHistory ? 'PART_SEARCH.HIDE_SUPERSESSION_HISTORY' : 'PART_SEARCH.VIEW_SUPERSESSION_HISTORY' | translate }}
                        </small>
                    </button>
                    <div class="custom-tooltip" ng-class="{'show': part.showSupersessionHistory, 'last-item': $last}">
                        <div class="tooltip-arrow"></div>
                        <div ng-if="!inlinePartsSearchCtrl.isSupersessionLoading" class="flex supersessionHistoryHeader mb-3">
                            <h3 class="mb-0" translate>PART_SEARCH.SUPERSESSION_HISTORY</h3>
                        </div>
                        <div ng-if="inlinePartsSearchCtrl.isSupersessionLoading" class="p-4 text-center">
                            <span class="spinner-border text-primary" role="status" aria-hidden="true"></span>
                            <p translate>GENERAL.LOADING</p>
                        </div>
                        <div class="d-flex flex-column align-items-center" ng-if="!inlinePartsSearchCtrl.isSupersessionLoading" 
                             ng-repeat="part in inlinePartsSearchCtrl.supersessionHistory track by $index">
                            <div class="supersessionViewerPart">
                                <small class="mb-0 font-weight-bold tooltip-title">{{ part.partNumber }}</small>
                                <small class="mb-0">-</small>
                                <small class="mb-0" ng-if="part.partDescription">
                                    {{ part.partDescription }}
                                </small>
                            </div>
                            <i class="fa fa-long-arrow-up text-primary py-2" ng-class="{'hide': $last}"></i>
                        </div>
                    </div>
                </div>
            </td>
            <td data-label="{{'INLINE_SEARCH.DESCRIPTION' | translate}}">
                <span ng-class="{'text-muted': inlinePartsSearchCtrl.accordionSupersession[$index]}">
                    {{part.description ? part.description : part.partDescription}}
                </span>
                <div class="cadTooltip" ng-if="part.partNote && part.partNote.length > 0">
                    <i class="fas fa-sticky-note cadBlue cursor-pointer"></i>
                    <ul class="list-unstyled cadTooltiptext mb-0 p-2">
                        <li ng-repeat="note in part.partNote track by $index">
                            <p>{{ note }}</p>
                        </li>
                    </ul>
                </div>
            </td>
            <td class="disableWordBreak" data-label="{{'INLINE_SEARCH.STOCK' | translate}}"
                ng-if="inlinePartsSearchCtrl.isPreviewStockLevelEnabled && !inlinePartsSearchCtrl.isDealerPlusCustomer && !inlinePartsSearchCtrl.isCustomerOrder()">
                <!-- Show stock number if isStockWarehousesEnabled is true -->
                <span ng-if="inlinePartsSearchCtrl.isStockWarehousesEnabled">
                    <span ng-if="part.superseded">-</span>
                    <span class="text-success" ng-if="!part.superseded && part.stock >= 3">{{ part.stock }}</span>
                    <span class="text-warning" ng-if="!part.superseded && part.stock < 3 && part.stock > 0">{{ part.stock }}</span>
                    <span class="text-danger" ng-if="!part.superseded && (part.stock === null || part.stock < 1)">{{ part.stock || '0' }}</span>
                </span>
                <!-- Show icons if isStockWarehousesEnabled is false -->
                <span ng-if="!inlinePartsSearchCtrl.isStockWarehousesEnabled">
                    <span ng-if="part.superseded">-</span>
                    <span title="{{'INLINE_SEARCH.IN_STOCK' | translate}}" tooltip-trigger="outsideClick"
                        uib-tooltip="{{'INLINE_SEARCH.IN_STOCK' | translate}}" class="success-alert"
                        ng-if="!part.superseded && part.stock >= 3"><i class="fas fa-layer-group text-success pointer"></i></span>
                    <span title="{{'INLINE_SEARCH.LOW_STOCK' | translate}}" tooltip-trigger="outsideClick"
                        uib-tooltip="{{'INLINE_SEARCH.LOW_STOCK' | translate}}" class="warning-alert"
                        ng-if="!part.superseded && part.stock < 3 && part.stock > 0"><i
                            class="fas fa-layer-group text-warning pointer"></i></span>
                    <span title="{{'INLINE_SEARCH.STOCK_SUBJECT' | translate}}" tooltip-trigger="outsideClick"
                        uib-tooltip="{{'INLINE_SEARCH.STOCK_SUBJECT' | translate}}" class="warning-alert"
                        ng-if="!part.superseded && (part.stock === null || part.stock < 1)"><i
                            class="fas fa-layer-group text-danger pointer"></i></span>
                </span>
            </td>

            <td data-label="{{'ORDER.QUANTITY' | translate}}">
                <span ng-if="part.superseded">-</span>
                <input class="inline-quantity-input" type="number" min="0" ng-model="part.quantity"
                ng-init="part.quantity=part.quantity||1" ng-change="inlinePartsSearchCtrl.quantityUpdated()" ng-if="!part.superseded"/>
            </td>
            <td class="disableWordBreak" data-label="{{'ORDER.ITEM_PRICE' | translate}}"
                ng-if="!inlinePartsSearchCtrl.hidePrice && part.price !== null">
                <span ng-if="!part.superseded">{{part.price | currency:inlinePartsSearchCtrl.defaultCurrency.symbol:2}}</span>
                <span ng-if="part.superseded">-</span>
            </td>
            <td data-label="{{'ORDER.ITEM_PRICE' | translate}}"
                ng-if="!inlinePartsSearchCtrl.hidePrice && part.price === null">-</td>
            <td class="disableWordBreak" data-label="{{'ORDER.TOTAL_PRICE' | translate}}"
                ng-if="!inlinePartsSearchCtrl.hidePrice && part.price !== null">
                <span ng-if="!part.superseded">{{part.totalPrice | currency:inlinePartsSearchCtrl.defaultCurrency.symbol:2}}</span>
                <span ng-if="part.superseded">-</span>
            </td>
            <td data-label="{{'ORDER.TOTAL_PRICE' | translate}}"
                ng-if="!inlinePartsSearchCtrl.hidePrice && part.price === null">-</td>
            <td>
            <button ng-if="!part.superseded" class="btn primary fixed-width-btn"
                ng-click="inlinePartsSearchCtrl.addPart(part)">
                <div ng-hide="part.clicked" translate>GENERAL.ADD_TO_BASKET</div>
                <div class="check-icon-wrapper" ng-show="part.clicked">
                    <i class="fa fa-check"></i>
                </div>
            </button>

            <button ng-if="part.superseded" class="btn primary-outline supersession-nowrap" ng-click="inlinePartsSearchCtrl.toggleSupersessionAccordion(part.maxSupersessionPartNumber, $index)"
            translate>{{ part.superseded && inlinePartsSearchCtrl.accordionSupersession[$index] ? 'PART_SEARCH.HIDE_SUPERSESSION' : 'PART_SEARCH.VIEW_SUPERSESSION' }}</button>
            </td>
        </tr>

        <!-- Supersession Details Row -->
        <tr ng-if="part.superseded && inlinePartsSearchCtrl.accordionSupersession[$index]" ng-class="{'borderBottomSupersession': inlinePartsSearchCtrl.accordionSupersession[$index]}" ng-repeat-end>
            <td data-label="{{'ORDER.PART_DETAILS' | translate}}">
                <div ng-if="part.supersessionDetails && part.supersessionDetails.length > 0">
                    {{ part.supersessionDetails[0].partNumber }}
                </div>
                <div class="tooltip-container" ng-if="part.superseded && inlinePartsSearchCtrl.accordionSupersession[$index]">
                    <button class="inline-tooltip-trigger" ng-click="inlinePartsSearchCtrl.toggleSupersessionHistory(part)">
                      <small>
                        {{ part.showSupersessionHistory ? 'PART_SEARCH.HIDE_SUPERSESSION_HISTORY' : 'PART_SEARCH.VIEW_SUPERSESSION_HISTORY' | translate }}
                      </small>
                    </button>
                    <div class="custom-tooltip" ng-class="{'show': part.showSupersessionHistory, 'last-item': $last}">
                    <div class="tooltip-arrow"></div>
                    <div ng-if="!inlinePartsSearchCtrl.isSupersessionLoading"class="flex supersessionHistoryHeader mb-3">
                        <h3 class="mb-0" translate>PART_SEARCH.SUPERSESSION_HISTORY</h3>
                    </div>
                    <div ng-if="inlinePartsSearchCtrl.isSupersessionLoading" class="p-4 text-center">
                        <span class="spinner-border text-primary" role="status" aria-hidden="true"></span>
                        <p translate>GENERAL.LOADING</p>
                    </div>
                    <div class="d-flex flex-column align-items-center" ng-if="!inlinePartsSearchCtrl.isSupersessionLoading" ng-repeat="part in inlinePartsSearchCtrl.supersessionHistory track by $index">
                        <div class="supersessionViewerPart">
                            <small class="mb-0 font-weight-bold tooltip-title">{{ part.partNumber }}</small>
                            <small class="mb-0">-</small>
                            <small class="mb-0" ng-if="part.partDescription">
                                {{ part.partDescription }}
                              </small>
                        </div>
                        <i class="fa fa-long-arrow-up text-primary py-2" ng-class="{'hide': $last}"></i>
                    </div>
                </div>
            </div>
            </td>
            
            <td data-label="{{'ORDER.DESCRIPTION' | translate}}">
                <span
                    ng-if="part.supersessionDetails[0].description && part.supersessionDetails[0].description.trim() !== ''">
                    {{ part.supersessionDetails[0].description }}
                </span>
                <span
                    ng-if="!part.supersessionDetails[0].description || part.supersessionDetails[0].description.trim() === ''">
                    -
                </span>
                <div class="cadTooltip"
                    ng-if="part.supersessionDetails[0].note && part.supersessionDetails[0].note.length > 0">
                    <i class="fas fa-sticky-note cadBlue cursor-pointer"></i>
                    <ul class="list-unstyled cadTooltiptext mb-0 p-2">
                        <li>
                            <p>{{ part.supersessionDetails[0].note }}</p>
                        </li>
                    </ul>
                </div>
            </td>
            
            <td ng-if="inlinePartsSearchCtrl.isCustomerSearchManualsOnly">
                {{part.supersessionDetails[0].manualName}}
            </td>

            <td class="disableWordBreak" data-label="{{'ORDER.STOCK' | translate}}"
                    ng-if="inlinePartsSearchCtrl.isPreviewStockLevelEnabled && !inlinePartsSearchCtrl.isDealerPlusCustomer">
                    <span ng-if="inlinePartsSearchCtrl.isStockWarehousesEnabled">
                        <span class="text-success" ng-if="part.supersessionDetails[0].stock >= 3">{{
                            part.supersessionDetails[0].stock }}</span>
                        <span class="text-warning"
                            ng-if="part.supersessionDetails[0].stock < 3 && part.supersessionDetails[0].stock > 0">{{
                            part.supersessionDetails[0].stock }}</span>
                        <span class="text-danger"
                            ng-if="!part.supersessionDetails[0].stock || part.supersessionDetails[0].stock < 1">{{
                            part.supersessionDetails[0].stock || '0' }}</span>
                    </span>
                    <span ng-if="!inlinePartsSearchCtrl.isStockWarehousesEnabled">
                        <span title="{{'ORDER.IN_STOCK' | translate}}" tooltip-trigger="outsideClick"
                            uib-tooltip="{{'ORDER.IN_STOCK' | translate}}" class="success-alert"
                            ng-if="part.supersessionDetails[0].stock >= 3">
                            <i class="fas fa-layer-group text-success pointer"></i>
                        </span>
                        <span title="{{'ORDER.LOW_STOCK' | translate}}" tooltip-trigger="outsideClick"
                            uib-tooltip="{{'ORDER.LOW_STOCK' | translate}}" class="warning-alert"
                            ng-if="part.supersessionDetails[0].stock < 3 && part.supersessionDetails[0].stock > 0">
                            <i class="fas fa-layer-group text-warning pointer"></i>
                        </span>
                        <span title="{{'ORDER.STOCK_SUBJECT' | translate}}" tooltip-trigger="outsideClick"
                            uib-tooltip="{{'ORDER.STOCK_SUBJECT' | translate}}" class="warning-alert"
                            ng-if="!part.supersessionDetails[0].stock || part.supersessionDetails[0].stock < 1">
                            <i class="fas fa-layer-group text-danger pointer"></i>
                        </span>
                    </span>
                </td>

            <td data-label="{{'ORDER.QUANTITY' | translate}}">
                    <input class="inline-quantity-input" type="number" min="0" ng-model="part.quantity"
                        ng-change="inlinePartsSearchCtrl.quantityUpdated()" />
            </td>
            
            <td class="disableWordBreak" data-label="{{'ORDER.ITEM_PRICE' | translate}}"
                    ng-if="!inlinePartsSearchCtrl.hidePrice">
                    <span ng-if="part.supersessionDetails[0].price !== null">
                        {{ part.supersessionDetails[0].price | currency:inlinePartsSearchCtrl.defaultCurrency.symbol:2
                        }}
                    </span>
                    <span ng-if="part.supersessionDetails[0].price === null">-</span>
                </td>
                
                <td class="disableWordBreak" data-label="{{'ORDER.TOTAL_PRICE' | translate}}"
                    ng-if="!inlinePartsSearchCtrl.hidePrice">
                    <span ng-if="part.supersessionDetails[0].price !== null">
                        {{ part.supersessionDetails[0].price * part.quantity |
                        currency:inlinePartsSearchCtrl.defaultCurrency.symbol:2 }}
                    </span>
                    <span ng-if="part.supersessionDetails[0].price === null">-</span>
                </td>

            <td ng-if="inlinePartsSearchCtrl.hasOrdersAccess">
                <button class="btn primary fixed-width-btn"
                ng-click="inlinePartsSearchCtrl.addPart(part)">
                <div ng-hide="part.clicked" translate>GENERAL.ADD_TO_BASKET</div>
                <div class="check-icon-wrapper" ng-show="part.clicked">
                    <i class="fa fa-check"></i>
                </div>
            </button>
            </td>
        </tr>
    </tbody>
</table>
