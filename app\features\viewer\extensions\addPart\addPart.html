<form class="add-part" ng-show="addPartCtrl.isVisible()" ng-submit="addPartCtrl.createPartsClicked()">

    <div class="add-part-header">
      {{addPartCtrl.title | uppercase}}
      <i class="pull-right fa fa-times" ng-click="addPartCtrl.cancelClicked()"></i>
    </div>


    <div class="add-part-scrollable side-forms">
        <table class="addpart-table product-table">
            <thead>
            <tr>
                <!--<th ng-show="addPartCtrl.isTwoD()">#</th>-->
                <th translate>ADD_PART.PART_NUM_DESC</th>
                <th></th>
            </tr>
            </thead>

            <tbody>

            <tr ng-show="addPartCtrl.isSpinnerActive" align="center">
                <td colspan="4"><img ng-src="images/cadpreloader.gif" class="ajax-loader" height="60" width="60"/></td>
            </tr>

            <tr ng-repeat="newPart in addPartCtrl.partsList">

                <td class="add-part-cell">

				<div class="row">
					  <div class="col-3 pr-0">
						    <h3 translate>ADD_PART.ID</h3>
						    <input maxlength="5" type="text" placeholder="{{'ADD_PART.ID'| translate}}" ng-model="newPart.itemNumber" class="mb-8"
							     ng-change="addPartCtrl.partChanged($index)">
					  </div>
					  <div class="col-9">
						    <h3 translate>ADD_PART.PART_NUMBER</h3>
						    <input type="text" placeholder="{{'ADD_PART.PART_NUMBER'| translate}}" ng-model="newPart.partNumber" class="mb-8"
							     ng-change="addPartCtrl.partChanged($index)" ng-required="newPart.itemNumber.length || newPart.partDescription.length > 0">
					  </div>

					  <!-- Force next columns to break to new line -->
					  <div class="w-100"></div>

					  <div class="col-12 mt-2">
						    <h3 translate>ADD_PART.DESC</h3>
						    <input type="text" placeholder="{{'ADD_PART.DESC'| translate}}" ng-model="newPart.partDescription"
							     ng-change="addPartCtrl.partChanged($index)">
					  </div>
				</div>

                </td>
                <td>
				<a href="" ng-click="addPartCtrl.removeNewPart($index)" title="{{'ADD_PART.DELETE_PART' | translate}}" class="fa-stack fa-lg text-danger hover-filled-circle">
					  <i class="fa fa-circle-thin fa-stack-2x"></i>
					  <i class="fa fa-trash fa-stack-1x"></i>
</a>
                </td>
            </tr>
            
            <tr>

                <td colspan="4">
                    <div class="add-another-part">
                        <button class="btn primary-outline pull-right" type="button"
                                ng-click="addPartCtrl.addNewPart()">
                            <i class="fa fa-plus-circle"></i> {{"ADD_PART.ADD_ANOTHER" | translate}}
                        </button>
                    </div>
                </td>
            </tr>

            </tbody>
        </table>

    </div>
    


    <div class="add-part-button-group text-right">
        <button class="btn secondary" type="button" ng-click="addPartCtrl.cancelClicked()">
            {{addPartCtrl.declineButtonText}}
        </button>
        <button class="btn primary" type="submit">
            {{addPartCtrl.confirmButtonText}}
        </button>

    </div>
</form>

