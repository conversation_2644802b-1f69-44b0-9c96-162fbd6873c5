(function () {
    'use strict';

    angular
        .module('app.services')
        .factory('customerPublicationsService', customerPublicationsService);

    customerPublicationsService.$inject = ['$http', 'apiConstants', 'userService'];

    function customerPublicationsService($http, apiConstants, userService) {

        return {
            fetchPublicationsByPurchaser: fetchPublicationsByPurchaser
        };

        function fetchPublicationsByPurchaser(purchaserId) {
            return $http.get(apiConstants.url + '/purchasers/' + purchaserId + '/publications', null);
        }

    }
})();
