<section class="body-content">
    <div class="order-details-holder">
        <h1 translate>CUSTOMERS.TITLE</h1>
        <p class="page-desc" translate>CUSTOMERS.SUBTITLE</p>
    </div>
</section>

<section class="responsiveContainer m-5">
    <div class="">

        <div class="success-alert" ng-if="dpCustomerCtrl.successMessage != ''">
            {{dpCustomerCtrl.successMessage}}
        </div>

        <div id="{{dpCustomerCtrl.isFixedHeader ? 'infiniteScrollFixedHeader' : 'infiniteScrollStaticHeader'}}"
            class="flex p-4 p-md-0">
            <search-filter class="col-12 col-md-3" state-name="'dpCustomers'" value="dpCustomerCtrl.searchValue"
                placeholder-key="CUSTOMERS.SEARCH_BY_NAME"></search-filter>
            
            <button class="btn primary ml-auto mr-4 col-12 col-md-auto mt-3 mt-md-0 create-machine"
                ng-click="dpCustomerCtrl.createCompany()" translate>CUSTOMERS.ADD_NEW
            </button>
        </div>

        <table class="table table-bordered">

            <thead>
            <tr>
                <th ng-class="dpCustomerCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'"
                    ng-if="dpCustomerCtrl.displayManufacturerSubEntityId"
                    ng-click="dpCustomerCtrl.customer_sort='manufacturerSubEntityId'; dpCustomerCtrl.sortReverse = !dpCustomerCtrl.sortReverse"
                    translate>CUSTOMERS.ID
                </th>
                <th ng-class="dpCustomerCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'"
                    ng-click="dpCustomerCtrl.customer_sort='name'; dpCustomerCtrl.sortReverse = !dpCustomerCtrl.sortReverse"
                    translate>CUSTOMERS.COMPANY
                </th>
                <th ng-class="dpCustomerCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'"
                    ng-click="dpCustomerCtrl.customer_sort='manufacturerSubEntityType'; dpCustomerCtrl.sortReverse = !dpCustomerCtrl.sortReverse"
                    translate>CUSTOMERS.TYPE
                </th>

                <th ng-class="dpCustomerCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'"
                    ng-click="dpCustomerCtrl.customer_sort='createdDate'; dpCustomerCtrl.sortReverse = !dpCustomerCtrl.sortReverse"
                    translate>CUSTOMERS.CUSTOMER_SINCE
                </th>
                <th translate>CUSTOMERS.ACTIONS</th>
            </tr>
            </thead>

            <tbody infinite-scroll="dpCustomerCtrl.loadMoreInfiniteScroll()" infinite-scroll-distance="3" infinite-scroll-disabled="dpCustomerCtrl.loadingInfiniteScrollData">
            <tr class="p-4"
                ng-repeat="customer in dpCustomerCtrl.customerList | orderBy:dpCustomerCtrl.customer_sort:dpCustomerCtrl.sortReverse | filter : dpCustomerCtrl.searchValue" ng-class="{'last-item': $last}"
                ng-show="dpCustomerCtrl.customerList.length > 0">
                <td ng-if="dpCustomerCtrl.displayManufacturerSubEntityId" data-label="{{'CUSTOMERS.ID' | translate}}">
                    {{customer.manufacturerSubEntityId}}
                </td>
                <td data-label="{{'CUSTOMERS.COMPANY' | translate}}">{{customer.name}}</td>
                <td data-label="{{'CUSTOMERS.TYPE' | translate}}">
                    {{customer.manufacturerSubEntityType === "DEALER" ? "Dealer" : "Customer"}}
                </td>
                <td data-label="{{'CUSTOMERS.CUSTOMER_SINCE' | translate}}">
                    {{customer.createdDate | date: "d MMMM y"}}
                </td>

                <td class="mobile-right-aligned-btn">
                    <div class="btn-group">
                        <a href="" class="btn xsmall secondary main-action"
                           ng-click="dpCustomerCtrl.viewCustomerUsers(customer)" translate>CUSTOMERS.VIEW_DETAILS</a>
                        <div href="" class="btn xsmall secondary dropdown-toggle" data-toggle="dropdown"
                             aria-haspopup="true" aria-expanded="false">
                            <div class="sub-popup">
                                <ul class="more-options">
                                    <li title="View Details">
                                        <a href="" class="dark-secondary"
                                           ng-click="dpCustomerCtrl.viewCustomerUsers(customer)"><i
                                                class="fa fa-fw fa-list-ul"></i> {{"CUSTOMERS.VIEW_DETAILS" | translate}}
                                        </a>
                                    </li>

                                    <li title="Edit Company">
                                        <a href="" class="dark-secondary"
                                           ng-click="dpCustomerCtrl.editCompany(customer)"><i
                                                class="fa fa-fw fa-pencil"></i> {{"CUSTOMERS.EDIT" | translate}}</a>
                                    </li>

                                    <li title="Assign Publications">
                                        <a href="" class="dark-secondary"
                                           ng-click="dpCustomerCtrl.assignPublications(customer)"> <i
                                                class="fa fa-fw fa-align-left"></i>
                                            {{"ASSIGN_PUBLICATIONS.ASSIGN" | translate}}</a>
                                    </li>
                                    <li title="Delete Company">
                                        <a href="" class="delete" ng-click="dpCustomerCtrl.deleteCompany(customer)"><i
                                                class="fa fa-fw fa-trash"></i> {{"CUSTOMERS.DELETE" | translate}}</a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </td>
            </tr>

            <tr ng-show="!dpCustomerCtrl.customerList.length > 0 && dpCustomerCtrl.areCustomersLoaded">
                <td colspan="5" translate>CUSTOMERS.NO_COMPANIES</td>
            </tr>

            <tr ng-hide="dpCustomerCtrl.areCustomersLoaded" align="center">
                <td class="preloader" colspan="4"><img ng-src="images/cadpreloader.gif" class="ajax-loader" height="60"
                                                       width="60"/></td>
            </tr>
            </tbody>
        </table>

    <span ng-click="dpCustomerCtrl.scrollToTop()" id="backToTopBtn" title="Go to top" class="fas fa-arrow-alt-circle-up"
        ng-show="dpCustomerCtrl.showBackToTopButton"></span>

    </div><!--panel closed-->
    </div>
</section>