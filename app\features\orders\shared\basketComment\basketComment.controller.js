(function () {
    'use strict';

    angular
        .module('app.orders')
        .controller('BasketCommentController', BasketCommentController);

    BasketCommentController.$inject = ['$uibModalInstance', 'commentObject'];

    function BasketCommentController($uibModalInstance, commentObject) {

        var vm = this;

        vm.confirm = confirm;
        vm.cancel = $uibModalInstance.dismiss;

        if(commentObject){
            vm.comment = commentObject;
        }

        function confirm() {
            $uibModalInstance.close(vm.comment);
        }


    }
})();
