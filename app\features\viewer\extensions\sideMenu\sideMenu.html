<div class="side-menu h-auto" ng-hide="sideMenuCtrl.isMinimized">
    <button class="btn-side-menu" ng-click="sideMenuCtrl.minimizeSideMenu()">
        {{"SIDE_MENU.CLOSE_MENU" | translate}} &nbsp
        <i class="pull-right fa fa-close"></i>
    </button>

    <uib-accordion class="side-menu-accordion">
        <uib-accordion-group is-open="sideMenuCtrl.status.isModelBrowserOpen">
            <uib-accordion-heading>
                <div>
                    <i class="fa fa-fw fa-sitemap"></i>&nbsp; {{"SIDE_MENU.MODEL_BROWSER" | translate}}
                    <span class="pull-right">
                        <i
                            class="fas"
                            ng-class="{'fa-lock': lockState, 'fa-lock-open': !lockState}"
                            id="explode_lock"
                            ng-click="sideMenuCtrl.handleToggleLockingClick($event)"
                        ></i>
                        <span ng-show="sideMenuCtrl.status.isModelBrowserOpen"><i class="fa fa-angle-down"></i>&nbsp;</span>
                        <span ng-hide="sideMenuCtrl.status.isModelBrowserOpen"><i class="fa fa-angle-right"></i>&nbsp;</span>
                    </span>
                </div>
            </uib-accordion-heading>
            <model-browser></model-browser>
        </uib-accordion-group>

        <uib-accordion-group is-open="sideMenuCtrl.status.isKitBuilderOpen">
            <uib-accordion-heading>
                <div>
                    <i class="fas fa-box-open"></i>&nbsp; {{"SIDE_MENU.KIT" | translate}}
                    <span ng-show="sideMenuCtrl.status.isKitBuilderOpen" class="pull-right"><i class="fa fa-angle-down"></i>&nbsp;</span>
                    <span ng-hide="sideMenuCtrl.status.isKitBuilderOpen" class="pull-right"><i class="fa fa-angle-right"></i>&nbsp;</span>
                </div>
            </uib-accordion-heading>
            <kit-builder></kit-builder>
            <create-kit></create-kit>
        </uib-accordion-group>

        <uib-accordion-group is-open="sideMenuCtrl.status.isOptionsSetOpen">
            <uib-accordion-heading>
                <div>
                    <i class="fas fa-clipboard-list"></i>&nbsp; {{"SIDE_MENU.OPTIONS" | translate}}
                    <span ng-show="sideMenuCtrl.status.isOptionsSetOpen" class="pull-right"><i class="fa fa-angle-down"></i>&nbsp;</span>
                    <span ng-hide="sideMenuCtrl.status.isOptionsSetOpen" class="pull-right"><i class="fa fa-angle-right"></i>&nbsp;</span>
                </div>
            </uib-accordion-heading>
            <options-set-builder></options-set-builder>
            <create-options-set></create-options-set>
        </uib-accordion-group>

        <uib-accordion-group is-open="sideMenuCtrl.status.isLinkedPartOpen">
            <uib-accordion-heading
                ><div>
                    <i class="fas fa-paperclip"></i>&nbsp; {{"SIDE_MENU.LINKS" | translate}}
                    <span ng-show="sideMenuCtrl.status.isLinkedPartOpen" class="pull-right"><i class="fa fa-angle-down"></i>&nbsp;</span>
                    <span ng-hide="sideMenuCtrl.status.isLinkedPartOpen" class="pull-right"><i class="fa fa-angle-right"></i>&nbsp;</span>
                </div>
            </uib-accordion-heading>
            <linked-part-summary></linked-part-summary>
            <create-linked-part></create-linked-part>
        </uib-accordion-group>

        <uib-accordion-group>
            <uib-accordion-heading
                ><div>
                    <i class="fas fa-cogs"></i>&nbsp; {{"SIDE_MENU.ADD_PARTS" | translate}}
                    <span ng-show="sideMenuCtrl.status.isNonModeledPartsOpen" class="pull-right"
                        ><i class="fa fa-angle-down"></i>&nbsp;</span
                    >
                    <span ng-hide="sideMenuCtrl.status.isNonModeledPartsOpen" class="pull-right"
                        ><i class="fa fa-angle-right"></i>&nbsp;</span
                    >
                </div>
            </uib-accordion-heading>
            <non-modeled-parts-summary></non-modeled-parts-summary>
            <create-non-modeled-part></create-non-modeled-part>
        </uib-accordion-group>

        <uib-accordion-group>
            <uib-accordion-heading
                ><div>
                    <i class="fas fa-cubes"></i>&nbsp; {{"SIDE_MENU.ASSEMBLIES" | translate}}
                    <span ng-show="sideMenuCtrl.status.isPurchasableAssembliesOpen" class="pull-right"
                        ><i class="fa fa-angle-down"></i>&nbsp;</span
                    >
                    <span ng-hide="sideMenuCtrl.status.isPurchasableAssembliesOpen" class="pull-right"
                        ><i class="fa fa-angle-right"></i>&nbsp;</span
                    >
                </div>
            </uib-accordion-heading>
            <purchasable-assembly-summary></purchasable-assembly-summary>
            <create-purchasable-assembly></create-purchasable-assembly>
        </uib-accordion-group>
        <uib-accordion-group is-open="sideMenuCtrl.status.isViewerSettingsOpen">
            <uib-accordion-heading
                ><div>
                    <i class="fas fa-sliders"></i>&nbsp; {{"SIDE_MENU.SETTINGS" | translate}}
                    <span ng-show="sideMenuCtrl.status.isViewerSettingsOpen" class="pull-right"
                        ><i class="fa fa-angle-down"></i>&nbsp;</span
                    >
                    <span ng-hide="sideMenuCtrl.status.isViewerSettingsOpen" class="pull-right"
                        ><i class="fa fa-angle-right"></i>&nbsp;</span
                    >
                </div>
            </uib-accordion-heading>
            <viewer-settings></viewer-settings>
        </uib-accordion-group>
    </uib-accordion>
</div>

<div class="expand-model-browser-button" ng-show="sideMenuCtrl.isMinimized">
    <button class="btn-model" ng-click="sideMenuCtrl.maximizeSideMenu()">
        <i class="fa fa-bars"></i> {{"SIDE_MENU.OPEN_MENU" | translate}}
    </button>
</div>
