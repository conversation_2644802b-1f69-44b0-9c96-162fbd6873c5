<div class="modal-header" ng-class="{'processing': supersessionHistoryCtrl.processing}">
    <button type="button" class="close" data-dismiss="modal" ng-click="supersessionHistoryCtrl.cancel()" aria-label="Close"><i class="fa fa-close" aria-hidden="true"></i></button>
    <h2 class="modal-title" translate>SUPERSEDE.TITLE</h2>
</div>

<div class="modalLoadingOverlay" ng-show="supersessionHistoryCtrl.processing">
    <div class="d-flex cadGap align-items-center">
        <div class="spinner-border text-light" role="status"></div>
        <p class="mb-0 text white font-weight-bold" translate>GENERAL.PROCESSING</p>
    </div>
</div>

<div class="modal-body" ng-class="{'processing': supersessionHistoryCtrl.processing}">

    <div class="responsiveContainer">

        <div ng-if="supersessionHistoryCtrl.hasError" class="d-flex align-items-center cadGap alert-danger-modal p-3 mb-4 mt-2" role="alert">
            <i class="fas fa-exclamation-circle mr-2"></i>
            <p class="mb-0" translate>MASTER_PART.SUPERSEDING_ERROR</p>
        </div>

        <form ng-submit="supersessionHistoryCtrl.search()">
            <div class="search-area">
                <div class="input-group">
                    <input class="form-control mr-0" type="search" ng-model="supersessionHistoryCtrl.searchValue"
                        placeholder="{{supersessionHistoryCtrl.isSearchOnlyPartNumbers ? 'INLINE_SEARCH.SEARCH_BY_PART' : 'INLINE_SEARCH.SEARCH_BY_PART_AND_DESC' | translate}}">
    
                    <div class="input-group-append">
                        <!-- Search Button -->
                        <button class="input-group-text-btn btn-anim"
                            ng-class="supersessionHistoryCtrl.isDealerPlusPage() ? 'dpGreenModal' : ''" ng-hide="supersessionHistoryCtrl.searching"
                            ng-disabled="!supersessionHistoryCtrl.searchValue || supersessionHistoryCtrl.searchValue.trim() === ''">
                            <i class="fa fa-search pr-0 pr-md-3"></i>
                            <span class="search_mobile_disable">{{'INLINE_SEARCH.SEARCH' | translate}}</span>
                        </button>
    
                        <!-- Loading Button -->
                        <button class="input-group-text-btn btn-anim"
                            ng-class="supersessionHistoryCtrl.isDealerPlusPage() ? 'dpGreenModal' : ''"
                            ng-show="supersessionHistoryCtrl.searching">
                            <i class="fa fa-search pr-0 pr-md-3"></i>
                            <span class="search_mobile_disable">{{'INLINE_SEARCH.SEARCHING' | translate}}</span>
                            <span class="spinner-border text-light" role="status" aria-hidden="true"></span>
                        </button>
                    </div>
                </div>
            </div>
    
            <!-- Search Options -->
            <div class="d-flex justify-content-center align-items-center mb-3 flex-wrap">
                <div class="d-flex cadGap" ng-hide="supersessionHistoryCtrl.isSearchOnlyPartNumbers">
                    <span translate>INLINE_SEARCH.SEARCH_BY</span>
                    <label class="radio-inline pointer">
                        <input type="radio" ng-model="supersessionHistoryCtrl.searchBy" class="pointer m-0" value="partNumber"
                            ng-class="supersessionHistoryCtrl.isDealerPlusPage() ? 'dpGreenModal radio' : ''">
                        <span translate>INLINE_SEARCH.PART_NUM</span>
                    </label>
                    <label class="radio-inline pointer">
                        <input type="radio" ng-model="supersessionHistoryCtrl.searchBy" class="pointer m-0" value="partDescription"
                            ng-class="supersessionHistoryCtrl.isDealerPlusPage() ? 'dpGreenModal radio' : ''">
                        <span translate>INLINE_SEARCH.PART_DESC</span>
                    </label>
                </div>
                <label class="checkbox-inline flex cadGap my-3 my-md-0" ng-if="supersessionHistoryCtrl.showSupersededToggle">
                    <input type="checkbox" ng-model="supersessionHistoryCtrl.showSuperseded" class="pointer m-0">
                    <span class="pointer" translate>MASTER_PART.SHOW_SUPERSEDED</span>
                </label>
            </div>
        </form>
    </div>
    
    <!-- Results Table -->
    <div ng-show="supersessionHistoryCtrl.masterParts.length > 0">
        <ul class="part-list">
            <li ng-init="$first && supersessionHistoryCtrl.masterParts.length === 1 && !part.disabled && supersessionHistoryCtrl.selectPart(part)" 
    ng-repeat="part in supersessionHistoryCtrl.masterParts | orderBy:supersessionHistoryCtrl.part_sort:supersessionHistoryCtrl.sortReverse"
    ng-click="supersessionHistoryCtrl.selectPart(part)" 
    ng-class="{'d-none': part.disabled,
               'disabled-button': part.inSupersession}" 
    ng-disabled="part.disabled || part.inSupersession"
    ng-hide="!supersessionHistoryCtrl.showSuperseded && part.insupersession">
    
                <input type="radio" ng-model="supersessionHistoryCtrl.selectedPart" ng-value="part" id="part-{{part.partNumber}}"
                    ng-disabled="part.disabled || part.inSupersession">
                
                <label for="part-{{part.partNumber}}" class="part-search-list">
                    <div class="d-flex align-items-center cadGap text-break">
                        <div class="d-flex flex-column superseded-text-container">
                            <div class="font-weight-bold part-number">{{part.partNumber}}</div>
                            <div ng-if="part.disabled || part.inSupersession" class="already-superseded-text" translate>
                                MASTER_PART.ALREADY_SUPERSEDED</div>
                        </div>
                        <div class="supersessionPart-description">{{part.description || part.partDescription}}</div>
                    </div>
                </label>
            </li>
        </ul>
    </div> 
    
    <!-- No Results / Error Message -->
    <div ng-show="!supersessionHistoryCtrl.masterParts.length && supersessionHistoryCtrl.resultsReturned">
        <h2 class="center-align" translate>INLINE_SEARCH.NO_PARTS</h2>
    </div>
    <div ng-show="supersessionHistoryCtrl.hasSearchError">
        <h3 class="error-alert center-align" translate>PART_SEARCH.SEARCH_ERROR</h3>
    </div>

</div>

<div class="modal-actions border-top p-4">
    <a class="btn small secondary" href="" ng-click="supersessionHistoryCtrl.cancel()" translate>GENERAL.CANCEL</a>
    <a class="btn small primary" href="" ng-click="supersessionHistoryCtrl.addPart()" ng-class="{'disabledElement': supersessionHistoryCtrl.selectedPart === null}" translate>SUPERSEDE.SAVE</a>
</div>
