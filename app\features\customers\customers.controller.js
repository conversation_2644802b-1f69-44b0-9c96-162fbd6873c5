(function () {
  "use strict";

  angular
    .module("app.customer")
    .controller("CustomersController", CustomersController);

  CustomersController.$inject = [
    "$uibModal",
    "$state",
    "headerBannerService",
    "customerService",
    "userService",
    "$translate",
    "shippingEngineService",
    "$timeout",
    "$scope",
    "$window"
  ];

  function CustomersController(
    $uibModal,
    $state,
    headerBannerService,
    customerService,
    userService,
    $translate,
    shippingEngineService,
    $timeout,
    $scope,
    $window
  ) {
    var vm = this;

    vm.sortReverse = false;
    vm.areCustomersLoaded = false;
    vm.successMessage = "";
    vm.filter_range = "";
    vm.ranges = [];
    vm.items = [];
    vm.sortReverse = true;
    vm.isFixedHeader = false;

     vm.loadingInfiniteScrollData = false;
        vm.areCustomersLoaded = false;
        vm.showBackToTopButton = false;

    vm.customer_sort = "manufacturerSubEntityId";
    vm.displayManufacturerSubEntityId =
      userService.getDisplayManufacturerSubEntityId();
    vm.isSupreme = userService.isSupreme();

    var COMPANY, CREATE_DATE, TYPE;
    $translate([
      "CUSTOMERS.COMPANY",
      "CUSTOMERS.CREATE_DATE",
      "CUSTOMERS.TYPE",
    ]).then(function (resp) {
      COMPANY = resp["CUSTOMERS.COMPANY"];
      CREATE_DATE = resp["CUSTOMERS.CREATE_DATE"];
      TYPE = resp["CUSTOMERS.TYPE"];

      vm.sortBy = [
        { name: "name", value: COMPANY },
        { name: "createdDate", value: CREATE_DATE },
        { name: "manufacturerSubEntityType", value: TYPE },
      ];
    });

    vm.editCompany = editCompany;
    vm.assignRange = assignRange;
    vm.assignPublications = assignPublications;
    vm.assignRanges = assignRanges;
    vm.createCompany = createCompany;
    vm.deleteCompany = deleteCompany;
    vm.viewCustomerUsers = viewCustomerUsers;
    vm.scrollToTop = scrollToTop;
        vm.loadMoreInfiniteScroll = loadMoreInfiniteScroll;

    initialize();

    function initialize() {
      fetchAllCustomers();

      if (vm.isSupreme) {
        shippingEngineService
          .getAllWarehouse(userService.getManufacturerId())
          .then(getAllWarehouseSuccess, getAllWarehouseFailed);
      }
    }

    function fetchAllCustomers() {
      vm.loadingInfiniteScrollData = true;
      customerService
        .getCustomers()
        .then(getCustomersSuccess, getCustomersFailed);
    }

    function getCustomersSuccess(response) {
          vm.allCustomers = response.data; 
            vm.totalItems = vm.allCustomers.length;
             vm.customerList = vm.allCustomers.slice(0, 100);  
            vm.areCustomersLoaded = true;
            vm.loadingInfiniteScrollData = false;
            handleInfiniteScroll();
    }

    function getCustomersFailed(error) {
      vm.areCustomersLoaded = false;
      headerBannerService.setNotification("ERROR", error.data.error, 10000);
    }

    function editCompany(customer) {
      var companyDetails = {
        name: customer.name,
        defaultDiscount: customer.defaultDiscount,
        subEntityId: customer.manufacturerSubEntityId,
        type: customer.manufacturerSubEntityType,
        visCustomerCode: customer.visCustomerCode,
        warehouseId: customer.warehouseId,
      };
      if (userService.getPriceListsEnabled()) {
        companyDetails.priceListIdentifierId = customer.priceListIdentifierId || null;
      }
      $uibModal
        .open({
          templateUrl:
            "features/customers/editCompany/editCustomerCompany.html",
          controller: "EditCustomerCompanyController",
          controllerAs: "editCustomerCompanyCtrl",
          size: "md",
          backdrop: "static",
          resolve: {
            companyDetails: function () {
              return companyDetails;
            },
            warehouses: function () {
              return vm.warehouses;
            },
          },
        })
        .result.then(function () {
          $state.reload();
        });
    }

    function createCompany() {
      $uibModal
        .open({
          templateUrl:
            "features/customers/createCompany/createCustomerCompany.html",
          controller: "CreateCustomerCompanyController",
          controllerAs: "createCustomerCompanyCtrl",
          size: "md",
          backdrop: "static",
          resolve: {
            warehouses: function () {
              return vm.warehouses;
            },
          },
        })
        .result.then(function () {
          $state.reload();
        });
    }

    function deleteCompany(customer) {
      $uibModal
        .open({
          templateUrl:
            "features/customers/deleteCompany/deleteCustomerCompany.html",
          controller: "DeleteCustomerCompanyController",
          controllerAs: "deleteCustomerCompanyCtrl",
          size: "md",
          backdrop: "static",
          resolve: {
            companyDetails: function () {
              return {
                name: customer.name,
                manufacturerSubEntityId: customer.manufacturerSubEntityId,
              };
            },
          },
        })
        .result.then(
          function () {
            $state.reload();
          },
          function () {
            console.log("Modal Cancelled");
          }
        );
    }

    function assignPublications(customer) {
      $state.go("assignPublications", {
        subEntityId: customer.manufacturerSubEntityId,
        name: customer.name,
      });
    }

    function assignRanges(customer) {
      $state.go("assignRanges", {
        subEntityId: customer.manufacturerSubEntityId,
        name: customer.name,
      });
    }

    function viewCustomerUsers(customer) {
      console.log("viewCustomerUsers clicked");
      $state.go("customerUsers", {
        subEntityId: customer.manufacturerSubEntityId,
        type: customer.manufacturerSubEntityType,
        name: customer.name,
      });
    }

    function assignRange(customer) {
      $uibModal
        .open({
          templateUrl: "features/customers/assignRange/assignRange.html",
          controller: "AssignRangeController",
          controllerAs: "assignRangeCtrl",
          size: "md",
          backdrop: "static",
          resolve: {
            companyDetails: function () {
              return {
                name: customer.name,
                subEntityId: customer.manufacturerSubEntityId,
              };
            },
          },
        })
        .result.then(function () {
          $state.reload();
        });
    }

    function getAllWarehouseSuccess(res) {
      vm.warehouses = res;
    }

    function getAllWarehouseFailed(error) {
      vm.warehouses = [];
      headerBannerService.setNotification("ERROR", error.data.error, 10000);
    }

           var lastScrollTop = 0;
window.addEventListener('scroll', handleInfiniteScroll);

function handleInfiniteScroll() {
    var threshold = 250;
    var scrollTop = window.scrollY;

    if (scrollTop > lastScrollTop) {
        vm.isFixedHeader = scrollTop > threshold;
    } else if (scrollTop < threshold){
        vm.isFixedHeader = false;
    }
    lastScrollTop = scrollTop;  

    
    if (window.innerHeight + scrollTop >= document.body.offsetHeight - 2 && !vm.loadingInfiniteScrollData && !vm.initialLoadDone) {
        loadMoreInfiniteScroll();
    }
}

        function loadMoreInfiniteScroll() {
            vm.loadingInfiniteScrollData = true;

            var nextItems = vm.allCustomers.slice(vm.customerList.length, vm.customerList.length + 100);
            if (nextItems.length === 0) {
                vm.initialLoadDone = true;
                vm.loadingInfiniteScrollData = false;
                return;
            }

            vm.customerList = vm.customerList.concat(nextItems);

            vm.loadingInfiniteScrollData = false;
            if (vm.customerList.length >= vm.totalItems) {
                vm.initialLoadDone = true;
            }
        }

  function scrollToTop() {
      $window.scrollTo({ top: 0, behavior: "smooth" });
      $("html, body").animate({ scrollTop: 0 }, "slow", function () {
        $("#scrollToTop").removeClass("scrolled-past");
      });
    }

    angular.element($window).on("scroll", function () {
      vm.showBackToTopButton = this.pageYOffset > 100;
      $scope.$apply();
    });

    vm.actions = [
      {
        title: "View Details",
        onClick: function (entity) {
          vm.viewCustomerUsers(entity);
        },
        icon: "fa-list-ul",
        label: function () {
          return $translate.instant("CUSTOMERS.VIEW_DETAILS");
        },
      },
      {
        title: "Edit Company",
        onClick: function (entity) {
          vm.editCompany(entity);
        },
        icon: "fa-pencil",
        label: function () {
          return $translate.instant("CUSTOMERS.EDIT");
        },
      },
      {
        title: "Assign Publications",
        onClick: function (entity) {
          vm.assignPublications(entity);
        },
        icon: "fa-align-left",
        label: function () {
          return $translate.instant("ASSIGN_PUBLICATIONS.ASSIGN");
        },
      },
      {
        title: "Assign Ranges",
        onClick: function (entity) {
          vm.assignRanges(entity);
        },
        icon: "fa-align-right",
        label: function () {
          return $translate.instant("ASSIGN_RANGES.LINK");
        },
      },
      {
        title: "Delete Company",
        onClick: function (entity) {
          vm.deleteCompany(entity);
        },
        icon: "fa-trash",
        label: function () {
          return $translate.instant("CUSTOMERS.DELETE");
        },
      },
    ];
  }
})();
