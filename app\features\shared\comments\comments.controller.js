(function () {
    'use strict';

    angular
        .module('app.shared')
        .controller('CommentController', CommentController);

    CommentController.$inject = ['$uibModalInstance', 'commentsService', 'headerBannerService', 'commentObject', 'userService', '$state'];

    function CommentController($uibModalInstance, commentsService, headerBannerService, commentObject, userService, $state) {

        var vm = this;
        var newMessageAdded = false;

        vm.isAddComment = false;
        vm.comments = [];

        vm.isDealerPlusPage = isDealerPlusPage;
        vm.addComment = addComment;
        vm.cancel = cancel;

        function cancel() {
            if (newMessageAdded) {
                $uibModalInstance.close(commentObject);
            } else {
                $uibModalInstance.dismiss();
            }
        }

        initialize();

        function initialize() {
            if (commentObject.threadId !== undefined) {
                commentsService.getComments(commentObject.threadId)
                    .then(getCommentSuccess, serviceCallFailed);
            }
            vm.allowAddingComments = commentObject.blockAddingComment ? !commentObject.blockAddingComment : true;
        }

        function getCommentSuccess(response) {
            vm.comments = response.data;
        }

        function serviceCallFailed(error) {
            headerBannerService.setNotification('ERROR', error.data.error, 10000);
        }


        function addComment() {
            if (vm.message && vm.message.length > 0) {
                if (commentObject.threadId === undefined) {
                    commentsService.createCommentThread(commentObject.orderId, commentObject.orderItemId)
                        .then(createCommentThreadSuccess, createCommentThreadFailed);
                } else {
                    addMessageToThread(commentObject.threadId, vm.message);
                }
            }
        }

        function createCommentThreadSuccess(response) {
            commentObject.threadId = response.data;
            addMessageToThread();
        }

        function createCommentThreadFailed(response) {
            if (response.status == 409) {
                commentObject.threadId = response.data;
                addMessageToThread();
            } else {
                serviceCallFailed
            }
        }

        function addMessageToThread() {
            commentsService.addMessage(commentObject.threadId, vm.message)
                .then(addMessageSuccess, serviceCallFailed)
        }

        function addMessageSuccess() {
            newMessageAdded = true;
            vm.message = '';
            initialize();
        }

        function isDealerPlusPage(){
            return userService.isDealerPlusUser() && $state.current.name.includes("customerOrders");
        }
    }
})();
