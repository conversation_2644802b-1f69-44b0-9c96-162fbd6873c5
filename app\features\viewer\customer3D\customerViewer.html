<div class="vertical-align loader" id="loader">
  <div id="loader-text">
    <i class="fas fa-sync-alt fa-spin"></i>
    <p translate>CUST_VIEWER.LOADING_VIEWER_ASSETS</p>
    <p translate>CUST_VIEWER.TAKE_A_FEW_MINUTES</p>
  </div>
</div>

<!-- Top Bar -->
<div id="topBar" class="d-flex justify-content-between px-4">
  <section class="backButton">
    <a class="d-flex align-items-center" href="" ng-click="customerViewerCtrl.backToViewables()"
      ng-if="!customerViewerCtrl.fromWhereUsedModal && !customerViewerCtrl.showCloseButton"
      title="{{customerViewerCtrl.machineName}}- {{customerViewerCtrl.viewableName}}">
      <i class="fa fa-chevron-left mr-3"></i>
      <div>
        <p class="mb-0">
          <small translate>CUST_VIEWER.BACK_TO_VIEWABLES</small>
        </p>
      </div>
    </a>
    <a class="d-flex align-items-center" href="" ng-click="customerViewerCtrl.goBackToPartSearch()"
      ng-if="customerViewerCtrl.fromWhereUsedModal"
      title="{{customerViewerCtrl.machineName}}- {{customerViewerCtrl.viewableName}}">
      <i class="fa fa-chevron-left mr-3"></i>
      <div>
        <p class="mb-0">
          <small translate>CUST_VIEWER.BACK_TO_PART_SEARCH</small>
        </p>
      </div>
    </a>
    <a class="d-flex align-items-center" href="" ng-click="customerViewerCtrl.closeTab()"
      ng-if="customerViewerCtrl.showCloseButton"
      title="{{customerViewerCtrl.machineName}}- {{customerViewerCtrl.viewableName}}">
      <i class="fa fa-chevron-left mr-3"></i>
      <div>
        <p class="mb-0">
          <small translate>CUST_VIEWER.CLOSE_TAB</small>
        </p>
      </div>
    </a>
  </section>

  <section class="viewableName text-center">
    <h4 class="mb-0">
      {{customerViewerCtrl.machineName}}
      {{customerViewerCtrl.viewableName}}
    </h4>
  </section>

  <section class="emptySection" style="width: 9%;"></section>
</div>

<!-- Sidebar -->
<div id="sidebar">
  <div class="customerViewer-sidebar-content d-flex flex-column mt-3 w-100 align-items-center cadGap">
    <button ng-click="customerViewerCtrl.toggleOverlayBox('partsPallet')" class="py-2 toggle-sidebar-icon"
      ng-class="{'active': customerViewerCtrl.isPartsPalletActive}">
      <i class="fa fa-file-text-o fa-fw"></i>
    </button>
    <button ng-click="customerViewerCtrl.toggleOverlayBox('basketBox')"
      class="position-relative py-2 toggle-sidebar-icon" ng-class="{'active': customerViewerCtrl.isBasketBoxActive}">
      <i class="fa fa-shopping-basket fa-fw"></i>
      <span ng-if="customerViewerCtrl.basketSize > 0" class="basket-quantity">{{customerViewerCtrl.basketSize}}</span>
    </button>
    <button ng-click="customerViewerCtrl.toggleOverlayBox('part-search-container')"
      class="py-2 toggle-sidebar-icon search-part-box" ng-class="{'active': customerViewerCtrl.isSearchBoxActive}">
      <i class="fa fa-search fa-fw"></i>
    </button>
  </div>
</div>

<div id="customerViewerId">

  <div class="overlay-container">
    <div class="overlay-row">
      <div style="display: flex; flex-direction: column; position: relative; z-index: 40;"> 
        <div id="part-search-container" ng-class="{'mr-3': customerViewerCtrl.isPartsPalletActive || customerViewerCtrl.isBasketBoxActive}" ng-show="customerViewerCtrl.isSearchBoxActive" class="mb-3" style="position: relative; z-index: 30;">
          <div class="search-header">
            <small class="small text-left mb-1 font-weight-bold">
              {{ customerViewerCtrl.searchMode === 'partNumber' ? ('CUST_VIEWER.PART_NUMBER_SEARCH' | translate) : ('CUST_VIEWER.PART_DESC_SEARCH' | translate) }}
            </small>
            <a href="#" class="search-mode-toggle" ng-click="customerViewerCtrl.toggleSearchMode(); $event.preventDefault()">
              <span translate>CUST_VIEWER.SEARCH_BY</span> {{ customerViewerCtrl.searchMode === 'partNumber' ? ('CUST_VIEWER.DESCRIPTION' | translate) : ('CUST_VIEWER.PART_NUMBER_FULL' | translate) }}
            </a>
          </div>
          <div id="search-box" style="position: relative;">
            <input id="partSearch" type="text" placeholder="{{ customerViewerCtrl.searchMode === 'partNumber' ? ('CUST_VIEWER.ENTER_PART_NUMBER' | translate) : ('CUST_VIEWER.ENTER_PART_DESCRIPTION' | translate) }}"
              ng-model="customerViewerCtrl.searchValue" ng-change="customerViewerCtrl.onSearchInputChange($event)" />
            <button id="clear-btn" ng-click="customerViewerCtrl.clearPartSearch()">
              <i class="fa fa-times"></i>
            </button>
          </div>
          
          <!-- No results message -->
          <div class="no-results-message" ng-show="customerViewerCtrl.showNoResultsMessage">
            <i class="fa fa-info-circle"></i>
            <span>{{ 'CUST_VIEWER.NO_PARTS_FOUND' | translate }} "{{customerViewerCtrl.searchValue}}"</span>
          </div>
          
          <!-- Search results dropdown -->
          <div id="search-results-container" ng-show="customerViewerCtrl.showSearchResults">
            <div class="search-results-header" ng-click="customerViewerCtrl.toggleDropdownMinimized()">
              <span class="results-title"><span translate>CUST_VIEWER.SEARCH_RESULTS</span></span>
              <button class="minimize-toggle">
                <i class="fa" ng-class="{'fa-chevron-up': !customerViewerCtrl.isDropdownMinimized, 'fa-chevron-down': customerViewerCtrl.isDropdownMinimized}"></i>
              </button>
            </div>
            <div class="search-results-dropdown" id="searchResultsDropdown">
              <!-- Show only selected item when minimized -->
              <ul ng-show="customerViewerCtrl.isDropdownMinimized && customerViewerCtrl.selectedPartNumber">
                <li ng-repeat="result in customerViewerCtrl.searchResults | filter:{partNumber:customerViewerCtrl.selectedPartNumber}:true" class="selected-part">
                  <span class="part-number">{{result.partNumber}}</span>
                  <span class="part-description" title="{{result.description}}">- {{result.description}}</span>
                </li>
              </ul>
              <!-- Show all results when not minimized -->
              <ul ng-show="!customerViewerCtrl.isDropdownMinimized">
                <li ng-repeat="result in customerViewerCtrl.searchResults track by result.partNumber" 
                    ng-click="customerViewerCtrl.selectSearchResult(result)"
                    ng-class="{'selected-part': result.partNumber === customerViewerCtrl.selectedPartNumber}">
                  <span class="part-number">{{result.partNumber}}</span>
                  <span class="part-description" title="{{result.description}}">- {{result.description}}</span>
                </li>
              </ul>
            </div>
          </div>
        </div>

        <div id="SupersessionHistory" ng-class="{'mr-3': customerViewerCtrl.isPartsPalletActive || customerViewerCtrl.isBasketBoxActive}" ng-if="customerViewerCtrl.showSupersessionHistory" style="position: relative; z-index: 20;">
          <div class="flex cadGap supersessionHistoryHeader">
              <h3 class="mb-0" translate>CUST_VIEWER.SUPERSESSION_HISTORY</h3>
          </div>
          <div ng-if="customerViewerCtrl.isSupersessionLoading" class="p-4 text-center">
              <span class="spinner-border text-primary" role="status" aria-hidden="true"></span>
              <p translate>GENERAL.LOADING</p>
          </div>
          <ul class="list-unstyled" ng-if="!customerViewerCtrl.isSupersessionLoading">
              <li class="px-3" ng-repeat="part in customerViewerCtrl.supersessionParts">
                  <div class="supersessionViewerPart text-center">
                      <span>
                      <span class="font-weight-bold">{{ part.partNumber }}</span> - {{ part.partDescription }}
                  </div>
                  <i class="fa fa-long-arrow-up cadBlue" ng-class="{'hide': $last}"></i>
              </li>
          </ul>
        </div>
      </div> <!-- End of new wrapper -->

      <div class="overlay-content">
        <div class="overlay-inner-container">
          <div class="overlay-box" id="partsPallet" ng-show="customerViewerCtrl.isPartsPalletActive"
            ng-include="'features/viewer/customer3D/customerViewerPartsPallet.html'"></div>
          <div class="overlay-box" id="basketBox" ng-show="customerViewerCtrl.isBasketBoxActive"
            ng-include="'features/viewer/customer3D/customerViewerBasket.html'"></div>
        </div>
      </div>
    </div>
  </div>

  <side-menu style="visibility: hidden"></side-menu>

  <div
    class="customer_viewer"
    ng-class="{'on-behalf-of-shrink': customerViewerCtrl.onBehalfOf}"
  >
    <div
      class="product-viewer vertical"
      ng-class="{'on-behalf-of-shrink': customerViewerCtrl.onBehalfOf}"
    >
      <div
        class="watermark-parent"
        id="watermark-container"
        ng-show="customerViewerCtrl.showWatermark"
      >
        <div class="watermark" id="watermark-tile">
          <img
            src="{{customerViewerCtrl.watermarkImage}}"
            id="watermark-image"
          />
          <div>
            {{"CUST_VIEWER.PATENT_PROTECTED" | translate}}
            {{customerViewerCtrl.fullName}} {{customerViewerCtrl.emailAddress}}
          </div>
        </div>
      </div>
      <viewer-banner></viewer-banner>
      <live-meeting-banner></live-meeting-banner>
      <div ng-show="customerViewerCtrl.isLoading" id="loadingIcon" class="loading-icon">
          <i class="fa fa-spinner fa-spin"></i>
        </div>
      <div
        id="MyViewerDiv" context="menuContext" context-enabled="{{customerViewerCtrl.canHideIsolate}}"
        ng-style="{'pointer-events': customerViewerCtrl.isLoading ? 'none' : 'auto'}"
        ng-mousemove="customerViewerCtrl.updateLoadingIconPosition($event)">
        
      </div>

      <div class="customer-explode-tool-container">
        <div
          class="customerExplodeRangeInput"
          ng-show="customerViewerCtrl.isExplodeSliderVisible"
        >
          <input
            type="range"
            id="explode_range"
            value="0"
            min="0"
            max="100"
            step="10"
            data-show-value="true"
          />
        </div>
      </div>
    </div>
  </div>

  <div
    class="custViewerHorizontal container pointerEventNone"
    ng-hide="customerViewerCtrl.isVertical"
  >
    <div class="HorizontalThumbStep d-flex">
      <div
        class="px-3 py-2 font-weight-bold mb-2 mr-2 product-thumb-step vertical-align pointerEventAuto"
        ng-repeat="step in customerViewerCtrl.steps track by step.stateId"
        title="{{step.stateName}}"
        ng-class="($last) ? (customerViewerCtrl.currentList < 1  ? 'product-thumb-step-selected-last' : 'product-thumb-step-selected') : ''"
        ng-click="customerViewerCtrl.loadSnapshot(step.stateId)"
      >
        <span class="textOverflow">{{step.stateName}}</span>
      </div>
    </div>
    <div
      id="custViewerHorizontal_container"
      class="custViewerHorizontal_container"
    >
      <ul class="custViewerHorizontal_list" style="transform: translateY(0px)">
        <li
          class="custViewerHorizontal_item col-md-auto pointerEventAuto"
          ng-repeat="snapshot in customerViewerCtrl.currentList track by snapshot.stateId"
        >
          <div
            class="custViewerHorizontal_item_img"
            ng-click="customerViewerCtrl.loadSnapshot(snapshot.stateId)"
            style="background-image:url({{snapshot.imgUrl}}); background-position: right; background-size: cover; background-repeat-x: no-repeat;height:140px; border: 5px solid white;"
          ></div>
          <h4
            class="custViewerHorizontal_text px-2 pb-2 mb-0 font-weight-bold"
            ng-click="customerViewerCtrl.loadSnapshot(snapshot.stateId)"
          >
            {{snapshot.stateName}}
          </h4>
        </li>
      </ul>
    </div>
    <button
      class="custViewerHorizontal_btn custViewerHorizontal_btn--prev flex-center mr-2 pointerEventAuto"
      title="Previous"
      aria-label="Previous"
      disabled=""
    >
      <i class="fas fa-angle-left" style="font-size: 2rem"></i>
    </button>
    <button
      class="custViewerHorizontal_btn custViewerHorizontal_btn--next flex-center pointerEventAuto"
      title="Next"
      aria-label="Next"
    >
      <i class="fas fa-angle-right" style="font-size: 2rem"></i>
    </button>
  </div>

  <!--<div ng-show="customerViewerCtrl.showNotes && !customerViewerCtrl.isVertical" >
    <div class="viewer-notes-opened-horizontal" ng-show="customerViewerCtrl.isNotesOpen">
        <div class="notes-header" ng-click="customerViewerCtrl.closeNotes()">
            <h4>{{"CUST_VIEWER.SNAPSHOT_NOTES" | translate}}<i class="fa fa-fw fa-window-minimize pull-right"></i>
            </h4>
        </div>
        <div class="notes-body">
            {{customerViewerCtrl.notes}}
        </div>
    </div>
    <div class="viewer-notes-closed-horizontal" ng-click="customerViewerCtrl.openNotes()">
        <h4>{{"CUST_VIEWER.SNAPSHOT_NOTES" | translate}} <i class="fa fa-fw fa-window-maximize pull-right"></i>
        </h4>
    </div>
</div>-->

  <div ng-show="customerViewerCtrl.showNotes && !customerViewerCtrl.isVertical">
    <div id="cadSnapshotNotesHorizontal" class="cadSnapshotNotes">
      <div class="card mb-0 position-relative border-0">
        <div
          class="card-header btn primary rounded-0 d-flex justify-content-between collapsed"
          data-toggle="collapse"
          href="#collapseHori"
          aria-expanded="true"
        >
          <a class="card-title m-0 font-weight-bold">
            {{"CUST_VIEWER.SNAPSHOT_NOTES" | translate}}
          </a>
        </div>

        <div
          id="collapseHori"
          class="collapse"
          aria-labelledby="headingOne"
          data-parent="#cadSnapshotNotesHorizontal"
        >
          <div class="card-body">{{customerViewerCtrl.notes}}</div>
        </div>
      </div>
    </div>
  </div>

  <div
    class="custViewerVertical custViewerVertical_height col-auto p-0"
    ng-show="customerViewerCtrl.isVertical"
  >
    <div class="d-none d-lg-flex custViewerVertical_header">
      <button
        class="custViewerVertical_btn custViewerVertical_btn--prev flex-center mr-2"
        title="Previous"
        aria-label="Previous"
        disabled=""
      >
        <i
          class="fas fa-angle-up"
          style="font-size: inherit; font-size: 2rem"
        ></i>
      </button>
      <button
        class="custViewerVertical_btn custViewerVertical_btn--next flex-center"
        title="Next"
        aria-label="Next"
      >
        <i
          class="fas fa-angle-down"
          style="font-size: inherit; font-size: 2rem"
        ></i>
      </button>
    </div>
    <div class="VerticalThumbStepContainer">
      <div
        style="width: 100%"
        class="px-3 py-2 font-weight-bold mb-2 mr-2 product-thumb-step vertical-align"
        ng-repeat="step in customerViewerCtrl.steps track by step.stateId"
        title="{{step.stateName}}"
        ng-class="($last) ? (customerViewerCtrl.currentList < 1  ? 'product-thumb-step-selected-last' : 'product-thumb-step-selected') : ''"
        ng-click="customerViewerCtrl.loadSnapshot(step.stateId)"
      >
        <span class="textOverflow">{{step.stateName}}</span>
      </div>
    </div>
    <div class="custViewerVertical_container" id="custViewerVertical_container">
      <ul class="custViewerVertical_list" style="transform: translateY(0px)">
        <li
          class="custViewerVertical_item"
          ng-repeat="snapshot in customerViewerCtrl.currentList track by snapshot.stateId"
        >
          <div
            class="custViewerVertical_item_img"
            ng-click="customerViewerCtrl.loadSnapshot(snapshot.stateId)"
            style="background-image:url({{snapshot.imgUrl}}); background-position: right; background-size: cover; background-repeat-x: no-repeat;border: 5px solid white;"
          ></div>
          <h4
            class="d-none d-lg-block px-2 pb-2 mb-0 font-weight-bold"
            ng-click="customerViewerCtrl.loadSnapshot(snapshot.stateId)"
          >
            {{snapshot.stateName}}
          </h4>
        </li>
      </ul>
    </div>

    <!--    <div ng-show="customerViewerCtrl.showNotes">
        <div class="viewer-notes-opened-vertical" ng-show="customerViewerCtrl.isNotesOpen">
            <div class="notes-header" ng-click="customerViewerCtrl.closeNotes()">
                <h4>{{"CUST_VIEWER.SNAPSHOT_NOTES" | translate}}<i class="fa fa-fw fa-window-minimize pull-right"></i>
                </h4>
            </div>
            <div class="notes-body">
                {{customerViewerCtrl.notes}}
            </div>
        </div>
        <div class="viewer-notes-closed-vertical" ng-hide="customerViewerCtrl.isNotesOpen"
             ng-click="customerViewerCtrl.openNotes()">
            <h4>{{"CUST_VIEWER.SNAPSHOT_NOTES" | translate}} <i class="fa fa-fw fa-window-maximize pull-right"></i>
            </h4>
        </div>-->
    <div
      ng-show="customerViewerCtrl.showNotes && customerViewerCtrl.isVertical"
    >
      <div id="cadSnapshotNotesVertical" class="cadSnapshotNotes">
        <div class="card mb-0 position-relative border-0">
          <div
            class="card-header btn primary rounded-0 d-flex justify-content-between collapsed"
            data-toggle="collapse"
            href="#collapseVert"
            aria-expanded="true"
          >
            <a class="card-title m-0 font-weight-bold">
              {{"CUST_VIEWER.SNAPSHOT_NOTES" | translate}}
            </a>
          </div>

          <div
            id="collapseVert"
            class="collapse"
            aria-labelledby="headingOne"
            data-parent="#cadSnapshotNotesVertical"
          >
            <div class="card-body">{{customerViewerCtrl.notes}}</div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- TODO: Remove context menu options -->
  <ul
    id="menuContext"
    class="dropdown dropdown-content"
    ng-show="customerViewerCtrl.isEntitySelected"
  >
    <li><a ng-click="customerViewerCtrl.hide()">Hide</a></li>
    <li><a ng-click="customerViewerCtrl.isolate()">Isolate</a></li>
  </ul>
</div>

<div class="rotate-device-overlay" ng-class="{'show': customerViewerCtrl.showRotateOverlay}" ng-if="customerViewerCtrl.showRotateOverlay">
  <div class="rotate-device-content">
    <div class="phone-animation">
      <div class="phone">
        <div class="phone-screen"></div>
      </div>
    </div>
    <p class="rotate-text">{{ 'CUST_VIEWER.ROTATE_DEVICE' | translate }}</p>
  </div>
</div>