.listBoxContainer{

  height: 50vh;

}

.listBoxInformation{

  justify-content: space-between;

}

.listBoxLeft{

  background:white;
  height:100%;
  overflow-y: auto;
  box-shadow: 0 3px 6px rgba(0,0,0,0.16), 0 3px 3px rgba(0,0,0,0.10);

}

.listBoxRight{

  background:white;
  height:100%;
  overflow-y: auto;
  box-shadow: 0 3px 6px rgba(0,0,0,0.16), 0 3px 3px rgba(0,0,0,0.10);

}


.btn-cancel{

  border: solid 1px #ACB2B8!important;
  background: #F2F6F9!important;
  color: #ACB2B8!important;
  opacity:.65!important;
  pointer-events: none;
  font-size: 0.85em;
  text-transform: uppercase;
  padding: 8px 16px;
  border-radius: 5px;
  display: inline-block;
  text-decoration: none;
  text-align: center;
  font-weight: 700;
  line-height: 1.69em;

}

.btn.assignlistboxLeft{

  background-color:#3392FC;
  color:white;
  border: solid 3px #3392FC;
  width: 50px;
  height: 50px;
  transition: 0.5s;
  border-radius:0;
  transition:0.5s;
  cursor: pointer;

}

.btn.assignlistboxLeft:hover{

  border: solid 3px #1a85fc;
  background-color:white;
  color:#3392FC;
  border-radius:0;

}

.btn.assignlistboxRight{

  border: solid 3px #1a85fc;
  width: 50px;
  height: 50px;
  transition: 0.5s;
  color:#3392FC;
  border-radius:0;
  cursor: pointer;

}

.btn.assignlistboxRight:hover{

  background-color:#3392FC;
  color:white;
  border: solid 3px #3392FC;
  border-radius:0;

}

.assignlistboxStyle p {

  text-align: left;

}

.list-group i{

  color: #3392FC;
  font-size: 1em;
  border: 1.5px lightgrey solid;
  padding: 2% 4%;
  border-radius: 2px;
  cursor: pointer;

}

.info_listbox{

  cursor: pointer;
  border: 1px solid #D2DAE5;
  background: #F2F6F9;

}

.info_listbox:hover{

  color: #1a89ff;
  background: #e3ebfa;

}

.listbox_accordion{

  background:white;
  margin-top: 5%;
  padding: 2.5%;
  cursor: initial;

}

.badge-pill {
  border: 1px solid lighten($textdark2,10%);
  color: $textdark2;
  display: inline-block;
  font-size: 0.8em;
  padding: 4px 8px;
  min-width: 10px;
  background:$white;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  @include border-radius(999999px);
  text-transform: uppercase;
  font-weight:700;
  &.primary {
    border-color:$blue;
    color: $blue;
  }
  &.primary i {
    border-color:$blue;
    color: $blue;
  }
  &.success {
    border-color:$textdark2;
    color: $green;
  }
  &.badge-lg {
    font-size: 0.7em;
    padding: 6px 12px;
  }
}

.assignlistboxStyle{

  background:#F8F8F8;
  border-left:4px solid #d9d9d9;
  cursor: pointer;

}

.assignlistboxStyleCustom{

  background:white;
  border-left:4px solid white;
  cursor: pointer;
  border-left: 4px solid #d9d9d9;
  padding-bottom: 0.5em;
  padding-top: 0.5em;

}

.assignlistboxHoverCustom{

  border-left:4px solid #1a89ff;
  background:white;
  cursor: pointer;
  padding-bottom: 0.5em;
  padding-top: 0.5em;

}

.assignlistboxHover{

  border-left:4px solid #1a89ff;
  background:#F8F8F8;
  box-shadow: 0 0 3px rgba(0,0,0,.20);
  cursor: pointer;

}

.search-clear{
  background: rgba(0,0,0, 0.15);
  border-radius: 50%;
  font-weight: bold;
  font-size: 12px;
  height: 16px;
  width: 16px;
  text-align: center;
  line-height: 16px;
  z-index: 1000;
  position: absolute;
  right: 65px;
  top: 50%;
  margin-top: -8px;
  text-decoration:none;
  color:white;
  cursor: pointer;
  transition: 0.5s ease-in-out;
}

.search-clear:hover{
  background: rgba(0,0,0, 0.55);
}



@media screen and (max-width: 770px) {

  .listBoxContainer{

    height: 100%;

  }

  .listbox_buttons {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: initial;
  }

  .listbox_items {
    flex-direction: row;
    display: flex;
    align-items: center;
  }

  .item{

    -webkit-transform: rotate(90deg);
    -moz-transform: rotate(90deg);
    -o-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    transform: rotate(90deg);
    cursor: pointer;

  }

}