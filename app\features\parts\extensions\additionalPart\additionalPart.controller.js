(function () {
    'use strict';

    angular
        .module('app.parts')
        .controller('AdditionalPartController', AdditionalPartController);

    AdditionalPartController.$inject = ['$stateParams', 'masterPartService', 'headerBannerService', '$state', '$translate'];

    function AdditionalPartController($stateParams, masterPartService, headerBannerService, $state, $translate) {
        var vm = this;

        vm.isEdit = $stateParams.isEdit;
        vm.masterPartId = $stateParams.masterPartId;
        vm.parts = [];

        var WENT_WRONG, BOM_SAVED;
        $translate(['GENERAL.WENT_WRONG', 'ADDITIONAL_PARTS.BOM_SAVED'])
            .then(function (resp) {
                WENT_WRONG = resp["GENERAL.WENT_WRONG"];
                BOM_SAVED = resp["ADDITIONAL_PARTS.BOM_SAVED"];
            });

        vm.onAddClicked = onAddClicked;
        vm.save = save;
        vm.cancel = cancel;
        vm.removePart = removePart;

        initialize();

        function initialize() {
            if (vm.isEdit) {
                masterPartService.getAdditionalPart(vm.masterPartId)
                    .then(getAdditionalPartSuccess, serviceFailed);
            }
        }

        function getAdditionalPartSuccess(response) {
            vm.parts = response.data.nonModelledPart;
        }

        function serviceFailed(error) {
            console.error(error);
            headerBannerService.setNotification('ERROR', WENT_WRONG, 10000);
        }

        function onAddClicked(masterPart) {
            if (_.findIndex(vm.parts, {masterPartId: masterPart.masterPartId}) < 0) {
                vm.parts.push(masterPart);
                vm.noPartsSelectedError = false;
            }
        }

        function removePart(index) {
            vm.parts.splice(index, 1);
        }

        function save() {
            vm.noPartsSelectedError = false;

            if (vm.parts.length < 1) {
                vm.noPartsSelectedError = true;
                return;
            }

            if (vm.isEdit) {
                masterPartService.editAdditionalPart(vm.masterPartId, vm.parts)
                    .then(createEditSuccess, serviceFailed);
            } else {
                masterPartService.createAdditionalPart(vm.masterPartId, vm.parts)
                    .then(createEditSuccess, serviceFailed);
            }
        }

        function createEditSuccess() {
            headerBannerService.setNotification('SUCCESS', BOM_SAVED, 5000);
            $state.go('masterPart', {masterPartId: vm.masterPartId});
        }

        function cancel() {
            $state.go('masterPart', {masterPartId: vm.masterPartId});
        }

    }
})();

