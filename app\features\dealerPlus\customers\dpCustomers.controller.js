(function () {
    'use strict';

    angular
        .module('app.customer')
        .controller('DPCustomersController', DPCustomersController);

    DPCustomersController.$inject = ['$uibModal', '$state', 'headerBannerService', 'dpCustomerService', 'userService', '$translate', '$window'];

    function DPCustomersController($uibModal, $state, headerBannerService, dpCustomerService, userService, $translate, $window) {

        var vm = this;

        vm.start = 0;
        vm.sortReverse = false;
        vm.endRecord = vm.itemPerPage;
        vm.areCustomersLoaded = false;
        vm.successMessage = "";
        vm.filter_range = "";
        vm.ranges = [];
        vm.sortReverse = true;
        vm.customer_sort = 'manufacturerSubEntityId';
        vm.displayManufacturerSubEntityId = userService.getDisplayManufacturerSubEntityId();

        vm.loadingInfiniteScrollData = false;
        vm.showBackToTopButton = false;
        vm.isFixedHeader = false;

        var COMPANY, CREATE_DATE, TYPE;
        $translate(['CUSTOMERS.COMPANY', 'CUSTOMERS.CREATE_DATE', 'CUSTOMERS.TYPE'])
            .then(function (resp) {
                COMPANY = resp["CUSTOMERS.COMPANY"];
                CREATE_DATE = resp["CUSTOMERS.CREATE_DATE"];
                TYPE = resp["CUSTOMERS.TYPE"];

                vm.sortBy = [
                    {name: 'name', value: COMPANY},
                    {name: 'createdDate', value: CREATE_DATE},
                    {name: 'manufacturerSubEntityType', value: TYPE}
                ];
            });


        vm.pageChanged = pageChanged;
        vm.editCompany = editCompany;
        vm.assignPublications = assignPublications;
        vm.createCompany = createCompany;
        vm.deleteCompany = deleteCompany;
        vm.scrollToTop = scrollToTop;
        vm.loadMoreInfiniteScroll = loadMoreInfiniteScroll;

        vm.viewCustomerUsers = viewCustomerUsers;

        initialize();

        function initialize() {
            fetchUsers();
        }

        function fetchUsers() {
            vm.loadingInfiniteScrollData = true;
            dpCustomerService.getCustomers()
                .then(getCustomersSuccess, getCustomersFailed)
        }

        function getCustomersSuccess(response) {
            loadMoreInfiniteScroll();
            vm.allCustomers = response.data;
            vm.customerList = vm.allCustomers.slice(0, 100);  
            vm.totalItems = vm.allCustomers.length;
            vm.areCustomersLoaded = true;
            vm.loadingInfiniteScrollData = false;
        }

        function getCustomersFailed(error) {
            vm.areCustomersLoaded = false;
            headerBannerService.setNotification('ERROR', error.data.error, 10000);
        }

        function pageChanged() {
            vm.start = ((vm.currentPage - 1) * vm.itemPerPage);
        }

        function editCompany(customer) {
            $uibModal.open({
                templateUrl: 'features/dealerPlus/customers/editCompany/dpEditCustomerCompany.html',
                controller: 'DPEditCustomerCompanyController',
                controllerAs: 'dpEditCustomerCompanyCtrl',
                size: 'md',
                backdrop: 'static',
                resolve: {
                    companyDetails: function () {
                        return {name: customer.name, defaultDiscount: customer.defaultDiscount, subEntityId: customer.manufacturerSubEntityId};
                    }
                }
            })
                .result.then(function () {
                $state.reload()
            });
        }

        function createCompany() {
            $uibModal.open({
                templateUrl: 'features/dealerPlus/customers/createCompany/dpCreateCustomerCompany.html',
                controller: 'DPCreateCustomerCompanyController',
                controllerAs: 'dpCreateCustomerCompanyCtrl',
                size: 'md',
                backdrop: 'static'
            })
                .result.then(function () {
                $state.reload()
            });
        }

        function deleteCompany(customer) {
            $uibModal.open({
                templateUrl: 'features/dealerPlus/customers/deleteCompany/dpDeleteCustomerCompany.html',
                controller: 'DPDeleteCustomerCompanyController',
                controllerAs: 'dpDeleteCustomerCompanyCtrl',
                size: 'md',
                backdrop: 'static',
                resolve: {
                    companyDetails: function () {
                        return {name: customer.name, manufacturerSubEntityId: customer.manufacturerSubEntityId};
                    }
                }
            })
                .result.then(function () {
                $state.reload()
            });

        }

        function assignPublications(customer) {
            $state.go("dpAssignPublications", {
                subEntityId: customer.manufacturerSubEntityId,
                name: customer.name
            });
        }

        function viewCustomerUsers(customer) {
            $state.go("dpCustomerUsers", {
                subEntityId: customer.manufacturerSubEntityId,
                type: customer.manufacturerSubEntityType,
                name: customer.name
            });
        }


  function loadMoreInfiniteScroll() {
   var threshold = 250;
    vm.isFixedHeader = window.scrollY > threshold;

        if (window.innerHeight >= document.body.offsetHeight - 2 && !vm.loadingInfiniteScrollData && !vm.initialLoadDone) {
            vm.loadingInfiniteScrollData = true;

            var nextItems = vm.allCustomers.slice(vm.customerList.length, vm.customerList.length + 100);
            if (nextItems.length === 0) {
                vm.initialLoadDone = true;
                vm.loadingInfiniteScrollData = false;
                return;
            }

            vm.customerList = vm.customerList.concat(nextItems);

            vm.loadingInfiniteScrollData = false;
            if (vm.customerList.length >= vm.totalItems) {
                vm.initialLoadDone = true;
            }
        }
}

  function scrollToTop() {
      $window.scrollTo({ top: 0, behavior: "smooth" });
      $("html, body").animate({ scrollTop: 0 }, "slow", function () {
        $("#scrollToTop").removeClass("scrolled-past");
      });
    }

    }
})();
