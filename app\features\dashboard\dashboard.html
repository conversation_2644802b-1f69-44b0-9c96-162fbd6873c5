<section class="m-4">
    <section class="dashboard">
        <div class="panel">
            <div class="dropdown-holder">
                <h3 class="mb-0" translate>DASHBOARD.USERS</h3>
                <select
                    ng-options="manufacturerSubEntity as manufacturerSubEntity.name for manufacturerSubEntity in dashboardCtrl.manufacturerSubEntities | orderBy:dashboardCtrl.sortAndPin"
                    ng-model="dashboardCtrl.selectedSubEntity"
                    ng-change="dashboardCtrl.populateOrderValues()"
                    class="dashboard-select"
                ></select>
            </div>

            <div class="dropdown-holder">
                <h3 class="mb-0" translate>DASHBOARD.TIME_PERIOD</h3>
                <select
                    ng-options="timePeriod as timePeriod.label for timePeriod in dashboardCtrl.timePeriods"
                    ng-model="dashboardCtrl.selectedTimePeriod"
                    ng-change="dashboardCtrl.populateOrderValues()"
                    class="dashboard-select"
                ></select>

                <div ng-show="dashboardCtrl.showCustomDatePickers">
                    <input
                        type="text"
                        id="datepickerStartDate"
                        ng-model="dashboardCtrl.customStartDate"
                        placeholder="DD/MM/YYYY"
                        name="customStartDate"
                        class="width-35"
                    />

                    <input
                        type="text"
                        id="datepickerEndDate"
                        ng-model="dashboardCtrl.customEndDate"
                        placeholder="DD/MM/YYYY"
                        name="customEndDate"
                        class="width-35"
                    />

                    <button class="btn primary width-20" ng-click="dashboardCtrl.populateOrderValues()" type="submit" translate>
                        DASHBOARD.FILTER
                    </button>
                </div>
            </div>

            <!--TOTAL ENQUIRIES-->
            <div class="panel quarter col-md-3 flex-column">
                <div class="heading">
                    <h3><i class="fas fa-briefcase"></i> {{'DASHBOARD.TOTAL_ENQUIRIES' | translate}}</h3>
                </div>
                <div class="body">
                    <p class="total-amount">{{dashboardCtrl.totalEnquiries}}</p>
                    <div
                        ng-hide="dashboardCtrl.selectedTimePeriod.enum === 'ALL_TIME' || dashboardCtrl.selectedTimePeriod.enum === 'CUSTOM'"
                    >
                        <p class="status" ng-if="dashboardCtrl.totalEnquiriesDifference > 0">
                            <i class="fa fa-chevron-up increased"></i> {{'DASHBOARD.INCREASED_BY' | translate}}
                            {{dashboardCtrl.totalEnquiriesDifference}}
                        </p>
                        <p class="status" ng-if="dashboardCtrl.totalEnquiriesDifference === 0">
                            <i class="fas fa-minus maintained"></i> {{'DASHBOARD.NO_CHANGE' | translate}}
                        </p>
                        <p class="status" ng-if="dashboardCtrl.totalEnquiriesDifference < 0">
                            <i class="fas fa-chevron-down decreased"></i> {{'DASHBOARD.DECREASED_BY' | translate}}
                            {{dashboardCtrl.totalEnquiriesDifference}}
                        </p>
                    </div>
                </div>
            </div>

            <!--TOTAL QUOTES-->
            <div class="panel quarter col-md-3 flex-column">
                <div class="heading">
                    <h3><i class="fas fa-briefcase"></i> {{'DASHBOARD.TOTAL_QUOTES' | translate}}</h3>
                </div>
                <div class="body">
                    <p class="total-amount">{{dashboardCtrl.totalQuotes}}</p>

                    <div
                        ng-hide="dashboardCtrl.selectedTimePeriod.enum === 'ALL_TIME' || dashboardCtrl.selectedTimePeriod.enum === 'CUSTOM'"
                    >
                        <p class="status" ng-if="dashboardCtrl.totalQuotesDifference > 0">
                            <i class="fa fa-chevron-up increased"></i> {{'DASHBOARD.INCREASED_BY' | translate}}
                            {{dashboardCtrl.totalQuotesDifference}}
                        </p>
                        <p class="status" ng-if="dashboardCtrl.totalQuotesDifference === 0">
                            <i class="fas fa-minus maintained"></i> {{'DASHBOARD.NO_CHANGE' | translate}}
                        </p>
                        <p class="status" ng-if="dashboardCtrl.totalQuotesDifference < 0">
                            <i class="fas fa-chevron-down decreased"></i> {{'DASHBOARD.DECREASED_BY' | translate}}
                            {{dashboardCtrl.totalQuotesDifference}}
                        </p>
                    </div>
                </div>
            </div>

            <!--TOTAL ORDERS-->
            <div class="panel quarter col-md-3 flex-column">
                <div class="heading">
                    <h3><i class="fas fa-briefcase"></i> {{'DASHBOARD.TOTAL_ORDERS' | translate}}</h3>
                </div>
                <div class="body">
                    <p class="total-amount">{{dashboardCtrl.totalOrders}}</p>
                    <div
                        ng-hide="dashboardCtrl.selectedTimePeriod.enum === 'ALL_TIME' || dashboardCtrl.selectedTimePeriod.enum === 'CUSTOM'"
                    >
                        <p class="status" ng-if="dashboardCtrl.totalOrdersDifference > 0">
                            <i class="fa fa-chevron-up increased"></i> {{'DASHBOARD.INCREASED_BY' | translate}}
                            {{dashboardCtrl.totalOrdersDifference}}
                        </p>
                        <p class="status" ng-if="dashboardCtrl.totalOrdersDifference === 0">
                            <i class="fas fa-minus maintained"></i> {{'DASHBOARD.NO_CHANGE' | translate}}
                        </p>
                        <p class="status" ng-if="dashboardCtrl.totalOrdersDifference < 0">
                            <i class="fas fa-chevron-down decreased"></i> {{'DASHBOARD.DECREASED_BY' | translate}}
                            {{dashboardCtrl.totalOrdersDifference}}
                        </p>
                    </div>
                </div>
            </div>
            <!--CONVERSION RATE-->
            <div class="panel quarter col-md-3 flex-column">
                <div class="heading">
                    <h3><i class="fas fa-briefcase"></i> {{'DASHBOARD.CONVERSION_RATE' | translate}}</h3>
                </div>
                <div class="body">
                    <p class="total-amount">{{dashboardCtrl.conversionRate | number: 2}}%</p>
                    <div
                        ng-hide="dashboardCtrl.selectedTimePeriod.enum === 'ALL_TIME' || dashboardCtrl.selectedTimePeriod.enum === 'CUSTOM'"
                    >
                        <p class="status" ng-if="dashboardCtrl.conversionRateDifference > 0">
                            <i class="fa fa-chevron-up increased"></i> {{'DASHBOARD.INCREASED_BY' | translate}}
                            {{dashboardCtrl.conversionRateDifference | number: 2}}
                        </p>
                        <p class="status" ng-if="dashboardCtrl.conversionRateDifference === 0">
                            <i class="fas fa-minus maintained"></i> {{'DASHBOARD.NO_CHANGE' | translate}}
                        </p>
                        <p class="status" ng-if="dashboardCtrl.conversionRateDifference < 0">
                            <i class="fas fa-chevron-down decreased"></i> {{'DASHBOARD.DECREASED_BY' | translate}}
                            {{dashboardCtrl.conversionRateDifference | number: 2}}
                        </p>
                    </div>
                </div>
            </div>

            <!--QUOTE VALUE-->
            <div class="panel half col-md-6 flex-column">
                <div class="heading">
                    <h3><i class="fas fa-money-bill"></i> {{'DASHBOARD.VALUE_OF_QUOTES' | translate}}</h3>
                </div>
                <div class="body">
                    <p class="total-amount">{{dashboardCtrl.valueOfQuotes | currency:dashboardCtrl.defaultCurrency.symbol:0}}</p>
                    <div
                        ng-hide="dashboardCtrl.selectedTimePeriod.enum === 'ALL_TIME' || dashboardCtrl.selectedTimePeriod.enum === 'CUSTOM'"
                    >
                        <p class="status" ng-if="dashboardCtrl.valueOfQuotesDifference > 0">
                            <i class="fa fa-chevron-up increased"></i> {{'DASHBOARD.INCREASED_BY' | translate}}
                            {{dashboardCtrl.valueOfQuotesDifference | currency:dashboardCtrl.defaultCurrency.symbol:0}}
                        </p>
                        <p class="status" ng-if="dashboardCtrl.valueOfQuotesDifference === 0">
                            <i class="fas fa-minus maintained"></i> {{'DASHBOARD.NO_CHANGE' | translate}}
                        </p>
                        <p class="status" ng-if="dashboardCtrl.valueOfQuotesDifference < 0">
                            <i class="fas fa-chevron-down decreased"></i> {{'DASHBOARD.DECREASED_BY' | translate}}
                            {{dashboardCtrl.valueOfQuotesDifference | currency:dashboardCtrl.defaultCurrency.symbol:0}}
                        </p>
                    </div>
                </div>
            </div>

            <!--ORDERS VALUE-->
            <div class="panel half col-md-6 flex-column">
                <div class="heading">
                    <h3><i class="fas fa-money-bill"></i> {{'DASHBOARD.VALUE_OF_ORDERS' | translate}}</h3>
                </div>
                <div class="body">
                    <p class="total-amount">{{dashboardCtrl.valueOfOrders | currency:dashboardCtrl.defaultCurrency.symbol:0}}</p>
                    <div
                        ng-hide="dashboardCtrl.selectedTimePeriod.enum === 'ALL_TIME' || dashboardCtrl.selectedTimePeriod.enum === 'CUSTOM'"
                    >
                        <p class="status" ng-if="dashboardCtrl.valueOfOrdersDifference > 0">
                            <i class="fa fa-chevron-up increased"></i> {{'DASHBOARD.INCREASED_BY' | translate}}
                            {{dashboardCtrl.valueOfOrdersDifference | currency:dashboardCtrl.defaultCurrency.symbol:0}}
                        </p>
                        <p class="status" ng-if="dashboardCtrl.valueOfOrdersDifference === 0">
                            <i class="fas fa-minus maintained"></i> {{'DASHBOARD.NO_CHANGE' | translate}}
                        </p>
                        <p class="status" ng-if="dashboardCtrl.valueOfOrdersDifference < 0">
                            <i class="fas fa-chevron-down decreased"></i> {{'DASHBOARD.DECREASED_BY' | translate}}
                            {{dashboardCtrl.valueOfOrdersDifference | currency:dashboardCtrl.defaultCurrency.symbol:0}}
                        </p>
                    </div>
                </div>
            </div>

            <!--MOST ORDERED PARTS-->
            <div
                class="panel threequarter col-md-12 col-12 flex-column"
                ng-class="dashboardCtrl.selectedSubEntity.manufacturerSubEntityId === -1 ? 'threequarter' : 'full' "
            >
                <div class="heading mb-0">
                    <h3><i class="fas fa-industry"></i> {{'DASHBOARD.MOST_ORDERED_PARTS' | translate}}</h3>
                </div>
                <div class="body pieChart" ng-show="dashboardCtrl.mostOrderedPartsHasData">
                    <canvas id="mostOrderedPartsChart"></canvas>
                </div>
                <div
                    class="body mostOrderedPartNoData"
                    ng-hide="dashboardCtrl.mostOrderedPartsHasData || dashboardCtrl.isMostOrderedLoading"
                >
                    <br />
                    <br />
                    <h4 translate>DASHBOARD.NO_DATA</h4>
                </div>
                <div class="body mostOrderedPartNoData" ng-show="dashboardCtrl.isMostOrderedLoading">
                    <br />
                    <br />
                    <h4 translate>DASHBOARD.PLEASE_WAIT</h4>
                    <img ng-src="images/cadpreloader.gif" class="ajax-loader" height="60" width="60" src="images/cadpreloader.gif" />
                </div>
            </div>

            <!--MOST ACTIVE USERS-->
            <div
                class="panel quarter col flex-column quarter_column"
                ng-if="dashboardCtrl.selectedSubEntity.manufacturerSubEntityId === -1"
            >
                <div class="heading mb-0">
                    <h3><i class="fas fa-clipboard-list"></i> {{'DASHBOARD.MOST_ACTIVE_USERS_LOGIN' | translate}}</h3>
                </div>
                <div class="body text-left">
                    <table>
                        <tbody>
                            <tr ng-repeat="customer in dashboardCtrl.mostActiveCustomers">
                                <td>{{$index + 1}}</td>
                                <td><a href="" ng-click="dashboardCtrl.mostActiveSubEntityClicked($index)">{{customer.name}}</a></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!--USERS MOST ORDERS-->
            <div
                class="panel quarter col flex-column quarter_column"
                ng-if="dashboardCtrl.selectedSubEntity.manufacturerSubEntityId === -1"
            >
                <div class="heading mb-0">
                    <h3><i class="fas fa-clipboard-list"></i> {{'DASHBOARD.MOST_ACTIVE_USER_ORDER' | translate}}</h3>
                </div>
                <div class="body text-left">
                    <table>
                        <tbody>
                            <tr ng-repeat="customer in dashboardCtrl.highestOrderValueCustomers">
                                <td>{{$index + 1}}</td>
                                <td><a href="" ng-click="dashboardCtrl.highestOrderSubEntityClicked($index)">{{customer.name}}</a></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </section>

    <section class="dashboard">
        <div class="panel col-md-12 flex-column">
            <div class="panel full col-md-12 flex-column">
                <div class="heading">
                    <h3><i class="fas fa-clipboard-list"></i> {{'DASHBOARD.ORDER_VALUE' | translate}}</h3>
                </div>

                <div>
                    <div class="dropdown-holder">
                        <h3 class="mb-0">{{'DASHBOARD.USERS' | translate}}</h3>
                        <select
                            ng-options="manufacturerSubEntity as manufacturerSubEntity.name for manufacturerSubEntity in dashboardCtrl.manufacturerSubEntities | orderBy:dashboardCtrl.sortAndPin"
                            ng-model="dashboardCtrl.chartSelectedSubEntity"
                            ng-change="dashboardCtrl.orderValueChartUpdate()"
                            class="dashboard-select"
                        ></select>
                    </div>
                    <div class="dropdown-holder">
                        <h3 class="mb-0">{{'DASHBOARD.TIME_PERIOD' | translate}}</h3>
                        <select
                            ng-options="timePeriod as timePeriod.label for timePeriod in dashboardCtrl.chartTimePeriods"
                            ng-model="dashboardCtrl.chartSelectedTimePeriod"
                            ng-change="dashboardCtrl.orderValueChartUpdate()"
                            class="dashboard-select"
                        ></select>
                    </div>
                    <div class="panel bar-chart">
                        <canvas id="orderValueChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </section>
</section>
