describe('CreateOptionsSetController', function () {

    beforeEach(module('cadshareApp'));

    var completePart = {partDescription: "One part", partNumber: "123-124"};
    var missingDescriptionPart = {partDescription: "", partNumber: "123-124"};
    var anotherCompletePart = {partDescription: "Two part", partNumber: "123-125"};
    var missingNamePart = {partDescription: "Two part", partNumber: ""};

    var $controller, $scope, controller;

    beforeEach(inject(function (_$controller_, $rootScope) {
        // The injector unwraps the underscores (_) from around the parameter names when matching
        $controller = _$controller_;
        $scope = $rootScope.$new();
        controller = $controller('CreateOptionsSetController',
            {
                $scope: $scope
            });
    }));

    describe('saveOptionsSet', function () {

        beforeEach(function () {
            controller.errors = {};
        });

        it('Errors if there are less than 2 options', function () {
            controller.optionsSet = [completePart];
            controller.saveOptionsSet();
            expect(controller.errors.notEnoughParts).toBeTruthy();
        });

        it('Returns false if partDescription is empty', function () {
            controller.optionsSet = [completePart, missingDescriptionPart];
            controller.saveOptionsSet();
            expect(controller.errors.notEnoughParts).toBeTruthy();
        });

        it('Returns false if partNumber is empty', function () {
            controller.optionsSet = [completePart, missingNamePart];
            controller.saveOptionsSet();
            expect(controller.errors.notEnoughParts).toBeTruthy();
        });

        it('No error if there are 2 parts fully completed', function () {
            controller.optionsSet = [completePart, anotherCompletePart];
            controller.saveOptionsSet();
            expect(controller.errors.notEnoughParts).toBeFalsy();
        });
    });

    describe('cancel', function () {

        it('Cancel to reset all values', function () {
            controller.optionsSet = [completePart, anotherCompletePart];
            controller.description = "This is a description";
            controller.isOpen = true;
            controller.cancel();
            expect(controller.optionsSet).toEqual([]);
            expect(controller.description).toEqual("");
            expect(controller.isOpen).toBeFalsy([]);
        });

    });

    describe('removeOption', function () {

        beforeEach(function(){
           controller.optionsSet = [];
        });

        it('should remove a part from the list when called', function () {
            controller.optionsSet = [completePart, anotherCompletePart, missingNamePart];
            controller.removeOption(2);
            expect(controller.optionsSet.length).toEqual(2);
        });

        it('should create a new option if there are less than 2 after delete', function () {
            controller.optionsSet = [completePart, anotherCompletePart];
            controller.removeOption(0);
            expect(controller.optionsSet.length).toEqual(2);
        });

        it('should should remove the correct option', function () {
            controller.optionsSet = [completePart, missingDescriptionPart, anotherCompletePart];
            controller.removeOption(1);
            expect(controller.optionsSet.length).toEqual(2);
            expect(controller.optionsSet[0]).toEqual(completePart);
            expect(controller.optionsSet[1]).toEqual(anotherCompletePart);
        });

    });

    describe('addAnotherOption', function () {

        it('should add another part', function () {
            controller.optionsSet = [completePart, anotherCompletePart];
            controller.addAnotherOption();
            expect(controller.optionsSet.length).toEqual(3);
            expect(controller.optionsSet[2]).toEqual({partDescription: "", partNumber: ""});
        });

    });

    //TODO: Need to add tests for $scope.$on methods
    //TODO: Need to add tests for initialization (and in debug)
});