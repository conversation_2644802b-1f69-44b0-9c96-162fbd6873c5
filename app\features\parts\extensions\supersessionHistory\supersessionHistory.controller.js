(function () {
    'use strict';

    angular
        .module('app.parts')
        .controller('SupersessionHistoryController', SupersessionHistoryController);

    SupersessionHistoryController.$inject = ['$uibModalInstance', 'masterPartService', 'userService', '$stateParams', 'masterPartId', 'partNumber', '$timeout'];

    function SupersessionHistoryController($uibModalInstance, masterPartService, userService, $stateParams, masterPartId, partNumber, $timeout) {
        var vm = this;
        vm.$onInit = onInit;

        vm.cancel = $uibModalInstance.dismiss;
        vm.selectedPart = null;
        vm.masterPartId = masterPartId;
        vm.partNumber = partNumber;

        function onInit() {
            setCurrency();
        }

        vm.sortReverse = false;
        vm.part_sort = 'partNumber';
        vm.searchBy = 'partNumber';
        vm.searching = false;
        vm.showSupersededToggle = false;
        vm.showSuperseded = true;
        vm.masterParts = [];
        vm.isPreviewStockLevelEnabled = userService.getPreviewStockLevelEnabled();
        vm.previewPricingEnabled = userService.getPreviewPricingEnabled();
        vm.isSearchOnlyPartNumbers = userService.getSearchOnlyPartNumbers();
        vm.isDealerPlusCustomer = userService.isManufacturerSubEntity() && userService.isDealerPlusCustomer();
        vm.isStockWarehousesEnabled = userService.getStockWarehousesEnabled();

        vm.search = search;
        vm.selectPart = selectPart;
        vm.addPart = addPart;
        vm.quantityUpdated = quantityUpdated;

        initialize();

        function initialize() { }

        function selectPart(part) {
            vm.selectedPart = part;
            vm.hasError = false;
            vm.errorMessage = '';
        }

        function addPart() {
            if (vm.selectedPart) {
                var manufacturerId = userService.getManufacturerId();
                var supersedingMasterPartId = vm.selectedPart.masterPartId;
                vm.processing = true;

                masterPartService.supersedePart(manufacturerId, vm.masterPartId, supersedingMasterPartId).then(
                    function () {
                        console.log("Supersession part number updated successfully for part:", vm.selectedPart);
                        $uibModalInstance.close(vm.selectedPart);
                    },
                    function (error) {
                        $timeout(function () {
                            console.error("Error updating supersession part number for part:", vm.selectedPart, error);
                            vm.hasError = true;
                            vm.errorMessage = error.data.error || "An unexpected error occurred.";
                            $timeout(function () {
                                vm.hasError = false;
                                vm.errorMessage = '';
                            }, 7000);
                        });
                    }
                ).finally(function () {
                    vm.processing = false;
                });
            } else {
                console.error("No part selected to supersede.");
            }
        }

        function search() {
            var onBehalfOf =
                $stateParams.onBehalfOf && $stateParams.onBehalfOf !== "null"
                    ? JSON.parse(decodeURIComponent(atob($stateParams.onBehalfOf)))
                    : undefined;

            console.log("is on behalf of: ", onBehalfOf);

            var manufacturerId = userService.getManufacturerId();
            var onBehalfOfUserId = userService.getOnBehalfOfUserId();

            vm.masterParts = [];
            vm.resultsReturned = false;
            vm.searching = true;

            var partNumber = vm.searchBy === 'partNumber' ? vm.searchValue : null;
            var partDescription = vm.searchBy === 'partDescription' ? vm.searchValue : null;

            if (manufacturerId !== null && manufacturerId !== undefined) {
                masterPartService.manufacturerPartSearch(manufacturerId, partDescription, partNumber).then(searchSuccess, searchFailed);
            }
        }

        function searchSuccess(resp) {
            vm.masterParts = resp.data.masterParts !== null ? resp.data.masterParts : [];
            vm.searching = false;
            vm.resultsReturned = true;
            setDefaultMasterPartsQuantity();
            disableMatchingParts();

            vm.showSupersededToggle = vm.masterParts.some(function (part) {
                return part.insupersession === true;
            });
        }

        function disableMatchingParts() {
            vm.masterParts.forEach(function (part) {
                part.disabled = part.partNumber === vm.partNumber || vm.masterParts.some(function (p) {
                    return p.partNumber === part.partNumber && p !== part;
                });
            });
        }

        function setDefaultMasterPartsQuantity() {
            for (var i = 0; i < vm.masterParts.length; i++) {
                vm.masterParts[i].quantity = 1;
            }
        }

        function searchFailed() {
            vm.hasSearchError = true;
            vm.searching = false;
        }

        function quantityUpdated() {
            for (var i = 0; i < vm.masterParts.length; i++) {
                vm.masterParts[i].totalPrice = vm.masterParts[i].price * vm.masterParts[i].quantity;
            }
        }

        function setCurrency() {
            vm.defaultCurrency = userService.getDefaultCurrency();
            if (userService.isOnBehalfOf()) {
                userService.getCurrencyForUser(userService.getOnBehalfOfUserId(), isDealerPlus)
                    .then(function (response) {
                        vm.defaultCurrency = response.data;
                    })
            }
        }
    }
})();
