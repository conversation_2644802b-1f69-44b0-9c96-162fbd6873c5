(function () {
    'use strict';

    angular
        .module('app.customer')
        .controller('DPCustomerUserController', DPCustomerUserController);

    DPCustomerUserController.$inject = ['$uibModal', '$state', '$stateParams', 'headerBannerService', 'dpUserService', 'apiConstants', '$translate', 'userService'];

    function DPCustomerUserController($uibModal, $state, $stateParams, headerBannerService, dpUserService, apiConstants, $translate, userService) {

        var vm = this;

        vm.isCDEUser = false;
        vm.sortReverse = false;
        vm.endRecord = vm.itemPerPage;
        vm.areUsersLoaded = false;
        vm.successMessage = "";
        vm.filter_range = "";
        vm.ranges = [];
        vm.subEntityname = $stateParams.name;
        vm.sortReverse = true;
        vm.customer_sort = 'userId';

        var SURNAME, EMAIL, USER_, SEND_RESET, WILL_BE_EMAILED, BY_CONFIRMING;
        $translate(['CUST_USER.SURNAME', 'CUST_USER.EMAIL', 'CUST_USER.USER_', 'CUST_USER.SEND_RESET', 'CUST_USER.WILL_BE_EMAILED', 'CUST_USER.BY_CONFIRMING'])
            .then(function (resp) {
                SURNAME = resp["CUST_USER.SURNAME"];
                EMAIL = resp["CUST_USER.EMAIL"];
                USER_ = resp["CUST_USER.USER_"];
                SEND_RESET = resp["CUST_USER.SEND_RESET"];
                WILL_BE_EMAILED = resp["CUST_USER.WILL_BE_EMAILED"];
                BY_CONFIRMING = resp["CUST_USER.BY_CONFIRMING"];

                vm.sortBy = [
                    {name: 'lastname', value: SURNAME},
                    {name: 'emailaddress', value: EMAIL}
                ];
            });

        vm.deleteUser = deleteUser;
        vm.resetPassword = resetPassword;
        vm.editUser = editUser;
        vm.createUser = createUser;
        vm.createOrder = createOrder;
        vm.goToCustomers = goToCustomers;

        var selectedUser = {};

        initialize();

        function initialize() {
            isUserCDE();
            fetchUsers();
        }

        function isUserCDE() {
            var cdeId = 0
            if (apiConstants.cdeId)
            {
                cdeId = apiConstants.cdeId;
            }
            vm.isCDEUser = userService.getManufacturerId() === cdeId;
        }

        function fetchUsers() {
            dpUserService.getCustomerUsers($stateParams.subEntityId)
                .then(getCustomerUsersSuccess, getCustomerUsersFailed)
        }

        function getCustomerUsersSuccess(response) {
            vm.userList = response.data;
            vm.totalItems = vm.userList.length;
            vm.areUsersLoaded = true;
        }

        function getCustomerUsersFailed(error) {
            vm.areUsersLoaded = false;
            headerBannerService.setNotification('ERROR', error.data.error, 10000);
        }

        function deleteUser(user) {
            vm.successMessage = "";
            var deleteObject = {
                name: USER_ + user.firstName + " " + user.lastName,
                id: user.userId,
                url: '/dealerplus/user/' + user.userId + '/customer'
            };

            $uibModal.open({
                templateUrl: 'features/shared/commonDelete/deleteDialogBox.html',
                controller: 'DeleteController',
                controllerAs: 'deleteCtrl',
                size: 'sm',
                resolve: {
                    deleteObject: function () {
                        return deleteObject;
                    }
                }
            }).result.then(function () {
                fetchUsers();
            }, function () {
                console.log('Modal Cancelled');
            });
        }

        function resetPassword(user) {
            selectedUser = user;
            var confirmObject = {
                titleText: SEND_RESET,
                bodyText: BY_CONFIRMING + " \"" + user.firstName + " " + user.lastName + "\" " + WILL_BE_EMAILED
            };
            $uibModal.open({
                templateUrl: 'features/shared/confirmationModal/confirmModal.html',
                controller: 'ConfirmModalController',
                controllerAs: 'confirmModalCtrl',
                size: 'sm',
                resolve: {
                    confirmObject: function () {
                        return confirmObject;
                    }
                }
            }).result
                .then(sendPasswordEmailConfirmed, doNothing);
        }

        function sendPasswordEmailConfirmed() {
            dpUserService.sendResetPassword(selectedUser.userId);
        }

        function doNothing() {
            //do nothing
        }

        function editUser(user) {
            var createObject = {
                type: $stateParams.type,
                permissionsArray: user.userPermissions,
                firstName: user.firstName,
                lastName: user.lastName,
                emailAddress: user.emailAddress,
                userId: user.userId,
                active: user.active,
                userStatus: user.userStatus,
                manufactuerSubEntityId: $stateParams.subEntityId
            };
            $uibModal.open({
                templateUrl: 'features/shared/editUserModal/editUser.html',
                controller: 'EditUserController',
                controllerAs: 'editUserCtrl',
                size: 'md',
                backdrop: 'static',
                resolve: {
                    createObject: function () {
                        return createObject;
                    }
                }
            })
                .result.then(function () {
                $state.reload()
            }, function () {
                console.log('Modal Cancelled');
            });
        }

        function createUser() {
            var createObject = {type: $stateParams.type, manufactuerSubEntityId: $stateParams.subEntityId};

            $uibModal.open({
                templateUrl: 'features/shared/createUserModal/createUser.html',
                controller: 'CreateUserController',
                controllerAs: 'createUserCtrl',
                size: 'md',
                backdrop: 'static',
                resolve: {
                    createObject: function () {
                        return createObject;
                    }
                }
            })
                .result.then(function () {
                $state.reload()
            }, function () {
                console.log('Modal Cancelled');
            });
        }

        function createOrder(user) {
            var onBehalfOf = user;
            onBehalfOf.manufacturerSubEntityId = $stateParams.subEntityId;
            onBehalfOf.currency = dpUserService.getUserCurrency(user.userId);
            $state.go("publishedProductsOnBehalfOf", {
                onBehalfOf: btoa(encodeURIComponent(JSON.stringify(onBehalfOf))),
            });
        }

        function goToCustomers() {
            $state.go('dpCustomers');
        }

    }
})();
