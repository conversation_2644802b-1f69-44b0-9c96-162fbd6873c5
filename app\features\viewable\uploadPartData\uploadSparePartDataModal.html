<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" ng-click="uploadSparePartDataCtrl.cancel()" aria-label="Close">
        <i class="fa fa-close" aria-hidden="true"></i>
    </button>
    <h2 class="modal-title" translate>UPLOAD_SPARE_PART.TITLE</h2>
</div>

<div class="modal-body">
    <form name="uploadDetailsForm" class="form">
        <p translate>UPLOAD_PART.UPLOAD_CSV</p>

        <div class="upload-box">
            <div>
                <i class="fa fa-upload"></i>
                <h4 class="file-uploader" translate>UPLOAD_PART.CHOOSE_FILE</h4>
                <input
                    type="file"
                    class="fileupload"
                    ng-click="$event = $event"
                    onchange="angular.element(this).scope().uploadSparePartDataCtrl.fileChanged(event)"
                    accept=".csv"
                />
            </div>
        </div>
        <p><emphasis>{{uploadSparePartDataCtrl.filename}}</emphasis></p>

        <p ng-show="uploadSparePartDataCtrl.isFileSelected && !uploadSparePartDataCtrl.isValidFile" class="error-message" translate>
            UPLOAD_PART.INVALID_FILE
        </p>

        <div class="modal-actions">
            <a class="btn small secondary" ng-click="uploadSparePartDataCtrl.cancel()" translate>GENERAL.CANCEL</a>
            <button
                type="button"
                class="btn small primary"
                ng-click="uploadSparePartDataCtrl.uploadDetails()"
                ng-disabled="!uploadSparePartDataCtrl.isValidFile"
                translate
            >
                UPLOAD_PART.SUBMIT
            </button>
        </div>
    </form>
</div>

<div class="loader modal-loader" ng-show="uploadSparePartDataCtrl.loading2">
    <div class="vertical-align loader modal-loader" id="loader">
        <div id="loader-text">
            <i class="fas fa-sync-alt fa-spin"></i>
            <p>{{"UPLOAD_PART.UPLOADING" | translate}}&hellip;</p>
            <p translate>UPLOAD_PART.FEW_MINS</p>
        </div>
    </div>
</div>
