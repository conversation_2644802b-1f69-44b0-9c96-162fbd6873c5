<div id="softCopyViewerIdentifier">
  <div class="" ng-show="softCopyViewerCtrl.showCreateNewPart"></div>

  <div class="vertical-align loader" id="loader">
    <div id="loader-text">
      <i class="fas fa-sync-alt fa-spin"></i>
      <p class="loader-info">Loading Viewer Asset&hellip;</p>
      <p>This may take a few minutes, please do not refresh</p>
    </div>
  </div>
  <header class="site-header viewer-header">
    <a href="" class="back-to-products" ng-click="softCopyViewerCtrl.backToSoftCopy()">
      <div class="viewer-back-arrow">
        <i class="fa fa-chevron-left"></i>
      </div>
      <h3 class="text-overflow">
        {{softCopyViewerCtrl.machineName}}- {{softCopyViewerCtrl.modelName}} - {{softCopyViewerCtrl.viewableName}}<br/>
        <small>Back to Viewables</small>
      </h3>
    </a>

  </header>

  <div class="product-viewer vertical">
    <viewer-banner></viewer-banner>

    <div class="softcopy-explode-tool-container">

      <div class="softExplodeRangeInput" ng-show="softCopyViewerCtrl.isExplodeSliderVisible">
        <input type="range" id="explode_range" value="0" min="0" max="100" step="10" data-show-value="true">
      </div>
    </div>

    <div class="viewerMessage" ng-show="softCopyViewerCtrl.viewerMessage !== ''"><h2>
      {{softCopyViewerCtrl.viewerMessage}}</h2>
    </div>

    <div class="softCopyViewer" id="MyViewerDiv"></div>

    <div id="softCopyTable" class="softCopyTable">

      <h3 ng-click="softCopyViewerCtrl.toggleKitsVisibility()" class="clickable">Kits
        <a href="" class="float-right" ng-show="softCopyViewerCtrl.isKitsVisible"><i class="fas fa-chevron-up"></i></a>
        <a href="" class="float-right" ng-hide="softCopyViewerCtrl.isKitsVisible"><i class="fas fa-chevron-down"></i></a>
      </h3>
      <table ng-show="softCopyViewerCtrl.isKitsVisible" class="softcopy-table">
        <thead class="width-100">
        <th class="width-10">Kit</th>
        <th class="width-20">Kit Title</th>
        <th class="width-70">Kit Description</th>
        <th class="text-right width-5"></th>
        </thead>
        <tbody ng-repeat="kit in softCopyViewerCtrl.kitTable">
        <td>{{kit.label}}</td>
        <td>{{kit.title}}</td>
        <td>{{kit.description}}</td>
        <td></td>
        </tbody>
      </table>

      <h3 ng-click="softCopyViewerCtrl.togglePartsVisibility()" class="clickable">Part List
        <a href="" class="float-right" ng-show="softCopyViewerCtrl.isPartsVisible"><i class="fas fa-chevron-up"></i></a>
        <a href="" class="float-right" ng-hide="softCopyViewerCtrl.isPartsVisible"><i class="fas fa-chevron-down"></i></a>
      </h3>
      <table ng-show="softCopyViewerCtrl.isPartsVisible" class="softcopy-table">
        <thead>
        <th class="width-10">Item</th>
        <th class="width-15">Included in Kit</th>
        <th class="width-15">Part Number</th>
        <th class="width-45">Part Description</th>
        <th class="text-right width-5">Remove</th>
        </thead>
        <tbody ng-repeat="leader in softCopyViewerCtrl.leaderTable | filter:softCopyViewerCtrl.notNull track by leader.objectId ">
        <td>{{$index + 1}}</td>
        <td>{{leader.kits}}</td>
        <td>{{leader.partNumber}}</td>
        <td>{{leader.partDescription}}</td>
        <td class="text-right"><a href="" ng-click="softCopyViewerCtrl.deleteLeaderFromTable(leader.objectId)" class="delete"><i class="fa fa-fw fa-trash"></i></a></td>
        </tbody>
      </table>
      <!--</div>-->
    </div>

  </div>

  <soft-side-menu></soft-side-menu>

  <div class="product-thumbnails-carousel-wrap vertical manufacturer">
    <div class="product-thumbnails-carousel">
      <div class="take-snapshot-container">
        <button class="btn btn-block primary snapshot"
                ng-click="softCopyViewerCtrl.saveSnapshot()">
          <i class="fa fa-camera" aria-hidden="true"></i> &nbsp;
          Add New Snapshot
        </button>
      </div>

      <div class="vertical-align" ng-repeat="step in softCopyViewerCtrl.steps track by step.stateId"
           ng-class="($last) ? (softCopyViewerCtrl.currentList < 1  ? 'product-thumb-step-selected-last' : 'product-thumb-step-selected') : 'product-thumb-step'"

           ng-click="softCopyViewerCtrl.loadSnapshot(step.stateId)">

        <img ng-src="{{step.imgUrl}}" width="36" height="36"/>
        <span class="text-area">{{step.stateName}}</span>
        <span ng-show="$last" class="overwrite-button" ng-click="softCopyViewerCtrl.overwriteSnapshot($event)">
                  <i class="fa fa-refresh"></i>
                </span>
      </div>

      <div class="product-thumb-cell"
           ng-repeat="snapshot in softCopyViewerCtrl.currentList track by snapshot.stateId">

        <div class="img-wrap" style="background-image:url({{snapshot.imgUrl}});"
             ng-click="softCopyViewerCtrl.loadSnapshot(snapshot.stateId)"></div>

        <div class="product-info">
          <h4 class="mouse-pointer full-width">
            {{snapshot.stateName}}
          </h4>

          <div class="full-width pt-1">

            <i class="fa fa-pencil pull-left" ng-click="softCopyViewerCtrl.editSnapshotName(snapshot.stateId)"></i>
            <i class="fa fa-trash pull-right" ng-click="softCopyViewerCtrl.deleteSnapshot(snapshot.stateId)"></i>
            </li>

          </div>
        </div>

      </div>
    </div>


  </div>

</div>
