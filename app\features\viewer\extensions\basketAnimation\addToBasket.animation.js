(function () {
    'use strict';


    angular
        .module('app.viewer')
        .animation('.basket-add-animation', function ($timeout) {
            return {
                enter: function (element, done) {
                    element.addClass('just-added');
                    $timeout(function () {
                        element.removeClass('just-added');
                        done();
                    }, 1000);
                },
                move: function (element, done) {
                    element.addClass('just-added');
                    $timeout(function () {
                        element.removeClass('just-added');
                        done();
                    }, 1000);
                }
            };
        });
})();