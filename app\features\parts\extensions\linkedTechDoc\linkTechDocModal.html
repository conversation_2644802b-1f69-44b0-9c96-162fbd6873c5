<div class="modal-header">
  <button type="button" class="close" data-dismiss="modal" ng-click="techDocCtrl.cancel()" aria-label="Close"><i class="fa fa-close" aria-hidden="true"></i></button>
  <h2 class="modal-title" translate>LINK_TECH_DOC.TITLE</h2>
</div>

<div class="modal-body">
  <form class="form" name="createCompanyForm">
      <h3 class="modal-head">{{"LINK_TECH_DOC.LINKED_TECHS_ARE" | translate}}: {{techDocCtrl.partNumber}}</h3>
      <table class="table table-bordered">
        <thead>
        <tr>
          <th translate>LINK_TECH_DOC.NAME</th>
          <th translate>LINK_TECH_DOC.DESC</th>
		    <th translate>LINK_TECH_DOC.CREATED_DATE</th>
          <th translate>LINK_TECH_DOC.PAGE</th>
          <th translate>LINK_TECH_DOC.LINKED</th>
        </tr>
        </thead>

        <tbody>

        <tr ng-repeat="techDoc in techDocCtrl.techDocs">
          <td class="disableWordBreak" data-label="{{'LINK_TECH_DOC.NAME' | translate}}">{{techDoc.name}}</td>
          <td data-label="{{'LINK_TECH_DOC.DESC' | translate}}">{{techDoc.description}}</td>
		    <td data-label="{{'LINK_TECH_DOC.CREATED_DATE' | translate}}">{{techDoc.createdDate | date: "d MMM y"}}</td>
          <td data-label="{{'LINK_TECH_DOC.PAGE' | translate}}" class="">

              <select class="custom_select" ng-options="pageNum for pageNum in techDoc.pageRange" ng-model="techDoc.linkedPage" required>
                <option value="" disabled selected>Select..</option>
              </select>

          </td>

          <td data-label="{{'LINK_TECH_DOC.LINKED' | translate}}">
            <input type="checkbox" ng-model="techDoc.selected" class="checkbox">
          </td>

        </tr>

        </tbody>

      </table>

    <p class="modal-message" style="color: #ff0000" ng-if="techDocCtrl.hasError" translate>
      GENERAL.WENT_WRONG
    </p>

    <div class="modal-actions mt-3 mb-2">
      <a class="btn small secondary" href="" ng-click="techDocCtrl.cancel()" translate>GENERAL.CANCEL</a>
      <a class="btn small primary" href="" ng-click="techDocCtrl.save()" translate>LINK_TECH_DOC.SAVE</a>
    </div>

  </form>
</div>


