(function () {
    'use strict';

    angular
        .module('app.services')
        .factory('commentsService', commentsService);

    commentsService.$inject = ['$http', 'apiConstants', '$location', 'userService', '$state'];

    function commentsService($http, apiConstants, $location, userService, $state) {

        var dealerPlusPrefix = "/dealerplus";
        return {
            getComments: getComments,
            addMessage: addMessage,
            createCommentThread: createCommentThread
        };

        function isDealerPlusPage(){
            return userService.isDealerPlusUser() && $state.current.name.includes("customerOrders");
        }

        function getComments(threadId) {
            var url =  isDealerPlusPage() ? apiConstants.url + dealerPlusPrefix : apiConstants.url;
            return $http.get(url + '/commentthread/' + threadId);
        }

        function addMessage(threadId, message) {
            var emailData = {message: message};
            var siteUrl = $location.protocol() + '://' + $location.host();
            var config = {headers: {'Site-Url': siteUrl}};

            var url = isDealerPlusPage() ? apiConstants.url + dealerPlusPrefix : apiConstants.url;
            return $http.post(url + '/commentthread/' + threadId + '/message', emailData, config);
        }

        function createCommentThread(orderId, orderItemId) {
            var url = isDealerPlusPage() ? apiConstants.url + dealerPlusPrefix : apiConstants.url;
            return $http.post(url + '/commentthread/', {orderId: orderId, orderItemId: orderItemId});
        }

    }
})();
