(function () {
    'use strict';

    angular
        .module('app.parts')
        .controller('AddToKitController', AddToKitController);

    AddToKitController.$inject = ['addToKitObject', 'masterPartService', '$uibModalInstance', 'userService', '$timeout', '$translate'];

    function AddToKitController(addToKitObject, masterPartService, $uibModalInstance, userService, $timeout, $translate) {
        var vm = this;

        vm.masterPartId = addToKitObject.masterPartId;
        vm.partNumber = addToKitObject.partNumber;

        vm.addToKit = addToKit;
        vm.cancel = cancel;

        var WENT_WRONG;
        $translate(['GENERAL.WENT_WRONG'])
            .then(function (resp) {
                WENT_WRONG = resp["GENERAL.WENT_WRONG"];
            });

        initialize();

        function initialize() {
            masterPartService.getKitsForManufacturer(userService.getManufacturerId())
                .then(getKitsForManufacturerSuccess, serviceFailure);
        }

        function getKitsForManufacturerSuccess(response) {
            vm.kits = response.data;
            updateIsPartInKitFlag();
        }

        function serviceFailure(err) {
            console.log("Service error: " + err);
            vm.error = WENT_WRONG;
        }

        function addToKit(kit) {
            vm.error = null;
            kit.parts.push({masterPartId: vm.masterPartId, quantity: 1});
            kit.clicked = true;
            $timeout(function () {
                kit.clicked = false;
            }, 500);
            masterPartService.editKit(kit.title, kit.description, kit.parts, kit.id)
                .then(addToKItSuccess, serviceFailure);
        }

        function addToKItSuccess() {
            updateIsPartInKitFlag();
        }

        function updateIsPartInKitFlag() {
            for (var i = 0; i < vm.kits.length; i++) {
                if (_.findWhere(vm.kits[i].parts, {masterPartId: vm.masterPartId}) || _.findWhere(vm.kits[i].parts, {masterPartId: parseInt(vm.masterPartId)})) {
                    vm.kits[i].containsPart = true;
                } else {
                    vm.kits[i].containsPart = false;
                }

            }
        }

        function cancel() {
            $uibModalInstance.close()
        }
    }
})();
