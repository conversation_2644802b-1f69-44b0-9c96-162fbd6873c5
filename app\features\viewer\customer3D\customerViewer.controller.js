(function () {
  "use strict";

  angular
    .module("app.viewer")
    .controller("CustomerViewerController", CustomerViewerController);

  CustomerViewerController.$inject = [
    "viewerSettingsService",
    "viewerService",
    "$stateParams",
    "$scope",
    "$state",
    "$rootScope",
    "viewerHelperService",
    "$uibModal",
    "userService",
    "$timeout",
    "basketService",
    "viewerBannerService",
    "purchasableAssemblyService",
    "optionsSetService",
    "securityService",
    "$translate",
    "$window",
    "$document",
    "$location",
    "liveMeetingBannerService",
    "createMeetingService",
    "viewerVariablesService",
    "apiConstants",
    "shippingEngineService",
    "masterPartService"
  ];

  function CustomerViewerController(
    viewerSettingsService,
    viewerService,
    $stateParams,
    $scope,
    $state,
    $rootScope,
    viewerHelperService,
    $uibModal,
    userService,
    $timeout,
    basketService,
    viewerBannerService,
    purchasableAssemblyService,
    optionsSetService,
    securityService,
    $translate,
    $window,
    $document,
    $location,
    liveMeetingBannerService,
    createMeetingService,
    viewerVariablesService,
    apiConstants,
    shippingEngineService,
    masterPartService
  ) {
    var vm = this;
    var theViewer;
    var viewerApp;

    var STATE_DETAIL_TYPE = "SNAPSHOT";
    var CAROUSEL_POSITION = 1;

    viewerHelperService.showSpinner();

    vm.machineName = $stateParams.machineName;
    vm.viewableName = $stateParams.viewableName;
    vm.manualId = $stateParams.manualId;
    vm.manualPartsCount = 0;
    vm.previewPricingEnabled = userService.getPreviewPricingEnabled();
    vm.isPreviewStockLevelEnabled = userService.getPreviewStockLevelEnabled();
    setCurrency();
    vm.canHideIsolate = userService.getCanHideIsolate();
    vm.sharingViewer = null;
    vm.meetingGuid = $location.search().guid;
    vm.fullName = userService.getFullName();
    vm.getVisibleIds = getVisibleIds;
    vm.hasOrdersAccess = userService.hasOrderRole();
    vm.isStockWarehousesEnabled = userService.getStockWarehousesEnabled();
    vm.searchValue = "";
    vm.searchResults = [];
    vm.showSearchResults = false;
    vm.showNoResultsMessage = false;
    vm.selectedPartNumber = null;
    vm.isDropdownMinimized = false;
    vm.searchMode = "partNumber";

    vm.isSearchBoxActive = false;
    vm.previouslyIsolatedNodes = [];
    vm.updateLoadingIconPosition = updateLoadingIconPosition;
    vm.shortenNote = shortenNote;
    vm.onSearchInputChange = onSearchInputChange;
    vm.selectSearchResult = selectSearchResult;
    vm.toggleDropdownMinimized = toggleDropdownMinimized;
    vm._searchDropdownScrollTop = 0;
    vm.toggleSearchMode = toggleSearchMode;
    vm.togglePriceVisibility = togglePriceVisibility;

    viewerHelperService.setMachineName(vm.machineName);
    var parentId = "ROOT";
    var BASE_STEP = { name: vm.machineName, id: "ROOT" };
    viewerHelperService.setCurrentSelectedPart(0);
    var leafNodes = [];
    var currentSnapshot = {};
    var linkedPartModalOpen = false;
    var priorSelection = [];
    var viewerLoaded = false;
    var statesReturned = false;
    var assemblies = [];
    var customsSettingsAppliedFlag = false;
    var modelState = {};
    var selectedPartNumber = $stateParams.partNumber;
    var currentSupersessionRequest = null;

    var isEntitySelected = false;

    vm.isNotesExpanded = true;
    vm.hidePrice = false;

    var excludeViewportStateFilter = {
      cutplanes: true,
      explodeNodeIdsWithParent: true,
      extensionScale: true,
      fragPositionArray: true,
      nodeIdsSetsExplodeAsSingleEntity: true,
      objectSet: true,
      recentlyUpdatedFragIds: true,
      renderOptions: true,
      rotatedFragIds: true,
      seedURN: true,
      transforms: true,
      translatedFragIds: true,
      translatedNodeIds: true,
      viewport: false,
    }; //everything true except viewport to prevent zoom on hover.

    vm.showWatermark = false;
    vm.fullName = userService.getFullName();
    vm.emailAddress = userService.getEmailAddress();
    vm.showSaveSpinner = false;
    vm.canExplode = userService.getCanExplode();
    vm.snapshots = [];
    vm.accordionStates = {};
    vm.hasWeldmentsEnabled = false;
    vm.part = {
      partNumber: "",
      alternatePartNumber: "",
      description: "",
      weight: "",
      massUnit: "",
      quantity: 1,
    };
    vm.selectedPartNote = [];
    vm.currentList = [];
    vm.snapshots = [];
    vm.steps = [BASE_STEP];
    vm.isPreviewMode = userService.isManufacturer();
    vm.myModelId = $stateParams.modelId;
    vm.onBehalfOf =
      $state.params.onBehalfOf && $state.params.onBehalfOf !== "null"
        ? JSON.parse(decodeURIComponent(atob($stateParams.onBehalfOf)))
        : undefined;
    vm.isPreviewMode = userService.isManufacturer() && !vm.onBehalfOf;
    vm.isBasketOpen = false;
    vm.showNotes = false;
    vm.isNotesOpen = false;
    vm.containsOptionsSet = false;
    vm.nonModelledParts = false;
    vm.optionsSets = [];
    vm.viewerSettings = {};
    vm.supersededPartsArray = [];
    vm.optionSelected = false;
    vm.isAddBasketButtonClicked = false;
    vm.showSupersessionHistory = false;
    vm.showRotateOverlay = false;
    vm.fromWhereUsedModal =
      $window.sessionStorage.getItem("fromWhereUsedModal") === "true";
    $window.sessionStorage.removeItem("fromWhereUsedModal");

    vm.isVertical = true;

    var storedValue = localStorage.getItem("isVertical");
    if (storedValue !== null) {
      vm.isVertical = JSON.parse(storedValue);
    } else {
      localStorage.setItem("isVertical", vm.isVertical);
    }

    if (window.window.innerWidth > 992) {
      var storedValue = localStorage.getItem("isVertical");
      vm.isVertical = storedValue !== undefined ? JSON.parse(storedValue) : true;
    } else {
      vm.isVertical = true;
    }

    vm.isDealerPlusCustomer =
      userService.isManufacturerSubEntity() &&
      userService.isDealerPlusCustomer();

    vm.backToViewables = backToViewables;
    vm.goBackToPartSearch = goBackToPartSearch;
    vm.loadSnapshot = loadSnapshot;
    vm.goHome = goHome;
    vm.addToBasket = addToBasket;
    vm.goToCreateEnquiry = goToCreateEnquiry;
    vm.selectFullAssembly = selectFullAssembly;
    vm.quantityChanged = quantityChanged;
    vm.openBasket = openBasket;
    vm.closeBasket = closeBasket;
    vm.removePart = removePart;
    vm.removeKitItem = removeKitItem;
    vm.filterMasterPartKitId = filterMasterPartKitId;
    vm.filterNoMasterPartKitId = filterNoMasterPartKitId;
    vm.partUpdated = partUpdated;
    vm.kitUpdated = kitUpdated;
    vm.openNotes = openNotes;
    vm.closeNotes = closeNotes;
    vm.hide = hide;
    vm.isolate = isolate;
    vm.viewLinkedTechDocs = viewLinkedTechDocs;
    vm.remoteUpdateViewer = remoteUpdateViewer;
    vm.toggleCarouselDirection = toggleCarouselDirection;
    vm.closePartList = closePartList;
    vm.openPartList = openPartList;
    vm.showSpinner = showSpinner;
    vm.hideSpinner = hideSpinner;
    vm.calculateDiscountedPrice = calculateDiscountedPrice;
    vm.closeTab = closeTab;
    vm.hidePrice = hidePrice;
    vm.fetchSupersessionHistory = fetchSupersessionHistory;
    vm.toggleSupersessionHistory = toggleSupersessionHistory;

    vm.selectedOption = {};
    vm.updateOptionSelected = updateOptionSelected;
    vm.optionQuantityChanged = optionQuantityChanged;
    vm.addOptionToBasket = addOptionToBasket;
    vm.partSearchClick = partSearchClick;

    vm.clearPartSearch = clearPartSearch;
    vm.toggleKitsAccordion = toggleKitsAccordion;
    vm.isOptionInBasket = isOptionInBasket;
    vm.toggleOverlayBox = toggleOverlayBox;
    vm.showPartNotes = showPartNotes;
    vm.getDescription = getDescription;

    var modelTreeLoaded = false;
    var customWeldments = [];
    vm.weldmentsUpdated = weldmentsUpdated;
    vm.isManufacturer = userService.isManufacturer();

    let toolbar;
    var hideToolBtn;
    var isolateToolBtn;

    $scope.$on("Basket-Updated", function () {
      updateBasket();
      vm.isBasketOpen = true;
    });
    $scope.$on("$destroy", function () {
      shutDownViewer();
    });
    $scope.$on("loading-complete", function () {
      if (viewerLoaded && statesReturned) {
        Promise.resolve().then(() => {
          loadSnapshot("ROOT");
          viewerHelperService.hideSpinner();
          if ($stateParams.partNumber) {
            document.getElementById("partSearch").value =
              $stateParams.partNumber;
            setTimeout(() => {
              partSearchClick($stateParams.partNumber);
            }, 1000);
          }
        });
      }
    });

    var WENT_WRONG, CANT_ADD_PREVIEW, PART_SEARCH, ENTER_PART_NUMBER;
    $translate([
      "GENERAL.WENT_WRONG",
      "CUST_VIEWER.CANT_ADD_PREVIEW",
      "CUST_PART_SEARCH.PART_SEARCH",
      "CUST_PART_SEARCH.ENTER_PART_NUMBER",
    ]).then(function (resp) {
      WENT_WRONG = resp["GENERAL.WENT_WRONG"];
      CANT_ADD_PREVIEW = resp["CUST_VIEWER.CANT_ADD_PREVIEW"];
      PART_SEARCH = resp["CUST_PART_SEARCH.PART_SEARCH"];
      ENTER_PART_NUMBER = resp["CUST_PART_SEARCH.ENTER_PART_NUMBER"];
    });

    var options = viewerService.getAutodeskToken().then(function (resp) {
      options = resp;
      options.useADP = false;
      if ($stateParams.translateType === "SVF2") {
        options.env = "AutodeskProduction2";
        options.api = "streamingV2";
      }
      initialize();
    });

    function getInitialStateDetailsSuccess(data) {
      addReturnedStateDetails(data);
      statesReturned = true;
      $rootScope.$broadcast("loading-complete");
    }

    function getInitialStateDetails() {
      getStateDetailPlusChildren("ROOT").then(getInitialStateDetailsSuccess);
    }

    function findSnapshotByStateId(id) {
      return _.findWhere(vm.snapshots, { stateId: id });
    }

    function addReturnedStateDetails(stateDetails) {
      return new Promise((resolve, reject) => {
        if (stateDetails) {
          for (var i = 0; i < stateDetails.length; i++) {
            if (!findSnapshotByStateId(stateDetails[i].stateId)) {
              vm.snapshots.push(stateDetails[i]);
            }
          }
          refreshSnapshotList();

          resolve();
        }
      });
    }

    $rootScope.$on("viewer-part-selected", function (event, partViewerDetail) {
      if (partViewerDetail.part && partViewerDetail.part.note && typeof partViewerDetail.part.note === 'string') {
        vm.selectedPartNote = partViewerDetail.part.note.split('\n');
      }
    });

    function showPartNotes () {
      return (
        vm.part.note &&
        !vm.containsOptionsSet &&
        !vm.nonModelledParts
      );
    };

    function shortenNote(note) {
      if (!note) return '';
      return note.length > 50 ? note.substring(0, 50) + '...' : note;
    }

    function getStateDetailPlusChildren(stateId) {
      return viewerService.getStateDetails(
        $stateParams.modelId,
        stateId,
        1,
        STATE_DETAIL_TYPE
      );
    }

    function initialize() {
      vm.hasOrderRole = userService.hasOrderRole();
      updateBasket();
      autodeskViewerInitialize();
      getSupersededParts();
      getDefaultDiscount();
      hidePrice();
      initializeOverlayBoxes();

      $(function () {
        $('[data-toggle="tooltip"]').tooltip();
      });

      vm.showCloseButton =
        localStorage.getItem("navigatedFrom") === "customerViewer";
      localStorage.removeItem("navigatedFrom"); // Clear after checking

      viewerService
        .getViewableByModelId($stateParams.modelId)
        .then(getModelStateSuccess);

      purchasableAssemblyService
        .fetchPurchasableAssemblysForModel($stateParams.modelId)
        .then(getPurchasableAssembliesForModelSuccess);

      if (!userService.isManufacturer() && !vm.onBehalfOf) {
        securityService
          .getCustomerWatermarkSettings($stateParams.modelId)
          .then(getWatermarkSettingsSuccess);
      }

      window.addEventListener('resize', checkOrientation);
  window.addEventListener('orientationchange', checkOrientation);
  
  checkOrientation();

  $scope.$on('$destroy', function() {
    window.removeEventListener('resize', checkOrientation);
    window.removeEventListener('orientationchange', checkOrientation);
  });
    }

    function getPurchasableAssembliesForModelSuccess(response) {
      response.data.forEach(function (assembly) {
        assemblies.push(assembly.objectId);
      });
    }

    function getWatermarkSettingsSuccess(response) {
      vm.showWatermark = response.data.enabled;
      vm.watermarkImage = response.data.imageUrl;
      if (vm.showWatermark) {
        createWatermark();
      }
    }

    function getDefaultDiscount() {
      vm.manufacturerSubEntityId = userService.getManufacturerSubEntityId();
      userService
        .getManufacturerSubEntity(vm.manufacturerSubEntityId)
        .then(function (response) {
          vm.defaultDiscount = response.data.defaultDiscount;
        })
        .catch(function (error) {
          console.error("Error fetching discount:", error);
        });
    }

    function calculateDiscountedPrice() {
      if (vm.defaultDiscount && vm.part && vm.part.price) {
        var discountAmount = vm.part.price * (vm.defaultDiscount / 100);
        return vm.part.price - discountAmount;
      }
      return vm.part.price; // Return original price if no discount
    }

    function closeTab() {
      try {
        window.close();
        // After a short delay, check if the window is still open
        setTimeout(function () {
          if (!window.closed) {
            alert("Please close this tab manually.");
          }
        }, 200);
      } catch (e) {
        alert(
          "An error occurred while trying to close this tab. Please close it manually."
        );
      }
    }

    function createWatermark() {
      if (document.getElementById("watermark-image").complete) {
        $timeout(function () {
          for (var i = 0; i < 500; i++) {
            var parent = document.getElementById("watermark-container");
            var watermark = document.getElementById("watermark-tile");
            var newWatermark = watermark.cloneNode(true);
            parent.appendChild(newWatermark);
          }
        }, 500);
      } else {
        createWatermark();
      }
    }

    function openBasket() {
      vm.isBasketOpen = true;
      $rootScope.$broadcast("Hide-Non-Modeled-Parts");

      $timeout(function () {
        viewer.resize();
      });
    }

    function closeBasket() {
      vm.isBasketOpen = false;

      $timeout(function () {
        viewer.resize();
      });
    }

    function getModelStateSuccess(data) {
      if (data) {
        modelState = data;
        leafNodes = modelState.leafNodes || [];

        vm.viewerSettings.lineDrawingEnabled = data.lineDrawingEnabled;
        vm.viewerSettings.edgingEnabled = data.edgingEnabled;
        vm.viewerSettings.viewLocked = data.viewLocked;
      }
      getInitialStateDetails();
    }

    function autodeskViewerInitialize() {
      $scope.$on("$locationChangeStart", function (event) {
        if (!viewerLoaded) {
          event.preventDefault();
          console.log("Viewable loading back navigation disabled");
        }
      });

      Autodesk.Viewing.Initializer(options, function onInitialized() {
        var config3d = { extensions: ["CADShareExtension", "Autodesk.NPR"] };
        viewerApp = new Autodesk.Viewing.ViewingApplication("MyViewerDiv");
        viewerApp.registerViewer(
          viewerApp.k3D,
          Autodesk.Viewing.GuiViewer3D,
          config3d
        );

        var documentId = "urn:" + $stateParams.autodeskURN;
        viewerApp.loadDocument(documentId, onDocumentLoadSuccess, loadFailure);
      });
    }

    function onDocumentLoadSuccess(doc) {
      var viewables = viewerApp.bubble.search({ type: "geometry" });
      if (viewables.length === 0) {
        console.error("Document contains no viewables.");
        return;
      }
      viewerApp.selectItem(viewables[0].data);
      theViewer = viewerApp.getCurrentViewer();

      theViewer.addEventListener(
        Autodesk.Viewing.SELECTION_CHANGED_EVENT,
        onSelectionChanged
      );
      theViewer.addEventListener(
        Autodesk.Viewing.GEOMETRY_LOADED_EVENT,
        onViewerLoadedSuccess
      );
      viewerHelperService.setViewerApp(theViewer);
    }

    function loadFailure(viewerErrorCode) {
      console.log("onDocumentLoadFailure() - errorCode:" + viewerErrorCode);
      viewerHelperService.hideSpinner();
    }

    function getSnapshotViewport(snapshot) {
      var state = snapshot.state;
      return JSON.parse(state).viewport;
    }

    function viewportsAreEquivalent(currentViewport, snapshotViewport) {
      var currentViewportDefiningFields = {
        distanceToOrbit: currentViewport.distanceToOrbit,
        eye: currentViewport.eye,
        target: currentViewport.target,
      };

      var snapshotViewportDefiningFields = {
        distanceToOrbit: snapshotViewport.distanceToOrbit,
        eye: snapshotViewport.eye,
        target: snapshotViewport.target,
      };

      return _.isEqual(
        snapshotViewportDefiningFields,
        currentViewportDefiningFields
      );
    }

    let finalRenderSpinnerHide = null;
    function onFinalRender(event) {
      if (finalRenderSpinnerHide !== null) clearTimeout(finalRenderSpinnerHide);
      if (!customsSettingsAppliedFlag) {
        var currentViewport = theViewer.getState().viewport;

        if (
          viewportsAreEquivalent(
            currentViewport,
            getSnapshotViewport(currentSnapshot)
          )
        ) {
          viewerHelperService.applyCustomViewerSettings(
            theViewer,
            vm.viewerSettings
          );
          configureToolbar(viewer, vm.viewerSettings.viewLocked);
          customsSettingsAppliedFlag = true;
          theViewer.removeEventListener(
            Autodesk.Viewing.FINAL_FRAME_RENDERED_CHANGED_EVENT,
            onFinalRender
          );
        }
      }
      finalRenderSpinnerHide = setTimeout(function () {
        viewerHelperService.hideSpinner();
      }, 1000);
      if (event.value.finalFrame) {
        viewerHelperService.hideSpinner();
        clearTimeout(finalRenderSpinnerHide);
      }
    }

    function unsetViewLocked(viewer) {
      viewer.navigation.setIsLocked(false);
      viewer.navigation.setLockSettings({});
    }

    function configureToolbar(viewer, viewLocked) {
      var toolbar = new Autodesk.Viewing.UI.ToolBar("cadshare-toolbar");

      if (viewLocked) {
        var reverseViewBtn = new Autodesk.Viewing.UI.Button("reverse-view");
        reverseViewBtn.onClick = function () {
          flip180();
        };
        reverseViewBtn.addClass("reverse-view");
        reverseViewBtn.setToolTip("Reverse angle");

        var customerSubToolbar = new Autodesk.Viewing.UI.ControlGroup(
          "cadshare-customer-toolbar"
        );
        customerSubToolbar.addControl(reverseViewBtn);
        toolbar.addControl(customerSubToolbar, { index: 0 });

        var toolbarId = guid();

        var html = "<div id=" + toolbarId + "> </div>";

        $(viewer.container).append(html);

        var $toolbar = $("#" + toolbarId);

        $toolbar[0].appendChild(toolbar.container);
      }
    }

    function guid() {
      var d = new Date().getTime();

      var guid = "xxxx-xxxx-xxxx-xxxx".replace(/[xy]/g, function (c) {
        var r = (d + Math.random() * 16) % 16 | 0;
        d = Math.floor(d / 16);
        return (c == "x" ? r : (r & 0x7) | 0x8).toString(16);
      });

      return guid;
    }

    function initializeButtons() {
      var selection = theViewer.getSelection();

      if (selection.length === 0) {
        if (hideToolBtn) {
          hideToolBtn.container.classList.add("disabled-button");
        }
        if (isolateToolBtn) {
          isolateToolBtn.container.classList.add("disabled-button");
        }
      }
    }

    function onViewerLoadedSuccess(response) {
      var viewer = response.target;
      createToolbar(viewer);
      var bgColour = viewerHelperService.getBackgroundColour();
      viewer.setBackgroundColor(
        bgColour.r,
        bgColour.g,
        bgColour.b,
        bgColour.r,
        bgColour.g,
        bgColour.b
      );
      viewer._hotkeyManager.popHotkeys("Autodesk.Escape");
      viewer.setTheme("light-theme");
      viewer.setEnvMapBackground(false);
      viewer.setGroundReflection(false);
      viewer.setGroundShadow(false);
      viewer.setOptimizeNavigation(false);
      viewer.hidePoints(true);
      viewer.hideLines(true);
      viewer.setProgressiveRendering(true);
      viewer.setQualityLevel(false, false);
      viewer.setGhosting(false);
      viewer.impl.selectionMaterialTop.opacity = 0;
      viewer.toolController.activateTool("freeorbit");

      viewerHelperService.setIsTwoD(false);
      viewerHelperService.setShowBuyParts(false);
      $scope.$digest();

      //Start of CDE trsnparent part hotfix
      var cdeId = apiConstants.cdeId ? apiConstants.cdeId[0] : 0;
      console.log(apiConstants.cdeId[0]);
      if (userService.getManufacturerId() === cdeId) {
        var interval = setInterval(() => {
          var matman = NOP_VIEWER.impl.matman();
          if (matman !== undefined) {
            clearInterval(interval);
            // matman._materials["model:1|mat:5"].opacity = 1;
            Object.keys(matman._materials).forEach((key) => {
              matman._materials[key].transparent = false;
            });
            NOP_VIEWER.impl.invalidate(true, false, true);
          }
        }, 500);
      }
      //End of CDE trsnparent part hotfix

      viewerLoaded = true;
      $rootScope.$broadcast("loading-complete");

      initializeButtons();
    }

    function createToolbar(viewer) {
      const verticalToolbar = new Autodesk.Viewing.UI.ToolBar(
        "toolbar-vertical"
      );
      const verticalToolGroup = new Autodesk.Viewing.UI.ControlGroup(
        "Autodesk.Research.TtIf.Extension.Toolbar.ControlGroup"
      );

      verticalToolGroup.addClass("toolbar-vertical-group");
      if (vm.canHideIsolate) {
        addHideTool(verticalToolGroup);
        addIsolateTool(verticalToolGroup);
      }
      if (vm.canExplode) {
        addExplodeTool(verticalToolGroup);
      }
      addToggleCarouselTool(verticalToolGroup);

      verticalToolbar.addControl(verticalToolGroup);
      const toolbarDivHtml = '<div id="divToolbar"> </div>';
      $(viewer.container).append(toolbarDivHtml);

      verticalToolbar.addEventListener(
        Autodesk.Viewing.UI.ToolBar.Event.SIZE_CHANGED,
        verticalToolbar.centerToolBar
      );

      toolbar = verticalToolbar;
      appendToolbarToDiv(toolbar);
      updateToolbarStyles(toolbar);
    }

    function addHideTool(verticalToolGroup) {
      hideToolBtn = new Autodesk.Viewing.UI.Button("hide-tool");
      const iconDiv = createHideIconDiv();
      hideToolBtn.icon.style.display = "none";
      hideToolBtn.icon.parentNode.appendChild(iconDiv);
      hideToolBtn.container.appendChild(iconDiv);

      hideToolBtn.onClick = function (e) {
        hideExplodeSlider();
        hide();
      };

      hideToolBtn.setToolTip("Hide selection");
      verticalToolGroup.addControl(hideToolBtn);
    }

    function addIsolateTool(verticalToolGroup) {
      isolateToolBtn = new Autodesk.Viewing.UI.Button("isolate-tool");
      const iconDiv = createIsolateIconDiv();
      isolateToolBtn.icon.style.display = "none";
      isolateToolBtn.icon.parentNode.appendChild(iconDiv);
      isolateToolBtn.container.appendChild(iconDiv);

      isolateToolBtn.onClick = function (e) {
        hideExplodeSlider();
        isolate();
      };

      isolateToolBtn.setToolTip("Isolate selection");
      verticalToolGroup.addControl(isolateToolBtn);
    }

    function addExplodeTool(verticalToolGroup) {
      const explodeToolBtn = new Autodesk.Viewing.UI.Button("explode-tool");
      explodeToolBtn.onClick = function (e) {
        toggleExplodePartVisibility();
      };
      explodeToolBtn.addClass("adsk-icon-explode");
      explodeToolBtn.setToolTip("Explode parts");

      verticalToolGroup.addControl(explodeToolBtn);
    }

    function addToggleCarouselTool(verticalToolGroup) {
      const toggleCarouselBtn = new Autodesk.Viewing.UI.Button("toggle-carousel");
    
      const isSmallScreen = window.innerWidth <= 915;
    
      if (isSmallScreen) {
        toggleCarouselBtn.container.style.display = "none";
      } else {
        toggleCarouselBtn.container.style.fontSize = "25px";
        toggleCarouselBtn.addClass("fa");
        toggleCarouselBtn.addClass(
          vm.isVertical ? "fa-chevron-up" : "fa-chevron-right"
        );
        toggleCarouselBtn.onClick = function (e) {
          toggleCarouselDirection(toggleCarouselBtn);
        };
        toggleCarouselBtn.setToolTip("Toggle carousel position");
        verticalToolGroup.addControl(toggleCarouselBtn);
      }
    }
    

    function toggleCarouselDirection(toggleCarouselBtn) {
      vm.isVertical = !vm.isVertical;
      localStorage.setItem("isVertical", vm.isVertical);
      $timeout(enableDisableScrollButtons, 10);
      updateToolbarStyles(toolbar);
      $(toggleCarouselBtn.container).toggleClass(
        "fa-chevron-up fa-chevron-right"
      );
    }

    function createHideIconDiv() {
      const iconDiv = document.createElement("div");
      iconDiv.style.fontSize = "25px";
      iconDiv.classList.add("fas", "fa-eye-slash");
      return iconDiv;
    }

    function createIsolateIconDiv() {
      const iconDiv = document.createElement("div");
      iconDiv.style.fontSize = "27px";
      iconDiv.classList.add("fas", "fa-crosshairs");
      return iconDiv;
    }

    function appendToolbarToDiv(verticalToolbar) {
      $("#divToolbar").css(getToolbarStyles(verticalToolbar));
      $("#divToolbar")[0].appendChild(verticalToolbar.container);
      const customerExplodeToolContainer = $(
        ".customer-explode-tool-container"
      );
      $("#divToolbar").append(customerExplodeToolContainer);
    }
    
    function getToolbarStyles(toolbar) {
      var isSmallScreen = $window.innerWidth <= 768;
      var flexDirection = vm.isVertical ? "row" : "column";
      
      var styles = {
        "position": "fixed",
        "z-index": "100",
        "flex-direction": flexDirection,
        "justify-content": "center",
        "align-items": "center"
      };
      
      if (vm.isVertical) {
        styles.bottom = isSmallScreen ? "20px" : "8vh";
        styles.left = "45%";
        styles.transform = "translateX(-45%)";
      } else {
        styles.bottom = isSmallScreen ? "20px" : "35%";
        styles.left = "0";
        styles.transform = "translateY(35%)";
      }
      
      return styles;
    }
    
    function updateToolbarStyles(toolbar) {
      var styles = getToolbarStyles(toolbar);
      $("#divToolbar").css(styles);
      
      $("#divToolbar .toolbar-vertical-group").css({
        display: "inline-flex",
        "flex-direction": styles["flex-direction"]
      });
    
      if (!vm.isVertical) {
        $(".customer-explode-tool-container").css({
          bottom: "172px",
          "margin-left": "94px"
        });
      } else {
        $(".customer-explode-tool-container").css({
          bottom: "180px",
          "margin-left": "88px"
        });
      }
    }
    
    $(window).resize(function() {
      if (typeof updateToolbarStyles === "function" && toolbar) {
        updateToolbarStyles(toolbar);
      }
    });

    function showSpinner() {
      viewerHelperService.showSpinner();
    }

    function hideSpinner() {
      viewerHelperService.hideSpinner();
    }

    function transitionToSnapshotViewLockSettings() {
      customsSettingsAppliedFlag = false;
      unsetViewLocked(theViewer);
    }

    function loadSnapshot(id, isSharingEvent) {
      viewerHelperService.showSpinner();

      if (vm.sharingViewer !== null && !isSharingEvent) {
        GLOBAL_SHARING_EVENT("SNAPSHOT_CHANGED", id);
      }

      transitionToSnapshotViewLockSettings();
      var snapshot = findSnapshotById(id);
      currentSnapshot = snapshot;
      parentId = snapshot.stateId;

      getStateDetailPlusChildren(id).then(function (data) {
        theViewer.addEventListener(
          Autodesk.Viewing.FINAL_FRAME_RENDERED_CHANGED_EVENT,
          onFinalRender
        );
        localStorage.setItem("EXPLODE_AXIS", "RADIAL");
        if (snapshot.explodeAxis) {
          localStorage.setItem("EXPLODE_AXIS", snapshot.explodeAxis);
        }
        theViewer.restoreState(JSON.parse(snapshot.state), false);

        if (snapshot.visibleDbIds) {
          try {
            vm.activeSnapshotVisibleDbIds = JSON.parse(snapshot.visibleDbIds);
          } catch (e) {
            console.error("Error parsing snapshot.visibleDbIds:", e);
            vm.activeSnapshotVisibleDbIds = [];
          }
        } else {
          vm.activeSnapshotVisibleDbIds = []; // Clear if no visibleDbIds
        }
        showHideNodes(vm.activeSnapshotVisibleDbIds);

        addReturnedStateDetails(data).then(function () {
          vm.steps = [];
          updateSteps(id);
          vm.showNotes = snapshot.notes !== "" && snapshot.notes !== undefined;
          vm.isNotesOpen = vm.showNotes;
          vm.notes = snapshot.notes;

          CAROUSEL_POSITION = 1;
          backToZero();

          if (vm.searchValue && vm.searchValue.length > 0) {
            onSearchInputChange();
          }
        });
      });
    }

    function updateSteps(id) {
      var snapshot = findSnapshotById(id);
      if (id !== "ROOT") {
        vm.steps.unshift(snapshot);
        updateSteps(snapshot.parentId);
      } else {
        snapshot.name = "Reset to Top Level";
        vm.steps.unshift(snapshot);
      }
    }

    function refreshSnapshotList() {
      vm.currentList = findChildSnapshots(parentId);
    }

    function showHideNodes(visibleDbIds) {
      if (visibleDbIds && visibleDbIds.length > 0) {
        viewerHelperService.hideParts(viewerHelperService.getPartTreeTopId);
        viewerHelperService.showParts(visibleDbIds);
      } else {
        vm.activeSnapshotVisibleDbIds = [];
        theViewer.showAll();
      }
    }

    function flip180() {
      var bbox = viewer.impl.getVisibleBounds(false, false);
      var pivot = bbox.center();
      var oldTarget = viewer.navigation.getTarget();
      var oldPosition = viewer.navigation.getPosition();

      var newPosition = {
        x: oldPosition.x + 2.0 * (pivot.x - oldPosition.x),
        y: oldPosition.y + 2.0 * (pivot.y - oldPosition.y),
        z: oldPosition.z + 2.0 * (pivot.z - oldPosition.z),
      };

      var newTarget = {
        x: oldTarget.x + 2.0 * (pivot.x - oldTarget.x),
        y: oldTarget.y + 2.0 * (pivot.y - oldTarget.y),
        z: oldTarget.z + 2.0 * (pivot.z - oldTarget.z),
      };

      viewer.navigation.setView(newPosition, newTarget);
    }

    function goHome() {
      theViewer.navigation.setRequestHomeView(true);
      theViewer.showAll();
      currentSnapshot = {};
      $rootScope.$broadcast("reset-viewer-clicked");
    }

    function findSparePart(objectId, treeData) {
      let obj = findObjectById(objectId, treeData);
      if (!obj) {
        return null;
      }

      if (obj.sparePart) {
        return objectId;
      } else {
        if (obj.parentObjectId !== -1) {
          return findSparePart(obj.parentObjectId, treeData);
        } else {
          return 1; // Top level parent // it should be null, specified 1 for testing purpose
        }
      }
    }

    function findObjectById(objectId, treeData) {
      for (let obj of treeData) {
        if (obj.objectId === objectId) {
          return obj;
        }

        if (obj.childParts && obj.childParts.length) {
          let result = findObjectById(objectId, obj.childParts);
          if (result) return result;
        }
      }
      return null;
    }

    var DOING_AGG_EVENT = false;

    //Called when a selection is changed in the viewer
    function onSelectionChanged(data) {
      var selection = theViewer.getSelection();
      vm.isEntitySelected = selection.length > 0;

      toggleToolButtonsState(hideToolBtn);
      toggleToolButtonsState(isolateToolBtn);

      if (selection.length === 0) {
        viewer.clearSelection();
        resetPartDetails();
      } else {
        
        var isLandscapeSmallScreen = $window.innerWidth <= 992 && $window.innerHeight < $window.innerWidth;
        if (isLandscapeSmallScreen && vm.isBasketBoxActive) {
          vm.isBasketBoxActive = false;
        }
      }

      $timeout(() => $scope.$apply());

      // select current node if that is sellable
      var id = data.dbIdArray[0];
      var x = [];
      while (x.length === 0 && id !== 0 && id !== undefined) {
        findEntitiesBydbId(vm.modelTree, id, x);
        id = viewerHelperService.getParentId(id);
      }
      if (x.length > 0 && x[0].sellablePart && x[0].sparePart) {
        handleValidSelection([x[0].objectId], data);
      } else {
        handleSelectionPart(data);

        if (DOING_AGG_EVENT) return true;
        handleAggregateEvent(data);
      }
    }

    function findEntitiesBydbId(modelTree, dbId, data) {
      for (let i = 0; i < modelTree.length; i++) {
        let x = modelTree[i];
        if (x.objectId === dbId) {
          data.push(x);
          return;
        }
        if (x.childParts && x.childParts.length > 0)
          findEntitiesBydbId(x.childParts, dbId, data);
      }
    }

    function resetPartDetails() {
      vm.part = {
        partNumber: "",
        alternatePartNumber: "",
        description: "",
        weight: "",
        massUnit: "",
        quantity: 1,
        price: null,
        superseded: false,
      };

      vm.showSupersessionHistoryIcon = false;
      vm.showSupersessionHistory = false;
      vm.supersessionParts = [];
    }

    //Toggle the state of a given tool button based on entity selection
    function toggleToolButtonsState(toolBtn) {
      if (!toolBtn) return;

      const action = vm.isEntitySelected ? "remove" : "add";
      toolBtn.container.classList[action]("disabled-button");
    }

    //Handles any special actions needed for the current selection
    function handleSelectionPart(data) {
      let selectedPartId = findSparePart(data.dbIdArray[0], vm.modelTree);

      if (selectedPartId && selectedPartId !== data.dbIdArray[0]) {
        data.dbIdArray = [selectedPartId];
      }
    }

    //Handles aggregate events for the selection
    function handleAggregateEvent(data) {
      const lockedArray = nodeIdsSetsExplodeAsSingleEntity.map((set) => set[0]);
      const currentSelection = viewerHelperService.getSelectedParts();
      let idsToSelect = [];
      let isSuperseded = false;
      let supersededPart;

      for (let dbId of data.dbIdArray) {
        let parentId = dbId;
        while (parentId !== 1) {
          if (isPartSuperseded(parentId)) {
            supersededPart = getSupersededPart(parentId);
            isSuperseded = true;
            break;
          }
          parentId = viewerHelperService.getParentId(parentId);
          if (lockedArray.includes(parentId)) {
            dbId = parentId;
          }
        }

        if (!currentSelection.includes(dbId) || data.dbIdArray.length === 1) {
          idsToSelect.push(dbId);
        }
      }
      if (isSuperseded) {
        openSupersedeModal(supersededModel);
      } else {
        handleValidSelection(idsToSelect, data);
      }
    }

    //Handles the actions needed for a valid selection
    function handleValidSelection(idsToSelect, data) {
      viewerHelperService.partViewerDetails = null;
      const current = Array.isArray(theViewer.getSelection())
        ? theViewer.getSelection()
        : [theViewer.getSelection()];

      if (idsToSelect.toString() !== current.toString()) {
        theViewer.select(idsToSelect);

        if (idsToSelect.length === 1) {
          triggerAggEvent(idsToSelect, data);
        } else if (idsToSelect.length > 1) {
          viewerHelperService.partViewerDetails = idsToSelect;
          $rootScope.$broadcast(
            "viewer-part-selected",
            viewerHelperService.partViewerDetails
          );
        }
      } else if (idsToSelect.length === 0) { // Only reset if no IDs to select
        resetViewer();
        console.log("Resetting viewer");
      }
    }

    //Triggers an aggregate event for the given IDs
    function triggerAggEvent(idsToSelect, data) {
      vm.isLoading = true;
      DOING_AGG_EVENT = true;
      const selections = [
        {
          model: data.model,
          dbIdArray: idsToSelect,
        },
      ];

      viewerApp.getCurrentViewer().setAggregateSelection(
        selections.map((selection) => ({
          model: selection.model,
          ids: selection.dbIdArray,
          selectionType: Autodesk.Viewing.SelectionType.OVERLAYED,
        }))
      );

      DOING_AGG_EVENT = false;

      const userId = vm.onBehalfOf ? vm.onBehalfOf.userId : null;

      viewerService
        .getPartViewerDetails(
          $stateParams.modelId,
          idsToSelect,
          vm.manualId,
          userId
        )
        .then(function (response) {
          onValidatedSelectionChange(response);
          vm.isPartsPalletActive = true;
          if (vm.isStockWarehousesEnabled) {
            fetchWarehouseStock()
          }
          vm.isLoading = false;
        });

      theViewer.select(idsToSelect);
    }

    //Resets the viewer state
    function resetViewer() {
      clearViewerPart();
      vm.optionSelected = false;
      vm.selectedOption = {};
      viewerBannerService.removeNotification();
      $rootScope.$broadcast("Hide-Non-Modeled-Parts");
      // Clear Section Analysis
      var sectionExt = viewer.getExtension('Autodesk.Section');
      if (sectionExt) {
        sectionExt.deactivate();
        if (sectionExt.tool) {
          sectionExt.tool.clearSection();
        }
      }
    }

    $scope.$on("customer-viewer-part-selected", function (event, resp) {
      onValidatedSelectionChange(resp);
    });

    $scope.$on("loading-complete", function () {
      if (viewerLoaded && statesReturned && modelTreeLoaded) {
        if (viewerApp.getCurrentViewer().model.getData().instanceTree) {
          viewerHelperService.clearOldLeafNodes();
          viewerHelperService.getAllLeafDbIds(viewerApp).then(function (res) {
            leafNodes = res;
            customWeldments = viewerHelperService.calculateWeldments(viewerApp);

            var groupedEntities = [];
            var lockedItems = [];
            for (var x = 0; x < customWeldments.length; x++) {
              if (leafNodes.indexOf(customWeldments[x]) < 0) {
                groupedEntities.push([customWeldments[x]]);
                lockedItems.push(customWeldments[x]);
              }
            }
            for (var y = 0; y < leafNodes.length; y++) {
              var leafsParent = viewerHelperService.getParentId(leafNodes[y]);
              groupedEntities.push([leafsParent]);
              lockedItems.push(leafsParent);
            }
            bulkAddIdToExplodeAsSingleEntity(groupedEntities);
            $rootScope.$broadcast("locked-model-tree-updated", lockedItems);
            viewerHelperService.showAllParts();
          });
        } else {
          $timeout(function () {
            $rootScope.$broadcast("loading-complete");
          }, 1000);
        }
      }
    });

    $scope.$on("model-tree-initialized", function (event, modelTree) {
      vm.modelTree = modelTree;
      viewerHelperService.setPartTreeTopId(vm.modelTree[0].objectId);
      modelTreeLoaded = true;
      $rootScope.$broadcast("loading-complete");
    });

    function weldmentsUpdated(weldmentArray) {
      var lockedItems = [];
      for (var x = 0; x < weldmentArray.length; x++) {
        var nonArrayId = weldmentArray[x][0];
        lockedItems.push(nonArrayId);
      }
      customWeldments = lockedItems;
      $rootScope.$broadcast("locked-model-tree-updated", lockedItems);
    }

    function onValidatedSelectionChange(resp) {
      var partViewerDetail = resp.data;

      $rootScope.$broadcast("viewer-part-selected", partViewerDetail);
      viewerHelperService.setCurrentSelectedPart(partViewerDetail);

      vm.containsOptionsSet = false;
      vm.optionsSets = null;

      vm.hasLinkedTechDocs = false;
      vm.linkedTechDocs = null;

      vm.nonModelledParts = false;
      vm.nonModelled = null;

      vm.showSupersessionHistoryIcon = false;
      vm.showSupersessionHistory = false;

      if (!vm.isPartsPalletActive) {
        if (vm.partsPalletDebounceTimer) {
          $timeout.cancel(vm.partsPalletDebounceTimer);
          vm.partsPalletDebounceTimer = null;
        }
        vm.isPartsPalletActive = true;
      }

      if (partViewerDetail.part.inSupersession === true) {
        vm.showSupersessionHistoryIcon = true;
        vm.masterPartNumber = partViewerDetail.part.partNumber
        vm.masterPartId = partViewerDetail.part.masterPartId;
        fetchSupersessionHistory(vm.masterPartNumber, vm.masterPartId);
      }
      if (partViewerDetail.nonModelled) {
          $rootScope.$broadcast(
            "Show-Non-Modeled-Parts",
            partViewerDetail.nonModelledPart,
            vm.part.partNumber = partViewerDetail.part.partNumber);
            vm.nonModelledParts = true;
            vm.isPartsPalletActive = true
      } else {
        $rootScope.$broadcast("Hide-Non-Modeled-Parts");
        vm.isBasketOpen = true;

        $timeout(function () {
          viewer.resize();
        });

        if (partViewerDetail.containsOptionsSet) {
          vm.optionsSets = resp.data.optionsSet[0];
          vm.optionsSets.quantity = 1;
          vm.containsOptionsSet = true;
        }

        if (partViewerDetail.hasLinkedTechDocs) {
          vm.hasLinkedTechDocs = true;
          vm.linkedTechDocs = partViewerDetail.linkedTechDocs;
        }
      }
      vm.part = partViewerDetail.part;
      vm.part.partNumber = partViewerDetail.part.partNumber
        ? partViewerDetail.part.partNumber
        : "";
      vm.part.description = partViewerDetail.part.partDescription
        ? partViewerDetail.part.partDescription
        : "";
      vm.part.quantity = 1;

      if (partViewerDetail.part.note) {
          vm.part.note = typeof partViewerDetail.part.note === 'string'
            ? partViewerDetail.part.note.split('\n')
            : partViewerDetail.part.note;
      }

      var isInAssembly = false;

      if (partViewerDetail.part.parentObjectId) {
        isInAssembly = checkIsParentAssembly(
          partViewerDetail.part.parentObjectId
        );
      }

      if (partViewerDetail.inKit || isInAssembly) {
        viewerBannerService.setKitNotification(
          partViewerDetail.part,
          partViewerDetail.inKit,
          partViewerDetail.kits,
          isInAssembly
        );
      } else {
        viewerBannerService.removeNotification();
      }

      if (partViewerDetail.linked) {
        if (!linkedPartModalOpen) {
          linkedPartModalOpen = true;
          $rootScope.$broadcast("Hide-Non-Modeled-Parts");

          var part = {
            partNumber: partViewerDetail.part.partNumber,
            partDescription: partViewerDetail.part.partDescription,
            modelId: $stateParams.modelId,
            quantity: 1,
          };
          linkedModelConfirmOpen(partViewerDetail.linkedModel, part);
        }
      }

      if (vm.sharingViewer !== null) {
        GLOBAL_SHARING_EVENT("SELECTION_CHANGED");
      }

      var isLandscapeSmallScreen = $window.innerWidth <= 992 && $window.innerHeight < $window.innerWidth;
      if (isLandscapeSmallScreen) {
          vm.isBasketBoxActive = false; // Ensure only pallet is open on small landscape
      }

    }

    function checkIsParentAssembly(parentObjectId) {
      return assemblies.includes(parentObjectId);
    }

    function clearViewerPart() {
      console.log("Clearing viewer part");
      vm.containsOptionsSet = false;
      vm.nonModelledPart = false;
      $timeout(function () {
        vm.part.partId = "";
        vm.part.partNumber = "";
        vm.part.description = "";
        vm.part.note = "";
        vm.part.weight = "";
        vm.part.massUnit = "";
        vm.part.quantity = "";
        vm.part.stock = "";
        vm.linkedTechDocs = [];
        vm.hasLinkedTechDocs = false;
      
      });
    }

    function linkedModelConfirmOpen(linkedModel, part) {
      var modalInstance = $uibModal
        .open({
          keyboard: false,
          templateUrl:
            "features/viewer/extensions/linkPart/linkPartConfirmNavigation.html",
          controller: "LinkPartOpenController as linkPartOpenCtrl",
          resolve: {
            linkedModel: function () {
              return linkedModel;
            },
            part: function () {
              return part;
            },
          },
        })
        .result.then(function (response) {
          linkedPartModalOpen = false;
          //viewerHelperService.selectParts([]);
        });
    }

    function toggleExplodePartVisibility() {
      vm.isExplodeSliderVisible = !vm.isExplodeSliderVisible;
      $scope.$apply();
    }

    function hideExplodeSlider() {
      if (vm.isExplodeSliderVisible) {
        vm.isExplodeSliderVisible = false;
        $scope.$apply();
      }
    }

    angular.element($window).on("storage", function (event) {
      if (event.key === "basketUpdate") {
        $scope.$apply(function () {
          updateBasket();
        });
      }
    });

    function updateBasket() {
      vm.basket = basketService.getBasket();
      vm.basketSize = 0;
      localStorage.setItem("basketUpdate", new Date().toISOString());
      for (var i = 0; i < vm.basket.length; i++) {
        vm.basketSize = vm.basketSize + vm.basket[i].quantity;
      }
      vm.manualPartsCount = basketService.getManualPartsCount();
      vm.basketSize = vm.basketSize + vm.manualPartsCount;
    }

    function selectFullAssembly() {
      viewerHelperService.selectParentPart(
        viewerHelperService.getCurrentSelectedPart()
      );
    }

    function addToBasket() {
      if (vm.isPreviewMode) {
        alert(CANT_ADD_PREVIEW);
      } else {
        var basketPart = {};
        basketPart.machineName = $stateParams.machineName;
        basketPart.modelId = parseInt($stateParams.modelId);
        basketPart.quantity = vm.part.quantity;
        basketPart.partId = vm.part.partId;
        basketPart.masterPartId = vm.part.masterPartId;
        basketPart.partNumber = vm.part.partNumber;
        basketPart.alternatePartNumber = vm.part.alternatePartNumber;
        basketPart.partDescription = vm.part.description
          ? vm.part.description
          : vm.part.partDescription;
        basketPart.stock = vm.part.stock;
        basketPart.price = vm.part.price;
        basketPart.hasLinkedTechDocs = vm.hasLinkedTechDocs;
        basketPart.linkedTechDocs = vm.linkedTechDocs;
        basketPart.note = vm.part.note;

        basketService.addPart(basketPart);
        updateBasket();

        vm.isAddBasketButtonClicked = true;

        $timeout(function () {
          vm.isAddBasketButtonClicked = false;
        }, 500);
      }
    }

    function addOptionToBasket() {
      if (vm.isPreviewMode) {
        alert(CANT_ADD_PREVIEW);
        alert(CANT_ADD_PREVIEW);
      } else {
        var basketPart = {};
        basketPart.machineName = $stateParams.machineName;
        basketPart.modelId = parseInt($stateParams.modelId);
        basketPart.quantity = vm.optionsSets.quantity;
        basketPart.partId = vm.selectedOption.id;
        basketPart.masterPartId = vm.selectedOption.masterPartId;
        basketPart.partNumber = vm.selectedOption.partNumber;
        basketPart.partDescription = vm.selectedOption.description
          ? vm.selectedOption.description
          : vm.selectedOption.partDescription;
        basketPart.stock = vm.selectedOption.stock;
        basketPart.price = vm.selectedOption.price;

        basketService.addPart(basketPart);
        updateBasket();

        vm.isAddBasketButtonClicked = true;

        $timeout(function () {
          vm.isAddBasketButtonClicked = false;
        }, 500);
      }
    }

    function updateOptionSelected() {
      if (
        vm.selectedOption != null &&
        (vm.selectedOption.id != 0 || vm.selectedOption.masterPartId != 0)
      ) {
        vm.optionSelected = true;
      } else {
        vm.optionSelected = false;
      }
    }

    function shutDownViewer() {
      if (theViewer != null) {
        theViewer.removeEventListener(
          Autodesk.Viewing.SELECTION_CHANGED_EVENT,
          onSelectionChanged
        );
        theViewer.finish();
      }
    }

    function goToCreateEnquiry() {
      $state.go("create", {
        onBehalfOf: $state.params.onBehalfOf,
      });
    }

    function findSnapshotById(id) {
      return _.findWhere(vm.snapshots, { stateId: id });
    }

    function findChildSnapshots(id) {
      return _.filter(vm.snapshots, function (snapshot) {
        return snapshot.parentId === id;
      });
    }

    function quantityChanged() {
      if (!vm.part.quantity) {
        vm.part.quantity = 1;
      }
    }

    function optionQuantityChanged() {
      if (!vm.optionsSets.quantity) {
        vm.optionsSets.quantity = 1;
      }
    }

    function backToViewables() {
      if (vm.isPreviewMode) {
        $state.go("productsModels", {
          productId: $stateParams.productId,
          machineName: $stateParams.machineName,
        });
      } else {
        goToViewables();
      }
    }

    function goBackToPartSearch() {
      $window.sessionStorage.setItem("fromWhereUsedModal", "false");
      $state.go("customerPartSearch");
    }

    function getManualParams() {
      var params = {
        manualId: vm.manualId,
        onBehalfOf: vm.onBehalfOf
          ? btoa(encodeURIComponent(JSON.stringify(vm.onBehalfOf)))
          : "null",
      };
      return params;
    }

    function goToViewables() {
      $state.go("customerManual.viewables", getManualParams());
    }

    function removePart(part) {
      basketService.removePart(part);
      updateBasket();
        }

      function removeKitItem(kit) {
          basketService.removeKit(kit);
          updateBasket();
      }

    function partUpdated(part) {
      basketService.updatePart(part);
      updateBasket();
        }

      function kitUpdated(kit) {
          var kitIndex = vm.basket.findIndex(basketItem => basketItem.kitId === kit.kitId);
          if (kitIndex !== -1) {
              vm.basket[kitIndex].quantity = kit.quantity;
          }
      
          basketService.updateKit(vm.basket[kitIndex]);
          updateBasket();
      }

    function filterMasterPartKitId(kit) {
      return kit.kitId !== null && kit.kitId !== undefined;
    }
    
    function filterNoMasterPartKitId(item) {
      return item.kitId === null || item.kitId === undefined;
  }

    function openNotes() {
      vm.isNotesOpen = true;
    }

    function closeNotes() {
      vm.isNotesOpen = false;
    }

    function isPartSuperseded(objectId) {
      return (
        _.findWhere(vm.supersededPartsArray, { objectId: objectId }) !==
        undefined
      );
    }

    function getSupersededPart(objectId) {
      return _.findWhere(vm.supersededPartsArray, { objectId: objectId });
    }

    function openSupersedeModal(supersededModel) {
      supersededModel.manualId = vm.manualId;
      supersededModel.productId = $stateParams.productId;
      $uibModal.open({
        keyboard: false,
        templateUrl:
          "features/viewer/extensions/supersededAssembly/openSupersededPartModal.html",
        controller: "OpenSupersededPartController as openSupersededPartCtrl",
        resolve: {
          supersededModel: function () {
            return supersededModel;
          },
        },
      });
    }

    function getSupersededParts() {
      viewerService
        .getSupersedeParts(vm.myModelId)
        .then(getSupersededPartsSuccess, serviceFailed);
    }

    function isOptionInBasket() {
      // checking ifvm.basket and vm.optionsSets.optionsSet are defined
      if (!vm.basket || !vm.optionsSets || !vm.optionsSets.optionsSet) {
          return false;
      }
      return vm.basket.some(basketItem => 
          vm.optionsSets.optionsSet.some(option => 
              option.partNumber === basketItem.partNumber
          )
      );
  }

    function getSupersededPartsSuccess(response) {
      vm.supersededPartsArray = response.data;
    }

    function serviceFailed(error) {
      console.error(error);
      viewerBannerService.setNotification("ERROR", WENT_WRONG, 10000);
    }

    function hide() {
      var selSet = viewer.getSelection();
      viewerHelperService.hideParts(selSet);

      if (vm.sharingViewer !== null) {
        GLOBAL_SHARING_EVENT("HIDE_EVENT");
      }
    }

    function isolate() {
      var selSet = viewer.getSelection();
      viewerHelperService.isolateParts(selSet);

      if (vm.sharingViewer !== null) {
        GLOBAL_SHARING_EVENT("ISOLATE_EVENT");
      }
    }

    function getVisibleIds() {
      //TODO @Dipika -> this method needs updated to a method that returns the parts that are visible in the viewer
      return JSON.stringify(viewerVariablesService.getVisibleNodes());
    }

    function viewLinkedTechDocs() {
      if (vm.linkedTechDocs.length == 1) {
        $window.open(vm.linkedTechDocs[0].url, "_blank");
      } else {
        $uibModal.open({
          templateUrl: "features/orders/linkedTechDocs/orderTechDocModal.html",
          controller: "OrderTechDocController",
          controllerAs: "orderTechDocCtrl",
          size: "md",
          resolve: {
            techDocObject: function () {
              return {
                techDocs: vm.linkedTechDocs,
                partNumber: vm.part.partNumber,
              };
            },
          },
        });
      }
    }

    function remoteUpdateViewer(state, ids) {
      var parsedIds = JSON.parse(ids);
      viewerApp.getCurrentViewer().restoreState(state, true);
      showHideNodes(parsedIds);
    }

    //NEW SCROLLBAR LOGICS

    var listH = $(".custViewerHorizontal_list");
    var prevH = $(".custViewerHorizontal_btn--prev");
    var nextH = $(".custViewerHorizontal_btn--next");
    var listV = $(".custViewerVertical_list");
    var prevV = $(".custViewerVertical_btn--prev");
    var nextV = $(".custViewerVertical_btn--next");

    prevH.on("click", prevClicked);
    prevV.on("click", prevClicked);
    nextH.on("click", nextClicked);
    nextV.on("click", nextClicked);

    function prevClicked() {
      if (CAROUSEL_POSITION > 1) {
        CAROUSEL_POSITION = CAROUSEL_POSITION - 1;
        snapCarouselScroll();
      }
    }

    function nextClicked() {
      var list = vm.isVertical ? listV : listH;
      var listSize = vm.isVertical ? listV.outerHeight() : listH.outerWidth();
      var numOfVisibleBoxes = Math.floor(
        listSize / list.children().eq(0).outerWidth()
      );
      if (CAROUSEL_POSITION < list.children().length - numOfVisibleBoxes + 1) {
        CAROUSEL_POSITION = CAROUSEL_POSITION + 1;
        snapCarouselScroll();
      }
    }

    function hidePrice() {
      if (vm.isManufacturer) {
        vm.hidePrice = false;
      } else {
        vm.hidePrice = !userService.getPreviewPricingEnabled();
      }
      return vm.hidePrice;
    }

    function togglePriceVisibility() {
      vm.priceManuallyToggle = !vm.priceManuallyToggle;
    }

    function snapCarouselScroll() {
      var scrollVDist = 0;
      var scrollHDist = 0;
      var list = vm.isVertical ? listV : listH;
      var gap = 8;
      for (var i = 1; i <= CAROUSEL_POSITION - 1; i++) {
        scrollVDist =
          scrollVDist +
          list
            .children()
            .eq(i - 1)
            .outerHeight() +
          gap;
        scrollHDist =
          scrollHDist +
          list
            .children()
            .eq(i - 1)
            .outerWidth() +
          gap;
      }
      listV.css("transform", "translateY(-" + scrollVDist + "px)");
      listH.css("transform", "translateX(-" + scrollHDist + "px)");

      enableDisableScrollButtons();
    }

     function updateLoadingIconPosition(event) {
        var loadingIcon = document.getElementById('loadingIcon');
        if (loadingIcon) {
            loadingIcon.style.top = event.clientY + 'px';
            loadingIcon.style.left = event.clientX + 'px';
        }
    }

    function enableDisableScrollButtons() {
      var list = vm.isVertical ? listV : listH;
      var listSize = vm.isVertical ? listV.outerHeight() : listH.outerWidth();
      var numOfVisibleBoxesV = Math.floor(
        listSize / list.children().eq(0).outerHeight()
      );
      var numOfVisibleBoxesH = Math.floor(
        listSize / list.children().eq(0).outerWidth()
      );
      var numOfVisibleBoxes = vm.isVertical
        ? numOfVisibleBoxesV
        : numOfVisibleBoxesH;
      if (
        CAROUSEL_POSITION ===
        list.children().length - numOfVisibleBoxes + 1
      ) {
        nextH.prop("disabled", true);
        nextV.prop("disabled", true);
      } else {
        nextH.prop("disabled", false);
        nextV.prop("disabled", false);
      }
      if (vm.currentList.length === 0) {
        nextH.prop("disabled", true);
        nextV.prop("disabled", true);
      } else {
        nextH.prop("disabled", false);
        nextV.prop("disabled", false);
      }
      if (CAROUSEL_POSITION === 1) {
        prevH.prop("disabled", true);
        prevV.prop("disabled", true);
      } else {
        prevH.prop("disabled", false);
        prevV.prop("disabled", false);
      }
    }

    var horizontalScrollContainer = document.getElementById(
      "custViewerHorizontal_container"
    );
    var verticalScrollContainer = document.getElementById(
      "custViewerVertical_container"
    );

    const doScroll = (evt) => {
      evt.preventDefault();

      horizontalScrollContainer.scrollLeft += evt.deltaX;
      verticalScrollContainer.scrollRight += evt.deltaY;
      var delta = vm.isVertical ? -evt.deltaY : evt.deltaY;
      if (delta <= 0) {
        nextClicked();
      } else {
        prevClicked();
      }
    };

    horizontalScrollContainer.addEventListener("wheel", doScroll);
    verticalScrollContainer.addEventListener("wheel", doScroll);

    function backToZero() {
      CAROUSEL_POSITION = 1;
      snapCarouselScroll();
    }

    function closePartList() {
      var close = document.querySelector(".cadBasketMobile");
      close.style.display = "none";
    }

    function openPartList() {
      var open = document.querySelector(".cadBasketMobile");
      open.style.display = "flex";
    }

    function setCurrency() {
      vm.defaultCurrency = userService.getDefaultCurrency();
      if (userService.isOnBehalfOf()) {
        userService
          .getCurrencyForUser(userService.getOnBehalfOfUserId())
          .then(function (response) {
            vm.defaultCurrency = response.data;
          });
      }
    }

    function toggleSupersessionHistory() {
      vm.showSupersessionHistory = !vm.showSupersessionHistory;
    }
    
    function fetchSupersessionHistory() {

      if (currentSupersessionRequest) {
        currentSupersessionRequest.cancelled = true;
      }

      vm.isSupersessionLoading = true;
      var masterPartNumber = vm.masterPartNumber;
      var masterPartId = vm.masterPartId;
      var purchaserId = userService.getManufacturerSubEntityId();
      var manufacturerId = userService.getManufacturerId();
      vm.isLoading = true;

      currentSupersessionRequest = { cancelled: false };
      var thisRequest = currentSupersessionRequest;

      if (userService.isManufacturer()) {
        viewerService.getSupersessionHistoryForManufacturer(manufacturerId, masterPartId)
          .then(function (response) {
            if (!thisRequest.cancelled) {
              processSupersessionHistory(response);
            }
          })
          .catch(function (error) {
            if (!thisRequest.cancelled) {
              console.error('Failed to fetch Supersession parts as Manufacturer:', error);
              vm.isLoading = false;
            }
          })
          .finally(function () {
            if (!thisRequest.cancelled) {
              vm.isSupersessionLoading = false;
            }
          });
      } else {
        viewerService.getSupersessionHistory(purchaserId, masterPartNumber)
          .then(function (response) {
            if (!thisRequest.cancelled) {
              processSupersessionHistory(response);
            }
          })
          .catch(function (error) {
            if (!thisRequest.cancelled) {
              console.error('Failed to fetch Supersession parts:', error);
              vm.isLoading = false;
            }
          })
          .finally(function () {
            if (!thisRequest.cancelled) {
              vm.isSupersessionLoading = false;
            }
          });
      }
}

    function processSupersessionHistory(response) {
      var currentLanguage = $translate.use();

      response.data.supersessionHistory.forEach(part => {
        if (part.languageDescriptions && part.languageDescriptions.length > 0) {
          var matchingDescription = part.languageDescriptions.find(desc => desc.code === currentLanguage) || part.languageDescriptions[0];
          part.partDescription = matchingDescription && matchingDescription.description ? matchingDescription.description : '';
        } else {
          part.partDescription = '';
        }
      });
      vm.supersessionParts = response.data.supersessionHistory.sort((a, b) => b.supersessionIndex - a.supersessionIndex);
      vm.isLoading = false;
    }

    function selectSearchResult(result) {
      vm.isPartsPalletActive = true;
      
      vm.partSearchInProgress = true;

      vm.selectedPartNumber = result.partNumber;
      
      partSearchClick(result.partNumber);

      var isLandscapeSmallScreen = $window.innerWidth <= 992 && $window.innerHeight < $window.innerWidth;
      if (isLandscapeSmallScreen) {
          vm.isBasketBoxActive = false;
      }
      
      $timeout(function() {
        vm.partSearchInProgress = false;
      }, 500);
    }

    function partSearchClick(partNumber) {
      var value =
        partNumber !== undefined
          ? partNumber
          : document.getElementById("partSearch").value.trim();

      var currentSearchMode =
        partNumber !== undefined
          ? "partNumber"
          : vm.searchMode; 

      // Store the current state of the parts pallet
      var wasPartsPalletActive = vm.isPartsPalletActive;

      var modelTree = JSON.parse(JSON.stringify(vm.modelTree));
      var nodes = [];

      searchPartRecursive(modelTree, value, nodes, currentSearchMode);

      if (nodes.length === 0) {
        alert("No part found matching your search criteria.");
      } else {
        vm.previouslyIsolatedNodes = viewer.getIsolatedNodes() || [];
        viewer.clearThemingColors();
        viewerHelperService.isolateParts(JSON.stringify(nodes));
        viewer.setGhosting(true);
        var color = new THREE.Vector4(0.0, 1.0, 0.5, 0.8);
        for (var node of nodes)
          viewer.setThemingColor(node, color, null, true);

        var userId = vm.onBehalfOf ? vm.onBehalfOf.userId : null;

        var callback = function(response) {
        var currentState = vm.isPartsPalletActive;
          
          onValidatedSelectionChange(response);
         
          if (wasPartsPalletActive && !vm.isPartsPalletActive) {
            vm.isPartsPalletActive = true;
          }
        };

        viewerService
          .getPartViewerDetails(
            $stateParams.modelId,
            nodes,
            vm.manualId,
            userId
          )
          .then(callback, function () {
            console.error("Auto part selection failed. Part not found.");
          });
      }
      viewerHelperService.hideSpinner();
    }

    function searchPartRecursive(modelTree, valueToFind, nodes, mode) {
      if (!modelTree || !modelTree.length) return;
      if (!nodes) nodes = [];

      for (var i = 0; i < modelTree.length; i++) {
        var node = modelTree[i];
        var isMatch = false;

        var searchValue = valueToFind ? valueToFind.toString().toLowerCase() : '';

        if (node.partNumber && searchValue) {
          if (mode === "partNumber") {
            var partNumStr = node.partNumber.toString().toLowerCase();
            if (searchValue.length === 1) {
              isMatch = partNumStr.startsWith(searchValue);
            } else {
              isMatch = partNumStr.includes(searchValue);
            }
          } else if (mode === "description" && node.partDescription) {
            var descriptionText = node.partDescription.toString().toLowerCase().replace(/-/g, ' ');
            var searchText = searchValue.toLowerCase().replace(/-/g, ' ');
            isMatch = descriptionText.includes(searchText);
          }

          if (isMatch) {
            var canAddNode = true;
            // If a snapshot is active, filter by its visibleDbIds
            if (vm.activeSnapshotVisibleDbIds && vm.activeSnapshotVisibleDbIds.length > 0) {
              if (node.objectId && !vm.activeSnapshotVisibleDbIds.includes(node.objectId)) {
                canAddNode = false;
              }
            }

            if (canAddNode) {
              // Add to results if not already present (by objectId)
              if (node.objectId && !nodes.includes(node.objectId)) {
                 nodes.push(node.objectId);
              }
            }
          }
        }

        if (node.childParts && node.childParts.length > 0) {
          searchPartRecursive(node.childParts, valueToFind, nodes, mode);
        }
      }
    }

    function clearPartSearch() {
      vm.searchValue = "";
      vm.searchResults = [];
      vm.showSearchResults = false;
      vm.showNoResultsMessage = false;
      vm.isDropdownMinimized = false;
      viewer.setGhosting(false);
      viewer.clearThemingColors();

      if (currentSnapshot && currentSnapshot.id !== 'ROOT' && vm.activeSnapshotVisibleDbIds && vm.activeSnapshotVisibleDbIds.length > 0) {
        showHideNodes(vm.activeSnapshotVisibleDbIds);
      } else {
        if (vm.previouslyIsolatedNodes.length > 0) {
          viewer.isolate(vm.previouslyIsolatedNodes);
        } else {
          viewer.isolate(0);
        }
        resetViewer();
        loadSnapshot('ROOT', false);
      }

      vm.previouslyIsolatedNodes = [];
      vm.part = {};
      clearViewerPart();
    }
  
    function toggleKitsAccordion(kitId) {
    
    if (vm.accordionStates[kitId] === undefined) {
      vm.accordionStates[kitId] = false;
    }
  
    // Toggle Accordion
    vm.accordionStates[kitId] = !vm.accordionStates[kitId];

    if (vm.accordionStates[kitId]) {
      fetchKitDetails(kitId);
    }
    }

    function fetchKitDetails(kitId) {

      var kit = vm.basket.find(k => k.kitId === kitId);
      if (!kit) {
        console.error("Kit not found in basket:", kitId);
        return;
      }
    
      // Fetch only if accordion is open and parts aren't loaded
      if (vm.accordionStates[kitId] && (!kit.parts || kit.parts.length === 0)) {
        var purchaserId = vm.onBehalfOf ? vm.onBehalfOf.id : userService.getManufacturerSubEntityId(); // Adjust based on context if needed
        if (!purchaserId) {
             purchaserId = userService.getUserId();
        }
    
        masterPartService.getPurchaserKit(purchaserId, kitId)
          .then(function (response) {
            if (response.data && response.data.parts) {
              var kitToUpdate = vm.basket.find(k => k.kitId === kitId);
              if (kitToUpdate) {
                kitToUpdate.parts = response.data.parts;
              }
            } else {
               console.warn("No parts data received for kitId:", kitId);
               var kitToUpdate = vm.basket.find(k => k.kitId === kitId);
               if (kitToUpdate && !kitToUpdate.parts) {
                   kitToUpdate.parts = [];
               }
            }
          })
          .catch(function(error) {
              console.error("Error fetching kit details for kitId:", kitId, error);
              var kitToUpdate = vm.basket.find(k => k.kitId === kitId);
               if (kitToUpdate && !kitToUpdate.parts) {
                   kitToUpdate.parts = [];
               }
          });
      }
    }

    function getDescription(item) { 
      if (!item) {
        return '';
      }
      
      var translations = item.descriptions || item.titles;
      if (Array.isArray(translations) && translations.length > 0) {
          var userLanguage = localStorage.getItem("NG_TRANSLATE_LANG_KEY") || "EN";
          var description = translations.find(
              (d) => d.languageCode === userLanguage && d.translation
          );
          // Fallback to English if user language not found or has no translation
          if (!description) {
              description = translations.find((d) => d.languageCode === 'EN' && d.translation);
          }
          if (description) {
              return description.translation;
          }
      }

      // If no descriptions array, check for simple description properties (from kit parts)
      if (typeof item.description === 'string' && item.description) {
        return item.description;
      }
      if (typeof item.partDescription === 'string' && item.partDescription) {
        return item.partDescription;
      }
      return '';
    }

    function fetchWarehouseStock() {
      if (!vm.part || !vm.part.partNumber || vm.part.stock == null) return;

      var masterPartIds = [vm.part.masterPartId];

      // Call to get all warehouse IDs
      shippingEngineService.getAllWarehouse(userService.getManufacturerId())
        .then(function (warehouses) {
          var warehouseIds = warehouses.map(function (warehouse) {
            return warehouse.id;
          });

          var matchedWarehouse = warehouses.find(function (warehouse) {
            return warehouse.id === vm.part.defaultWarehouseId;
          });

          if (matchedWarehouse) {
            vm.warehouseName = matchedWarehouse.name;
          } else {
            vm.warehouseName = 'No default warehouse';
          }

          // Fetch stock for each warehouse
          return masterPartService.getBasketWarehouseStock(userService.getManufacturerId(), masterPartIds, warehouseIds);
        })
        .then(function (response) {
          vm.warehouseStock = response.data[0].warehouseStock;
        })
        .catch(function (error) {
          console.error('Error fetching warehouse stock:', error);
        });
    }

    function adjustDropdownPosition() {
      var dropdown = document.querySelector('.user-dropdown-stock');
      var rect = dropdown.getBoundingClientRect();

      if (rect.right > window.innerWidth) {
        dropdown.style.left = 'auto';
        dropdown.style.right = '0';
      }
    }

    document.querySelector('.profile').addEventListener('mouseenter', adjustDropdownPosition);

    function initializeOverlayBoxes() {
      if ($window.innerWidth <= 992 && $window.innerHeight < $window.innerWidth) {
        vm.isPartsPalletActive = false;
        vm.isBasketBoxActive = false;
        vm.isSearchBoxActive = false;
      } else {
        // vm.isPartsPalletActive = false;
        vm.isBasketBoxActive = false;
        vm.isSearchBoxActive = true;
      }
    }

    function toggleOverlayBox(boxId) {
      var isLandscapeSmallScreen = $window.innerWidth <= 992 && $window.innerHeight < $window.innerWidth;

      if (boxId === 'basketBox') {
        vm.isBasketBoxActive = !vm.isBasketBoxActive;
        if (isLandscapeSmallScreen && vm.isBasketBoxActive) {
          vm.isPartsPalletActive = false;
        }
      } else if (boxId === 'partsPallet') {
        vm.isPartsPalletActive = !vm.isPartsPalletActive;
        if (isLandscapeSmallScreen && vm.isPartsPalletActive) {
          vm.isBasketBoxActive = false; 
        }
        vm.showSupersessionHistory = false;
      } else if (boxId === 'part-search-container') {
        vm.isSearchBoxActive = !vm.isSearchBoxActive;
      }
    }

    function checkOrientation() {
      if (window.innerWidth <= 992) {
        vm.showRotateOverlay = window.innerHeight > window.innerWidth;
        $scope.$apply();
      } else {
        vm.showRotateOverlay = false;
      }
    }

    function onSearchInputChange() {
      if (!vm.searchValue || vm.searchValue.length === 0) {
        vm.searchResults = [];
        vm.showSearchResults = false;
        vm.showNoResultsMessage = false;
        return;
      }

      var modelTree = JSON.parse(JSON.stringify(vm.modelTree));
      vm.searchResults = [];
      var addedPartNumbers = new Set();
      findMatchingParts(modelTree, vm.searchValue, vm.searchResults, addedPartNumbers);
      
      vm.searchResults.sort((a, b) => {
        if (vm.searchMode === "partNumber") {
          return a.partNumber.localeCompare(b.partNumber, undefined, {numeric: true, sensitivity: 'base'});
        } else {
          return a.description.localeCompare(b.description, undefined, {numeric: true, sensitivity: 'base'});
        }
      });
      
      // Show no results message if search is 3+ characters but no results found
      vm.showNoResultsMessage = vm.searchValue.length >= 3 && vm.searchResults.length === 0;
      vm.showSearchResults = vm.searchResults.length > 0;
    }

    function findMatchingParts(modelTree, searchValue, results, addedPartNumbers, isRootLevel) {
      if (!modelTree || !modelTree.length) return;
      if (!addedPartNumbers) addedPartNumbers = new Set();
      if (typeof isRootLevel === "undefined") isRootLevel = true; // default on first call
    
      for (var i = 0; i < modelTree.length; i++) {
        var node = modelTree[i];
        var isMatch = false;
    
        if (node.partNumber && searchValue) {
          if (vm.searchMode === "partNumber") {
            var partNumberStr = node.partNumber.toString().toLowerCase();
            var searchStr = searchValue.toLowerCase();
            if (searchStr.length === 1) {
              isMatch = partNumberStr.startsWith(searchStr);
            } else {
              isMatch = partNumberStr.includes(searchStr);
            }
          } else if (vm.searchMode === "description" && node.partDescription) {
            var descriptionText = node.partDescription.toString().toLowerCase().replace(/-/g, ' ');
            var searchText = searchValue.toLowerCase().replace(/-/g, ' ');
            isMatch = descriptionText.includes(searchText);
          }
    
          if (isMatch) {
            var canAddNode = true;
            // If a snapshot is active, filter by its visibleDbIds
            if (vm.activeSnapshotVisibleDbIds && vm.activeSnapshotVisibleDbIds.length > 0) {
              if (node.objectId && !vm.activeSnapshotVisibleDbIds.includes(node.objectId)) {
                canAddNode = false;
              }
            }

            if (canAddNode) {
              if (!addedPartNumbers.has(node.partNumber)) {
                results.push({
                  objectId: node.objectId,
                  partNumber: node.partNumber,
                  name: node.name || node.partNumber,
                  description: node.partDescription || ''
                });
                addedPartNumbers.add(node.partNumber);
              }
            }
          }
        }
    
        if (node.childParts && node.childParts.length > 0) {
          findMatchingParts(node.childParts, searchValue, results, addedPartNumbers, false);
        }
      }
    
      if (isRootLevel && vm.searchMode === "partNumber") {
        results.sort(function (a, b) {
          return a.partNumber.localeCompare(b.partNumber, undefined, { numeric: true, sensitivity: 'base' });
        });
      }
  }   

    function toggleDropdownMinimized() {
      var dropdown = document.getElementById('searchResultsDropdown');
      if (!vm.isDropdownMinimized && dropdown) {
        // Save scroll position before minimizing
        vm._searchDropdownScrollTop = dropdown.scrollTop;
      }
      vm.isDropdownMinimized = !vm.isDropdownMinimized;
      if (!vm.isDropdownMinimized && dropdown && typeof vm._searchDropdownScrollTop === 'number') {
        // Restore scroll position after expanding
        setTimeout(function() {
          dropdown.scrollTop = vm._searchDropdownScrollTop;
        }, 0);
      }
    }
    
    function toggleSearchMode() {
      vm.searchMode = vm.searchMode === "partNumber" ? "description" : "partNumber";
      onSearchInputChange();
    }

    function togglePriceVisibility() {
      vm.hidePrice = !vm.hidePrice;
    }

    function adjustDropdownHeight() {
      $timeout(function() {
        var dropdownElement = $document[0].getElementById('searchResultsDropdown');
        var containerElement = $document[0].getElementById('search-results-container');

        if (!dropdownElement || !containerElement || !vm.showSearchResults) {
            if(dropdownElement) {
              dropdownElement.style.maxHeight = '300px';
            }
            return;
        }

        var rect = containerElement.getBoundingClientRect();
        var viewportHeight = $window.innerHeight;
        var topBarHeight = viewportHeight * 0.05;
        var spaceBelowContainer = viewportHeight - rect.top - topBarHeight;
        var buffer = 20;

        var dynamicMaxHeight = Math.max(100, spaceBelowContainer - buffer); 

        var absoluteMaxVh = 50;
        var absoluteMaxHeightPx = viewportHeight * (absoluteMaxVh / 100);

        var finalMaxHeight = Math.min(dynamicMaxHeight, absoluteMaxHeightPx);

        dropdownElement.style.maxHeight = finalMaxHeight + 'px';
      }, 0, false);
    }

    $scope.$watch(() => vm.showSearchResults, (newValue) => {
      if (newValue) {
          adjustDropdownHeight();
      } else {
          var dropdownElement = $document[0].getElementById('searchResultsDropdown');
           if(dropdownElement) {
              dropdownElement.style.maxHeight = '400px';
          }
      }
    });

    var onResize = () => {
      $timeout(adjustDropdownHeight, 50, false); 
    };

    var angularWindow = angular.element($window);
    angularWindow.on('resize', onResize);

    $scope.$on('$destroy', () => {
      angularWindow.off('resize', onResize);
    });

    function adjustHistoryHeight() {
      $timeout(function() {
        var historyContainer = $document[0].getElementById('SupersessionHistory');
        var historyList = historyContainer ? historyContainer.querySelector('ul') : null;

        if (!historyList || !historyContainer || !vm.showSupersessionHistory) {
            if(historyList) {
                historyList.style.maxHeight = '300px';
            }
            return;
        }

        var rect = historyContainer.getBoundingClientRect();
        var viewportHeight = $window.innerHeight;
        var topBarHeight = viewportHeight * 0.05;
        var spaceBelowContainer = viewportHeight - rect.top - topBarHeight;
        var buffer = 20;

        var headerElement = historyContainer.querySelector('.supersessionHistoryHeader');
        var headerHeight = headerElement ? headerElement.offsetHeight : 0;
        var dynamicMaxHeight = Math.max(50, spaceBelowContainer - buffer - headerHeight);

        var absoluteMaxVh = 40;
        var absoluteMaxHeightPx = viewportHeight * (absoluteMaxVh / 100);

        var finalMaxHeight = Math.min(dynamicMaxHeight, absoluteMaxHeightPx);

        historyList.style.maxHeight = finalMaxHeight + 'px';
      }, 0, false); 
    }

    $scope.$watch(() => vm.showSupersessionHistory, (newValue) => {
      if (newValue) {
          adjustHistoryHeight();
      } else {
          var historyContainer = $document[0].getElementById('SupersessionHistory');
          var historyList = historyContainer ? historyContainer.querySelector('ul') : null;
          if(historyList) {
              historyList.style.maxHeight = '300px';
          }
      }
    });

    var onResize = () => {
      $timeout(function() {
        adjustDropdownHeight();
        adjustHistoryHeight();
      }, 50, false); 
    };
  }
})();
