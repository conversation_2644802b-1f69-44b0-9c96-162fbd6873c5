(function () {
    'use strict';

    angular
        .module('app.shared')
        .controller('PurchaseOrderController', PurchaseOrderController);

    PurchaseOrderController.$inject = ['$uibModalInstance', 'ordersService', '$translate', 'userService', '$state'];

    function PurchaseOrderController($uibModalInstance, ordersService, $translate, userService, $state) {
        var vm = this;

        vm.save = save;
        vm.cancel = $uibModalInstance.dismiss;
        vm.purchaseOrder = '';

        function save() {
            if (!vm.purchaseOrder) {
            vm.hasError = true;
            vm.errorMessage = $translate.instant('ERROR.EMPTY_PO');
            return;
        }
        vm.hasError = false;
        $uibModalInstance.close(vm.purchaseOrder);
        }

    }
})();
