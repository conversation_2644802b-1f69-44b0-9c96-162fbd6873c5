(function () {
    'use strict';

    angular
        .module('app.viewer')
        .controller('PDFViewerController', PDFViewerController);

    PDFViewerController.$inject = ['viewerService', '$q', '$uibModal', '$stateParams', '$state', '$scope', '$window', 'viewerHelperService', '$timeout', 'viewerVariablesService', 'viewerBannerService', '$translate'];

    function PDFViewerController(viewerService, $q, $uibModal, $stateParams, $state, $scope, $window, viewerHelperService, $timeout, viewerVariablesService, viewerBannerService, $translate) {
        var vm = this;
        var viewerApp;
        var viewables;
        var viewerLoaded = false;
        var options = viewerService.getAutodeskToken()
            .then(function (resp) {
                options = resp;
                initialize();
            });

        var autodeskURN = $stateParams.autodeskURN;
        vm.machineName = $stateParams.machineName;
        vm.viewableName = $stateParams.viewableName;
        var documentId = "urn:" + autodeskURN;
        var currentModelStateId = '';
        var firstLoad = false;
        var CURRENT_SELECTED_PART = 0;

        vm.modelId = $stateParams.modelId;
        viewerVariablesService.setModelId(vm.modelId);
        vm.isTwoD = true;
        vm.pdfPages = [];
        vm.showCreateNewPart = false;
        vm.fromWhereUsedModal = $window.sessionStorage.getItem('fromWhereUsedModal') === 'true';
        $window.sessionStorage.removeItem("fromWhereUsedModal");

        vm.editPDFName = editPDFName;
        vm.goToPage = goToPage;
        vm.openCustomerViewerPreviewPage = openCustomerViewerPreviewPage;
        vm.updateNonModeledParts = updateNonModeledParts;
        vm.getNonModeledParts = getNonModeledParts;
        vm.hideCreateNewPart = hideCreateNewPart;
        vm.isCreateNewPartVisible = isCreateNewPartVisible;
        vm.openAddParts = openAddParts;
        vm.backToViewables = backToViewables;
        vm.goBackToPartSearch = goBackToPartSearch;

        var WENT_WRONG, NON_MODELLED_SUCCESS;
        $translate(['GENERAL.WENT_WRONG', 'PDF_VIEWER.NON_MODELLED_SUCCESS'])
            .then(function (resp) {
                WENT_WRONG = resp["GENERAL.WENT_WRONG"];
                NON_MODELLED_SUCCESS = resp["PDF_VIEWER.NON_MODELLED_SUCCESS"];
            });

        function initialize() {

            $scope.$on('$locationChangeStart', function (event) {
                if (!viewerLoaded) {
                    event.preventDefault();
                    console.log("Viewable loading back navigation disabled");
                } else {
                    console.log("Viewer Shutdown On Navigation");
                }
            });

            Autodesk.Viewing.Initializer(options, function onInitialized() {
                viewerApp = new Autodesk.Viewing.ViewingApplication('MyViewerDiv');
                viewerApp.registerViewer(viewerApp.k3D, Autodesk.Viewing.Viewer3D);
                viewerApp.loadDocument(documentId, onDocumentLoadSuccess, onDocumentLoadFailure);
            });

            getPages();
        }

        function getPages(){
            viewerService.getPDFStates(vm.modelId)
                .then(function (data) {
                    if (data) {
                        vm.pdfPages = data;
                        vm.pagesCreated = data.length > 0;
                        if (vm.pdfPages && vm.pdfPages[0]) {
                            for (var i = 0; i < vm.pdfPages.length; i++) {
                                vm.pdfPages[i].selected = (i === 0);
                            }
                        }
                    }
                });
        }

        function onDocumentLoadSuccess(doc) {
            viewables = viewerApp.bubble.search({'type': 'geometry'});
            if (viewables.length === 0) {
                console.error('Document contains no viewables.');
                return;
            }

            viewerApp.selectItem(viewables[0].data, onItemLoadSuccess, onItemLoadFail);
            viewerHelperService.setViewerApp(viewerApp.getCurrentViewer());
        }

        function onDocumentLoadFailure(viewerErrorCode) {
            console.error('onDocumentLoadFailure() - errorCode:' + viewerErrorCode);
            viewerHelperService.hideSpinner();
        }

        function onItemLoadSuccess(viewer, isPageChange) {
            var bgColour = viewerHelperService.getBackgroundColour();
            viewer.setBackgroundColor(bgColour.r, bgColour.g, bgColour.b, bgColour.r, bgColour.g, bgColour.b);
            viewer.setTheme("light-theme");
            viewer.setEnvMapBackground(false);
            viewer.setGhosting(false);
            viewer.setGroundReflection(false);
            viewer.setGroundShadow(false);
            viewer.setOptimizeNavigation(false);
            viewer.hidePoints(true);
            viewer.hideLines(true);
            viewer.setProgressiveRendering(true);
            viewer.setQualityLevel(false, false);
            viewer.setGhosting(false);

            viewer.setContextMenu(null);

            viewerHelperService.setIsTwoD(true);

            createPageSnapshots();

            if (!firstLoad) {
                firstLoad = true;
                goToPage(vm.pdfPages[0].stateId);
            }

            viewerLoaded = true;
            viewerHelperService.hideSpinner();

        }

        function onItemLoadFail(errorCode) {
            viewerHelperService.hideSpinner();
        }

        function shutDownViewer() {
            if (viewerApp && viewerApp.getCurrentViewer() != null) {
                viewerApp.getCurrentViewer().finish();
            }
        }

        $scope.$on('$destroy', function () {
            shutDownViewer();
        });

        function saveToBackend() {
            vm.showSaveSpinner = true;
            viewerService.save2dState(vm.snapshots, vm.modelId, vm.pagesCreated)
                .then(function (resp) {
                    if(!vm.pagesCreated){
                        getPages();
                    }
                    vm.pagesCreated = resp.data;
                    vm.showSaveSpinner = false;
                });
        }

        function createPageSnapshots() {
            if (vm.pdfPages.length === 0) {
                for (var i = 0; i < viewables.length; i++) {
                    var number = viewables[i].data.order;
                    var pageName = 'Page ' + number;
                    vm.pdfPages[number - 1] = {stateName: pageName, stateId: i};
                }
                vm.snapshots = vm.pdfPages;
                saveToBackend();
            }
        }

        function goToPage(stateId) {
            hideCreateNewPart();
            viewerLoaded = false;
            viewerHelperService.showSpinner();
            viewerApp.selectItem(viewables[stateId].data, onItemLoadSuccess, onItemLoadFail);
            viewerHelperService.setCurrentSelectedPart(stateId);
            for (var i = 0; i < vm.pdfPages.length; i++) {
                vm.pdfPages[i].selected = (vm.pdfPages[i].stateId === stateId);
            }
            CURRENT_SELECTED_PART = stateId;
        }

        function editPDFName(stateId) {
            var selectedPage = vm.pdfPages.find(function (page) {
                return page.stateId === stateId;
            });

            var modalInstance = $uibModal.open({
                templateUrl: 'features/viewer/extensions/editName/editNameModal.html',
                controller: 'EditNameModalController as editNameCtrl',
                resolve: {
                    stateName: function () {
                        return selectedPage.stateName;
                    }
                }
            });

            modalInstance.result
                .then(function (newName) {
                    if (newName !== '' && newName !== undefined) {
                        for (var i = 0; i < vm.pdfPages.length; i++) {
                            if (vm.pdfPages[i].stateId === stateId) {
                                vm.pdfPages[i].stateName = newName;
                                vm.snapshots = vm.pdfPages;
                                saveToBackend();
                                break;
                            }
                        }
                    }
                });
        }

        function openCustomerViewerPreviewPage() {
            var openUrlNW = $state.href("customerPdfViewer", {
                machineName: vm.machineName,
                autodeskURN: autodeskURN,
                modelId: vm.modelId,
                viewableName: vm.viewableName,
                productId: $stateParams.productId
            }, {absolute: true});
            $window.open(openUrlNW, '_blank');
        }

        function backToViewables() {
            $state.go("productsModels", {
                productId: $stateParams.productId,
                machineName: $stateParams.machineName
            });
        }

        function goBackToPartSearch() {
            $window.sessionStorage.setItem('fromWhereUsedModal', 'false');
            $state.go('parts.partsSearch');
        }

        function updateNonModeledParts() {
            $timeout(function () {

                viewerService.addPartToNonModeledParts(vm.createdParts, CURRENT_SELECTED_PART, vm.modelId)
                    .then(function (response) {
                        viewerHelperService.selectParts([]);
                        if (response.data) {
                            viewerBannerService.setNotification('SUCCESS', NON_MODELLED_SUCCESS, 2000);
                            hideCreateNewPart();
                            if (vm.createdParts && vm.createdParts.length !== 0) {
                                $rootScope.$broadcast('non-modeled-part-added', CURRENT_SELECTED_PART);
                            } else {
                                $rootScope.$broadcast('non-modeled-part-deleted', CURRENT_SELECTED_PART);
                            }
                        } else {
                            addNonModeledPartFailed();
                        }

                    }, addNonModeledPartFailed);
            }, 1);
        }

        function addNonModeledPartFailed() {
            viewerBannerService.setNotification('WARN', WENT_WRONG, 5000);
            console.error("Failed to create non modeled parts. ");
            hideCreateNewPart();
        }

        function getNonModeledParts() {
            var selectedPart = CURRENT_SELECTED_PART;
            return viewerService.getNonModeledParts(selectedPart, vm.modelId);
        }

        function isCreateNewPartVisible() {
            return vm.showCreateNewPart;
        }

        function hideCreateNewPart() {
            viewerHelperService.selectParts([]);
            vm.showCreateNewPart = false;
            vm.isNonModeledPartsActive = false;
        }

        function openAddParts() {
            vm.showCreateNewPart = true;
        }
    }
})();
