<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" ng-click="registMachineCtrl.cancel()" aria-label="Close">
        <i class="fa fa-close" aria-hidden="true"></i>
    </button>
    <h2 class="modal-title" translate>REGISTER.MACHINE.TITLE</h2>
</div>

<div class="modal-body">
    <form ng-submit="registMachineCtrl.submit()">
        <div class="input-group">
            <label translate>REGISTER.MACHINE.SELECT_RANGE</label>
            <div class="select-box">
                <select
                    ng-model="registMachineCtrl.rangeSelected"
                    ng-options="range.name for range in registMachineCtrl.ranges | orderBy:'name'"
                    ng-change="registMachineCtrl.onRangeChanged()"
                ></select>
                <div class="select-arrow"></div>
            </div>
        </div>

        <div class="input-group">
            <label translate>REGISTER.MACHINE.SELECT_MACHINE</label>

            <div
                ng-dropdown-multiselect
                class="select-check-box multicheckbox"
                ng-required="true"
                options="registMachineCtrl.manuals | orderBy: 'id'"
                selected-model="registMachineCtrl.manualSelected"
                extra-settings="registMachineCtrl.manualDropdownSetting"
                translation-texts="registMachineCtrl.manualDropdownTextSettings"
                disabled="registMachineCtrl.disableSelectRange"
                events="multiSelectEvent"
            ></div>
        </div>

        <div class="input-group">
            <label translate>REGISTER.MACHINE.ENTER_SERIAL_NUMBER</label>
            <input
                type="text"
                ng-required="true"
                ng-model="createNewAddressCtrl.data.city"
                placeholder="{{'REGISTER.MACHINE.ENTER_SERIAL_NUMBER' | translate}}"
            />
        </div>
    </form>

    <div class="input-group mb-0">
        <label translate>REGISTER.MACHINE.CURRENTLY_SELECTED_MACHINES</label>
    </div>

    <table class="table table-bordered">
        <thead>
            <tr>
                <th translate>REGISTER.MACHINE.MACHINE_NAME</th>
                <th translate>REGISTER.MACHINE.RANGE_NAME</th>
                <th translate>GENERAL.ACTIONS</th>
            </tr>
        </thead>
        <tbody>
            <tr ng-repeat="item in registMachineCtrl.machines">
                <td data-label="{{ 'REGISTER.MACHINE.MACHINE_NAME' | translate}}">{{ item.manualName }}</td>
                <td data-label="{{ 'REGISTER.MACHINE.RANGE_NAME' | translate}}">{{ item.rangeName }}</td>
                <td data-label="{{ 'GENERAL.ACTIONS' | translate}}">
                    <button class="btn secondary small danger" ng-click="registMachineCtrl.removeMachine(item, true)">
                        <i class="fa fa-trash-o"></i>
                    </button>
                </td>
            </tr>

            <tr ng-if="!registMachineCtrl.machines.length > 0">
                <td class="flex-start noPartsBG" colspan="10" translate>REGISTER.MACHINE.NO_MACHINE</td>
            </tr>
        </tbody>
    </table>

    <div class="modal-actions">
        <button class="btn secondary" ng-click="registMachineCtrl.cancel()" translate>GENERAL.CANCEL</button>
        <button class="btn primary" ng-disabled="!registMachineCtrl.machines.length" ng-click="registMachineCtrl.save()" translate>
            GENERAL.SAVE
        </button>
    </div>
</div>
