(function () {
    'use strict';

    angular
        .module('app.services')
        .factory('pdfMakerService', pdfMakerService);

    pdfMakerService.$inject = ['$q', '$timeout', '$http', 'apiConstants', 'softCopyService', 'manufacturerService', '$rootScope', 'headerBannerService', '$translate'];

    function pdfMakerService($q, $timeout, $http, apiConstants, softCopyService, manufacturerService, $rootScope, headerBannerService, $translate) {
        var WENT_WRONG;
        $translate(['GENERAL.WENT_WRONG'])
            .then(function (resp) {
                WENT_WRONG = resp["GENERAL.WENT_WRONG"];
            });

        var dd = {};
        var manufacturer = {};
        var logo;
        var currentSoftCopy = {};

        var tableHeaderRow = [{text: 'Pos', style: 'tableHeader'},
            {text: 'Kit', style: 'tableHeader'},
            {text: 'Included In', style: 'tableHeader'},
            {text: 'Part No', style: 'tableHeader'},
            {text: 'Description', style: 'tableHeader'},
            {text: 'Qty', style: 'tableHeader'}
        ];

        var styles = {
            titlePage: {
                alignment: 'center',
                fontSize: 25,
                bold: true,
                color: '#222'
            },
            header: {
                fontSize: 18,
                bold: true,
                margin: [100, 0, 0, 16],
                color: '#222'
            },
            subheader: {
                fontSize: 16,
                bold: true,
                margin: [0, 16, 0, 8],
                color: '#222'
            },
            tableContent: {
                alignment: 'center',
                fontSize: 6,
                color: '#222'
            },
            tableHeader: {
                alignment: 'center',
                bold: true,
                fontSize: 8,
                color: '#222'
            }
        };

        return {
            createSoftCopyPdf: createSoftCopyPdf
        };

        function getPageTitle(page) {
            return {
                style: 'header',
                text: page.title,
                tocItem: true,
                pageBreak: 'before'
            };

        }

        function getPageImageDataUrl(page) {
            return new Promise((resolve, reject) => {
                var baseImage = new Image();

                baseImage.setAttribute('crossOrigin', 'anonymous');

                baseImage.onload = function () {

                    var canvas = document.createElement("canvas");
                    canvas.width = this.width;
                    canvas.height = this.height;
                    var context = canvas.getContext('2d');
                    context.drawImage(baseImage, 0, 0);

                    var dataUrl = canvas.toDataURL("image/png");
                    resolve({
                        page: page,
                        dataUrl: dataUrl
                    });
                };
                baseImage.onerror = reject;

                baseImage.src = page.image + "?" + new Date().getTime();

            })
        }

        function getLogoImageDataUrl(url) {

            return new Promise((resolve, reject) => {
                var baseImage = new Image();

                baseImage.setAttribute('crossOrigin', 'anonymous');

                baseImage.onload = function () {

                    var canvas = document.createElement("canvas");
                    canvas.width = this.width;
                    canvas.height = this.height;
                    var context = canvas.getContext('2d');
                    context.drawImage(baseImage, 0, 0);

                    var dataUrl = canvas.toDataURL("image/png");
                    resolve(dataUrl);
                };
                baseImage.onerror = reject;

                if (url) {
                    baseImage.src = url + "?" + new Date().getTime();

                } else {
                    baseImage.src = "https://cadshare-dev-bucket.s3.amazonaws.com/117e-7e09-a3b6-7a16-34e2-1b7d-02df-cca3.png";
                }
            })
        }

        function getPageImage(dataUrl) {
            return {
                image: dataUrl,
                width: 520,
                margin: [0, 8, 0, 16]
            };

        }

        function findValueById(kitToReference, id) {
            for (var [key, value] of kitToReference.entries()) {
                if (key.id === id) {
                    return value;
                }
            }
            return undefined;

        }

        function getIncludedIn(kits, kitToReference) {

            var inKits = '';
            for (var i = 0; i < kits.length; i++) {
                if (i !== 0) {
                    inKits += ', ';
                }
                inKits += findValueById(kitToReference, kits[i].id);
            }
            return inKits;

        }

        function getPageTableBody(page) {
            // body.push(tableHeaderRow);
            var parts = [];
            var kitToReference = new Map();

            var kitLetter = 'A';

            function incrementKitLetter() {
                kitLetter = String.fromCharCode(kitLetter.charCodeAt(0) + 1);

            }

            function kitIsAlreadyInMap(kitToCheck) {
                for (var kit of kitToReference.keys()) {
                    if (kitToCheck.id === kit.id) {
                        return true;
                    }
                }
                return false;

            }

            for (var i = 0; i < page.parts.length; i++) {
                var partDetails = page.parts[i];
                var part = partDetails.part;

                var kits = partDetails.kits;
                if (kits) {
                    for (var j = 0; j < kits.length; j++) {
                        if (!kitIsAlreadyInMap(kits[j])) {
                            kitToReference.set(kits[j], kitLetter);
                            incrementKitLetter();
                        }
                    }

                }
                parts.push([
                    {
                        text: i + 1,
                        style: 'tableContent'
                    }, '', {
                        text: kits ? getIncludedIn(kits, kitToReference) : '',
                        style: 'tableContent'
                    }, {
                        text: part.partNumber,
                        style: 'tableContent'
                    }, {
                        text: part.partDescription ? part.partDescription : '',
                        style: 'tableContent',
                        alignment: 'left'
                    }, {
                        text: 1,
                        style: 'tableContent'
                    }]);
            }

            var kitRecords = [];
            for (var [kit, reference] of kitToReference) {
                kitRecords.push(['', {
                    text: reference,
                    style: 'tableContent'
                }, '', {
                    text: kit.title,
                    style: 'tableContent'
                }, {
                    text: kit.description,
                    alignment: 'left',
                    style: 'tableContent'
                }, ''

                ])

            }
            var body = [];

            body.push(JSON.parse(JSON.stringify(tableHeaderRow)));
            return body.concat(kitRecords).concat(parts);

        }

        function getPageTable(page) {
            return {
                table: {
                    heights: 10,
                    headerRows: 1,
                    widths: [25, 45, 45, 100, 225, 30],
                    body: getPageTableBody(page)

                },
                layout: {
                    hLineWidth: function () {
                        return 0.5;
                    },
                    vLineWidth: function () {
                        return 0.5;
                    },
                    fillColor: function (rowIndex, node, columnIndex) {
                        return (rowIndex % 2 === 0) ? null : '#e0e0e0';
                    }
                }
            };

        }

        function createPdfContent(pages, i) {
            return getPageImageDataUrl(pages[i]).then(function (returnedObject) {
                dd.content.push(getPageTitle(returnedObject.page), getPageImage(returnedObject.dataUrl),
                    getPageTable(returnedObject.page));
                i++;
                if (i < pages.length) {
                    return createPdfContent(pages, i);
                }
            });
        }

        function createDocumentDefinition(pages) {
            dd.footer = function (currentPage, pageCount) {
                if (currentPage !== 1) {
                    return {alignment: 'center', text: currentPage.toString() + ' of ' + pageCount}
                }
            };
            dd.header = function (currentPage) {
                if (currentPage !== 1 && logo != null) {
                    return {
                        image: logo,
                        width: 100,
                        margin: [20, 16, 0, 16]
                    };
                }
            };
            dd.styles = styles;

            dd.content = logo !== null ?
                [
                    {
                        image: logo,
                        width: 200,
                        alignment: 'center',
                        margin: [0, 200, 0, 0]
                    },
                    {
                        text: currentSoftCopy.name,
                        style: 'titlePage',
                        pageBreak: 'after'
                    }, {
                    toc: {
                        title: {text: 'Contents', style: 'header'}
                    }
                }] : [
                    {
                        text: currentSoftCopy.name,
                        style: 'titlePage',
                        pageBreak: 'after'
                    }, {
                        toc: {
                            title: {text: 'Contents', style: 'header'}
                        }
                    }];
            return createPdfContent(pages, 0);

        }

        function getSoftCopyDetailsSuccess(response) {
            var pdfPages = response.data;

            var pages = [];
            for (var i = 0; i < pdfPages.length; i++) {
                var pageData = {
                    title: pdfPages[i].stateName,
                    image: pdfPages[i].highResImgUrl,
                    parts: pdfPages[i].parts,
                    kits: pdfPages[i].kits
                };
                pages.push(pageData);

            }
            createDocumentDefinition(pages)
                .then(function () {
                    pdfMake.createPdf(dd)
                        .download(currentSoftCopy.name, clearSpinner());
                });
        }

        function getSoftCopyDetailsFailed() {
            headerBannerService.setNotification('ERROR', WENT_WRONG, 10000);
            clearSpinner();
        }

        function clearSpinner() {
            $rootScope.$broadcast("hideSpinner")
        }

        function getLogoImageDataUrlSuccess(logoDataUrl) {
            logo = logoDataUrl;
            softCopyService.getSoftCopyDetailsForViewable(currentSoftCopy.viewableId)
                .then(getSoftCopyDetailsSuccess, getSoftCopyDetailsFailed);
        }

        function fetchManufacturerDetails(response) {
            manufacturer = response.data;
            if (manufacturer.logoUrl === null) {
                getLogoImageDataUrlSuccess(null);
            } else {
                getLogoImageDataUrl(manufacturer.logoUrl)
                    .then(getLogoImageDataUrlSuccess);
            }
        }

        function createSoftCopyPdf(softCopy) {
            currentSoftCopy = softCopy;
            manufacturerService.fetchManufacturerDetails()
                .then(fetchManufacturerDetails);
        }
    }
})
();
