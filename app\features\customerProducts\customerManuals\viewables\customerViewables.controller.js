(function () {
    'use strict';

    angular
        .module('app.products')
        .controller('CustomerViewablesController', CustomerViewablesController);

    CustomerViewablesController.$inject = ['publicationService', 'customerModelService', '$state', '$stateParams'];

    function CustomerViewablesController(publicationService, customerModelService, $state, $stateParams) {
        var vm = this;
        var manualId = $stateParams.manualId;

        vm.goToViewer = goToViewer;
        vm.isReady = false;

        initialize();

        function initialize() {
            publicationService.getPublication(manualId)
                .then(getPublicationSuccess)
                .catch(getPublicationFailed);
        }

        function getPublicationSuccess(response) {
            vm.modelList = response.data.viewables || [];
            vm.totalItems = vm.modelList.length;
            
            // Fetch additional model details
            if (vm.modelList.length > 0) {
                customerModelService.fetchManualModels(manualId)
                    .then(fetchModelsSuccess)
                    .catch(fetchModelsFailed);
            } else {
                vm.isReady = true;
            }
        }

        function fetchModelsSuccess(response) {
            var modelData = response.data;
            
            // Merge model data with viewables
            vm.modelList = vm.modelList.map(viewable => {
                var model = modelData.find(m => m.modelId === viewable.id);
                return model ? Object.assign({}, viewable, model) : viewable;
            });
            
            vm.isReady = true;
        }

        function fetchModelsFailed(error) {
            vm.isReady = true;
        }

        function getPublicationFailed(error) {
            vm.modelList = [];
            vm.isReady = true;
        }

        function goToViewer(viewable) {
            var stateName = "customerViewer";
            $state.go(stateName, {
                viewableName: viewable.name,
                onBehalfOf: $stateParams.onBehalfOf,
                manualId: viewable.manualId,
                productId: $stateParams.productId,
                roomGuid: null,
                machineName: viewable.machineName,
                modelId: viewable.modelId,
                autodeskURN: viewable.autodeskURN,
                translateType: viewable.translateType
            });
        }
    }
})();
