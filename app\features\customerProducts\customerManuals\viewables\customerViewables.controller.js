(function () {
    'use strict';

    angular
        .module('app.products')
        .controller('CustomerViewablesController', CustomerViewablesController);

    CustomerViewablesController.$inject = ['customerModelService', '$state', '$stateParams'];

    function CustomerViewablesController(customerModelService, $state, $stateParams) {
        var vm = this;
        var manualId = $stateParams.manualId;

        vm.goToViewer = goToViewer;

        vm.machineImageURL = '';

        initialize();

        function initialize() {
            customerModelService.fetchManualModels(manualId)
                .then(fetchModelsSuccess)
                .catch(fetchModelsFailed);
        }

        function fetchModelsSuccess(response) {
            vm.modelList = response.data;
            vm.totalItems = vm.modelList.length;
            for (var i = 0; i < vm.modelList.length; i++) {
                if (vm.modelList[i].is2d) {
                    vm.modelList[i].thumbnailUrl = "./images/PDF-placeholder.png";
                }
            }
            vm.isReady = true;
        }

        function fetchModelsFailed(error) {
            vm.modelsSuccessMessage = false;
        }

        function goToViewer(model) {
            var stateName = model.is2d ? "customerPdfViewer"
                : "customerViewer";
            $state.go(stateName, {
                machineName: model.machineName,
                autodeskURN: model.autodeskURN,
                modelId: model.modelId,
                viewableName: model.modelName,
                onBehalfOf: $stateParams.onBehalfOf,
                manualId: manualId,
                translateType: model.translateType,
                productId: $stateParams.productId,
                roomGuid: null
            });
        }
    }
})();
