// =============================================================================
// Dropdown
// =============================================================================

.sub-popup {
  position: absolute;
  text-transform: none;
  top: 70px;
  background: $white;
  padding: $spacing*4;
  @include border-radius($border-radius);
  border: 1px solid $divider-color;
  z-index: 10;
  -webkit-box-shadow: 0px 2px 6px 0px rgba($black, 0.26);
  -moz-box-shadow: 0px 2px 6px 0px rgba($black, 0.26);
  box-shadow: 0px 2px 6px 0px rgba($black, 0.26);

  &:after,
  &:before {
    bottom: 100%;
    right: 10px;
    border: solid transparent;
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
  }

  &:after {
    border-color: rgba(255, 255, 255, 0);
    border-bottom-color: $white;
    border-width: 10px;
  }

  &:before {
    border-color: rgba(255, 255, 255, 0);
    border-width: 17px;
  }
}

.sub-popupwindow {
  position: absolute;
  text-transform: none;
  top: 70px;
  background: $white;
  padding: $spacing*4;
  @include border-radius($border-radius);
  border: 1px solid $divider-color;
  z-index: 10;
  -webkit-box-shadow: 0px 2px 6px 0px rgba($black, 0.26);
  -moz-box-shadow: 0px 2px 6px 0px rgba($black, 0.26);
  box-shadow: 0px 2px 6px 0px rgba($black, 0.26);

  &:after,
  &:before {
    bottom: 100%;
    border: solid transparent;
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
    right: 5px;
  }

  &:after {
    border-color: rgba(255, 255, 255, 0);
    border-bottom-color: $white;
    border-width: 10px;
  }

  &:before {
    border-color: rgba(255, 255, 255, 0);
    border-width: 17px;
  }
}

.dropdown-toggle {

  &:hover {
    .sub-popup {
      display: block;
    }
  }

  .sub-popup {
    top: 30px;
    right: -9px;
    margin-left: -90px;
    display: none;
    padding: $spacing;

    .more-options {
      @extend %clearlist;
      width: 100%;
      right: 0;

      li {
        position: relative;
        width: 100%;
        line-height: 2;
        margin-left: 0;
        text-align: left;
        font-weight: 400;
        font-size: 1em;

        a:hover {
          color: darken($blue,10%);
          text-decoration: underline;
        }

        a.delete:hover {
          color: $red;
          text-decoration: underline;
        }

        input {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          z-index: 2;
          opacity: 0;

          &:hover {
            cursor: pointer;
          }
        }
      }

      &.user-setting-menu-active {
        display: block;
      }
    }
  }
}

.dropdown-toggle-window {

  &:hover {
    .sub-popupwindow {
      visibility: visible;
    }
  }

  .sub-popupwindow {
    top: $spacing*4;
    // margin-left: -90px;
    margin-left: -157px;
    visibility: hidden;
    padding: $spacing;

    .more-options {
      @extend %clearlist;
      width: 160px;
      right: 0;

      li {
        position: relative;
        width: 100%;
        line-height: 2;
        margin-left: 0;
        text-align: left;
        font-weight: 400;
        font-size: 1em;

        a:hover {
          color: darken($blue,10%);
          text-decoration: underline;
        }

        a.delete:hover {
          color: $red;
          text-decoration: underline;
        }

        input {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          z-index: 2;
          opacity: 0;

          &:hover {
            cursor: pointer;
          }
        }
      }

      &.user-setting-menu-active {
        visibility: visible;
      }
    }
  }
  &:after{
    display: inline-block;
    margin-left: .255em;
    vertical-align: .255em;
    content: "";
    border-top: .3em solid;
    border-right: .3em solid transparent;
    border-bottom: 0;
    border-left: .3em solid transparent;
  }
}

.dot {
  height: 15px;
  width: 15px;
  margin: 6px 0px 0px 0px;
  border-radius: 50%;
  display: inline-block;
}
.cross {
  color: red;
}

.multicheckbox {
  color: $textdark;
  width: 100%;
  float: left;
  
  .dropdown-menu {
    padding: $spacing*2;
    right: auto;
    left:0;
    width: 100%;
  
    li {  
      margin:$spacing 0;
      padding:0;
      float: left;
      width: 100%;
      position: relative;
      
      a {
        padding: 0 0 0 $spacing*3 ;
        cursor: pointer;
      }
    
    }

     li.active:after {
        content: "\f046";
        font-family: FontAwesome;
        font-style: normal;
        font-weight: normal;
        text-decoration: inherit;
        font-size: 18px;
        position: absolute;
        top:0;
        bottom:0;
        pointer-events: none;
      }

     li:not(.active):after {
        content: "\f096";
        font-family: FontAwesome;
        font-style: normal;
        font-weight: normal;
        text-decoration: inherit;
        font-size: 18px;
        position: absolute;
        top:0;
        bottom:0;
        pointer-events: none;
      }

  }


  
  .btn-group {
    width:100%;
    .dropdown-toggle {
      border-top-left-radius: $border-radius;
      border-bottom-left-radius: $border-radius;
      display: block;
      width:100%;
    }
  }
}

div.multicheckbox li a[disabled]{
 color:lightgrey;
}

li.ng-scope.active > a > span:nth-child(2) > span {
  font-weight: bold;
}

.select-box-new {
  padding: 0;
  display: inline-block;
  width: 500px;
  max-width: 100%;
  cursor: pointer;
  font-family: "Open Sans", sans-serif;
  font-size: 1em;
  line-height: 1.2em;
  outline: 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  height: $spacing*5;
  color: #373D45;
  background: #F2F6F9;
  border: 1px solid #D2DAE5;
  @include border-radius(5px);
  display: block;
}

  .last-item .dropdown-toggle:last-child .sub-popup:last-child {
    bottom: 30px;
    top: initial;
    right: -9px;
  }

  .last-item .sub-popup:last-child:after, .last-item .sub-popup:last-child:before {
    top: 100%;
    right: 10px;
    transform: rotate(180deg);
  }
