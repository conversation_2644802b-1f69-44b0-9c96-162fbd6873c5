(function () {
    "use strict";

    angular.module("app.products").controller("CustomerProductsController", CustomerProductsController);

    angular.module("app.products").directive("matchHeight", [
        "$window",
        "$timeout",
        function ($window, $timeout) {
            return {
                restrict: "A",
                scope: {
                    rangesData: "=",
                    toggleValue: "=",
                },
                link: function (scope, element, attrs) {
                    let initialHeightSet = false;

                    const getTargetElement = function () {
                        return document.querySelector(".customerProductsItemContainer");
                    };

                    function adjustHeight() {
                        if (!initialHeightSet) {
                            const targetElement = getTargetElement();
                            if (targetElement) {
                                const targetHeight = targetElement.offsetHeight;
                                element[0].style.maxHeight = targetHeight * 2 + "px";
                                initialHeightSet = true;
                            }
                        }
                    }

                    function waitForImages() {
                        const images = document.querySelectorAll(".customerProductsImage img");
                        let loadedImages = 0;
                        Array.from(images).forEach((img) => {
                            if (img.complete) {
                                loadedImages++;
                            } else {
                                img.addEventListener("load", () => {
                                    loadedImages++;
                                    if (loadedImages === images.length) {
                                        $timeout(adjustHeight, 0);
                                    }
                                });
                            }
                        });

                        // In case images are already loaded
                        if (loadedImages === images.length) {
                            $timeout(adjustHeight, 0);
                        }
                    }

                    scope.$watch("rangesData", function (newVal) {
                        if (newVal && newVal.length > 0) {
                            waitForImages();
                        }
                    });

                    scope.$watch("toggleValue", function (newVal, oldVal) {
                        if (newVal !== oldVal) {
                            waitForImages();
                        }
                    });

                    scope.$watch(
                        function () {
                            return attrs.matchHeight;
                        },
                        function () {
                            waitForImages();
                        }
                    );

                    angular.element($window).bind("resize", function () {
                        scope.$apply(function () {
                            waitForImages();
                        });
                    });

                    scope.$on("$destroy", function () {
                        angular.element($window).unbind("resize");
                    });
                },
            };
        },
    ]);

    CustomerProductsController.$inject = [
        "$timeout",
        "customerPublicationsService",
        "$state",
        "customerModelService",
        "viewerService",
        "viewerSettingsService",
        "ordersService",
        "securityService",
        "optionsSetService",
        "purchasableAssemblyService",
        "userService",
        "$stateParams",
        "$translate",
        "$transitions",
    ];

    function CustomerProductsController(
        $timeout,
        customerPublicationsService,
        $state,
        customerModelService,
        viewerService,
        viewerSettingsService,
        ordersService,
        securityService,
        optionsSetService,
        purchasableAssemblyService,
        userService,
        $stateParams,
        $translate,
        $transitions
    ) {
        var vm = this;
        vm.currentPage = 1;
        vm.count = 8;
        vm.itemPerPage = 8;
        vm.start = 0;
        vm.ranges = [];
        vm.publicationList = [];
        vm.totalItems;
        vm.endRecord = vm.itemPerPage;
        vm.isGridView = true;
        vm.toggleView = function () {
            vm.isGridView = !vm.isGridView;
        };
        vm.onBehalfOf =
            $stateParams.onBehalfOf && $stateParams.onBehalfOf !== "null"
                ? JSON.parse(decodeURIComponent(atob($stateParams.onBehalfOf)))
                : undefined;

        vm.searchValue = "";
        vm.pageChanged = pageChanged;
        vm.displayModels = displayModels;
        vm.filterMachinesList = filterMachinesList;
        vm.sortAndPin = sortAndPin;
        vm.hasFeaturedViewable = hasFeaturedViewable;

        var originalPublicationList = [];
        var manufacturerSubEntityId = vm.onBehalfOf ? vm.onBehalfOf.manufacturerSubEntityId : userService.getManufacturerSubEntityId();

        var MACHINE, SERIAL_NUMBER, ALL_PRODUCTS;
        $translate(["CUST_PRODUCTS.MACHINE", "CUST_PRODUCTS.SERIAL_NUMBER", "CUST_PRODUCTS.ALL_PRODUCTS"]).then(function (resp) {
            SERIAL_NUMBER = resp["CUST_PRODUCTS.SERIAL_NUMBER"];
            MACHINE = resp["CUST_PRODUCTS.MACHINE"];
            ALL_PRODUCTS = resp["CUST_PRODUCTS.ALL_PRODUCTS"];
            vm.sortBy = [
                { name: "serialNumber", value: SERIAL_NUMBER },
                { name: "name", value: MACHINE },
            ];
        });

        initialize();

        function initialize() {
            vm.activeTab = "ALL_PRODUCTS";
            customerPublicationsService.fetchPublicationsByPurchaser(manufacturerSubEntityId).then(fetchPublicationsSuccess).catch(fetchPublicationsFailed);

            var customerProductsRoutes = [
                "p-products",
                "customerViewables",
                "customerViewer",
                "customerPdfViewer",
                "customerManual.viewables",
                "customerManual",
                "viewables",
                "publishedProducts",
                "customerManual.kits",
                "customerManual.techDocs",
                "customerManual.videos",
            ];

            $transitions.onStart({}, (transition) => {
                var fromStateName = transition.from().name;
                var toStateName = transition.to().name;

                if (customerProductsRoutes.includes(fromStateName) && customerProductsRoutes.includes(toStateName)) {
                    return;
                } else {
                    clearActiveTab();
                }
            });
        }

        function fetchPublicationsSuccess(response) {
            var publications = Array.isArray(response.data) ? response.data : 
                                (response.data.publications ? response.data.publications : []);

            vm.publicationList = publications.map(publication => {
                return {
                    publicationId: publication.id,
                    publicationName: publication.name,
                    serialNumber: publication.serialNumber,
                    coverImageUrl: publication.coverImage ? publication.coverImage.url : 'images/placeholder.jpg',
                    attachedModels: publication.viewables ? publication.viewables.length : 0,
                    categoryName: publication.publicationCategoryName || null,
                    originalPublication: publication
                };
            }).sort((a, b) => {
                return a.publicationName.localeCompare(b.publicationName, undefined, { numeric: true, sensitivity: 'base' });
            });
            
            console.log('Mapped manualList:', vm.publicationList);
            originalPublicationList = angular.copy(vm.publicationList);
            vm.totalItems = vm.publicationList.length;
            console.log('Total items:', vm.totalItems);

            createRangeTabs();

            var storedTab = sessionStorage.getItem("activeTab") || $stateParams.activeTab;
            if (storedTab) {
                filterMachinesList(storedTab);
            }
        }

        function fetchPublicationsFailed(error) {
            vm.machinesSuccessMessage = false;
        }

        function filterMachinesList(categoryName) {
            if (categoryName === "ALL_PRODUCTS") {
                vm.publicationList = angular.copy(originalPublicationList);
                vm.ranges.forEach(function (range) {
                    range.active = false;
                });
                vm.activeTab = "ALL_PRODUCTS";
            } else {
                vm.publicationList = originalPublicationList.filter(function (element) {
                    return element.categoryName === categoryName;
                });
                vm.ranges.forEach(function (range) {
                    range.active = range.name === categoryName;
                });
                vm.activeTab = categoryName;
            }
            sessionStorage.setItem("activeTab", categoryName);
            $timeout(function () {});
        }

        function createRangeTabs() {
            var activeTabFromSession = sessionStorage.getItem("activeTab") || "ALL_PRODUCTS";
            vm.ranges = [];
            originalPublicationList.forEach(function (element) {
                var exists = _.some(vm.ranges, function (r) {
                    return r.name === element.categoryName;
                });
                if (exists) {
                    var position = _.findIndex(vm.ranges, function (item) {
                        return item.name === element.categoryName;
                    });
                    if (position !== -1) {
                        vm.ranges[position].count = ++vm.ranges[position].count;
                    }
                } else {
                    var tab = {
                        name: element.categoryName,
                        count: 1,
                        active: element.categoryName === activeTabFromSession,
                    };
                    vm.ranges.push(tab);
                }
            });
        }

        function sortAndPin(range) {
            return range.name;
        }

        function pageChanged() {
            vm.start = (vm.currentPage - 1) * vm.itemPerPage;
        }

        function getManualParams(publicationId) {
            var params = {
                manualId: publicationId,  // Keep as manualId for backward compatibility with other components
                manufacturerSubEntityId: manufacturerSubEntityId,
                onBehalfOf: vm.onBehalfOf ? btoa(encodeURIComponent(JSON.stringify(vm.onBehalfOf))) : "null",
            };
            return params;
        }

        function displayModels(manual) {
            customerModelService.setMachineImageUrl(manual.coverImageUrl);
            var publicationId = manual.publicationId;

            var activeTab;
            for (var i = 0; i < vm.ranges.length; i++) {
                if (vm.ranges[i].active) {
                    activeTab = vm.ranges[i].name;
                    break;
                }
            }
            var activeTabName = activeTab || "";

            var params = getManualParams(publicationId);
            params.activeTab = activeTabName;
            $state.go("customerManual.viewables", params);
        }

        function clearActiveTab() {
            sessionStorage.removeItem("activeTab");
        }

        function hasFeaturedViewable(manual) {
            if (!manual || !manual.originalPublication || !manual.originalPublication.viewables) {
                return false;
            }
            
            for (var i = 0; i < manual.originalPublication.viewables.length; i++) {
                if (manual.originalPublication.viewables[i].featuredViewable) {
                    return true;
                }
            }
            return false;
        }
    }
})();
