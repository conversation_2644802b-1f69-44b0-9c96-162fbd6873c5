// =============================================================================
// Tables
// =============================================================================

.product-table {
  width: 100%;
  font-family: $body-font;

  thead {
    background: $lightback;

    th {
      padding: $spacing;
      text-align: left;
      text-transform: uppercase;
      font-weight: 700;
      border-top: 1px solid $divider-color;
      border-bottom: 1px solid $divider-color;
      font-size: 0.875em;
    }
  }

  &.master-part-table{
    td, th {
      padding: 4px 0px;
      //height: 40px;
      line-height: 1.5;
      word-break: break-word;

      &:first-of-type {
        padding-left: $spacing*2;
      }

      //&:last-of-type {padding-right: $spacing*2; text-align: right;}

      input {
        margin: 0 !important;
      }
    }
  }

  td,
  th {
    padding: $spacing*2 $spacing;
    height: 40px;
    line-height: 1.5;

    &:first-of-type {
      padding-left: $spacing*4;
    }

    //&:last-of-type {padding-right: $spacing*2; text-align: right;}

    input {
      margin: 0 !important;
    }
  }

  tbody {
    tr {

    }

    tr:hover {
      .btn.secondary {
        color: $white;
        background: $blue;
        border-color: darken($blue, 5%);

        &:hover {
          background: darken($blue, 10%);
        }
      }
    }
  }

  td {
    border-bottom: 1px solid #D2DAE5;
    position: relative;
    line-height: 1.5714285714285714em;
    padding: 15px 8px 16px 8px;


    h3 {
      margin-bottom: 0;
    }

    h4 {
      margin-bottom: 0;
      font-weight: 400;
    }

    &.has-dropdown {
      .sub-popup {
        margin: 0;
      }
    }

    &.emptytable {
      text-align: center;
      font-size: 1.3em;
      padding-top: $spacing*1.5;
      padding-bottom: $spacing*1.5;
    }

    &.preloader {
      text-align: center;
      padding-top: $spacing*3;
      padding-bottom: $spacing*3;
      border-bottom: none;
    }
  }

  .clickableRow:hover {
    cursor: pointer;
    background: $lightback;
  }

  tr.unread {
    font-weight: 700;
    background: $lightgreen;

    &:hover {
      background: darken($lightgreen, 5%);
    }
  }

  .itemPrice {
    display: inline-block;
    font-size: 1em;
    font-family: $body-font;
    padding: 0 $spacing*2;
    line-height: 34px;
    border: 1px solid $divider-color;
    @include border-radius($border-radius);
    background: $lightback;
    color: $textdark;

    .priceInput {
    }
  }

  .orderItemTotal {
    width: 138px;
  }

  &.customer-viewable {
    margin-top: $spacing;
    margin-bottom: $spacing;
  }

  &.basket-table {
    margin-top: 0px !important;

    .text-right {
      text-align: right;
    }

    td {
      padding: $spacing*2;
      font-size: 1em;
      line-height: 1.3;

      &:last-of-type {
        text-align: center;
      }
    }

    th {
      line-height: 1.1;
      padding: $spacing*2;
      font-size: 0.875em;
    }

    tr {
      td {
        background: white;
        color: $textdark;
      }
    }
  }

  &.addpart-table {
    margin-top: 0px !important;

    td {
      padding: $spacing*2;
      font-size: 0.85em;
      line-height: 1.3;

      &:last-of-type {
        text-align: center;
      }
    }

    th {
      line-height: 1.1;
      padding: $spacing;
      font-size: 11px;
    }

    tr {
      td {
        background: white;
        color: $textdark;
      }
    }
  }

  &.optionSet-table {
    margin-top: 0px !important;

    .text-right {
      text-align: right;
    }

    td {
      padding: $spacing;
      font-size: 0.85em;
      line-height: 1.3;

      &:last-of-type {
        text-align: center;
      }
    }

    th {
      line-height: 1.1;
      padding: $spacing*2;
      font-size: 11px;
    }

    tr {
      td {
        background: white;
        color: $textdark;
      }
    }
  }
}

.has-dropdown::before {
  display:none;
}

@-webkit-keyframes fadeinout {
  0%, 100% {
    background: white;
    color: $textdark;
  }
  5%, 80% {
    background: lighten($green, 30%);
    color: darken($green, 15%);
  }
}

@keyframes fadeinout {
  0%, 100% {
    background: white;
    color: $textdark;
  }
  5%, 80% {
    background: lighten($green, 30%);
    color: darken($green, 15%);
  }
}

.part-header {
  padding: $spacing;
}

.hide {
  top: 0px;
  right: 0px;
  position: absolute;
  width: 60px !important;
  height: 32px;
}


.simple-table {
  width: 100%;
  font-family: $body-font;

  thead {

    th {
      text-align: left;
      font-size: 0.8em;
      text-transform: uppercase;
      font-weight: 700;
      border-bottom: 1px solid $divider-color;
    }
  }

  td,
  th {
    padding: $spacing 10px;
    font-size: 0.9em;
    line-height: 1.3;

    &:last-of-type {
      text-align: right;
    }
  }

  td {
    border-top: 1px solid $divider-color;
    position: relative;

    h3 {
      margin-bottom: 0px;
    }

  }

  tr {
    &:first-of-type {
      td {
        border-top: none;
      }
    }
  }


  .priceInput {
    display: inline-block;
    font-size: 1em;
    font-family: $body-font;
    padding: 0px 3px;
    line-height: 1.8;
    border: 1px solid $divider-color;
    @include border-radius($border-radius);
    background: $lightback;
    color: $textdark;
    width: 85%;
  }
}

.quantityInput {
  width: 100%!important;
}

.table-footer {
  border-top: 1px solid $divider-color;
  width: 100%;
  display: block;

  .right {
    padding: $spacing*2 $spacing*2 $spacing $spacing*2;
    display: flex;
    justify-content: flex-end;

    .btn {
      margin-left: 16px;
    }
  }


  .left {
    padding: $spacing*2 $spacing*2 $spacing $spacing*2;
  }

  .right,
  .left {
    display: inline-block;
  }

  .right:after,
  .left:after {
    content: none;
  }
}

td, th {
  input {
    margin: 0 !important;
  }

  .select-box {
    margin: 8px 0 0 0 !important;

  }
}

.td-background{

  background: #F2F6F9 !important;

}

.tr-border{

  border: 1px solid #D2DAE5;

}