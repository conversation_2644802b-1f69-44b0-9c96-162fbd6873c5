(function () {
    'use strict';

    angular
        .module('app.services')
        .factory('awsS3Service', awsS3Service);

    awsS3Service.$inject = ['$http', 'apiConstants', '$q', 'userService'];

    function awsS3Service($http, apiConstants, $q, userService) {
        return {
            uploadTechDoc: uploadTechDoc,
            uploadOrderPDF: uploadOrderPDF
        };

        function uploadTechDoc(file) {
            if (file.type === 'application/pdf') {
                return uploadPDF(file, 'techDoc');
            } else if (file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || file.type === 'application/vnd.ms-excel') {
                return uploadExcel(file, 'techDoc');
            } else {
                return $q.reject('Unsupported file type');
            }
        }


        function uploadExcel(file, type) {
            var promise = $q.defer();

            var newFileName = file.name.endsWith('.xlsx') ? file.name : file.name + '.xlsx';
            var blob = file.slice(0, file.size, file.type);
            var renamedFile = new File([blob], newFileName, {type: file.type});
            var endpoint = userService.isDealerPlusUser() ? '/dealerplus/aws/s3/getpresignedurl/' : '/aws/s3/getpresignedurl/';

            $http.post(apiConstants.url + endpoint + type, {
                objectKey: renamedFile.name,
                fileSizeInBytes: renamedFile.size,
                fileType: 'xlsx' // file type for Excel
            })
                .then(function getPresignedUrlSuccess(resp) {
                    return $http.put(resp.data.URL, renamedFile, {headers: {'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'}})
                        .then(function (response) {
                                if (response && response.status === 200) {
                                    var fileURL = resp.data.URL.split("?");
                                    return promise.resolve(fileURL[0]);
                                } else {
                                    return promise.reject();
                                }
                            }, function (error) {
                                return promise.reject(error);
                            }
                        );
                }, function (error) {
                    return promise.reject(error);
                });

            return promise.promise;
        }


        function uploadOrderPDF(file) {
            return uploadPDF(file, 'order');
        }

        function uploadPDF(file, type) {
            var promise = $q.defer();

            var newFileName = file.name.endsWith('.pdf') ? file.name : file.name + '.pdf';
            var blob = file.slice(0, file.size, file.type);
            var renamedFile = new File([blob], newFileName, {type: file.type});
            var endpoint = userService.isDealerPlusUser() ? '/dealerplus/aws/s3/getpresignedurl/' : '/aws/s3/getpresignedurl/'

            $http.post(apiConstants.url + endpoint + type, {
                objectKey: renamedFile.name,
                fileSizeInBytes: renamedFile.size,
                fileType: 'pdf'
            })
                .then(function getPresignedUrlSuccess(resp) {
                    return $http.put(resp.data.URL, renamedFile, {headers: {'Content-Type': renamedFile.type}})
                        .then(function (response) {
                                if (response && response.status === 200) {
                                    var imageURL = resp.data.URL.split("?");
                                    return promise.resolve(imageURL[0]);
                                } else {
                                    return promise.reject();
                                }
                            }, function (error) {
                                return promise.reject(error);
                            }
                        );
                }, function (error) {
                    return promise.reject(error);
                });

            return promise.promise;
        }
    }
})();
