<section class="px-5 pt-5">
  <p><a href="" class="dark-secondary" ng-click="manufacturerViewableCtrl.goToProducts()"><i
        class="fa fa-caret-left"></i> {{"VIEWABLE.BACK_TO_ALL_PRODUCTS" | translate}}</a></p>
  <h2 class="viewables-header">{{"VIEWABLE.LIST_OF" | translate}} "{{manufacturerViewableCtrl.machineName}}"</h2>
</section>

<section class="responsiveContainer m-5">

  <div class="responsiveContainer">

<div id="{{manufacturerViewableCtrl.isFixedHeader ? 'infiniteScrollFixedHeader' : 'infiniteScrollStaticHeader'}}"
  class="flex p-4 p-md-0">
  <search-filter class="col-12 col-md-3" state-name="'viewables'"
    on-search-change="manufacturerViewableCtrl.searchFilterChange()" value="manufacturerViewableCtrl.searchValue"
    placeholder-key="VIEWABLE.SEARCH_BY"></search-filter>

  <button class="btn primary mr-2 col-12 col-md-auto my-3 my-md-0"
    ng-click="manufacturerViewableCtrl.toggleAdvancedFilter()">
    <span class="text-white">{{'VIEWABLE.FILTER' | translate}}</span>
    <i ng-hide="manufacturerViewableCtrl.displayAdvancedFilter" class="pl-2 fa fa-plus"></i>
    <i ng-show="manufacturerViewableCtrl.displayAdvancedFilter" class="pl-2 fa fa-minus"></i>
  </button>
  <button class="btn secondary col-12 col-md-auto mb-3 mb-md-0" href=""
    ng-disabled="!manufacturerViewableCtrl.filter_createdBy && !manufacturerViewableCtrl.filter_status && !manufacturerViewableCtrl.searchValue"
    ng-click="manufacturerViewableCtrl.clearFilter()" translate>VIEWABLE.CLEAR_ALL</button>

  <button class="btn primary ml-auto mr-4 col-12 col-md-auto create-machine"
    ng-click="manufacturerViewableCtrl.openUploadModal(machine)" translate>VIEWABLE.UPLOAD_VIEWABLE</button>

  <div class="gloBl-filter-panel cadGap mb-4" ng-show="manufacturerViewableCtrl.displayAdvancedFilter">

    <div class="filter-option">
      <div class="input-icon-wrap first-filter">
        <small ng-show="!manufacturerViewableCtrl.filter_createdBy" translate>VIEWABLE.CREATED_BY</small>
        <div class="select-box">
          <select ng-change="manufacturerViewableCtrl.applyFilter()"
            ng-model="manufacturerViewableCtrl.filter_createdBy"
            ng-options="createdByUser as createdByUser for createdByUser in manufacturerViewableCtrl.createdByUsers">
          </select>
        </div>
        <i ng-class="{'selectActive': manufacturerViewableCtrl.filter_createdBy}" class="fa fa-filter"
          aria-hidden="true"></i>
      </div>
    </div>
    <div class="filter-option">
      <div class="input-icon-wrap first-filter">
        <small ng-show="!manufacturerViewableCtrl.filter_status" translate>VIEWABLE.STATUS</small>
        <div class="select-box">
          <select ng-change="manufacturerViewableCtrl.applyFilter()" ng-change="manufacturerViewableCtrl.applyFilter()"
            ng-model="manufacturerViewableCtrl.filter_status"
            ng-options="status as status for status in manufacturerViewableCtrl.statuses">
          </select>
        </div>
        <i ng-class="{'selectActive': manufacturerViewableCtrl.filter_status}" class="fa fa-filter"
          aria-hidden="true"></i>
      </div>
    </div>
  </div>
</div>

    <table class="table table-bordered table_resp_bomExport">

      <thead>
        <tr>
          <th ng-class="manufacturerViewableCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'"
            ng-click="manufacturerViewableCtrl.viewable_sort='modelName'; manufacturerViewableCtrl.sortReverse = !manufacturerViewableCtrl.sortReverse"
            translate>VIEWABLE.VIEWABLE</th>
          <th ng-class="manufacturerViewableCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'"
            ng-click="manufacturerViewableCtrl.viewable_sort='originalFilename'; manufacturerViewableCtrl.sortReverse = !manufacturerViewableCtrl.sortReverse"
            translate>
            VIEWABLE.FILE_NAME</th>
          <th ng-class="manufacturerViewableCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'"
            ng-click="manufacturerViewableCtrl.viewable_sort='autodeskStatusDisplay'; manufacturerViewableCtrl.sortReverse = !manufacturerViewableCtrl.sortReverse"
            translate>
            VIEWABLE.STATUS</th>
          <th ng-class="manufacturerViewableCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'"
            ng-click="manufacturerViewableCtrl.viewable_sort='is2d'; manufacturerViewableCtrl.sortReverse = !manufacturerViewableCtrl.sortReverse">
            2D/3D</th>
          <th ng-class="manufacturerViewableCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'"
            ng-click="manufacturerViewableCtrl.viewable_sort='createdDate'; manufacturerViewableCtrl.sortReverse = !manufacturerViewableCtrl.sortReverse"
            translate>
            VIEWABLE.CREATED_DATE</th>
          <th ng-class="manufacturerViewableCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'"
            ng-click="manufacturerViewableCtrl.viewable_sort='createdByUserFullName'; manufacturerViewableCtrl.sortReverse = !manufacturerViewableCtrl.sortReverse"
            translate>
            VIEWABLE.CREATED_BY</th>
          <th ng-class="manufacturerViewableCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'" translate>
            VIEWABLE.ACTIONS</th>
        </tr>
      </thead>

      <tbody infinite-scroll="manufacturerViewableCtrl.loadMoreInfiniteScroll()" infinite-scroll-distance="3"
        infinite-scroll-disabled="manufacturerViewableCtrl.loadingInfiniteScrollData">
        <tr
          ng-repeat-start="model in manufacturerViewableCtrl.modelList | orderBy:manufacturerViewableCtrl.viewable_sort:manufacturerViewableCtrl.sortReverse | filter :manufacturerViewableCtrl.searchValue | filter : manufacturerViewableCtrl.filterValue : true"
          ng-class="{'last-item': $last}">

          <td data-label="{{'VIEWABLE.VIEWABLE' | translate}}">{{model.modelName}}</td>
          <td data-label="{{'VIEWABLE.FILE_NAME' | translate}}">{{model.originalFilename}}</td>
          <td data-label="{{'VIEWABLE.STATUS' | translate}}">
            <span
              ng-class="model.autodeskStatus.includes('PROPERTIES_PROCESSED') ? 'badge-pill primary' : 'badge-pill'">
              {{model.autodeskStatusDisplay | capitalize}} </span>
            <a title="View Warnings" href="" class="dark-secondary fa fa-exclamation-triangle pull-right"
              ng-click="manufacturerViewableCtrl.showAutodeskWarnings(model)"
              ng-if="model.autodeskStatus == 'PROPERTIES_PROCESSED_WITH_WARNINGS'"></a>
          </td>
          <td data-label="2D/3D">
            <div ng-hide="model.autodeskStatusDisplay == 'PREPARING'">{{model.is2d ? '2D' : '3D'}}</div>
            <!-- <i title="Model linked to by {{model.linkedPartCount}} parts" ng-show="model.linkedPartCount > 0" class="fa fa-paperclip" aria-hidden="true"></i>-->
          </td>
          <td data-label="{{'VIEWABLE.CREATED_DATE' | translate}}">{{model.createdDate | date : "d MMMM y"}}</td>
          <td data-label="{{'VIEWABLE.CREATED_BY' | translate}}">{{model.createdByUserFirstName}}
            {{model.createdByUserLastName}}</td>

          <td class="has-dropdown mobile-right-aligned-btn">
            <div class="btn-group">
              <button ng-disabled="!model.autodeskStatus.includes('PROPERTIES_PROCESSED')" href=""
                class="btn xsmall secondary" ng-click="manufacturerViewableCtrl.openAdminViewerPage(model)"
                translate>VIEWABLE.VIEW
              </button>
              <div href="" class="btn xsmall secondary main-action dropdown-toggle" data-toggle="dropdown"
                aria-haspopup="true" aria-expanded="false">
                <div class="sub-popup viewables">
                  <ul class="more-options">
                    <li title="List Viewables">
                      <a href="" class="dark-secondary" ng-click="manufacturerViewableCtrl.openAdminViewerPage(model)"
                        ng-if="model.autodeskStatus.includes('PROPERTIES_PROCESSED')"><i class="fa fa-fw fa-eye"></i>
                        {{'VIEWABLE.VIEW' | translate}}</a>
                    </li>
                    <li title="Edit">
                      <a href="" class="dark-secondary" ng-click="manufacturerViewableCtrl.editModelPopup(model)"><i
                          class="fa fa-fw fa-pencil"></i> {{"VIEWABLE.EDIT_VIEWABLE" | translate}}</a>
                    </li>
                    <li ng-hide="model.is2d" title="Upload"
                      ng-if="model.autodeskStatus.includes('PROPERTIES_PROCESSED')">
                      <a href="" class="dark-secondary"
                        ng-click="manufacturerViewableCtrl.exportPartDetailsToCSV(model)"><i
                          class="fa fa-fw fa-save"></i>
                        {{"VIEWABLE.EXPORT_BOM" | translate}}</a>
                    </li>
                    <li ng-hide="model.is2d" title="BOM Upload"
                      ng-if="model.autodeskStatus.includes('PROPERTIES_PROCESSED')">
                      <a href="" class="dark-secondary"
                        ng-click="manufacturerViewableCtrl.createUploadCsvDetailsPopUp(model)"><i
                          class="fa fa-fw fa-upload"></i>
                        {{"VIEWABLE.UPLOAD_BOM" | translate}}</a>
                    </li>
                    <li ng-hide="model.is2d" title="Upload Spare IDs"
                      ng-if="model.autodeskStatus.includes('PROPERTIES_PROCESSED')">
                      <a href="" class="dark-secondary"
                        ng-click="manufacturerViewableCtrl.csvUploadSparePartDataPopUp(model)"><i
                          class="fa fa-fw fa-upload"></i>
                        {{"VIEWABLE.SPARE_UPLOAD" | translate}}</a>
                    </li>
                    <li title="Delete">
                      <a href="" class="delete" ng-click="manufacturerViewableCtrl.openDeletePopup(model)"><i
                          class="fa fa-fw fa-trash"></i> {{"VIEWABLE.DELETE" | translate}}</a>
                    </li>
                    <!--<li title="Work Instructions" ng-if="!model.is2d && model.isSetupComplete">
                  <a href="" class="dark-secondary" ng-click="manufacturerViewableCtrl.manageWorkInstructions(model)">
                    <i class="fa fa-fw fa-check-square-o"></i> {{"VIEWABLE.MANAGE_WORK_INSTRUCTIONS" | translate}}</a>
                </li>-->
                    <li title="Manage Soft Copies"
                      ng-if="!model.is2d && model.isSetupComplete && manufacturerViewableCtrl.softCopyEnabled">
                      <a href="" class="dark-secondary" ng-click="manufacturerViewableCtrl.manageSoftCopies(model)">
                        <i class="fa fa-fw fa-book"></i> {{"VIEWABLE.MANAGE_SOFT_COPIES" | translate}}</a>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </td>
        </tr>
        <tr class="p-0" ng-show="model.proccesses.length > 0" ng-repeat-end>
          <td class="p-0" colspan="7">
            <table class="table table-bordered resp_bomExport">
              <thead>
                <tr>
                  <th>{{"PARTS_UPLOAD.PROCESS" | translate}}</th>
                  <th>{{"PARTS_UPLOAD.TIME" | translate}}</th>
                  <th>{{"PARTS_UPLOAD.STATUS" | translate}}</th>
                  <th>{{"PARTS_UPLOAD.FILE" | translate}}</th>
                  <th>{{"GENERAL.DELETE" | translate}}</th>
                </tr>
              </thead>
              <tbody>
                <tr ng-repeat="process in model.proccesses">
                  <td data-label="{{'PARTS_UPLOAD.PROCESS' | translate}}">{{process.processDisplayName}}</td>
                  <td data-label="{{'PARTS_UPLOAD.TIME' | translate}}">{{process.createdDate | date : "d MMMM y h:mm
                    a"}}</td>
                  <td data-label="{{'PARTS_UPLOAD.STATUS' | translate}}">{{process.status}}</td>
                  <td data-label="{{'PARTS_UPLOAD.FILE' | translate}}">
                    <button class="btn btn-primary"
                      ng-if="process.status === 'COMPLETE' && process.process === 'VIEWABLE_BOM_EXPORT'"
                      ng-click="manufacturerViewableCtrl.downloadCSV(model, process)" translate>
                      VIEWABLE.DOWNLOAD
                    </button>
                  </td>
                  <td data-label="{{'GENERAL.DELETE' | translate}}">
                    <button class="btn btn-danger"
                      ng-click="manufacturerViewableCtrl.remove(process.id)" translate>
                    <i class="fa fa-trash"></i>
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </td>
        </tr>

        <tr ng-show="!manufacturerViewableCtrl.modelList.length > 0">
          <td colspan="7" translate>VIEWABLE.NO_VIEWABLE</td>
        </tr>

        <tr ng-hide="manufacturerViewableCtrl.isModelsLoaded" align="center">
          <td class="preloader" colspan="7"><img ng-src="images/cadpreloader.gif" class="ajax-loader" height="60"
              width="60" />
          </td>
        </tr>
      </tbody>
    </table>

  </div>

  <span ng-click="manufacturerViewableCtrl.scrollToTop()" id="backToTopBtn" title="Go to top"
    class="fas fa-arrow-alt-circle-up" ng-show="manufacturerViewableCtrl.showBackToTopButton"></span>

  </div>
  </div>
</section>