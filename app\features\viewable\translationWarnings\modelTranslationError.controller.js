(function () {
    'use strict';

    angular
        .module('app.viewable')
        .controller('ModelTranslationController', ModelTranslationController);

    ModelTranslationController.$inject = ['modelService', '$uibModalInstance', 'modalObject'];

    function ModelTranslationController(modelService, $uibModalInstance, modalObject) {

        var vm = this;

        vm.isTranslationErrors = true;
        vm.isLoaded = false;

        vm.missingWarnings = [];
        vm.emptyWarnings = [];
        vm.extraWarnings = [];

        vm.cancel = $uibModalInstance.dismiss;
        vm.showAutodeskWarnings = showAutodeskWarnings;

        initialize();

        function initialize() {
            if (modalObject) {
                showAutodeskWarnings(modalObject.modelId);
            }
        }

        function showAutodeskWarnings(model){
            modelService.fetchModelTranslationWarnings(model)
                .then(fetchModelTranslationSuccess)
                .catch(fetchModelTranslationFailed);
        }

        function fetchModelTranslationSuccess(response) {
            vm.missingWarnings = response.data.missingWarnings;
            vm.emptyWarnings = response.data.emptyWarnings;
            vm.extraWarnings = response.data.extraWarnings;
            vm.isTranslationErrors=true;
            vm.isLoaded = true;
        }

        function fetchModelTranslationFailed(error) {
            console.log(error);
            vm.isTranslationErrors=false;
        }
    }
})();
