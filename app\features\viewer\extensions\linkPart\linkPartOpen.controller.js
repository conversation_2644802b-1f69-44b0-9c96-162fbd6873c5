(function () {
    'use strict';

    angular
        .module('app.viewer')
        .controller('LinkPartOpenController', LinkPartOpenController);

    LinkPartOpenController.$inject = ['$uibModalInstance', '$state', '$window', '$rootScope', 'linkedModel', 'part', 'viewerHelperService', 'basketService', 'userService', '$stateParams', '$translate'];

    function LinkPartOpenController($uibModalInstance, $state, $window, $rootScope, linkedModel, part, viewerHelperService, basketService, userService, $stateParams, $translate) {
        var vm = this;

        vm.viewableName = "";
        vm.isPreviewMode = userService.isManufacturer() && !$stateParams.onBehalfOf;

        vm.openLinkedViewable = openLinkedViewable;
        vm.addToBasket = addToBasket;
        vm.cancel = cancel;

        var PREVIEW_NO_ADD;
        $translate(['LINK_CONFIRM_NAV.PREVIEW_NO_ADD'])
            .then(function (resp) {
                PREVIEW_NO_ADD = resp["LINK_CONFIRM_NAV.PREVIEW_NO_ADD"];
            });

        initialize();

        function initialize() {
            vm.viewableName = linkedModel.modelName.toUpperCase();
        }

        function openLinkedViewable() {
            var openUrlNW;
            if (linkedModel.is2d) {
                localStorage.setItem('navigatedFrom', 'customerViewer');
                openUrlNW = $state.href("customerPdfViewer", {
                    machineName: linkedModel.machineName,
                    autodeskURN: linkedModel.urn,
                    modelId: linkedModel.modelId,
                    viewableName: linkedModel.modelName,
                }, {absolute: true});
            } else {
                localStorage.setItem('navigatedFrom', 'customerViewer');
                var options = {
                    machineName: linkedModel.machineName,
                    autodeskURN: linkedModel.urn,
                    modelId: linkedModel.modelId,
                    viewableName: linkedModel.modelName,
                };
                if (linkedModel.translateType === "SVF2") {
                    options.env = 'AutodeskProduction2';
                    options.api = 'streamingV2';
                }
                openUrlNW = $state.href("customerViewer", options, {absolute: true});
            }
            $window.open(openUrlNW, '_blank');
            $uibModalInstance.close();
        }

        function addToBasket() {
           if (vm.isPreviewMode) {
                alert(PREVIEW_NO_ADD);
            } else {
                var machineName = viewerHelperService.getMachineName();
                part.machineName = machineName;
                basketService.addPart(part);
                $uibModalInstance.close();
                $rootScope.$broadcast("Basket-Updated");
           }
        }

        function cancel() {
            $uibModalInstance.close();
        }



    }

})();
