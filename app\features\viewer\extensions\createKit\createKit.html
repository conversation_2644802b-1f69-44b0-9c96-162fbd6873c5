<div class="sidebar-content" ng-show="createKitCtrl.isOpen">
    <form class="form" name="kitForm">
      
      <div>
        
        <div class="input-group mb-0">
            <label translate>CREATE_KIT.TITLE</label>
                <input type="text" placeholder="{{'CREATE_KIT.ENTER_TITLE' | translate}}" class="full-width-element"
                       ng-model="createKitCtrl.title"
                       required="required">
        </div>
        <div class="input-group mb-0">
            <label translate>CREATE_KIT.DESCRIPTION</label>
                <input type="text" placeholder="{{'CREATE_KIT.ENTER_DESCRIPTION' | translate}}" class="full-width-element"
                       ng-model="createKitCtrl.description"
                       required="required">
        </div>


        <div class="well clearfix mb-16">
            <div class="selected-part-text-with-btn" ng-hide="createKitCtrl.selectedPart.length > 1">
                <h4 translate>CREATE_KIT.SELECTED_PART</h4>
                <span ng-hide="createKitCtrl.selectedPart.length === 0">
                    {{createKitCtrl.selectedPart.partNumber}}
                    <small><strong>{{createKitCtrl.selectedPart.partDescription}}</strong></small>
                </span>
                <span ng-show="createKitCtrl.selectedPart.length === 0" translate>CREATE_KIT.NONE</span>
            </div>
            <div class="selected-part-button" ng-hide="createKitCtrl.selectedPart.length > 1">
                <button type=button class="btn primary float-right" ng-click="createKitCtrl.addToKit(createKitCtrl.selectedPart)" translate>
                    CREATE_KIT.ADD_KIT
                </button>
            </div>
            <div class="selected-part-error" ng-show="createKitCtrl.selectedPart.length > 1">
                <h4 translate>CREATE_KIT.MULTI_PART</h4>
                <p translate>CREATE_KIT.MUST_BE</p>
            </div>
        </div>
        
      </div><!-- /side-menu-content -->

        <table class="tableViewer table-bordered w-100 bg-white ml-0">
            <thead>
            <tr>
                <th translate>CREATE_KIT.KIT_PARTS</th>
                <th class="text-right" translate>CREATE_KIT.QUANTITY</th>
                <th></th>
            </tr>
            </thead>
            <tbody>
            <tr ng-repeat="part in createKitCtrl.kitParts track by part.partNumber">
                <td>{{part.partNumber}}
                    </small>
                    <small><strong>{{part.partDescription}}</strong></small>
                </td>
                <td>
                    <input ng-model="part.quantity" ng-change="createKitCtrl.quantityChanged(part)" type="number"
                           min="0" ng-model-options="{debounce: 500}" class="quantity-box">
                </td>
                <td>
                    <a href="" ng-click="createKitCtrl.removePartFromKit($index)"
                       class="delete fa fa-trash"></a>
                </td>
            </tr>

            <tr ng-show="!createKitCtrl.kitParts.length > 0">
                <td class="emptytable" colspan="3" translate>CREATE_KIT.NO_PARTS</td>
            </tr>
            </tbody>
        </table>

      <div class="side-menu-content pt-3">
        <div class="kit-actions">
            <button class="btn small secondary" type="button" ng-click="createKitCtrl.cancel()" translate>GENERAL.CANCEL</button>
            <button class="btn small primary" type="submit" ng-click="kitForm.$valid && createKitCtrl.saveKit()" translate>CREATE_KIT.SAVE_KIT</button>
        </div>
      </div><!-- /side-menu-content -->

    </form>


</div>