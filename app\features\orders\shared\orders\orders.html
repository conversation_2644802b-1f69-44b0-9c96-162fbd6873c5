<section class="responsiveContainer">

    <div id="{{ordersListCtrl.isFixedHeader ? 'infiniteScrollFixedHeader' : 'infiniteScrollStaticHeader'}}"
        class="flex justify-content-between p-4 p-md-0">
        <search-filter class="col-12 col-md-3" state-name="'orders'" value="ordersListCtrl.searchValue"
            placeholder-key="ORDERS.SEARCH_BY_ORDER" on-search-change="ordersListCtrl.searchFilterChange()"></search-filter>
            <div class="d-flex align-items-center">
                <span ng-if="ordersListCtrl.isArchiveActive" class="tooltip-icon mr-4" style="cursor: pointer;" data-toggle="tooltip" data-placement="top" title="{{'ORDERS.ARCHIVE_MODAL' | translate}}">
                    <i class="fa fa-info-circle cadBlue" style="font-size: 1.25em;"></i>
                </span>
                <button class="btn primary-outline mr-4" ng-click="ordersListCtrl.bulkArchiveOrder()"
                    ng-disabled="!ordersListCtrl.isAnyOrderSelected()" ng-if="ordersListCtrl.isArchiveActive">
                    <i class="fa fa-archive"></i>
                    {{"ORDERS.ARCHIVE_SELECTED" | translate}}
                </button>
            </div>
    </div>

    <table class="table table-bordered tableFixedWidth">
        <thead>
            <tr>
                <th class="checkbox-column" ng-if="ordersListCtrl.isArchiveActive" ng-if="ordersListCtrl.isArchiveActive">
                    <input type="checkbox" ng-model="ordersListCtrl.selectAll" ng-change="ordersListCtrl.toggleSelectAll()"
                        >
                </th>
                <th
                    ng-class="ordersListCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'"
                    class="text-left" ng-click="ordersListCtrl.order_item='orderId'; ordersListCtrl.sortReverse = !ordersListCtrl.sortReverse"
                    translate
                >
                    ORDERS.ORDER_DETAILS
                </th>
                <th
                    ng-class="ordersListCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'"
                    ng-click="ordersListCtrl.order_item='orderStatus'; ordersListCtrl.sortReverse = !ordersListCtrl.sortReverse"
                    translate
                >
                    ORDERS.STATUS
                </th>
                <th
                    ng-class="ordersListCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'"
                    ng-click="ordersListCtrl.order_item='newComments'; ordersListCtrl.sortReverse = !ordersListCtrl.sortReverse"
                    translate
                >
                    ORDERS.NEW_COMMENTS
                </th>
                <th translate>GENERAL.ACTIONS</th>
            </tr>
        </thead>

        <tbody class="orders_clickable" infinite-scroll="ordersListCtrl.loadMoreInfiniteScroll()" infinite-scroll-distance="3" infinite-scroll-disabled="ordersListCtrl.loadingInfiniteScrollData">
            <tr
                ng-class="order.readOrder?'clickableRow' : 'clickableRow unread'"
                ng-repeat="order in ordersListCtrl.orders | orderBy:'modifieddate':ordersListCtrl.sortReverse | filter :ordersListCtrl.searchValue"
            >
            <td ng-if="ordersListCtrl.isArchiveActive"><input type="checkbox" ng-model="order.selected" ng-click="ordersListCtrl.preventClickPropagation($event)"
                ></td>
                <td
                    data-label="{{'ORDERS.ORDER_DETAILS' | translate}}"
                    ng-click="ordersListCtrl.viewOrder(order.orderId)"
                    class="tableColumn text-left">
                    <h4 class="mb-0">
                        <strong>{{ordersListCtrl.stage.single}}# {{order.customOrderDisplay}}</strong> {{"ORDERS.FOR" | translate}}
                        <strong>{{order.manufacturerSubEntityName}}</strong>
                    </h4>
                    <p>{{"ORDERS.CREATED_ON" | translate}} <strong>{{order.createdDate | date: "d MMM y"}}</strong></p>
                    <p>
                        {{"ORDERS.DELIVERY_REQUESTED_ON" | translate}}
                        <strong>{{order.requestedDeliveryDate | date: "d MMM y"}}</strong>
                    </p>
                    <p>
                        {{order.emailAddress}}
                        <a href="mailto:{{order.emailAddress}}" ng-click="ordersListCtrl.preventClickPropagation($event)"
                            title="{{order.emailAddress}}">
                            <i class="fa fa-envelope clickable-icon icon-circle" aria-hidden="true"></i>
                        </a>
                    </p>
                    <p>
                        {{"ORDERS.CONTAINS" | translate}} <span class="font-weight-bold"> {{order.totalPartsQuantity}} </span> 
                        <span ng-if="order.totalPartsQuantity === 1">{{"ORDERS.PART_ACROSS" | translate}}</span>
                        <span ng-if="order.totalPartsQuantity !== 1">{{"ORDERS.PARTS_ACROSS" | translate}}</span>
                        <span class="font-weight-bold"> {{order.totalLineItems}} </span>
                        <span ng-if="order.totalLineItems === 1">{{"ORDERS.LINE" | translate}}</span>
                        <span ng-if="order.totalLineItems !== 1">{{"ORDERS.LINES" | translate}}</span>
                        <span ng-if="ordersListCtrl.showPrice && !ordersListCtrl.hidePrice">
                            <span ng-if="order.orderValue">{{"ORDERS.VALUE" | translate}} <strong>{{ order.orderValue }}</strong></span>
                            <span ng-if="order.price">{{"ORDERS.VALUE" | translate}} <strong>{{ order.price | currency:order.currency.symbol:2 }}</strong></span>
                        </span>
                    </p>
                </td>
                <td data-label="{{'ORDERS.STATUS' | translate}}" ng-click="ordersListCtrl.viewOrder(order.orderId)">
                    <span ng-class="order.readOrder?'primary' : 'success'" class="badge-pill">{{order.orderStatusDisplay}}</span>
                    <p>{{"ORDERS.ON" | translate}} {{order.statusUpdatedDate | date: "d MMM y"}}</p>
                </td>

                <td data-label="{{'ORDERS.NEW_COMMENTS' | translate}}" ng-click="ordersListCtrl.viewOrder(order.orderId)">
                    <p class="blob_unreadEnquiries blobs-container" ng-if="order.unreadComment" ng-translate>
                        <i class="blob_orders grey_pulse mr-3 fas fa-bell"></i> {{"ORDERS.NEW_COMMENTS" | translate}}
                    </p>
                </td>

                <td class="has-dropdown mobile-right-aligned-btn" ng-hide="ordersListCtrl.hideCancelOrderButton">
                    <split-button-dropdown
                        main-action="ordersListCtrl.viewOrder(order.orderId)"
                        main-action-label="{{'ORDERS.VIEW' | translate}} {{ordersListCtrl.stage.single}}"
                        actions="ordersListCtrl.actions"
                        entity="order"
                    >
                    </split-button-dropdown>
                </td>
                <td ng-show="ordersListCtrl.hideCancelOrderButton">
                    <button type="button" class="btn secondary" ng-click="ordersListCtrl.viewOrder(order.orderId)">
                        {{"ORDERS.VIEW" | translate}} {{ordersListCtrl.stage.single}}
                    </button>
                </td>
            </tr>

            <tr ng-show="!ordersListCtrl.orders.length > 0 && ordersListCtrl.isOrdersLoaded">
                <td class="emptytable" colspan="6">{{"ORDERS.NO" | translate}} {{ordersListCtrl.stage.plural}}</td>
            </tr>

            <tr ng-hide="ordersListCtrl.isOrdersLoaded">
                <td class="preloader" colspan="6">
                    <img ng-src="images/cadpreloader.gif" class="ajax-loader" height="60" width="60" />
                </td>
            </tr>
        </tbody>
    </table>

    <span ng-click="ordersListCtrl.scrollToTop()" id="backToTopBtn" title="Go to top"
        class="fas fa-arrow-alt-circle-up" ng-show="ordersListCtrl.showBackToTopButton"></span>


</section>
