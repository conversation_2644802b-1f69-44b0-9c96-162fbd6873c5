(function () {
    'use strict';

    angular
        .module('app.services')
        .factory('assignCategoriesService', assignCategoriesService);

    assignCategoriesService.$inject = ['$http', 'apiConstants', 'userService'];

    function assignCategoriesService($http, apiConstants, userService) {
        return {
            assignCategoriesToPurchaser: assignCategoriesToPurchaser,
            getCategoriesForManufacturer: getCategoriesForManufacturer,
            getAssignedCategoryIdsForPurchaser: getAssignedCategoryIdsForPurchaser
        };

        function assignCategoriesToPurchaser(manufacturerId, purchaserId, categoryIds) {
            var payload = {
                purchaserId: purchaserId,
                publicationCategoryIds: categoryIds
            };
            
            return $http.post(apiConstants.url + '/manufacturers/' + manufacturerId + '/publication-categories/assign-to-purchaser', payload);
        }

        function getCategoriesForManufacturer() {
            var manufacturerId = userService.getManufacturerId();
            return $http.get(apiConstants.url + '/manufacturers/' + manufacturerId + '/publication-categories');
        }

        function getAssignedCategoryIdsForPurchaser(purchaserId) {
            return $http.get(apiConstants.url + '/purchasers/' + purchaserId + '/publication-categories');
        }
    }
})();
