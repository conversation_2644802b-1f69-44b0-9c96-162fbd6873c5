(function () {
    "use strict";

    angular.module("app.publications").controller("CreatePublicationController", CreatePublicationController);

    CreatePublicationController.$inject = [
        "manufacturerPublicationService",
        "$state",
        "$stateParams",
        "$uibModal",
        "headerBannerService",
        "manufacturerProductService",
        "$translate",
        "publicationService",
        "$filter",
        "userService",
        "$timeout",
        "publicationViewableManagerService",
        "$rootScope",
        "$scope",
        "$http",
        "apiConstants",
        "$q"
    ];

    function CreatePublicationController(
        manufacturerPublicationService,
        $state,
        $stateParams,
        $uibModal,
        headerBannerService,
        manufacturerProductService,
        $translate,
        publicationService,
        $filter,
        userService,
        $timeout,
        publicationViewableManagerService,
        $rootScope,
        $scope,
        $http,
        apiConstants,
        $q
    ) {
        var vm = this;
        vm.selectAllAvailableModel = false;
        vm.data = { machineId: "", manualId: null, modelId: "", manufacturerSubEntityId: "", useViewableImage: false, name: "", publicationCategoryId: null, coverImageId: null, coverImage: null, published: false };
        vm.featuredModelUrl = "images/placeholder.jpg";
        vm.featuredModelUrlForSubmit = null;
        vm.featuredViewableImageId = null;
        vm.activeViewableTab = 'find';
        
        vm.coverImageTimestamp = Date.now();
        vm.featuredImageTimestamp = Date.now();

        vm.viewableManager = publicationViewableManagerService;

        vm.viewableManager.featuredViewableImage = null;
        vm.isEditing = false;
        vm.techDocs = [];
        vm.videos = [];
        vm.kits = [];
        vm.publicationCategories = [];
        vm.publicationTechDocs = [];

        vm.modelIds = []; 
        
        vm.multiCheckdownSettings = {
            checkBoxes: true,
            showCheckAll: false,
            showUncheckAll: false,
            buttonClasses: buttonStyling(),
            styleActive: true,
            closeOnBlur: true,
            idProp: 'id',
        };

        vm.isCustomersDropdownDisabled = true;
        vm.selectedCustomers = [];
        vm.customerSelectSettings = {
            checkBoxes: true,
            showCheckAll: true,
            showUncheckAll: false,
            buttonClasses: buttonStyling(),
            styleActive: true,
            closeOnBlur: true,
        };
        
        vm.isMachineDropdownDisabled = true; 
        vm.machineDropdownClass = "disabled-select";
        vm.publishUnpublishManual = publicationService.publishUnpublishManual;

        vm.manufacturerId = userService.getManufacturerId();

        vm.shouldShowAvailableViewablesList = function() { return vm.viewableManager.shouldShowAvailableViewablesList(); };
        vm.rangeChanged = function() { 
            // Preserve temporary selections when changing range
            vm.viewableManager.rangeChanged(vm.viewableManager.data.rangeId, null); 
        };
        vm.productChanged = function() { 
            // Preserve temporary selections when changing product
            vm.viewableManager.productChanged(); 
        };
        vm.savePublication = savePublication; 
        vm.saveAndPublishPublication = saveAndPublishPublication; 
        vm.addFeaturedImage = addFeaturedImage; 
        vm.modelsSelected = prepareSubmitData; 
        vm.techDocsSelected = techDocsSelected;
        vm.videosSelected = videosSelected;
        vm.kitsSelected = kitsSelected;
        vm.handleCustomerClick = handleCustomerClick;
        vm.isSelected = isSelected;
        vm.toggleSelectAll = toggleSelectAll;
        vm.isAllSelected = isAllSelected;
        vm.getSelectedCustomerLabels = getSelectedCustomerLabels;
        vm.addViewable = function(viewable) {
            vm.viewableManager.addViewable(viewable);
            vm.sortSelectedViewables();
        };
        vm.removeViewable = function(viewable) {
            vm.viewableManager.removeViewable(viewable);
            vm.sortSelectedViewables();
        };
        vm.manageCategoriesModal = manageCategoriesModal;
        vm.getPublicationCategories = getPublicationCategories;
        vm.saveAndPublishPublication = saveAndPublishPublication;
        vm.addCoverImage = addCoverImage;
        vm.cancel = cancel;
        vm.isSelectAllAvailableEnabled = isSelectAllAvailableEnabled;
        vm.toggleSelectAllAvailableViewables = toggleSelectAllAvailableViewables;
        vm.isAllAvailableViewablesSelected = isAllAvailableViewablesSelected;
        vm.getViewableItemClass = getViewableItemClass;
        vm.clearTempSelectedViewables = clearTempSelectedViewables;
        vm.changeViewableTab = function(tabName) { 
            // Clear any temporary selections when changing tabs
            vm.viewableManager.tempSelectedViewableIds = [];
            vm.viewableManager.tempSelectedViewables = [];
            vm.viewableManager.setActiveTab(tabName);
        };
        vm.performSearch = function(event) { 
            if (event && angular.isFunction(event.preventDefault)) {
                event.preventDefault(); // Prevent default form submission if applicable
            }
            // Clear any temporary selections when performing a search
            vm.viewableManager.tempSelectedViewableIds = [];
            vm.viewableManager.tempSelectedViewables = [];
            vm.viewableManager.performSearch();
        };
        vm.handleSearchKeydown = function(event) {
            event.preventDefault(); // Prevent form submission
            vm.performSearch();
        };
        vm.clearSearch = function() { 
            // Clear any temporary selections when clearing a search
            vm.viewableManager.tempSelectedViewableIds = [];
            vm.viewableManager.tempSelectedViewables = [];
            vm.viewableManager.clearSearch(); 
        };
        vm.getDisplayedViewables = function() { return vm.viewableManager.getDisplayedViewables(); };
        
        vm.isEditing = false;
        vm.setAsFeatured = function(viewable) {
            vm.viewableManager.setAsFeatured(viewable);
            vm.sortSelectedViewables();
        };
        vm.removeFeatured = function(viewable) {
            vm.viewableManager.removeFeatured(viewable);
            vm.sortSelectedViewables();
        };
        vm.isSubmitDisabled = isSubmitDisabled;

        // Initialize actions for the dropdown menu
        vm.actions = [
            {
                labelKey: 'CREATE_PUBLICATION.SAVE_AND_PUBLISH', // Default label
                icon: 'fa fa-cloud-upload', // Default icon
                onClick: saveAndPublishPublication
            }
        ];

        function buttonStyling() {
            return "btn btn-default multiCheckbox";
        }

        var WENT_WRONG,
            SELECT_VIEWABLES,
            VIEWABLES_SELECTED,
            SELECT_KIT,
            KITS_SELECTED,
            SELECT_VIDEO,
            VIDEOS_SELECTED,
            SELECT_TECH_DOC,
            TECH_DOCS_SELECTED,
            UNASSIGNED,
            CUSTOMERS,
            UPDATED_SUCCESS,
            PUBLISHED_SUCCESS,
            UNPUBLISHED_SUCCESS,
            PUBLISH_FAILURE,
            UNPUBLISH_FAILURE;
        
        $translate([
            "GENERAL.WENT_WRONG",
            "CREATE_PUBLICATION.SELECT_VIEWABLES",
            "CREATE_PUBLICATION.VIEWABLES_SELECTED",
            "CREATE_PUBLICATION.SELECT_KIT",
            "CREATE_PUBLICATION.KITS_SELECTED",
            "CREATE_PUBLICATION.SELECT_VIDEO",
            "CREATE_PUBLICATION.VIDEOS_SELECTED",
            "CREATE_PUBLICATION.SELECT_TECH_DOC",
            "CREATE_PUBLICATION.TECH_DOCS_SELECTED",
            "CREATE_PUBLICATION.UNASSIGNED",
            "CREATE_PUBLICATION.CUSTOMERS",
            "CREATE_PUBLICATION.SAVE",
            "CREATE_PUBLICATION.SAVE_AND_PUBLISH",
            "CREATE_PUBLICATION.SAVE_AND_UNPUBLISH",
            "CREATE_PUBLICATION.UPDATED_SUCCESS"
        ]).then(function (resp) {
            WENT_WRONG = resp["GENERAL.WENT_WRONG"];
            SELECT_VIEWABLES = resp["CREATE_PUBLICATION.SELECT_VIEWABLES"];
            VIEWABLES_SELECTED = resp["CREATE_PUBLICATION.VIEWABLES_SELECTED"];
            SELECT_TECH_DOC = resp["CREATE_PUBLICATION.SELECT_TECH_DOC"];
            TECH_DOCS_SELECTED = resp["CREATE_PUBLICATION.TECH_DOCS_SELECTED"];
            SELECT_VIDEO = resp["CREATE_PUBLICATION.SELECT_VIDEO"];
            VIDEOS_SELECTED = resp["CREATE_PUBLICATION.VIDEOS_SELECTED"];
            SELECT_KIT = resp["CREATE_PUBLICATION.SELECT_KIT"];
            KITS_SELECTED = resp["CREATE_PUBLICATION.KITS_SELECTED"];
            UNASSIGNED = resp["CREATE_PUBLICATION.UNASSIGNED"];
            CUSTOMERS = resp["CREATE_PUBLICATION.CUSTOMERS"];
            UPDATED_SUCCESS = resp["CREATE_PUBLICATION.UPDATED_SUCCESS"];

            vm.modelTextSettings = {
                buttonDefaultText: SELECT_VIEWABLES,
                dynamicButtonTextSuffix: VIEWABLES_SELECTED,
                emptyPlaceholderText: 'No Viewables Available'
            };
            vm.techDocTextSettings = {
                buttonDefaultText: SELECT_TECH_DOC,
                dynamicButtonTextSuffix: TECH_DOCS_SELECTED,
                emptyPlaceholderText: 'No Tech Docs Available'
            };
            vm.videoTextSettings = {
                buttonDefaultText: SELECT_VIDEO,
                dynamicButtonTextSuffix: VIDEOS_SELECTED,
                emptyPlaceholderText: 'No Videos Available'
            };
            vm.kitTextSettings = {
                buttonDefaultText: SELECT_KIT,
                dynamicButtonTextSuffix: KITS_SELECTED,
                emptyPlaceholderText: 'No Kits Available'
            };
            vm.customerTextSettings = {
                buttonDefaultText: UNASSIGNED,
                dynamicButtonTextSuffix: CUSTOMERS,
                emptyPlaceholderText: 'No Customers Available'
            };
        });

        initialize();

        function initialize() {
            vm.getPublicationCategories();
            vm.viewableManager.setUseViewableImageFlag(vm.data.useViewableImage); 

            if ($stateParams.id) { 
                vm.isEditing = true;
                fetchPublicationDataForEdit($stateParams.id);
            } else {
                vm.viewableManager.initializeManager(false, null, vm.data.useViewableImage);
                getManufacturerSubEntities(); 
                getTechDocs();
                getVideos();
                getKits();
                externalUpdateToSelectedCustomers();
                updateSaveAndPublishActionState();
                
                // Set a timeout to check for featured viewables after initialization
                $timeout(function() {
                    checkForFeaturedViewables();
                }, 500);
            }
        }

        vm.sortSelectedViewables = function() {
            vm.viewableManager.selectedViewables.sort(function(a, b) {
                if (a.featuredViewable) return -1;
                if (b.featuredViewable) return 1;
                return 0;
            });
        };

        $scope.$watch(function () {
            return vm.viewableManager.data.productId;
        }, function (newValue, oldValue) {
            if (newValue !== oldValue) {
                vm.selectAllAvailableModel = false;
            }
        });

        $scope.$watch(function () {
            return vm.viewableManager.data.rangeId;
        }, function (newValue, oldValue) {
            if (newValue !== oldValue) {
                vm.selectAllAvailableModel = false;
            }
        });

        function cancel() {
            $state.go('publications');
        };

        // Enable 'Select All' only if both filters are applied in 'find' tab, or a search has been performed in 'search' tab
        function isSelectAllAvailableEnabled() {
            var mgr = vm.viewableManager;
            if (mgr.activeViewableTab === 'find') {
                return !!(mgr.data.rangeId && mgr.data.productId);
            } else if (mgr.activeViewableTab === 'search') {
                return !!mgr.searchPerformed;
            }
            return false;
        }
        
        // Check if all available viewables are currently selected
        function isAllAvailableViewablesSelected() {
            var availableViewables = vm.viewableManager.getDisplayedViewables();
            if (!availableViewables || availableViewables.length === 0) {
                return false;
            }
            
            // Check if all available viewables are in the tempSelectedViewableIds array
            return availableViewables.every(function(viewable) {
                return vm.viewableManager.tempSelectedViewableIds.indexOf(viewable.id) !== -1;
            });
        }
        
        // Toggle selection of all available viewables
        function toggleSelectAllAvailableViewables(isSelected) {
            var availableViewables = vm.viewableManager.getDisplayedViewables();
            if (!availableViewables || availableViewables.length === 0) {
                return;
            }
            
            // Clear current selections first
            vm.viewableManager.tempSelectedViewableIds = [];
            vm.viewableManager.tempSelectedViewables = [];
            
            // If checked, add all viewable IDs and objects to the temp selection
            if (isSelected) {
                availableViewables.forEach(function(viewable) {
                    vm.viewableManager.tempSelectedViewableIds.push(viewable.id);
                    vm.viewableManager.tempSelectedViewables.push(angular.copy(viewable));
                });
            }
        }
        
        // Return the appropriate CSS class based on whether a viewable is selected
        function getViewableItemClass(viewableId) {
            if (vm.viewableManager.tempSelectedViewableIds.indexOf(viewableId) !== -1) {
                return 'viewable-item-assigned';
            } else {
                return 'viewable-item';
            }
        }
        
        // Clear all temporary selected viewables
        function clearTempSelectedViewables() {
            vm.viewableManager.tempSelectedViewableIds = [];
            vm.viewableManager.tempSelectedViewables = [];
        }
        
        function checkForFeaturedViewables() {
            // Find any viewable with featuredViewable: true
            var featuredViewable = vm.viewableManager.selectedViewables.find(function(viewable) {
                return viewable.featuredViewable === true;
            });
            
            // If a featured viewable is found, set it as the featured model ID
            if (featuredViewable) {
                vm.viewableManager.data.featuredModelId = featuredViewable.id;
                vm.viewableManager.setAsFeatured(featuredViewable);
            }
        }

        function fetchPublicationDataForEdit(publicationId) { 
            if (!publicationId || typeof publicationId !== 'number' && typeof publicationId !== 'string') {
                console.error("Invalid publication ID:", publicationId);
                headerBannerService.setNotification('ERROR', 'Invalid publication ID', 5000);
                $state.go('app.publications');
                return;
            }

            // Convert to number if it's a string
            var numericId = typeof publicationId === 'string' ? parseInt(publicationId, 10) : publicationId;
            if (isNaN(numericId)) {
                console.error("Invalid numeric publication ID:", publicationId);
                headerBannerService.setNotification('ERROR', 'Invalid publication ID', 5000);
                $state.go('app.publications');
                return;
            }

            manufacturerPublicationService.getPublication(numericId)
                .then(function(response) {
                    if (response && response.data) {
                        var publicationData = response.data;
                        populateData(publicationData); 
                        
                        vm.viewableManager.setUseViewableImageFlag(vm.data.useViewableImage); 
                        vm.viewableManager.initializeManager(true, publicationData, vm.data.useViewableImage);
                        vm.sortSelectedViewables();
                        updateSaveAndPublishActionState(); // Update button based on fetched status
                        
                        if (vm.publicationCategories && vm.publicationCategories.length > 0) {
                            const selectedCategory = vm.publicationCategories.find(cat => cat.id === vm.data.publicationCategoryId);
                            if (selectedCategory) {
                                vm.data.publicationCategoryId = selectedCategory.id;
                            }
                        }
                        
                        if (publicationData.rangeId) {
                            vm.viewableManager.data.rangeId = publicationData.rangeId; 
                            $timeout(function() { 
                                vm.viewableManager.rangeChanged(publicationData.rangeId, publicationData.productId);
                                
                                // Check for featured viewables after the range and product are loaded
                                $timeout(function() {
                                    checkForFeaturedViewables();
                                }, 1000);
                            });
                        } else {
                            // The current logic in service correctly calls getRange() if no rangeId provided during init.
                            // Still check for featured viewables
                            $timeout(function() {
                                checkForFeaturedViewables();
                            }, 1000);
                        }

                        getTechDocs(); 
                        getVideos(); 
                        getKits(); 
                        getManufacturerSubEntities(); 
                    } else {
                        console.error("Error fetching publication data: No data in response", response);
                        headerBannerService.setNotification('ERROR', WENT_WRONG, 5000);
                        $state.go('app.publications'); 
                    }
                })
                .catch(function(error) {
                    console.error("Error fetching publication data:", error);
                    var errorMsg = 'Failed to load publication data. Please try again.';
                    if (error.status === 404) {
                        errorMsg = 'Publication not found.';
                    }
                    headerBannerService.setNotification('ERROR', errorMsg, 5000);
                    $state.go('app.publications'); 
                });
        }

        function populateData(publicationData) {

            vm.data.manualId = publicationData.manualId; 
            vm.data.id = publicationData.id || publicationData.manualId; // Ensure vm.data.id is set for updates
            vm.data.published = publicationData.published; // Store the published status

            vm.data.publicationCategoryId = publicationData.publicationCategoryId; 

            vm.data.name = publicationData.name; 
            vm.data.useViewableImage = publicationData.useViewableImage; 

            vm.manualName = publicationData.manualName;
            vm.serialNumber = publicationData.serialNumber;
            vm.region = publicationData.region; 
            vm.site = publicationData.site; 
            // Handle cover image data
            if (publicationData.coverImage) {
                vm.data.coverImage = publicationData.coverImage;
                vm.featuredModelUrl = publicationData.coverImage.url;
            } else {
                vm.featuredModelUrl = publicationData.featuredModelUrl || "images/placeholder.jpg";
            }
            
            // Handle featured viewable image data
            if (publicationData.featuredViewableImage) {
                vm.viewableManager.featuredViewableImage = publicationData.featuredViewableImage;
                vm.featuredViewableImageId = publicationData.featuredViewableImage.id;
                if (!vm.data.coverImage) { // Only set if cover image is not already set
                    vm.viewableManager.featuredModelUrl = publicationData.featuredViewableImage.url;
                }
            } 
            vm.publicationKits = publicationData.kits || [];
            vm.publicationTechDocs = publicationData.techDocs || [];
            vm.publicationVideos = publicationData.videos || [];
            vm.publicationCustomers = publicationData.customers || [];

            // Populate the models for ng-dropdown-multiselect pre-selection
            if (vm.isEditing) { 
                vm.techDocs = (publicationData.techDocs || []).map(function(doc) { 
                    return { id: doc.id }; 
                });

                vm.videos = (publicationData.videos || []).map(function(video) { 
                    return { id: video.id }; 
                });

                vm.kits = (publicationData.kits || []).map(function(kit) { 
                    return { id: kit.id }; 
                });
            }

            // If category ID is 0, set to null
            if (vm.data.publicationCategoryId === 0) {
                vm.data.publicationCategoryId = null;
            }
        }

        function getTechDocs() {
            manufacturerProductService.getTechDocs().then(getTechDocsSuccess, serviceFailed);
        }

        function getVideos() {
            manufacturerProductService.getVideos().then(getVideosSuccess, serviceFailed);
        }

        function getKits() {
            manufacturerProductService.getKits().then(getKitsSuccess, serviceFailed);
        }

        function getKitsSuccess(response) {
            var kits = response.data.masterKits || response.data || [];
            
            vm.kitsAll = Array.isArray(kits) ? kits : [];
            vm.kitSelect = vm.kitsAll
                .filter(function(kit) { return kit && kit.id; })
                .map(function(kit) {
                    var label = kit.partNumber && kit.description 
                        ? kit.partNumber + ' - ' + kit.description
                        : kit.partNumber || kit.description || 'Unnamed Kit ' + kit.id;
                    
                    return { id: kit.id, label: label };
                });

            // Sort selected items to the top
            vm.kitSelect.sort(function(a, b) {
                var aSelected = vm.kits.some(function(selected) { return selected.id === a.id; });
                var bSelected = vm.kits.some(function(selected) { return selected.id === b.id; });
                if (aSelected && !bSelected) return -1;
                if (!aSelected && bSelected) return 1;
                return a.label.localeCompare(b.label);
            });

            // Configure dropdown
            vm.kitTextSettings.emptyPlaceholderText = vm.kitSelect.length === 0 ? 'No Kits Available' : '';
            vm.isKitsDropdownDisabled = false;
        }

        function getTechDocsSuccess(response) {
            vm.techDocsAll = response.data;
            vm.techDocSelect = [];

            if (!vm.techDocsAll || !Array.isArray(vm.techDocsAll)) {
                console.error('TechDocs: vm.techDocsAll is not a valid array:', vm.techDocsAll);
                vm.techDocsAll = [];
            }

            vm.techDocsAll.forEach(function(doc, index) {
                if (typeof doc === 'undefined' || doc === null) {
                    console.warn('TechDocs: Item at index ' + index + ' in vm.techDocsAll is undefined or null. Skipping.');
                    return;
                }
                if (typeof doc.id === 'undefined') {
                    console.warn('TechDocs: Item at index ' + index + ' (Name: "' + doc.name + '") has an undefined id. Using fallback or skipping if critical.');
                }
                var techDocData = { 
                    id: doc.id, 
                    label: doc.name || ('Unnamed Tech Doc ' + (doc.id || index)) 
                };
                vm.techDocSelect.push(techDocData);
            });

            // Sort selected items to the top
            vm.techDocSelect.sort(function(a, b) {
                var aSelected = vm.techDocs.some(function(selected) { return selected.id === a.id; });
                var bSelected = vm.techDocs.some(function(selected) { return selected.id === b.id; });
                if (aSelected && !bSelected) return -1;
                if (!aSelected && bSelected) return 1;
                return a.label.localeCompare(b.label);
            });

            if (!vm.techDocSelect || vm.techDocSelect.length === 0) {
                vm.techDocTextSettings.emptyPlaceholderText = 'No Tech Docs Available';
            }

            vm.istechDocsDropdownDisabled = false;
        }

        function getVideosSuccess(response) {
            vm.videosAll = response.data;
            vm.videoSelect = [];

            if (!vm.videosAll || !Array.isArray(vm.videosAll)) {
                console.error('Videos: vm.videosAll is not a valid array:', vm.videosAll);
                vm.videosAll = [];
            }

            vm.videosAll.forEach(function(vid, index) {
                if (typeof vid === 'undefined' || vid === null) {
                    console.warn('Videos: Item at index ' + index + ' in vm.videosAll is undefined or null. Skipping.');
                    return;
                }
                if (typeof vid.id === 'undefined') {
                    console.warn('Videos: Item at index ' + index + ' (Name: "' + vid.name + '") has an undefined id.');
                }
                var videoData = { 
                    id: vid.id, 
                    label: vid.name || ('Unnamed Video ' + (vid.id || index)) 
                };
                vm.videoSelect.push(videoData);
            });

            // Sort selected items to the top
            vm.videoSelect.sort(function(a, b) {
                var aSelected = vm.videos.some(function(selected) { return selected.id === a.id; });
                var bSelected = vm.videos.some(function(selected) { return selected.id === b.id; });
                if (aSelected && !bSelected) return -1;
                if (!aSelected && bSelected) return 1;
                return a.label.localeCompare(b.label);
            });

            if (!vm.videoSelect || vm.videoSelect.length === 0) {
                vm.videoTextSettings.emptyPlaceholderText = 'No Videos Available';
            }

            vm.isvideosDropdownDisabled = false;
        }

        function getManufacturerSubEntities() {
            manufacturerPublicationService.getManufacturerSubEntitiesForManufacturer().then(getSubEntitiesSuccess, getSubEntitiesFailure);
        }

        function getSubEntitiesSuccess(response) {
            vm.allCustomers = [];
            vm.selectedCustomers = []; 

            response.data.forEach(function(entity) {
                var customerData = { id: entity.manufacturerSubEntityId, label: entity.name };
                vm.allCustomers.push(customerData);

                if (vm.isEditing && vm.publicationCustomers) {
                    var pubCustomer = vm.publicationCustomers.find(p => p.id === entity.manufacturerSubEntityId); 
                    if (pubCustomer) {
                        if (!vm.selectedCustomers.some(c => c.id === customerData.id)) {
                            vm.selectedCustomers.push(customerData);
                        }
                    }
                }
            });

            // Sort selected customers to the top
            vm.allCustomers.sort(function(a, b) {
                var aSelected = vm.selectedCustomers.some(function(selected) { return selected.id === a.id; });
                var bSelected = vm.selectedCustomers.some(function(selected) { return selected.id === b.id; });
                if (aSelected && !bSelected) return -1;
                if (!aSelected && bSelected) return 1;
                return a.label.localeCompare(b.label);
            });

            vm.isCustomersDropdownDisabled = false;
        }

        function handleCustomerClick(customer) {
            event.stopPropagation();
            var customerId = parseInt(customer.id, 10); 
            var index = vm.selectedCustomers.findIndex(c => parseInt(c.id, 10) === customerId);
            if (index > -1) {
                vm.selectedCustomers.splice(index, 1);
            } else {
                vm.selectedCustomers.push({
                    id: customerId, 
                    label: customer.label
                });
            }
        }

        function isSelected(customer) {
            return vm.selectedCustomers.some(c => c.id === customer.id);
        }

        function toggleSelectAll() {
            event.stopPropagation();
            if (isAllSelected()) {
                vm.selectedCustomers = [];
            } else {
                vm.selectedCustomers = angular.copy(vm.allCustomers);
            }
        }

        function isAllSelected() {
            if (!vm.allCustomers || !vm.selectedCustomers) {
                return false;
            }
            return vm.allCustomers.length && vm.selectedCustomers.length === vm.allCustomers.length;
        }
        function getSelectedCustomerLabels() {
            if (vm.selectedCustomers.length > 3) {
                return vm.selectedCustomers.length + ' Selected';
            }
            return vm.selectedCustomers.map(c => c.label).slice(0, 3).join(', ');
        }

        function externalUpdateToSelectedCustomers() {
                    vm.selectedCustomers = angular.copy(vm.allCustomers);
        }
        function getSubEntitiesFailure(err) {
            console.log(err);
        }

        function techDocsSelected() {
            vm.techDocIds = [];
            for (var i = 0; i < vm.techDocs.length; i++) {
                vm.techDocIds.push(vm.techDocs[i].id);
            }
        }

        function videosSelected() {
            vm.videoIds = [];
            for (var i = 0; i < vm.videos.length; i++) {
                vm.videoIds.push(vm.videos[i].id);
            }
        }

        function kitsSelected() {
            vm.kitIds = [];
            for (var i = 0; i < vm.kits.length; i++) {
                vm.kitIds.push(vm.kits[i].id);
            }
        }

        function performSaveOperation() {
            vm.data.viewables = vm.viewableManager.selectedViewables.map(function(viewable) {
                return { id: viewable.id, featuredViewable: !!viewable.featuredViewable };
            });
            
            vm.data.techDocs = vm.techDocs.map(function(doc) { return doc.id; });
            vm.data.videos = vm.videos.map(function(video) { return video.id; });
            vm.data.kits = vm.kits.map(function(kit) { return kit.id; });
            vm.data.manufacturerSubEntityIds = vm.selectedCustomers.map(function(customer) { return customer.id; });

            var customerIds = (vm.data.manufacturerSubEntityIds || []).map(function(id) { return typeof id === 'string' ? parseInt(id, 10) : id; }).filter(function(id) { return !isNaN(id); });
            var viewableIds = (vm.data.viewables || []).map(function(viewable) {
                if (typeof viewable === 'object' && viewable !== null && viewable.id !== undefined) {
                    return { 
                        id: typeof viewable.id === 'string' ? parseInt(viewable.id, 10) : viewable.id,
                        featuredViewable: !!viewable.featuredViewable
                    };
                } else {
                    var id = typeof viewable === 'string' ? parseInt(viewable, 10) : viewable;
                    return { id: id, featuredViewable: false };
                }
            }).filter(function(viewable) { return viewable && !isNaN(viewable.id); });
            var techDocIds = (vm.data.techDocs || []).map(function(id) { return typeof id === 'string' ? parseInt(id, 10) : id; }).filter(function(id) { return !isNaN(id); });
            var videoIds = (vm.data.videos || []).map(function(id) { return typeof id === 'string' ? parseInt(id, 10) : id; }).filter(function(id) { return !isNaN(id); });
            var kitIds = (vm.data.kits || []).map(function(id) { return typeof id === 'string' ? parseInt(id, 10) : id; }).filter(function(id) { return !isNaN(id); });

            var payload = {
                name: vm.data.name,
                serialNumber: vm.data.serialNumber,
                viewables: viewableIds,
                techDocs: techDocIds,
                videos: videoIds,
                kits: kitIds,
                manufacturerSubEntityIds: customerIds,
                publicationCategoryId: vm.data.publicationCategoryId || null,
                featuredViewableImageId: vm.viewableManager.featuredViewableImage ? vm.viewableManager.featuredViewableImage.id : null,
                coverImageId: vm.data.coverImage ? vm.data.coverImage.id : null,
                useViewableImage: vm.data.useViewableImage || false,
                published: vm.data.published || false
            };

            if (vm.data.id) {
                return manufacturerPublicationService.updatePublication(vm.data.id, payload)
                    .then(function(response) {
                        if (response && response.data && !response.data.id && vm.data.id) {
                            response.data.id = vm.data.id;
                        }
                        if (vm.data.id) {
                            return manufacturerPublicationService.assignManualCustomers(vm.data.id, customerIds)
                                .then(function() { return response; });
                        }
                        return response;
                    });
            } else {
                return manufacturerPublicationService.createPublication(payload)
                    .then(function(response) {
                        var publicationId = response.data && response.data.publicationId ? response.data.publicationId :
                                          response.data && response.data.id ? response.data.id :
                                          response.id;
                        if (response && response.data && publicationId) {
                            response.data.id = publicationId;
                            response.data.publicationId = publicationId;
                        }
                        if (publicationId) {
                            return manufacturerPublicationService.assignManualCustomers(publicationId, customerIds)
                                .then(function() { return response; });
                        }
                        return response;
                    });
            }
        }

        function savePublication() {
            var currentPublicationStatus = vm.data.published;
            var currentCoverImage = angular.copy(vm.data.coverImage);
            var currentFeaturedViewableImage = angular.copy(vm.viewableManager.featuredViewableImage);
            var currentFeaturedViewableImageId = vm.featuredViewableImageId;

            performSaveOperation()
                .then(function (response) {
                    var publicationId;

                    if (response && response.data) {
                        publicationId = response.data.id || response.data.publicationId;
                    }

                    var publicationIdToUse = publicationId || vm.data.id;

                    return manufacturerPublicationService.getPublication(publicationIdToUse)
                        .then(function (response) {
                            if (response && response.data) {
                                var serverPublishedStatus = response.data.published;
                                var serverData = response.data;

                                if (!serverData.coverImage && currentCoverImage) {
                                    vm.data.coverImage = currentCoverImage;
                                    vm.data.coverImageId = currentCoverImage.id;
                                    vm.featuredModelUrl = currentCoverImage.url;
                                }

                                if (!serverData.featuredViewableImage && currentFeaturedViewableImage) {
                                    vm.viewableManager.featuredViewableImage = currentFeaturedViewableImage;
                                    vm.featuredViewableImageId = currentFeaturedViewableImageId;
                                    vm.viewableManager.featuredModelUrl = currentFeaturedViewableImage.url;
                                }

                                if (serverPublishedStatus !== currentPublicationStatus) {
                                    return publicationService.publishUnpublishManual(
                                        publicationIdToUse,
                                        currentPublicationStatus
                                    ).then(function () {
                                        vm.data.published = currentPublicationStatus;
                                        updateSaveAndPublishActionState();
                                        headerBannerService.setNotification('SUCCESS', UPDATED_SUCCESS, 5000);
                                        $state.go('publications');
                                    });
                                } else {
                                    headerBannerService.setNotification('SUCCESS', UPDATED_SUCCESS, 5000);
                                    $state.go('publications');
                                }
                            } else {
                                if (currentCoverImage) {
                                    vm.data.coverImage = currentCoverImage;
                                    vm.data.coverImageId = currentCoverImage.id;
                                    vm.featuredModelUrl = currentCoverImage.url;
                                }

                                if (currentFeaturedViewableImage) {
                                    vm.viewableManager.featuredViewableImage = currentFeaturedViewableImage;
                                    vm.featuredViewableImageId = currentFeaturedViewableImageId;
                                    vm.viewableManager.featuredModelUrl = currentFeaturedViewableImage.url;
                                }

                                headerBannerService.setNotification('SUCCESS', UPDATED_SUCCESS, 5000);
                                $state.go('publications');
                            }
                        });
                })
                .catch(function (error) {
                    console.error('Error during save operation:', error);
                    serviceFailed(error);
                });
        }    

        function saveAndPublishPublication() {
            // Store current image data to ensure it's not lost during save
            var currentCoverImage = angular.copy(vm.data.coverImage);
            var currentFeaturedViewableImage = angular.copy(vm.viewableManager.featuredViewableImage);
            var currentFeaturedViewableImageId = vm.featuredViewableImageId;
            
            // First save the publication data
            performSaveOperation()
                .then(function(response) {
                    // Extract the publication ID from the response
                    var publicationId;
                    
                    if (response && response.data) {
                        publicationId = response.data.id || response.data.publicationId;
                    }
                    
                    // Fall back to the existing ID if we couldn't get it from the response
                    var publicationIdToOperateOn = publicationId || vm.data.id;
                    
                    console.log('Publication ID for publish/unpublish:', publicationIdToOperateOn);
                    
                    if (!publicationIdToOperateOn) {
                        console.error("Publication ID not available for publish/unpublish operation.");
                        throw new Error("Publication ID not available for publish/unpublish operation.");
                    }

                    // Calculate the target status (opposite of current status)
                    var targetStatus = !vm.data.published;
                    console.log('Changing publication status to:', targetStatus);
                    
                    // Ensure image data is preserved
                    if (currentCoverImage) {
                        vm.data.coverImage = currentCoverImage;
                        vm.data.coverImageId = currentCoverImage.id;
                        vm.featuredModelUrl = currentCoverImage.url;
                        console.log('Restored cover image before publish/unpublish:', currentCoverImage);
                    }
                    
                    if (currentFeaturedViewableImage) {
                        vm.viewableManager.featuredViewableImage = currentFeaturedViewableImage;
                        vm.featuredViewableImageId = currentFeaturedViewableImageId;
                        vm.viewableManager.featuredModelUrl = currentFeaturedViewableImage.url;
                        console.log('Restored featured viewable image before publish/unpublish:', currentFeaturedViewableImage);
                    }
                    
                    // Explicitly set the publication status using the dedicated endpoint
                    return publicationService.publishUnpublishManual(publicationIdToOperateOn, targetStatus)
                        .then(function() {
                            // Update local state
                            vm.data.published = targetStatus;
                            updateSaveAndPublishActionState(); // Update button label and icon

                            if (!vm.data.coverImage && currentCoverImage) {
                                vm.data.coverImage = currentCoverImage;
                                vm.data.coverImageId = currentCoverImage.id;
                            }
                            
                            if (!vm.viewableManager.featuredViewableImage && currentFeaturedViewableImage) {
                                vm.viewableManager.featuredViewableImage = currentFeaturedViewableImage;
                                vm.featuredViewableImageId = currentFeaturedViewableImageId;
                            }

                            headerBannerService.setNotification('SUCCESS', UPDATED_SUCCESS, 5000);
                            
                            // Navigate back to publications list
                            try {
                                $state.go('publications');
                            } catch (e) {
                                console.error('Error during navigation:', e);
                                headerBannerService.setNotification('ERROR', WENT_WRONG, 5000);
                            }
                        });
                })
                .catch(function(error){
                    console.error("Error in save and publish process:", error);
                    var errorMsg = vm.data.published ? PUBLISH_FAILURE : UNPUBLISH_FAILURE; 
                    headerBannerService.setNotification('ERROR', errorMsg || WENT_WRONG, 5000);
                });
        }

        function updateSaveAndPublishActionState() {
            if (vm.actions && Array.isArray(vm.actions)) {
                const action = vm.actions.find(a => a.onClick === saveAndPublishPublication);

                if (action) {
                    if (vm.data.published) {
                        action.labelKey = 'CREATE_PUBLICATION.SAVE_AND_UNPUBLISH';
                        action.icon = 'fa fa-cloud-download'; // Update icon for unpublish
                    } else {
                        action.labelKey = 'CREATE_PUBLICATION.SAVE_AND_PUBLISH';
                        action.icon = 'fa fa-cloud-upload'; // Ensure default icon for publish
                    }
                } else {
                    // console.warn("Could not find the 'Save and Publish/Unpublish' action in vm.actions to update its label.");
                }
            } else {
                // console.warn("vm.actions is not defined or not an array. Cannot update 'Save and Publish/Unpublish' button state.");
            }
        }

        function prepareSubmitData() {
            // Find any viewable with featuredViewable: true
            var featuredViewable = vm.viewableManager.selectedViewables.find(function(viewable) {
                return viewable.featuredViewable === true;
            });
            
            // If a featured viewable is found, set it as the featured model ID
            if (featuredViewable) {
                vm.viewableManager.data.featuredModelId = featuredViewable.id;
                vm.viewableManager.setAsFeatured(featuredViewable);
            }
            
            vm.modelIds = vm.viewableManager.selectedViewables.map(function(viewable) {
                return viewable.id;
            });

            // Ensure image data is properly set for submission
            if (vm.data.coverImage && vm.data.coverImage.id) {
                vm.data.coverImageId = vm.data.coverImage.id;
            }
            
            if (vm.viewableManager.featuredViewableImage && vm.viewableManager.featuredViewableImage.id) {
                vm.featuredViewableImageId = vm.viewableManager.featuredViewableImage.id;
            }

            vm.featuredModelUrlForSubmit = vm.data.useViewableImage ? vm.viewableManager.featuredModelUrl : vm.featuredModelUrl;
            
        }

        function serviceFailed(err) {
            console.log(err);
            headerBannerService.setNotification("ERROR", WENT_WRONG, 3000);
        }

        function addCoverImage() {
            var cropType = "MACHINE";
            $uibModal
                .open({
                    templateUrl: "features/products/imageCropper/imageCropper.html",
                    controller: "ImageCropperController",
                    controllerAs: "imageCropperCtrl",
                    size: "xl",
                    backdrop: "static",
                    resolve: {
                        cropType: function () {
                            return cropType;
                        },
                        manufacturerId: function () {
                            return userService.getManufacturerId();
                        },
                        source: function () { 
                            return 'createPublication';
                        }
                    },
                })
                .result.then(function (response) {
                    if (response) {
                        // Get the image ID from the response
                        var imageId = typeof response === 'object' ? response.id : response;
                        
                        // Fetch the full image data using the new service
                        manufacturerPublicationService.getPublicationImage(imageId)
                            .then(function(imageResponse) {
                                if (imageResponse && imageResponse.data) {
                                    vm.data.coverImageId = imageId;
                                    vm.data.coverImage = {
                                        id: imageId,
                                        url: imageResponse.data.locationUrl
                                    };
                                    vm.featuredModelUrl = imageResponse.data.locationUrl;
                                    vm.coverImageTimestamp = Date.now();
                                    
                                    // Force UI update
                                    if (!$scope.$$phase) {
                                        $scope.$apply();
                                    }
                                }
                            })
                            .catch(function(error) {
                                console.error('Error fetching cover image:', error);
                                vm.data.coverImageId = null;
                                vm.data.coverImage = null;
                                vm.featuredModelUrl = null;
                                vm.coverImageTimestamp = Date.now();
                            });
                    }
                });
        }

        function addFeaturedImage() {
            var cropType = "MACHINE";
            $uibModal
                .open({
                    templateUrl: "features/products/imageCropper/imageCropper.html",
                    controller: "ImageCropperController",
                    controllerAs: "imageCropperCtrl",
                    size: "xl",
                    backdrop: "static",
                    resolve: {
                        cropType: function () {
                            return cropType;
                        },
                        manufacturerId: function () {
                            return userService.getManufacturerId();
                        },
                        source: function () { 
                            return 'createPublication';
                        }
                    },
                })
                .result.then(function (response) {
                    if (response) {
                        // Get the image ID from the response
                        var imageId = typeof response === 'object' ? response.id : response;
                        
                        manufacturerPublicationService.getPublicationImage(imageId)
                            .then(function(imageResponse) {
                                if (imageResponse && imageResponse.data) {
                                    vm.featuredViewableImageId = imageId;
                                    vm.viewableManager.featuredModelUrl = imageResponse.data.locationUrl;
                                    vm.featuredModelUrlForSubmit = imageResponse.data.locationUrl;
                                    vm.viewableManager.featuredViewableImage = {
                                        id: imageId,
                                        url: imageResponse.data.locationUrl
                                    };
                                    vm.featuredImageTimestamp = Date.now();
                                    
                                    // Force UI update
                                    if (!$scope.$$phase) {
                                        $scope.$apply();
                                    }
                                }
                            })
                            .catch(function(error) {
                                console.error('Error fetching featured image:', error);
                                vm.featuredViewableImageId = null;
                                vm.viewableManager.featuredModelUrl = null;
                                vm.featuredModelUrlForSubmit = null;
                                vm.viewableManager.featuredViewableImage = null;
                                vm.featuredImageTimestamp = Date.now();
                            });
                    }
                });
        }

        function getPublicationCategories() {
            publicationService.getPublicationCategories()
                .then(function (response) {
                    if (response && response.data && response.data.publicationCategories) {
                        vm.publicationCategories = response.data.publicationCategories;
                    } else {
                        console.warn('Unexpected response structure or no categories found:', response);
                        vm.publicationCategories = [];
                    }
                })
                .catch(function (error) {
                    console.error('Error fetching publication categories:', error);
                    vm.publicationCategories = [];
                });
        }

        function manageCategoriesModal() {            
            $uibModal
                .open({
                    templateUrl: "features/publications/manageCategories/manageCategories.html",
                    controller: "ManageCategoriesController",
                    controllerAs: "manageCategoriesCtrl",
                    size: "md", 
                    backdrop: "static",
                    resolve: {                        
                        existingCategories: function () {
                            return angular.copy(vm.publicationCategories);
                        }
                    },
                })
                .result.then(function (response) {
                    if (response) {
                        // nothing
                    }
                }, function () {
                    vm.getPublicationCategories();
                });
        }

        function isSubmitDisabled() {
            if (!vm.createPublicationForm) {
                return true;
            }
            return !vm.createPublicationForm.$valid;
        }
    }
})();
