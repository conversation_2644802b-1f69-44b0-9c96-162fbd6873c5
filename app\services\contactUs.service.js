(function () {
    'use strict';

    angular
        .module('app.services')
        .factory('contactUsService', contactUsService);

    contactUsService.$inject = ['$http', 'apiConstants'];

    function contactUsService($http, apiConstants) {
        return {
            submitMessage: submitMessage
        };

        function submitMessage(subject, message) {
            return $http.post(apiConstants.url + '/contactUs/', {subject: subject, message: message});
        }

    }
})();
