(function () {
    'use strict';

    angular
        .module('cadshareApp')
        .constant('apiConstants',
            {
                //************  LOCAL  ************
                url: 'http://localhost:5000',
                autodeskBucket: 'dev1bucket.cadshare.co',
                hppUrl: 'https://pay.sandbox.realexpayments.com/pay',
                jsAlertDisabled: true,
                cdeId: [0, 1],
                supremeId: [2],


                //************  DEV1  ************
                //url: 'http://development.qwsbgumwf2.eu-west-1.elasticbeanstalk.com',
                //autodeskBucket: 'dev1bucket.cadshare.co'

                //************  DEMO1  ************
                //url: 'https://d2opbj0o3pcszs.cloudfront.net',
                // autodeskBucket: 'demo1.cadshare.co'

                //************  TEST1  ************
                //url: 'https://d2ex5qluyitzv1.cloudfront.net',
                //autodeskBucket: 'test1.cadshare.co'

                //************  TEST2  ************
                //url: 'https://dbz9tmicz5bfu.cloudfront.net',
                //autodeskBucket: 'test1.cadshare.co'

                //************  PROD1  ************
                //url: 'https://api.cadshare.co',
                // autodeskBucket: 'webapp.cadshare.co'
            });
})();
