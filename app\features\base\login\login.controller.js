(function () {
    'use strict';

    angular
        .module('app.base')
        .controller('LoginController', LoginController);

    LoginController.$inject = ['tokenService', '$state', '$rootScope', '$timeout', 'userService', 'mfaService', '$location',
        '$stateParams', '$window', 'apiConstants', '$uibModal'];

    function LoginController(tokenService, $state, $rootScope, $timeout, userService, mfaService, $location, $stateParams,
                             $window, apiConstants, $uibModal) {
        var vm = this;
        vm.doRememberMe = false;
        vm.isEmailEntry = true;
        vm.emailNotRecognisedError = false;
        vm.emailPasswordIncorrect = false;
        vm.onAppSubdomain = false;
        vm.email = "";
        vm.password = "";
        vm.manufacturerId = "";

        vm.signIn = signIn;
        vm.backToEmailEntry = backToEmailEntry;
        vm.redirectToSubDomain = redirectToSubDomain;
        vm.goToForgotPassword = goToForgotPassword;
        vm.goToRegistrationForm = goToRegistrationForm;

        initialize();

        function initialize() {
            if(userService.isUserLoggedIn().then(()=>{
                $state.go('dashboard');
            }, () => {
                rememberMeCheck();
                checkBrowserCompatibility();
            }));
        }

        function signIn() {
            resetErrorMessages();
            if (vm.isEmailEntry) {
                tokenService.validateEmail(vm.email)
                    .then(validateEmailSuccess, validateEmailFailed)
            } else {
                authenticateUser();
            }
        }

        function isCustomerOrDealerUserType(response) {
            return response.data[0].userType.toUpperCase().includes("MANUFACTURER_SUB_ENTITY");
        }

        function validateEmailFailed() {
            vm.serverError = true;
        }

        function validateEmailSuccess(response) {
            if (response === undefined) {
                validateEmailFailed();
            } else {
                if (isOnValidSubDomain(response.data)) {
                    vm.isEmailEntry = false;
                    return;
                } else if (isOnAppSubdomain()) {
                    if (response.data.length === 1) {
                        if (response.data[0].subdomain) {
                            redirectToSubDomain(response.data[0].subdomain);
                            return;
                        }
                    } else if (response.data.length > 1) {
                        vm.altDomains = response.data;
                        vm.onAppSubdomain = true;
                        vm.isEmailEntry = false;
                        return;
                    }
                }
                vm.emailNotRecognisedError = true;
            }
        }


        function isOnAppSubdomain() {
            return getSubdomain().toUpperCase() === "App".toUpperCase();
        }

        function redirectToSubDomain(subDomain) {
            if (subDomain) {
                window.location.href = "https://" + subDomain;
            }
        }

        function getSubdomain() {
            return $location.host();
        }

        function isOnValidSubDomain(manufacturerList) {
            var isValid = false;
            var currentSubDomain = getSubdomain();
            for (var i = 0; i < manufacturerList.length; i++) {
                if (manufacturerList[i].subdomain.toUpperCase() === currentSubDomain.toUpperCase()) {
                    isValid = true;
                    vm.userId = manufacturerList[i].userId;
                    vm.manufacturerId = manufacturerList[i].id;
                }
            }

            return isValid;
        }

        function resetErrorMessages() {
            vm.emailNotRecognisedError = false;
            vm.emailPasswordIncorrect = false;
            vm.onAppSubdomain = false;
        }

        function backToEmailEntry() {
            vm.isEmailEntry = true;
            resetErrorMessages();
        }

        function rememberMeCheck() {
            var email = userService.getRememberMeUsername();
            if (email) {
                vm.doRememberMe = true;
                vm.email = email;
            } else {
                vm.doRememberMe = false;
                vm.email = email;
            }
        }

        function authenticateUser() {
            vm.credentialsError = false;
            vm.accountLocked = false;
            vm.serverError = false;
            if (vm.doRememberMe) {
                userService.setRememberMe(vm.email);
            } else {
                userService.clearRememberMe();
            }
            tokenService.oauthLogin(vm.userId, vm.password, vm.manufacturerId)
                .then(authenticateUserSuccess, authenticateUserFailed);
        }

        function authenticateUserSuccess() {
            userService.getUserLoginInfo()
                .then(function (response) {
                    var data = response.data;
                    if (data.using2FA) {
                        if (data.mfaSetupComplete) {
                            // Request Verification Code
                            $state.go('verificationCode');
                        } else {
                            //Setup QR scan and continue
                            mfaService.setQrCode(data.qrCode);

                            $state.go('verificationSetup');
                        }
                    } else {
                        userService.getUserInfo()
                            .then(getUserInfoSuccess, getUserInfoFailed);
                    }
                }, function (error) {
                    console.log(error);
                });
        }

        function getUserInfoSuccess() {
            //checkLanguageSettings();
            updateTrackJsSettings();

            if ($stateParams.redirect && $stateParams.redirect !== "") {
                $state.go($stateParams.redirect, $stateParams.params);
            } else {
                if (userService.isManufacturer()) {
                    if (userService.hasDashboardRole()) {
                        $state.go('dashboard');
                    } else if (userService.hasOrderRole()) {
                        $state.go('orders');
                    } else if (userService.hasProductsRole()) {
                        $state.go('products.catalogue');
                    } else if (userService.hasPublicationRole()) {
                        $state.go('publications');
                    } else if (userService.hasCustomerRole()) {
                        $state.go('customers');
                    } else if (userService.hasAdminRole()) {
                        $state.go('admin');
                    } else if (userService.hasSecurityRole()) {
                        $state.go('security');
                    } else if (userService.hasPartsRole()) {
                        $state.go('parts');
                    } else {
                        vm.rolesError = true;
                    }
                } else if(userService.isDealerPlusUser()) {
                    $state.go('dpOrders.myOrders.orders.enquiries');
                } else {
                    if (userService.hasPublishedProductsRole()) {
                        $state.go('publishedProducts');
                    } else if (userService.hasOrderRole()) {
                        $state.go('orders');
                    } else if (userService.hasPartSearchRole()) {
                        $state.go('partSearch');
                    }else{
                        $state.go('contactUs');
                    }
                }
            }

        }

        function getUserInfoFailed(error) {
            console.log(error);
        }

        function authenticateUserFailed(errorMessage) {
            $timeout(function() {
                if (errorMessage) {
                    if (errorMessage.includes('Bad credentials')) {
                        vm.credentialsError = true;
                    } else if (errorMessage.includes('User account is locked')) {
                        vm.accountLocked = true;
                    } else {
                        vm.serverError = true;
                    }
                } else {
                    vm.serverError = true;
                }
            });
        }

        function checkBrowserCompatibility() {
            var ua = window.navigator.userAgent;
            var msie = ua.indexOf("MSIE ");

            vm.browserIE = msie > 0 || !!navigator.userAgent.match(/Trident.*rv\:11\./);
        }

        function goToForgotPassword() {
            $state.go("forgotPassword", {manufacturerId: vm.manufacturerId, emailAddress: vm.email});
        }

        function updateTrackJsSettings(){
            window.TrackJS && TrackJS.configure({
                userId: JSON.stringify(vm.userId) + " - " + JSON.stringify(vm.email)
            });
        }

        function goToRegistrationForm() {
            $state.go("register");
        }

    }
})();

