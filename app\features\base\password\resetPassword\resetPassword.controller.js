(function () {
    'use strict';

    angular
        .module('app.base')
        .controller('ResetPasswordController', ResetPasswordController);

    ResetPasswordController.$inject = ['$stateParams', 'userService', '$rootScope', '$location', '$sce', '$translate'];

    function ResetPasswordController($stateParams, userService, $rootScope, $location, $sce, $translate) {
        var vm = this;

        vm.showPasswordRequirementsAsAlert = false;
        vm.showSuccessMessage = false;
        vm.showPasswordMismatchMessage = false;
        vm.recoveryCode = "";
        vm.newPassword = "";
        vm.confirmPassword = "";
        vm.errorMessage = "";

        var EXPIRED = "";
        var FORGOT, CREATE, RESET;
        $translate(['PASSWORD.CREATE','PASSWORD.RESET', 'PASSWORD.EXPIRED', 'PASSWORD.FORGOT_PASSWORD'])
            .then(function (resp) {
                CREATE = resp["PASSWORD.CREATE"];
                RESET = resp["PASSWORD.RESET"];
                EXPIRED = resp["PASSWORD.EXPIRED"];
                FORGOT = resp["PASSWORD.FORGOT_PASSWORD"];

                if ($location.search().action) {
                    vm.passwordAction = $location.search().action === "create" ? CREATE : RESET;
                }
            });


        vm.resetPassword = resetPassword;
        vm.arePasswordsValid = arePasswordsValid;

        initialize();

        function initialize() {
            checkBrowserCompatibility();
            vm.recoveryCode = $stateParams.code ? $stateParams.code : "";
        }

        function resetPassword() {

            if (arePasswordsValid() && !vm.form.confirmPassword.$pristine) {
                vm.errorMessage = '';
                userService.resetPassword(vm.newPassword, vm.recoveryCode)
                    .then(resetPasswordSuccess, resetPasswordFailed);
            }
        }

        function resetPasswordSuccess(response) {
            vm.showAsAlert = response.data;
            vm.showSuccessMessage = response.data;
        }

        function resetPasswordFailed(response) {
            vm.errorMessage = response.data.message === "The reset code has expired." ? $sce.trustAsHtml(EXPIRED + " <a ui-sref='forgotpassword'>" + FORGOT + "</a>") : response.data.message;
            vm.showSuccessMessage = false;
        }

        function arePasswordsValid() {
            if (vm.form.confirmPassword.$pristine) {
                return true;
            }
            return vm.newPassword !== "" && vm.confirmPassword !== "" && vm.newPassword === vm.confirmPassword;

        }

        function checkBrowserCompatibility() {
            var ua = window.navigator.userAgent;
            var msie = ua.indexOf("MSIE ");

            vm.browserIE = msie > 0 || !!navigator.userAgent.match(/Trident.*rv\:11\./);
        }
    }
})();
