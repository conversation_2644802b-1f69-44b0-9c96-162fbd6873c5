(function () {
    'use strict';

    angular
        .module('app.parts')
        .controller('LinkController', LinkController);

    LinkController.$inject = ['linkObject', 'masterPartService', '$uibModalInstance', 'manufacturerPublicationService'];

    function LinkController(linkObject, masterPartService, $uibModalInstance, manufacturerPublicationService) {
        var vm = this;
        var isEdit = false;
        var isEditSetup = false;

        vm.masterPartId = linkObject.masterPartId;
        vm.partNumber = linkObject.partNumber;
        vm.errors = {};
        vm.isMachineDropdownDisabled = true;
        vm.isViewableDropdownDisabled = true;

        vm.rangeChanged = rangeChanged;
        vm.machineChanged = machineChanged;
        vm.save = save;
        vm.cancel = $uibModalInstance.dismiss;

        initialize();

        function initialize() {
            vm.ranges = null;
            vm.rangeId = null;
            vm.machines = null;
            vm.machineId = null;
            vm.viewables = null;
            vm.viewableId = null;
            isEdit = !!linkObject.rangeId;
            isEditSetup = isEdit;

            manufacturerPublicationService.getRangeByManufacturer()
                .then(getRangeSuccess, serviceFailure);
        }

        function getRangeSuccess(response) {
            vm.ranges = response.data.productRanges;
            vm.isRangeDropdownDisabled = false;

            if (isEditSetup) {
                rangeChanged(linkObject.rangeId);
            }
        }

        function serviceFailure(err) {
            console.log("Service error: " + err);
            vm.errors.serviceError = true;
        }

        function rangeChanged(rangeId) {
            if (rangeId) {
                vm.isMachineDropdownDisabled = true;
                vm.isViewableDropdownDisabled = true;
                vm.rangeId = rangeId;
                vm.machineId = null;
                vm.machines = null;
                vm.viewableId = null;
                vm.viewables = null;
                manufacturerPublicationService.getMachineByRange(rangeId)
                    .then(machineRangeSuccess, serviceFailure);
            }
        }

        function machineRangeSuccess(response) {
            vm.machines = response.data;
            vm.isMachineDropdownDisabled = false;

            if (isEditSetup) {
                machineChanged(linkObject.machineId);
            }
        }

        function machineChanged(machineId) {
            if (machineId) {
                vm.isViewableDropdownDisabled = true;
                vm.machineId = machineId;
                vm.viewables = null;
                vm.viewableId = null;
                manufacturerPublicationService.getCompletedModelUploadsByMachine(machineId)
                    .then(getViewablesByMachineSuccess, serviceFailure);
            }
        }

        function getViewablesByMachineSuccess(response) {
            if (response && response.data.length > 0) {
                vm.isViewableDropdownDisabled = false;
                vm.viewables = response.data;

                if (isEditSetup) {
                    vm.viewableId = linkObject.viewableId;
                    isEditSetup = false;
                }
            }
        }

        function save() {
            if (isEdit) {
                masterPartService.editLink(vm.masterPartId, vm.viewableId)
                    .then(createEditLinkSuccess, serviceFailure);
            } else {
                masterPartService.createLink(vm.masterPartId, vm.viewableId)
                    .then(createEditLinkSuccess, serviceFailure);
            }
        }

        function createEditLinkSuccess() {
            $uibModalInstance.close();
        }
    }
})();
