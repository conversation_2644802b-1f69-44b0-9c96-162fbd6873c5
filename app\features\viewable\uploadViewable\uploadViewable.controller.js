(function () {
    "use strict";

    angular.module("app.viewable").controller("UploadViewableController", UploadViewableController);

    UploadViewableController.$inject = [
        "$uibModalInstance",
        "$http",
        "$base64",
        "apiConstants",
        "machineId",
        "uploadModelService",
        "viewerService",
        "tokenService",
        "manufacturerMachineService",
        "$scope",
        "$timeout",
        "$q",
    ];

    function UploadViewableController(
        $uibModalInstance,
        $http,
        $base64,
        apiConstants,
        machineId,
        uploadModelService,
        viewerService,
        tokenService,
        manufacturerMachineService,
        $scope,
        $timeout,
        $q
    ) {
        var vm = this;

        var successfulChunkUploads = 0;
        const MAX_CHUNKS = 25;
        const MIN_CHUNK_SIZE = 5 * 1024 * 1024;

        var fileLength = 0;
        var failCount = 0;
        var hasFailed = false;
        var originalFileName = "";

        vm.isFileSelected = false;
        vm.assemblySelected = false;
        vm.assemblys = [];
        vm.progressSize = 0;
        vm.loading = false;
        vm.showPRTFiles = false;
        vm.isValid = true;

        vm.fileChanged = fileChanged;
        vm.addToMachine = addToMachine;
        vm.showPRTFilesChanged = showPRTFilesChanged;

        vm.cancel = $uibModalInstance.dismiss;

        function confirm() {
            $uibModalInstance.close();
        }

        function isValidFile(file) {
            var ext = file.name.slice(file.name.lastIndexOf("."));

            var nameLength = file.name.length - ext.length;

            if (nameLength > 80) {
                return false;
            }
            return true;
        }

        function fileChanged(obj) {
            vm.loading = true;

            var elem = obj.target || obj.srcElement;
            if (elem.files.length > 0) {
                vm.file = elem.files[0];

                if (!isValidFile(vm.file)) {
                    vm.isValid = false;
                    vm.loading = false;
                    $scope.$digest();
                    return;
                }

                vm.isValid = true;

                var uploadType = null;
                vm.isFileSelected = true;
                $scope.$digest();

                var ext = vm.file.name.substring(vm.file.name.lastIndexOf(".") + 1);

                if (
                    ext.toUpperCase() === "PDF" ||
                    ext.toUpperCase() === "STP" ||
                    ext.toUpperCase() === "STEP" ||
                    ext.toUpperCase() === "F3D" ||
                    ext.toUpperCase() === "JT" ||
                    ext.toUpperCase() === "SKP" ||
                    ext.toUpperCase() === "DWF" ||
                    ext.toUpperCase() === "IPT" ||
                    ext.toUpperCase() === "NWD"
                ) {
                    uploadType = "single";
                    vm.assemblySelected = false;
                } else if (ext === "zip" || ext === "ZIP") {
                    uploadType = "assembly";
                    vm.assemblySelected = true;
                }

                if (uploadType) {
                    if (vm.assemblySelected) {
                        uploadModelService.getZipFolderContents(vm.file, vm.showPRTFiles, function (response) {
                            vm.assemblys = response;
                            vm.disableDropdown = false;
                            vm.isAssembly = true;
                            vm.loading = false;
                            $scope.$digest();
                        });
                    } else {
                        vm.isAssembly = false;
                        vm.assemblys = [];
                        vm.loading = false;
                        $scope.$digest();
                    }
                } else {
                    console.log("INVALID FILE TYPE");
                }
            } else {
                vm.isFileSelected = false;
                vm.loading = false;
                $scope.$digest();
            }
        }

        function showPRTFilesChanged() {
            vm.loading = true;
            uploadModelService.getZipFolderContents(vm.file, vm.showPRTFiles, function (response) {
                vm.assemblys = response;
                vm.loading = false;
                $scope.$digest();
            });
        }

        function addToMachine() {
            vm.loading = true;
            viewerService.getAutodeskToken().then(
                function (accessToken) {
                    originalFileName = vm.file.name;
                    var sanitizedFileName = vm.file.name.replace(/\s+/g, "");
                    var nameTimestamp = Date.now().toString().concat(encodeURIComponent(sanitizedFileName));

                    if (vm.file.size > MIN_CHUNK_SIZE) {
                        chunkedUploadToS3(nameTimestamp, vm.file, accessToken.accessToken);
                    } else {
                        singleUploadToS3(nameTimestamp, vm.file, accessToken.accessToken);
                    }
                },
                function (error) {
                    console.error("failed at: addToMachine: ", error);
                }
            );
        }

        function getSignedUrls(token, bucketKey, objectKey, numChunks) {
            var deferred = $q.defer();

            var xhr = new XMLHttpRequest();
            var url = `https://developer.api.autodesk.com/oss/v2/buckets/${bucketKey}/objects/${encodeURIComponent(
                objectKey
            )}/signeds3upload?parts=${numChunks}&minutesExpiration=60`;

            xhr.open("GET", url, true);
            xhr.setRequestHeader("Authorization", "Bearer " + token);
            xhr.onload = function () {
                if (xhr.status === 200) {
                    deferred.resolve(JSON.parse(xhr.responseText));
                } else {
                    deferred.reject(new Error("Failed to get signed URLs"));
                }
            };
            xhr.onerror = function () {
                deferred.reject(new Error("XHR error when fetching signed URLs"));
            };
            xhr.send();

            return deferred.promise;
        }

        function chunkedUploadToS3(name, fileToUpload, token) {
            vm.progress = true;
            vm.progressSize = 0;
            successfulChunkUploads = 0;
            hasFailed = false;
            failCount = 0;
            fileLength = fileToUpload.size;
            var sliceMethod = getSliceMethod(fileToUpload);

            let calculatedChunkSize = Math.ceil(fileToUpload.size / MAX_CHUNKS);
            if (calculatedChunkSize < MIN_CHUNK_SIZE) {
                calculatedChunkSize = MIN_CHUNK_SIZE;
            }
            let totalChunks = Math.ceil(fileToUpload.size / calculatedChunkSize);

            getSignedUrls(token, apiConstants.autodeskBucket, name, totalChunks).then(function (response) {
                var signedUrls = response.urls;
                var uploadKey = response.uploadKey;
                if (!signedUrls || !signedUrls.length) {
                    console.error("Signed URLs not received or empty:", signedUrls);
                }
                for (var i = 0; i < totalChunks; i++) {
                    var rangeStart = i * calculatedChunkSize;
                    var rangeEnd = Math.min((i + 1) * calculatedChunkSize, fileToUpload.size);
                    var chunk = fileToUpload[sliceMethod](rangeStart, rangeEnd);
                    sendChunk(chunk, signedUrls[i], name, token, uploadKey, calculatedChunkSize, totalChunks);
                }
            });
        }

        function sendChunk(chunk, signedUrl, name, token, uploadKey, calculatedChunkSize, totalChunks) {
            var xhr = new XMLHttpRequest();
            xhr.open("PUT", signedUrl, true);
            xhr.onload = function () {
                if (xhr.status >= 200 && xhr.status < 300) {
                    vm.progressSize += calculatedChunkSize;
                    vm.progressPercentage = Math.min(100, parseInt((100.0 * vm.progressSize) / fileLength));

                    successfulChunkUploads++;

                    if (successfulChunkUploads === totalChunks) {
                        notifyAutodeskOfCompletion(name, token, uploadKey);
                    }
                } else {
                    handleChunkError(chunk, signedUrl, name, token, uploadKey);
                }
            };
            xhr.onerror = function () {
                handleChunkError(chunk, signedUrl, name, token, uploadKey);
            };
            xhr.send(chunk);
        }

        function singleUploadToS3(name, file, token) {
            vm.progress = true;
            var sliceMethod = getSliceMethod(file);
            var chunk = file[sliceMethod](0, file.size);

            getSignedUrls(token, apiConstants.autodeskBucket, name, 1).then(function (response) {
                var signedUrls = response.urls;
                var uploadKey = response.uploadKey;

                var xhr = new XMLHttpRequest();
                xhr.open("PUT", signedUrls[0], true);
                xhr.onload = function () {
                    if (xhr.status >= 200 && xhr.status < 300) {
                        vm.progressPercentage = 100;
                        vm.progress = false;
                        notifyAutodeskOfCompletion(name, token, uploadKey);
                    } else {
                        uploadToAutodeskFailed();
                    }
                };
                xhr.onerror = uploadToAutodeskFailed;
                xhr.send(chunk);
            });
        }

        function notifyAutodeskOfCompletion(name, token, uploadKey) {
            var xhr = new XMLHttpRequest();
            xhr.open(
                "POST",
                `https://developer.api.autodesk.com/oss/v2/buckets/${apiConstants.autodeskBucket}/objects/${encodeURIComponent(
                    name
                )}/signeds3upload`,
                true
            );
            xhr.setRequestHeader("Authorization", "Bearer " + token);
            xhr.setRequestHeader("Content-Type", "application/json");
            xhr.onload = function () {
                if (xhr.status >= 200 && xhr.status < 300) {
                    var response = JSON.parse(xhr.responseText);
                    vm.fileUploadedSuccess = true;
                    var fileType = vm.file.name.includes(".zip") ? "ARCHIVE" : "SINGLE_FILE";
                    createModel(vm.modelName, machineId, response.objectId, vm.topLevelAssembly, fileType, name);
                } else {
                    uploadToAutodeskFailed();
                }
            };
            xhr.onerror = uploadToAutodeskFailed;
            xhr.send(JSON.stringify({ uploadKey: uploadKey }));
        }

        function uploadToAutodeskFailed() {
            vm.progress = false;
            vm.fileUploadedFailure = true;
        }

        function handleChunkError(chunk, signedUrl, name, token, uploadKey) {
            failCount++;
            if (failCount < 10) {
                setTimeout(function () {
                    sendChunk(chunk, signedUrl, name, token, uploadKey);
                }, 5000);
            } else if (!hasFailed) {
                vm.fileUploadedFailure = true;
                hasFailed = true;
                vm.progress = false;
            }
        }

        function getSliceMethod(file) {
            if ("mozSlice" in file) {
                return "mozSlice";
            } else if ("webkitSlice" in file) {
                return "webkitSlice";
            }
            return "slice";
        }

        function createModel(modelName, machineId, urn, topLevelAssembly, fileType, fileName) {
            urn = $base64.encode(urn);
            manufacturerMachineService
                .createModel(modelName, machineId, urn, topLevelAssembly, fileType, fileName, originalFileName)
                .then(createModelSuccess, createModelFailure);
        }

        function createModelSuccess(resp) {
            vm.loading = false;
            $uibModalInstance.close();
        }

        function createModelFailure(resp) {
            vm.loading = false;
            $uibModalInstance.dismiss();
        }
    }
})();
