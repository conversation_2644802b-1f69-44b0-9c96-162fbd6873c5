(function () {
    'use strict';

    angular
        .module('app.viewer')
        .directive('nonModeledPartsSummary', nonModeledPartsSummary);

    function nonModeledPartsSummary() {
        var directive = {
            restrict: 'E',
            templateUrl: 'features/viewer/extensions/nonModeledParts/nonModeledPartsSummary/nonModeledPartsSummary.html',
            controller: 'NonModeledPartSummaryController',
            controllerAs: 'nonModeledPartSummaryCtrl',
            bindToController: true
        };
        return directive;
    }

})();