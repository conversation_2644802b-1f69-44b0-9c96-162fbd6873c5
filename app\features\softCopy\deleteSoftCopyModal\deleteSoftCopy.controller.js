(function () {
    'use strict';

    angular
        .module('app.customer')
        .controller('DeleteSoftCopyController', DeleteSoftCopyController);

    DeleteSoftCopyController.$inject = ['softCopyService', '$uibModalInstance', 'softCopy'];

    function DeleteSoftCopyController(softCopyService, $uibModalInstance, softCopy) {
        var vm = this;

        vm.cancel = $uibModalInstance.dismiss;
        vm.softCopy = softCopy;

        vm.deleteSoftCopy = deleteSoftCopy;

        function deleteSoftCopy() {
            vm.isDisabled = true;

            softCopyService.deleteSoftCopy(softCopy.id)
                .then(deleteSoftCopySuccess, deleteSoftCopyFailed);
        }

        function deleteSoftCopySuccess() {
            $uibModalInstance.close();
        }

        function deleteSoftCopyFailed(response) {
            vm.deleteFailure = true;
            vm.internalFailureMessage = response.data.message;
        }
    }
})();