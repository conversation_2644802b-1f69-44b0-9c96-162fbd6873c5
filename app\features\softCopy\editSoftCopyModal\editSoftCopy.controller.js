(function () {
    'use strict';

    angular
        .module('app.viewable')
        .controller('EditSoftCopyController', EditSoftCopyController);

    EditSoftCopyController.$inject = ['softCopyService', '$uibModalInstance', 'softCopy'];

    function EditSoftCopyController(softCopyService, $uibModalInstance, softCopy) {

        var vm = this;
        vm.cancel = $uibModalInstance.dismiss;
        vm.softCopyName = softCopy.name;
        vm.submitDisabled = false;

        vm.editSoftCopy = editSoftCopy;

        initialize();

        function initialize() {
        }

        function editSoftCopy() {
            vm.submitDisabled = true;

            softCopy.name = vm.softCopyName;
            softCopyService.updateSoftCopy(softCopy.id, softCopy)
                .then(editSoftCopySuccess, editSoftCopyFailure);
        }

        function editSoftCopySuccess() {
            $uibModalInstance.close();
        }

        function editSoftCopyFailure(error) {
            console.log(error);
        }

    }
})();
