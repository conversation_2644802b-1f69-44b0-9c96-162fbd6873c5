<div class="vertical-align loader" id="loader">
    <div id="loader-text">
        <i class="fas fa-sync-alt fa-spin"></i>
        <p translate>CUST_PDF_VIEWER.LOADING</p>
        <p translate>CUST_PDF_VIEWER.FEW_MINS</p>
    </div>
</div>

<div class="mobile-orientation-alert d-md-none d-flex justify-content-center align-items-center">
    <div class="text-center">
        <i class="fa fa-mobile-alt fa-3x mb-3"></i>
        <h3 translate>CUST_PDF_VIEWER.LANDSCAPE_MOBILE</h3>
        <p translate>CUST_PDF_VIEWER.FLIP_YOUR_PHONE</p>
    </div>
</div>

<header class="pdfHeader navbar navbar-expand-lg navbar-light d-flex justify-content-between bg-light p-0">
    <a ng-if="!customerPdfViewerCtrl.fromWhereUsedModal && !customerPdfViewerCtrl.showCloseButton" href="" class="d-flex align-items-center back-to-products" ng-click="customerPdfViewerCtrl.backToViewables()">
        <div class="viewer-back-arrow px-4">
            <i class="fa fa-chevron-left"></i>
        </div>
        <h3 class="text-overflow mb-0">
            {{customerPdfViewerCtrl.machineName}}- {{customerPdfViewerCtrl.viewableName}}<br/>
            <small translate>CUST_PDF_VIEWER.BACK_TO_VIEWABLES</small>
        </h3>
    </a>

    <a ng-if="customerPdfViewerCtrl.fromWhereUsedModal" href="" class="d-flex align-items-center back-to-products" ng-click="customerPdfViewerCtrl.goBackToPartSearch()">
        <div class="viewer-back-arrow px-4">
            <i class="fa fa-chevron-left"></i>
        </div>
        <h3 class="text-overflow mb-0">
            {{customerPdfViewerCtrl.machineName}}- {{customerPdfViewerCtrl.viewableName}}<br/>
            <small translate>CUST_PDF_VIEWER.BACK_TO_PART_SEARCH</small>
        </h3>
    </a>

    <a ng-if="customerPdfViewerCtrl.showCloseButton" href="" class="d-flex align-items-center back-to-products" ng-click="customerPdfViewerCtrl.closeTab()">
        <div class="viewer-back-arrow px-4">
            <i class="fa fa-chevron-left"></i>
        </div>
        <h3 class="text-overflow mb-0">
            {{customerPdfViewerCtrl.machineName}}- {{customerPdfViewerCtrl.viewableName}}<br/>
            <small translate>CUST_VIEWER.CLOSE_TAB</small>
        </h3>
    </a>

    <div ng-if="customerPdfViewerCtrl.hasOrdersAccess">
        <button class="btn-model basketButtonWidth" ng-click="customerPdfViewerCtrl.openBasket()"
                ng-hide="customerPdfViewerCtrl.isBasketOpen">
            <i class="fa fa-shopping-basket" aria-hidden="true"></i> &nbsp;{{"CUST_PDF_VIEWER.VIEW_BASKET" | translate}}
            ({{customerPdfViewerCtrl.basketSize}})
        </button>
    </div>
    <div ng-show="customerPdfViewerCtrl.isBasketOpen">
        <button class="btn-model basketButtonWidth openBasket d-flex align-items-center justify-content-center cadGap" ng-click="customerPdfViewerCtrl.closeBasket()"
                ng-show="customerPdfViewerCtrl.isBasketOpen">
            <h3 class="upper mb-0">{{"CUST_PDF_VIEWER.BACK_TO_ADD_PART" | translate}}</h3></i>
            <i class="fas fa-chevron-right"></i>
        </button>
    </div>
</header>

<div class="container-fluid flex-column">
    <div class="row d-flex">
        <!-- Left Column -->
        <div class="col-md-2 p-0" ng-class="{'scrollable-col-default': !customerPdfViewerCtrl.onBehalfOf, 'scrollable-col-on-behalf': customerPdfViewerCtrl.onBehalfOf}">
            <div class=""
                 ng-class="{'on-behalf-of-shrink': customerPdfViewerCtrl.onBehalfOf}">
                <div class="">

                    <div class="product-thumb-cell thumb-cell-box-shadow m-2" ng-repeat="page in customerPdfViewerCtrl.pdfPages track by $index">

                        <div ng-class="page.selected === true ? 'selected-pdf' : 'unselected-pdf'"
                             ng-click="customerPdfViewerCtrl.goToPage(page.stateId)" class="pdf-title">
                            <span>{{page.stateId -0 +1 }} - {{page.stateName}}</span>
                        </div>

                    </div>

                </div>
            </div>
        </div>

        <!-- Middle Column -->
        <div class="col-md-7 p-0">

            <div class="viewer" id="MyViewerDiv"></div>


            <div class="customerPdfViewer product-viewer vertical" ng-class="{'on-behalf-of-shrink': customerPdfViewerCtrl.onBehalfOf}">
                <div class="watermark-parent" id="watermark-container" ng-show="customerPdfViewerCtrl.showWatermark">
                    <div class="watermark" id="watermark-tile">
                        <img src="{{customerPdfViewerCtrl.watermarkImage}}" id="watermark-image">
                        <div>{{"CUST_VIEWER.PATENT_PROTECTED" | translate}} {{customerPdfViewerCtrl.fullName}}
                            {{customerPdfViewerCtrl.emailAddress}}
                        </div>
                    </div>
                </div>

                <viewer-banner></viewer-banner>

            </div>

            <div id="forgeViewer" ng-class="{'scrollable-col-default': !customerPdfViewerCtrl.onBehalfOf, 'scrollable-col-on-behalf': customerPdfViewerCtrl.onBehalfOf}"></div>

            <form name="addManualPartForm" class="row d-flex align-items-end p-2 no-gutters pdfViewerFooterContainerMin" ng-show="customerPdfViewerCtrl.hasOrdersAccess && (!customerPdfViewerCtrl.isSupreme || customerPdfViewerCtrl.isAdditionalPartsEnabled)">
                <div class="col pr-2">
                    <h5 class="d-inline-flex font-weight-bold m-0" translate>CUST_PDF_VIEWER.PART_NUM</h5>
                    <input placeholder="{{'CUST_PDF_VIEWER.PART_NUMBER' | translate}}" class="mb-0" type="text" id="manualPartNumber"
                           ng-model="customerPdfViewerCtrl.manualPart.partNumber" required name="partNumber"></div>

                <div class="col-auto pr-2"><h5 class="d-inline-flex font-weight-bold m-0" translate>CUST_PDF_VIEWER.PART_DESC</h5>
                    <input placeholder="{{'CUST_PDF_VIEWER.PART_DESCRIPTION_EXAMPLE' | translate}}" class="mb-0" id="manualPartDescription" type="text" ng-model="customerPdfViewerCtrl.manualPart.partDescription" required></div>

                <div class="col pr-2"><h5 class="d-inline-flex font-weight-bold m-0" translate>CUST_PDF_VIEWER.QTY</h5>
                    <input placeholder="{{'CUST_PDF_VIEWER.QUANTITY' | translate}}" ng-model="customerPdfViewerCtrl.manualPart.quantity" type="number"
                           id="manualPartQuantity" class="mb-0" min="1" max="1000" ng-model-options=""></div>

                <div class="col-auto mt-1 mt-md-0">
                    <button class="btn primary" ng-click="customerPdfViewerCtrl.addManualPartToBasket()">
                        <span class="m-1" ng-hide="customerPdfViewerCtrl.isAddBasketButtonClicked" ><i class="fas fa-cart-plus"></i></span>
                        <span class="m-1" ng-show="customerPdfViewerCtrl.isAddBasketButtonClicked" ><i class="fa fa-check"></i></span>
                    </button>
                </div>

            </form>

        </div>

        <!-- Right Column -->
        <div class="col-lg-3 col-md-6 col-6 p-0 position-absolute partColumnWidth" ng-class="{'scrollable-col-default': !customerPdfViewerCtrl.onBehalfOf, 'scrollable-col-on-behalf': customerPdfViewerCtrl.onBehalfOf}">
            <div ng-hide="customerPdfViewerCtrl.isBasketOpen" >
                    <h2 class="mb-0 p-2" translate>BUY_PART.TITLE</h2>
                    <div class="basket-box side-forms mh-100">
                        <table class="tableViewer w-100 table-bordered bg-white">
                            <thead>
                            <tr>
                                <th translate>BUY_PART.PART_ID</th>
                                <th translate>BUY_PART.PART_NUMBER</th>
                                <th translate>BUY_PART.PART_DESCRIPTION</th>
                                <th translate>BUY_PART.QTY</th>
                            </tr>
                            </thead>

                            <tbody>

                            <tr ng-repeat="part in customerPdfViewerCtrl.partsList track by part.partNumber+part.partId+part.partDescription">

                                <td class="font-weight-bold text-nowrap">{{part.itemNumber}}
                                </td>
                                <td class="col-3">{{part.partNumber}}</td>
                                <td class="wordWrap-partDescription">{{part.partDescription ? part.partDescription : part.description}}</td>
                                <td>
                                    <input type="number" ng-model="part.quantity" min="0" pattern="[0-9.]+"
                                           class="quantity-box inputWidth2D">
                                </td>
                                <td>
                                    <button class="btn primary mr-0" ng-click="customerPdfViewerCtrl.addPartToBasket(part)">
                                        <div ng-hide="part.clicked"><i class="fas fa-cart-plus"></i></div>
                                        <div ng-show="part.clicked"><i class="fa fa-check"></i></div>
                                    </button>
                                </td>
                            </tr>
                            </tbody>



                        </table>

                        <div class="add-part-button-group text-right stickyButton">
                            <button class="btn primary ng-binding" ng-if="customerPdfViewerCtrl.hasOrdersAccess" ng-disabled="customerPdfViewerCtrl.isQuantityZero()" ng-click="customerPdfViewerCtrl.addPartsToBasket()" type="button" translate>
                                BUY_PART.ADD_BASKET
                            </button>
                        </div>

                    </div>

            </div>

            <div class="" ng-show="customerPdfViewerCtrl.isBasketOpen">

                <div ng-class="customerPdfViewerCtrl.part.number !== '' ? 'viewer-basket-plus' : 'viewer-basket-only'">
                    <div class="px-3 pt-3">
                        <i class="fa fa-shopping-basket cadGap d-inline-flex align-items-baseline" aria-hidden="true"><h3 ng-show="customerPdfViewerCtrl.basketSize > 0">
                            {{"CUST_PDF_VIEWER.CURRENT_BASKET" | translate}} ({{customerPdfViewerCtrl.basketSize}})</h3>
                            <h3 ng-show="customerPdfViewerCtrl.basketSize < 1">{{"CUST_PDF_VIEWER.EMPTY_BASKET" | translate}}</h3></i>
                        <p ng-show="customerPdfViewerCtrl.manualPartsCount > 0" >
                            {{"CUST_PDF_VIEWER.THERE_ARE" | translate}} <strong>{{customerPdfViewerCtrl.manualPartsCount}}</strong> {{"CUST_PDF_VIEWER.NO_PREVIEW" | translate}}
                        </p>
                    </div>
                    <div class="basket-box" ng-hide="customerPdfViewerCtrl.basket.length < 1">
                        <table class="tableViewer w-100 table-bordered bg-white">
                            <thead>
                            <tr>
                                <th translate>CUST_PDF_VIEWER.ID</th>
                                <th translate>CUST_PDF_VIEWER.DESCRIPTION</th>
                                <th translate>CUST_PDF_VIEWER.QTY</th>
                                <th></th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr ng-repeat="part in customerPdfViewerCtrl.basket track by part.partNumber+part.modelId"
                                class="basket-add-animation">
                                <td>{{part.partNumber}}
                                </td>
                                <td>{{part.partDescription}}</td>
                                <td><input ng-model="part.quantity" ng-change="customerPdfViewerCtrl.partUpdated(part)"
                                           type="number" min="0" ng-model-options="{debounce: 500}" class="quantity-box">
                                </td>
                                <td><a href="" ng-click="customerPdfViewerCtrl.removePart(part)"
                                       class="dark-secondary fa fa-trash"></a></td>
                            </tr>
                            </tbody>
                        </table>

                    </div>

                    <div class="add-part-button-group text-right stickyButton">
                        <button class="btn primary ng-binding" ng-if="customerPdfViewerCtrl.hasOrdersAccess" ng-disabled="customerPdfViewerCtrl.basketSize < 1" ng-click="customerPdfViewerCtrl.goToCreateEnquiry()"type="button" translate>
                            CUST_PDF_VIEWER.PLACE_ORDER
                        </button>
                    </div>

                </div>
            </div>
            </div>
        </div>
</div>