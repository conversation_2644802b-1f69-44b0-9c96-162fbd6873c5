(function () {
    'use strict';

    angular
        .module('app.shared')
        .controller('CustomNumberController', CustomNumberController);

    CustomNumberController.$inject = ['$uibModalInstance', 'customNumberObject', 'ordersService', '$translate', 'userService', '$state'];

    function CustomNumberController($uibModalInstance, customNumberObject, ordersService, $translate, userService, $state) {
        var vm = this;

        vm.save = save;
        vm.cancel = $uibModalInstance.dismiss;
        vm.isDealerPlusPage = isDealerPlusPage

        var ALREADY_IN_USE, ERROR;
        $translate(['CUSTOM_NUMBER.ALREADY_IN_USE', 'GENERAL.ERROR'])
            .then(function (resp) {
                ALREADY_IN_USE = resp["CUSTOM_NUMBER.ALREADY_IN_USE"];
                ERROR = resp["GENERAL.ERROR"];
            });

        if (customNumberObject) {
            vm.orderId = customNumberObject.orderId;
            vm.customOrderDisplay = customNumberObject.customOrderDisplay;
        }

        function save() {
            vm.hasError = false;
            vm.numberInUse = false;
            ordersService.updateCustomOrderNumber(vm.customOrderDisplay, vm.orderId)
                .then(saveSuccess, saveFailure);
        }

        function saveSuccess() {
            $uibModalInstance.close(vm.customOrderDisplay);
        }

        function saveFailure(error) {
            vm.hasError = true;
            if (error.status === 409) {
                vm.errorMessage = ALREADY_IN_USE;
            } else {
                vm.errorMessage = ERROR;
            }
        }

        function isDealerPlusPage(){
            return userService.isDealerPlusUser() && $state.current.name.includes("customerOrders");
        }
    }
})();
