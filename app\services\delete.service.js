(function () {
    'use strict';

    angular
        .module('app.shared')
        .factory('deleteService', deleteService);

    deleteService.$inject = ['$http', 'apiConstants'];

    function deleteService($http, apiConstants) {
        return {
        	commonDelete : commonDelete
        };
        
        function commonDelete(url) {
            return $http.delete(apiConstants.url + url, null);
        }
        
    }
})();
