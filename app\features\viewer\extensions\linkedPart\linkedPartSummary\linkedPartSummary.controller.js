(function () {
    'use strict';

    angular
        .module('app.viewer')
        .controller('LinkedPartSummaryController', LinkedPartSummaryController);

    LinkedPartSummaryController.$inject = ['linkedPartService', 'viewerBannerService', '$stateParams', '$uibModal', '$rootScope', '$scope'];

    function LinkedPartSummaryController(linkedPartService, viewerBannerService, $stateParams, $uibModal, $rootScope, $scope) {
        var vm = this;

        vm.isOpen = true;
        vm.existingLinkedParts = {};
        vm.createLinkedPart = createLinkedPart;
        vm.editLinkedPart = editLinkedPart;
        vm.deleteLinkedPart = deleteLinkedPart;

        initialize();

        function initialize() {
            vm.modelId = $stateParams.modelId;
            fetchLinkedParts();
        }

        function fetchLinkedParts(){
            linkedPartService.fetchLinkedPartsForModel(vm.modelId)
                 .then(fetchLinkedPartsForModelSuccess, fetchLinkedPartsForModelFailed);
        }

        function fetchLinkedPartsForModelSuccess(response) {
            vm.existingLinkedParts = response.data;
        }

        function fetchLinkedPartsForModelFailed(error) {
            viewerBannerService.setNotification("ERROR", error);
        }

        function createLinkedPart() {
            vm.isOpen = false;
            $rootScope.$broadcast("create-linked-part-opened");
        }

        function editLinkedPart(linkedPart) {
            vm.isOpen = false;
            $rootScope.$broadcast("create-linked-part-opened", linkedPart.id);
        }

        function deleteLinkedPart(linkedPart) {
            vm.successMessage = "";
            var deleteObject = {
                name: "LinkedPart for part " + linkedPart.partNumber,
                id: linkedPart.id,
                url: '/model/partLink/' + linkedPart.id
            };

            $uibModal.open({
                templateUrl: 'features/shared/commonDelete/deleteDialogBox.html',
                controller: 'DeleteController',
                controllerAs: 'deleteCtrl',
                size: 'sm',
                resolve: {
                    deleteObject: function () {
                        return deleteObject;
                    }
                }
            }).result.then(function () {
                fetchLinkedParts();
            });
        }

        $scope.$on("create-linked-part-closed", function () {
            vm.isOpen = true;
            initialize();
        });
    }

})();
