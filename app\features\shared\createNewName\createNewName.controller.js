(function () {
    'use strict';

    angular
        .module('app.shared')
        .controller('CreateNewNameController', CreateNewNameController);

    CreateNewNameController.$inject = ['$uibModalInstance', 'ordersService', '$state', 'customerUserId', 'userService'];

    function CreateNewNameController($uibModalInstance, ordersService, $state, customerUserId, userService) {

        var vm = this;

        vm.cancel = $uibModalInstance.dismiss;
        vm.createName = createName;
        vm.isDealerPlusPage = isDealerPlusPage;

        function createName() {
            var userId = null;
            if(customerUserId !== null){
                userId = customerUserId;
            }else{
                if($state.params.onBehalfOf && $state.params.onBehalfOf !== "null"){
                    var decrypted = atob($state.params.onBehalfOf);
                    var parsed = JSON.parse(decodeURIComponent(decrypted));
                    userId = parsed.userId
                }
            }
            ordersService.createName(vm.data, userId)
                .then(createNameSuccess, createNameFailure);
        }

        function createNameSuccess(resp) {
            $uibModalInstance.close(resp.data);
        }

        function createNameFailure(error) {
            headerBannerService.setNotification('ERROR', error.data.error, 10000);
        }

        function isDealerPlusPage(){
            return userService.isDealerPlusUser() && $state.current.name.includes("customerOrders");
        }

    }
})();
