(function () {
    'use strict';

    angular
        .module('app.orders')
        .controller('PartialShipOrderController', PartialShipOrderController);

    PartialShipOrderController.$inject = ['$uibModalInstance', 'partsObject',  'awsS3Service', '$scope'];

    function PartialShipOrderController($uibModalInstance, partsObject, awsS3Service, $scope) {
        var vm = this;

        vm.partsList = partsObject.orderItems;
        vm.orderId = partsObject.orderId;
        vm.displayId = partsObject.displayId;

        vm.cancel = cancel;
        vm.shipOrderItems = shipOrderItems;

        vm.invoiceAttached = invoiceAttached;
        vm.invoiceBucketURL = [];
        vm.invoiceFiles = [];
        vm.invoiceFileNames = [];
        vm.hasInvoice = false;
        vm.isConfirming = false;
        var respCount = 0;
        var invoiceCount = 0;

        initialize();

        function initialize() {
            for (var i = 0; i < vm.partsList.length; i++) {
                var shippedPartsCount = 0;
                for (var n = 0; n < vm.partsList[i].shippedOrderItems.length; n++) {
                    shippedPartsCount = shippedPartsCount + vm.partsList[i].shippedOrderItems[n].quantity;
                }
                vm.partsList[i].quantity = vm.partsList[i].quantity - shippedPartsCount;
                vm.partsList[i].oldQuantity = (vm.partsList[i].quantity > 0) ? vm.partsList[i].quantity : 0;
            }
            configureDatePicker();
        }

        function cancel() {
            $uibModalInstance.dismiss();
        }

        function shipOrderItems() {
            vm.errorShippingItems = false;
            vm.parts = [];

            for (var i = 0; i < vm.partsList.length; i++) {
                if (vm.partsList[i].isSelected) {
                    if (vm.partsList[i].quantity > 0) {
                        vm.parts.push(vm.partsList[i])
                    }
                }
            }

            if (validateSplitOrderData(vm.parts)) {
                vm.isConfirming = true;
                if (vm.hasInvoice) {
                    for (var i = 0; i < vm.invoiceFiles.length; i++) {
                        invoiceCount++;
                        awsS3Service.uploadOrderPDF(vm.invoiceFiles[i])
                            .then(uploadPDFSuccess, uploadPDFFailed);
                    }
                } else {
                    var response = {
                        parts: vm.parts,
                        shippingDate: vm.shippingDate,
                        invoiceURL: []
                    };
                    $uibModalInstance.close(response);
                }
            }
        }

        function validateSplitOrderData(parts) {
            vm.nothingSelectedError = false;
            vm.quantityMismatchError = false;
            vm.noDateSelectedError = vm.shippingDate === null || vm.shippingDate === undefined;

            if (parts.length > 0) {
                for (var i = 0; i < parts.length; i++) {
                    if (parts[i].quantity === undefined || parts[i].quantity > parts[i].oldQuantity) {
                        vm.quantityMismatchError = true;
                        break;
                    }
                }
            } else {
                vm.nothingSelectedError = true;
            }
            return !vm.nothingSelectedError && !vm.quantityMismatchError && !vm.noDateSelectedError;
        }

        function configureDatePicker() {
            var d = new Date();
            setTimeout(function () {
                $('#datepicker').datepicker({
                    dateFormat: 'dd/mm/yy',
                    onSelect: function (dateText) {
                        vm.shippingDate = dateText;
                        $scope.$apply();
                    }
                });
            }, 200);
        }

        function uploadPDFFailed(error){
            vm.errorShippingItems = true;
            vm.isConfirming = false;
            console.error("Upload pdf failed: " + error);
        }

        function uploadPDFSuccess(resp){
            vm.invoiceBucketURL.push(resp);
            respCount++;
            if(respCount === invoiceCount){
                var response = {
                    parts: vm.parts,
                    shippingDate: vm.shippingDate,
                    invoiceURL: vm.invoiceBucketURL
                };
                $uibModalInstance.close(response);
            }
        }

        function invoiceAttached(obj) {
            vm.hasInvoice = false;

            var elem = obj.target || obj.srcElement;
            if (elem.files.length > 0) {
                for(var j=0; j<elem.files.length; j++){
                    vm.invoiceFiles.push(elem.files[j]);
                    vm.invoiceFileNames.push(elem.files[j].name);
                }

                $scope.$digest();
                vm.hasInvoice = true;
            }
        }

    }
})();