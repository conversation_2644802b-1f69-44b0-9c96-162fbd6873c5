<section>
  <div>
    <div class="customDivStyling">
      <h2><span translate>ORDER.KITS</span></h2>
      <div class="d-flex cadGap flex-wrap">
        <p translate>CREATE_ORDER.SELECTED_FROM_INTERACTIVE</p>
        <p translate>CREATE_ORDER.EX_WORKS_DISCLAIMER</p>
      </div>

      <table class="table table-bordered equal-width">
        <thead>
          <tr>
            <th class="col-md-3 col-12" translate>ORDER.KIT</th>
            <th class="col-md-1 col-12" translate>ORDER.QUANTITY</th>
            <th ng-if="!createCtrl.hidePrice && createCtrl.areAnyPricesReturned()" translate>ORDER.ITEM_PRICE
            </th>
            <th ng-show="createCtrl.defaultDiscount && !createCtrl.hidePrice && createCtrl.areAnyPricesReturned()" translate>ORDER.DISCOUNTED_PRICE</th>
            <th ng-if="!createCtrl.hidePrice && createCtrl.areAnyPricesReturned()" translate>ORDER.PRICE</th>
            <th
              ng-if="createCtrl.isPreviewStockLevelEnabled && !createCtrl.isDealerPlusCustomer || createCtrl.isStockWarehousesEnabled && !createCtrl.isDealerPlusCustomer"
              translate>ORDER.STOCK</th>
            <th translate>GENERAL.ACTIONS</th>
            <!-- linked tech doc-->
          </tr>
        </thead>
        <tbody>
          <tr ng-repeat-start="kit in createCtrl.basket | filter: {kitId: ''}" class="hoverTableBG"
            ng-click="createCtrl.toggleKitsAccordion(kit.kitId, $event)" 
            ng-class="{'strike-through': kit.quantity <= 0, 'borderLeft': createCtrl.accordionStates[kit.kitId]}">
            <td data-label="{{'ORDER.KIT' | translate}}">
              <p>
                <i class="fa" ng-class="createCtrl.accordionStates[kit.kitId] ? 'fa-chevron-up' : 'fa-chevron-down'"
                  style="pointer-events: none;"></i>
                <strong><span>{{kit.masterPartNumber || kit.partNumber}}</span> </strong> - {{ kit.description || kit.description || kit.partDescription }}
              </p>
            </td>
            <td data-label="{{'ORDER.QUANTITY' | translate}}">
              <input class="w-100" ng-model="kit.quantity" ng-change="createCtrl.kitUpdated(kit)" type="number" min="0"
                ng-model-options="{debounce: 500}" />
            </td>
            <td class="disableWordBreak" data-label="{{'ORDER.ITEM_PRICE' | translate}}" ng-if="!createCtrl.hidePrice && createCtrl.areAnyPricesReturned()">
              <span ng-show="kit.kitPrice === 'TBC' || kit.kitPrice === null">
                -
              </span>
              <span ng-show="kit.kitPrice !== 'TBC' && kit.kitPrice !== null">
                {{kit.kitPrice || kit.price | currency:createCtrl.defaultCurrency.symbol:2}}
              </span>
            </td>
            <td ng-show="createCtrl.defaultDiscount && !createCtrl.hidePrice && createCtrl.areAnyPricesReturned()" class="disableWordBreak"
              data-label="{{'ORDER.DISCOUNTED_PRICE' | translate}}">
              <span ng-show="createCtrl.defaultDiscount && kit.kitPrice !== 'TBC' && kit.kitPrice !== null">
                {{createCtrl.calculateDiscountedPrice(kit) | currency:createCtrl.defaultCurrency.symbol:2}}
              </span>
              <span ng-show="kit.kitPrice === 'TBC' || kit.kitPrice === null">
                -
              </span>
            </td>
            

            <td data-label="{{'ORDER.PRICE' | translate}}" ng-if="!createCtrl.hidePrice && createCtrl.areAnyPricesReturned()">
              <span ng-show="kit.kitPrice === 'TBC' || kit.kitPrice === null">
                -
              </span>
              <span ng-show="kit.kitPrice !== 'TBC' && kit.kitPrice !== null">
                {{kit.totalPrice | currency:createCtrl.defaultCurrency.symbol:2}}
              </span>
            </td>
            <td class="disableWordBreak" data-label="{{'ORDER.STOCK' | translate}}"
              ng-if="createCtrl.isPreviewStockLevelEnabled && !createCtrl.isDealerPlusCustomer">
              <span title="{{'ORDER.IN_STOCK' | translate}}" tooltip-trigger="outsideClick"
                uib-tooltip="{{'ORDER.IN_STOCK' | translate}}" class="success-alert"
                ng-if="kit.kitStockLevel === 'IN_STOCK' || createCtrl.isStockHigh(kit.stock)">
                <i class="fas fa-layer-group text-success pointer"></i>
              </span>
              <span title="{{'ORDER.LOW_STOCK' | translate}}" tooltip-trigger="outsideClick"
                uib-tooltip="{{'ORDER.LOW_STOCK' | translate}}" class="warning-alert" ng-if="kit.kitStockLevel === 'LOW_STOCK' || createCtrl.isStockLow(kit.stock) "
              ><i class="fas fa-layer-group text-warning pointer"></i
            ></span>
            <span
              title="{{'ORDER.STOCK_SUBJECT' | translate}}"
              tooltip-trigger="outsideClick"
              uib-tooltip="{{'ORDER.STOCK_SUBJECT' | translate}}"
              class="danger-alert"
              ng-if="kit.kitStockLevel === 'OUT_OF_STOCK' || createCtrl.isStockEmptyOrNull(kit.stock)"
              ><i class="fas fa-layer-group text-danger pointer"></i
            ></span>
          </td>

            <td
              class="disableWordBreak"
              data-label="{{'ORDER.STOCK' | translate}}"
              ng-if="createCtrl.isStockWarehousesEnabled"
            >
              <div class="dropdownWarehouse">
                <button
                  class="dropdown-toggle d-flex align-items-center"
                  type="button"
                  id="dropdownMenuButton"
                  data-toggle="dropdown"
                  aria-haspopup="true"
                  aria-expanded="false"
                >
                  <span class="mr-2"
                    >{{ kit.selectedWarehouse || kit.warehouseStock[1].name
                    }}</span
                  >
                  <span
                    class="pill-badge badge-success mr-2"
                    ng-if="createCtrl.isStockHigh(kit.stock)"
                    >{{kit.stock > 1000 ? '>999' : kit.stock}}</span
                  >
                  <span
                    class="pill-badge badge-warning mr-2"
                    ng-if="createCtrl.isStockLow(kit.stock)"
                    >{{kit.stock}}</span
                  >
                  <span
                    class="pill-badge badge-danger mr-2"
                    ng-if="createCtrl.isStockEmptyOrNull(kit.stock)"
                    >{{kit.stock || kit.stock === 0 ? kit.stock : 0}}</span
                  >
                </button>
                <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                  <a
                    class="dropdown-item"
                    value="{{warehouse.warehouseId}}"
                    ng-repeat="warehouse in createCtrl.getUniqueKitWarehouses(kit)"
                    ng-click="createCtrl.updateBasketWarehouseKit(kit, warehouse)"
                  >
                    <span class="mr-2">{{warehouse.name}}</span>
                    <span
                      class="pill-badge badge-success mr-2"
                      ng-if="createCtrl.isStockHigh(warehouse.stock)"
                      >{{kit.warehouse.stock > 1000 ? '>999' :
                      warehouse.stock}}</span
                    >
                    <span
                      class="pill-badge badge-warning mr-2"
                      ng-if="createCtrl.isStockLow(warehouse.stock)"
                      >{{warehouse.stock}}</span
                    >
                    <span
                      class="pill-badge badge-danger mr-2"
                      ng-if="createCtrl.isStockEmptyOrNull(warehouse.stock)"
                      >{{warehouse.stock || warehouse.stock === 0 ?
                      warehouse.stock : 0}}</span
                    >
                  </a>
                </div>
              </div>
            </td>

            <td data-label="{{'GENERAL.ACTIONS' | translate}}">
              <span
                class="d-flex justify-content-center align-items-center cadGap"
              >

                    <button type="button" uib-tooltip="{{'ORDER.VIEW_PART_NOTE' | translate}}"
                      class="btn secondary partnotes disableButton">
                      <i class="fas fa-sticky-note fa-lg fa-fw"></i>
                    </button>
                    
                    <button type="button" ng-disabled="!item.hasLinkedTechDocs"
                      uib-tooltip="{{'ORDER.VIEW_TECH_INFO' | translate}}"
                      class="btn secondary warning disableButton">
                      <i class="fa fa-file-pdf fa-lg fa-fw"></i>
                    </button>

                <button
                  type="button"
                  uib-tooltip="{{'ORDER.COMMENTS_TOOLTIP' | translate}}"
                  ng-click="createCtrl.addKitComment(kit.comment, kit)"
                  class="btn secondary comments"
                >
                  <i
                    ng-hide="kit.comment"
                    class="far fa-comments fa-lg fa-fw"
                  ></i>
                  <i
                    ng-show="kit.comment"
                    class="fa fa-comments fa-lg fa-fw"
                  ></i>
                </button>

                <button
                  type="button"
                  uib-tooltip="{{'ORDER.REMOVE' | translate}}"
                  ng-show="!item.archived"
                  class="btn secondary danger"
                  ng-click="createCtrl.removeKitItem(kit)"
                >
                  <i class="fa fa-trash-o fa-lg fa-fw"></i>
                </button>
              </span>
            </td>
          </tr>

          <tr ng-repeat-end>
            <td class="p-0" colspan="100%">
              <div class="accordion-anim" ng-class="{'open': createCtrl.accordionStates[kit.kitId]}">
                <table class="table table-bordered">
                  <tbody>
                    <tr class="blueTableBG borderLeft"
                    ng-repeat="part in kit.kitDetails"
                      ng-class="{'strike-through': part.quantity <= 0}">
                      <td class="col-12 col-md-3" data-label="{{'ORDER.PART_DETAILS' | translate}}">
                        <p>
                          <strong><span>{{ part.partNumber }}</span></strong> - {{ ((part.descriptions | filter: {languageCode: currentLanguage})[0].translation || part.description ||
                          part.partDescription)}}
                        </p>
                      </td>
                      <td class="col-12 col-md-1">
                        {{ part.quantity || 0 }}
                      </td>
                      </td>
                      <td class="bg-white"></td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </td>
          </tr>

          <tr ng-if="!createCtrl.hasKits()">
            <td class="flex-start noPartsBG" colspan="10" translate>
              CREATE_ORDER.NO_KITS_PARTS
            </td>
          </tr>
            <tr
              ng-if="!createCtrl.hidePrice && createCtrl.areAnyPricesReturned() && createCtrl.hasKits()"
            >
              <td
                colspan="10"
                style="text-align: right"
                ng-show="createCtrl.areAllKitPricesReturned()"
              >
                <span>
                  <strong class="pr-2" translate>CREATE_ORDER.EST_TOTAL</strong
                  >{{createCtrl.estimatedTotalForKits |
                  currency:createCtrl.defaultCurrency.symbol:2}}
                </span>
              </td>
              <td
                colspan="10"
                style="text-align: right"
                class="flex-start"
                ng-hide="createCtrl.areAllKitPricesReturned()"
              >
                <strong class="pr-2" translate>CREATE_ORDER.EST_TOTAL</strong>
                TBC
              </td>
            </tr>
        </tbody>
      </table>
    </div>
  </div>
</section>
