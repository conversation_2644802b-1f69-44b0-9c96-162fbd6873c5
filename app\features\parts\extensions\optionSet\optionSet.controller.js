(function () {
    'use strict';

    angular
        .module('app.parts')
        .controller('OptionSetController', OptionSetController);

    OptionSetController.$inject = ['$stateParams', 'masterPartService', 'headerBannerService','$state', '$translate'];

    function OptionSetController($stateParams, masterPartService, headerBannerService, $state, $translate) {
        var vm = this;

        vm.id = $stateParams.id;
        vm.masterPartId = $stateParams.masterPartId;
        vm.description = "";
        vm.parts = [];

        vm.onAddClicked = onAddClicked;
        vm.save = save;
        vm.cancel = cancel;
        vm.removePart = removePart;

        var SAVE_SUCCESS, WENT_WRONG;
        $translate(['OPTION_SET.SAVE_SUCCESS', 'GENERAL.WENT_WRONG'])
            .then(function (resp) {
                SAVE_SUCCESS = resp["OPTION_SET.SAVE_SUCCESS"];
                WENT_WRONG = resp["GENERAL.WENT_WRONG"];
            });

        initialize();

        function initialize() {
            if (vm.id) {
                masterPartService.getOptionSet(vm.id)
                    .then(fetchOptionSuccess, serviceFailed);
            }
        }

        function fetchOptionSuccess(response) {
            vm.description = response.data.description;
            vm.parts = response.data.optionsSet;
        }

        function serviceFailed(error) {
            console.error(error);
            headerBannerService.setNotification('ERROR', WENT_WRONG, 10000);
        }

        function onAddClicked(masterPart) {
            if (_.findIndex(vm.parts, {masterPartId: masterPart.masterPartId}) < 0) {
                vm.parts.push(masterPart);
                vm.noPartsSelectedError = false;
            }
        }

        function removePart(index){
            vm.parts.splice(index, 1);
        }

        function save() {
            vm.noPartsSelectedError = false;

            if (vm.parts.length < 1) {
                vm.noPartsSelectedError = true;
                return;
            }

            if (vm.id) {
                masterPartService.editOptionSet(vm.description, vm.parts, vm.id)
                    .then(createEditSuccess, serviceFailed);
            } else {
                masterPartService.createOptionSet(vm.masterPartId, vm.description, vm.parts)
                    .then(createEditSuccess, serviceFailed);
            }
        }

        function  createEditSuccess(){
            headerBannerService.setNotification('SUCCESS', SAVE_SUCCESS, 5000);
            $state.go('masterPart', {masterPartId: vm.masterPartId});
        }

        function cancel(){
            $state.go('masterPart', {masterPartId: vm.masterPartId});
        }

    }
})();

