// =============================================================================
// Customer 2D Viewer
// =============================================================================

/* Default Nav */
.scrollable-col-default {
  height: calc(100vh - 56px);
  overflow-y: auto;
  overflow-x: hidden;
}

/* On Behalf Of Nav */
.scrollable-col-on-behalf {
  height: calc(100vh - 112px);
  overflow-y: auto;
  overflow-x: hidden;
}

.stickyButton {
  position: sticky;
  bottom: 0;
}

.basketButtonWidth {
  width: 200px;
  text-align: center;
}

.mobile-orientation-alert {
  display: none; /* By default, it's hidden */
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(49, 49, 49, 1);
  z-index: 9999; /* Ensures it's on top of everything else */
  color: white;
}

.customerPdfViewer{

  &.vertical {
      width: 100%;
  }

}

@media (max-width: 767.98px) and (orientation: portrait) {
  .mobile-orientation-alert {
    display: flex;
  }
}

.pdfHeader{

  width: 100%;
  height: 100%;
  min-height: 56px;
  background: #FFFFFF;
  position: relative;
  z-index: 10;
  -webkit-box-shadow: 0px 4px 6px 0px rgba(0, 0, 0, 0.06);
  -moz-box-shadow: 0px 4px 6px 0px rgba(0, 0, 0, 0.06);
  box-shadow: 0px 4px 6px 0px rgba(0, 0, 0, 0.06);
  padding: 0% 2.5%;

}

.thumb-cell-box-shadow{
  -webkit-box-shadow: 0px 4px 6px 0px rgba(0, 0, 0, 0.06);
  -moz-box-shadow: 0px 4px 6px 0px rgba(0, 0, 0, 0.06);
  box-shadow: 0px 4px 6px 0px rgba(0, 0, 0, 0.06);
}

.partColumnWidth{

  right:0;
  z-index: 2;
  background: white;
  min-width: 400px;
  box-shadow: 0 4px 6px 0 rgba(0, 0, 0, 0.06);

}

// =============================================================================
// Manufacturer 2D Viewer
// =============================================================================

