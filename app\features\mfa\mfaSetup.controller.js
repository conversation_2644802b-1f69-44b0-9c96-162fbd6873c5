(function () {
    'use strict';

    angular
        .module('app.mfa')
        .controller('MfaSetupController', MfaSetupController);

    MfaSetupController.$inject = ['mfaService'];

    function MfaSetupController(mfaService) {
        var vm = this;

        vm.verifyUserCode = verifyUserCode;

        vm.verifyCode = "";

        initialize();

        function initialize() {
            vm.qrCode = mfaService.getQrCode();
        }

        function verifyUserCode() {
            mfaService.verifyMfa(vm.verifyCode);
        }

    }
})();
