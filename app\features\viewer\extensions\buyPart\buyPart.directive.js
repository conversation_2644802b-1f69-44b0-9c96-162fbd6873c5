(function () {
    'use strict';

    angular
        .module('app.viewer')
        .directive('buyPart', buyPart);

    function buyPart() {
        var directive = {
            restrict: 'E',
            templateUrl: 'features/viewer/extensions/buyPart/buyPart.html',
            scope: {
                modelId: '@'
            },
            controller: 'BuyPartController',
            controllerAs: 'buyPartCtrl',
            bindToController: true
        };
        return directive;
    }

})();