(function () {
    'use strict';

    angular
        .module('app.orders')
        .controller('ShippingOptionsController', ShippingOptionsController);

    ShippingOptionsController.$inject = ['$uibModalInstance', 'shipInfo', 'shippingEngineService'];

    function ShippingOptionsController($uibModalInstance, shipInfo, shippingEngineService) {
        var vm = this;
        vm.carrier = null;
        vm.shipInfo = shipInfo;
        vm.rates = vm.shipInfo.rate_response.rates;

        vm.processToCheckOut = processToCheckOut;
        vm.onCarrierSelected = onCarrierSelected;
        vm.cancel = cancel;
        vm.currentWarehouse = shippingEngineService.getCurrentWarehouse();
        vm.isSplitWarehouse = shippingEngineService.getSplitWarehouse();

        function processToCheckOut() {
            vm.carrier.shipment_id = vm.shipInfo.shipment_id;
            $uibModalInstance.close(vm.carrier);
        }

        function cancel() {
            $uibModalInstance.close(null);
        }

        function onCarrierSelected(rate) {
            vm.rates.forEach(item => {
                if(item.rate_id !== rate.rate_id) item.carrierSelected = false;
            });

            vm.carrier = rate.carrierSelected ? rate : null;
        }
    }
})();
