<div class="sidebar-content" ng-show="createNonModeledPartCtrl.isOpen">
    <form class="form">

        <div>

            <div class="well clearfix">
                <div class="selected-part-text" ng-hide="createNonModeledPartCtrl.selectedPart.length > 1">
                    <h4 translate>CREATE_NON_MODELLED.SELECTED_PART</h4>
                    <span ng-hide="createNonModeledPartCtrl.selectedPart.length === 0">
                    {{createNonModeledPartCtrl.selectedPart.partNumber}} &nbsp;<small><strong>{{createNonModeledPartCtrl.selectedPart.partDescription}}</strong></small>
                </span>
                    <span ng-show="createNonModeledPartCtrl.selectedPart.length === 0" translate>CREATE_NON_MODELLED.NONE</span>
                </div>

                <div class="selected-part-error" ng-show="createNonModeledPartCtrl.selectedPart.length > 1">
                    <h4 translate>CREATE_NON_MODELLED.MULTI_PARTS</h4>
                    <p translate>CREATE_NON_MODELLED.ONLY_INDIVIDUAL</p>
                </div>
            </div>
            <div class="warning-well" ng-show="createNonModeledPartCtrl.hasNonModeled">
                <p translate>CREATE_NON_MODELLED.ALREADY_CONFIGURED</p>
                <p translate>CREATE_NON_MODELLED.IF_IN_KIT</p>

            </div><!-- /side-menu-content -->

            <div class="option-set-scrollable side-forms">
                <table class="tableViewer table-bordered w-100 bg-white ml-0">
                    <thead>
                    <tr>
                        <th translate>CREATE_NON_MODELLED.PART_NUM</th>
                        <th translate>CREATE_NON_MODELLED.PART_DESC</th>
                        <th></th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr ng-repeat="part in createNonModeledPartCtrl.nonModeledParts">
                        <td class="option-set-cell">
                            <input type="text" placeholder="{{'CREATE_NON_MODELLED.PART_NUM' | translate}}" ng-model="part.partNumber" required="required">
                        </td>
                        <td class="option-set-cell">
                            <input type="text" placeholder="{{'CREATE_NON_MODELLED.DESC'| translate}}" ng-model="part.partDescription"
                                   required="required">
                        </td>
                        <td><a href="" class="delete"
                               ng-click="createNonModeledPartCtrl.removePart($index)"><i class="fa fa-trash"></i></a>&nbsp;&nbsp;
                        </td>
                    </tr>

                    <tr>
                        <td colspan="3">
                            <div class="add-another-part">
                                <button class="btn primary-outline pull-right" type="button"
                                        ng-click="createNonModeledPartCtrl.addAnotherPart()">
                                    <i class="fa fa-plus-circle"></i> &nbsp;{{"CREATE_NON_MODELLED.ADD_ANOTHER" | translate}}
                                </button>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>


            <div class="side-menu-content">

                <div class="error-well"
                     ng-show="createNonModeledPartCtrl.errors.noPartSelected  || createNonModeledPartCtrl.errors.notEnoughParts || createNonModeledPartCtrl.errors.duplicatePartNumber">
                    <p ng-show="createNonModeledPartCtrl.errors.noPartSelected" translate>
                        CREATE_NON_MODELLED.SELECT_ERROR
                    </p>
                    <p ng-show="createNonModeledPartCtrl.errors.notEnoughParts" translate>
                        CREATE_NON_MODELLED.AT_LEAST_ONE_ERROR
                    </p>
                    <p ng-show="createNonModeledPartCtrl.errors.duplicatePartNumber" translate>
                        CREATE_NON_MODELLED.UNIQUE_ERROR
                    </p>
                </div>

                <div class="kit-actions pt-3">
                    <button class="btn small secondary" type="button" ng-click="createNonModeledPartCtrl.cancel()"
                            translate>
                        GENERAL.CANCEL
                    </button>
                    <button class="btn small primary" ng-click="createNonModeledPartCtrl.saveNonModeledPart()" translate>
                        CREATE_NON_MODELLED.SAVE
                    </button>
                </div>

            </div><!-- /side-menu-content -->
        </div>
    </form>


</div>