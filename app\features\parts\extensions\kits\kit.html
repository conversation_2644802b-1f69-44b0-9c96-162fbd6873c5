<section class="body-content">
    <div class="order-details-holder">
        <h1 translate>KIT.PAGE_TITLE</h1>
        <p class="page-desc" translate>KIT.SUBTITLE</p>
        <br>
        <p translate>KIT.USE_SEARCH</p>
    </div>
</section>
<section class="body-content page-body">
    <div class="row">
        <div class="col-lg-6">
            <div class="responsiveContainer">
                <form class="form" name="kitForm" ng-submit="kitCtrl.save()">

                    <div class="m-16">
                        <label><strong translate>KIT.TITLE</strong></label>
                        <input type="text" placeholder="{{'KIT.ENTER_TITLE' | translate}}" ng-model="kitCtrl.title"
                               required="required">
                    </div>

                    <div class="m-16">
                        <label><strong translate>KIT.ENTER_DESCRIPTION</strong></label>
                        <input type="text" placeholder="{{'KIT.ENTER_DESC' | translate}}" ng-model="kitCtrl.description"
                               required="required">
                    </div>

                    <table class="table table-bordered">
                        <thead>
                        <tr>
                            <th translate>KIT.PART_NUM</th>
                            <th translate>KIT.DESCRIPTION</th>
                            <th translate>KIT.QUANTITY</th>
                            <th></th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr ng-repeat="part in kitCtrl.parts track by part.partNumber">
                            <td data-label="{{'KIT.PART_NUM' | translate}}">{{part.partNumber}}
                            </td>
                            <td data-label="{{'KIT.DESCRIPTION' | translate}}">{{part.description ? part.description : part.partDescription}}
                            </td>
                            <td data-label="{{'KIT.QUANTITY' | translate}}">
                                <input ng-model="part.quantity" ng-change="kitCtrl.quantityChanged(part)" type="number"
                                       min="0" ng-model-options="{debounce: 500}" class="quantity-box">
                            </td>
                            <td>
                                <a href="" ng-click="kitCtrl.removePart($index)"
                                   class="delete fa fa-trash"></a>
                            </td>
                        </tr>

                        <tr ng-show="!kitCtrl.parts.length > 0">
                            <td class="emptytable" colspan="3" translate>KIT.NO_PARTS</td>
                        </tr>
                        </tbody>
                    </table>
                    <div class="error-well" ng-if="kitCtrl.noPartsSelectedError" translate> KIT.SELECT_ONE</div>

                    <div class="btn-actions">
                        <button class="btn small secondary" type="button" ng-click="kitCtrl.cancel()" translate>GENERAL.CANCEL</button>
                        <button class="btn small primary mr-16" type="submit" translate>KIT.SAVE
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <div class="col-lg-6 py-lg-0 py-5 mb-5">
            <div>
                <inline-parts-search on-add-clicked="kitCtrl.onAddClicked(masterPart)"></inline-parts-search>
            </div>
        </div>

    </div>
</section>



