(function () {
    'use strict';

    angular
        .module('app.parts')
        .controller('PartsSearchController', PartsSearchController);

    PartsSearchController.$inject = ['masterPartService', 'userService', '$state', '$uibModal', '$translate', '$window', '$rootScope', 'persistentSearch'];

    function PartsSearchController(masterPartService, userService, $state, $uibModal, $translate, $window, $rootScope, persistentSearch) {
        var vm = this;

        vm.sortReverse = false;
        vm.part_sort = 'partNumber';
        vm.searchBy = 'partNumber';
        vm.searching = false;
        vm.masterParts = [];
        vm.accordionStates = [];
        vm.isPreviewStockLevelEnabled = userService.getPreviewStockLevelEnabled();
        vm.isCustomer = userService.isManufacturerSubEntity();
        vm.isStockWarehousesEnabled = userService.getStockWarehousesEnabled();
        vm.searchValue = persistentSearch.getPartSearchValue();
        vm.searchBy = persistentSearch.getPartSearchType();

        vm.search = search;
        vm.goToMasterPart = goToMasterPart;
        vm.addNewPart = addNewPart;
        vm.whereUsedModal = whereUsedModal;
        vm.handleModalClose = handleModalClose;
        vm.toggleKitsAccordion = toggleKitsAccordion;

        if (vm.searchValue) {
            search();
        }

        $rootScope.$watch(function () {
            return vm.searchBy;
        }, function (newValue, oldValue) {
            if (newValue !== oldValue) {
                persistentSearch.setPartSearchType(newValue);
            }
        });

        $rootScope.$on('$locationChangeStart', function(event, next, current) {

            var routePart = next.split('#!/')[1];

            var isMasterPartRoute = routePart && routePart.startsWith('masterPart/');
            var isPartsSearchRoute = routePart && routePart.startsWith('parts/partsSearch');

            if (!isMasterPartRoute && !isPartsSearchRoute) {
                persistentSearch.setPartSearchValue('');
                vm.searchValue = '';
            } else {
                vm.searchValue = persistentSearch.getPartSearchValue();

                if (isPartsSearchRoute && vm.searchValue) {
                    search();
                }
            }
        });

        var WHERE_USED, WHERE_USED_BODY;
        $translate(['PART_SEARCH.WHERE_USED', 'PART_SEARCH.WHERE_USED_BODY'])
            .then(function (resp) {
                WHERE_USED = resp["PART_SEARCH.WHERE_USED"];
                WHERE_USED_BODY = resp["PART_SEARCH.WHERE_USED_BODY"];
            });

        function goToMasterPart(masterPartId) {
                $state.go('masterPart', {masterPartId: masterPartId})
        }

        function search() {
            persistentSearch.setPartSearchValue(vm.searchValue);
            var manufacturerId = userService.getManufacturerId();

            vm.masterParts = [];
            vm.resultsReturned = false;
            vm.searching = true;
            vm.accordionStates = [];

            var partNumber, partDescription;

            if (vm.searchBy === 'partNumber') {
                partNumber = vm.searchValue;
                partDescription = null;
            } else if (vm.searchBy === 'partDescription') {
                partDescription = vm.searchValue;
                partNumber = null;
            }

            masterPartService.manufacturerPartSearch(manufacturerId, partDescription, partNumber)
                .then(searchSuccess, searchFailed);
        }

        function searchSuccess(resp) {
            vm.searching = false;
            vm.resultsReturned = true;
            vm.searchError = false;

            vm.masterParts = resp.data.masterParts !== null ? resp.data.masterParts : [];
            vm.masterParts.forEach(function(part) {
                part.showWhereUsedButton = part.usedInModelCount > 0;
            });
        }

        function serviceFailed(error) {
            console.error('Service call failed: ', error);
        }

        function searchFailed() {
            vm.searchError = true;
            vm.searching = false;
        }

        function addNewPart(){
            $uibModal.open({
                templateUrl: 'features/parts/masterPart/create/createMasterPart.html',
                controller: 'CreateMasterPartController',
                controllerAs: 'createMPCtrl',
                size: 'md',
                backdrop: 'static'
            }).result.then(function () {
                console.log('Modal Cancelled');
            }, function () {
                console.log('Modal Cancelled');
            });
        }

        function addNewPart() {
            var modalInstance = $uibModal.open({
                templateUrl: 'features/parts/masterPart/create/createMasterPart.html',
                controller: 'CreateMasterPartController',
                controllerAs: 'createMPCtrl',
                size: 'md',
                backdrop: 'static'
            });

            modalInstance.result.then(function () {
                console.log('Modal Closed');
            }, function () {
                console.log('Modal Dismissed');
            });
        }

        function handleModalClose(reason) {
            if (reason === 'viewModel') {
                console.log("Closed due to view model");
                // Don't change the session storage value here
            } else {
                console.log("Handling modal close for other reasons");
                $window.sessionStorage.setItem('fromWhereUsedModal', 'false');
            }
        }

        function whereUsedModal(part) {
            console.log('whereUsedModal called with part:', part);

            masterPartService.getModelsForParts(part.masterPartId)
                .then(function(response) {
                    part.models = response.data;

                    var modalInstance = $uibModal.open({
                        templateUrl: 'features/parts/extensions/whereUsedModal/whereUsedModal.html',
                        controller: 'whereUsedModalController',
                        controllerAs: 'whereUsedModalCtrl',
                        resolve: {
                            partNumber: function() {
                                return part.partNumber;
                            },
                            models: function() {
                                return part.models;
                            },
                            confirmObject: function() {
                                return {
                                    titleText: WHERE_USED,
                                    bodyText: WHERE_USED_BODY
                                };
                            }
                        }
                    });

                    // Handle modal close or dismiss events
                    modalInstance.result.then(handleModalClose, handleModalClose);

                }, serviceFailed);
        }

        function toggleKitsAccordion(kitId, index, $event) {
            if ($event.target.tagName !== 'INPUT' && $event.target.tagName !== 'BUTTON' && $event.target.tagName !== 'I') {
                if (vm.accordionStates[index] === undefined) {
                    vm.accordionStates[index] = false;
                }

                var manufacturerId = userService.getManufacturerId();
                vm.accordionStates[index] = !vm.accordionStates[index];
                fetchAndDisplayKitDetails(manufacturerId, kitId, index)
            }
        }

    }
})();
