(function () {
        'use strict';

        angular
            .module('app.services')
            .factory('dpUserService', dpUserService);

        dpUserService.$inject = ['$http', 'apiConstants', 'tokenService', '$rootScope', '$location'];

        function dpUserService($http, apiConstants, tokenService, $rootScope, $location) {
            var dealerPlusPrefix = "/dealerplus";
            return {
                sendResetPassword: sendResetPassword,
                getCustomerUsers: getCustomerUsers,
                getManufacturerSubEntitySettings: getManufacturerSubEntitySettings,
                getUserCurrency: getUserCurrency
            };

            function getCustomerUsers(subEntityId) {
                return $http.get(apiConstants.url + dealerPlusPrefix + '/user/manufacturerSubEntity/' + subEntityId)
            }

            function getManufacturerSubEntitySettings(subEntityId) {
                return $http.get(apiConstants.url + dealerPlusPrefix + '/manufacturersubentity/' + subEntityId + '/settings')
            }

            function sendResetPassword(userId) {
                var siteUrl = $location.protocol() + '://' + $location.host();
                var config = {headers: {'Site-Url': siteUrl}};

                var theme = localStorage.getItem("theme");
                if (theme && theme != null) {
                    return $http.get(apiConstants.url + dealerPlusPrefix + '/user/' + userId + '/password/reset?theme=' + theme.toLowerCase(), config);
                } else {
                    return $http.get(apiConstants.url + dealerPlusPrefix + '/user/' + userId + '/password/reset', config);
                }
            }

            function getUserCurrency(userId) {
                return $http.get(apiConstants.url + dealerPlusPrefix + "/user/" + userId + "/currency")
            }
        }
    }

)();