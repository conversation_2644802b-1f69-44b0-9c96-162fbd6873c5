.heightVh100 {
  min-height: 40vh !important;
}

.hoverActiveLink {
  transition: ease-in-out 0.2s;
  color: #007bff;
}

.hoverActiveLink:hover {
  color: #00438a;
}

.container-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.orderSumary {
  position: relative;
}

.totalSection {
  background-color: #f9f9f9;
  border-top: lightgrey 1px solid;
  border-bottom-left-radius: 0.5em;
  border-bottom-right-radius: 0.5em;
  margin-left: -1em;
  margin-right: -1em;
  position: absolute;
  bottom: 0;
  width: 100%;

  &-content {
    padding: 1em;
    justify-content: space-between;
    align-items: center;
  }
}
