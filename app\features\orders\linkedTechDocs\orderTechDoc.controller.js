(function () {
    'use strict';

    angular
        .module('app.shared')
        .controller('OrderTechDocController', OrderTechDocController);

    OrderTechDocController.$inject = ['$uibModalInstance', 'techDocObject', '$window'];

    function OrderTechDocController($uibModalInstance, techDocObject, $window) {
        var vm = this;

        vm.viewLinkedTechDoc = viewLinkedTechDoc;
        vm.cancel = $uibModalInstance.dismiss;

        if (techDocObject) {
            vm.techDocs = techDocObject.techDocs;
            vm.partNumber = techDocObject.partNumber;
        }

        function viewLinkedTechDoc(item) {
            $window.open(item.url, "_blank");
        }

    }
})();
