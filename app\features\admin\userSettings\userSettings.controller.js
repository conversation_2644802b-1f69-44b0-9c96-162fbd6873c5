(function () {
    "use strict";

    angular
        .module("app.admin")
        .controller("userSettingsController", userSettingsController);

    userSettingsController.$inject = [
        "createUserService",
        "userService",
        "dpUserService",
        "dpCreateUserService",
        "$translate",
        "$state",
        "$stateParams",
        "$window",
        "$filter"
    ];

    function userSettingsController(
        createUserService,
        userService,
        dpUserService,
        dpCreateUserService,
        $translate,
        $state,
        $stateParams,
        $window,
        $filter
    ) {
        var vm = this;

        vm.getUserDetails = getUserDetails;
        vm.cancel = cancel;

        vm.dashboardEnabled = userService.getDashboardEnabled();
        vm.isDealerPlus = userService.isDealerPlusUser();
        vm.isManufacturer = userService.isManufacturer();
        vm.isSupreme = userService.isSupreme();
        vm.showMenuAccessTab = showMenuAccessTab;
        vm.showPermissionsTab = showPermissionsTab;
        vm.showAccountSubscriptionsTab = showAccountSubscriptionsTab;
        vm.checkPartSearchEnabled = checkPartSearchEnabled;
        vm.createNewUser = createNewUser;
        vm.editUser = editUser;
        vm.toggleSelectAll = toggleSelectAll;
        vm.toggleShowSelectedOnly = toggleShowSelectedOnly;
        vm.toggleSubscribeAll = toggleSubscribeAll;
        vm.handleCheckboxChange = handleCheckboxChange;
        vm.saveUser = saveUser;
        vm.disableIfCurrentUserId = disableIfCurrentUserId;

        vm.notificationSubscriptions = [];
        vm.userList = [];
        vm.activeTab = "menuAccess";
        vm.isSupreme = userService.isSupreme();
        vm.mode = $stateParams.mode || "edit";
        vm.isCreateMode = vm.mode === "create";
        vm.type = "Internal";
        vm.isDealer = vm.type === "DEALER";
        vm.searchValue = "";
        vm.userId = String(userService.getUserId());

        vm.isShowSelectedOnlyChecked = false;
        vm.isSelectAllChecked = false;
        vm.isSubscribeAllChecked = false;
        vm.isFormSubmitted = false;

        initialize();
    
        function initialize() {
            userService.getManufacturerSubEntities().then(function (response) {
                vm.userList = response.data;

                $stateParams.userId
                console.log($stateParams);
                if (vm.isCreateMode) {
                    getUserDetails();
                } else {
                    if ($stateParams.userId && $stateParams.userId !== "null") {
                        getUserDetails($stateParams.userId);
                    } else {
                        console.error("Invalid or missing user ID.");
                    }
                }
                disableIfCurrentUserId();
            });

            $(function () {
                $('[data-toggle="tooltip"]').tooltip();
            });
        }

        function getUserDetails(userId) {
            if (vm.isCreateMode) {
                return;
            }

            var manufacturerId = userService.getManufacturerId();

            userId = String(userId)
        console.log("userId:", userId, "vm.userId:", vm.userId);
        if (userId === vm.userId) {
                console.log("User ID matches the current user ID.");
                userService.getNotificationSubscriptions(manufacturerId, userId)
                    .then(function (notificationSubscriptions) {
                        vm.notificationSubscriptions = notificationSubscriptions;
                        markCheckedSubscriptions();
                    })
                    .catch(function (error) {
                        console.error("Error loading notification subscriptions:", error);
                    });
                return; // Ensure no further code is executed
            } else {
                userService
                    .getSingleManufacturerUser(manufacturerId, userId)
                    .then(function (response) {
                        var userDetails = response.data;
                        if (userDetails) {
                            vm.user = userDetails;

                            vm.emailAddress = userDetails.emailAddress;
                            vm.firstName = userDetails.firstName;
                            vm.lastName = userDetails.lastName;
                            vm.notificationSubscriptions = userDetails.notificationSubscriptions || [];
                            vm.visContactId = userDetails.visContactId;
                            vm.permissionsArray = userDetails.userPermissions;
                            vm.userSettings = userDetails.userSettings;
                            vm.userStatus = userDetails.userStatus;

                            vm.isDiscountEditable = userDetails.userSettings.disableDiscountEditing;

                            vm.permissions = {
                                orders: false,
                                products: false,
                                publications: false,
                                parts: false,
                                customers: false,
                                admin: false,
                                security: false,
                                dashboard: false,
                                publishedProducts: false,
                                partSearch: false,
                            };

                            userDetails.userPermissions.forEach((permission) => {
                                switch (permission) {
                                    case "Order":
                                        vm.permissions.orders = true;
                                        break;
                                    case "Products":
                                        vm.permissions.products = true;
                                        break;
                                    case "Publication":
                                        vm.permissions.publications = true;
                                        break;
                                    case "Parts":
                                        vm.permissions.parts = true;
                                        break;
                                    case "Customer":
                                        vm.permissions.customers = true;
                                        break;
                                    case "Admin":
                                        vm.permissions.admin = true;
                                        break;
                                    case "Security":
                                        vm.permissions.security = true;
                                        break;
                                    case "Dashboard":
                                        vm.permissions.dashboard = true;
                                        break;
                                    case "PublishedProducts":
                                        vm.permissions.publishedProducts = true;
                                        break;
                                    case "PartSearch":
                                        vm.permissions.partSearch = true;
                                        break;
                                }
                            });

                            markCheckedSubscriptions();
                        } else {
                            console.error("User not found");
                        }
                    })
                    .catch(function (error) {
                        console.error("Error loading user details:", error);
                    });
            }
        }

        function createNewUser() {
            vm.hasErrorMessage = false;
            vm.isDisabled = true;

            var permissionsArray = createPermissionArray();
            var userSettings = createUserSettings();
            console.log("User settings created:", userSettings);

            var userObject = {
                emailAddress: vm.emailAddress,
                firstName: vm.firstName,
                lastName: vm.lastName,
                permissionsArray: permissionsArray,
                userSettings: userSettings,
                visContactId: vm.visContactId,
                notificationSubscriptions: vm.userList.filter(user => user.checked).map(user => ({ purchaserId: user.manufacturerSubEntityId }))
            };

            if (userObject.permissionsArray.length > 0) {
                var manufacturerId = userService.getManufacturerId();
                createUserService.createSingleManufacturerUser(userObject, manufacturerId)
                    .then(createUserSuccess, createUserFailed);
            } else {
                vm.errorMessage = SELECT_CHECKBOX;
                vm.hasErrorMessage = true;
                vm.isDisabled = false;
            }
        }
        function createUserSuccess() {
            $state.go('admin');
        }

        function createUserFailed(response) {
            vm.isDisabled = false;
            vm.hasErrorMessage = true;
            vm.errorMessage = response.data.error || "An error occurred";
        }

        function cancel() {
            $state.go('admin');
        }

        function markCheckedSubscriptions() {
            vm.userList.forEach(function (user) {
                user.checked = vm.notificationSubscriptions.some(function (subscription) {
                    return subscription.purchaserId === user.manufacturerSubEntityId;
                });
            });

            vm.notificationSubscriptions.forEach(function (subscription) {
                subscription.checked = true;
            });
        }

        function editUser() {
            vm.hasErrorMessage = false;
            vm.isDisabled = true;

            var manufacturerId = userService.getManufacturerId();

            if (String(vm.userId) === String($stateParams.userId)) {
                // Use updateNotificationSubscriptions endpoint
                var notificationSubscriptions = vm.userList.filter(user => user.checked).map(user => user.manufacturerSubEntityId);
                userService.updateNotificationSubscriptions(manufacturerId, vm.userId, notificationSubscriptions)
                    .then(editUserSuccess, editUserFailed);
            } else {
                // Use editSingleManufacturerUser endpoint
                var permissionsArray = createPermissionArray();
                var userSettings = createUserSettings();
                console.log(userSettings);

                var userObject = {
                    emailAddress: vm.emailAddress,
                    firstName: vm.firstName,
                    lastName: vm.lastName,
                    userStatus: vm.userStatus,
                    permissionsArray: permissionsArray,
                    notificationSubscriptions: vm.userList.filter(user => user.checked).map(user => ({ purchaserId: user.manufacturerSubEntityId })),
                    userSettings: userSettings,
                    visContactId: vm.visContactId,
                    userId: vm.id,
                };

                if (userObject.permissionsArray.length > 0) {
                    createUserService.editSingleManufacturerUser(userObject, manufacturerId, vm.user.id)
                        .then(editUserSuccess, editUserFailed);
                } else {
                    vm.errorMessage = "Please select at least one permission.";
                    vm.hasErrorMessage = true;
                    vm.isDisabled = false;
                }
            }
        }

        function disableIfCurrentUserId() {
            vm.isCurrentUserId = String(vm.userId) === String($stateParams.userId);
        }

        function editUserSuccess() {
            vm.isDisabled = false;
            $state.go('admin');
        }

        function editUserFailed(error) {
            vm.hasErrorMessage = true;
            vm.errorMessage = error.data.message;
            vm.isDisabled = false;
        }


        function createPermissionArray() {
            var permissionsArray = [];
            if (vm.permissions.orders) {
                permissionsArray.push("Order");
            }
            if (vm.permissions.products) {
                permissionsArray.push("Products");
            }
            if (vm.permissions.publications) {
                permissionsArray.push("Publication");
            }
            if (vm.permissions.parts) {
                permissionsArray.push("Parts");
            }
            if (vm.permissions.customers) {
                permissionsArray.push("Customer");
            }
            if (vm.permissions.admin) {
                permissionsArray.push("Admin");
            }
            if (vm.permissions.security) {
                permissionsArray.push("Security");
            }
            if (vm.permissions.dashboard) {
                permissionsArray.push("Dashboard");
            }
            if (vm.permissions.publishedProducts) {
                permissionsArray.push("PublishedProducts");
            }
            if (vm.permissions.partSearch) {
                permissionsArray.push("PartSearch");
            }
            return permissionsArray;
        }

        function checkPartSearchEnabled() {
            if (vm.user.type !== "Internal") {
                if (vm.isManufacturer) {
                    userService
                        .getManufacturerSubEntitySettings(vm.user.manufacturerSubEntityId)
                        .then(getManufacturerSubEntitySettingsSuccess);
                } else if (vm.isDealerPlus) {
                    dpUserService
                        .getManufacturerSubEntitySettings(vm.user.manufacturerSubEntityId)
                        .then(getManufacturerSubEntitySettingsSuccess);
                }
            }
        }

        function getManufacturerSubEntitySettingsSuccess(response) {
            vm.hasPartSearchEnabled = response.data.partSearchEnabled;
        }

        function createUserSettings() {
            var userSettings = { disableDiscountEditing: vm.isDiscountEditable };
            return userSettings;
        }

        function showMenuAccessTab() {
            vm.activeTab = "menuAccess";
        }

        function showPermissionsTab() {
            vm.activeTab = "permissions";
        }

        function showAccountSubscriptionsTab() {
            vm.activeTab = "accountSubscriptions";
        }
        function toggleSelectAll() {
            vm.isSelectAllChecked = !vm.isSelectAllChecked;

            vm.userList.forEach(function (user) {
                user.checked = vm.isSelectAllChecked;
            });
        }

        function toggleShowSelectedOnly() {
            vm.isShowSelectedOnlyChecked = !vm.isShowSelectedOnlyChecked;
        }

        function handleCheckboxChange() {
            vm.isSelectAllChecked = vm.userList.every(function (user) {
                return user.checked;
            });
        }

        function toggleSubscribeAll() {
            vm.isSubscribeAllChecked = !vm.isSubscribeAllChecked;
            vm.notificationSubscriptions.forEach(function (subscription) {
                subscription.subscribed = vm.isSubscribeAllChecked;
                if (subscription.subscribed) {
                    subscription.checked = true;
                }
            });
        }

        function saveUser(form) {
            vm.isFormSubmitted = true;

            if (String(vm.userId) === String($stateParams.userId)) {
                editUser();
                return;
            }

            if (form.$valid) {
                if (vm.isCreateMode) {
                    createNewUser();
                } else {
                    editUser();
                }
            } else {
                console.log("Form is invalid");
            }
        }
        
    }
})();
