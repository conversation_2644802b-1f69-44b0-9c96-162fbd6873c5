<div class="modal-header info">
    <button type="button" class="close" data-dismiss="modal" ng-click="whereUsedModalCtrl.cancel()" aria-label="Close">
        <i class="fa fa-close" aria-hidden="true"></i>
    </button>

    <h2 class="modal-title">{{whereUsedModalCtrl.titleText}}</h2>
</div>


<div class="modal-body">

    <div ng-if="whereUsedModalCtrl.models.length > 0">
        <p class="modal-message">{{whereUsedModalCtrl.bodyText}}</p>

        <table class="table table-bordered">
            <thead>
            <tr>
                <th translate>PART_SEARCH.EQUIPMENT</th>
                <th translate>PART_SEARCH.MODEL</th>
                <th translate>PART_SEARCH.ACTION</th>
            </tr>
            </thead>
            <tbody>
                
            <tr ng-repeat="model in whereUsedModalCtrl.models">
                <td data-label="{{'PART_SEARCH.MACHINE_NAME' | translate}}">{{model.machineName}}</td>
                <td data-label="{{'PART_SEARCH.MODEL_NUM' | translate}}">{{model.modelName}}</td>
                <td data-label="{{'PART_SEARCH.VIEW_MODEL' | translate}}" ng-show="!whereUsedModalCtrl.isManufacturerSubEntity">
                    <button class="btn btn-primary" ng-click="whereUsedModalCtrl.goToModel(model)">
                        {{'PART_SEARCH.VIEW_MODEL' | translate}}
                    </button>
                </td>
                <td data-label="{{'PART_SEARCH.VIEW_MODEL' | translate}} " ng-show="whereUsedModalCtrl.isManufacturerSubEntity">
                    <button class="btn btn-primary" ng-click="whereUsedModalCtrl.goToViewer(model)">
                        {{'PART_SEARCH.VIEW_MODEL' | translate}}
                    </button>
                </td>
            </tr>
            </tbody>
        </table>
    </div>
    <div ng-if="whereUsedModalCtrl.models.length === 0">
        <p class="modal-message" translate>PART_SEARCH.WHERE_USED_NO_EQUIPMENT</p>
    </div>

    <div class="modal-actions">
        <a class="btn small secondary" href="" ng-click="whereUsedModalCtrl.confirm()" translate>GENERAL.CLOSE</a>
    </div>
</div>