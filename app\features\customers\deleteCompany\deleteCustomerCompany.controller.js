(function () {
    'use strict';

    angular
    .module('app.customer')
    .controller('DeleteCustomerCompanyController', DeleteCustomerCompanyController);

    DeleteCustomerCompanyController.$inject = ['createUserService', '$uibModalInstance', 'companyDetails'];

    function DeleteCustomerCompanyController(createUserService, $uibModalInstance, companyDetails) {
        var vm = this;

        vm.cancel = $uibModalInstance.dismiss;
        vm.companyName = companyDetails.name;
        vm.manufacturerSubEntityId = companyDetails.manufacturerSubEntityId;

        vm.deleteCompany = deleteCompany;

        function deleteCompany() {
            vm.isDisabled = true;

            createUserService.deleteManufacturerSubEntity(vm.manufacturerSubEntityId)
            .then(deleteCustomerSuccess, deleteCustomerFailed);
        }

        function deleteCustomerSuccess() {
            $uibModalInstance.close();
        }

        function deleteCustomerFailed(response) {
            vm.companyFailure = true;
            vm.internalFailureMessage = response.data.message;
        }

    }
})();