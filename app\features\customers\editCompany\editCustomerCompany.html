<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" ng-click="editCustomerCompanyCtrl.cancel()"
            aria-label="Close"><i
            class="fa fa-close" aria-hidden="true"></i></button>
    <h2 class="modal-title" translate>EDIT_COMPANY.TITLE</h2>
    <h3 ng-if="editCustomerCompanyCtrl.companyFailure" class="error-alert">
        {{editCustomerCompanyCtrl.internalFailureMessage}}</h3>
</div>


<div class="modal-body" ng-submit="editCustomerCompanyCtrl.editCompany()">
    <form class="form" name="createCompanyForm">
        <div class="input-group">
            <label translate>EDIT_COMPANY.COMPANY_NAME</label>
            <input type="text" placeholder="{{'EDIT_COMPANY.CREATE_NEW' | translate}}"
                   ng-model="editCustomerCompanyCtrl.newCompanyName" ng-required="true">
        </div>

        <div class="input-group mb-0">
            <label translate>EDIT_COMPANY.DEFAULT_DISCOUNT</label>
            <input type="number" step="1" placeholder="{{'EDIT_COMPANY.ENTER_DISCOUNT' | translate}}"
                   ng-model="editCustomerCompanyCtrl.newDefaultDiscount" ng-required="true">
        </div>

        <div class="input-group" ng-if="editCustomerCompanyCtrl.isPriceListEnabled">
            <label translate>EDIT_COMPANY.PRICE_LIST</label>
            <div class="select-box">
                <select ng-model="editCustomerCompanyCtrl.priceList"
                    ng-options="priceList as priceList.identifier for priceList in editCustomerCompanyCtrl.priceLists">
                    <option value="">{{'EDIT_COMPANY.SELECT_NONE' | translate}}</option>
                </select>
                <div class="select-arrow"></div>
            </div>
        </div>

        <div class="input-group" ng-if="editCustomerCompanyCtrl.isSupreme && editCustomerCompanyCtrl.isDealer">
            <label translate>EDIT_COMPANY.WAREHOUSE</label>
            <div class="select-box">
                <select ng-model="editCustomerCompanyCtrl.warehouseSelected"
                        ng-options="warehouse.name for warehouse in editCustomerCompanyCtrl.warehouses"
                        ng-required="editCustomerCompanyCtrl.isSupreme">
                </select>
                <div class="select-arrow"></div>
            </div>
        </div>

        <div class="input-group" ng-if="editCustomerCompanyCtrl.isSupreme && editCustomerCompanyCtrl.isDealer">
            <label>Visibility ID</label>
            <input type="text" placeholder="Visibility ID" ng-model="editCustomerCompanyCtrl.visCustomerCode" ng-required="true">
        </div>

        <div class="modal-actions">

            <button type="button" class="btn small secondary" data-dismiss="modal"
                    ng-click="editCustomerCompanyCtrl.cancel()" translate>
                GENERAL.CANCEL
            </button>

            <button type="submit" class="btn small primary"
                    ng-disabled="!createCompanyForm.$valid || editCustomerCompanyCtrl.isDisabled" translate>
			  EDIT_COMPANY.EDIT_BUTTON
            </button>

        </div>
    </form>
</div>


