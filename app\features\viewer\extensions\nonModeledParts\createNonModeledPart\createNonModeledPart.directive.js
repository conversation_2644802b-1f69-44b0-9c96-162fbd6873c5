(function () {
    'use strict';

    angular
        .module('app.viewer')
        .directive('createNonModeledPart', createNonModeledPart);

    function createNonModeledPart() {
        var directive = {
            restrict: 'E',
            templateUrl: 'features/viewer/extensions/nonModeledParts/createNonModeledPart/createNonModeledPart.html',
            controller: 'CreateNonModeledPartController',
            controllerAs: 'createNonModeledPartCtrl',
            bindToController: true
        };
        return directive;
    }

})();