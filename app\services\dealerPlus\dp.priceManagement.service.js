(function () {
    'use strict';

    angular
        .module('app.services')
        .factory('DPMasterPartService', DPMasterPartService);

    DPMasterPartService.$inject = ['$http', 'apiConstants', 'userService'];

    function DPMasterPartService($http, apiConstants, userService) {
        var dealerPlusPrefix = "/dealerplus";
        return {
            uploadInventory: uploadInventory,
            getInventoryHeaders: getInventoryHeaders,
            getInventory: getInventory,
            updatePrice: updatePrice,
            getLinkedTechDocsForPart: getLinkedTechDocsForPart,
            partSearch: partSearch,
            getMasterPartDetails: getMasterPartDetails,
            getPrice: getPrice,
            getKitsForMasterPart: getKitsForMasterPart,
            getKit: getKit,
            getKitsForManufacturer: getKitsForManufacturer,
            getOptionSetForMasterPart: getOptionSetForMasterPart,
            getOptionSet: getOptionSet,
            getAdditionalPart: getAdditionalPart,
            getLink: getLink,
            getSupersede: getSupersede,
            getTranslations: getTranslations,
            getModelsForParts: getModelsForParts,
            getPartHeaders: getPartHeaders,
            getParts: getParts,
            uploadParts: uploadParts,
            fetchProgress: fetchProgress
        };

        function uploadInventory(file) {
            var fd = new FormData();
            fd.append('file', file);
            var uploadUrl = apiConstants.url + dealerPlusPrefix + '/inventory';
            return $http.post(uploadUrl, fd, {
                transformRequest: angular.identity,
                headers: {'Content-Type': undefined}
            });
        }

        function getInventoryHeaders() {
            return $http.get(apiConstants.url + dealerPlusPrefix + '/inventory/headers');
        }

        function getInventory() {
            return $http.get(apiConstants.url + dealerPlusPrefix + '/inventory');
        }

        function partSearch(manufacturerId, purchaserId, searchParam, searchBy, onBehalfOfUserId) {
            if (manufacturerId && purchaserId) {
                return $http.post(apiConstants.url + '/purchasers/' + purchaserId + '/master-parts-for-dealer-plus-prices/search', {
                    partNumber: searchBy === 'partNumber' ? searchParam : null,
                    partDescription: searchBy === 'description' ? searchParam : null,
                    onBehalfOfUserId: onBehalfOfUserId
                });
            }

            if (manufacturerId && !purchaserId) {
                return $http.post(apiConstants.url + '/manufacturers/' + manufacturerId + '/master-parts/search', {
                    partNumber: searchBy === 'partNumber' ? searchParam : null,
                    partDescription: searchBy === 'description' ? searchParam : null
                });
            }
        }

        function getMasterPartDetails(masterPartId) {
            return $http.get(apiConstants.url + dealerPlusPrefix + '/masterPart/' + masterPartId);
        }

        function getPrice(masterPartId) {
            return $http.get(apiConstants.url + dealerPlusPrefix + '/masterPart/' + masterPartId + "/price");
        }

        function updatePrice(masterPartId, price) {
            return $http.put(apiConstants.url + dealerPlusPrefix + '/masterPart/' + masterPartId + "/price", {price: price});
        }

        function getLinkedTechDocsForPart(masterPartId) {
            return $http.get(apiConstants.url + dealerPlusPrefix + '/masterPart/' + masterPartId + "/techDoc");
        }

        function getLink(masterPartId) {
            return $http.get(apiConstants.url + dealerPlusPrefix + '/masterPart/' + masterPartId + "/linkedPart");
        }

        function getSupersede(masterPartId) {
            return $http.get(apiConstants.url + dealerPlusPrefix + '/masterPart/' + masterPartId + "/superseded");
        }

        function getTranslations(masterPartId) {
            return $http.get(apiConstants.url + dealerPlusPrefix + '/masterPart/' + masterPartId + "/translation");
        }

        function getKitsForMasterPart(masterPartId) {
            return $http.get(apiConstants.url + dealerPlusPrefix + '/masterPart/' + masterPartId + "/kit");
        }

        function getKit(kitId) {
            return $http.get(apiConstants.url + dealerPlusPrefix + '/masterPart/kit/' + kitId);
        }

        function getKitsForManufacturer(manufacturerId) {
            return $http.get(apiConstants.url + dealerPlusPrefix + '/manufacturer/' + manufacturerId + '/kits');
        }

        function getOptionSetForMasterPart(masterPartId) {
            return $http.get(apiConstants.url + dealerPlusPrefix + '/masterPart/' + masterPartId + "/optionSet");
        }

        function getOptionSet(optionId) {
            return $http.get(apiConstants.url + dealerPlusPrefix + '/masterPart/optionSet/' + optionId);
        }

        function getAdditionalPart(masterPartId) {
            return $http.get(apiConstants.url + dealerPlusPrefix + '/masterPart/' + masterPartId + "/nonModelled");
        }

        function getModelsForParts(masterPartId) {
            return $http.get(apiConstants.url + dealerPlusPrefix + '/masterPart/' + masterPartId + "/models");
        }

        function getPartHeaders() {
            return $http.get(apiConstants.url + '/dealerplus/inventory/headers');
        }
        function getParts() {
            return $http.get(apiConstants.url + '/dealerplus/inventory');
        }
        function uploadParts(file) {
            var fd = new FormData();
            fd.append('file', file);
            var uploadUrl = apiConstants.url + '/dealerplus/inventory/price';
            return $http.post(uploadUrl, fd, {
                transformRequest: angular.identity,
                headers: {'Content-Type': undefined}
            });
        }

        function fetchProgress(process) {
            var subEntityId = userService.getManufacturerSubEntityId();
            var paramString = "?";
            if (Array.isArray(process)) {
                for (var i = 0; i < process.length; i++) {
                    paramString = paramString + 'process=' + process[i] + '&';
                }
            } else {
                paramString = paramString + 'process=' + process;
            }
            return $http.get(apiConstants.url + '/dealerplus/progress/' + subEntityId + paramString, null);
        }

    }
})();
