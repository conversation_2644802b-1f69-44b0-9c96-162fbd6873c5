(function () {
    "use strict";

    angular.module("app.orders").controller("PartNoteModalController", PartNoteModalController);

    PartNoteModalController.$inject = ["$uibModalInstance", "partNoteData"];

    function PartNoteModalController($uibModalInstance, partNoteData) {
        var vm = this;

        vm.close = close;
        vm.cancel = $uibModalInstance.dismiss;

        if (partNoteData) {
            vm.isPartNoteAdded = partNoteData.isPartNoteAdded;
            vm.partNote = typeof partNoteData.partNote === 'string'
                ? partNoteData.partNote.split('\n')
                : partNoteData.partNote;
        }

        function close() {
            $uibModalInstance.close();
        }
    }
})();
