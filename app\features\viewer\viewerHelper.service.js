(function () {
    'use strict';

    angular
        .module('app.viewer')
        .factory('viewerHelperService', viewerHelperService);

    viewerHelperService.$inject = ['$q', '$rootScope', '$stateParams', 'viewerService', 'viewerVariablesService', 'userService'];

    function viewerHelperService($q, $rootScope, $stateParams, viewerService, viewerVariablesService, userService) {
        var alldbId = [];
        var leafNodes = [];
        var hasEditedParts = false;
        var doShowParts = false;
        var machineName = '';
        var currentSelectedPart = 0;
        var isTwoD = false;
        var theViewer;
        var partTreeTopId = '';
        var spinnerCount = 0;

        var partViewerDetails = null;
        var sharingViewer = null;

        return {
            getParentId: getParentId,
            getAlldbIds: getAlldbIds,
            selectParts: selectParts,
            getSelectedParts: getSelectedParts,
            /*highlightParts: highlightParts,
            removeHighlights: removeHighlights,*/
            getAllLeafDbIds: getAllLeafDbIds,
            clearOldLeafNodes: clearOldLeafNodes,
            getChildrenDbIds: getChildrenDbIds,

            getSiblingDBIds: getSiblingDBIds,
            selectParentPart: selectParentPart,
            showSpinner: showSpinner,
            hideSpinner: hideSpinner,
            getHasEditedParts: getHasEditedParts,
            setHasEditedParts: setHasEditedParts,
            getCurrentSelectedPart: getCurrentSelectedPart,
            setCurrentSelectedPart: setCurrentSelectedPart,
            getMachineName: getMachineName,
            setMachineName: setMachineName,
            getShowBuyParts: getShowBuyParts,
            setShowBuyParts: setShowBuyParts,
            getIsTwoD: getIsTwoD,
            setIsTwoD: setIsTwoD,
            showParts: showParts,
            showAllParts: showAllParts,
            hideParts: hideParts,
            isolateParts: isolateParts,
            getPartTreeTopId: getPartTreeTopId,
            setPartTreeTopId: setPartTreeTopId,
            setViewerApp: setViewerApp,
            calculateWeldments: calculateWeldments,
            buildCustomContextMenu: buildCustomContextMenu,
            applyCustomViewerSettings: applyCustomViewerSettings,
            getBackgroundColour: getBackgroundColour,
            setEdging: setEdging
        };

        function getHasEditedParts() {
            return hasEditedParts;
        }

        function setHasEditedParts(value) {
            hasEditedParts = value;
        }

        function getCurrentSelectedPart() {
            return currentSelectedPart;
        }

        function setCurrentSelectedPart(value) {
            currentSelectedPart = value;
        }

        function getShowBuyParts() {
            return doShowParts;
        }

        function setShowBuyParts(value) {
            doShowParts = value;
        }

        function getIsTwoD() {
            return isTwoD;
        }

        function setIsTwoD(value) {
            isTwoD = value;
        }

        function getMachineName() {
            return machineName;
        }

        function setMachineName(value) {
            machineName = value;
        }

        function showSpinner() {
            spinnerCount++;
            angular.element(document.getElementById('loader')).addClass('loader');
            angular.element(document.getElementById('loader2')).addClass('loader2');
            angular.element(document.getElementById('loader-text')).css('display', 'block');
        }

        function hideSpinner() {
            spinnerCount--;
            if (spinnerCount < 1) {
                angular.element(document.getElementById('loader')).removeClass('loader');
                angular.element(document.getElementById('loader2')).removeClass('loader2');
                angular.element(document.getElementById('loader-text')).css('display', 'none');
            }
            if(spinnerCount < 0)
                spinnerCount = 0;
        }

        function selectParentPart(dbId) {
            selectParts(getParentDbId(theViewer, dbId));
        }

        function selectParts(dbIds) {
            partViewerDetails = null;
            var current = theViewer.getSelection();
            if (!(current instanceof Array)) {
                current = [current];
            }
            if (dbIds.toString() !== current.toString()) {
                theViewer.select(dbIds);
                var dbIds = Array.isArray(dbIds) ? dbIds : [dbIds];
                if (dbIds.length === 1) {
                    var manualId = null;
                    if(userService.getCustomerSearchManualsOnly() && $stateParams.manualId){
                        manualId = $stateParams.manualId;
                    }
                    viewerService.getPartViewerDetails($stateParams.modelId, dbIds, manualId)
                        .then(getPartDetailsSuccess, getPartDetailsFailed);
                } else if (dbIds.length > 1) {
                    partViewerDetails = dbIds;
                    $rootScope.$broadcast("viewer-part-selected", partViewerDetails);
                }

            }
        }

        function getPartDetailsSuccess(resp) {

            var parts = Array.isArray(resp.data) ? resp.data : [resp.data];

            $rootScope.$broadcast("viewer-part-selected", parts);
            $rootScope.$broadcast("customer-viewer-part-selected", resp);
        }

        function getPartDetailsFailed() {

        }

        function getSelectedParts() {
            return theViewer.getSelection();
        }

        /*function highlightParts(dbIds) {
            var colorVector = new THREE.Vector4(0, 1, 1, 1);
            theViewer.setThemingColor(dbIds, colorVector);
        }

        function removeHighlights() {
            theViewer.clearThemingColors();
        }*/

        function getParentDbId(viewer, dbId) {
            return viewer.model.getData().instanceTree.nodeAccess.getParentId(dbId);
        }

        function getParentId(dbId) {
            return theViewer.model.getData().instanceTree.nodeAccess.getParentId(dbId);
        }

        function getPartTreeTopId() {
            return partTreeTopId;
        }

        function setPartTreeTopId(objectId) {
            partTreeTopId = objectId;
        }

        function getAlldbIds() {

            var instanceTree = theViewer.model.getData().instanceTree;
            var rootId = instanceTree.getRootId();
            var alldbId = [];
            var queue = [];
            queue.push(rootId);
            while (queue.length > 0) {
                var node = queue.shift();
                alldbId.push(node);
                instanceTree.enumNodeChildren(node, function (childrenIds) {
                    queue.push(childrenIds);
                });
            }
            // console.log("all dbids are: " + alldbId);
            return alldbId;
        }

        function getChildrenDbIds(dbId) {
            var instanceTree = theViewer.model.getData().instanceTree;

            var childArray = [];
            instanceTree.enumNodeChildren(dbId, function (childrenIds) {
                childArray.push(childrenIds);
            });

            return childArray;
        }

        function getSiblingDBIds(viewer, dbId) {
            var parentDbId = getParentDbId(viewer, dbId);
            var allParentsChildren = getChildrenDbIds(parentDbId);
            var index = allParentsChildren.indexOf(dbId);
            //allParentsChildren.splice(index, 1);
            return allParentsChildren;
        }

        function clearOldLeafNodes(){
            leafNodes = [];
        }

        function getAllLeafDbIds(viewer) {
            if (leafNodes.length === 0) {
                return getLeafNodes(viewer);
            } else {
                var promise = $q.defer();
                promise.resolve(leafNodes);
                return promise.promise;
            }
        }

        function getLeafNodes(viewer, dbIds) {

            return $q(function (resolve, reject) {
                try {
                    var instanceTree = viewer.getCurrentViewer().model.getData().instanceTree;
                    dbIds = dbIds || instanceTree.getRootId();
                    var dbIdArray = Array.isArray(dbIds) ? dbIds : [dbIds];
                    var leafIds = [];

                    var getLeafNodesRec = function getLeafNodesRec(id) {
                        var childCount = 0;

                        instanceTree.enumNodeChildren(id, function (childId) {
                            getLeafNodesRec(childId);
                            ++childCount;
                        });

                        if (childCount == 0) {
                            leafIds.push(id);
                        }
                    };

                    for (var i = 0; i < dbIdArray.length; ++i) {
                        getLeafNodesRec(dbIdArray[i]);
                    }
                    leafNodes = leafIds;
                    resolve(leafIds);

                } catch (ex) {

                    reject(ex);
                }
            });
        }

        function showParts(ids) {
            theViewer.show(ids);
            var idsArray = Array.isArray(ids) ? ids : [ids];
            $rootScope.$broadcast("show-parts", idsArray);
        }

        function showAllParts() {
            var allDbIds = getAlldbIds();
            showParts(allDbIds);
        }

        function hideParts(ids) {
            theViewer.hide(ids);
            var idsArray = Array.isArray(ids) ? ids : [ids];
            $rootScope.$broadcast("hide-parts", idsArray);
            sharingViewer = viewer.getExtension("SharingViewer");
            if (sharingViewer !== null) {
                GLOBAL_SHARING_EVENT("HIDE_EVENT");
            }
        }

        function isolateParts(currentSelection) {

            var allParts = getAlldbIds(theViewer);
            hideParts(allParts);

            var jsonSelection = Array.isArray(currentSelection) ? currentSelection : JSON.parse(currentSelection);
            var idsArray = Array.isArray(jsonSelection) ? jsonSelection : [jsonSelection];
            viewerVariablesService.setVisibleNodes(idsArray);
            showParts(idsArray);
        }

        function setViewerApp(viewer) {
            theViewer = viewer;
        }

        function calculateWeldments(viewer) {
            var customWeldments = [];
            showSpinner();
            //Part is top level of a weldment if all grand children are leaf nodes
            var allDbIds = getAlldbIds(viewer);

            for (var i = 0; i < allDbIds.length; i++) {
                var dbId = allDbIds[i];
                var allGrandChildrenAreLeafs = true;

                var children = getChildrenDbIds(dbId);
                allGrandChildrenAreLeafs = children.length > 0;

                grandChildIsNotLeaf:
                    for (var j = 0; j < children.length; j++) {

                        var grandChildren = getChildrenDbIds(children[j]);
                        allGrandChildrenAreLeafs = grandChildren.length > 0;
                        for (var k = 0; k < grandChildren.length; k++) {
                            if ((leafNodes.indexOf(grandChildren[k]) < 0)) {
                                allGrandChildrenAreLeafs = false;
                                break grandChildIsNotLeaf;
                            }
                        }
                    }

                if (allGrandChildrenAreLeafs) {
                    if (customWeldments.indexOf(dbId) < 0) {
                        customWeldments.push(dbId);
                    }
                }

            }
            hideSpinner();
            return customWeldments;
        }

        function buildCustomContextMenu(viewer) {
            viewer.registerContextMenuCallback('CustomContextMenuItems', function (menu, status) {
                if (status.hasSelected) {
                    if(menu === null){menu=[];}
                    while (menu.length > 0) {
                        menu.pop();
                    }

                    menu.push({
                        title: 'Isolate',
                        target: function () {
                            var selSet = viewer.getSelection();
                            isolateParts(selSet);
                        }
                    });

                    menu.push({
                        title: 'Hide',
                        target: function () {
                            var selSet = viewer.getSelection();
                            hideParts(selSet);
                        }
                    });

                } else {
                    while (menu.length > 0) {
                        menu.pop();
                    }
                }
            });
        }

        function setLineDrawing(viewer) {
            viewer.setBackgroundColor(255, 255, 255, 255, 255, 255);
            var ext=viewer.getExtension('Autodesk.NPR');
            ext.setParameter("style", "graphite");
            ext.setParameter("brightness", 1.0);
            viewer.impl.setSelectionColor(new THREE.Color(0.4, 0.6, 1));
        }

        function setLineDrawingAndEdging(viewer) {
            viewer.setBackgroundColor(255, 255, 255, 255, 255, 255);
            var ext=viewer.getExtension('Autodesk.NPR');
            ext.setParameter("style", "graphite");
            ext.setParameter("brightness", 1.0);
            ext.setParameter("style", "edging");
            viewer.impl.setSelectionColor(new THREE.Color(0.4, 0.6, 1));
        }

        function setEdging(viewer) {
            setNoPostProcessStyling(viewer);
            var ext=viewer.getExtension('Autodesk.NPR');
            ext.setParameter("style", "edging");
            
            ext.setParameter("normalEdges", true);
            ext.setParameter("depthEdges", false);
            ext.setParameter("idEdges", false);
        }

        function setNoPostProcessStyling(viewer) {
            var bgColour = getBackgroundColour();
            viewer.setBackgroundColor(bgColour.r, bgColour.g, bgColour.b, bgColour.r, bgColour.g, bgColour.b);
            var ext=viewer.getExtension('Autodesk.NPR');
            ext.setParameter("style", "");
            ext.setParameter("brightness", 0.0);
        }

        function setViewLocked(viewer) {
            viewer.navigation.setIsLocked(true);
            viewer.navigation.setLockSettings({"pan": true})
        }

        function unsetViewLocked(viewer) {
            viewer.navigation.setIsLocked(false);
            viewer.navigation.setLockSettings({"zoom": true})
        }

        function isMobile(){
            return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            }

        function applyCustomViewerSettings(viewer, viewerSettings) {
            viewerSettings.edgingEnabled = isMobile() ? false : viewerSettings.edgingEnabled;
            if (viewerSettings.lineDrawingEnabled && viewerSettings.edgingEnabled ) {
                setLineDrawingAndEdging(viewer);
            } else if(viewerSettings.lineDrawingEnabled ) {
                setLineDrawing(viewer);
            }else if(viewerSettings.edgingEnabled){
                setEdging(viewer);
            }else{
                setNoPostProcessStyling(viewer);
            }

            if (viewerSettings.viewLocked) {
                setViewLocked(viewer);
                if(viewer.toolbar && viewer.toolbar.getControl('navTools')) {
                    viewer.toolbar.getControl('navTools').setVisible(true);
                }
                viewer.toolController.activateTool('pan');
            } else {
                unsetViewLocked(viewer);
                if(viewer.toolbar && viewer.toolbar.getControl('navTools')){
                    viewer.toolbar.getControl('navTools').setVisible(true);
                }
            }
        }

        function getBackgroundColour() {
            var selectedColour = userService.getBackgroundColour();
            var trimmed = selectedColour.substring(4, selectedColour.length -1);

            var split = trimmed.split(",");

            var r = split[0] ? split[0] : 135;
            var g = split[1] ? split[1] : 206;
            var b = split[2] ? split[2] : 250;

            return {r: r, g: g, b: b}
        }

    }
})();

