<p class="page-desc" translate>TECH_DOCS.DESC</p>

<section class="responsiveContainer">


            <div id="{{techDocsCtrl.isFixedHeader ? 'infiniteScrollFixedHeader' : 'infiniteScrollStaticHeader'}}"
                class="flex p-4 p-md-0">
                <search-filter class="col-12 col-md-3" state-name="'techDocs'" value="techDocsCtrl.searchValue"
                    placeholder-key="TECH_DOCS.SEARCH_BY"></search-filter>
                
                <button class="btn primary ml-auto mr-4 col-12 col-md-auto mt-3 mt-md-0 create-machine"
                    ng-click="techDocsCtrl.createDoc()" translate>TECH_DOCS.CREATE_NEW
                </button>
            </div>

        <table class="table table-bordered tableFixedWidth">

            <thead>
            <tr>
                <th ng-class="techDocsCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'" ng-click="techDocsCtrl.viewable_sort='name'; techDocsCtrl.sortReverse = !techDocsCtrl.sortReverse" translate>TECH_DOCS.DOCUMENT_NAME</th>
                <th ng-class="techDocsCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'"
                    ng-click="techDocsCtrl.viewable_sort='description'; techDocsCtrl.sortReverse = !techDocsCtrl.sortReverse" translate>TECH_DOCS.DOCUMENT_SUMMARY</th>
                <th ng-class="techDocsCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'" translate>TECH_DOCS.ACTIONS</th>
            </tr>
            </thead>

            <tbody infinite-scroll="techDocsCtrl.loadMoreInfiniteScroll()" infinite-scroll-distance="3" infinite-scroll-disabled="techDocsCtrl.loadingInfiniteScrollData">
            <tr ng-repeat="doc in techDocsCtrl.docsList | orderBy:techDocsCtrl.viewable_sort:techDocsCtrl.sortReverse | filter : techDocsCtrl.searchValue"
                ng-show="techDocsCtrl.docsList.length > 0">
                <td data-label="{{'TECH_DOCS.DOCUMENT_NAME' | translate}}" class="">{{doc.name}}</td>
                <td data-label="{{'TECH_DOCS.DOCUMENT_SUMMARY' | translate}}" class="">{{doc.description}}</td>
                <td>
                    <split-button-dropdown
                            main-action="techDocsCtrl.viewDoc(doc)"
                            main-action-label="{{doc.url.endsWith('.pdf')? 'TECH_DOCS.VIEW' : 'TECH_DOCS.DOWNLOAD' | translate}}"
                            actions="techDocsCtrl.actions"
                            entity="doc">
                    </split-button-dropdown>
                </td>

            </tr>


            <tr ng-show="!techDocsCtrl.docsList.length > 0">
                <td colspan="4" translate>TECH_DOCS.NO_DOCS</td>
            </tr>

            <tr ng-show="techDocsCtrl.docsList === null" align="center">
                <td class="preloader" colspan="4">
                    <img ng-src="images/cadpreloader.gif" class="ajax-loader" height="60" width="60"/>
                </td>
            </tr>
            </tbody>
        </table>

    <span ng-click="techDocsCtrl.scrollToTop()" id="backToTopBtn" title="Go to top" class="fas fa-arrow-alt-circle-up"
        ng-show="techDocsCtrl.showBackToTopButton"></span>

</section>