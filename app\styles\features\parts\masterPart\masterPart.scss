.word-break{

  word-break: break-word;
}
.iconRightMp{
  position: absolute;
  right: 0;
}
.superseded-text-container {
  min-width: 120px;
.already-superseded-text {
  font-size: 9px;
}
}

.small-text{
  font-size: 14px;
  color:#007bff;
}

.flexible-container {
  display: flex;
  flex-direction: column;
  position: relative;
}

.button-container-bottom-right {
  margin-top: auto;
  align-self: flex-end;
}

/* Part List */

.part-list {
  list-style: none;
  padding: 0;
  margin: 0;

  li {
    display: flex;
    align-items: center;
    padding: 1rem;
    border: 1px solid #c3c3c3;
    margin-bottom: 0.5rem;
    border-radius: 0.375rem;
    cursor: pointer;
    min-height: 4rem;

    &:hover {
      background-color: #f8fafc;
    }

    &.selected {
      background-color: #e6f2ff;
      border-color: #3b82f6;
    }

    &.disabled {
      opacity: 0.6;
      cursor: not-allowed;

      &:hover {
        background-color: transparent;
      }
    }
  }

  input[type="radio"] {
    width: 1.5rem;
    height: 1.5rem;
    margin: 0.5rem;
    flex-shrink: 0;
    cursor: pointer;

    &:disabled {
      cursor: not-allowed;
    }
  }

  .part-search-list {
    flex: 1;
    margin-left: 0.75rem;
    min-width: 0;
    cursor: pointer;
    margin-bottom: 0;
  }

  .part-number {
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .supersessionPart-description {
    color: #4a5568;
    display: -webkit-box;
  }

  .stock-info {
    margin-top: 0.75rem;
    font-size: 1rem;
  }
}

.alert-danger-modal {
  padding: 12px 15px;
  border-radius: 4px;
  margin: 8px 0;
  border-left: 4px solid #dc3545;
  background-color: #f8d7da;
  color: #721c24;
  line-height: 1.4;
  width: 800px;
}

.modalLoadingOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1051;

}