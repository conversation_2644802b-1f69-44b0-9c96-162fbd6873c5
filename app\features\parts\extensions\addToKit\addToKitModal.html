<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" ng-click="addToKitCtrl.cancel()" aria-label="Close"><i
            class="fa fa-close" aria-hidden="true"></i></button>
    <h2 class="modal-title" translate>ADD_TO_KIT.TITLE</h2>
</div>

</div>

<div class="modal-body">
    <p translate>ADD_TO_KIT.SELECT_KIT</p>

    <table class="table table-bordered">
        <thead>
        <tr>
            <th translate>ADD_TO_KIT.KIT</th>
            <th translate>ADD_TO_KIT.DESCRIPTION</th>
            <th></th>
        </tr>
        </thead>
        <tbody>
        <tr ng-repeat="kit in addToKitCtrl.kits track by kit.id">
            <td data-label="{{'ADD_TO_KIT.KIT' | translate}}">{{kit.title}}</td>
            <td data-label="{{'ADD_TO_KIT.DESCRIPTION' | translate}}">{{kit.description}}</td>
            <td ng-if="!kit.containsPart">
                <button  class="btn btn-primary" href="" ng-click="addToKitCtrl.addToKit(kit)">
                    <div ng-hide="kit.clicked" translate>ADD_TO_KIT.ADD_TO_KIT</div>
                    <div ng-show="kit.clicked"><i class="fa fa-check"></i></div>
                </button>
            </td>
            <td ng-if="kit.containsPart">
                <button  class="btn btn-secondary" href="" disabled translate>ADD_TO_KIT.ALREADY_IN_KIT</button>
            </td>
        </tr>

        <tr ng-show="!addToKitCtrl.kits.length > 0">
            <td class="emptytable" colspan="3" translate>ADD_TO_KIT.NO_KITS</td>
        </tr>
        </tbody>
    </table>

    <p class="modal-message" style="color: red" ng-if="addToKitCtrl.error">
        {{addToKitCtrl.error}}
    </p>

    <div class="modal-actions">
        <a class="btn small secondary" href="" ng-click="addToKitCtrl.cancel()" translate>ADD_TO_KIT.EXIT</a>
    </div>

</div>


