(function () {
        'use strict';

        angular
            .module('app.products')
            .controller('CustomerManualController', CustomerManualController);

    CustomerManualController.$inject = ['$stateParams', 'publicationService', 'headerBannerService', 'customerModelService', '$timeout', '$translate', '$state'];

    function CustomerManualController($stateParams, publicationService, headerBannerService, customerModelService, $timeout, $translate, $state) {
            var vm = this;

            var manualId = $stateParams.manualId;
            var onBehalfOf = $stateParams.onBehalfOf;
            var viewablesTranslation;
            var kitsTranslation;
            var techDocsTranslation;
            var videoTranslation;
            var WENT_WRONG;
            var translationsComplete = false;
            var getManualComplete = false;
            var getModelComplete = false;

            vm.goToViewer = goToViewer;

            $translate(['CUST_MANUAL.VIEWABLES', 'CUST_MANUAL.KITS', 'CUST_MANUAL.TECH_DOCS', 'CUST_MANUAL.VIDEOS', 'GENERAL.WENT_WRONG'])
                .then(function (resp) {
                    viewablesTranslation = resp["CUST_MANUAL.VIEWABLES"];
                    kitsTranslation = resp["CUST_MANUAL.KITS"];
                    techDocsTranslation = resp["CUST_MANUAL.TECH_DOCS"];
                    videoTranslation = resp["CUST_MANUAL.VIDEOS"];
                    translationsComplete = true
                    WENT_WRONG = resp["GENERAL.WENT_WRONG"];
                });


            initialize();

            function initialize() {
                createTabs();
                publicationService.getManual(manualId)
                    .then(getManualSuccess, serviceFailed);
                customerModelService.fetchManualModels(manualId)
                    .then(fetchModelsSuccess);
            }

            function fetchModelsSuccess(response) {
                vm.modelList = response.data;
                getModelComplete = true;
                setupFeaturedModel();
            }

            function createTabs() {
                if (translationsComplete) {
                    vm.tabs = [
                        {
                            title: viewablesTranslation,
                            route: 'customerManual.viewables({manualId:"' + manualId + '", onBehalfOf:"' + onBehalfOf + '"})'
                        },
                        {
                            title: kitsTranslation,
                            route: 'customerManual.kits({manualId:"' + manualId + '", onBehalfOf:"' + onBehalfOf + '"})'
                        },
                        {
                            title: techDocsTranslation,
                            route: 'customerManual.techDocs({manualId:"' + manualId + '", onBehalfOf:"' + onBehalfOf + '"})'
                        },
                        {
                            title: videoTranslation,
                            route: 'customerManual.videos({manualId:"' + manualId + '", onBehalfOf:"' + onBehalfOf + '"})'
                        }];
                } else {
                    $timeout(createTabs, 500)
                }
            }

            function getManualSuccess(response) {
                vm.manual = response.data;
                vm.manualName = response.data.manualName;
                vm.imageURL = (response.data.featuredModelUrl && response.data.featuredModelUrl !== "images/placeholder.jpg") ? response.data.featuredModelUrl : (customerModelService.getMachineImageUrl() ? customerModelService.getMachineImageUrl() : './images/PDF-placeholder.png');
                getManualComplete = true;
                setupFeaturedModel();
            }

            function serviceFailed(error) {
                headerBannerService.setNotification('ERROR', WENT_WRONG, 10000);
                console.error(error.data);
            }

            function goToViewer(model) {
                var stateName = model.is2d ? "customerPdfViewer" : "customerViewer";
                $state.go(stateName, {
                    machineName: model.machineName,
                    autodeskURN: model.autodeskURN,
                    modelId: model.modelId,
                    viewableName: model.modelName,
                    onBehalfOf: $stateParams.onBehalfOf,
                    manualId: manualId,
                    translateType: model.translateType,
                    productId: $stateParams.productId,
                    roomGuid: null
                });
            }

            function setupFeaturedModel() {
                if (getModelComplete && getManualComplete) {
                    var index = _.findIndex(vm.modelList, {modelId: vm.manual.featuredModelId});
                    vm.featuredViewable = vm.modelList[index];
                }
            }

        }
    }

)();
