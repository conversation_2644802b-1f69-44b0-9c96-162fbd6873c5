(function () {
        'use strict';

        angular
            .module('app.products')
            .controller('CustomerManualController', CustomerManualController);

    CustomerManualController.$inject = ['$stateParams', 'publicationService', 'headerBannerService', 'customerModelService', '$timeout', '$translate', '$state'];

    function CustomerManualController($stateParams, publicationService, headerBannerService, customerModelService, $timeout, $translate, $state) {
            var vm = this;

            var manualId = $stateParams.manualId;
            var onBehalfOf = $stateParams.onBehalfOf;
            var viewablesTranslation;
            var kitsTranslation;
            var techDocsTranslation;
            var videoTranslation;
            var WENT_WRONG;
            var translationsComplete = false;
            var getManualComplete = false;
            var getModelComplete = false;

            vm.goToViewer = goToViewer;

            $translate(['CUST_MANUAL.VIEWABLES', 'CUST_MANUAL.KITS', 'CUST_MANUAL.TECH_DOCS', 'CUST_MANUAL.VIDEOS', 'GENERAL.WENT_WRONG'])
                .then(function (resp) {
                    viewablesTranslation = resp["CUST_MANUAL.VIEWABLES"];
                    kitsTranslation = resp["CUST_MANUAL.KITS"];
                    techDocsTranslation = resp["CUST_MANUAL.TECH_DOCS"];
                    videoTranslation = resp["CUST_MANUAL.VIDEOS"];
                    translationsComplete = true
                    WENT_WRONG = resp["GENERAL.WENT_WRONG"];
                });


            initialize();

            function initialize() {
                createTabs();
                publicationService.getPublication(manualId)
                    .then(getPublicationSuccess, serviceFailed);
            }

            function createTabs() {
                if (translationsComplete) {
                    vm.tabs = [
                        {
                            title: viewablesTranslation,
                            route: 'customerManual.viewables({manualId:"' + manualId + '", onBehalfOf:"' + onBehalfOf + '"})'
                        },
                        {
                            title: kitsTranslation,
                            route: 'customerManual.kits({manualId:"' + manualId + '", onBehalfOf:"' + onBehalfOf + '"})'
                        },
                        {
                            title: techDocsTranslation,
                            route: 'customerManual.techDocs({manualId:"' + manualId + '", onBehalfOf:"' + onBehalfOf + '"})'
                        },
                        {
                            title: videoTranslation,
                            route: 'customerManual.videos({manualId:"' + manualId + '", onBehalfOf:"' + onBehalfOf + '"})'
                        }];
                } else {
                    $timeout(createTabs, 500)
                }
            }

            function getPublicationSuccess(response) {
                vm.manual = response.data;
                vm.manualName = response.data.name;
                if (response.data.featuredViewableImage && response.data.featuredViewableImage.url) {
                    vm.imageURL = response.data.featuredViewableImage.url;
                } else if (response.data.coverImage && response.data.coverImage.url) {
                    vm.imageURL = response.data.coverImage.url;
                } else {
                    vm.imageURL = './images/placeholder.jpg';
                }
                
                // Find featured viewable
                if (response.data.viewables && response.data.viewables.length > 0) {
                    vm.featuredViewable = response.data.viewables.find(v => v.featuredViewable);
                    
                    // Fetch model details for the featured viewable
                    if (vm.featuredViewable) {
                        customerModelService.fetchManualModels(manualId)
                            .then(fetchModelsSuccess);
                    }
                }
                
                getManualComplete = true;
            }

            function fetchModelsSuccess(response) {
                vm.modelList = response.data;
                getModelComplete = true;
                setupFeaturedModel();
            }

            function setupFeaturedModel() {
                if (getModelComplete && getManualComplete && vm.featuredViewable) {
                    var index = _.findIndex(vm.modelList, {modelId: vm.featuredViewable.id});
                    if (index !== -1) {
                        vm.featuredViewable = Object.assign({}, vm.featuredViewable, vm.modelList[index]);
                    }
                }
            }

            function serviceFailed(error) {
                headerBannerService.setNotification('ERROR', WENT_WRONG, 10000);
                console.error(error.data);
            }

            function goToViewer(viewable) {
                var stateName = "customerViewer";
                $state.go(stateName, {
                    viewableName: viewable.name,
                    onBehalfOf: $stateParams.onBehalfOf,
                    manualId: viewable.manualId,
                    productId: $stateParams.productId,
                    roomGuid: null,
                    machineName: viewable.machineName,
                    modelId: viewable.modelId,
                    autodeskURN: viewable.autodeskURN,
                    translateType: viewable.translateType
                });
            }

        }
    }

)();
