(function () {
    'use strict';

    angular
        .module('app.shared')
        .controller('StockController', StockController);

    StockController.$inject = ['$uibModalInstance', 'stockObject', 'masterPartService'];

    function StockController($uibModalInstance, stockObject, masterPartService) {
        var vm = this;

        vm.save = save;
        vm.cancel = $uibModalInstance.dismiss;

        if (stockObject) {
            vm.stock = stockObject.stock;
            vm.partNumber = stockObject.partNumber;
            vm.masterPartId = stockObject.masterPartId;
        }

        function save() {
            vm.hasError = false;
            masterPartService.updateStock(vm.masterPartId, vm.stock)
                .then(saveSuccess, saveFailure);
        }

        function saveSuccess() {
            $uibModalInstance.close(vm.stock);
        }

        function saveFailure(error) {
            vm.hasError = true;
            console.log("Error saving stock: " + error)
        }
    }
})();
