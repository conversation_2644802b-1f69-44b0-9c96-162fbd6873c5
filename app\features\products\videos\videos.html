<p class="page-desc" translate>VIDEOS.DESC</p>

<section class="responsiveContainer">

        <div id="{{videosCtrl.isFixedHeader ? 'infiniteScrollFixedHeader' : 'infiniteScrollStaticHeader'}}"
            class="flex p-4 p-md-0">
                <search-filter class="col-12 col-md-3" state-name="'videos'" value="videosCtrl.searchValue"
                    placeholder-key="VIDEOS.SEARCH_BY"></search-filter>
                
                <button class="btn primary ml-auto mr-4 col-12 col-md-auto mt-3 mt-md-0 create-machine"
                    ng-click="videosCtrl.createVideo()" translate>VIDEOS.CREATE_NEW
                </button>
        </div>

        <table class="table table-bordered tableFixedWidth">

            <thead class="videos_heading">
            <tr>
                <th ng-class="videosCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'" ng-click="videosCtrl.viewable_sort='name'; videosCtrl.sortReverse = !videosCtrl.sortReverse" translate>VIDEOS.DOCUMENT_NAME</th>
                <th ng-class="videosCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'"
                    ng-click="videosCtrl.viewable_sort='description'; videosCtrl.sortReverse = !videosCtrl.sortReverse" translate>VIDEOS.DOCUMENT_SUMMARY</th>
                <th ng-class="videosCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'" translate>VIDEOS.ACTIONS</th>
            </tr>
            </thead>

            <tbody class="videos_body" infinite-scroll="videosCtrl.loadMoreInfiniteScroll()" infinite-scroll-distance="3" infinite-scroll-disabled="videosCtrl.loadingInfiniteScrollData">
            <tr ng-repeat="video in videosCtrl.videosList | orderBy:videosCtrl.viewable_sort:videosCtrl.sortReverse | filter : videosCtrl.searchValue" ng-class="{'last-item': $last}"
                ng-show="videosCtrl.videosList.length > 0">
                <td data-label="Video Name" class="">{{video.name}}</td>
                <td data-label="Video Summary" class="">{{video.description}}</td>
                <td>
                    <split-button-dropdown
                            main-action="videosCtrl.viewVideo(video)"
                            main-action-label="{{'VIDEOS.VIEW' | translate}}"
                            actions="videosCtrl.actions"
                            entity="video">
                    </split-button-dropdown>
                </td>

            </tr>


            <tr ng-show="!videosCtrl.videosList.length > 0">
                <td colspan="4" translate>VIDEOS.NO_DOCS</td>
            </tr>

            <tr ng-show="videosCtrl.videosList === null" align="center">
                <td class="preloader" colspan="4">
                    <img ng-src="images/cadpreloader.gif" class="ajax-loader" height="60" width="60"/>
                </td>
            </tr>
            </tbody>
        </table>

        <span ng-click="videosCtrl.scrollToTop()" id="backToTopBtn" title="Go to top" class="fas fa-arrow-alt-circle-up"
            ng-show="videosCtrl.showBackToTopButton"></span>

</section>