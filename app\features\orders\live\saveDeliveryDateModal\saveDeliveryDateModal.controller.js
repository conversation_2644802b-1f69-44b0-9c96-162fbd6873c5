(function () {
    'use strict';

    angular
        .module('app.shared')
        .controller('SaveDeliveryDateModalController', SaveDeliveryDateModalController);

    SaveDeliveryDateModalController.$inject = ['$uibModalInstance', 'confirmObject'];

    function SaveDeliveryDateModalController($uibModalInstance, confirmObject) {

        var vm = this;

        vm.confirm = confirm;
        vm.confirmNoNotification = confirmNoNotification;
        vm.cancel = $uibModalInstance.dismiss;

        if(confirmObject){
            vm.titleText = confirmObject.titleText;
            vm.bodyText = confirmObject.bodyText;
        }

        function confirmNoNotification() {
            confirmObject.notifyDeliveryDateUpdated = false;
            $uibModalInstance.close();
        }

        function confirm() {
            $uibModalInstance.close();
        }


    }
})();
