(function () {
    "use strict";

    angular.module("app.parts").controller("editTranslationController", editTranslationController);

    editTranslationController.$inject = ["$uibModalInstance", "translationObject", "masterPartService"];

    function editTranslationController($uibModalInstance, translationObject, masterPartService) {
        var vm = this;

        vm.languageId = translationObject.language.languageId;
        vm.descTranslation = translationObject.description;
        vm.langKey = translationObject.partNumber;
        vm.isEdit = translationObject.isEdit;
        vm.update = update;
        vm.cancel = $uibModalInstance.dismiss;

        if (vm.descTranslation === "") {
            vm.isEdit = true;
        }

        function update() {
            console.log('Update button clicked, current values:', {
                languageId: vm.languageId,
                translation: vm.descTranslation,
                isEdit: vm.isEdit
            });

            var updatedData = {
                languageId: vm.languageId,
                langKey: vm.lang<PERSON><PERSON>,
                translation: vm.descTranslation,
            };

            // Just close the modal with the updated data - don't save to server yet
            // Translation will be saved when the main "Update" button is clicked
            $uibModalInstance.close(updatedData);
        }
    }
})();