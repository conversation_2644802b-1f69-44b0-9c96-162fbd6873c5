(function () {
    "use strict";

    angular.module("app.parts").controller("editTranslationController", editTranslationController);

    editTranslationController.$inject = ["$uibModalInstance", "translationObject", "masterPartService"];

    function editTranslationController($uibModalInstance, translationObject, masterPartService) {
        var vm = this;

        vm.languageId = translationObject.language.languageId;
        vm.descTranslation = translationObject.description;
        vm.langKey = translationObject.partNumber;
        vm.isEdit = translationObject.isEdit;
        vm.update = update;
        vm.cancel = $uibModalInstance.dismiss;

        if (vm.descTranslation === "") {
            vm.isEdit = true;
        }

        function update() {
            var updatedData = {
                languageId: vm.languageId,
                langKey: vm.langKey,
                translation: vm.descTranslation,
            };

            var translationService;
            if (vm.isEdit) {
                translationService = masterPartService.updateTranslation;
            } else {
                translationService = masterPartService.createTranslation;
            }

            translationService(translationObject.masterPartId, updatedData.languageId, updatedData.translation)
                .then(function () {
                    $uibModalInstance.close(updatedData);
                })
                .catch(function (error) {
                    console.error("Error handling translation:", error);
                });
        }
    }
})();