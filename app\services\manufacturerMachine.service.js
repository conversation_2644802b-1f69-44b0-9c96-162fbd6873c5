(function () {
    'use strict';

    angular
        .module('app.services')
        .factory('manufacturerProductService', manufacturerProductService);

    manufacturerProductService.$inject = ['$http', 'apiConstants', 'userService'];

    function manufacturerProductService($http, apiConstants, userService) {
        return {
            fetchMachines: fetchMachines,
            createNewRange: createNewRange,
            createModel: createModel,
            createMachine: createMachine,
            getRangeByManufacturer: getRangeByManufacturer,
            editMachine: editMachine,
        };
        function fetchMachines() {
            var manufacturerId = userService.getManufacturerId();

            return $http.get(apiConstants.url + '/manufacturer/' + manufacturerId + '/machines', null);
        }

        function getRangeByManufacturer() {
            var manufacturerId = userService.getManufacturerId();
            var url = apiConstants.url + '/manufacturers/' + manufacturerId + '/product-ranges';
            return $http.get(url);
        }

        function createMachine(rangeId, name, thumnailUrl) {
            var machineData = createMachineData(name, rangeId, thumnailUrl);
            return $http.post(apiConstants.url + '/machine', machineData);
        }

        function createMachineData(name, rangeId, thumnailUrl) {
            return {
                "name": name,
                "description": "",
                "rangeId": rangeId,
                "thumbnailUrl": thumnailUrl
            };
        }

        function createNewRange(range) {
            var manufacturerId = userService.getManufacturerId();
            var rangeData = {
                "name": range,
                "description": "",
                "manufacturerId": manufacturerId
            };
            return $http.post(apiConstants.url + '/range', rangeData);
        }

        function createModel(modelName, machineId, urn, topLevelAssembly, fileType, fileName, originalFileName) {

            var data = {
                "modelName": modelName,
                "machineId": machineId,
                "modelDescription": "",
                "autodeskUrn": urn,
                "topLevelAssembly": topLevelAssembly,
                "fileType": fileType,
                "filename": fileName,
                "originalFilename": originalFileName
            };
            return $http.post(apiConstants.url + '/model', data);
        }

        function editMachine(rangeId, name, machineId, thumbnailUrl) {
            var updateData = {
                "name": name,
                "description": "",
                "rangeId": parseInt(rangeId),
                "thumbnailUrl": thumbnailUrl
            };
            return $http.put(apiConstants.url + '/machine/' + machineId, updateData);
        }
    }
})();
