<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" ng-click="orderMgmtCtrl.cancel()"
            aria-label="Close"><i class="fa fa-close" aria-hidden="true"></i>
    </button>
    <h2 class="modal-title underline">{{"ORDER_MGMT.TITLE" | translate}}</h2>
</div>

<div class="modal-body">
    <div class="error-well" ng-if="orderMgmtCtrl.errorSplittingOrder">
        <p translate>ORDER_MGMT.ERROR</p>
    </div>
    <div class="error-well" ng-if="orderMgmtCtrl.quantityMismatchError">
        <p translate>ORDER_MGMT.ENTER_VALID_QUANTITY</p>
    </div>
    <div class="error-well" ng-if="orderMgmtCtrl.nothingSelectedError">
        <p translate>ORDER_MGMT.AT_LEAST_ONE</p>
    </div>


    <p translate>ORDER_MGMT.CHOOSE_ITEMS</p>

    <form class="form" name="detailsForm">
        <span ng-show="orderMgmtCtrl.partsList.length > 0">
            <h3 translate>ORDER_MGMT.IDENTIFIED_PARTS</h3>
            <table class="table table-bordered">
                <thead>
                <tr>
                    <th translate>ORDER_MGMT.PART_NO</th>
                    <th translate>ORDER_MGMT.PART_DESC</th>
                    <th translate>ORDER_MGMT.QTY</th>
                    <th translate>ORDER_MGMT.QTY_TO_ORDER</th>
                </tr>
                </thead>

                <tbody>
                <tr ng-repeat="item in orderMgmtCtrl.partsList">
                    <td data-label="{{'ORDER_MGMT.PART_NO' | translate}}">{{item.partNumber}}</td>
                    <td data-label="{{'ORDER_MGMT.PART_DESC' | translate}}">{{item.partDescription}}</td>
                    <td data-label="{{'ORDER_MGMT.QTY' | translate}}">{{item.oldQuantity}}</td>
                    <td data-label="{{'ORDER_MGMT.QTY_TO_ORDER' | translate}}"><input ng-model="item.quantity" type="number" min="0"></td>
                </tr>

                </tbody>
            </table>
            <br>
        </span>

        <h3 class="underline" ng-show="orderMgmtCtrl.manualPartsList.length>0" translate>ORDER_MGMT.MANUALLY_ADDED_PARTS</h3>
        <table class="table table-bordered" ng-show="orderMgmtCtrl.manualPartsList.length>0">
            <thead>
            <tr>
                <th translate>ORDER_MGMT.PART_NO</th>
                <th translate>ORDER_MGMT.PART_DESC</th>
                <th translate>ORDER_MGMT.QTY</th>
                <th translate>ORDER_MGMT.QTY_TO_ORDER</th>
            </tr>
            </thead>

            <tbody>
            <tr ng-repeat="manualItem in orderMgmtCtrl.manualPartsList">
                <td>{{manualItem.partNumber}}</td>
                <td>{{manualItem.partDescription}}</td>
                <td>{{manualItem.oldQuantity}}</td>
                <td><input ng-model="manualItem.quantity" type="number" min="0"></td>
            </tr>

            </tbody>
        </table>

        <h3 class="underline" translate>ORDER.CONTACT_DETAILS</h3>
        <div class="d-flex flex-wrap no-gutters underline">
            <div class="col-12 col-md-4">

                <div class="input-group">
                    <label translate>ORDER.DELIVERY_ADDRESS</label>
                    <div class="select-box">
                        <select class="h-100" ng-model="orderMgmtCtrl.deliveryAddress" name="deliveryAddress" required
                                ng-class="{'has-error': detailsForm.$submitted && detailsForm.deliveryAddress.$invalid}">
                            <option ng-hide="true" value='' disabled="" translate>ORDER.SELECT_DELIVERY_ADDRESS</option>
                            <option ng-repeat="address in orderMgmtCtrl.addresses" value="{{address}}">
                                {{address.addressLine1}}, {{address.city}}, {{address.state}}, {{address.postcode}},
                                {{address.country}}
                            </option>

                        </select>

                        <div class="select-arrow"></div>
                    </div>
                </div>

                <div class="input-group">
                    <label translate>ORDER.BILLING_ADDRESS</label>
                    <div class="select-box">
                        <select class="h-100" ng-model="orderMgmtCtrl.billingAddress" name="billingAddress" required
                                ng-class="{'has-error': detailsForm.$submitted && detailsForm.billingAddress.$invalid }">
                            <option ng-hide="true" value='' disabled="" translate>ORDER.SELECT_BILLING_ADDRESS</option>
                            <option ng-repeat="address in orderMgmtCtrl.addresses" value="{{address}}">
                                {{address.addressLine1}}, {{address.city}}, {{address.state}}, {{address.postcode}},
                                {{address.country}}
                            </option>

                        </select>

                        <div class="select-arrow"></div>
                    </div>

                    <a class="btn xsmall secondary" href="" style="margin-top:10px;"
                       ng-click="orderMgmtCtrl.addNewAddress()" translate>ORDER.ADD_NEW_ADDRESS</a>

                </div>

            </div>

            <div class="col-12 col-md-4 pl-0 pl-md-4">

                <div class="input-group">
                    <label>
                        {{"ORDER.DELIVERY_CONTACT_NAME" | translate}}
                    </label>
                    <div class="select-box">
                        <select ng-model="orderMgmtCtrl.deliveryName" name="deliveryName" required
                                ng-class="{'has-error': detailsForm.$submitted && detailsForm.deliveryName.$invalid}">
                            <option ng-hide="true" value='' disabled="" translate>ORDER.SELECT_DELIVERY_NAME</option>
                            <option ng-repeat="name in orderMgmtCtrl.names" value="{{name.contactName}}">
                                {{name.contactName}}
                            </option>
                        </select>
                        <div class="select-arrow"></div>
                    </div>
                </div>

                <div class="input-group mb-0">
                    <label>
                        {{"ORDER.ORDER_CONTACT_NAME" | translate}}
                    </label>
                    <div class="select-box">
                        <select ng-model="orderMgmtCtrl.contactName" name="contactName" required
                                ng-class="{'has-error': detailsForm.$submitted && detailsForm.contactName.$invalid}">
                            <option ng-hide="true" value='' disabled="" translate>ORDER.SELECT_ORDER_NAME</option>
                            <option ng-repeat="name in orderMgmtCtrl.names" value="{{name.contactName}}">
                                {{name.contactName}}
                            </option>
                        </select>
                        <div class="select-arrow"></div>
                    </div>
                </div>

                <a class="btn xsmall secondary" href="" style="margin-top:10px;"
                   ng-click="orderMgmtCtrl.addNewName()" translate>ORDER.ADD_NEW_NAME</a>

            </div>

            <div class="col-12 col-md-4 pl-0 pl-md-4">

                <div class="input-group">
                    <label>
                        {{"ORDER.DELIVERY_NUMBER" | translate}}
                    </label>
                    <div class="select-box">
                        <select ng-model="orderMgmtCtrl.deliveryNumber" name="deliveryNumber" required
                                ng-class="{'has-error': detailsForm.$submitted && detailsForm.deliveryNumber.$invalid}">
                            <option ng-hide="true" value='' disabled="" translate>ORDER.SELECT_DELIVERY_NUMBER</option>
                            <option ng-repeat="number in orderMgmtCtrl.numbers" value="{{number.contactNumber}}">
                                {{number.contactNumber}}
                            </option>
                        </select>
                        <div class="select-arrow"></div>
                    </div>
                </div>


                <div class="input-group mb-0 ">
                    <label>
                        {{"ORDER.CONTACT_NUMBER" | translate}}
                    </label>
                    <div class="select-box">
                        <select ng-model="orderMgmtCtrl.contactNumber" name="contactNumber" required
                                ng-class="{'has-error': detailsForm.$submitted && detailsForm.contactNumber.$invalid && !detailsForm.purchaseOrder.$pristine}">
                            <option ng-hide="true" value='' disabled="" translate>ORDER.SELECT_CONTACT_NUMBER</option>
                            <option ng-repeat="number in orderMgmtCtrl.numbers" value="{{number.contactNumber}}">
                                {{number.contactNumber}}
                            </option>
                        </select>
                        <div class="select-arrow"></div>
                    </div>
                </div>

                <a class="btn xsmall secondary mb-2 mb-md-0" href="" style="margin-top:10px;"
                   ng-click="orderMgmtCtrl.addNewNumber()" translate>ORDER.ADD_NEW_NUMBER</a>

            </div>


        </div>
        
        <div >
            <h3 translate>CREATE_ORDER.REQUESTED_DELIVERY_DATE</h3>
            <input type="text" id="datepicker" autocomplete="off" ng-model="orderMgmtCtrl.requestedDeliveryDate"
                   placeholder="dd/mm/yyyy" required
                   ng-class="{'has-error': detailsForm.$submitted && detailsForm.requestedDeliveryDate.$invalid }"
                   name="requestedDeliveryDate"/>
        </div>

        <div class="d-flex flex-wrap no-gutters pt-4 underline">
            <div class="col-12 col-md-4" ng-if="orderMgmtCtrl.enquiryPurchaseOrder">
                <h3 translate>CREATE_ORDER.PURCHASE_ORDER_NUMBER</h3>
                <input placeholder="{{'ORDER.ENTER_PO_NUM' | translate}}" class="mb-0" type="text" ng-model="orderMgmtCtrl.purchaseOrder" name="purchaseOrder"
                       ng-class="{'has-error': detailsForm.$submitted && detailsForm.purchaseOrder.$invalid && !detailsForm.purchaseOrder.$pristine }">
            </div>

            <div class="col-12 col-md-4 pl-0 pl-md-3" ng-if="orderMgmtCtrl.isRequiredSerialNumber">
                <h3 translate>CREATE_ORDER.SERIAL_NUMBER</h3>
                <input placeholder="{{'ORDER.ENTER_SERIAL_NUMBER' | translate}}" class="mb-0" type="text" ng-disabled="orderMgmtCtrl.stockOrderSelected"
                       ng-model="orderMgmtCtrl.serialNumber" name="serialNumber"
                       ng-required="!orderMgmtCtrl.stockOrderSelected"
                       ng-class="{'has-error': detailsForm.$submitted && !orderMgmtCtrl.stockOrderSelected}">
            </div>

            <div class="col-12 col-md-4 pl-0 pl-md-3" class="d-flex stockorderContainer" ng-if="orderMgmtCtrl.isRequiredSerialNumber">
                <h3 class="pr-4" translate>CREATE_ORDER.STOCK_ORDER</h3>
                <input class="mb-0" style="width:35px!important; height:35px; padding:8px;" type="checkbox" ng-model="orderMgmtCtrl.stockOrder"
                       ng-change="orderMgmtCtrl.toggleStockOrder()" class="checkbox">
            </div>

        </div>
        <h3 ng-if="orderMgmtCtrl.validationError" class="warning-alert" translate>
            CREATE_ORDER.PLEASE_COMPLETE_REQUIRED
        </h3>
    </form>

    <div class="modal-actions">
        <button type="button" class="btn secondary" ng-click="orderMgmtCtrl.cancel()" translate>GENERAL.CANCEL
        </button>
        <button class="btn primary dpGreenModal" ng-show="orderMgmtCtrl.submitting">
            <span class="spinner-border text-light" role="status" aria-hidden="true"></span>
            {{'CREATE_ORDER.SUBMITTING' | translate}}
        </button>
        <button type="button" class="btn primary dpGreenModal" ng-hide="orderMgmtCtrl.submitting" ng-disabled="orderMgmtCtrl.submitDisabled"
                ng-click="orderMgmtCtrl.submitEnquiry()" translate>ORDER_MGMT.ORDER_MGMT
        </button>

    </div>


</div>