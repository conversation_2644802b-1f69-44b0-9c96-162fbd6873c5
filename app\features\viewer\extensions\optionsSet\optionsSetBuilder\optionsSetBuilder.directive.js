(function () {
    'use strict';

    angular
        .module('app.viewer')
        .directive('optionsSetBuilder', optionsSetBuilder);

    function optionsSetBuilder() {
        var directive = {
            restrict: 'E',
            templateUrl: 'features/viewer/extensions/optionsSet/optionsSetBuilder/optionsSetBuilder.html',
            controller: 'OptionsSetBuilderController',
            controllerAs: 'optionSetBuilderCtrl',
            bindToController: true
        };
        return directive;
    }

})();