<div class="d-flex justify-content-center align-items-center flex-column vh-100 auth {{appCtrl.subdomain}}-theme">
    <!--<div class="overlay visible"></div>-->
    <div class="sign-panels d-flex justify-content-center align-items-center flex-column">
            <div class="content">
                <img class="col-5 col-md-7" alt="CadShareLogo" ng-src="{{appCtrl.logoUrl}}">
                <h3 class="text-capitalize font-weight-normal mt-4" translate>PASSWORD.RESET_PASSWORD</h3>
                <form id="loginform" name="loginform" class="form" role="form">

                    <p class="message" ng-hide="forgotCtrl.showSuccessMessage" translate>PASSWORD.SUBMIT</p>

                    <p class="message error-alert" role="alert" ng-show="forgotCtrl.showErrorMessage">{{'LOGIN.EMAIL_NOT_RECOGNISED' | translate}} <a class="light" href="mailto:<EMAIL>"><EMAIL></a> {{'PASSWORD.ASSISTANCE' | translate}}</p>

                    <p class="message success-alert" role="alert" ng-show="forgotCtrl.showSuccessMessage" translate>PASSWORD.LINK_SENT</p>
					<div ng-hide="forgotCtrl.showSuccessMessage">
    					<div class="input-group mb-0">
                            <label translate>LOGIN.EMAIL</label>
                  <input id="forgot-email" type="email"  ng-model="forgotCtrl.emailAddress" placeholder="{{'LOGIN.ENTER_EMAIL' | translate}}">
    					</div>
    					
    					<button class="btn-signin mt-0 mb-4" ng-click="forgotCtrl.forgotPasswordClicked()" type="submit" translate>PASSWORD.RESET</button>
					</div>
                    <a class="light forgot" ui-sref="login" translate>PASSWORD.BACK_TO_LOGIN</a>
                </form>
            </div>
        </div>
</div>
