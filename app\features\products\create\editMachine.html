<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" ng-click="editMachineCtrl.cancel()" aria-label="Close">
        <i class="fa fa-close" aria-hidden="true"></i>
    </button>

    <h2 ng-if="editMachineCtrl.isEditMachine" class="modal-title">
        {{editMachineCtrl.isCreateMode ? editMachineCtrl.createModal : editMachineCtrl.editModal}} {{editMachineCtrl.isCreateMode ?
        editMachineCtrl.newProduct : editMachineCtrl.product}}
    </h2>
    <h2 ng-if="!editMachineCtrl.isEditMachine" class="modal-title">
        {{editMachineCtrl.isCreateMode ? editMachineCtrl.createModal : editMachineCtrl.editModal}} {{"PRODUCTS_CATALOG.NEW_RANGE" |
        translate}}
    </h2>
</div>
<div class="modal-body">
    <form name="createMachineForm" class="form">
        <div ng-if="editMachineCtrl.isEditMachine">
            <div class="input-group">
                <label translate>PRODUCTS_CATALOG.RANGE</label>
                <div class="d-flex w-100 align-items-center">
                    <div class="select-box">
                        <small ng-if="!data.rangeId" translate>PRODUCTS_CATALOG.SELECT_RANGE</small>
                        <select
                            ng-options="rangeValue.rangeId as rangeValue.name for rangeValue in editMachineCtrl.rangeValues | orderBy:'name'"
                            ng-model="data.rangeId"
                            ng-change="editMachineCtrl.rangeChanged(data.rangeId)"
                        ></select>
                        <div class="select-arrow"></div>
                    </div>
                </div>

                <button class="btn xsmall secondary mt-3" href="" ng-click="editMachineCtrl.createNewRangeField()" translate>
                    PRODUCTS_CATALOG.CREATE_NEW_RANGE
                </button>
            </div>

            <div class="input-group">
                <label translate>PRODUCTS_CATALOG.PRODUCT_NAME</label>
                <input
                    type="text"
                    placeholder="{{'PRODUCTS_CATALOG.ENT_PRODUCT_NAME' | translate}}"
                    ng-required="true"
                    ng-model="editMachineCtrl.machineName"
                />
            </div>
        </div>

        <div ng-if="editMachineCtrl.isEditMachine">
            <div class="input-group">
                <label translate>PRODUCTS_CATALOG.ADD_PRODUCT_IMAGE</label>
                <div ng-show="editMachineCtrl.machineThumbnailUrl != null">
                    <img ng-src="{{editMachineCtrl.machineThumbnailUrl}}" />
                </div>
            </div>

            <button type="button" class="btn small secondary" ng-click="editMachineCtrl.addMachineImage()" translate>
                EDIT_MACHINE.ADD_IMAGE
            </button>
        </div>

        <div class="input-group" ng-if="!editMachineCtrl.isEditMachine">
            <label translate>EDIT_MACHINE.CREATE_RANGE</label>
            <h3 ng-if="editMachineCtrl.rangeFailure" class="error-alert">{{editMachineCtrl.internalFailureMessage}}</h3>
            <input
                type="text"
                placeholder="{{'EDIT_MACHINE.ENTER_RANGE' | translate}}"
                ng-model="editMachineCtrl.newRange"
                ng-required="true"
            />
        </div>

        <div class="modal-actions" ng-if="editMachineCtrl.isEditMachine">
            <button type="button" class="btn small secondary" ng-click="editMachineCtrl.cancel()" translate>GENERAL.CANCEL</button>
            <button
                ng-hide="editMachineCtrl.isCreateMode"
                type="button"
                class="btn small primary"
                ng-disabled="!createMachineForm.$valid"
                ng-click="editMachineCtrl.editMachine()"
                translate
            >
                EDIT_MACHINE.EDIT
            </button>
            <button
                ng-show="editMachineCtrl.isCreateMode"
                type="button"
                class="btn small primary"
                ng-disabled="!createMachineForm.$valid"
                ng-click="editMachineCtrl.createMachine()"
                translate
            >
                PRODUCTS_CATALOG.CREATE_NEW
            </button>
        </div>

        <div class="modal-actions">
            <div ng-if="!editMachineCtrl.isEditMachine">
                <button type="button" class="btn small secondary" ng-click="editMachineCtrl.cancelRange()" translate>GENERAL.CANCEL</button>
                <button
                    type="button"
                    class="btn small primary"
                    ng-disabled="!createMachineForm.$valid"
                    ng-click="editMachineCtrl.createNewRange()"
                    translate
                >
                    EDIT_MACHINE.CREATE_RANGE
                </button>
            </div>
        </div>
    </form>
</div>
