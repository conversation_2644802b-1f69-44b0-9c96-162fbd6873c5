(function () {
    'use strict';

    angular
        .module('app.orders')
        .controller('DPEnquiryController', DPEnquiryController);

    DPEnquiryController.$inject = ['dpOrdersService', '$rootScope', '$scope', '$controller', 'userService', '$state', 'headerBannerService', '$uibModal', '$translate'];

    function DPEnquiryController(dpOrdersService, $rootScope, $scope, $controller, userService, $state, headerBannerService, $uibModal, $translate) {
        var vm = this;

        angular.extend(vm, $controller('DPOrderController', {$scope: $scope}));

        var QUOTE_GENERATED_TEXT, DRAFT_SAVED_TEXT, PRICE_ZERO, PRICE_ZERO_DESCRIPTION, SHIPPING_ZERO, SHIPPING_ZERO_DESCRIPTION, SHIPPING_PRICE_ZERO, SHIPPING_PRICE_ZERO_DESCRIPTION;

        vm.enquiryPurchaseOrder = userService.getEnquiryPurchaseOrder();
        vm.enquiriesOnly = userService.getEnquiriesOnly();
        vm.currencies = userService.getCurrencyList();
        
        vm.addManualParts = [];

        vm.nextStep = nextStep;
        vm.altStep = altStep;
        vm.goToSplitOrder = goToSplitOrder;
        vm.orderEdited = orderEdited;
        vm.editCustomOrderNumber = editCustomOrderNumber;
        vm.removeAddManualPart = removeAddManualPart;
        vm.saveAddManualParts = saveAddManualParts;
        vm.cancelCreateAddManualParts = cancelCreateAddManualParts;
        vm.createAddManualPart = createAddManualPart;
        vm.openComment = openComment;
        vm.reloadOrder = reloadOrder;
        vm.openPriceIsZeroModal = openPriceIsZeroModal;
        vm.openShippingIsZeroModal = openShippingIsZeroModal;
        vm.openShippingPriceIsZeroModal = openShippingPriceIsZeroModal;
        vm.isPartInformationAvailable = isPartInformationAvailable;
        vm.hasVisibleDiscountedPrices = hasVisibleDiscountedPrices
        initialize();

        function initialize() {

            vm.showPurchaseOrder = vm.enquiryPurchaseOrder;
            vm.showEstimatedDelivery = false;
            vm.displayAdditionalParts = false;
            vm.isDiscountEditable = false;
            vm.isDiscountVisible = true;
            vm.isOrderEdited = false;
            vm.isAddPartsVisible = true;
            vm.showCancelOrderButton = true;
            vm.showEditDetailsButton = true;
            vm.showEditOrderNumberButton = true;
            vm.deleteOrderItem = false;
            vm.showPartDescription = false;
            vm.showModelName = false;
            vm.showProductName = false;
            vm.splitOrders = [];

            vm.showNextStep = true;
            vm.showAltStepBtn = true;
            vm.isShippingEditable = true;
            vm.isPriceEditable = true;
            vm.isQuantityEditable = true;
            vm.isActionActive = true;
            vm.isDetailsEditable = false;
            vm.isNotesEditable = true;
            vm.displayNotes = true;
            vm.showSplitOrderBtn = true;
            vm.showEditDetailsButton = true;
            vm.showEditOrderNumberButton = true;
            vm.showCurrencySelection = true;
            vm.hidePrice = false;
            vm.exportPartDataCsvVisible = true;
            if (vm.externallyProcessed) {
                vm.showExternallyProcessButton = true;
            }

            $translate(['ENQUIRY.PENDING_SUBMITTED', 'ENQUIRY.DRAFT_SAVED_TEXT', 'ENQUIRY.QUOTE_GENERATED', 'ORDER.PRICE_ZERO', 'ORDER.PRICE_ZERO_DESCRIPTION', 'ORDERS.ENQUIRY', 'ENQUIRY.GENERATE_QUOTE', 'ENQUIRY.SAVE_DRAFT', 'ORDER.SHIPPING_ZERO_DESCRIPTION', 'ORDER.SHIPPING_ZERO', 'ORDER.SHIPPING_PRICE_ZERO', 'ORDER.SHIPPING_PRICE_ZERO_DESCRIPTION'])
                .then(function (resp) {
                    vm.orderStatusPill = resp['ENQUIRY.PENDING_SUBMITTED'];
                    DRAFT_SAVED_TEXT = resp['ENQUIRY.DRAFT_SAVED_TEXT'];
                    QUOTE_GENERATED_TEXT = resp['ENQUIRY.QUOTE_GENERATED'];
                    PRICE_ZERO = resp["ORDER.PRICE_ZERO"];
                    PRICE_ZERO_DESCRIPTION = resp["ORDER.PRICE_ZERO_DESCRIPTION"];
                    SHIPPING_ZERO = resp["ORDER.SHIPPING_ZERO"];
                    SHIPPING_ZERO_DESCRIPTION = resp["ORDER.SHIPPING_ZERO_DESCRIPTION"];
                    SHIPPING_PRICE_ZERO = resp["ORDER.SHIPPING_PRICE_ZERO"];
                    SHIPPING_PRICE_ZERO_DESCRIPTION = resp["ORDER.SHIPPING_PRICE_ZERO_DESCRIPTION"];
                    vm.STAGE = resp['ORDERS.ENQUIRY'];
                    vm.nextStepText = resp['ENQUIRY.GENERATE_QUOTE'];
                    vm.altStepText = resp['ENQUIRY.SAVE_DRAFT'];
                });
            getOrder();
        }

        function getOrder() {
            dpOrdersService.getOrder(vm.orderId)
                .then(getOrdersSuccess, serviceCallFailed);
        }

        function getOrdersSuccess(response) {

            $rootScope.$broadcast("Update-Unread-DP-Order-Tabs");
            if (response.data.orderStatus === 'SUBMITTED' || response.data.orderStatus === 'PREPARING') {
                vm.data = response.data;
                vm.orderLists = response.data.orderItems;
                vm.totalItems = vm.orderLists.length;
                vm.billingAddress = vm.createReadableAddress(response.data.billingAddress);
                vm.shippingAddress = vm.createReadableAddress(response.data.shippingAddress);
                vm.data.originalShippingPrice = vm.data.shippingPrice;
                vm.data.shippingPrice = vm.data.shippingPrice ? vm.data.shippingPrice : 0;
                vm.isOrderItemsLoaded = true;
                vm.additionalParts = vm.data.additionalParts;
                vm.parentOrderId = vm.data.parentOrderId;
                vm.parentCustomOrderDisplay = vm.data.parentCustomOrderDisplay;
                vm.customOrderDisplay = vm.data.customOrderDisplay;
                vm.isPreviewStockLevelEnabled = userService.getPreviewStockLevelEnabled();

                vm.selectedCurrency = userService.getCurrencyData(vm.data.currency);

                if (vm.additionalParts.length > 0) {
                    vm.displayAdditionalParts = true;
                    vm.updateManualPartTotals();
                }

                if (response.data.orderStatus === 'PREPARING') {
                    $translate(['ENQUIRY.ENQUIRY_PREPARING'])
                        .then(function (resp) {
                            vm.orderStatusPill = resp['ENQUIRY.ENQUIRY_PREPARING'];
                        });

                }
                vm.isDiscountVisible = true;
                vm.isDiscountEditable = true;

                calculateSplitOrders();
                vm.updateItemTotals(true, this, true);
            } else {
                vm.redirectToOrder(response.data.orderId, response.data.orderStatus);
            }
        }

        function serviceCallFailed(error) {
            vm.isOrderItemsLoaded = true;
            headerBannerService.setNotification('ERROR', error.data.error, 10000);
        }

        function openPriceIsZeroModal() {
            var confirmObject = {
                titleText: PRICE_ZERO,
                bodyText: PRICE_ZERO_DESCRIPTION
            };
            $uibModal.open({
                templateUrl: 'features/shared/confirmationModal/confirmModal.html',
                controller: 'ConfirmModalController',
                controllerAs: 'confirmModalCtrl',
                size: 'sm',
                resolve: {
                    confirmObject: function () {
                        return confirmObject;
                    }
                }
            }).result.then(function () {
                vm.data.orderStatus = "QUOTE";
                updateData();
                dpOrdersService.updateOrder(vm.data)
                    .then(generateQuoteSuccess, updateOrderFailed);
            }, function () {
                console.log('Modal Cancelled');
            });
        }

        function openShippingIsZeroModal() {
            var confirmObject = {
                titleText: SHIPPING_ZERO,
                bodyText: SHIPPING_ZERO_DESCRIPTION
            };
            $uibModal.open({
                templateUrl: 'features/shared/confirmationModal/confirmModal.html',
                controller: 'ConfirmModalController',
                controllerAs: 'confirmModalCtrl',
                size: 'sm',
                resolve: {
                    confirmObject: function () {
                        return confirmObject;
                    }
                }
            }).result.then(function () {
                vm.data.orderStatus = "QUOTE";
                updateData();
                dpOrdersService.updateOrder(vm.data)
                    .then(generateQuoteSuccess, updateOrderFailed);
            }, function () {
                console.log('Modal Cancelled');
            });
        }

        function openShippingPriceIsZeroModal() {
            var confirmObject = {
                titleText: SHIPPING_PRICE_ZERO,
                bodyText: SHIPPING_PRICE_ZERO_DESCRIPTION
            };
            $uibModal.open({
                templateUrl: 'features/shared/confirmationModal/confirmModal.html',
                controller: 'ConfirmModalController',
                controllerAs: 'confirmModalCtrl',
                size: 'sm',
                resolve: {
                    confirmObject: function () {
                        return confirmObject;
                    }
                }
            }).result.then(function () {
                vm.data.orderStatus = "QUOTE";
                updateData();
                dpOrdersService.updateOrder(vm.data)
                    .then(generateQuoteSuccess, updateOrderFailed);
            }, function () {
                console.log('Modal Cancelled');
            });
        }

        function nextStep() {
            vm.isNextStepDisabled = true;
            if (vm.isShippingEditable && vm.data.shippingPrice === 0 && (vm.hasPricesMissing || vm.manualHasPricesMissing))  {
                openShippingPriceIsZeroModal();
                vm.isNextStepDisabled = false;
            } else if (vm.hasPricesMissing || vm.manualHasPricesMissing ) {
                openPriceIsZeroModal();
                vm.isNextStepDisabled = false;
            } else if (vm.isShippingEditable && vm.data.shippingPrice === 0) {
                openShippingIsZeroModal();
                vm.isNextStepDisabled = false;
            } else if (vm.splitOrders.length > 0 && vm.isShippingEditable && vm.data.shippingPrice !== 0) {
                vm.data.orderStatus = "QUOTE";
                updateData();
                dpOrdersService.updateOrder(vm.data)
                    .then(generateQuoteSuccess, updateOrderFailed);
            } else {
                vm.data.orderStatus = "QUOTE";
                updateData();
                dpOrdersService.updateOrder(vm.data)
                    .then(generateQuoteSuccess, updateOrderFailed);
            }

        }

        function updateData() {
            vm.data.price = vm.total;
            vm.data.additionalParts = vm.additionalParts;
            vm.data.currency = vm.selectedCurrency;
        }

        function altStep() {
            vm.data.orderStatus = "PREPARING";
            updateData();
            updateOrder();
        }

        function updateOrder() {
            dpOrdersService.updateOrder(vm.data)
                .then(saveDraftSuccess, updateOrderFailed);
        }

        function saveDraftSuccess() {
            headerBannerService.setNotification('SUCCESS', DRAFT_SAVED_TEXT, 10000);
            vm.isOrderEdited = false;
            getOrder();
        }

        function generateQuoteSuccess() {
            headerBannerService.setNotification('SUCCESS', QUOTE_GENERATED_TEXT, 10000);
            vm.isOrderEdited = false;
            $state.go('dpOrders.customerOrders.orders.quotation', {
                orderId: vm.data.orderId
            });
        }

        function updateOrderFailed(error) {
            headerBannerService.setNotification('ERROR', error.data.error, 10000);
            vm.isNextStepDisabled = false;
        }

        function goToSplitOrder(splitOrderId) {
            $state.go('dpOrders.customerOrders.orders.enquiry', {orderId: splitOrderId});
        }

        function calculateSplitOrders() {
            vm.splitOrders = [];
            if (vm.orderLists) {
                for (var i = 0; i < vm.orderLists.length; i++) {
                    if (vm.orderLists[i].splitOrderIds && vm.orderLists[i].splitOrderIds.length > 0) {
                        for (var j = 0; j < vm.orderLists[i].splitOrderIds.length; j++) {
                            if (!_.findWhere(vm.splitOrders, {orderId: vm.orderLists[i].splitOrderIds[j]})) {
                                var splitOrder = {
                                    orderId: vm.orderLists[i].splitOrderIds[j],
                                    displayId: vm.orderLists[i].splitOrderCustomDisplayNumbers[j] ? vm.orderLists[i].splitOrderCustomDisplayNumbers[j] : vm.orderLists[i].splitOrderIds[j]
                                }
                                vm.splitOrders.push(splitOrder);
                            }

                        }
                    }
                }
            }
            if (vm.additionalParts) {
                for (var k = 0; k < vm.additionalParts.length; k++) {
                    if (vm.additionalParts[k].splitOrderIds && vm.additionalParts[k].splitOrderIds.length > 0) {
                        for (var l = 0; l < vm.additionalParts[k].splitOrderIds.length; l++) {
                            if (!_.findWhere(vm.splitOrders, {orderId: vm.additionalParts[k].splitOrderIds[l]})) {
                                var splitOrder = {
                                    orderId: vm.additionalParts[k].splitOrderIds[l],
                                    displayId: vm.additionalParts[k].splitOrderCustomDisplayNumbers[l] ? vm.additionalParts[k].splitOrderCustomDisplayNumbers[l] : vm.additionalParts[k].splitOrderIds[l]
                                }
                                vm.splitOrders.push(splitOrder);
                            }
                        }
                    }
                }
            }

        }

        $scope.$on("Order-Edited", orderEdited);

        function orderEdited() {
            vm.isOrderEdited = true;
        }

        function editCustomOrderNumber() {
            $uibModal.open({
                templateUrl: 'features/orders/enquiries/customNumber/customNumberModal.html',
                controller: 'CustomNumberController',
                controllerAs: 'customNumberCtrl',
                size: 'sm',
                resolve: {
                    customNumberObject: function () {
                        return {customOrderDisplay: vm.customOrderDisplay, orderId: vm.orderId};
                    }
                }
            }).result.then(function (customOrderDisplay) {
                vm.data.customOrderDisplay = customOrderDisplay
            }, function () {
                console.log('Modal Cancelled');
            });
        }

        function createAddManualPart() {
            var emptyPart = {
                "partNumber": "",
                "partDescription": "",
                "machineName": "",
                "quantity": 1
            };
            vm.addManualParts.push(emptyPart);
            vm.addManualPartsActive = true;
        }

        function removeAddManualPart(manualPart) {
            var itemToRemoveIndex = _.findIndex(vm.addManualParts, {
                partNumber: manualPart.partNumber,
                partDescription: manualPart.partDescription
            });
            if (itemToRemoveIndex > -1) {
                vm.addManualParts.splice(itemToRemoveIndex, 1);
            }
            vm.addManualPartsActive = vm.addManualParts.length > 0;

        }

        function saveAddManualParts() {
            if (vm.addManualPartsForm.$valid) {
                for (var i = 0; i < vm.addManualParts.length; i++) {
                    vm.addManualParts[i].price = 0;
                    vm.addManualParts[i].totalPrice = 0;
                    vm.addManualParts[i].orderId = vm.orderId;
                    vm.data.additionalParts.push(vm.addManualParts[i])
                }

                vm.addManualParts = [];
                if (vm.data.additionalParts.length > 0) {
                    vm.displayAdditionalParts = true;
                }
                vm.addManualPartsActive = false;

                updateOrder();
            }
        }

        function cancelCreateAddManualParts() {
            vm.addManualParts = [];
            vm.addManualPartsActive = false;
        }

        $rootScope.$on('$stateChangeStart',
            function (event, toState, toParams, fromState, fromParams) {
                event.preventDefault();
                // transitionTo() promise will be rejected with
                // a 'transition prevented' error
            });

        function openComment(commentThread, orderItemId) {
            var commentObject = {
                threadId: commentThread,
                orderId: vm.orderId,
                orderItemId: orderItemId
            };
            if (vm.enquiriesOnly) {
                commentObject.blockAddingComment = true;
            }

            $uibModal.open({
                templateUrl: 'features/shared/comments/comments.html',
                controller: 'CommentController',
                controllerAs: 'commentsCtrl',
                resolve: {
                    commentObject: function () {
                        return commentObject;
                    }
                }
            }).result.then(function (commentObject) {

                if (commentObject.orderItemId) {
                    var index = _.findIndex(vm.data.orderItems, {orderItemId: commentObject.orderItemId});
                    vm.data.orderItems[index].commentThread = {};
                    vm.data.orderItems[index].commentThread.orderItemId = commentObject.orderItemId;
                    vm.data.orderItems[index].commentThread.id = commentObject.threadId;
                    vm.data.orderItems[index].commentThread.orderId = commentObject.orderId;
                } else {
                    vm.data.commentThread = {};
                    vm.data.commentThread.id = commentObject.threadId;
                    vm.data.commentThread.orderId = commentObject.orderId;
                }
            }, function () {
                console.log('Modal Cancelled');
            });

        }

        function reloadOrder() {
            getOrder();
        }

        function isPartInformationAvailable(item) {
            var partInformation = {
                showPartDescription: item.partDescription,
                showModelName: item.modelName,
                showMachineName: item.machineName
            };
            vm.showPartDescription = partInformation.showPartDescription;
            vm.showModelName = partInformation.showModelName;
            vm.showMachineName = partInformation.showMachineName;
            return partInformation;
        }

        function hasVisibleDiscountedPrices() {

            if (!vm.data || !vm.data.orderItems) {
                return false;
            }

            var orderItems = vm.data.orderItems;

            var hasDiscountedPrices = orderItems.some(item => item.discountedPrice && item.discountedPrice !== 0);

            return hasDiscountedPrices;
        }

    }
})();
