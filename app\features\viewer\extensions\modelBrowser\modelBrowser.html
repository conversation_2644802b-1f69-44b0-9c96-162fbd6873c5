<div class="side-menu-model">
    <div class="input-icon-wrap">
        <i class="fa fa-search"></i>
        <input type="search" ng-model="modelBrowserCtrl.browserFilter" placeholder="{{'MODEL_BROWSER.SEARCH_MODEL' | translate}}"/>
        <button ng-click="modelBrowserCtrl.clearSearch()">
            <i class="fa fa-times-circle" aria-hidden="true"></i>
        </button>
    </div>


    <div class="model-scroll" ng-if="modelBrowserCtrl.showTree">
        <div class="sidebar-content sidebar-content-search-field" ivh-treeview="modelBrowserCtrl.modelTree"
             ivh-treeview-filter="modelBrowserCtrl.browserFilter"
             ivh-treeview-options="modelBrowserCtrl.customOpts"
             ivh-treeview-on-cb-change="modelBrowserCtrl.onSelectionChange(ivhNode)"
             ivh-treeview-on-toggle="modelBrowserCtrl.toggleChange(ivhNode)">
        </div>
    </div>



</div>