<div class="overlay-cover-tablet panel-dark form" ng-show="softCopyCtrl.showLoader">
    <div class="download-preloader-bg" style="margin-top:170px">
        <div>
            <i class="fas fa-sync-alt fa-spin"></i>

            <p id="loader-text" translate>SOFT_COPY.PLEASE_WAIT </p>

            <p translate>SOFT_COPY.NOW_EXPORTING</p>
        </div>
    </div>
</div>

<section class="px-5 pt-5">

    <div class="">
        <p><a class="dark-secondary" href="" ng-click="softCopyCtrl.backToModels()"><i class="fa fa-caret-left"></i>
            {{SOFT_COPY.BACK_TO | translate}}
            {{softCopyCtrl.machineName}}</a></p>
        <h2 class="viewables-header">{{SOFT_COPY.SOFTCOPIES_FOR | translate}} {{softCopyCtrl.modelName}}</h2>
    </div>
</section>

<section class="d-flex flex-wrap">

    <div class="col-lg-4 col-12">
        <div class="responsiveContainer my-4 my-lg-0 p-5">
            <div class="manufacturer-details">
                <h2 class=""><span translate>SOFT_COPY.VIEWABLE_DETAILS</span></h2>
                <p class="font-weight-bold title" translate>SOFT_COPY.FILENAME</p>
                <p>{{softCopyCtrl.model.originalFilename}}</p>
                <p class="font-weight-bold title" translate>SOFT_COPY.STATUS</p>
                <span ng-class="softCopyCtrl.model.autodeskStatus.includes('PROPERTIES_PROCESSED') ? 'badge-pill primary' : 'badge-pill'"> {{softCopyCtrl.model.autodeskStatusDisplay | capitalize}} </span>
                <p class="font-weight-bold title" translate>SOFT_COPY.CREATED_DATE</p>
                <p>{{softCopyCtrl.model.createdDate | date: "d MMMM y"}}</p>
                <p class="font-weight-bold title" translate>SOFT_COPY.CREATED_BY</p>
                <p>{{softCopyCtrl.model.createdByUserFullName}}</p>
            </div>
        </div>
    </div>

    <div class="col-lg-8 col-12">
        <div class="responsiveContainer">

                <div class="flex mb-4 px-4 px-md-0">

                    <div class="input-group mb-0 col-12 col-md-4 px-0 px-md-4 mb-3 mb-md-0">
                        <input  class="form-control mr-0" type="search" ng-change="softCopyCtrl.searchFilterChange()" ng-model="softCopyCtrl.searchValue" placeholder="{{'SOFT_COPY.SEARCH_BY_SOFT_COPY' | translate}}">
                        <div class="input-group-append">
                            <span class="input-group-text"><i class="fa fa-search"></i></span>
                        </div>
                    </div>

                    <button class="btn primary ml-auto mr-4 col-12 col-md-auto create-machine" href="" ng-click="softCopyCtrl.createSoftCopy()" translate>
                        SOFT_COPY.CREATE_NEW
                    </button>

                </div>


            <div class="responsiveContainer">

                <table class="table table-bordered">
                    <thead>
                    <tr>
                        <th class="col-8" translate>SOFT_COPY.SOFT_COPY</th>
                        <th class="col-4" translate>GENERAL.ACTIONS</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr ng-class="{'last-item': $last}" ng-repeat="softCopy in softCopyCtrl.softCopiesList | filter :softCopyCtrl.searchValue | filter : softCopyCtrl.filterValue : true | myLimitTo : softCopyCtrl.count : softCopyCtrl.start">

                        <td data-label="{{'SOFT_COPY.SOFT_COPY' | translate}}">{{softCopy.name}}</td>

                        <td class="has-dropdown flex justify-content-end justify-content-md-start">
                            <div class="btn-group">

                                    <a href="" class="btn xsmall secondary main-action"
                                       ng-click="softCopyCtrl.openSoftCopyViewerPage(softCopy)" translate>SOFT_COPY.VIEW</a>

                                    <div href="" class="btn xsmall secondary dropdown-toggle" data-toggle="dropdown"
                                         aria-haspopup="true" aria-expanded="false">

                                    <div class="sub-popup viewables">
                                        <ul class="more-options">
                                            <li title="{{'SOFT_COPY.VIEW' | translate}}">
                                                <a href="" class="dark-secondary"
                                                   ng-click="softCopyCtrl.openSoftCopyViewerPage(softCopy)"><i
                                                        class="fa fa-fw fa-eye"></i>{{'SOFT_COPY.VIEW_SOFT' | translate}}</a>
                                            </li>
                                            <li title="{{'SOFT_COPY.EDIT' | translate}}">
                                                <a href="" class="dark-secondary"
                                                   ng-click="softCopyCtrl.editSoftCopy(softCopy)"><i
                                                        class="fa fa-fw fa-pencil"></i>{{'SOFT_COPY.EDIT_SOFT' | translate}}</a>
                                            </li>
                                            <li title="{{'SOFT_COPY.EXPORT' | translate}}">
                                                <a href="" class="dark-secondary"
                                                   ng-click="softCopyCtrl.exportSoftCopy(softCopy)"><i
                                                        class="fa fa-fw fa-download"></i>{{'SOFT_COPY.EXPORT_SOFT' | translate}}</a>
                                            </li>
                                            <li title="{{'SOFT_COPY.DELETE' | translate}}">
                                                <a href="" class="delete"
                                                   ng-click="softCopyCtrl.deleteSoftCopy(softCopy)"><i
                                                        class="fa fa-fw fa-trash"></i>{{'SOFT_COPY.DELETE_SOFT' | translate}}</a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </td>
                    </tr>

                    <tr ng-show="!softCopyCtrl.softCopiesList.length > 0 && softCopyCtrl.isSoftCopiesLoaded">
                        <td colspan="7" data-label="{{'SOFT_COPY.NO_SOFT_AVAILABLE' | translate}}" translate>
                            SOFT_COPY.NO_SOFT_AVAILABLE
                        </td>
                    </tr>

                    <tr align="center" ng-hide="softCopyCtrl.isSoftCopiesLoaded">
                        <td class="preloader" colspan="7"><img class="ajax-loader" height="60"
                                                               ng-src="images/cadpreloader.gif"
                                                               width="60"/>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

</section>

