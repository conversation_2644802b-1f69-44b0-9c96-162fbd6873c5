(function () {
    'use strict';

    angular
        .module('app.shared')
        .controller('FullShipOrderModalController', FullShipOrderModalController);

    FullShipOrderModalController.$inject = ['$uibModalInstance', 'dataObject', '$filter', '$scope', 'awsS3Service'];

    function FullShipOrderModalController($uibModalInstance, dataObject, $filter, $scope, awsS3Service) {

        var vm = this;

        vm.confirm = confirm;
        vm.invoiceAttached = invoiceAttached;
        vm.invoiceBucketURL = [];
        vm.invoiceFiles = [];
        vm.invoiceFileNames = [];
        vm.cancel = $uibModalInstance.dismiss;
        vm.hasInvoice = false;
        vm.isConfirming = false;
        var respCount = 0;
        var invoiceCount = 0;

        initialize();

        function initialize() {
            if (dataObject) {
                vm.estDate = dataObject.estDate;
                vm.estimatedDeliveryDate = dataObject.estimatedDeliveryDate;
            }
            configureDatePicker();
        }

        function confirm() {
            if(vm.shippingDate) {
                vm.isConfirming = true;
                if (vm.hasInvoice) {

                    for (var i = 0; i < vm.invoiceFiles.length; i++) {
                        invoiceCount++;
                        awsS3Service.uploadOrderPDF(vm.invoiceFiles[i])
                            .then(uploadPDFSuccess, uploadPDFFailed);
                    }
                } else {
                    var response = {
                        shippingDate: vm.shippingDate,
                        invoiceURL: []
                    };
                    $uibModalInstance.close(response);
                }
            }else{
                vm.shippingDateError = true;
            }
        }

        function configureDatePicker() {
            var d = new Date();
            setTimeout(function () {
                $('#datepicker').datepicker({
                    dateFormat: 'dd/mm/yy',
                    onSelect: function (dateText) {
                        vm.shippingDate = dateText;
                        $scope.$apply();
                    }
                });
            }, 200);

            if (!vm.shippingDate && vm.estimatedDeliveryDate) {
                var d = new Date(vm.estimatedDeliveryDate);
                var dateString = ("0" + d.getDate()).slice(-2) + "/" + ("0" + (d.getMonth() + 1)).slice(-2) + "/" +
                    d.getFullYear();
                vm.shippingDate = dateString;
            }
        }

        function uploadPDFFailed(error){
            console.error("Upload pdf failed: " + error);
        }

        function uploadPDFSuccess(resp){
            vm.invoiceBucketURL.push(resp);
            respCount++;
            if(respCount === invoiceCount){
                var response = {
                    shippingDate: vm.shippingDate,
                    invoiceURL: vm.invoiceBucketURL
                };
                $uibModalInstance.close(response);
            }
        }

        function invoiceAttached(obj) {
            vm.hasInvoice = false;

            var elem = obj.target || obj.srcElement;
            if (elem.files.length > 0) {
                for(var j=0; j<elem.files.length; j++){
                    vm.invoiceFiles.push(elem.files[j]);
                    vm.invoiceFileNames.push(elem.files[j].name);
                }

                $scope.$digest();
                vm.hasInvoice = true;
            }
        }


    }
})();
