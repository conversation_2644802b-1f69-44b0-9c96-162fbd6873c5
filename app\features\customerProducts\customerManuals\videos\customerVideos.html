<section id="videoBox" ng-show="customerVideosCtrl.videosList.length > 0">
    <div class="center-align" >
        <div class="embed-responsive embed-responsive-16by9">
            <iframe ng-src="{{customerVideosCtrl.playingVideoURL}}" allowfullscreen></iframe>
        </div>
        <h3>{{"CUST_VIDEOS.NOW_PLAYING" | translate}}: {{customerVideosCtrl.nowPlaying.name}} - {{customerVideosCtrl.nowPlaying.description}}</h3>
    </div>

</section>

    <section class="responsiveContainer">

                <div class="flex p-4 p-md-0">
                    <search-filter on-search-change="customerVideosCtrl.searchFilterChange()" class="col-12 col-md-3" state-name="'customerVideos'" value="customerVideosCtrl.searchValue" placeholder-key="CUST_VIDEOS.SEARCH_BY"></search-filter>
                </div>

    <table class="table table-bordered">
        <thead>
        <tr>
            <th ng-class="customerVideosCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'"
                ng-click="customerVideosCtrl.viewable_sort='name'; customerVideosCtrl.sortReverse = !customerVideosCtrl.sortReverse"
                translate>
                CUST_VIDEOS.NAME
            </th>
            <th ng-class="customerVideosCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'"
                ng-click="customerVideosCtrl.viewable_sort='description'; customerVideosCtrl.sortReverse = !customerVideosCtrl.sortReverse"
                translate>
                CUST_VIDEOS.SUMMARY
            </th>
            <th style="width:230px;" translate>GENERAL.ACTIONS</th>
        </tr>
        </thead>
        <tbody>
        <tr ng-repeat="video in customerVideosCtrl.videosList | orderBy:customerVideosCtrl.viewable_sort:customerVideosCtrl.sortReverse | filter : customerVideosCtrl.searchValue  | myLimitTo : customerVideosCtrl.count : customerVideosCtrl.start"
            ng-show="customerVideosCtrl.videosList.length > 0">
            <td data-label="{{'CUST_VIDEOS.NAME' | translate}}">{{video.name}}</td>
            <td data-label="{{'CUST_VIDEOS.SUMMARY' | translate}}">{{video.description}}</td>
            <td>
                <button href="" class="btn xsmall primary" ng-click="customerVideosCtrl.playVideo(video)" translate>
                    CUST_VIDEOS.PLAY
                </button>
            </td>
        </tr>

        <tr ng-hide="customerVideosCtrl.videosList.length > 0" align="center">
            <td colspan="3" translate>CUST_VIDEOS.NO_VIDEOS</td>
        </tr>

        <tr ng-show="customerVideosCtrl.videosList === null" align="center">
            <td class="preloader" colspan="3">
                <img ng-src="images/cadpreloader.gif" class="ajax-loader" height="60" width="60"/>
            </td>
        </tr>
        </tbody>
    </table>

    <div ng-show="customerVideosCtrl.totalItems > 0">
        <div class="text-right">
            <uib-pagination max-size="4" force-ellipses="true" total-items="customerVideosCtrl.totalItems"
                            ng-model="customerVideosCtrl.currentPage"
                            items-per-page="customerVideosCtrl.itemPerPage"
                            ng-change="customerVideosCtrl.pageChanged()" boundary-links="true" class="pagination-md"
                            previous-text="&lsaquo;" next-text="&rsaquo;" first-text="&laquo;"
                            last-text="&raquo;"></uib-pagination>
        </div>
    </div>
</section>