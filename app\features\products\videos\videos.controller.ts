interface IHttpResponse<T> {
    data: T;
    status: number;
    statusText: string;
    headers: any;
    config: any;
}

class VideosController {
    static $inject = [
        'manufacturerProductService',
        '$uibModal',
        '$state',
        'uploadModelService',
        'headerBannerService',
        '$window',
        '$translate',
    ];

    public start: number;
    public sortReverse: boolean;
    public videosList: any;
    public actions: any;
    public UPLOAD_SUCCESS?: string;
    public DELETE_SUCCESS?: string;
    public totalItems?: number; 
    public isFixedHeader: boolean;

    public allVideos: any[];
    public isLoading: boolean;
    public initialLoadDone: boolean;

    constructor(
        private manufacturerProductService: any,
        private $uibModal: any,
        private $state: any,
        private uploadModelService: any,
        private headerBannerService: any,
        private $window: any,
        private $translate: any
        
    ) {
        this.start = 0;
        this.sortReverse = false;
        this.videosList = null;
        this.isFixedHeader = false;
        
        this.isLoading = false;
        this.initialLoadDone = false;
        this.videosList = [];
        this.allVideos = [];
        this.totalItems = 0;

        this.handleInfiniteScroll = this.handleInfiniteScroll.bind(this);

        window.addEventListener('scroll', this.handleInfiniteScroll);

        this.$translate(['VIDEOS.UPLOAD_SUCCESS', 'VIDEOS.DELETE_SUCCESS']).then((resp: Record<string, string>) => {
            this.UPLOAD_SUCCESS = resp['VIDEOS.UPLOAD_SUCCESS'];
            this.DELETE_SUCCESS = resp['VIDEOS.DELETE_SUCCESS'];
        });

        this.actions = [
            {
                title: 'List Viewables',
                onClick: (entity: any) => {
                    this.viewVideo(entity);
                },
                icon: 'fa-eye',
                label: () => {
                    return $translate.instant('VIDEOS.VIEW');
                },
            },
            {
                title: 'Edit',
                onClick: (entity: any) => {
                    this.editVideo(entity);
                },
                icon: 'fa-pencil',
                label: () => {
                    return $translate.instant('VIDEOS.EDIT');
                },
            },
            {
                title: 'Delete',
                onClick: (entity: any) => {
                    this.deleteVideo(entity);
                },
                icon: 'fa-trash',
                label: () => {
                    return $translate.instant('VIDEOS.DELETE');
                },
            },
        ];

        this.initialize();
    }

    initialize() {
        this.getVideos();
    }

    getVideos(): void {
          if (this.isLoading) return;
        this.isLoading = true;
        this.manufacturerProductService.getVideos()
            .then((response: IHttpResponse<any>) => this.getVideosSuccess(response))
            .catch((error: any) => this.getVideosFailed(error))
            .finally(() => {
                this.isLoading = false;
            });
    }

    getVideosSuccess(response: IHttpResponse<any>) {
        this.allVideos = response.data;
        this.videosList = this.allVideos.slice(0, 100);
        this.totalItems = this.allVideos.length;
        this.handleInfiniteScroll();
    }

    getVideosFailed(error: any) {
        this.headerBannerService.setNotification('ERROR', error.data.error, 10000);
    }

    viewVideo(video: any) {
        this.$window.open(video.url, '_blank');
    }

    createVideo() {
        this.headerBannerService.removeNotification();
        const dataObject = {};
        this.openUploadVideoModal(dataObject);
    }

    editVideo(video: any) {
        this.headerBannerService.removeNotification();
        const dataObject = {
            name: video.name,
            description: video.description,
            id: video.id,
            url: video.url,
        };
        this.openUploadVideoModal(dataObject);
    }

    openUploadVideoModal(dataObject: any) {
        this.$uibModal
            .open({
                templateUrl: 'features/products/videos/create/uploadVideo.html',
                controller: 'UploadVideoController',
                controllerAs: 'uploadVideoCtrl',
                size: 'md',
                resolve: {
                    dataObject: () => {
                        return dataObject;
                    },
                },
            })
            .result.then(
            () => {
                this.headerBannerService.setNotification('SUCCESS', this.UPLOAD_SUCCESS, 10000);
                this.getVideos();
            },
            () => {
                console.log('Modal Cancelled');
            }
        );
    }

    deleteVideo(video: any) {
        this.headerBannerService.removeNotification();
        const deleteObject = {
            name: video.name,
            id: video.id,
            url: '/video/' + video.id,
        };

        this.$uibModal
            .open({
                templateUrl: 'features/shared/commonDelete/deleteDialogBox.html',
                controller: 'DeleteController',
                controllerAs: 'deleteCtrl',
                size: 'sm',
                resolve: {
                    deleteObject: () => {
                        return deleteObject;
                    },
                },
            })
            .result.then(
            () => {
                this.headerBannerService.setNotification('SUCCESS', this.DELETE_SUCCESS, 5000);
                this.getVideos();
            },
            () => {
                console.log('Modal Cancelled');
            }
        );
    }

 handleInfiniteScroll = (): void => {
        var threshold = 250;
        var scrollTop = window.scrollY;
        var lastScrollTop = 0;

        if (scrollTop > lastScrollTop) {
            this.isFixedHeader = scrollTop > threshold;
        } else if (scrollTop < threshold) {
            this.isFixedHeader = false;
        }
        lastScrollTop = scrollTop;

        if (window.innerHeight + scrollTop >= document.body.offsetHeight - 2) {
            this.loadMoreInfiniteScroll();
        }
    }

loadMoreInfiniteScroll = (): void => {
    if (this.isLoading) return;
    this.isLoading = true;

    let nextItems = this.allVideos.slice(this.videosList.length, this.videosList.length + 100);
    if (nextItems.length === 0) {
        this.initialLoadDone = true;
        this.isLoading = false;
        return;
    }

    this.videosList = [...this.videosList, ...nextItems];
    this.isLoading = false;
    if (this.videosList.length >= (this.totalItems ?? 0)) {
        this.initialLoadDone = true;
    }
}
}

angular.module('app.products').controller('VideosController', VideosController);