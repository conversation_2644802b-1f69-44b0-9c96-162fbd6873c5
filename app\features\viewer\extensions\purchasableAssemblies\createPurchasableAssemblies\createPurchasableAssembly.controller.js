(function () {
    'use strict';

    angular
        .module('app.viewer')
        .controller('CreatePurchasableAssemblyController', CreatePurchasableAssemblyController);

    CreatePurchasableAssemblyController.$inject = ['$stateParams', '$scope', 'purchasableAssemblyService', 'viewerBannerService', '$rootScope', 'viewerHelperService', '$translate'];

    function CreatePurchasableAssemblyController($stateParams, $scope, purchasableAssemblyService, viewerBannerService, $rootScope, viewerHelperService, $translate) {
        var vm = this;

        vm.errors = {};
        vm.isOpen = false;
        vm.selectedPart = [];

        vm.selectParentPart = selectParentPart;
        vm.savePurchasableAssembly = savePurchasableAssembly;
        vm.cancel = cancel;

        var CREATE_SUCCESS;
        $translate(['CREATE_ASSEMBLY.CREATE_SUCCESS'])
            .then(function (resp) {
                CREATE_SUCCESS = resp["CREATE_ASSEMBLY.CREATE_SUCCESS"];
            });

        initialize();

        function initialize(exisitingPurchasableAssemblies) {
            vm.exisitingPurchasableAssemblies = exisitingPurchasableAssemblies;
        }

        function selectParentPart(){
            if(vm.selectedPart && vm.selectedPart.objectId){
                viewerHelperService.selectParentPart(vm.selectedPart.objectId);
            }
        }
        function savePurchasableAssembly() {
            vm.errors.noPartSelected = vm.selectedPart.partId === undefined;
            vm.errors.isPartAlreadyAnAssembly = isPartAlreadyAnAssembly();

            if (vm.errors.noPartSelected || vm.errors.isPartAlreadyAnAssembly ) {
                return;
            }

            purchasableAssemblyService.createPurchasableAssembly($stateParams.modelId, vm.selectedPart.partId)
                .then(saveEditPartModelLinkSuccess)
                .catch(saveEditPartModelLinkFailed);

        }

        function isPartAlreadyAnAssembly() {
            return !!_.findWhere(vm.exisitingPurchasableAssemblies, {objectId: vm.selectedPart.objectId});
        }

        function saveEditPartModelLinkSuccess() {
            viewerBannerService.setNotification('SUCCESS', CREATE_SUCCESS, 2000);
            cancel();
        }

        function saveEditPartModelLinkFailed(error) {
            viewerBannerService.setNotification('ERROR', error.data.error, 3000);
        }

        $scope.$on("viewer-part-selected", function (event, partViewerDetails) {
            if (partViewerDetails.length === 1) {
                vm.selectedPart = partViewerDetails[0].part;
                vm.errors.isPartAlreadyAnAssembly = isPartAlreadyAnAssembly();
            } else if (partViewerDetails.length > 1) {
                vm.selectedPart = partViewerDetails;
            } else {
                vm.selectedPart = [];
                vm.errors.isPartAlreadyAnAssembly = false;
            }
        });

        $scope.$on("create-purchasable-assembly-opened", function (event, exisitingPurchasableAssemblies) {
            vm.isOpen = true;
            initialize(exisitingPurchasableAssemblies);
        });

        function cancel() {
            vm.isOpen = false;
            $rootScope.$broadcast("create-purchasable-assembly-closed");
        }

    }

})();
