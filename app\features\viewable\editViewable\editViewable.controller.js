(function () {
    'use strict';

    angular
        .module('app.viewable')
        .controller('EditViewableController', EditViewableController);

    EditViewableController.$inject = ['modelService',  '$uibModalInstance','model'];
    function EditViewableController(modelService, $uibModalInstance,model) {

        var vm = this;
        vm.editModel = editModel;
        vm.cancel = $uibModalInstance.dismiss;
        vm.modelName = model.modelName;
        vm.modelId = model.modelId;

        initialize();
        function initialize() {
        }
        
        function editModel() {
        	modelService.editModel(vm.modelName,vm.modelId)
                .then(editModelSuccess, editModelFailure);
        }

        function editModelSuccess() {
            $uibModalInstance.close();
        }

        function editModelFailure(error) {
            console.log(error);
        }


       }
})();
