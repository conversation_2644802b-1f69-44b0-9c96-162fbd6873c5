(function () {
    'use strict';

    angular
        .module('app.viewer')
        .controller('PurchasableAssemblySummaryController', PurchasableAssemblySummaryController);

    PurchasableAssemblySummaryController.$inject = ['purchasableAssemblyService', 'viewerBannerService', '$stateParams', '$uibModal', '$rootScope', '$scope'];

    function PurchasableAssemblySummaryController(purchasableAssemblyService, viewerBannerService, $stateParams, $uibModal, $rootScope, $scope) {
        var vm = this;

        vm.isOpen = true;
        vm.existingPurchasableAssemblies = {};
        vm.createPurchasableAssembly = createPurchasableAssembly;
        vm.deletePurchasableAssembly = deletePurchasableAssembly;

        initialize();

        function initialize() {
            vm.modelId = $stateParams.modelId;
            fetchPurchasableAssemblies();
        }

        function fetchPurchasableAssemblies(){
            purchasableAssemblyService.fetchPurchasableAssemblysForModel(vm.modelId)
                 .then(fetchPurchasableAssembliesForModelSuccess, fetchPurchasableAssembliesForModelFailed);
        }

        function fetchPurchasableAssembliesForModelSuccess(response) {
            vm.existingPurchasableAssemblies = response.data;
        }

        function fetchPurchasableAssembliesForModelFailed(error) {
            viewerBannerService.setNotification("ERROR", error);
        }

        function createPurchasableAssembly() {
            vm.isOpen = false;
            $rootScope.$broadcast("create-purchasable-assembly-opened", vm.existingPurchasableAssemblies);
        }

        function deletePurchasableAssembly(purchasableAssembly) {
            vm.successMessage = "";
            var deleteObject = {
                name: "Purchasable Assembly for part " + purchasableAssembly.partNumber,
                id: purchasableAssembly.id,
                url: '/modelAssembly/' + purchasableAssembly.id
            };

            $uibModal.open({
                templateUrl: 'features/shared/commonDelete/deleteDialogBox.html',
                controller: 'DeleteController',
                controllerAs: 'deleteCtrl',
                size: 'sm',
                resolve: {
                    deleteObject: function () {
                        return deleteObject;
                    }
                }
            }).result.then(function () {
                fetchPurchasableAssemblies();
            });
        }

        $scope.$on("create-purchasable-assembly-closed", function () {
            vm.isOpen = true;
            initialize();
        });
    }

})();
