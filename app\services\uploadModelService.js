(function () {
    'use strict';

    angular
        .module('app.services')
        .factory('uploadModelService', uploadModelService);

    uploadModelService.$inject = [];

    function uploadModelService() {
        return {
        	getZipFolderContents : getZipFolderContents
        };
        
        function getZipFolderContents(file, getPRTFiles, successFunction) {
            var new_zip = new JSZip();
            var newList = [];
            new_zip.loadAsync(file)
                .then(function (zip) {
                    angular.forEach(zip, function (value, key) {
                        //var reg = /\.(iam|sldasm|asm)$/i; - remove end of file regex to allow for asm.1
                        var reg = getPRTFiles ? /\.(iam|sldasm|asm|prt)/i : /\.(iam|sldasm|asm)/i;
                        if(reg.test(value))
                        {
                            var indexOfSlash = value.lastIndexOf("/");
                            if (indexOfSlash == -1) {
                                newList.push({'name': value});
                            } else {
                                newList.push({'name': value.substring(indexOfSlash + 1)})
                            }
                        }
                    });
                    return successFunction(newList);
                });
            
        }
        
    }
})();
