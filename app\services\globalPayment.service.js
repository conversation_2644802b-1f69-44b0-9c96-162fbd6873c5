(function () {
  'use strict';

  angular
    .module('app.services')
    .factory('globalPaymentService', globalPaymentService);

  globalPaymentService.$inject = ['$http', 'apiConstants', 'ordersService', '$location', 'shippingEngineService'];

  function globalPaymentService($http, apiConstants, ordersService, $location, shippingEngineService) {
    var orderDetail = null;

    return {
      getGlobalPaymentConfig: getGlobalPaymentConfig,
      setCurrentOrderDetail: setCurrentOrderDetail,
      getCurrentOrderDetail: getCurrentOrderDetail,
      createOrder: createOrder
    };


    function setCurrentOrderDetail(order) {
      orderDetail = order;
    }

    function getCurrentOrderDetail() {
      return orderDetail;
    }

    function getGlobalPaymentConfig(order, orderExtraObj) {
      const orderMapped = ordersService.mapOrderToPayload(order) || null;
      if (!orderMapped || !orderMapped.orderItems.length) return;
      
      shippingEngineService.splitWarehouseItems(orderMapped);

      const currentWarehouse = shippingEngineService.getCurrentWarehouse();

      var config = { params: { userId: order.userId } };
      orderMapped.price = order.price;
      orderMapped.orderExtraData = orderExtraObj;
      const payload = {
        order: orderMapped,
        wareHouseId: currentWarehouse.id
      }

      return $http.post(apiConstants.url + '/payment/hpp-config', payload, config);
    }

    function createOrder(data, hppReponse) {
      const orderPayload = ordersService.mapOrderToPayload(data.order) || null;
      if (!orderPayload || !orderPayload.orderItems.length) return;

      shippingEngineService.splitWarehouseItems(orderPayload);
      var siteUrl = $location.protocol() + '://' + $location.host();
      var config = {
        headers: { 'Site-Url': siteUrl },
        params: { userId: data.order.userId }
      };

      data.associatedOrderId && (orderPayload.associatedOrderId = data.associatedOrderId);
      
      const taxInfo = data.taxInfo;
      orderPayload.price = data.order.price;
      orderPayload.orderExtraData = {
        shipEngineShipmentId: data.shippingCarrier && data.shippingCarrier.shipment_id,
        avalaraTransactionCode: taxInfo && taxInfo.code,
        avalaraCompanyId: taxInfo && taxInfo.companyId, 
        taxAmount: taxInfo && taxInfo.totalTax,
        shipEngineCarrierCode: data.shippingCarrier && data.shippingCarrier.carrier_code,
        shipEngineServiceCode: data.shippingCarrier && data.shippingCarrier.service_code,
        shippingCost: data.shippingCarrier && data.shippingCarrier.shipping_amount && data.shippingCarrier.shipping_amount.amount
      };
      
      const payload = {
        order: orderPayload,
        rateId: data.shippingCarrier.rate_id,
        globalPaymentResponse: JSON.stringify(hppReponse),
        wareHouseId: data.wareHouseId
      }
      return $http.post(apiConstants.url + '/payment/create-order', payload, config);
    }
  }
})();
