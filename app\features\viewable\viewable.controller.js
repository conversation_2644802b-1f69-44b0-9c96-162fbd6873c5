(function () {
    "use strict";

    angular.module("app.viewable").controller("ManufacturerViewableController", ManufacturerViewableController);

    ManufacturerViewableController.$inject = [
        "$window",
        "$transitions",
        "viewableService",
        "$stateParams",
        "$uibModal",
        "$interval",
        "$scope",
        "$state",
        "$filter",
        "modelService",
        "manufacturerProgressService",
        "userService",
    ];

    function ManufacturerViewableController(
        $window,
        $transitions,
        viewableService,
        $stateParams,
        $uibModal,
        $interval,
        $scope,
        $state,
        $filter,
        modelService,
        manufacturerProgressService,
        userService
    ) {
        var vm = this;
        var dropdownStateKey = "publicationsDropdownState";

        vm.machineName = $stateParams.machineName;

        vm.endRecord = vm.itemPerPage;
        vm.sortReverse = false;
        vm.isModelsLoaded = false;
        vm.isHeaderListLoaded = false;
        vm.missingTranslationWarnings = [];
        vm.isOpened = false;
        vm.loadingInfiniteScrollData = false;
        vm.isOrdersLoaded = false;
        vm.showBackToTopButton = false;

        vm.openAdminViewerPage = openAdminViewerPage;
        vm.editModelPopup = editModelPopup;
        vm.openDeletePopup = openDeletePopup;
        vm.openClonePopup = openClonePopup;
        vm.createManualPopUp = createManualPopUp;
        vm.goToProducts = goToProducts;
        vm.showAutodeskWarnings = showAutodeskWarnings;
        vm.exportPartDetailsToCSV = exportPartDetailsToCSV;
        vm.createUploadCsvDetailsPopUp = createUploadCsvDetailsPopUp;
        vm.csvUploadSparePartDataPopUp = csvUploadSparePartDataPopUp;
        vm.manageSoftCopies = manageSoftCopies;
        vm.manageWorkInstructions = manageWorkInstructions;
        vm.downloadCSV = downloadCSV;
        vm.scrollToTop = scrollToTop;
        vm.loadMoreInfiniteScroll = loadMoreInfiniteScroll;
        vm.remove = remove;

        vm.openUploadModal = openUploadModal;
        vm.upload = upload;

        vm.displayAdvancedFilter = false;
        vm.filterValue = {};

        vm.createdByUsers = [""];
        vm.filter_createdBy = "";
        vm.statuses = [""];
        vm.filter_status = "";

        vm.toggleAdvancedFilter = toggleAdvancedFilter;
        vm.searchFilterChange = searchFilterChange;
        vm.applyFilter = applyFilter;
        vm.clearFilter = clearFilter;

        vm.sortReverse = true;
        vm.isFixedHeader = false;
        vm.viewable_sort = "modelId";
        vm.softCopyEnabled = userService.getSoftCopyEnabled();

        //Manufacturer Progress Timeout for loop
        var timeout;

        initialize();

        function initialize() {
        
            vm.filter_createdBy = localStorage.getItem("filter_createdBy") || vm.createdByUsers[0];
            vm.filter_status = localStorage.getItem("filter_status") || vm.statuses[0];
            fetchViewables();

            var savedDropdownState = localStorage.getItem(dropdownStateKey);
    
            if (savedDropdownState === 'open' || vm.filter_createdBy || vm.filter_status) {
                vm.displayAdvancedFilter = true;
            } else {
                vm.displayAdvancedFilter = false;
            }

            $transitions.onStart({}, (transition) => {
                var fromStateUrl = transition.from().url.toString();
                var toStateUrl = transition.to().url.toString();

                var viewablesRoutes = ["/products/", "/productsCatalogue", "/techDocs", "/videos", "/manufacturerViewer/", "/adminViewer/"];

                var isFromViewables = viewablesRoutes.some((route) => fromStateUrl.includes(route));
                var isToViewables = viewablesRoutes.some((route) => toStateUrl.includes(route));

                if (isFromViewables && isToViewables) {
                    return;
                } else {
                    vm.searchValue = "";
                }
            });
        }

        function goToProducts() {
            $state.go("products.catalogue");
        }

        function openUploadModal(product) {
            var productId = $stateParams.productId;
            $uibModal
                .open({
                    templateUrl: "features/viewable/uploadViewable/uploadViewable.html",
                    controller: "UploadViewableController",
                    controllerAs: "uploadCtrl",
                    size: "md",
                    resolve: {
                        productId: function () {
                            return productId;
                        },
                    },
                })
                .result.then(
                    function () {
                        fetchViewables();
                    },
                    function () {
                        console.log("Modal Cancelled");
                    }
                );
        }

        function fetchViewables() {
            vm.loadingInfiniteScrollData = true;
            var machineId = $stateParams.productId;
            modelService.fetchModels(machineId).then(fetchModelSuccess).catch(fetchModelFailed);
            vm.isOpened = false;
        }

        function fetchModelSuccess(response) {
            vm.allModels = response.data;
            vm.modelList = vm.allModels.slice(0, 100);  
            vm.totalItems = vm.allModels.length;
            vm.isModelsLoaded = true;
            vm.loadingInfiniteScrollData = false;
            handleInfiniteScroll();

            for (var n = 0; n < vm.totalItems; n++) {
                var createdBy = vm.modelList[n].createdByUserFirstName;
                var status = vm.modelList[n].autodeskStatusDisplay;

                if (!vm.createdByUsers.includes(createdBy)) {
                    vm.createdByUsers.push(createdBy);
                }
                if (!vm.statuses.includes(status)) {
                    vm.statuses.push(status);
                }
            }

            checkForInProgressBOM();
        }

        function fetchModelFailed(error) {
            console.log(error);
            vm.isModelsLoaded = true;
        }

        function openAdminViewerPage(model) {
            if (model.is2d) {
                $state.go("pdfViewer", {
                    productId: $stateParams.productId,
                    autodeskURN: model.autodeskUrn,
                    modelId: model.modelId,
                    machineName: vm.machineName,
                    viewableName: model.modelName,
                });
            } else {
                $state.go("manufacturerViewer", {
                    productId: $stateParams.productId,
                    autodeskURN: model.autodeskUrn,
                    modelId: model.modelId,
                    machineName: vm.machineName,
                    viewableName: model.modelName,
                    translateType: model.translateType,
                });
            }
        }

        function manageSoftCopies(model) {
            $state.go("softCopy", {
                productId: $stateParams.productId,
                machineName: vm.machineName,
                modelId: model.modelId,
                modelName: model.modelName,
            });
        }

        function manageWorkInstructions(model) {
            $state.go("workInstructions", {
                productId: $stateParams.productId,
                machineName: vm.machineName,
                modelId: model.modelId,
                modelName: model.modelName,
            });
        }

        function editModelPopup(model) {
            $uibModal
                .open({
                    templateUrl: "features/viewable/editViewable/editViewable.html",
                    controller: "EditViewableController",
                    controllerAs: "editViewableCtrl",
                    size: "md",
                    resolve: {
                        model: function () {
                            return model;
                        },
                    },
                })
                .result.then(
                    function () {
                        fetchViewables();
                    },
                    function () {
                        console.log("Modal Cancelled");
                    }
                );
        }

        function openDeletePopup(model) {
            var deleteObject = {
                name: model.modelName,
                id: model.modelId,
                url: "/model/" + model.modelId,
                linkedPartsCount: model.linkedPartCount,
            };
            $uibModal
                .open({
                    templateUrl: "features/shared/commonDelete/deleteDialogBox.html",
                    controller: "DeleteController",
                    controllerAs: "deleteCtrl",
                    size: "sm",
                    resolve: {
                        deleteObject: function () {
                            return deleteObject;
                        },
                    },
                })
                .result.then(
                    function () {
                        fetchViewables();
                    },
                    function () {
                        console.log("Modal Cancelled");
                    }
                );
        }

        function openClonePopup(model) {
            $uibModal
                .open({
                    templateUrl: "features/viewable/cloneModal/cloneModel.html",
                    controller: "CloneModelController",
                    controllerAs: "cloneModelCtrl",
                    size: "md",
                    resolve: {
                        model: function () {
                            return model;
                        },
                    },
                })
                .result.then(
                    function () {
                        fetchViewables();
                    },
                    function () {
                        console.log("Modal Cancelled");
                    }
                );
        }

        function upload(obj) {
            var elem = obj.target || obj.srcElement;
            if (elem.files.length > 0) {
                vm.file = elem.files[0];
            }
        }

        function createManualPopUp(model) {
            model.rangeId = $stateParams.rangeId;
            $uibModal.open(
                {
                    templateUrl: "features/publications/create/createManual.html",
                    controller: "CreateManualController",
                    controllerAs: "createManualCtrl",
                    size: "md",
                    resolve: {
                        modalObject: function () {
                            return model;
                        },
                    },
                },
                function () {
                    console.log("Modal Cancelled");
                }
            );
        }

        function showAutodeskWarnings(model) {
            $uibModal.open({
                templateUrl: "features/viewable/translationWarnings/modelWarnings.html",
                controller: "ModelTranslationController",
                controllerAs: "modelTranslationCtrl",
                size: "md",
                resolve: {
                    modalObject: function () {
                        return model;
                    },
                },
            });
        }

        function exportPartDetailsToCSV(model) {
            vm.bomProcessing = true;
            modelService.exportPartDetailsToCSV(model.modelId).then(checkForInProgressBOM, exportPartDetailsToCSVFailed);
        }

        function checkForInProgressBOM() {
            manufacturerProgressService
                .fetchViewableBOMProgress($stateParams.productId, 0)
                .then(bomProgressSuccess, exportPartDetailsToCSVFailed);
        }

        function bomProgressSuccess(response) {
            if (response.data && response.data.length > 0) {
                vm.bomProcesses = response.data;

                //Check all found records are complete
                var processComplete = true;
                for (var i = 0; i < vm.bomProcesses.length; i++) {
                    if (vm.bomProcesses[i].status != "COMPLETE") {
                        processComplete = false;
                        break;
                    } else {
                        processComplete = true;
                    }
                }

                viewableService.setIsCsvProcessing(!processComplete);

                var bomProcesses = JSON.parse(JSON.stringify(vm.bomProcesses));
                populateViewablesInProgressProcesses(bomProcesses);

                if (processComplete) {
                    //IF all completed
                    //Stop polling to prevent spamming services
                    stopPollingBomUpdates();
                } else {
                    //IF NOT all completed
                    //Start timer to poll for updates every 10 seconds
                    beginPollingBomUpdates();
                }
            }
        }

        function populateViewablesInProgressProcesses(bomProcesses) {
            //Loop each model and get it's related processes
            for (var n = 0; n < vm.modelList.length; n++) {
                var processArray = [];
                //Loop each process and if it matches id of current viewable add it to that viewables list of processes
                for (var i = 0; i < bomProcesses.length; i++) {
                    if (vm.modelList[n].modelId == bomProcesses[i].modelId) {
                        if (bomProcesses[i].process === "VIEWABLE_BOM_EXPORT") {
                            bomProcesses[i].processDisplayName = "BOM Export";
                        } else if (bomProcesses[i].process === "VIEWABLE_BOM_UPLOAD") {
                            bomProcesses[i].processDisplayName = "BOM Upload";
                        } else {
                            bomProcesses[i].processDisplayName = "Spare Part Upload";
                        }
                        processArray.push(bomProcesses[i]);
                        bomProcesses.splice(i, 1);
                        i--;
                    }
                }
                vm.modelList[n].proccesses = processArray;
            }
        }

        function stopPollingBomUpdates() {
            clearTimeout(timeout);
        }

        function beginPollingBomUpdates() {
            timeout = setTimeout(checkForInProgressBOM, 15000);
        }


        function remove(progressId) {
            manufacturerProgressService.remove(progressId);
            fetchViewables();
        }

        function downloadCSV(model, process) {
            var filename = model.modelName + "_partExport.csv";
            downloadCSVFileContents(process.s3Url, filename);
        }

        function downloadCSVFileContents(s3Url, filename) {
            var csvResponse = "";
            var xhr = new XMLHttpRequest();
            xhr.open("GET", s3Url);
            xhr.onload = function () {
                if (xhr.status == 200) {
                    csvResponse = xhr.responseText;
                    exportToCSV(csvResponse, filename);
                }
            };
            xhr.send();
        }

        function exportToCSV(content, filename) {
            var blob = new Blob([content], { type: "text/csv;charset=utf-8;" });
            if (navigator.msSaveBlob) {
                // IE 10+
                navigator.msSaveBlob(blob, filename);
            } else {
                var link = document.createElement("a");
                if (link.download !== undefined) {
                    // feature detection
                    // Browsers that support HTML5 download attribute
                    var url = URL.createObjectURL(blob);
                    link.setAttribute("href", url);
                    link.setAttribute("download", filename);
                    link.style.visibility = "hidden";
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                }
            }
        }

        function exportPartDetailsToCSVFailed(error) {
            console.log(error);
        }

        function createUploadCsvDetailsPopUp(model) {
            vm.model = model;

            $uibModal
                .open({
                    templateUrl: "features/viewable/uploadPartData/uploadPartDataModal.html",
                    controller: "UploadPartDataController",
                    controllerAs: "uploadPartDataCtrl",
                    size: "md",
                    backdrop: "static",
                    resolve: {
                        model: function () {
                            return model;
                        },
                    },
                })
                .result.then(
                    function () {
                        fetchViewables();
                    },
                    function () {
                        console.log("Modal Cancelled");
                    }
                );
        }

        function csvUploadSparePartDataPopUp(model) {
            vm.model = model;

            $uibModal
                .open({
                    templateUrl: "features/viewable/uploadPartData/uploadSparePartDataModal.html",
                    controller: "UploadSparePartDataController",
                    controllerAs: "uploadSparePartDataCtrl",
                    size: "md",
                    backdrop: "static",
                    resolve: {
                        model: function () {
                            return model;
                        },
                    },
                })
                .result.then(
                    function () {
                        fetchViewables();
                    },
                    function () {
                        console.log("Modal Cancelled");
                    }
                );
        }

        var promise;
        promise = $interval(function () {
            fetchViewables();
        }, 60000);

        $scope.$on("$destroy", function () {
            $interval.cancel(promise);
        });

        function toggleAdvancedFilter() {
            vm.displayAdvancedFilter = !vm.displayAdvancedFilter;

            if (!vm.displayAdvancedFilter) {
                clearClosedFilter();
            }

            $window.localStorage.setItem(dropdownStateKey, vm.displayAdvancedFilter ? 'open' : 'closed');
        }

        function searchFilterChange() {
            updateTotalItemCount();
        }

        function updateTotalItemCount() {
            var textFilter = $filter("filter")(vm.modelList, vm.searchValue);
            vm.totalItems = $filter("filter")(textFilter, vm.filterValue, true).length;
        }

        function applyFilter() {
            vm.filterValue = {};

            if (vm.filter_createdBy) {
                vm.filterValue.createdByUserFirstName = vm.filter_createdBy;
                localStorage.setItem("filter_createdBy", vm.filter_createdBy);
            }
            if (vm.filter_status) {
                vm.filterValue.autodeskStatusDisplay = vm.filter_status;
                localStorage.setItem("filter_status", vm.filter_status);
            }

            updateTotalItemCount();
            vm.isFixedHeader = false;
        }

        function clearFilter() {
            vm.filterValue = {};
            vm.totalItems = vm.modelList.length;

            vm.filter_createdBy = vm.createdByUsers[0];
            vm.filter_status = vm.statuses[0];

            var searchBox = document.getElementById("searchInput");
            if (searchBox) {
                searchBox.value = "";
            }
            vm.searchValue = "";

            vm.displayAdvancedFilter = false;

            localStorage.removeItem(dropdownStateKey);
            localStorage.removeItem("filter_createdBy");
            localStorage.removeItem("filter_status");
            sessionStorage.removeItem("searchValue-'viewables'");
        }

        function clearClosedFilter() {
            vm.filterValue = {};
            vm.totalItems = vm.modelList.length;

            vm.filter_createdBy = vm.createdByUsers[0];
            vm.filter_status = vm.statuses[0];

            vm.displayAdvancedFilter = false;

            localStorage.removeItem(dropdownStateKey);
            localStorage.removeItem("filter_createdBy");
            localStorage.removeItem("filter_status");
            sessionStorage.removeItem("searchValue-'viewables'");
        }

           var lastScrollTop = 0;
window.addEventListener('scroll', handleInfiniteScroll);

function handleInfiniteScroll() {
    var threshold = 250;
    var scrollTop = window.scrollY;

    if (scrollTop > lastScrollTop) {
        vm.isFixedHeader = scrollTop > threshold;
    } else if (scrollTop < threshold){
        vm.isFixedHeader = false;
    }
    lastScrollTop = scrollTop;  

    
    if (window.innerHeight + scrollTop >= document.body.offsetHeight - 2 && !vm.loadingInfiniteScrollData && !vm.initialLoadDone) {
        loadMoreInfiniteScroll();
    }
}

        function loadMoreInfiniteScroll() {
            vm.loadingInfiniteScrollData = true;

            var nextItems = vm.allModels.slice(vm.modelList.length, vm.modelList.length + 100);
            if (nextItems.length === 0) {
                vm.initialLoadDone = true;
                vm.loadingInfiniteScrollData = false;
                return;
            }

            vm.modelList = vm.modelList.concat(nextItems);

            vm.loadingInfiniteScrollData = false;
            if (vm.modelList.length >= vm.totalItems) {
                vm.initialLoadDone = true;
            }
        }

function scrollToTop() {
      $window.scrollTo({ top: 0, behavior: "smooth" });
      $("html, body").animate({ scrollTop: 0 }, "slow", function () {
        $("#scrollToTop").removeClass("scrolled-past");
      });
    }

    angular.element($window).on("scroll", function () {
      vm.showBackToTopButton = this.pageYOffset > 100;
      $scope.$apply();
    });

    }
})();
