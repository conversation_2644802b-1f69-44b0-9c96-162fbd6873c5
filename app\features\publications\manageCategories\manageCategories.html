<div class="modal-header">
    <h2 class="modal-title" translate>CREATE_PUBLICATION.MANAGE_PUBLICATION_CATEGORIES</h2>
    <button type="button" class="close" aria-label="Close" ng-click="manageCategoriesCtrl.cancel()">
        <span aria-hidden="true">&times;</span>
    </button>
</div>

<div class="modal-body manage-categories-modal-body">
    <!-- Error Message Display -->
    <div class="mb-4 error-well" ng-if="manageCategoriesCtrl.errorMessage">
        <p class="mb-0">{{manageCategoriesCtrl.errorMessage}}</p>
    </div>

    <!-- Existing Categories Section -->
    <div class="existing-categories-section mb-4">
        <h4 translate>CREATE_PUBLICATION.EXISTING_CATEGORIES</h4>
        <div class="category-list list-group scrollable-list">
            <div 
                class="list-group-item d-flex justify-content-between align-items-center category-item"
                ng-class="{'category-item-new': category.isNew}"
                ng-repeat="category in manageCategoriesCtrl.existingCategories track by category.id"
            >
                <!-- View Mode -->
                <div ng-if="!category.editing" class="d-flex justify-content-between align-items-center w-100">
                    <span>{{ category.name }}</span>
                    <div class="category-actions ml-2 text-nowrap cadGap">
                        <button class="btn btn-sm btn-outline-primary"
                                ng-click="manageCategoriesCtrl.startCategoryEdit(category)"
                                ng-disabled="category.editing"
                                aria-label="Edit {{category.name}}">
                            Edit
                        </button>
                        <button class="btn btn-sm btn-outline-danger"
                                ng-click="manageCategoriesCtrl.deleteCategory(category)"
                                ng-disabled="category.editing" 
                                aria-label="Delete {{category.name}}">
                            Delete
                        </button>
                    </div>
                </div>

                <!-- Edit Mode -->
                <div ng-if="category.editing" class="d-flex justify-content-between align-items-center w-100 edit-category-form">
                    <input type="text" class="form-control form-control-sm mr-2" 
                           ng-model="category.editName" 
                           required />
                    <div class="category-actions ml-2 text-nowrap">
                        <button class="btn btn-sm btn-primary cadGap"
                                ng-click="manageCategoriesCtrl.saveCategoryEdit(category)"
                                ng-disabled="!category.editName || category.editName === category.name"
                                aria-label="Save changes for {{category.name}}">
                            Save
                        </button>
                        <button class="btn btn-sm btn-secondary"
                                ng-click="manageCategoriesCtrl.cancelCategoryEdit(category)"
                                aria-label="Cancel editing {{category.name}}">
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
            <div ng-if="manageCategoriesCtrl.existingCategories.length === 0" class="list-group-item text-muted" translate>
                CREATE_PUBLICATION.NO_CATEGORIES_DEFINED
            </div>
        </div>
    </div>

    <!-- Add New Category Section -->
    <div class="add-category-section">
        <h4 translate>CREATE_PUBLICATION.ADD_NEW_CATEGORY</h4>
        <div class="form-group d-flex flex-wrap justify-content-end">
            <input 
                type="text" 
                class="form-control" 
                placeholder="{{'CREATE_PUBLICATION.ENTER_CATEGORY_NAME_PLACEHOLDER' | translate}}"
                ng-model="manageCategoriesCtrl.newCategoryName"
            />
            <button 
                type="button" 
                class="btn primary mt-3 text-nowrap" 
                ng-click="manageCategoriesCtrl.addCategory()"
                ng-disabled="!manageCategoriesCtrl.newCategoryName"
                translate>CREATE_PUBLICATION.ADD_CATEGORY
            </button>
        </div>
    </div>
</div>

<div class="modal-footer">
    <button type="button" class="btn btn-secondary" ng-click="manageCategoriesCtrl.cancel()" translate>CREATE_PUBLICATION.CLOSE</button>
</div>
