(function () {
    'use strict';

    angular
        .module('app.customer')
        .controller('AssignCategoriesController', AssignCategoriesController);

    AssignCategoriesController.$inject = ['assignCategoriesService', 'userService', '$stateParams', 'headerBannerService', '$translate', 'filterFilter'];

    function AssignCategoriesController(assignCategoriesService, userService, $stateParams, headerBannerService, $translate, filterFilter) {
        var vm = this;

        var CHANGES_SAVED, CHANGES_CANCELED, CHANGES_SAVED_FAILED;
        $translate(['ASSIGN_CATEGORIES.CHANGES_SAVED', 'ASSIGN_CATEGORIES.CHANGES_CANCELED', 'GENERAL.WENT_WRONG'])
            .then(function (resp) {
                CHANGES_SAVED = resp["ASSIGN_CATEGORIES.CHANGES_SAVED"];
                CHANGES_CANCELED = resp["ASSIGN_CATEGORIES.CHANGES_CANCELED"];
                CHANGES_SAVED_FAILED = resp["GENERAL.WENT_WRONG"];
            });

        vm.moveSelectedFromAvailableToAssigned = moveSelectedFromAvailableToAssigned;
        vm.moveSelectedFromAssignedToAvailable = moveSelectedFromAssignedToAvailable;
        vm.toggleSelectAllAvailable = toggleSelectAllAvailable;
        vm.toggleSelectAllAssigned = toggleSelectAllAssigned;
        vm.updateAllAvailableCheckbox = updateAllAvailableCheckbox;
        vm.updateAllAssignedCheckbox = updateAllAssignedCheckbox;
        vm.clearAssignedSearch = clearAssignedSearch;
        vm.clearAvailableSearch = clearAvailableSearch;
        vm.save = save;
        vm.cancel = cancel;

        initialize();

        function initialize() {
            vm.availableCategories = [];
            vm.assignedCategories = [];
            vm.areAllAssignedSelected = false;
            vm.areAllAvailableSelected = false;
            getAllCategories();
            vm.customerName = $stateParams.name;
        }

        function getAllCategories() {
            var manufacturerId = userService.getManufacturerId()
            assignCategoriesService.getCategoriesForManufacturer(manufacturerId)
                .then(getCategoriesForManufacturerSuccess);
        }

        function getCategoriesForManufacturerSuccess(response) {
            vm.availableCategories = response.data.publicationCategories || [];
            getAssignedCategoryIdsForPurchaser();
        }

        function getAssignedCategoryIdsForPurchaser() {
            var purchaserId = $stateParams.subEntityId;
            assignCategoriesService.getAssignedCategoryIdsForPurchaser(purchaserId)
                .then(getAssignedCategoryIdsForPurchaserSuccess);
        }

        function getAssignedCategoryIdsForPurchaserSuccess(response) {
            var categoryIds = response.data;

            for (var i = 0; i < categoryIds.length; i++) {
                var categoryId = categoryIds[i];
                moveBetweenListsById(categoryId, vm.availableCategories, vm.assignedCategories);
            }
        }

        function getSelectedcategoryIdsFromList(list, filter) {
            var selectedcategoryIds = [];
            var filterList = filterFilter(list, filter);
            for (var i = 0; i < filterList.length; i++) {
                if (filterList[i].selected === true) {
                    selectedcategoryIds.push(filterList[i].id);
                }
            }
            return selectedcategoryIds;
        }

        function toggleSelectAllAvailable() {
            if(vm.areAllAvailableSelected){
                deselectAll(vm.availableCategories)
            }else{
                selectAll(vm.availableCategories)
            }
        }

        function toggleSelectAllAssigned() {
            if(vm.areAllAssignedSelected){
                deselectAll(vm.assignedCategories)
            }else{
                selectAll(vm.assignedCategories)
            }
        }

        function selectAll(list) {
            for (var i = 0; i < list.length; i++) {
                list[i].selected = true;
            }
        }

        function deselectAll(list) {
            for (var i = 0; i < list.length; i++) {
                list[i].selected = false;
            }
        }

        function updateAllAvailableCheckbox(){
            for (var i = 0; i < vm.availableCategories.length; i++) {
                if(vm.availableCategories[i].selected !== true){
                    vm.areAllAvailableSelected = false;
                    return;
                }
            }
            vm.areAllAvailableSelected = true;
        }

        function updateAllAssignedCheckbox(){
            for (var i = 0; i < vm.assignedCategories.length; i++) {
                if(vm.assignedCategories[i].selected !== true){
                    vm.areAllAssignedSelected = false;
                    return;
                }
            }
            vm.areAllAssignedSelected = true;
        }

        function getIndexOfRangeInList(categoryId, list) {
            var myRange = _.findWhere(list, {id: categoryId});
            return list.indexOf(myRange);
        }

        function moveSelectedFromAvailableToAssigned() {
            var selectedcategoryIds = getSelectedcategoryIdsFromList(vm.availableCategories, vm.availableSearchValue);
            for (var i = 0; i < selectedcategoryIds.length; i++) {
                var categoryId = selectedcategoryIds[i];
                moveBetweenListsById(categoryId, vm.availableCategories, vm.assignedCategories);
                updateButtonAndCheckboxes();
            }
        }

        function moveBetweenListsById(categoryId, startList, endList) {
            var index = getIndexOfRangeInList(categoryId, startList);
            var rangeToMove = startList[index];
            startList.splice(index, 1);
            rangeToMove.selected = false;
            endList.push(rangeToMove);
        }

        function moveSelectedFromAssignedToAvailable() {
            var selectedcategoryIds = getSelectedcategoryIdsFromList(vm.assignedCategories, vm.assignedSearchValue);
            for (var i = 0; i < selectedcategoryIds.length; i++) {
                var categoryId = selectedcategoryIds[i];
                moveBetweenListsById(categoryId, vm.assignedCategories, vm.availableCategories);
                updateButtonAndCheckboxes();
            }
        }

        function updateButtonAndCheckboxes(){
            vm.isPageEdited = true;
            vm.areAllAssignedSelected = false;
            vm.areAllAvailableSelected = false;
        }

        function save() {
            var assignedcategoryIds = [];
            for (var i = 0; i < vm.assignedCategories.length; i++) {
                assignedcategoryIds.push(vm.assignedCategories[i].id)
            }

            var manufacturerId = userService.getManufacturerId();
            var purchaserId = $stateParams.subEntityId;
            
            assignCategoriesService.assignCategoriesToPurchaser(manufacturerId, purchaserId, assignedcategoryIds)
                .then(assignSuccess, assignFailure)
        }

        function assignSuccess(response){
            console.log('Categories assigned successfully:', response);
            vm.isPageEdited = false;
            headerBannerService.setNotification('SUCCESS', CHANGES_SAVED, 5000);
        }

        function assignFailure(error){
            console.error('Failed to assign categories:', error);
            headerBannerService.setNotification('ERROR', CHANGES_SAVED_FAILED, 5000);
        }

        function cancel() {
            initialize();
            headerBannerService.setNotification('INFO', CHANGES_CANCELED, 5000);
        }

        function clearAvailableSearch(){
            vm.availableSearchValue = "";
        }

        function clearAssignedSearch(){
            vm.assignedSearchValue = "";
        }
    }
})();
