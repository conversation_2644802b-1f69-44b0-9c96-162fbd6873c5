(function () {
    'use strict';

    angular
        .module('app.customer')
        .controller('CreateCustomerCompanyController', CreateCustomerCompanyController);

    CreateCustomerCompanyController.$inject = ['createUserService', '$uibModalInstance', 'userService', 'priceListService', 'warehouses'];

    function CreateCustomerCompanyController(createUserService, $uibModalInstance, userService, priceListService, warehouses) {
        var vm = this;

        vm.cancel = $uibModalInstance.dismiss;

        vm.warehouses = warehouses;
        vm.createNewCompany = createNewCompany;
        vm.isPriceListEnabled = userService.getPriceListsEnabled();
        vm.isSupreme = userService.isSupreme();
        if (vm.isSupreme) {
            vm.type = "Dealer"
        }

        initialize();

        function initialize(){
            if(vm.isPriceListEnabled){
                priceListService.getPriceLists()
                    .then(getPriceListsSuccess, getPriceListFailed)
            }
        }

        function getPriceListsSuccess(resp){
            vm.priceLists = resp.data;
        }

        function getPriceListFailed(response){
            vm.isDisabled = false;
            vm.internalFailureMessage = response.data.message;
        }

        function createNewCompany() {
            vm.isDisabled = true;
            const warehouseId = vm.warehouseSelected ? vm.warehouseSelected.id : null;

            const priceListId = (vm.priceListId === "none") ? null : vm.priceListId;

            if (vm.type === "Dealer") {
                createUserService.createManufacturerSubEntityDealer(vm.newCompanyName, vm.defaultDiscount, priceListId, vm.visCustomerCode, warehouseId)
                    .then(createCustomerSuccess, createCustomerFailed);
            } else {
                createUserService.createManufacturerSubEntityCustomer(vm.newCompanyName, vm.defaultDiscount, priceListId)
                    .then(createCustomerSuccess, createCustomerFailed);
            }
        }

        function createCustomerSuccess() {
            $uibModalInstance.close();
        }

        function createCustomerFailed(response) {
            vm.companyFailure = true;
            vm.isDisabled = false;
            vm.internalFailureMessage = response.data.message;
        }

    }
})();