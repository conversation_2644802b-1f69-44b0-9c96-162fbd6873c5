(function () {
    'use strict';

    angular
        .module('app.services')
        .factory('mfaService', mfaService);

    mfaService.$inject = ['$http', 'apiConstants', '$q', '$state', '$stateParams', '$rootScope', 'userService'];
    function mfaService($http, apiConstants, $q, $state, $stateParams, $rootScope, userService) {
        return {
            setQrCode : setQrCode,
            getQrCode : getQrCode,
            verifyMfa: verifyMfa
        };

        function setQrCode(code) {
            localStorage.setItem("qr", code);
        }

        function getQrCode() {
            return localStorage.getItem("qr");
        }

        function verifyMfa(verificationCode) {

            return $http.get(apiConstants.url + '/security/user/mfa/' + verificationCode)
                .then(verifyMfaSuccess, verifyMfaFailed);
        }

        function verifyMfaSuccess(response) {
            if (response.data) {
                userService.getUserInfo().then(userSuccess, userFailure);
            } else {
                console.log("INVALID VERIFCATION CODE");
            }
        }

        function verifyMfaFailed(error) {
            console.log("FAILED TO VERIFY CODE");
            return $q.reject(error.data.error_description);
        }

        function userSuccess() {
            localStorage.removeItem("qr");

            $rootScope.$broadcast("User-Info-Updated");
            if ($stateParams.redirect && $stateParams.redirect !== "") {
                $state.go($stateParams.redirect, $stateParams.params);
            } else {
                if(userService.isManufacturer()){
                    $state.go('dashboard');
                }else{
                    $state.go('publishedProducts');
                }
            }
        }

        function userFailure(error) {
            console.log(error);
        }
    }
})();
