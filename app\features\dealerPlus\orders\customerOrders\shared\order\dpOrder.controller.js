(function () {
    'use strict';

    angular
        .module('app.orders')
        .controller('DPOrderController', DPOrderController);

    DPOrderController.$inject = ['$stateParams', '$uibModal', '$state', 'headerBannerService', 'dpOrdersService', '$translate', '$window', '$rootScope', 'userService', 'basketService', '$timeout'];

    function DPOrderController($stateParams, $uibModal, $state, headerBannerService, dpOrdersService, $translate, $window, $rootScope, userService, basketService, $timeout) {
        var vm = this;

        vm.sortReverse = false;
        vm.isOrderItemsLoaded = false;
        vm.isBasketClicked = false;
        vm.endRecord = vm.itemPerPage;
        vm.orderId = $stateParams.orderId;
        vm.addCommentText = "";
        vm.viewCommentText = "";
        vm.showExternallyProcessButton = false;
        vm.showPartSearch = userService.hasPartSearchRole();
        vm.showRequiredSerialNumber = userService.getRequiredSerialNumber();
        vm.enquiriesOnly = userService.getEnquiriesOnly();
        vm.externallyProcessed = userService.getExternallyProcessed();
        vm.isDealerPlus = userService.isDealerPlusUser();

        var CANCEL_ORDER, EXTERNALLY_PROCESS_ORDER, ARE_YOU_SURE, EXTERNALLY_PROCESS_ARE_YOU_SURE, DELETE_FAILED,
            CANCEL_SUCCESS, EXTERNALLY_PROCESS_SUCCESS, DELETE_PART, DELETE_PART_MODAL_1, DELETE_PART_MODAL_2, ADD_SUCCESS;
        $translate(['ORDER.ADD_COMMENT', 'ORDER.VIEW_COMMENTS', 'ORDER.CANCEL_ORDER', 'ORDER.EXTERNALLY_PROCESS_ORDER',
            'ORDER.ARE_YOU_SURE', 'ORDER.EXTERNALLY_PROCESS_ARE_YOU_SURE', 'ORDER.DELETE_FAILED', 'ORDER.CANCEL_SUCCESS', 'ORDER.EXTERNALLY_PROCESS_SUCCESS',
            'ORDER.DELETE_PART', 'ORDER.DELETE_PART_MODAL_1', 'ORDER.DELETE_PART_MODAL_2', 'ADD_TO_BASKET.SUCCESS'])
            .then(function (resp) {
                vm.addCommentText = resp["ORDER.ADD_COMMENT"]
                vm.viewCommentText = resp["ORDER.VIEW_COMMENTS"];
                CANCEL_ORDER = resp["ORDER.CANCEL_ORDER"];
                EXTERNALLY_PROCESS_ORDER = resp["ORDER.EXTERNALLY_PROCESS_ORDER"];
                ARE_YOU_SURE = resp["ORDER.ARE_YOU_SURE"];
                EXTERNALLY_PROCESS_ARE_YOU_SURE = resp["ORDER.EXTERNALLY_PROCESS_ARE_YOU_SURE"];
                DELETE_FAILED = resp["ORDER.DELETE_FAILED"];
                CANCEL_SUCCESS = resp["ORDER.CANCEL_SUCCESS"];
                EXTERNALLY_PROCESS_SUCCESS = resp["ORDER.EXTERNALLY_PROCESS_SUCCESS"];
                DELETE_PART = resp["ORDER.DELETE_PART"];
                DELETE_PART_MODAL_1 = resp["ORDER.DELETE_PART_MODAL_1"];
                DELETE_PART_MODAL_2 = resp["ORDER.DELETE_PART_MODAL_2"];
                ADD_SUCCESS = resp["ADD_TO_BASKET.SUCCESS"];
            });

        var selectedOrderItem = {};

        vm.updateItemTotals = updateItemTotals;
        vm.viewLinkedTechDocs = viewLinkedTechDocs;
        vm.viewPartiallyShipped = viewPartiallyShipped;
        vm.createReadableAddress = createReadableAddress;
        vm.removeItem = removeItem;
        vm.deleteItem = deleteItem;
        vm.redirectToOrder = redirectToOrder;
        vm.updateManualPartTotals = updateManualPartTotals;
        vm.removeManualPart = removeManualPart;
        vm.exportOrderPartsToCSV = exportOrderPartsToCSV;
        vm.splitOrder = splitOrder;
        vm.cancelOrder = cancelOrder;
        vm.externallyProcessOrder = externallyProcessOrder;
        vm.onManualEdit = onManualEdit;
        vm.onEdit = onEdit;
        vm.openPartSearchModal = openPartSearchModal;
        vm.goToMasterPart = goToMasterPart;
        vm.editCustomerDetails = editCustomerDetails;
        vm.addToBasket = addToBasket;
        vm.createEnquiry = createEnquiry;
        vm.goToCreatedOrder = goToCreatedOrder;
        vm.isCustomerOrder = isCustomerOrder;

        function onEdit() {
            vm.updateItemTotals(true, this);
        }

        function onManualEdit() {
            vm.updateManualPartTotals(true, this);
        }

        function updateItemTotals(isEdit, that, initialLoad) {
            if (isEdit && !initialLoad) {
                $rootScope.$broadcast("Order-Edited");
            }
            if (!that) {
                that = this;
            }
            that.itemsTotal = 0; // Total before discounts
            that.discountedItemsTotal = 0; // Total after discounts
            that.hasPricesMissing = that.data.orderItems.length === 0;
            that.hasNoPrices = true;

            for (var i = 0; i < that.data.orderItems.length; i++) {
                var item = that.data.orderItems[i];
                item.price = Number(item.price) || 0;

                if (item.price > 0) {
                    that.hasNoPrices = false;
                    var totalItemPrice = item.price * item.quantity; // Price before discount
                    that.itemsTotal += totalItemPrice; // Accumulating total before discounts

                    var discountAmount = item.price * (that.data.percentageDiscount / 100);
                    item.discountedPrice = item.price - discountAmount;
                    item.totalPrice = item.discountedPrice * item.quantity; // Apply discount to total price
                    that.discountedItemsTotal += item.totalPrice; // Accumulate total after discounts
                } else {
                    item.totalPrice = 0;
                    item.discountedPrice = 0;
                    if (item.quantity > 0) {
                        that.hasPricesMissing = true;
                    }
                }

                that.hasQuantityMissing = item.quantity === 1;
            }

            if (!that.manualPartsTotal) {
                that.manualPartsTotal = 0;
            }

            // combinedTotalPrice is the total price of items before discount
            that.combinedTotalPrice = that.itemsTotal + that.manualPartsTotal;

            // Calculate total discount
            var saving = (that.combinedTotalPrice * (that.data.percentageDiscount || 0)) / 100;
            var discountedTotal = that.combinedTotalPrice - saving;

            // Total after applying discount
            that.total = discountedTotal + (that.data.shippingPrice || 0) + (that.data.taxPrice || 0);
            that.total = Number(that.total.toFixed(2));
        }


        function updateManualPartTotals(isEdit, that) {
            if (isEdit) {
                $rootScope.$broadcast("Order-Edited");
            }
            if (!that) {
                that = this;
            }
            that.manualHasPricesMissing = that.additionalParts.length === 0
            that.manualPartsTotal = 0;
            for (var i = 0; i < that.additionalParts.length; i++) {
                if (that.additionalParts[i].price) {
                    that.additionalParts[i].totalPrice = that.additionalParts[i].price * that.additionalParts[i].quantity;
                    that.manualPartsTotal += that.additionalParts[i].totalPrice;
                } else {
                    that.additionalParts[i].totalPrice = 0;
                    that.additionalParts[i].price = 0;
                    if (that.manualHasPricesMissing || that.additionalParts[i].quantity > 0) {
                        that.manualHasPricesMissing = true;
                    } else {
                        that.manualHasPricesMissing = false;
                    }
                }
                that.hasManuallyAddedQuantityMissing = that.additionalParts[i].quantity === 1;
            }
            if (!that.itemsTotal) {
                that.itemsTotal = 0;
            }
            that.combinedTotalPrice = (that.itemsTotal + that.manualPartsTotal);
            if (!that.data.percentageDiscount) {
                that.data.percentageDiscount = 0;
                that.total = that.itemsTotal + that.manualPartsTotal + that.data.shippingPrice;
            } else {
                var saving = (that.combinedTotalPrice / 100) * (that.data.percentageDiscount);
                var discountedTotal = that.combinedTotalPrice - saving;

                that.total = discountedTotal + that.data.shippingPrice;
            }
        }

        function editCustomerDetails() {
            var that = this;
            var customerDetailsObject = {
                isDealerPlus: true,
                customerId: this.data.createdByUserId,
                orderId: this.data.orderId,
                shippingAddressId: this.data.shippingAddress.id,
                billingAddressId: this.data.billingAddress.id,
                contactNumber: this.data.contactNumber,
                deliveryNumber: this.data.deliveryNumber,
                emailAddress: this.data.emailAddress,
                contactName: this.data.contactName,
                deliveryName: this.data.deliveryName,
                createdByUserId: this.data.createdByUserId
            };
            $uibModal.open({
                templateUrl: 'features/shared/editCustomerDetails/editCustomerDetails.html',
                controller: 'EditCustomerDetailsController',
                controllerAs: 'editCustDetailsCtrl',
                resolve: {
                    customerDetailsObject: function () {
                        return customerDetailsObject;
                    }
                }
            }).result.then(function (returnObject) {
                var parsedDeliveryAddress = JSON.parse(returnObject.shippingAddress);
                var parsedShippingAddress = JSON.parse(returnObject.billingAddress);
                that.data.shippingAddress.id = parsedDeliveryAddress.id;
                that.data.billingAddress.id = parsedShippingAddress.id;
                that.billingAddress = createReadableAddress(parsedDeliveryAddress);
                that.shippingAddress = createReadableAddress(parsedShippingAddress);

                that.data.contactNumber = returnObject.contactNumber;
                that.data.deliveryNumber = returnObject.deliveryNumber;
                that.data.contactName = returnObject.contactName;
                that.data.deliveryName = returnObject.deliveryName;
            }, function () {
                console.log('Modal Cancelled');
            });
        }

        function createReadableAddress(addressObject) {
            var readableAddress = '';
            readableAddress += (addressObject.companyName !== null && addressObject.companyName !== '') ? addressObject.companyName + ", " : '';
            readableAddress += (addressObject.addressLine1 !== null && addressObject.addressLine1 !== '') ? addressObject.addressLine1 : '';
            readableAddress += (addressObject.addressLine2 !== null && addressObject.addressLine2 !== '') ? ', ' + addressObject.addressLine2 : '';
            readableAddress += (addressObject.city !== null && addressObject.city !== '') ? ', ' + addressObject.city : '';
            readableAddress += (addressObject.state !== null && addressObject.state !== '') ? ', ' + addressObject.state : '';
            readableAddress += (addressObject.postcode !== null && addressObject.postcode !== '') ? ', ' + addressObject.postcode : '';
            readableAddress += (addressObject.country !== null && addressObject.country !== '') ? ', ' + addressObject.country : '';
            return readableAddress;
        }

        function removeItem(index) {
            if (this.data.orderItems[index].archived) {
                this.data.orderItems[index].quantity = this.data.orderItems[index].originalQuantity;
                this.data.orderItems[index].archived = false;
            } else {
                this.data.orderItems[index].quantity = 0;
                this.data.orderItems[index].archived = true;
            }
            updateItemTotals(true, this);
        }

        var items;

        function deleteItem(index) {
            items = this;
            selectedOrderItem = this.data.orderItems[index];
            var confirmObject = {
                titleText: DELETE_PART,
                bodyText: DELETE_PART_MODAL_1 + this.data.orderItems[index].partNumber + DELETE_PART_MODAL_2
            };
            $uibModal.open({
                templateUrl: 'features/shared/confirmationModal/confirmModal.html',
                controller: 'ConfirmModalController',
                controllerAs: 'confirmModalCtrl',
                size: 'sm',
                resolve: {
                    confirmObject: function () {
                        return confirmObject;
                    }
                }
            }).result
                .then(deleteOrderItemConfirmed, doNothing);
        }

        function deleteOrderItemConfirmed() {
            dpOrdersService.deleteOrderItem(selectedOrderItem.orderItemId)
                .then(deleteOrderItemSuccess, serviceFailed);
        }

        function deleteOrderItemSuccess() {
            selectedOrderItem.quantity = 0;
            selectedOrderItem.archived = true;
            updateItemTotals(true, items);
        }

        function serviceFailed(error) {
            headerBannerService.setNotification('ERROR', WENT_WRONG, 10000);
            console.error(error.data);
        }

        function removeManualPart(index) {
            if (this.additionalParts[index].archived) {
                this.additionalParts[index].quantity = this.additionalParts[index].originalQuantity;
                this.additionalParts[index].archived = false;
            } else {
                this.additionalParts[index].originalQuantity = this.additionalParts[index].quantity;
                this.additionalParts[index].quantity = 0;
                this.additionalParts[index].archived = true;
            }
            updateManualPartTotals(true, this);
        }

        function exportOrderPartsToCSV() {
            dpOrdersService.exportOrderPartsToCSV(vm.orderId).then(exportOrderPartsToCSVSuccess, exportOrderPartsToCSVFailed);
        }

        function exportOrderPartsToCSVSuccess(response) {
            var csvFile = response.data.toString();
            var filename = "orderParts.csv";

            var blob = new Blob([csvFile], {type: 'text/csv;charset=utf-8;'});
            if (navigator.msSaveBlob) { // IE 10+
                navigator.msSaveBlob(blob, filename);
            } else {
                var link = document.createElement("a");
                if (link.download !== undefined) { // feature detection
                    // Browsers that support HTML5 download attribute
                    var url = URL.createObjectURL(blob);
                    link.setAttribute("href", url);
                    link.setAttribute("download", filename);
                    link.style.visibility = 'hidden';
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                }
            }
        }

        function viewLinkedTechDocs(item) {
            if (item.techDocs.length == 1) {
                $window.open(item.techDocs[0].url, "_blank");
            } else {
                $uibModal.open({
                    templateUrl: 'features/orders/linkedTechDocs/orderTechDocModal.html',
                    controller: 'OrderTechDocController',
                    controllerAs: 'orderTechDocCtrl',
                    size: 'md',
                    resolve: {
                        techDocObject: function () {
                            return {techDocs: item.techDocs, partNumber: item.partNumber};
                        }
                    }
                });
            }
        }

        function viewPartiallyShipped() {
            $uibModal.open({
                templateUrl: 'features/orders/live/partialShipOrder/partialShipOrderModal.html',
                controller: 'PartialShipOrderModalController',
                controllerAs: 'partialShipOrderModalCtrl',
                size: 'md',
                resolve: {
                    orderId: function () {
                        return $stateParams.orderId;
                    }
                }
            });
        }

        function exportOrderPartsToCSVFailed(error) {
            console.log(error)
        }

        function redirectToOrder(orderId, orderStatus) {
            var viewState = 'dpOrders.customerOrders.orders.enquiry';
            if (orderStatus === 'QUOTE') {
                viewState = 'dpOrders.customerOrders.orders.quotation';
            } else if (orderStatus === 'RECEIVED' || orderStatus === 'PROCESSED' || orderStatus === 'SHIPPED' || orderStatus === 'PARTIALLY_SHIPPED') {
                viewState = 'dpOrders.customerOrders.orders.liveorder';
            } else if (orderStatus === 'CLOSED' || orderStatus === 'CANCELLED' || orderStatus === 'EXTERNAL') {
                viewState = 'dpOrders.customerOrders.orders.historicalorder';
            }

            $state.go(viewState, {
                orderId: orderId
            });
        }

        function splitOrder(orderItems, manualItems, orderId, displayId) {
            var partsObject = {
                orderItems: orderItems,
                manualItems: manualItems,
                orderId: orderId,
                displayId: displayId
            };

            $uibModal.open({
                templateUrl: 'features/orders/splitOrder/splitOrder.html',
                controller: 'SplitOrderController',
                controllerAs: 'splitOrderCtrl',
                resolve: {
                    partsObject: function () {
                        return partsObject;
                    }
                }
            }).result
                .then(splitOrderComplete, splitOrderCancelled);
        }

        function splitOrderComplete() {
            $window.location.reload();
        }

        function splitOrderCancelled() {
            console.log("cancelled");
        }

        var cancelOrderId;

        function cancelOrder(orderId, displayId) {
            cancelOrderId = orderId;
            displayId = displayId ? displayId : orderId;
            var confirmObject = {
                titleText: CANCEL_ORDER + " #" + displayId,
                bodyText: ARE_YOU_SURE + " #" + displayId + "?"
            };
            $uibModal.open({
                templateUrl: 'features/shared/confirmationModal/confirmModal.html',
                controller: 'ConfirmModalController',
                controllerAs: 'confirmModalCtrl',
                size: 'sm',
                resolve: {
                    confirmObject: function () {
                        return confirmObject;
                    }
                }
            }).result
                .then(cancelOrderConfirmed, doNothing);
        }

        function cancelOrderConfirmed() {
            dpOrdersService.cancelOrder(cancelOrderId)
                .then(cancelOrderSuccess, cancelOrderFailed)
        }

        function doNothing() {
        }

        function cancelOrderSuccess() {
            headerBannerService.setNotification('SUCCESS', CANCEL_SUCCESS, 2000);
            $state.go('dpOrders.customerOrders.orders.historicalorder', {orderId: cancelOrderId});

        }

        function cancelOrderFailed() {
            headerBannerService.setNotification('ERROR', DELETE_FAILED, 2000);
        }

        var externalProcessOrderId;

        function externallyProcessOrder(order) {
            externalProcessOrderId = order.orderId;
            var displayId = order.customOrderDisplay;
            var confirmObject = {
                titleText: EXTERNALLY_PROCESS_ORDER + " #" + displayId,
                bodyText: EXTERNALLY_PROCESS_ARE_YOU_SURE + " #" + displayId + "?"
            };
            $uibModal.open({
                templateUrl: 'features/shared/confirmationModal/confirmModal.html',
                controller: 'ConfirmModalController',
                controllerAs: 'confirmModalCtrl',
                size: 'sm',
                resolve: {
                    confirmObject: function () {
                        return confirmObject;
                    }
                }
            }).result
                .then(externallyProcessOrderConfirmed, doNothing);
        }

        function externallyProcessOrderConfirmed() {
            dpOrdersService.externallyProcessOrder(externalProcessOrderId)
                .then(externallyProcessOrderSuccess, externallyProcessOrderFailed)
        }

        function externallyProcessOrderSuccess() {
            headerBannerService.setNotification('SUCCESS', EXTERNALLY_PROCESS_SUCCESS, 2000);
            $state.go('dpOrders.customerOrders.orders.historicalorder', {orderId: externalProcessOrderId});

        }

        function externallyProcessOrderFailed() {
            headerBannerService.setNotification('ERROR', DELETE_FAILED, 2000);
        }

        function openPartSearchModal() {
            var orderDetails = {};
            orderDetails.orderId = $stateParams.orderId;
            orderDetails.manufacturerSubEntityId = this.data.manufacturerSubEntityId;
            orderDetails.userId = this.data.createdByUserId;
            var closeModal = $uibModal.open({
                keyboard: false,
                templateUrl: 'features/orders/shared/partSearchModal/partSearchModal.html',
                controller: 'PartSearchModalController',
                size: 'xl',
                controllerAs: 'partSearchModalCtrl',
                resolve: {
                    orderDetails: function () {
                        return orderDetails;
                    }
                }
            }); closeModal.result
                .then(this.reloadOrder);

            document.addEventListener('keyup', function(e) {
                if(e.keyCode === 27){
                    closeModal.close();
                }
            });
        }

        function goToMasterPart(masterPartId) {
            $state.go('dpMasterPart', {masterPartId: masterPartId})
        }

        function goToCreatedOrder(orderId) {
            $state.go('orders.enquiry', {orderId: orderId});
        }

        function addToBasket(orderItems, manualItems, orderId, displayId) {
                var partsObject = {
                    orderItems: orderItems,
                    manualItems: manualItems,
                    orderId: orderId,
                    displayId: displayId
                };

                $uibModal.open({
                    templateUrl: 'features/dealerPlus/orders/customerOrders/shared/dpAddToBasket/dpAddToBasket.html',
                    controller: 'DPAddToBasketController',
                    controllerAs: 'dpAddToBasketCtrl',
                    resolve: {
                        partsObject: function () {
                            return partsObject;
                        }
                    }
                }).result
                    .then(addToBasketSuccess, splitOrderCancelled);
        }

        function addToBasketSuccess(){
            headerBannerService.setNotification('SUCCESS', ADD_SUCCESS, 5000);
            this.isBasketClicked = true;
            $timeout(function () {
                vm.isBasketClicked = false;
            }, 500);
        }

        function createEnquiry() {
            var orderObject = this.data;
            $uibModal.open({
                templateUrl: 'features/dealerPlus/orders/customerOrders/shared/orderManagement/orderManagement.html',
                controller: 'OrderManagementController',
                size: 'lg',
                controllerAs: 'orderMgmtCtrl',
                resolve: {
                    orderObject: function () {
                        return orderObject;
                    }
                }
            }).result
                .then(function (orderId) {
                    headerBannerService.setNotification('SUCCESS', 'Enquiry submitted successfully', 5000);
                    $state.go('dpOrders.myOrders.orders.enquiry', {orderId: orderId});
                }, function () {
                    console.log('Modal Cancelled');
                });
        }

        function isCustomerOrder() {
            return $state.current.name.includes("customerOrders");
        }

    }
})();
