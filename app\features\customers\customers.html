<section class="body-content">
    <div class="order-details-holder">
        <h1 translate>CUSTOMERS.TITLE</h1>
        <p class="page-desc" translate>CUSTOMERS.SUBTITLE</p>
    </div>
</section>

<section class="responsiveContainer m-5">

        <div class="success-alert" ng-if="customerCtrl.successMessage != ''">
            {{customerCtrl.successMessage}}
        </div>

        <div id="{{customerCtrl.isFixedHeader ? 'infiniteScrollFixedHeader' : 'infiniteScrollStaticHeader'}}"
            class="flex p-4 p-md-0">
            <search-filter class="col-12 col-md-3" state-name="'customers'" value="customerCtrl.searchValue"
                placeholder-key="CUSTOMERS.SEARCH_BY_NAME"></search-filter>
        
        
            <button class="btn primary ml-auto mr-4 col-12 col-md-auto mt-3 mt-md-0 create-machine"
                ng-click="customerCtrl.createCompany()" translate>CUSTOMERS.ADD_NEW
            </button>
        </div>

    <table class="table table-bordered">

        <thead>
        <tr>
            <th ng-class="customerCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'" ng-if="customerCtrl.displayManufacturerSubEntityId" ng-click="customerCtrl.customer_sort='manufacturerSubEntityId'; customerCtrl.sortReverse = !customerCtrl.sortReverse" translate>CUSTOMERS.ID</th>
            <th ng-class="customerCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'"
                ng-click="customerCtrl.customer_sort='name'; customerCtrl.sortReverse = !customerCtrl.sortReverse" translate>CUSTOMERS.COMPANY</th>
            <th ng-class="customerCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'"
                ng-click="customerCtrl.customer_sort='manufacturerSubEntityType'; customerCtrl.sortReverse = !customerCtrl.sortReverse" translate>CUSTOMERS.TYPE</th>

            <th ng-class="customerCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'"
                ng-click="customerCtrl.customer_sort='createdDate'; customerCtrl.sortReverse = !customerCtrl.sortReverse" translate>CUSTOMERS.CUSTOMER_SINCE</th>
            <th translate>CUSTOMERS.ACTIONS</th>
        </tr>
        </thead>

        <tbody infinite-scroll="customerCtrl.loadMoreInfiniteScroll()" infinite-scroll-distance="3" infinite-scroll-disabled="customerCtrl.loadingInfiniteScrollData">
        <tr class="p-4" ng-repeat="customer in customerCtrl.customerList | orderBy:customerCtrl.customer_sort:customerCtrl.sortReverse | filter : customerCtrl.searchValue">
            <td ng-if="customerCtrl.displayManufacturerSubEntityId" data-label="{{'CUSTOMERS.ID' | translate}}">{{customer.manufacturerSubEntityId}}</td>
            <td data-label="{{'CUSTOMERS.COMPANY' | translate}}">{{customer.name}}</td>
            <td data-label="{{'CUSTOMERS.TYPE' | translate}}">{{customer.manufacturerSubEntityType === "DEALER" ? "Dealer" : (customer.manufacturerSubEntityType === "DEALER_PLUS" ? "Dealer Plus" : "Customer")}}</td>
            <td data-label="{{'CUSTOMERS.CUSTOMER_SINCE' | translate}}">{{customer.createdDate | date : "d MMMM y"}}</td>

            <td>
                <split-button-dropdown
                        main-action="customerCtrl.viewCustomerUsers(customer)"
                        main-action-label="{{'CUSTOMERS.VIEW_DETAILS' | translate}}"
                        actions="customerCtrl.actions"
                        entity="customer">
                </split-button-dropdown>
            </td>

        </tr>

        <tr ng-show="!customerCtrl.customerList.length > 0 && customerCtrl.areCustomersLoaded">
            <td colspan="4" translate>CUSTOMERS.NO_COMPANIES</td>
        </tr>

        <tr ng-hide="customerCtrl.areCustomersLoaded" align="center">
            <td class="preloader" colspan="4"><img ng-src="images/cadpreloader.gif" class="ajax-loader" height="60" width="60"/></td>
        </tr>
        </tbody>
    </table>
    </div>

<span ng-click="customerCtrl.scrollToTop()" id="backToTopBtn" title="Go to top" class="fas fa-arrow-alt-circle-up"
    ng-show="customerCtrl.showBackToTopButton"></span>

    </div>
</section>