<div class="auth default-theme">
    <div class="side-panel">
        <div class="auth-panel">
            <div class="content">
                <img alt="CadShareLogo center-align" ng-src="{{appCtrl.logoUrl}}">
                <h3 translate>MFA.TWO_FACTOR_TITLE</h3>
                <div>
                    <p>{{'MFA.TWO_ENABLED' | translate}} {{mfaSetupCtrl.manufacturerName}} {{'MFA.FOR_ACCESS' | translate}}</p>
                    <p translate>MFA.USE_GOOGLE_AUTH<p/>
                </div>

                <form id="loginform" name="loginform" class="form" role="form">
                    <img class="center-align qr-verification" src="{{mfaSetupCtrl.qrCode}}" />


                    <input id="verificationCode" type="text" class="form-control" maxlength="6" ng-model="mfaSetupCtrl.verifyCode" placeholder="{{'MFA.ENTER_CODE' | translate}}">

                    <button class="btn large primary mt-3" ng-click="mfaSetupCtrl.verifyUserCode()" type="submit"  ng-disabled="mfaSetupCtrl.verifyCode.length < 6" translate>MFA.VERIFY</button>

                </form>

                <p translate>MFA.NO_AUTHENTICATOR</p>
                <div class="center-align store-img">

                    <a href="https://itunes.apple.com/gb/app/google-authenticator/id388497605?mt=8"><img class="app-store-logos" src="https://linkmaker.itunes.apple.com/assets/shared/badges/en-us/appstore-lrg.svg"/></a>
                    <a href='https://play.google.com/store/apps/details?id=com.google.android.apps.authenticator2&hl=en_GB&pcampaignid=MKT-Other-global-all-co-prtnr-py-PartBadge-Mar2515-1'><img class="google-logo
" alt='Get it on Google Play' src='https://play.google.com/intl/en_gb/badges/images/generic/en_badge_web_generic.png'/></a>
                    <a href="https://www.microsoft.com/en-gb/p/microsoft-authenticator/9nblgggzmcj6"><img class="app-store-logos" src="https://developer.microsoft.com/store/badges/images/English_get_L.png" alt="Get it from Microsoft" /></a>
                </div>
            </div>
        </div>
    </div>
</div>