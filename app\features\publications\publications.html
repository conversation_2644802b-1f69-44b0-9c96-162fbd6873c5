<section class="body-content">
    <div class="order-details-holder">
        <h1 translate>PUBLICATIONS.TITLE</h1>
        <p class="page-desc" translate>PUBLICATIONS.SUBTITLE</p>
    </div>
</section>

<section class="responsiveContainer m-5">

    <div class="success-alert" ng-if="customerCtrl.successMessage != ''">
        {{customerCtrl.successMessage}}
    </div>

    <div id="{{publicationsCtrl.isFixedHeader ? 'infiniteScrollFixedHeader' : 'infiniteScrollStaticHeader'}}"
        class="flex p-4 p-md-0">
        <search-filter class="col-12 col-md-3" state-name="'publications'"
            on-search-change="publicationsCtrl.searchFilterChange()" value="publicationsCtrl.searchValue"
            placeholder-key="PUBLICATIONS.SEARCH_BY"></search-filter>

        <button class="btn primary mr-2 col-12 col-md-auto my-3 my-md-0"
            ng-click="publicationsCtrl.toggleAdvancedFilter()">
            <span class="text-white">{{'PUBLICATIONS.FILTER' | translate}}</span>
            <i ng-hide="publicationsCtrl.displayAdvancedFilter" class="pl-2 fa fa-plus"></i>
            <i ng-show="publicationsCtrl.displayAdvancedFilter" class="pl-2 fa fa-minus"></i>
        </button>
        <button class="btn secondary col-12 col-md-auto mb-3 mb-md-0"
            ng-disabled="!publicationsCtrl.filter_status && !publicationsCtrl.filter_viewable && !publicationsCtrl.searchValue"
            href="" ng-click="publicationsCtrl.clearFilter()" translate>PUBLICATIONS.CLEAR_ALL</button>

        <button class="btn primary ml-auto mr-4 col-12 col-md-auto create-machine"
            ng-click="publicationsCtrl.createPublication()" translate>PUBLICATIONS.CREATE_NEW_PUBLICATION
        </button>

        <div class="gloBl-filter-panel align-items-center cadGap mb-4" ng-show="publicationsCtrl.displayAdvancedFilter">

            <div class="filter-option">
                <div class="input-icon-wrap first-filter">
                    <small ng-show="!publicationsCtrl.filter_status" translate>PUBLICATIONS.FILTER_BY_STATUS</small>
                    <div class="select-box">
                        <select ng-change="publicationsCtrl.applyFilter()" ng-model="publicationsCtrl.filter_status"
                            ng-options="status as status for status in publicationsCtrl.status track by status">
                        </select>
                    </div>
                    <i ng-class="{'selectActive': publicationsCtrl.filter_status}" class="fa fa-filter"
                        aria-hidden="true"></i>
                </div>
            </div>

            <div class="dropdown filter-option dropdownTextViewable">
                <button class="input-icon-wrap px-3 first-filter dropdownTextSearchBtn dropdown-toggle" type="button"
                    data-toggle="dropdown">
                    <span class="dropdownTextSearchBtnInnerTxt text-truncate mr-4">{{ publicationsCtrl.filter_viewable
                        ||
                        'PUBLICATIONS.FILTER_BY_VIEWABLE' | translate }}</span>
                    <i ng-class="{'selectActive': publicationsCtrl.filter_viewable}" class="fa fa-filter"
                        aria-hidden="true"></i>
                </button>
                <div class="dropdown-menu dropdownViewable col-12 col-md-auto" style="overflow:hidden;">
                    <div class="search-container">
                        <input class="mb-0 form-control" id="textFilterDropdown"
                            ng-model="publicationsCtrl.filterViewableValue"
                            ng-keyup="publicationsCtrl.filterViewableDropdown()" type="text" placeholder="Search..">
                    </div>
                    <div class="items-container">
                        <!-- Added orderBy:'toString()' for alphanumeric sorting -->
                        <li ng-repeat="viewable in publicationsCtrl.viewables | orderBy:'toString()' track by $index">
                            <a href="javascript:void(0)"
                                ng-click="publicationsCtrl.filter_viewable = viewable; publicationsCtrl.applyFilter()">{{
                                viewable }}</a>
                        </li>
                    </div>
                </div>
            </div>
        </div>
        </div>

        <div class="responsiveContainer">

            <table class="table table-bordered">

            <thead>
            <tr>
                <th ng-class="publicationsCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'" ng-click="publicationsCtrl.publication_sort='name'; publicationsCtrl.sortReverse = !publicationsCtrl.sortReverse" translate>PUBLICATIONS.MANUAL_DETAILS</th>
                <th ng-class="publicationsCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'" class="sortIconDown"
                    ng-click="publicationsCtrl.publication_sort='manualStatus'; publicationsCtrl.sortReverse = !publicationsCtrl.sortReverse" translate>PUBLICATIONS.CUSTOMERS</th>
                <th ng-class="publicationsCtrl.sortReverse ? 'sortIconDown' : 'sortIconUp'" class="sortIconDown"
                    ng-click="publicationsCtrl.publication_sort='manualStatus'; publicationsCtrl.sortReverse = !publicationsCtrl.sortReverse" translate>PUBLICATIONS.STATUS</th>
                <th translate>PUBLICATIONS.ACTIONS</th>
            </tr>
            </thead>

                <tbody infinite-scroll="publicationsCtrl.loadMoreInfiniteScroll()" infinite-scroll-distance="3"
                    infinite-scroll-disabled="publicationsCtrl.loadingInfiniteScrollData">
                    <tr class="p-4"
                        ng-repeat="publications in publicationsCtrl.publicationList | orderBy:publicationsCtrl.publication_sort: publicationsCtrl.sortReverse | filter :publicationsCtrl.searchValue | filter : publicationsCtrl.combinedModelStatusFilter"
                        ng-show="publicationsCtrl.publicationList.length > 0">
                        <td data-label="{{'PUBLICATIONS.MANUAL_DETAILS' | translate}}" class="tableColumn">
                            <img ng-if="" ng-src="{{publications.coverImage.url || 'images/placeholder.jpg'}}"
                                class="img-fluid" width="100" height="100" alt="coverImagePublication" />
                            <span class="mb-0 ">{{publications.name}}</span>
                            <!-- <p ng-if="publications.viewables && publications.viewables.length > 0">{{"PUBLICATIONS.BASED_ON" | translate}}
                                <span ng-if="publications.viewables.length != 1">{{"PUBLICATIONS.MULTIPLE_VIEWABLES" |
                                    translate}}</span>
                                <span ng-if="publications.viewables.length === 1">{{"PUBLICATIONS.VIEWABLE" | translate}}
                                    ({{publications.viewables[0].name}})</span>
                            </p> -->
                        </td>
                        <td>
                            <p>
                                <span ng-if="publications.customers.length === 0"
                                    translate>PUBLICATIONS.UNASSIGNED</span>
                                <span ng-if="publications.customers.length >= 1 && publications.customers.length <= 3">
                                    {{publications.customerNamesString}}
                                </span>
                                <span ng-if="publications.customers.length > 3">
                                    {{ 'PUBLICATIONS.MULTI_CUST' | translate }} ({{publications.customers.length}})
                                </span>
                            </p>
                        </td>

                        <td data-label="{{'PUBLICATIONS.STATUS' | translate}}">

                            <span class="badge-pill "
                                ng-class="publications.published ? 'primary' : 'secondary' ">
                                {{publications.published ? ('PUBLICATIONS.PUBLISHED' | translate) : ('PUBLICATIONS.UNPUBLISHED' | translate)}}</span>

                        </td>

                        <td>
                            <split-button-dropdown main-action="publicationsCtrl.editPublication(publications)"
                                main-action-label="{{'PUBLICATIONS.EDIT_MANUAL' | translate}}"
                                actions="publicationsCtrl.actions" entity="publications">
                            </split-button-dropdown>
                        </td>

                    </tr>

                    <tr
                        ng-show="!publicationsCtrl.publicationList.length > 0 && publicationsCtrl.isPublicationsLoaded && !publicationsCtrl.filter_viewable">
                        <td colspan="11" translate>PUBLICATIONS.NO_PUBLICATIONS</td>
                    </tr>
                    <tr
                        ng-show="!publicationsCtrl.publicationList.length > 0 && publicationsCtrl.isPublicationsLoaded && publicationsCtrl.filter_viewable">
                        <td colspan="11" translate>PUBLICATIONS.FILTER_NO_PUBLICATIONS</td>
                    </tr>
                    <tr ng-hide="publicationsCtrl.isPublicationsLoaded" align="center">
                        <td class="preloader" colspan="11"><img ng-src="images/cadpreloader.gif" class="ajax-loader"
                                height="60" width="60" /></td>
                    </tr>
                </tbody>
            </table>

        </div>

        <span ng-click="publicationsCtrl.scrollToTop()" id="backToTopBtn" title="Go to top"
            class="fas fa-arrow-alt-circle-up" ng-show="publicationsCtrl.showBackToTopButton"></span>

</section>