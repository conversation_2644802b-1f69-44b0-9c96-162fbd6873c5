(function () {
    'use strict';

    angular
        .module('app.viewer')
        .directive('viewerSettings', viewerSettings);

    function viewerSettings() {
        var directive = {
            restrict: 'E',
            templateUrl: 'features/viewer/extensions/viewerSettings/viewerSettings.html',
            controller: 'ViewerSettingsController',
            controllerAs: 'viewerSettingsCtrl',
            bindToController: true
        };
        return directive;
    }

})();