(function () {
    'use strict';

    angular
        .module('app.services')
        .factory('workInstructionsService', workInstructionsService);

    workInstructionsService.$inject = ['$http', 'apiConstants'];

    function workInstructionsService($http, apiConstants) {
        return {
            fetchWorkInstructions: fetchWorkInstructions,
            createWorkInstructions: createWorkInstructions,
            updateWorkInstructions: updateWorkInstructions,
            deleteWorkInstructions: deleteWorkInstructions,

            getWorkInstructionsDetails: getWorkInstructionsDetails,
            createWorkInstructionsDetail: createWorkInstructionsDetail,
            updateWorkInstructionsDetail: updateWorkInstructionsDetail,
            deleteWorkInstructionsDetail: deleteWorkInstructionsDetail
        };

        function fetchWorkInstructions(modelId){
            return $http.get(apiConstants.url + '/instruction/model/' + modelId);
        }

        function createWorkInstructions(modelId, name, description){
            return $http.post(apiConstants.url + '/instruction/model/' + modelId, {
                "name": name,
                "description": description
            });
        }

        function updateWorkInstructions(viewableId, name, description){
            var updateData = {
                "name": name,
                "description": description
            };
            return $http.put(apiConstants.url + '/instruction/' + viewableId, updateData);
        }

        function deleteWorkInstructions(viewableId){
            return $http.delete(apiConstants.url + '/instruction/' + viewableId);
        }

        function getWorkInstructionsDetails(viewableId){
            return $http.get(apiConstants.url + '/instruction/' + viewableId);
        }

        function createWorkInstructionsDetail(state, viewableId, stateId, visibleIds, stateName, notes){
            var data = {
                "state": state,
                "viewableId": viewableId,
                "stateId": stateId,
                "visibleDbIds": visibleIds,
                "stateName":stateName,
                "notes": notes
            };
            return $http.post(apiConstants.url + '/instruction/viewable/' + viewableId, data);
        }

        function updateWorkInstructionsDetail(instructionDetailId, instructionDetail){

            return $http.put(apiConstants.url + '/instruction/detail/' + instructionDetailId, instructionDetail);
        }

        function deleteWorkInstructionsDetail(instructionDetailId){
            return $http.delete(apiConstants.url + '/instruction/detail/' + instructionDetailId);
        }
        
    }

})();