(function () {
    'use strict';

    angular
        .module('app.services')
        .factory('createMeetingService', createMeetingService);

    createMeetingService.$inject = ['$http', 'apiConstants', '$location', 'userService'];

    function createMeetingService($http, apiConstants, $location, userService) {
        var vm = this;
        vm.isHost = false;
        
        function socketInit(){ 
            vm.fullName = userService.getFullName();
            vm.socket = io.connect(apiConstants.websocket);
            vm.socket.emit("createUser", vm.fullName);
        
            vm.socket.on("updateChat", function(username, data) {
                console.log(username, data);
            });

            return vm.socket;
        }

        function getSocket(){
            return vm.socket;
        }

        function createRoom(roomId){
            vm.socket.emit("createRoom", roomId, function(iscreated){
                if(iscreated) { 
                    vm.socket.emit("updateRooms", roomId, function(isRoomJoined){
                        if(isRoomJoined){
                            console.log("switched room");
                        } else{
                            console.log("Not able to join room: ", roomId);
                        }
                    }); 
                } else{
                    console.log("Chat room is not created ", guid);
                }
            });
        }

        return {
            sendEmail: sendEmail,
            socketInit: socketInit,
            createRoom: createRoom,
            getSocket: getSocket,
            setInvitedUsers: setInvitedUsers,
            getInvitedUsers: getInvitedUsers
        };

        function setInvitedUsers(invitedUsers){
            localStorage.setItem("liveMeetingInfo", angular.toJson(invitedUsers));
        }

        function getInvitedUsers(meetingGuid){
            return new Promise((resolve, reject) => {
                vm.invitedUsers = angular.fromJson(localStorage.getItem("liveMeetingInfo"));
                if(vm.invitedUsers !== null) resolve(vm.invitedUsers)
                else vm.socket.emit("getInvitedUsers", meetingGuid, function(meetingInfo){
                    if(meetingInfo && meetingInfo.host === vm.fullName){
                        vm.invitedUsers  = meetingInfo;
                        localStorage.setItem("liveMeetingInfo", angular.toJson(meetingInfo));
                        resolve(meetingInfo);
                    } else{
                        resolve();
                    }
                });
            });
        }

        function sendEmail(modelId, customerUserId, manufacturerUserId) {
            var siteUrl = $location.protocol() + '://' + $location.host();
            return  $http({
                method: 'POST',
                url: apiConstants.url + '/liveMeeting?language=EN',
                data: {
                    "modelId": modelId,
                    "customerUserId": customerUserId,
                    "manufacturerUserId": manufacturerUserId
                },
                headers: {'Content-Type': 'application/json', 'Site-Url': siteUrl},
                transformResponse: transformData
            })            
        }
        function transformData(data, headersGetter, status) {
            return data;
        }

    }
})();
