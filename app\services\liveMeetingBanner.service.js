(function () {
    'use strict';

    angular
        .module('app.services')
        .factory('liveMeetingBannerService', liveMeetingBannerService);

    liveMeetingBannerService.$inject = ['$rootScope', '$timeout', '$translate'];

    function liveMeetingBannerService($rootScope, $timeout, $translate) {

        var notification = {};
        var presenter = "";
        var leaveSharing = null;

        $translate(['LIVE_MEETING_BANNER.PRESENTER'])
            .then(function (resp) {
                presenter = resp["LIVE_MEETING_BANNER.PRESENTER"];
            });


        return {
            setNotification: setNotification,
            getNotification: getNotification,
            removeNotification: removeNotification,
            setMethodToRemoveEvents: setMethodToRemoveEvents,
            getMethodToRemoveEvents: getMethodToRemoveEvents
        };

        /**
         * @param {string} level - values are ERROR, WARN, SUCCESS and INFO
         **/
        function setNotification(liveMeetingData) {
            notification = {"presenter":liveMeetingData.presenter, 
                "users": liveMeetingData.users,
                "removeBanner": false
            };
            $rootScope.$broadcast("meetingNotification");
        }

        function getNotification() {
            return notification;
        }

        function setMethodToRemoveEvents(removeEvents){
            leaveSharing = removeEvents;
        }

        function getMethodToRemoveEvents(){
            return leaveSharing;
        }

        function removeNotification() {
            $timeout(function () {
                notification = {"removeBanner": true};
                $rootScope.$broadcast("meetingNotification");
            });
        }
    }
})();
