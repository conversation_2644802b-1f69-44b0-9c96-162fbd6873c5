(function () {
    'use strict';

    angular
        .module('app.services')
        .factory('linkedPartService', linkedPartService);

    linkedPartService.$inject = ['$http', 'apiConstants'];

    function linkedPartService($http, apiConstants) {
        return {
            fetchLinkedParts: fetchLinkedParts,
            fetchLinkedPartsForModel: fetchLinkedPartsForModel
        };

        function fetchLinkedParts(optionsSetId) {
            return $http.get(apiConstants.url + '/optionsSet/' + optionsSetId);
        }

        function fetchLinkedPartsForModel(modelId) {
            return $http.get(apiConstants.url + '/model/' + modelId + '/partLink');
        }





    }
})();
