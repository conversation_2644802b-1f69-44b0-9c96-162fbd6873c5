angular
    .module("app.viewer")
    .directive("nameNodeFunctions", [
        "viewerHelperService",
        "$uibModal",
        "$rootScope",
        function (viewerHelperService, $uibModal, $rootScope) {
            "use strict";

            return {
                restrict: "A",
                require: "^ivhTreeview",
                link: function (scope, element) {
                    var node = scope.node;

                    element.addClass("name-node-functions");

                    element.bind("click", function (event) {
                        if (event.ctrlKey) {
                            var currentSelections = viewerHelperService.getSelectedParts();
                            if (!(currentSelections instanceof Array)) {
                                currentSelections = [currentSelections];
                            }

                            if (currentSelections.indexOf(node.objectId) > -1) {
                                currentSelections.splice(currentSelections.indexOf(node.objectId), 1);
                            } else {
                                currentSelections.push(node.objectId);
                            }

                            viewerHelperService.selectParts(currentSelections);
                            viewerHelperService.showParts(currentSelections);
                        } else {
                            var tempNode = node.objectId;
                            if (!(tempNode instanceof Array)) {
                                tempNode = [tempNode];
                            }
                            viewerHelperService.showParts(tempNode);
                            viewerHelperService.selectParts(tempNode);
                        }
                    });

                    scope.openViewEditPart = function (node) {
                        var modalInstance = $uibModal.open({
                            templateUrl: "features/viewer/extensions/viewEditPart/viewEditPartModal.html",
                            controller: "ViewEditPartModalController as viewEditPartCtrl",
                            resolve: {
                                partNode: function () {
                                    return node;
                                },
                            },
                        });

                        modalInstance.result.then(function (edits) {
                            if (edits) {
                                node.partNumber = edits.partNumber;
                                node.partDescription = edits.partDescription;
                            }
                        });
                    };

                    scope.togglePartLock = function (node) {
                        node.isLocked = !node.isLocked;
                        $rootScope.$broadcast("unlock-children-of-locked-nodes");
                        doExplosion();
                        $rootScope.$broadcast("lock-state-updated-broadcast");
                        //$rootScope.$broadcast("collapse-nodes-children", node);
                    };

                    $rootScope.$on(
                        "unlockAllNodesEvent",
                        function (event, ivhTreeviewBfs, modelTree, customOpts, initiallyLockedNodes, updatedLockedNodes) {
                            ivhTreeviewBfs(modelTree, customOpts, function (node) {
                                if (
                                    node.isLocked &&
                                    node.childParts !== null &&
                                    node.childParts !== undefined &&
                                    node.childParts.length > 0
                                ) {
                                    initiallyLockedNodes.push(node);
                                    updatedLockedNodes.push([node.objectId]);
                                    node.isLocked = false;
                                }
                                return true;
                            });
                        }
                    );

                    scope.nonModeledPartClicked = function (node) {
                        viewerHelperService.selectParts([]);
                        $rootScope.$broadcast("activateNonModeledParts", { ids: node.objectId });
                        viewerHelperService.selectParts(node.objectId);
                    };

                    scope.getSpaces = function (num) {
                        var spaces = "";
                        for (var i = 0; i < num; i++) {
                            spaces += "\u00A0 \u00A0 ";
                        }
                        return spaces;
                    };
                },
            };
        },
    ])

    .directive("ivhTreeviewCheckboxCadshare", [
        function () {
            "use strict";
            return {
                restrict: "AE",
                require: "^ivhTreeview",
                template: '<span ivh-treeview-checkbox-helper-cadshare="node"></span>',
            };
        },
    ])

    .directive("ivhTreeviewCheckboxHelperCadshare", [
        function () {
            "use strict";
            return {
                restrict: "A",
                scope: {
                    node: "=ivhTreeviewCheckboxHelperCadshare",
                },
                require: "^ivhTreeview",
                link: function (scope, element, attrs, trvw) {
                    var node = scope.node,
                        opts = trvw.opts(),
                        indeterminateAttr = opts.indeterminateAttribute,
                        selectedAttr = opts.selectedAttribute;

                    // Set initial selected state of this checkbox
                    scope.isSelected = node[selectedAttr];

                    // Local access to the parent controller
                    scope.trvw = trvw;

                    // Enforce consistent behavior across browsers by making indeterminate
                    // checkboxes become checked when clicked/selected using spacebar
                    scope.resolveIndeterminateClick = function () {
                        //intermediate state is not handled when CheckBoxes state propagation is disabled
                        if (opts.disableCheckboxSelectionPropagation) {
                            return;
                        }

                        if (node[indeterminateAttr]) {
                            trvw.select(node, true);
                        }
                    };

                    // Update the checkbox when the node's selected status changes
                    scope.$watch("node." + selectedAttr, function (newVal) {
                        scope.isSelected = newVal;
                    });

                    if (!opts.disableCheckboxSelectionPropagation) {
                        // Update the checkbox when the node's indeterminate status changes
                        scope.$watch("node." + indeterminateAttr, function (newVal) {
                            element.find("input").prop("indeterminate", newVal);
                        });
                    }
                },
                template: [
                    '<label class="fancy-checkbox">',
                    '<input type="checkbox"',
                    'class="ivh-treeview-checkbox-cadshare"',
                    'ng-model="isSelected"',
                    'ng-click="resolveIndeterminateClick()"',
                    'ng-change="trvw.select(node, isSelected)" class=""/>',
                    '<i class="fa fa-fw fa-eye-slash grey unchecked"></i>',
                    '<i class="fa fa-fw fa-eye checked"></i>',
                    "</label>",
                ].join("\n"),
            };
        },
    ]);
