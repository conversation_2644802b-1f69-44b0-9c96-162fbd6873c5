(function () {
    'use strict';

    angular
        .module('app.base')
        .controller('PreAuthController', PreAuthController);

    PreAuthController.$inject = ['$state', '$rootScope', 'userService', '$stateParams', 'tokenService', '$window'];

    function PreAuthController($state, $rootScope, userService, $stateParams, tokenService, $window) {
        var vm = this;

        vm.authenticateUser = authenticateUser;
        initialize();

        vm.tokenExpired = false;
        vm.userNotFound = false;
        vm.unknownFailure = false;
        vm.errorCode = 0;

        function initialize() {
            checkBrowserCompatibility();
            authenticateUser();
        }

        function checkBrowserCompatibility() {
            var ua = window.navigator.userAgent;
            var msie = ua.indexOf("MSIE ");

            vm.browserIE = msie > 0 || !!navigator.userAgent.match(/Trident.*rv\:11\./);
        }

        function authenticateUser() {
            vm.credentialsError = false;
            vm.accountLocked = false;
            vm.rolesError = false;
            tokenService.preAuthLogin($stateParams.client, $stateParams.token)
                .then(authenticateUserSuccess, authenticateUserFailed);
        }

        function authenticateUserSuccess() {
            userService.getUserLoginInfo()
                .then(function (response) {
                    userService.getUserInfo()
                        .then(getUserInfoSuccess, getUserInfoFailed);
                }, function (error) {
                    console.log(error);
                });
        }

        function getUserInfoSuccess() {
            if ($stateParams.redirect && $stateParams.redirect !== "") {
                $state.go($stateParams.redirect, $stateParams.params);
            } else {
                $state.go('publishedProducts');
            }

        }

        function getUserInfoFailed(error) {
            // TODO add proper error handling here
            console.log(error);
        }

        function authenticateUserFailed(error) {
            if (error.responseType === 'EXPIRED') {
                vm.tokenExpired = true;
                $window.location.href = error.redirect;
            } else if (error.responseType === 'NOT_FOUND') {
                vm.userNotFound = true;
                vm.errorCode = 1;
            } else {
                vm.unknownFailure = true;
                vm.errorCode = 2;
            }
        }
    }
})();
