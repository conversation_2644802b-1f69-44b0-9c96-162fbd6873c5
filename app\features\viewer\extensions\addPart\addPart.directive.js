(function () {
    'use strict';

    angular
        .module('app.viewer')
        .directive('addPart', addPart);

    function addPart() {
        var directive = {
            restrict: 'E',
            templateUrl: 'features/viewer/extensions/addPart/addPart.html',
            scope: {
                title: '@',
                confirmButtonText: '@',
                declineButtonText: '@',
                loadParts: '&',
                onConfirm: '&',
                onDecline: '&',
                isVisible: '&',
                modelId: '@',
                addPartList: '='
            },
            controller: 'AddPartController',
            controllerAs: 'addPartCtrl',
            bindToController: true
        };
        return directive;
    }

})();